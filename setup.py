from setuptools import setup, find_packages
import os

def read_version():
    """Read version from gbs/__init__.py"""
    with open(os.path.join("gbs", "__init__.py"), "r") as f:
        for line in f:
            if line.startswith("__version__"):
                # Extract version string
                version = line.split("=")[1].strip().strip('"').strip("'")
                return version
    return "0.1.0"  # Default version if not found

setup(
    name="gold-beast-system",
    version=read_version(),
    description="Gold Beast Trading System",
    author="Ran <PERSON>",
    author_email="<EMAIL>",
    packages=find_packages(),
    install_requires=[
        "pandas>=1.3.0",
        "torch>=1.10.0",
        "lightgbm>=3.3.0",
        "mlflow>=2.3.0",
        "scikit-learn>=1.0.0",
        "matplotlib>=3.4.0",
        "numpy>=1.20.0",
        "pyyaml>=5.4.0",
        "tqdm>=4.62.0",
        "fire>=0.4.0",
    ],
    entry_points={
        "console_scripts": [
            "gbsrun=gbs.workflow.cli:run",
        ],
    },
    python_requires=">=3.8",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Financial and Insurance Industry",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
)
