/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AnalysisReport: typeof import('./src/components/AnalysisReport.vue')['default']
    BacktestResultsBlock: typeof import('./src/components/report/BacktestResultsBlock.vue')['default']
    BlockRenderer: typeof import('./src/components/report/BlockRenderer.vue')['default']
    CompanyLogo: typeof import('./src/components/CompanyLogo.vue')['default']
    CompanySelect: typeof import('./src/components/CompanySelect.vue')['default']
    ConfigBlock: typeof import('./src/components/report/ConfigBlock.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAutoResizer: typeof import('element-plus/es')['ElAutoResizer']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSegmented: typeof import('element-plus/es')['ElSegmented']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTableV2: typeof import('element-plus/es')['ElTableV2']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    EquityCurveBlock: typeof import('./src/components/report/EquityCurveBlock.vue')['default']
    FactorAnalysisTab: typeof import('./src/components/portfolio/FactorAnalysisTab.vue')['default']
    GeneralBlock: typeof import('./src/components/report/GeneralBlock.vue')['default']
    ICTimeSeriesChart: typeof import('./src/components/charts/ICTimeSeriesChart.vue')['default']
    IRStatsChart: typeof import('./src/components/charts/IRStatsChart.vue')['default']
    MetricsBlock: typeof import('./src/components/report/MetricsBlock.vue')['default']
    MetricsObjectBlock: typeof import('./src/components/report/MetricsObjectBlock.vue')['default']
    PaginatedTable: typeof import('./src/components/common/PaginatedTable.vue')['default']
    PortfolioDialog: typeof import('./src/components/portfolio/PortfolioDialog.vue')['default']
    RecentCompanyTags: typeof import('./src/components/RecentCompanyTags.vue')['default']
    ReportViewer: typeof import('./src/components/report/ReportViewer.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RunPortfolioDialog: typeof import('./src/components/portfolio/RunPortfolioDialog.vue')['default']
    RunStrategyDialog: typeof import('./src/components/strategy/RunStrategyDialog.vue')['default']
    SvgBlock: typeof import('./src/components/report/SvgBlock.vue')['default']
    TableBlock: typeof import('./src/components/report/TableBlock.vue')['default']
    TextBlock: typeof import('./src/components/report/TextBlock.vue')['default']
    TimeConsuming: typeof import('./src/components/TimeConsuming.vue')['default']
    TimeSeriesFactorAnalysisTab: typeof import('./src/components/portfolio/TimeSeriesFactorAnalysisTab.vue')['default']
    WeightsHistoryBlock: typeof import('./src/components/report/WeightsHistoryBlock.vue')['default']
    WeightsPieChart: typeof import('./src/components/report/WeightsPieChart.vue')['default']
    WeightsRoseChart: typeof import('./src/components/report/WeightsRoseChart.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
