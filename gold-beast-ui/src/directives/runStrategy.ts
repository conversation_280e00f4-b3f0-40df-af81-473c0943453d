import type { Directive, DirectiveBinding } from 'vue';
import { useRunStrategyStore } from '@/stores/runStrategy';

/**
 * Run Strategy Directive
 *
 * This directive adds the ability to run a strategy when clicking on an element.
 * It can either show a configuration dialog or run the strategy directly.
 *
 * Usage:
 * v-run-strategy="{
 *   getWorkflowContent: () => string, // Function that returns the current content
 *   file_type: string,
 *   strategy_id: string,
 *   skip_configuration: boolean
 * }"
 */
export const runStrategy: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    // Get the value from the binding
    const value = binding.value;

    // Validate the value
    if (!value || typeof value !== 'object') {
      console.error('v-run-strategy directive requires an object value');
      return;
    }

    const { getWorkflowContent, file_type, strategy_id, skip_configuration = false } = value;

    if (!getWorkflowContent || typeof getWorkflowContent !== 'function' || !file_type || !strategy_id) {
      console.error('v-run-strategy directive requires getWorkflowContent (function), file_type, and strategy_id');
      return;
    }

    // Add click event listener
    el.addEventListener('click', async () => {
      const store = useRunStrategyStore();

      // Get the current content at the time of the click
      const workflow_content = getWorkflowContent();

      if (!workflow_content) {
        console.error('getWorkflowContent function returned empty content');
        return;
      }

      if (skip_configuration) {
        // Run the strategy directly
        try {
          store.openDialog(workflow_content, file_type, strategy_id);
          await store.runStrategy(true);
        } catch (error) {
          console.error('Error running strategy:', error);
        }
      } else {
        // Show the configuration dialog
        store.openDialog(workflow_content, file_type, strategy_id);
      }
    });
  },

  // Clean up when the element is unmounted
  unmounted(el: HTMLElement) {
    // Remove event listeners if needed
    el.removeEventListener('click', () => {});
  }
};

export default runStrategy;
