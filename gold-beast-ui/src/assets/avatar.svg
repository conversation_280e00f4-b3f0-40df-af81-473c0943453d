<?xml version="1.0" encoding="UTF-8"?>
<svg width="128px" height="128px" viewBox="0 0 128 128" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>User Avatar</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="avatarGradient">
            <stop stop-color="#1890FF" offset="0%"></stop>
            <stop stop-color="#096DD9" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle fill="url(#avatarGradient)" cx="64" cy="64" r="64"></circle>
        <circle fill="#FFFFFF" cx="64" cy="50" r="20"></circle>
        <path d="M64,80 C42,80 24,94 24,112 L104,112 C104,94 86,80 64,80 Z" fill="#FFFFFF"></path>
    </g>
</svg>
