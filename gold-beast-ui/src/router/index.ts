import { createRouter, createWebHashHistory } from "vue-router";

// 路由配置
const routes = [
  {
    path: "/",
    redirect: "/backtest",
  },
  {
    path: "/",
    component: () => import("../layout/index.vue"),
    children: [
      {
        path: "/strategy/list",
        name: "StrategyList",
        component: () => import("../views/strategy/list.vue"),
        meta: {
          title: "策略列表",
          icon: "List",
          keepAlive: true,
        },
      },
      {
        path: "/strategy/edit/:id",
        name: "StrategyEdit",
        component: () => import("../views/strategy/edit.vue"),
        meta: {
          title: "编辑策略",
          icon: "Edit",
          keepAlive: true,
          hidden: true, // 在菜单中隐藏
        },
      },
      {
        path: "/strategy/run-detail/:experiment_id/:run_id",
        name: "StrategyRunDetail",
        component: () => import("../views/strategy/run-detail.vue"),
        meta: {
          title: "策略运行详情",
          icon: "Document",
          keepAlive: true,
          hidden: true, // 在菜单中隐藏
        },
      },
      {
        path: "/factor",
        name: "FactorSystem",
        component: () => import("../views/factor/index.vue"),
        meta: {
          title: "因子系统",
          icon: "DataBoard",
          keepAlive: true,
        },
        children: [
          {
            path: "covariance",
            name: "FactorCovariance",
            component: () => import("../views/factor/covariance.vue"),
            meta: {
              title: "因子协方差分析",
              icon: "Grid",
              keepAlive: true,
            },
          },
          {
            path: "returns",
            name: "FactorReturns",
            component: () => import("../views/factor/returns.vue"),
            meta: {
              title: "因子收益分析(计算)",
              icon: "Money",
              keepAlive: true,
            },
          },
          {
            path: "read",
            name: "FactorRead",
            component: () => import("../views/factor/read.vue"),
            meta: {
              title: "因子收益分析(读取)",
              icon: "Money",
              keepAlive: true,
            },
          },
          {
            path: "features",
            name: "FactorFeatures",
            component: () => import("../views/factor/features.vue"),
            meta: {
              title: "因子特征分析",
              icon: "DataAnalysis",
              keepAlive: true,
            },
          },
        ],
      },
      // 回测系统
      {
        path: "/backtest",
        name: "BacktestSystem",
        component: () => import("../views/backtest/index.vue"),
        meta: {
          title: "回测系统",
          icon: "TrendCharts",
          keepAlive: true,
        },
      },
      {
        path: "/backtest/detail/:experiment_id/:run_id",
        name: "BacktestDetail",
        component: () => import("../views/backtest/detail.vue"),
        meta: {
          title: "回测详情",
          icon: "Document",
          keepAlive: true,
          hidden: true, // 在菜单中隐藏
        },
      },
      // 公司看板
      {
        path: "/company",
        name: "CompanyDashboard",
        component: () => import("../views/company/dashboard.vue"),
        meta: {
          title: "公司看板",
          icon: "Monitor",
          keepAlive: true,
        },
      },
      // 资产看板
      {
        path: "/asset",
        name: "AssetDashboard",
        component: () => import("../views/asset/dashboard.vue"),
        meta: {
          title: "资产看板",
          icon: "TrendCharts",
          keepAlive: true,
        },
      },
      // 投资组合
      {
        path: "/portfolio",
        name: "PortfolioList",
        component: () => import("../views/portfolio/index.vue"),
        meta: {
          title: "投资组合",
          icon: "Wallet",
          keepAlive: true,
        },
      },
      {
        path: "/portfolio/:id",
        name: "PortfolioDetail",
        component: () => import("../views/portfolio/detail.vue"),
        meta: {
          title: "投资组合详情",
          icon: "Wallet",
          keepAlive: true,
          hidden: true, // 在菜单中隐藏
        },
      },
    ],
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("../views/error/404.vue"),
    meta: {
      title: "404",
      hidden: true,
    },
  },
];

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 路由前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = `金兽系统 - ${to.meta.title || ""}`;

  // 检查是否是页面刷新
  const isRefresh = from.name === null;

  // 如果是刷新并且是投资组合相关页面，预加载数据
  if (isRefresh || to.path === '/portfolio') {
    if (to.path === '/portfolio') {
      // 动态导入 store 以避免循环依赖
      const { usePortfolioStore } = await import('../stores/portfolio');
      const portfolioStore = usePortfolioStore();

      // 如果没有数据，加载数据
      console.log('Router guard: checking portfolios', {
        hasPortfolios: portfolioStore.hasPortfolios,
        loading: portfolioStore.loading,
        portfolios: portfolioStore.portfolios
      });

      // Always load portfolios on refresh or direct navigation to portfolio page
      if (!portfolioStore.loading) {
        console.log('Router guard: loading portfolios');
        try {
          await portfolioStore.fetchPortfolios();
          console.log('Router guard: portfolios loaded', {
            hasPortfolios: portfolioStore.hasPortfolios,
            portfolios: portfolioStore.portfolios
          });
        } catch (error) {
          console.error('Failed to preload portfolios:', error);
        }
      }
    }
    else if (to.name === 'PortfolioDetail' && to.params.id) {
      // 动态导入 store 以避免循环依赖
      const { usePortfolioStore } = await import('../stores/portfolio');
      const portfolioStore = usePortfolioStore();

      // 如果没有当前投资组合数据，加载数据
      if (!portfolioStore.currentPortfolio && !portfolioStore.loading) {
        try {
          await portfolioStore.fetchPortfolio(to.params.id as string);
        } catch (error) {
          console.error('Failed to preload portfolio details:', error);
        }
      }
    }
  }

  next();
});

export default router;
