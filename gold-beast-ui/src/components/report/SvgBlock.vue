<template>
  <div class="svg-block" v-html="block.content"></div>
</template>

<script setup lang="ts">
defineProps({
  block: {
    type: Object,
    required: true
  }
});
</script>

<style lang="scss">
.svg-block {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 20px 0;

  svg {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }
}
</style>
