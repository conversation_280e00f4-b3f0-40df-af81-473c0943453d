<template>
  <div class="text-block">
    <div v-if="block.format === 'markdown'" v-html="renderedMarkdown"></div>
    <div v-else v-html="block.content"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { marked } from "marked";

const props = defineProps({
  block: {
    type: Object,
    required: true,
  },
});

// 渲染 Markdown
const renderedMarkdown = computed(() => {
  if (props.block.format === "markdown" && props.block.content) {
    return marked(props.block.content);
  }
  return props.block.content;
});
</script>

<style lang="scss">
.text-block {
  width: 100%;
  margin: 20px 0;
  line-height: 1.6;
  color: #303133;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    font-weight: 600;
    line-height: 1.25;
    color: #303133;
  }

  h1 {
    font-size: 24px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 0.3em;
  }

  h2 {
    font-size: 20px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 0.3em;
  }

  h3 {
    font-size: 18px;
  }

  h4 {
    font-size: 16px;
  }

  h5,
  h6 {
    font-size: 14px;
  }

  p {
    margin-top: 0;
    margin-bottom: 16px;
    line-height: 1.6;
  }

  ul,
  ol {
    padding-left: 2em;
    margin-top: 0;
    margin-bottom: 16px;

    li {
      margin-bottom: 8px;
    }
  }

  a {
    color: #409eff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  blockquote {
    margin: 0 0 16px;
    padding: 0 1em;
    color: #6a737d;
    border-left: 0.25em solid #dfe2e5;
  }

  code {
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 3px;
  }

  pre {
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f6f8fa;
    border-radius: 4px;
    margin-top: 0;
    margin-bottom: 16px;

    code {
      background-color: transparent;
      padding: 0;
      border-radius: 0;
    }
  }

  img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 16px 0;
    border-radius: 4px;
  }

  hr {
    height: 0.25em;
    padding: 0;
    margin: 24px 0;
    background-color: #e1e4e8;
    border: 0;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 16px;

    th,
    td {
      border: 1px solid #dfe2e5;
      padding: 8px 12px;
    }

    th {
      background-color: #f6f8fa;
      font-weight: 600;
    }

    tr:nth-child(even) {
      background-color: #f8f8f8;
    }
  }
}
</style>
