<template>
  <div class="weights-rose-chart">
    <div class="chart-container" ref="chartRef"></div>

    <!-- 如果持仓数量超过显示限制，显示查看更多按钮 -->
    <div v-if="weights.length > maxWeightsToShow" class="view-more">
      <el-button type="primary" link @click="showAllWeights">
        查看全部 {{ weights.length }} 个持仓
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue';
import * as echarts from 'echarts';
import { ElMessageBox } from 'element-plus';
import { useEchartResize } from "@/composables/useEchartResize";

const props = defineProps({
  weights: {
    type: Array,
    required: true
  },
  date: {
    type: String,
    default: ''
  },
  maxWeightsToShow: {
    type: Number,
    default: 10
  }
});

const chartRef = ref(null);
let chart = null;
let chartResize = null;

// 按权重绝对值排序的持仓，排除权重为0的数据
const sortedWeights = computed(() => {
  if (!props.weights || props.weights.length === 0) return [];

  return [...props.weights]
    .filter(w => w.weight !== 0) // 排除权重为0的数据
    .sort((a, b) => Math.abs(b.weight) - Math.abs(a.weight))
    .slice(0, props.maxWeightsToShow);
});

// 格式化权重
const formatWeight = (weight) => {
  if (typeof weight === "number") {
    return weight.toFixed(2);
  }
  return weight;
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  // 清除旧图表
  if (chart) {
    chart.dispose();
    chartResize = null;
  }

  // 创建新图表
  chart = echarts.init(chartRef.value);
  chartResize = useEchartResize(chart);

  // 检查是否有有效数据
  const filteredWeights = props.weights?.filter(w => w.weight !== 0) || [];

  // 如果没有有效数据，直接显示空状态
  if (filteredWeights.length === 0) {
    chart.setOption({
      title: {
        text: '无持仓数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal',
          color: '#909399'
        }
      },
      series: []
    });
    return;
  }

  // 更新图表数据
  updateChart();
};

// 更新图表数据
const updateChart = () => {
  if (!chart) return;

  const weights = sortedWeights.value;
  if (weights.length === 0) {
    // 完全清除图表，确保不会显示上一次的数据
    chart.clear();
    // 设置空图表，只显示提示文本
    chart.setOption({
      title: {
        text: '无持仓数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal',
          color: '#909399'
        }
      },
      // 清空所有系列数据
      series: []
    });
    return;
  }

  // 分离正权重和负权重
  const positiveWeights = weights.filter(w => w.weight >= 0);
  const negativeWeights = weights.filter(w => w.weight < 0);

  // 准备南丁格尔玫瑰图数据
  const series = [];

  // 创建南丁格尔玫瑰图配置的通用部分
  const createRoseConfig = (name, center, weights, isPositive) => ({
    name,
    type: 'pie',
    // 设置为南丁格尔玫瑰图
    roseType: 'area',
    // 调整图表大小，减小半径使图表更紧凑
    radius: positiveWeights.length > 0 && negativeWeights.length > 0 ? [15, 70] : [20, 85],
    center,
    // 确保标签不会被裁剪
    avoidLabelOverlap: true,
    // 使用标签引导线显示ticker和权重
    label: {
      show: true,
      // 使用外部标签
      position: 'outside',
      // 格式化标签内容，使用富文本格式
      formatter: (params) => {
        // 获取权重值
        const value = params.value;
        // 获取股票代码
        const ticker = params.name;

        // 不再需要折行处理，直接使用原始股票代码
        let formattedTicker = ticker;

        // 格式化权重值，确保显示两位小数
        let formattedValue = value;
        if (typeof value === 'number') {
          formattedValue = value.toFixed(2);
        }

        // 返回富文本格式的标签，将股票代码和权重放在同一行
        return '{ticker|' + formattedTicker + '}{value|' + formattedValue + '%}';
      },
      // 确保标签始终显示完整
      alignTo: 'edge',
      edgeDistance: '10%',
      margin: 15,
      // 防止标签被截断
      overflow: 'breakAll',
      // 允许标签换行，但减小宽度使布局更紧凑
      width: 150,
      // 富文本样式定义
      rich: {
        ticker: {
          fontSize: 14,
          fontWeight: 'bold',
          padding: [0, 0, 0, 0],
          color: '#333'
        },
        value: {
          fontSize: 12,
          fontWeight: 'normal',
          padding: [0, 0, 0, 0],
          color: '#666'
        }
      }
    },
    // 配置标签引导线
    labelLine: {
      show: true,
      length: 15,
      length2: 20,
      smooth: true,
      maxSurfaceAngle: 80,
      lineStyle: {
        width: 1,
        type: 'solid',
        opacity: 0.8
      }
    },
    // 禁用动画效果
    animation: false,
    // 禁用高亮效果，避免闪烁
    emphasis: {
      scale: false,
      itemStyle: {
        shadowBlur: 0
      }
    },

    data: weights.map(w => ({
      name: w.ticker,
      value: parseFloat(formatWeight(isPositive ? w.weight : Math.abs(w.weight))),
      itemStyle: {
        color: getRandomColor(w.ticker, isPositive),
        borderRadius: 8,
        borderWidth: 1,
        borderColor: 'rgba(255, 255, 255, 0.5)'
      }
    }))
  });

  // 根据是否有正负权重决定布局
  if (positiveWeights.length > 0 && negativeWeights.length > 0) {
    // 同时有正负权重，使用左右布局
    series.push(createRoseConfig('正权重', ['30%', '50%'], positiveWeights, true));
    series.push(createRoseConfig('负权重', ['70%', '50%'], negativeWeights, false));
  } else if (positiveWeights.length > 0) {
    // 只有正权重，居中显示
    series.push(createRoseConfig('正权重', ['50%', '50%'], positiveWeights, true));
  } else if (negativeWeights.length > 0) {
    // 只有负权重，居中显示
    series.push(createRoseConfig('负权重', ['50%', '50%'], negativeWeights, false));
  }

  // 创建标题配置
  const createTitles = () => {
    const titles = [
      {
        text: props.date ? `${props.date} 持仓权重分布` : '持仓权重分布',
        left: 'center',
        top: 0,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      }
    ];

    // 根据是否有正负权重决定副标题布局
    if (positiveWeights.length > 0 && negativeWeights.length > 0) {
      // 同时有正负权重，使用左右布局
      titles.push({
        text: '正权重',
        left: '30%',
        top: '8%',
        textAlign: 'center',
        textStyle: {
          fontSize: 14,
          color: '#67c23a',
          fontWeight: 'bold'
        }
      });

      titles.push({
        text: '负权重',
        left: '70%',
        top: '8%',
        textAlign: 'center',
        textStyle: {
          fontSize: 14,
          color: '#f56c6c',
          fontWeight: 'bold'
        }
      });
    } else if (positiveWeights.length > 0) {
      // 只有正权重，居中显示
      titles.push({
        text: '正权重',
        left: '50%',
        top: '8%',
        textAlign: 'center',
        textStyle: {
          fontSize: 14,
          color: '#67c23a',
          fontWeight: 'bold'
        }
      });
    } else if (negativeWeights.length > 0) {
      // 只有负权重，居中显示
      titles.push({
        text: '负权重',
        left: '50%',
        top: '8%',
        textAlign: 'center',
        textStyle: {
          fontSize: 14,
          color: '#f56c6c',
          fontWeight: 'bold'
        }
      });
    }

    return titles;
  };

  // 设置图表选项
  chart.setOption({
    title: createTitles(),
    // 优化提示框
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}%',
      // 禁用动画
      showDelay: 0,
      hideDelay: 0,
      transitionDuration: 0,
      // 美化样式
      backgroundColor: 'rgba(50,50,50,0.9)',
      borderColor: '#eee',
      borderWidth: 1,
      padding: [5, 10]
    },

    // 添加全局配置
    animationDuration: 0,
    animationEasing: 'linear',
    hoverLayerThreshold: 0,
    series: series
  });
};

// 生成随机颜色（但对同一个ticker保持一致）
const getRandomColor = (ticker, isPositive) => {
  // 计算字符串的哈希值
  const calculateHash = (str) => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }
    return hash;
  };

  // 使用ticker字符串生成一个数字
  const hash = calculateHash(ticker);

  // 生成HSL颜色
  const hue = hash % 360;                // 色相 (0-359)
  const saturation = 70 + (hash % 20);   // 饱和度 (70-90%)
  const lightness = isPositive ? 50 : 40; // 亮度 (正权重50%，负权重40%)

  return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
};

// 显示所有持仓
const showAllWeights = () => {
  if (!props.weights || props.weights.length === 0) return;

  const allWeights = [...props.weights]
    .filter(w => w.weight !== 0) // 排除权重为0的数据
    .sort((a, b) => Math.abs(b.weight) - Math.abs(a.weight));

  const weightsHtml = allWeights
    .map(w => `
      <tr>
        <td style="border: 1px solid #ddd; padding: 8px;">${w.ticker}</td>
        <td style="border: 1px solid #ddd; padding: 8px;">${formatWeight(w.weight)}%</td>
      </tr>
    `)
    .join('');

  const content = `
    <div style="max-height: 400px; overflow-y: auto;">
      <table style="width: 100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">股票代码</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">权重(%)</th>
          </tr>
        </thead>
        <tbody>
          ${weightsHtml}
        </tbody>
      </table>
    </div>
  `;

  ElMessageBox.alert(
    content,
    `${props.date} 全部持仓 (${props.weights.length})`,
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: "关闭",
    }
  );
};

// 监听权重变化
watch(() => props.weights, (newWeights) => {
  nextTick(() => {
    // 如果权重数组变为空或全部为0，需要重新初始化图表以清除旧数据
    if (!newWeights || newWeights.length === 0 || newWeights.every(w => w.weight === 0)) {
      initChart();
    } else {
      updateChart();
    }
  });
}, { deep: true, immediate: true });

// 组件挂载时初始化图表
onMounted(() => {
  nextTick(() => {
    initChart();
  });
});
</script>

<style lang="scss" scoped>
.weights-rose-chart {
  padding: 8px;

  .chart-container {
    width: 100%;
    height: 400px; // 进一步减小高度使图表更紧凑
    margin: 0 auto;
  }

  .view-more {
    margin-top: 8px;
    text-align: center;
  }

  :deep(.el-button--primary.is-link) {
    font-weight: bold;
  }
}
</style>
