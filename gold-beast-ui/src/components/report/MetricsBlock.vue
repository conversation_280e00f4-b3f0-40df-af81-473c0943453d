<template>
  <div class="metrics-block" :class="{ 'inline': block.layout === 'inline' }">
    <div
      v-for="metric in block.metrics"
      :key="metric.key"
      class="metric-card"
      :style="{ color: metric.color }"
    >
      <h3 class="metric-label">{{ metric.label }}</h3>
      <div class="metric-value">
        {{ formatValue(metric.value, metric.format) }}
        <span v-if="metric.unit" class="metric-unit">{{ metric.unit }}</span>
      </div>
      <div v-if="metric.trend" class="metric-trend">
        <i
          :class="[
            'el-icon',
            metric.trend === 'up' ? 'el-icon-top' :
            metric.trend === 'down' ? 'el-icon-bottom' : 'el-icon-right'
          ]"
        ></i>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  block: {
    type: Object,
    required: true
  }
});

// 格式化值
const formatValue = (value, format) => {
  if (value === undefined || value === null) return '';

  // 如果没有格式化字符串，直接返回值
  if (!format) return value;

  // 处理百分比格式
  if (format.includes('%')) {
    const numFormat = format.replace('%', '');
    const numValue = parseFloat(value) * 100;
    return numFormat.includes('.')
      ? numValue.toFixed(parseInt(numFormat.split('.')[1].length)) + '%'
      : numValue.toFixed(0) + '%';
  }

  // 处理数字格式
  if (format.includes('f')) {
    const precision = parseInt(format.split('.')[1].replace('f', ''));
    return parseFloat(value).toFixed(precision);
  }

  // 默认返回原值
  return value;
};
</script>

<style lang="scss">
.metrics-block {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin: 24px 0;

  &.inline {
    flex-direction: row;
  }

  .metric-card {
    background-color: #f5f7fa;
    border-radius: 8px;
    padding: 20px;
    min-width: 180px;
    flex: 1;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid #ebeef5;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.1);
    }

    .metric-label {
      font-size: 14px;
      color: #909399;
      margin: 0 0 12px 0;
      font-weight: 600;
    }

    .metric-value {
      font-size: 28px;
      font-weight: bold;
      color: #303133;
      line-height: 1.2;

      .metric-unit {
        font-size: 14px;
        color: #909399;
        margin-left: 4px;
        font-weight: normal;
      }
    }

    .metric-trend {
      margin-top: 12px;
      font-size: 14px;
      display: flex;
      align-items: center;

      i {
        margin-right: 4px;
      }
    }
  }
}
</style>
