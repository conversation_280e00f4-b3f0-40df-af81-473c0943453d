<template>
  <div class="backtest-results-block">
    <!-- 指标展示 -->
    <div
      v-if="metrics && Object.keys(metrics).length > 0"
      class="metrics-container"
    >
      <el-card shadow="hover" class="metrics-card">
        <template #header>
          <div class="card-header">
            <h3>回测指标</h3>
          </div>
        </template>
        <div class="metrics-grid">
          <div
            v-for="(value, key) in formattedMetrics"
            :key="key"
            class="metric-item"
          >
            <div class="metric-label">{{ formatLabel(key) }}</div>
            <div class="metric-value">{{ value }}</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 权益曲线图表 -->
    <div
      v-if="equityCurve && equityCurve.dates && equityCurve.equity"
      class="equity-curve-container"
    >
      <el-card shadow="hover" class="chart-card">
        <template #header>
          <div class="card-header">
            <h3>权益曲线</h3>
          </div>
        </template>
        <div class="chart-container" ref="equityChartRef"></div>
      </el-card>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  block: {
    type: Object,
    required: true,
  },
});

// 提取数据
const metrics = computed(() => {
  const allMetrics = props.block.metrics || {};
  // 排除 turnovers，因为它是一个数组
  if (allMetrics.turnovers) {
    const { turnovers, ...otherMetrics } = allMetrics;
    return otherMetrics;
  }
  return allMetrics;
});


const equityCurve = computed(() => {
  // 确保我们能正确获取equity_curve数据
  if (props.block.equity_curve) {
    return props.block.equity_curve;
  }
  // 如果没有直接的equity_curve字段，返回空对象
  return {};
});


// 格式化指标
const formattedMetrics = computed(() => {
  const result: Record<string, string | number | boolean | null> = {};
  Object.entries(metrics.value).forEach(([key, value]) => {
    if (typeof value === "number") {
      // 根据指标名称应用不同的格式化规则
      if (
        key.includes("return") ||
        key.includes("alpha") ||
        key.includes("beta") ||
        key.includes("drawdown")
      ) {
        result[key] = `${(value * 100).toFixed(2)}%`;
      } else if (
        key.includes("ratio") ||
        key.includes("sharpe") ||
        key.includes("sortino")
      ) {
        result[key] = value.toFixed(3);
      } else {
        result[key] = value.toFixed(2);
      }
    } else if (value === null) {
      result[key] = "N/A";
    } else if (typeof value === "string" || typeof value === "boolean") {
      result[key] = value;
    } else {
      result[key] = String(value);
    }
  });
  return result;
});

// 格式化标签
const formatLabel = (key: string) => {
  return key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
};



// 权益曲线图表引用
const equityChartRef = ref<HTMLElement | null>(null);
let equityChart: echarts.ECharts | null = null;



// 初始化权益曲线图表
const initEquityChart = () => {
  if (!equityChartRef.value) {
    return;
  }

  // 确保我们有有效的数据
  if (
    !equityCurve.value ||
    !equityCurve.value.dates ||
    !equityCurve.value.equity
  ) {
    // 如果没有数据，显示一个空图表
    if (equityChart) {
      equityChart.dispose();
    }

    equityChart = echarts.init(equityChartRef.value);
    equityChart.setOption({
      title: {
        text: "权益曲线 (无数据)",
        left: "center",
      },
      tooltip: {
        trigger: "axis",
      },
      xAxis: {
        type: "category",
        data: [],
      },
      yAxis: {
        type: "value",
      },
      series: [],
    });

    return;
  }

  if (equityChart) {
    equityChart.dispose();
  }

  equityChart = echarts.init(equityChartRef.value);

  const option = {
    title: {
      text: "权益曲线",
      left: "center",
    },
    tooltip: {
      trigger: "axis",
      formatter: "{b}: {c}",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: equityCurve.value.dates,
      boundaryGap: false,
    },
    yAxis: {
      type: "value",
      name: "权益",
      axisLabel: {
        formatter: "{value}",
      },
    },
    series: [
      {
        name: "权益",
        type: "line",
        data: equityCurve.value.equity,
        smooth: true,
        lineStyle: {
          width: 2,
        },
        areaStyle: {
          opacity: 0.2,
        },
      },
    ],
  };

  equityChart.setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", () => {
    equityChart?.resize();
  });
};

// 监听数据变化，更新图表
watch(
  () => equityCurve.value,
  () => {
    initEquityChart();
  },
  { deep: true }
);

// 创建一个引用来存储observer实例，以便在组件卸载时可以断开连接
const observer = ref<IntersectionObserver | null>(null);

// 组件挂载后初始化图表
onMounted(() => {
  initEquityChart();

  // 监听元素可见性变化
  observer.value = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting && equityChartRef.value) {
        // 当元素变为可见时，重新调整图表大小
        setTimeout(() => {
          equityChart?.resize();
        }, 100);
      }
    });
  }, { threshold: 0.1 });

  // 开始观察图表容器
  if (equityChartRef.value) {
    observer.value.observe(equityChartRef.value);
  }
});

// 组件卸载时清理资源
onUnmounted(() => {
  // 断开observer连接
  if (observer.value) {
    observer.value.disconnect();
    observer.value = null;
  }

  // 销毁图表实例
  if (equityChart) {
    equityChart.dispose();
    equityChart = null;
  }

  // 移除window resize事件监听器
  window.removeEventListener('resize', () => equityChart?.resize());
});
</script>

<style lang="scss" scoped>
.backtest-results-block {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .metrics-container,
  .equity-curve-container {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;

    .metric-item {
      padding: 8px;
      border-radius: 4px;
      background-color: #f5f7fa;

      .metric-label {
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
      }

      .metric-value {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }
  }

  .chart-container {
    height: 300px;
    width: 100%;
  }


}
</style>
