<template>
  <div class="metrics-object-block">
    <div
      v-if="metricsData && Object.keys(metricsData).length > 0"
      class="metrics-container"
    >
      <div class="metrics-grid">
        <div
          v-for="(value, key) in formattedMetrics"
          :key="key"
          class="metric-item"
        >
          <div class="metric-label">{{ formatLabel(key) }}</div>
          <div class="metric-value">{{ value }}</div>
        </div>
      </div>
    </div>
    <div v-else class="empty-metrics">
      <el-empty description="无指标数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps({
  block: {
    type: Object,
    required: true,
  },
});

// 提取指标数据
const metricsData = computed(() => {
  if (props.block.metrics) {
    return props.block.metrics;
  }
  return {};
});

// 格式化指标
const formattedMetrics = computed(() => {
  const result: Record<string, string | number | boolean | null> = {};
  const allMetrics = metricsData.value;
  
  // 处理所有指标
  Object.entries(allMetrics).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      // 跳过数组类型的指标
      return;
    } else if (typeof value === "number") {
      // 根据指标名称应用不同的格式化规则
      if (
        key.includes("return") ||
        key.includes("alpha") ||
        key.includes("beta") ||
        key.includes("drawdown") ||
        key.includes("capture") ||
        key.includes("contribution")
      ) {
        result[key] = `${(value * 100).toFixed(2)}%`;
      } else if (
        key.includes("ratio") ||
        key.includes("sharpe") ||
        key.includes("sortino") ||
        key.includes("calmar")
      ) {
        result[key] = value.toFixed(3);
      } else {
        result[key] = value.toFixed(2);
      }
    } else if (value === null) {
      result[key] = "N/A";
    } else if (typeof value === "string" || typeof value === "boolean") {
      result[key] = value;
    } else {
      result[key] = String(value);
    }
  });
  
  return result;
});

// 格式化标签
const formatLabel = (key: string) => {
  return key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
};
</script>

<style lang="scss" scoped>
.metrics-object-block {
  .metrics-container {
    margin-bottom: 20px;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;

    .metric-item {
      padding: 12px;
      border-radius: 4px;
      background-color: #f5f7fa;
      transition: all 0.3s ease;
      border: 1px solid #ebeef5;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      .metric-label {
        font-size: 12px;
        color: #909399;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .metric-value {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

  .empty-metrics {
    padding: 20px;
    text-align: center;
  }
}
</style>
