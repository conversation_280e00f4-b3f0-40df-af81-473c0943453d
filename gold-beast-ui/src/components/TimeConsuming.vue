<template>{{ timer_text }}</template>

<script lang="ts" setup>
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(relativeTime);

import { computed, onBeforeUnmount, onMounted, ref } from "vue";

const props = defineProps<{
  modelValue: Date | string;
}>();

const now = ref(Date.now());

onMounted(() => {
  const timer = setInterval(() => {
    now.value = Date.now();
  }, 1000);

  onBeforeUnmount(() => {
    clearInterval(timer);
  });
});

const timer_text = computed(() => {
  return dayjs(props.modelValue).fromNow();
});
</script>
