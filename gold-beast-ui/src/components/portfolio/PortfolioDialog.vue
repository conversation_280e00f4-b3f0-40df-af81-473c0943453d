<template>
  <el-dialog
    :title="isEdit ? '编辑投资组合' : '新建投资组合'"
    v-model="dialogVisible"
    width="650px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      size="default"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入投资组合名称" />
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="2"
          placeholder="请输入投资组合描述"
        />
      </el-form-item>

      <el-form-item label="模型类型">
        <el-select
          v-model="formData.model_type"
          placeholder="请选择模型类型"
          style="width: 100%"
        >
          <el-option label="MH" value="MH" />
          <el-option label="SH" value="SH" />
          <el-option label="MH-S" value="MH-S" />
          <el-option label="SH-S" value="SH-S" />
        </el-select>
      </el-form-item>


      <el-divider>股票列表</el-divider>

      <div class="stocks-header">
        <h4>股票列表</h4>
        <el-button size="small" @click="addStock">添加股票</el-button>
      </div>

      <el-table :data="formData.stocks" style="width: 100%" border>
        <el-table-column label="股票代码">
          <template #default="scope">
            <el-input
              v-model="scope.row.ticker"
              placeholder="输入股票代码"
            />
          </template>
        </el-table-column>
        <el-table-column label="权重">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.weight"
              :min="0"
              :max="1"
              :step="0.01"
              :precision="2"
              style="width: 120px"
              controls-position="right"
            />
          </template>
        </el-table-column>
        <el-table-column width="90" label="操作">
          <template #default="scope">
            <el-button
              type="danger"
              size="small"
              @click="removeStock(scope.$index)"
              :disabled="formData.stocks.length <= 1"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, defineModel, defineProps, watch } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import type { Portfolio, PortfolioCreateUpdate, PortfolioStock } from "@/api/portfolioService";

// Props
const props = defineProps<{
  loading?: boolean;
}>();

// Models
const dialogVisible = defineModel<boolean>("visible");
const portfolio = defineModel<Portfolio | null>("portfolio");

// Form ref
const formRef = ref<FormInstance>();

// Form data
const formData = reactive<PortfolioCreateUpdate>({
  name: "",
  description: "",
  model_type: "MH",
  stocks: [
    { ticker: "", weight: 1.0 },
  ],
});

// Form rules
const rules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入投资组合名称", trigger: "blur" },
    { min: 2, max: 50, message: "长度在 2 到 50 个字符", trigger: "blur" },
  ],
});

// Computed
const isEdit = ref(false);

// Methods
const resetForm = () => {
  formData.name = "";
  formData.description = "";
  formData.model_type = "MH";
  formData.stocks = [{ ticker: "", weight: 1.0 }];
};

// Watch portfolio changes
watch(
  portfolio,
  (newVal) => {
    if (newVal) {
      isEdit.value = true;
      // Fill form data
      formData.name = newVal.name;
      formData.description = newVal.description || "";
      formData.model_type = newVal.model_type;
      formData.stocks = [...newVal.stocks];
    } else {
      isEdit.value = false;
      resetForm();
    }
  },
  { immediate: true }
);

const addStock = () => {
  formData.stocks.push({ ticker: "", weight: 0.0 });
  rebalanceWeights();
};

const removeStock = (index: number) => {
  formData.stocks.splice(index, 1);
  rebalanceWeights();
};

const rebalanceWeights = () => {
  const count = formData.stocks.length;
  if (count > 0) {
    const weight = parseFloat((1 / count).toFixed(2));
    formData.stocks.forEach((stock) => {
      stock.weight = weight;
    });
  }
};

const validateForm = async (): Promise<boolean> => {
  if (!formRef.value) return false;

  try {
    await formRef.value.validate();

    // Validate stocks
    if (formData.stocks.length === 0) {
      ElMessage.warning("请添加至少一只股票");
      return false;
    }

    // Validate stock tickers
    for (const stock of formData.stocks) {
      if (!stock.ticker) {
        ElMessage.warning("请输入所有股票的代码");
        return false;
      }
      if (stock.weight <= 0) {
        ElMessage.warning("所有股票的权重必须大于0");
        return false;
      }
    }

    // Validate total weight
    const totalWeight = formData.stocks.reduce(
      (sum, stock) => sum + stock.weight,
      0
    );
    if (Math.abs(totalWeight - 1) > 0.01) {
      ElMessage.warning(`权重总和应为1，当前为${totalWeight.toFixed(2)}`);
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
};

const handleSubmit = async () => {
  const valid = await validateForm();
  if (!valid) return;

  // Emit submit event with form data
  const submitEvent = isEdit.value ? "update" : "create";
  dialogVisible.value = false;
  emit(submitEvent, { ...formData });
};

const handleClosed = () => {
  formRef.value?.resetFields();
  resetForm();
  isEdit.value = false;
  emit("closed");
};

// Define emits
const emit = defineEmits<{
  (e: "create", portfolio: PortfolioCreateUpdate): void;
  (e: "update", portfolio: PortfolioCreateUpdate): void;
  (e: "closed"): void;
}>();
</script>

<style lang="scss" scoped>
.stocks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  h4 {
    margin: 0;
    font-weight: 500;
  }
}
</style>
