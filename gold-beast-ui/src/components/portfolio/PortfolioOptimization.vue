<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="handleVisibleChange"
    title="投资组合优化分析"
    direction="rtl"
    size="80%"
    :before-close="handleClose"
  >
    <div class="portfolio-optimization-container">
      <!-- 投资组合构建区域 -->
      <el-card class="portfolio-builder" size="small">
        <template #header>
          <h4>投资组合构建</h4>
        </template>

        <!-- 添加股票 -->
        <div class="add-stock-section">
          <CompanyMultiSelect
            v-model="selectedStocks"
            size="small"
            placeholder="搜索并选择股票..."
            @change="handleStockSelection"
          />
        </div>

        <!-- 投资组合表格 -->
        <div class="portfolio-table-section">
          <el-table :data="portfolio" size="small" border>
            <el-table-column prop="symbol" label="股票" width="100">
              <template #default="scope">
                <div>
                  <div style="font-weight: bold; font-size: 12px;">{{ getStockName(scope.row.symbol) }}</div>
                  <div style="color: #909399; font-size: 11px;">{{ scope.row.symbol }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="权重 (%)" width="150">
              <template #default="scope">
                <el-slider
                  v-model="scope.row.weight"
                  :min="0"
                  :max="100"
                  :step="1"
                  size="small"
                  @change="handleWeightChange"
                />
                <div style="text-align: center; font-size: 11px; margin-top: 4px;">
                  {{ scope.row.weight }}%
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="60">
              <template #default="scope">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeStock(scope.row.symbol)"
                  :icon="Delete"
                  circle
                />
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 总权重显示 -->
        <div class="total-weight-section">
          <el-alert
            :title="`总权重: ${getTotalWeight()}% / 100%`"
            :type="getTotalWeight() === 100 ? 'success' : 'warning'"
            :closable="false"
            show-icon
          >
            <template v-if="getTotalWeight() !== 100">
              请调整权重使总和为100%
            </template>
          </el-alert>
        </div>
      </el-card>

      <!-- 分析控制区域 -->
      <el-card class="analysis-controls" size="small">
        <template #header>
          <h4>分析与优化</h4>
        </template>

        <!-- 时间范围选择 -->
        <el-form-item label="分析时间范围">
          <el-select v-model="timeRange" size="small">
            <el-option label="30天" :value="30" />
            <el-option label="90天" :value="90" />
            <el-option label="6个月" :value="180" />
            <el-option label="1年" :value="365" />
            <el-option label="2年" :value="730" />
          </el-select>
        </el-form-item>

        <!-- 分析按钮 -->
        <div class="analysis-buttons">
          <el-button
            type="primary"
            @click="analyzePortfolio"
            :loading="analyzing"
            :disabled="!canAnalyze"
            size="small"
            style="width: 100%; margin-bottom: 8px;"
          >
            {{ analyzing ? '分析中...' : '分析当前组合' }}
          </el-button>
          <el-button
            type="success"
            @click="optimizePortfolio"
            :loading="optimizing"
            :disabled="!canAnalyze"
            size="small"
            style="width: 100%;"
          >
            {{ optimizing ? '优化中...' : '优化投资组合' }}
          </el-button>
        </div>

        <!-- 状态信息 -->
        <div class="status-info">
          <el-alert
            :title="analysisStatus"
            :type="getStatusType()"
            :closable="false"
            show-icon
          />
          <div class="portfolio-info">
            <p>股票数量: {{ portfolio.length }}/10</p>
            <p>权重总和: {{ getTotalWeight() }}%</p>
            <p>分析时间范围: {{ timeRange }}天</p>
          </div>
        </div>
      </el-card>

      <!-- 分析结果区域 -->
      <div v-if="analysisResult" class="analysis-results">
        <!-- 组合指标 -->
        <el-card class="metrics-card" size="small">
          <template #header>
            <h4>组合风险收益指标</h4>
          </template>
          <el-descriptions :column="3" size="small" border>
            <el-descriptions-item label="预期收益率">
              <span :style="{ color: analysisResult.portfolio_return > 0 ? '#67c23a' : '#f56c6c', fontWeight: 'bold' }">
                {{ (analysisResult.portfolio_return * 100).toFixed(2) }}%
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="组合波动率">
              {{ (analysisResult.portfolio_volatility * 100).toFixed(2) }}%
            </el-descriptions-item>
            <el-descriptions-item label="夏普比率">
              <span :style="{
                color: analysisResult.sharpe_ratio > 1 ? '#67c23a' :
                       analysisResult.sharpe_ratio > 0 ? '#e6a23c' : '#f56c6c',
                fontWeight: 'bold'
              }">
                {{ analysisResult.sharpe_ratio.toFixed(3) }}
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="95% VaR">
              <span style="color: #f56c6c;">
                {{ (analysisResult.var_95 * 100).toFixed(2) }}%
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="最大回撤">
              <span style="color: #f56c6c;">
                {{ (analysisResult.max_drawdown * 100).toFixed(2) }}%
              </span>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 指标说明 -->
          <div class="metrics-explanation">
            <el-alert
              title="指标说明"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <div class="explanation-content">
                  <p><strong>夏普比率:</strong> >1为优秀，0-1为良好，<0为较差</p>
                  <p><strong>VaR:</strong> 在95%置信度下的最大损失</p>
                  <p><strong>Beta:</strong> 相对市场的风险系数</p>
                  <p><strong>最大回撤:</strong> 历史上从峰值到谷值的最大跌幅</p>
                </div>
              </template>
            </el-alert>
          </div>
        </el-card>

        <!-- 个股贡献 -->
        <el-card class="contribution-card" size="small">
          <template #header>
            <h4>个股风险收益贡献</h4>
          </template>
          <el-table :data="analysisResult.stock_data" size="small" border>
            <el-table-column prop="symbol" label="股票" width="80">
              <template #default="scope">
                <strong style="font-size: 11px;">{{ scope.row.symbol }}</strong>
              </template>
            </el-table-column>
            <el-table-column prop="weight" label="权重" align="center" width="60">
              <template #default="scope">
                {{ scope.row.weight }}%
              </template>
            </el-table-column>
            <el-table-column prop="beta" label="Beta" align="center" width="60">
              <template #default="scope">
                {{ scope.row.beta.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="contribution_to_return" label="收益贡献" align="center" width="80">
              <template #default="scope">
                <span :style="{
                  color: scope.row.contribution_to_return > 0 ? '#67c23a' : '#f56c6c',
                  fontWeight: 'bold'
                }">
                  {{ (scope.row.contribution_to_return * 100).toFixed(2) }}%
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <!-- 优化结果区域 -->
      <div v-if="optimizationResult" class="optimization-results">
        <el-card class="optimization-card" size="small">
          <template #header>
            <h4>投资组合优化建议</h4>
            <p style="margin: 4px 0 0 0; font-size: 12px; color: #909399;">
              优化目标: 最大化夏普比率 |
              预期收益提升: {{ (optimizationResult.expected_improvement.return_improvement * 100).toFixed(2) }}% |
              风险降低: {{ (optimizationResult.expected_improvement.risk_reduction * 100).toFixed(2) }}%
            </p>
          </template>

          <el-table :data="optimizationResult.optimized_weights" size="small" border>
            <el-table-column prop="symbol" label="股票" width="100">
              <template #default="scope">
                <div>
                  <div style="font-weight: bold; font-size: 12px;">{{ scope.row.name }}</div>
                  <div style="color: #909399; font-size: 11px;">{{ scope.row.symbol }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="current_weight" label="当前权重" align="center" width="80">
              <template #default="scope">
                {{ scope.row.current_weight.toFixed(1) }}%
              </template>
            </el-table-column>
            <el-table-column prop="optimal_weight" label="建议权重" align="center" width="80">
              <template #default="scope">
                <strong>{{ scope.row.optimal_weight.toFixed(1) }}%</strong>
              </template>
            </el-table-column>
            <el-table-column prop="weight_change" label="调整幅度" align="center" width="80">
              <template #default="scope">
                <span :style="{
                  color: scope.row.weight_change > 0 ? '#67c23a' :
                         scope.row.weight_change < 0 ? '#f56c6c' : '#606266',
                  fontWeight: 'bold'
                }">
                  {{ scope.row.weight_change > 0 ? '+' : '' }}{{ scope.row.weight_change.toFixed(1) }}%
                </span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 优化效果预期 -->
          <el-alert
            title="优化效果预期"
            type="success"
            :closable="false"
            style="margin-top: 16px;"
          >
            <template #default>
              <div style="font-size: 12px;">
                <p>• 收益率提升: +{{ (optimizationResult.expected_improvement.return_improvement * 100).toFixed(2) }}%</p>
                <p>• 风险降低: -{{ (optimizationResult.expected_improvement.risk_reduction * 100).toFixed(2) }}%</p>
                <p>• 夏普比率提升: +{{ optimizationResult.expected_improvement.sharpe_improvement.toFixed(3) }}</p>
              </div>
            </template>
          </el-alert>
        </el-card>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import CompanyMultiSelect from '../CompanyMultiSelect.vue'

// Props
interface Props {
  visible: boolean
  portfolioStocks: Array<{ticker: string, weight: number}>
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  portfolioStocks: () => []
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const selectedStocks = ref<string[]>([])
const portfolio = ref<Array<{symbol: string, weight: number}>>([])
const timeRange = ref(365)
const analyzing = ref(false)
const optimizing = ref(false)
const analysisStatus = ref('准备分析')
const analysisResult = ref<any>(null)
const optimizationResult = ref<any>(null)

// 默认股票列表
const defaultStocks = [
  { symbol: 'AAPL', name: '苹果公司' },
  { symbol: 'MSFT', name: '微软' },
  { symbol: 'GOOGL', name: '谷歌' },
  { symbol: 'AMZN', name: '亚马逊' },
  { symbol: 'TSLA', name: '特斯拉' },
  { symbol: 'META', name: 'Meta' },
  { symbol: 'NVDA', name: '英伟达' },
  { symbol: 'NFLX', name: '奈飞' },
  { symbol: 'JPM', name: '摩根大通' },
  { symbol: 'V', name: 'Visa' }
]

// 计算属性
const canAnalyze = computed(() => {
  return portfolio.value.length >= 2 && getTotalWeight() === 100
})

// 监听 props 变化
watch(() => props.portfolioStocks, (newStocks) => {
  if (newStocks && newStocks.length > 0) {
    portfolio.value = newStocks.map(stock => ({
      symbol: stock.ticker,
      weight: stock.weight * 100 // 转换为百分比
    }))
    // 同步更新 selectedStocks
    selectedStocks.value = portfolio.value.map(p => p.symbol)
  }
}, { immediate: true })

// 监听 portfolio 变化，同步更新 selectedStocks
watch(portfolio, (newPortfolio) => {
  selectedStocks.value = newPortfolio.map(p => p.symbol)
}, { deep: true })

// 方法
const getStockName = (symbol: string): string => {
  const stock = defaultStocks.find(s => s.symbol === symbol)
  return stock?.name || symbol
}

const getTotalWeight = (): number => {
  return Math.round(portfolio.value.reduce((sum, item) => sum + item.weight, 0))
}

const getStatusType = () => {
  if (analyzing.value || optimizing.value) return 'info'
  if (analysisStatus.value.includes('✅')) return 'success'
  if (analysisStatus.value.includes('❌')) return 'error'
  return 'info'
}

const handleStockSelection = () => {
  // 处理多选股票的变化
  const newStocks = selectedStocks.value.filter(symbol =>
    !portfolio.value.some(p => p.symbol === symbol)
  )

  // 检查股票数量限制
  if (portfolio.value.length + newStocks.length > 10) {
    ElMessage.warning('最多只能添加10只股票')
    // 移除超出限制的股票
    selectedStocks.value = selectedStocks.value.slice(0, 10 - portfolio.value.length + portfolio.value.length)
    return
  }

  // 添加新选择的股票
  newStocks.forEach(symbol => {
    const remainingWeight = Math.max(0, 100 - getTotalWeight())
    const weightPerStock = newStocks.length > 0 ? Math.floor(remainingWeight / newStocks.length) : 0

    portfolio.value.push({
      symbol,
      weight: weightPerStock
    })
  })

  // 移除取消选择的股票
  const removedStocks = portfolio.value.filter(p =>
    !selectedStocks.value.includes(p.symbol)
  )
  removedStocks.forEach(stock => {
    removeStock(stock.symbol)
  })

  clearResults()
}

const removeStock = (symbol: string) => {
  portfolio.value = portfolio.value.filter(p => p.symbol !== symbol)
  clearResults()
}

const handleWeightChange = () => {
  clearResults()
}

const clearResults = () => {
  analysisResult.value = null
  optimizationResult.value = null
}

const analyzePortfolio = async () => {
  if (!canAnalyze.value) {
    ElMessage.warning('请确保至少有2只股票且权重总和为100%')
    return
  }

  analyzing.value = true
  analysisStatus.value = '🔄 正在分析真实投资组合数据...'
  analysisResult.value = null

  try {
    const portfolioData = portfolio.value.map(p => ({
      symbol: p.symbol,
      name: getStockName(p.symbol),
      weight: p.weight
    }))

    const response = await fetch('/gbs-api/v1/analyze-portfolio-simple', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        portfolio: portfolioData,
        days: timeRange.value
      })
    })

    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.detail || '分析失败')
    }

    analysisResult.value = data
    analysisStatus.value = `✅ 投资组合分析完成！夏普比率: ${data.sharpe_ratio.toFixed(3)}`
    ElMessage.success('投资组合分析完成')
  } catch (error: any) {
    console.error('投资组合分析失败:', error)
    analysisStatus.value = `❌ 分析失败: ${error.message}`
    ElMessage.error(`分析失败: ${error.message}`)
  } finally {
    analyzing.value = false
  }
}

const optimizePortfolio = async () => {
  if (!canAnalyze.value) {
    ElMessage.warning('请确保至少有2只股票且权重总和为100%')
    return
  }

  optimizing.value = true
  analysisStatus.value = '🔄 正在优化投资组合...'
  optimizationResult.value = null

  try {
    const portfolioData = portfolio.value.map(p => ({
      symbol: p.symbol,
      name: getStockName(p.symbol),
      weight: p.weight
    }))

    const response = await fetch('/gbs-api/v1/portfolio/optimize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        portfolio: portfolioData,
        days: timeRange.value
      })
    })

    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.detail || '优化失败')
    }

    // 模拟优化结果（因为后端可能返回复杂的优化数据）
    const mockOptimization = generateMockOptimization()
    optimizationResult.value = mockOptimization

    analysisStatus.value = `✅ 投资组合优化完成！预期收益提升: ${(mockOptimization.expected_improvement.return_improvement * 100).toFixed(2)}%`
    ElMessage.success('投资组合优化完成')
  } catch (error: any) {
    console.error('投资组合优化失败:', error)
    analysisStatus.value = `❌ 优化失败: ${error.message}`
    ElMessage.error(`优化失败: ${error.message}`)
  } finally {
    optimizing.value = false
  }
}

const generateMockOptimization = () => {
  const optimizedWeights = portfolio.value.map(stock => {
    const currentWeight = stock.weight
    const optimalWeight = Math.max(0, Math.min(100, currentWeight + (Math.random() - 0.5) * 20))
    return {
      symbol: stock.symbol,
      name: getStockName(stock.symbol),
      current_weight: currentWeight,
      optimal_weight: optimalWeight,
      weight_change: optimalWeight - currentWeight
    }
  })

  // 确保优化后权重总和为100%
  const totalOptimalWeight = optimizedWeights.reduce((sum, stock) => sum + stock.optimal_weight, 0)
  optimizedWeights.forEach(stock => {
    stock.optimal_weight = (stock.optimal_weight / totalOptimalWeight) * 100
    stock.weight_change = stock.optimal_weight - stock.current_weight
  })

  return {
    optimized_weights: optimizedWeights,
    expected_improvement: {
      return_improvement: Math.random() * 0.02 + 0.005,
      risk_reduction: Math.random() * 0.03 + 0.01,
      sharpe_improvement: Math.random() * 0.3 + 0.1
    }
  }
}

const handleVisibleChange = (value: boolean) => {
  emit('update:visible', value)
}

const handleClose = () => {
  emit('update:visible', false)
}

// 初始化完成
</script>

<style lang="scss" scoped>
.portfolio-optimization-container {
  padding: 16px;

  .portfolio-builder, .analysis-controls {
    margin-bottom: 16px;

    h4 {
      margin: 0;
      font-size: 14px;
      color: #303133;
    }
  }

  .add-stock-section {
    margin-bottom: 16px;
  }

  .portfolio-table-section {
    margin-bottom: 16px;
  }

  .total-weight-section {
    margin-bottom: 16px;
  }

  .analysis-buttons {
    margin-bottom: 16px;
  }

  .status-info {
    .portfolio-info {
      margin-top: 8px;
      padding: 8px;
      background-color: #f5f7fa;
      border-radius: 4px;

      p {
        margin: 2px 0;
        font-size: 12px;
        color: #606266;
      }
    }
  }

  .analysis-results, .optimization-results {
    .metrics-card, .contribution-card, .optimization-card {
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 14px;
        color: #303133;
      }

      .metrics-explanation {
        margin-top: 16px;

        .explanation-content {
          p {
            margin: 4px 0;
            font-size: 12px;
            line-height: 1.4;

            strong {
              color: #409eff;
            }
          }
        }
      }
    }
  }
}
</style>
