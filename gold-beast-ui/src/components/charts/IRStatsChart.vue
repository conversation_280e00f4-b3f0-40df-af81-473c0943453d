<template>
  <div class="ir-stats-chart">
    <h4 class="chart-title">IR统计分布</h4>
    <div class="chart-container" ref="chartRef"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';
import { useEchartResize } from '@/composables/useEchartResize';

interface IRStats {
  mean_ir: number;
  median_ir: number;
  max_ir: number;
  min_ir: number;
}

interface Props {
  irStats: IRStats;
}

const props = defineProps<Props>();

const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;
let resizeManager: { resizeChart: () => void } | null = null;

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  if (chart) {
    chart.dispose();
  }

  chart = echarts.init(chartRef.value);
  renderChart();

  // 初始化响应式调整
  if (chart) {
    resizeManager = useEchartResize(chart);
  }
};

// 渲染图表
const renderChart = () => {
  if (!chart || !props.irStats) return;

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#409EFF',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '15%',
      right: '15%',
      top: '10%',
      bottom: '15%'
    },
    xAxis: {
      type: 'category',
      data: ['最小IR', '平均IR', '中位IR', '最大IR'],
      axisLabel: {
        fontSize: 12,
        color: '#606266'
      },
      axisLine: {
        lineStyle: {
          color: '#DCDFE6'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 12,
        color: '#606266',
        formatter: (value: number) => value.toFixed(3)
      },
      axisLine: {
        lineStyle: {
          color: '#DCDFE6'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#F2F6FC',
          type: 'dashed'
        }
      }
    },
    series: [{
      type: 'bar',
      data: [
        {
          value: props.irStats.min_ir,
          itemStyle: {
            color: props.irStats.min_ir >= 0 ? '#52C41A' : '#FF4D4F', // 更专业的绿色和红色
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          value: props.irStats.mean_ir,
          itemStyle: {
            color: props.irStats.mean_ir >= 0 ? '#52C41A' : '#FF4D4F',
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          value: props.irStats.median_ir,
          itemStyle: {
            color: props.irStats.median_ir >= 0 ? '#52C41A' : '#FF4D4F',
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          value: props.irStats.max_ir,
          itemStyle: {
            color: props.irStats.max_ir >= 0 ? '#52C41A' : '#FF4D4F',
            borderRadius: [4, 4, 0, 0]
          }
        }
      ],
      label: {
        show: true,
        position: 'top',
        formatter: (params: any) => params.value.toFixed(3),
        fontSize: 11,
        color: '#303133',
        fontWeight: 500
      },
      barWidth: '60%'
    }]
  };

  chart.setOption(option);
};

// 监听数据变化
watch(() => props.irStats, () => {
  renderChart();
}, { deep: true });

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  resizeManager = null;
});
</script>

<style lang="scss" scoped>
.ir-stats-chart {
  .chart-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 16px;
    text-align: center;
  }

  .chart-container {
    height: 300px;
    width: 100%;
    border-radius: 8px;
    border: 1px solid var(--el-border-color-light);
    background: #fff;
  }
}
</style>
