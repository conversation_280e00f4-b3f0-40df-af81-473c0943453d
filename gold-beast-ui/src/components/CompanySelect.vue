<template>
  <el-select-v2
    style="width: 100%"
    v-model="value"
    :size="props.size || 'default'"
    :options="filter_companies"
    :filterable="true"
    value-key="ticker"
    :placeholder="props.placeholder || 'Company name'"
    default-first-option
    @change="onChange"
    :teleported="true"
    :loading="loading"
    :item-height="50"
    :height="400"
    :reserve-keyword="false"
    popper-class="doc-viewer-company-select-popper"
    :filter-method="filterMethod"
    ref="companySelectRef"
  >
    <template #default="{ item }">
      <div class="doc-viewer-company-select">
        <span class="company-logo-wrapper">
          <CompanyLogo :key="item.value.ticker" :ticker="item.value.ticker" />
        </span>
        <div>
          <div class="label">{{ item.value.long_name }}</div>
          <div class="ticker">{{ item.value.ticker }}</div>
        </div>
      </div>
    </template>
  </el-select-v2>
</template>

<script lang="ts" setup>
import { type Ref, onMounted, ref, watch } from "vue";
import { ElSelectV2 } from "element-plus";
import { getCompanies, processCompanyList } from "../api/companies";
import CompanyLogo from "./CompanyLogo.vue";
import { on$ } from "../utils/subject";
import { useCompanyDashboardStore } from "../stores/company";
const loading = ref(false);
const companySelectRef = ref() as Ref<InstanceType<typeof ElSelectV2>>;

const props = defineProps<{
  size?: "small" | "large" | "default";
  placeholder?: string;
  teleported?: boolean;
}>();

// 使用 Pinia store
const dashboardStore = useCompanyDashboardStore();

// 使用 defineModel 创建双向绑定
const modelValue = defineModel<string>();

// 内部值，用于绑定到 el-select-v2
const value = ref();

// 保存等待处理的 ticker 值
const pendingTicker = ref<string | undefined>();

// 公司列表
const companies: Ref<
  {
    label: string;
    value: {
      ticker: string;
      industry: string;
      long_name: string;
    };
  }[]
> = ref([]);

// 监听 modelValue 变化，更新内部 value
watch(
  modelValue,
  (newVal) => {
    if (newVal !== undefined) {
      // 如果外部传入的是字符串，需要找到对应的公司对象
      if (companies.value.length > 0) {
        const company = companies.value.find(item => item.value.ticker === newVal);
        if (company) {
          value.value = company.value;
        } else {
          // 如果找不到对应的公司，可能是公司列表还没加载完成
          // 保存 ticker 值，等公司列表加载完成后再处理
          pendingTicker.value = newVal;
        }
      } else {
        // 公司列表还没加载，保存 ticker 值
        pendingTicker.value = newVal;
      }
    } else {
      value.value = undefined;
    }
  },
  { immediate: true }
);

const filter_companies = ref<
  {
    label: string;
    value: {
      ticker: string;
      industry: string;
      long_name: string;
    };
  }[]
>([]);

function filterMethod(query: string) {
  query = query.trim().toUpperCase();
  if (query === "") {
    filter_companies.value = companies.value;
    return;
  }
  let arr = companies.value.filter((company) => {
    return company.value.ticker.includes(query);
  });
  arr.sort((a, b) => {
    if (a.value.ticker === query) return -1;
    if (b.value.ticker === query) return 1;
    return 0;
  });
  if (arr.length === 0) {
    arr = companies.value.filter((company) =>
      company.value.long_name.toUpperCase().includes(query)
    );
  }
  filter_companies.value = arr;
}

const emit = defineEmits<{
  (e: "change"): void;
}>();

function onChange() {
  emit("change");
  // 当选择变化时，直接更新 modelValue
  if (value.value) {
    modelValue.value = value.value.ticker;

    // 更新最近选择的公司列表
    dashboardStore.setSelectedCompany(
      value.value.ticker,
      value.value.long_name
    );
  } else {
    modelValue.value = "";
  }
}

async function load() {
  loading.value = true;
  getCompanies().subscribe({
    next: (list) => {
      loading.value = false;
      if (list.length === 0) {
        return;
      }
      companies.value = processCompanyList(list);
      filter_companies.value = companies.value;

      // 处理待处理的 ticker 值
      if (pendingTicker.value) {
        const company = companies.value.find(item => item.value.ticker === pendingTicker.value);
        if (company) {
          value.value = company.value;
          pendingTicker.value = undefined;
        }
      }
    },
  });
}

onMounted(() => {
  load();
  const sub = on$("NOTIFY_SUCCESS", () => {
    load();
  });
  return () => {
    sub.unsubscribe();
  };
});

defineExpose({
  instance: companySelectRef,
});
</script>

<style lang="scss" setup>
.doc-viewer-company-select-popper {
  .el-select-dropdown,
  .el-vl__window.el-select-dropdown__list {
    width: 300px !important;
  }
}

.doc-viewer-company-select {
  display: flex;
  align-items: center;
  line-height: normal;
  height: 50px;
  .label {
    font-size: 100%;
    font-weight: 500;
  }
  .ticker {
    font-size: 90%;
  }
  .company-logo-wrapper {
    box-sizing: content-box;
    margin-right: 10px;
    width: 30px;
    height: 30px;
    flex: 0 0 30px;
    display: inline-flex;
    background: rgba(255, 255, 255, 1);
    border-radius: 50% 50%;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    img {
      width: 22px;
      height: 22px;
    }
  }
}
</style>
