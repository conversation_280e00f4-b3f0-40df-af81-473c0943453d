import axios from "axios";

// 策略列表接口
export interface Strategy {
  strategy_id: string;
  strategy_name: string;
  description: string;
  tags: string[];
  created_at: string;
  last_updated: string;
  backtest_count: number;
  latest_backtest_id: string | null;
  strategy_type: string;
  file_id?: string;
  experiment_name?: string;
}

// 策略详情接口
export interface StrategyDetail extends Strategy {
  parameters: Record<string, any>;
  risk_management: Record<string, any>;
  model: Record<string, any>;
  data: Record<string, any>;
  backtests: BacktestInfo[];
  ui_configs: any;
}

// 回测信息接口
export interface BacktestInfo {
  backtest_id: string;
  strategy_id: string;
  strategy_name: string;
  start_date: string;
  end_date: string;
  created_at: string;
  tags: string[];
}

// 策略历史运行记录接口
export interface StrategyRunInfo {
  run_id: string;
  experiment_id: string;
  experiment_name: string;
  run_name?: string;
  start_time: string;
  end_time: string;
  duration: number;
  workflow_file: string;
  strategy_class: string;
  backtest_period: string;
  instruments: string[];
  artifact_path: string;
  tags?: Record<string, string>;
  metrics?: Record<string, number>;
}

// 策略运行详情接口
export interface StrategyRunDetail {
  status: string;
  run_id: string;
  experiment_id: string;
  experiment_name: string;
  start_time: string;
  end_time: string;
  duration: number;
  artifact_path: string;
  params: Record<string, string>;
  metrics: Record<string, string>;
  tags: Record<string, string>;
  analysis_results: any[];
}

// 获取策略列表
export const getStrategies = async (): Promise<Strategy[]> => {
  try {
    const response = await axios.get(`/gbs-api/v1/strategies`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch strategies:", error);
    throw error;
  }
};

// 获取策略详情
export const getStrategyById = async (id: string): Promise<StrategyDetail> => {
  try {
    const response = await axios.get(`/gbs-api/v1/strategies/${id}`);
    const strategy = response.data;

    return strategy;
  } catch (error) {
    console.error(`Failed to fetch strategy ${id}:`, error);
    throw error;
  }
};

// 策略历史筛选参数接口
export interface StrategyHistoryParams {
  strategy_name?: string;
  strategy_class?: string;
  start_date?: string;
  end_date?: string;
  limit?: number;
}

// 获取策略历史运行记录
export const getStrategyHistory = async (
  strategyName?: string,
  limit: number = 100,
  params?: StrategyHistoryParams
): Promise<StrategyRunInfo[]> => {
  try {
    // 构建查询参数
    const queryParams: Record<string, string> = { limit: limit.toString() };

    // 兼容旧的调用方式
    if (strategyName) {
      queryParams.strategy_name = strategyName;
    }

    // 如果提供了新的参数对象，使用它
    if (params) {
      if (params.strategy_name) queryParams.strategy_name = params.strategy_name;
      if (params.strategy_class) queryParams.strategy_class = params.strategy_class;
      if (params.start_date) queryParams.start_date = params.start_date;
      if (params.end_date) queryParams.end_date = params.end_date;
      if (params.limit) queryParams.limit = params.limit.toString();
    }

    // 构建URL
    const queryString = Object.entries(queryParams)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');

    const url = `/gbs-api/v1/mlflows/history?${queryString}`;

    const response = await axios.get(url);
    return response.data.runs || [];
  } catch (error) {
    console.error("Failed to fetch strategy history:", error);
    throw error;
  }
};

// 获取策略运行详情
export const getStrategyRunDetail = async (
  runId: string,
  experimentId: string
): Promise<StrategyRunDetail> => {
  try {
    const url = `/gbs-api/v1/mlflows/run-detail?run_id=${runId}&experiment_id=${experimentId}`;
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error(`Failed to fetch run detail for ${runId}:`, error);
    throw error;
  }
};

// 策略元数据接口
export interface StrategyMetadata {
  status: string;
  strategy_names: Array<{value: string, label: string}>;
  strategy_classes: Array<{value: string, label: string}>;
}

// 获取策略元数据
export const getStrategyMetadata = async (): Promise<StrategyMetadata> => {
  try {
    const response = await axios.get(`/gbs-api/v1/mlflows/metadata`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch strategy metadata:", error);
    throw error;
  }
};

// 删除策略运行记录
export const deleteStrategyRun = async (
  experimentId: string,
  runId: string
): Promise<any> => {
  try {
    const response = await axios.delete(`/gbs-api/v1/mlflows/runs/${experimentId}/${runId}`);
    return response.data;
  } catch (error) {
    console.error(`Failed to delete strategy run ${runId}:`, error);
    throw error;
  }
};

// 更新运行名称请求接口
export interface UpdateRunNameRequest {
  experiment_id: string;
  run_id: string;
  new_name: string;
}

// 更新运行名称响应接口
export interface UpdateRunNameResponse {
  status: string;
  message: string;
  run_id: string;
  experiment_id: string;
  new_name: string;
}

// 更新策略运行名称
export const updateRunName = async (
  experimentId: string,
  runId: string,
  newName: string
): Promise<UpdateRunNameResponse> => {
  try {
    const response = await axios.post(`/gbs-api/v1/mlflows/update-run-name`, {
      experiment_id: experimentId,
      run_id: runId,
      new_name: newName
    });
    return response.data;
  } catch (error) {
    console.error(`Failed to update run name for ${runId}:`, error);
    throw error;
  }
};
