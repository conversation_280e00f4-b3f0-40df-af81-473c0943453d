import { Observable, from, of } from "rxjs";
import { catchError, map, shareReplay } from "rxjs/operators";
import { ticker_to_company_name } from "../store";
import axios from "axios";

// 创建axios实例
const apiClient = axios.create({
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

export interface CompanyDetails {
  name: string;
  ticker: string;
  industry: string;
}
export type CompanyList = Array<[string, CompanyDetails]>;

export function processCompanyList(list: CompanyList) {
  return list
    .filter(([_, company]) => company?.ticker && company.ticker.length > 0)
    .map(([ticker, { name, industry }]) => {
      return {
        label: `(${ticker})  ${name} `,
        value: {
          ticker: ticker.toUpperCase(),
          industry: industry,
          long_name: name,
          type: "company",
        },
      };
    });
}

let cacheCompanies$: Observable<CompanyList> | null = null;

export function getCompanies() {
  if (!cacheCompanies$) {
    cacheCompanies$ = from(apiClient.get("/gbs-api/v1/companies/")).pipe(
      map((response) => {
        ticker_to_company_name.value = response.data.data.reduce(
          (
            acc: Record<string, string>,
            [ticker, company]: [string, CompanyDetails]
          ) => {
            if (company) {
              acc[ticker] = company.name;
            }
            return acc;
          },
          {}
        );

        return response.data.data;
      }),
      catchError((error) => {
        cacheCompanies$ = null;
        console.error("Failed to fetch companies", error);
        return of([]);
      }),
      shareReplay(1)
    );
  }
  return cacheCompanies$;
}
