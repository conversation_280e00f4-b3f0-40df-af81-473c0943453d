import { defineStore } from 'pinia';
import { ref, reactive, computed } from 'vue';
import { calculateAssetCovariance } from '@/api/factorService';
import { ElMessage } from 'element-plus';
import { useCompanyDashboardStore } from './companyDashboard';

/**
 * Company Asset Covariance Store
 * 
 * This store manages the state for the CompanyAssetCovariance component,
 * including form data, loading state, and covariance data.
 */
export const useCompanyAssetCovarianceStore = defineStore('companyAssetCovariance', () => {
  // Get the main dashboard store
  const dashboardStore = useCompanyDashboardStore();

  // State
  const formData = reactive({
    date: '',
    model_type: 'MH',
  });

  const loading = ref(false);
  const assetCovarianceData = ref<any>(null);
  const activeTab = ref('covariance');
  const tableData = ref<any[]>([]);

  // Computed
  const hasData = computed(() => assetCovarianceData.value !== null);

  // Actions
  async function fetchAssetCovarianceData() {
    if (!dashboardStore.selectedCompany || !formData.date) {
      ElMessage.warning('请填写必要的查询参数');
      return;
    }

    loading.value = true;
    try {
      const params = {
        date: formData.date,
        ticker: dashboardStore.selectedCompany,
        model_type: formData.model_type,
      };

      const data = await calculateAssetCovariance(params);
      assetCovarianceData.value = data;
      
      // 转换数据为表格格式
      transformToTableData();
    } catch (error) {
      console.error('获取资产协方差数据失败:', error);
      ElMessage.error('获取数据失败，请检查参数后重试');
    } finally {
      loading.value = false;
    }
  }

  function resetForm() {
    formData.date = '';
    formData.model_type = 'MH';
    assetCovarianceData.value = null;
    tableData.value = [];
  }

  // 转换数据为表格格式
  function transformToTableData() {
    if (!assetCovarianceData.value) return;
    
    const { assets, covariance, correlation } = assetCovarianceData.value;
    tableData.value = assets.map((asset: string) => ({
      asset,
      covariance: covariance[asset]?.toFixed(6) || '-',
      correlation: correlation[asset]?.toFixed(4) || '-',
    }));
  }

  // Reset the store state
  function reset() {
    resetForm();
    activeTab.value = 'covariance';
  }

  return {
    // State
    formData,
    loading,
    assetCovarianceData,
    activeTab,
    tableData,
    hasData,
    
    // Actions
    fetchAssetCovarianceData,
    resetForm,
    reset,
    transformToTableData
  };
});
