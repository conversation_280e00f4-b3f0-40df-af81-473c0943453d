import { defineStore } from 'pinia';
import { ref, reactive, computed } from 'vue';
import { getFactorExposure } from '@/api/factorService';
import { ElMessage } from 'element-plus';
import { useCompanyDashboardStore } from './companyDashboard';

/**
 * Company Exposure Store
 * 
 * This store manages the state for the CompanyExposure component,
 * including form data, loading state, and exposure data.
 */
export const useCompanyExposureStore = defineStore('companyExposure', () => {
  // Get the main dashboard store
  const dashboardStore = useCompanyDashboardStore();

  // State
  const formData = reactive({
    start_date: '',
    end_date: '',
    model_type: 'MH',
    factors: [] as string[],
  });

  const availableFactors = ref([
    'Size', 'Value', 'Momentum', 'Volatility', 'Growth', 'Liquidity', 'Beta', 'Leverage'
  ]);

  const loading = ref(false);
  const exposureData = ref<any>(null);
  const activeTab = ref('chart');
  const tableData = ref<any[]>([]);

  // Computed
  const hasData = computed(() => exposureData.value !== null);

  // Actions
  async function fetchExposureData() {
    if (!dashboardStore.selectedCompany || !formData.start_date || !formData.end_date) {
      ElMessage.warning('请填写必要的查询参数');
      return;
    }

    loading.value = true;
    try {
      const params = {
        ticker: dashboardStore.selectedCompany,
        start_date: formData.start_date,
        end_date: formData.end_date,
        model_type: formData.model_type,
        factors: formData.factors.length > 0 ? formData.factors : undefined,
      };

      const data = await getFactorExposure(params);

      // 过滤掉所有日期中值都为 null 的因子
      const filteredData = filterNullFactors(data);
      exposureData.value = filteredData;

      // 转换数据为表格格式
      transformToTableData();
    } catch (error) {
      console.error('获取因子暴露数据失败:', error);
      ElMessage.error('获取数据失败，请检查参数后重试');
    } finally {
      loading.value = false;
    }
  }

  function resetForm() {
    formData.start_date = '';
    formData.end_date = '';
    formData.model_type = 'MH';
    formData.factors = [];
    exposureData.value = null;
    tableData.value = [];
  }

  // 过滤掉所有日期中值都为 null 的因子
  function filterNullFactors(data: any) {
    if (!data || !data.factors || !data.dates || !data.data) return data;

    const { factors, dates, data: factorData } = data;
    
    // 找出有效的因子（至少在一个日期中有非null值）
    const validFactors = factors.filter((factor: string) => {
      // 检查该因子在所有日期中是否都为null
      const isAllNull = dates.every((date: string) => {
        return factorData[date][factor] === null || factorData[date][factor] === undefined;
      });
      
      // 返回非全null的因子
      return !isAllNull;
    });
    
    // 如果没有有效因子，返回原始数据
    if (validFactors.length === 0) return data;
    
    // 返回过滤后的数据
    return {
      ...data,
      factors: validFactors
    };
  }

  // 转换数据为表格格式
  function transformToTableData() {
    if (!exposureData.value) return;
    
    const { dates, factors, data } = exposureData.value;
    tableData.value = dates.map((date: string) => {
      const row: any = { date };
      factors.forEach((factor: string) => {
        row[factor] = data[date][factor]?.toFixed(4) || '-';
      });
      return row;
    });
  }

  // Reset the store state
  function reset() {
    resetForm();
    activeTab.value = 'chart';
  }

  return {
    // State
    formData,
    availableFactors,
    loading,
    exposureData,
    activeTab,
    tableData,
    hasData,
    
    // Actions
    fetchExposureData,
    resetForm,
    reset,
    transformToTableData
  };
});
