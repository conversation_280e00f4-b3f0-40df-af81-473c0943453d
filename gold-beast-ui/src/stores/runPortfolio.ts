import { defineStore } from "pinia";
import { ref, reactive } from "vue";
import axios from "axios";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";

/**
 * Run Portfolio Store
 *
 * This store manages the state for the run portfolio dialog,
 * including dialog visibility, form data, and loading state.
 */
export const useRunPortfolioStore = defineStore("runPortfolio", () => {
  // Router instance
  const router = useRouter();

  // State
  const dialogVisible = ref(false);
  const loading = ref(false);
  const portfolioId = ref("");
  const analysisResults = ref([]);
  const drawerVisible = ref(false);

  // Form data
  const formData = reactive({
    start_date: "",
    end_date: "",
    initial_capital: 1000000,
    benchmark: "SPY",
    freq: "day",
    rebalance_freq: "monthly",
  });

  // Actions
  const openDialog = (id: string) => {
    portfolioId.value = id;
    dialogVisible.value = true;
    return true; // Return a value to make it chainable
  };

  const closeDialog = () => {
    dialogVisible.value = false;
  };

  const runPortfolio = async (skipConfiguration = false) => {
    if (!portfolioId.value) {
      ElMessage.error("Portfolio ID is missing");
      return;
    }

    loading.value = true;

    const payload = {
      portfolio_id: portfolioId.value,
      start_date: formData.start_date,
      end_date: formData.end_date,
      initial_capital: formData.initial_capital,
      benchmark: formData.benchmark,
      freq: formData.freq,
      rebalance_freq: formData.rebalance_freq,
    };

    try {
      const response = await axios.post(`/gbs-api/v1/portfolio-backtest`, payload);

      // Handle successful response
      ElMessage.success("Portfolio backtest executed successfully");

      // If we have analysis results, show them in the drawer
      if (response.data.analysis_results) {
        analysisResults.value = Object.values(response.data.analysis_results);
        drawerVisible.value = true;
      }

      // Navigate to the run detail page if we have run_id and experiment_id
      if (response.data.run_id && response.data.experiment_id) {
        router.push({
          path: `/backtest/detail/${response.data.experiment_id}/${response.data.run_id}`,
        });
      }

      return response.data;
    } catch (error: any) {
      console.error("Failed to run portfolio backtest:", error);
      ElMessage.error(error.message || "Failed to run portfolio backtest");
      throw error;
    } finally {
      loading.value = false;
      if (!skipConfiguration) {
        dialogVisible.value = false;
      }
    }
  };

  return {
    dialogVisible,
    loading,
    portfolioId,
    formData,
    analysisResults,
    drawerVisible,
    openDialog,
    closeDialog,
    runPortfolio,
  };
});
