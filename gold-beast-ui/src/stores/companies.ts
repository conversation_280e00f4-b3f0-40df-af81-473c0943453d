import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { getCompanies, processCompanyList } from "@/api/companies";

/**
 * Companies Store
 *
 * This store manages the global state for company data,
 * providing a centralized way to access company information
 * including ticker symbols and company names.
 */
export const useCompaniesStore = defineStore("companies", () => {
  // State
  const companies = ref<
    Array<{
      label: string;
      value: {
        ticker: string;
        industry: string;
        long_name: string;
        type: string;
      };
    }>
  >([]);

  const loading = ref(false);
  const loaded = ref(false);

  // Computed
  const companiesMap = computed(() => {
    const map = new Map<string, string>();
    companies.value.forEach((company) => {
      map.set(company.value.ticker, company.value.long_name);
    });
    return map;
  });

  const companiesList = computed(() => companies.value);

  // Actions
  const loadCompanies = async () => {
    if (loaded.value || loading.value) {
      return;
    }

    loading.value = true;

    try {
      return new Promise<void>((resolve, reject) => {
        getCompanies().subscribe({
          next: (list) => {
            if (list.length > 0) {
              companies.value = processCompanyList(list);
              loaded.value = true;
            }
            loading.value = false;
            resolve();
          },
          error: (error) => {
            console.error("Failed to load companies:", error);
            loading.value = false;
            reject(error);
          },
        });
      });
    } catch (error) {
      console.error("Failed to load companies:", error);
      loading.value = false;
      throw error;
    }
  };

  const getCompanyName = (ticker: string): string => {
    return companiesMap.value.get(ticker) || ticker;
  };

  const getCompanyByTicker = (ticker: string) => {
    return companies.value.find((company) => company.value.ticker === ticker);
  };

  const searchCompanies = (query: string) => {
    if (!query.trim()) {
      return companies.value;
    }

    const upperQuery = query.trim().toUpperCase();

    // First try to match ticker exactly
    let results = companies.value.filter((company) =>
      company.value.ticker.includes(upperQuery)
    );

    // Sort exact matches first
    results.sort((a, b) => {
      if (a.value.ticker === upperQuery) return -1;
      if (b.value.ticker === upperQuery) return 1;
      return 0;
    });

    // If no ticker matches, search by company name
    if (results.length === 0) {
      results = companies.value.filter((company) =>
        company.value.long_name.toUpperCase().includes(upperQuery)
      );
    }

    return results;
  };

  // Initialize companies on store creation
  loadCompanies();

  return {
    // State
    companies: companiesList,
    loading,
    loaded,

    // Computed
    companiesMap,

    // Actions
    loadCompanies,
    getCompanyName,
    getCompanyByTicker,
    searchCompanies,
  };
});
