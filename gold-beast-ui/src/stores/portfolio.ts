import { defineS<PERSON> } from 'pinia';
import { ref, computed } from 'vue';
import {
  getPortfolios,
  getPortfolio,
  createPortfolio,
  updatePortfolio,
  deletePortfolio,
  calculatePortfolioAttribution,
  type Portfolio,
  type PortfolioCreateUpdate,
  type PortfolioStock
} from '@/api/portfolioService';
import { ElMessage } from 'element-plus';

/**
 * Portfolio Store
 *
 * This store manages the state for the portfolio feature,
 * including portfolio list, current portfolio, and loading states.
 */
export const usePortfolioStore = defineStore('portfolio', () => {
  // State
  const portfolios = ref<Portfolio[]>([]);
  const currentPortfolio = ref<Portfolio | null>(null);
  const loading = ref(false);
  const attributionData = ref(null);
  const attributionLoading = ref(false);

  // Initialize portfolios as an empty array to avoid undefined
  portfolios.value = [];

  // Computed
  const hasPortfolios = computed(() => {
    // Ensure portfolios.value is initialized as an array
    if (!portfolios.value) return false;

    const result = Array.isArray(portfolios.value) && portfolios.value.length > 0;
    console.log('Computing hasPortfolios:', {
      portfoliosValue: portfolios.value,
      isArray: Array.isArray(portfolios.value),
      length: Array.isArray(portfolios.value) ? portfolios.value.length : 'N/A',
      result
    });
    return result;
  });

  // Actions
  const fetchPortfolios = async () => {
    loading.value = true;
    try {
      console.log('Fetching portfolios...');
      const data = await getPortfolios();
      console.log('Portfolios fetched:', data);
      portfolios.value = data;
      console.log('Portfolios state updated:', portfolios.value);
    } catch (error) {
      console.error('Failed to fetch portfolios:', error);
      ElMessage.error('获取投资组合列表失败');
    } finally {
      loading.value = false;
    }
  };

  const fetchPortfolio = async (id: string) => {
    loading.value = true;
    try {
      currentPortfolio.value = await getPortfolio(id);
    } catch (error) {
      console.error(`Failed to fetch portfolio ${id}:`, error);
      ElMessage.error('获取投资组合详情失败');
    } finally {
      loading.value = false;
    }
  };

  const addPortfolio = async (portfolio: PortfolioCreateUpdate) => {
    loading.value = true;
    try {
      const newPortfolio = await createPortfolio(portfolio);
      portfolios.value.push(newPortfolio);
      ElMessage.success('投资组合创建成功');
      return newPortfolio;
    } catch (error) {
      console.error('Failed to create portfolio:', error);
      ElMessage.error('创建投资组合失败');
      return null;
    } finally {
      loading.value = false;
    }
  };

  const editPortfolio = async (id: string, portfolio: PortfolioCreateUpdate) => {
    loading.value = true;
    try {
      const updatedPortfolio = await updatePortfolio(id, portfolio);

      // Update in the list
      const index = portfolios.value.findIndex(p => p.id === id);
      if (index !== -1) {
        portfolios.value[index] = updatedPortfolio;
      }

      // Update current portfolio if it's the one being edited
      if (currentPortfolio.value && currentPortfolio.value.id === id) {
        currentPortfolio.value = updatedPortfolio;
      }

      ElMessage.success('投资组合更新成功');
      return updatedPortfolio;
    } catch (error) {
      console.error(`Failed to update portfolio ${id}:`, error);
      ElMessage.error('更新投资组合失败');
      return null;
    } finally {
      loading.value = false;
    }
  };

  const removePortfolio = async (id: string) => {
    loading.value = true;
    try {
      await deletePortfolio(id);

      // Remove from the list
      portfolios.value = portfolios.value.filter(p => p.id !== id);

      // Clear current portfolio if it's the one being deleted
      if (currentPortfolio.value && currentPortfolio.value.id === id) {
        currentPortfolio.value = null;
      }

      ElMessage.success('投资组合删除成功');
      return true;
    } catch (error) {
      console.error(`Failed to delete portfolio ${id}:`, error);
      ElMessage.error('删除投资组合失败');
      return false;
    } finally {
      loading.value = false;
    }
  };

  const calculateAttribution = async (params: {
    portfolio: PortfolioStock[];
    date: string;
    model_type?: string;
    factors_dir?: string;
  }) => {
    attributionLoading.value = true;
    try {
      attributionData.value = await calculatePortfolioAttribution(params);
      return attributionData.value;
    } catch (error) {
      console.error('Failed to calculate attribution:', error);
      ElMessage.error('计算归因分析失败');
      return null;
    } finally {
      attributionLoading.value = false;
    }
  };

  const clearCurrentPortfolio = () => {
    currentPortfolio.value = null;
  };

  const clearAttributionData = () => {
    attributionData.value = null;
  };

  return {
    // State
    portfolios,
    currentPortfolio,
    loading,
    attributionData,
    attributionLoading,

    // Computed
    hasPortfolios,

    // Actions
    fetchPortfolios,
    fetchPortfolio,
    addPortfolio,
    editPortfolio,
    removePortfolio,
    calculateAttribution,
    clearCurrentPortfolio,
    clearAttributionData
  };
});
