<template>
  <div class="company-exposure-container">
    <el-card class="company-exposure-card">
      <template #header>
        <div class="card-header">
          <h3>因子暴露分析</h3>
        </div>
      </template>
      <div class="card-content">
        <!-- 参数表单 -->
        <el-form
          :model="formData"
          label-width="120px"
          class="exposure-form"
          size="default"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="股票代码">
                <CompanySelect
                  v-model="formData.ticker"
                  placeholder="请选择股票"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="日期范围">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYYMMDD"
                  value-format="YYYYMMDD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="模型类型">
                <el-select
                  v-model="formData.model_type"
                  placeholder="请选择模型类型"
                  style="width: 100%"
                >
                  <el-option label="MH" value="MH" />
                  <el-option label="SH" value="SH" />
                  <el-option label="MH-S" value="MH-S" />
                  <el-option label="SH-S" value="SH-S" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="因子选择">
                <el-select
                  v-model="formData.factors"
                  multiple
                  collapse-tags
                  placeholder="请选择因子（可多选）"
                  style="width: 100%"
                >
                  <el-option
                    v-for="factor in availableFactors"
                    :key="factor"
                    :label="factor"
                    :value="factor"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <el-button
                  type="primary"
                  @click="fetchExposureData"
                  :loading="loading"
                >
                  查询
                </el-button>
                <el-button @click="resetForm">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="10" animated />
        </div>

        <!-- 结果展示 -->
        <div v-else-if="exposureData" class="result-container">
          <el-tabs v-model="activeTab" type="border-card">
            <el-tab-pane label="图表展示" name="chart">
              <div class="chart-container" ref="chartRef"></div>
            </el-tab-pane>
            <el-tab-pane label="数据表格" name="table">
              <el-table
                :data="tableData"
                border
                style="width: 100%"
                height="400"
                stripe
              >
                <el-table-column prop="date" label="日期" width="120" fixed />
                <el-table-column
                  v-for="factor in exposureData.factors"
                  :key="factor"
                  :prop="factor"
                  :label="factor"
                  width="120"
                />
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 空状态 -->
        <el-empty v-else description="请输入参数并点击查询按钮" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from "vue";
import { getFactorExposure } from "@/api/factorService";
import * as echarts from "echarts";
import { ElMessage } from "element-plus";
import CompanySelect from "@/components/CompanySelect.vue";
import { useEchartResize } from "@/composables/useEchartResize";

// 表单数据
const formData = reactive({
  ticker: "",
  start_date: "",
  end_date: "",
  model_type: "MH",
  factors: [] as string[],
});

// 使用计算属性处理日期范围
const dateRange = computed({
  get: () => {
    // 如果开始日期和结束日期都有值，返回数组
    if (formData.start_date && formData.end_date) {
      return [formData.start_date, formData.end_date];
    }
    return null;
  },
  set: (val) => {
    if (val && Array.isArray(val) && val.length === 2) {
      formData.start_date = val[0];
      formData.end_date = val[1];
    } else {
      formData.start_date = "";
      formData.end_date = "";
    }
  },
});

// 可用因子列表（示例数据，实际应从API获取）
const availableFactors = ref([
  "Size",
  "Value",
  "Momentum",
  "Volatility",
  "Growth",
  "Liquidity",
  "Beta",
  "Leverage",
]);

const loading = ref(false);
const exposureData = ref<any>(null);
const activeTab = ref("chart");
const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;
let chartResize: { resizeChart: () => void } | null = null;
const tableData = ref<any[]>([]);

// 获取因子暴露数据
const fetchExposureData = async () => {
  if (!formData.ticker || !formData.start_date || !formData.end_date) {
    ElMessage.warning("请填写必要的查询参数");
    return;
  }

  loading.value = true;
  try {
    const params = {
      ticker: formData.ticker,
      start_date: formData.start_date,
      end_date: formData.end_date,
      model_type: formData.model_type,
      factors: formData.factors.length > 0 ? formData.factors : undefined,
    };

    const data = await getFactorExposure(params);

    // 过滤掉所有日期中值都为 null 的因子
    const filteredData = filterNullFactors(data);
    exposureData.value = filteredData;

    // 转换数据为表格格式
    transformToTableData();

    // 在下一个tick渲染图表
    setTimeout(() => {
      renderChart();
    }, 0);
  } catch (error) {
    console.error("获取因子暴露数据失败:", error);
    ElMessage.error("获取数据失败，请检查参数后重试");
  } finally {
    loading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  formData.ticker = "";
  formData.start_date = "";
  formData.end_date = "";
  formData.model_type = "MH";
  formData.factors = [];
  exposureData.value = null;
  if (chart) {
    chart.dispose();
    chart = null;
    chartResize = null;
  }
};

// 过滤掉所有日期中值都为 null 的因子
const filterNullFactors = (data: any) => {
  if (!data || !data.factors || !data.dates || !data.data) return data;

  const { factors, dates, data: factorData } = data;

  // 找出有效的因子（至少在一个日期中有非null值）
  const validFactors = factors.filter((factor: string) => {
    // 检查该因子在所有日期中是否都为null
    const isAllNull = dates.every((date: string) => {
      return (
        factorData[date][factor] === null ||
        factorData[date][factor] === undefined
      );
    });

    // 返回非全null的因子
    return !isAllNull;
  });

  // 如果没有有效因子，返回原始数据
  if (validFactors.length === 0) return data;

  // 返回过滤后的数据
  return {
    ...data,
    factors: validFactors,
  };
};

// 转换数据为表格格式
const transformToTableData = () => {
  if (!exposureData.value) return;

  const { dates, factors, data } = exposureData.value;
  tableData.value = dates.map((date: string) => {
    const row: any = { date };
    factors.forEach((factor: string) => {
      row[factor] = data[date][factor]?.toFixed(4) || "-";
    });
    return row;
  });
};

// 渲染图表
const renderChart = () => {
  if (!chartRef.value || !exposureData.value) return;

  if (chart) {
    chart.dispose();
    chartResize = null;
  }

  chart = echarts.init(chartRef.value);
  // 应用 useEchartResize
  chartResize = useEchartResize(chart);

  const { dates, factors, data } = exposureData.value;
  const series = factors.map((factor: string) => {
    return {
      name: factor,
      type: "line",
      symbol: "none",
      data: dates.map((date: string) => data[date][factor] || 0),
    };
  });

  const option = {
    title: {
      text: `${formData.ticker} 因子暴露分析`,
      left: "center",
    },
    tooltip: {
      trigger: "axis",
      appendToBody: true,
    },
    legend: {
      data: factors,
      type: "scroll",
      bottom: 0,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: dates,
    },
    yAxis: {
      type: "value",
      name: "暴露值",
    },
    series,
  };

  chart.setOption(option);
};

// 监听activeTab变化，切换到图表时重新渲染
watch(activeTab, (newVal) => {
  if (newVal === "chart" && exposureData.value) {
    setTimeout(() => {
      renderChart();
    }, 0);
  }
});

onMounted(() => {
  console.log("Company Exposure component mounted");
});
</script>

<style lang="scss">
.company-exposure-container {
  .company-exposure-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-content {
      min-height: 600px;

      .exposure-form {
        margin-bottom: 20px;
      }

      .loading-container {
        padding: 20px;
      }

      .result-container {
        .chart-container {
          height: 400px;
          width: 100%;
        }
      }
    }
  }
}
</style>
