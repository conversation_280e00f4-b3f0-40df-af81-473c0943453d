<template>
  <div class="company-asset-covariance-component">
    <!-- 参数表单 -->
    <el-form :model="covarianceStore.formData" label-width="120px" class="asset-covariance-form" size="default">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="日期">
            <el-date-picker
              v-model="covarianceStore.formData.date"
              type="date"
              placeholder="选择日期"
              format="YYYYMMDD"
              value-format="YYYYMMDD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="模型类型">
            <el-select v-model="covarianceStore.formData.model_type" placeholder="请选择模型类型" style="width: 100%">
              <el-option label="MH" value="MH" />
              <el-option label="SH" value="SH" />
              <el-option label="MH-S" value="MH-S" />
              <el-option label="SH-S" value="SH-S" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-button type="primary" @click="covarianceStore.fetchAssetCovarianceData()" :loading="covarianceStore.loading">
              查询
            </el-button>
            <el-button @click="covarianceStore.resetForm()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 加载状态 -->
    <div v-if="covarianceStore.loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 结果展示 -->
    <div v-else-if="covarianceStore.assetCovarianceData" class="result-container">
      <!-- 图表选择器 -->
      <div class="chart-selector">
        <el-segmented v-model="activeChartType" :options="chartTypeOptions" size="large" />
      </div>

      <!-- 热力图 -->
      <div v-if="activeChartType === 'heatmap'" class="section-container">
        <h3 class="section-title">相关性热力图</h3>
        <div class="chart-container" ref="heatmapChartRef"></div>
      </div>

      <!-- 散点图 -->
      <div v-if="activeChartType === 'scatter'" class="section-container">
        <h3 class="section-title">协方差-相关性散点图</h3>
        <div class="chart-container" ref="scatterChartRef"></div>
      </div>

      <!-- 网络图 -->
      <div v-if="activeChartType === 'network'" class="section-container">
        <h3 class="section-title">资产关系网络图</h3>
        <div class="chart-container" ref="networkChartRef"></div>
      </div>

      <!-- 条形图 -->
      <div v-if="activeChartType === 'bar'" class="section-container">
        <el-tabs v-model="activeBarTab">
          <el-tab-pane label="协方差" name="covariance">
            <div class="chart-container" ref="covarianceChartRef"></div>
          </el-tab-pane>
          <el-tab-pane label="相关性" name="correlation">
            <div class="chart-container" ref="correlationChartRef"></div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 数据表格 -->
      <div class="section-container">
        <h3 class="section-title">数据表格</h3>
        <PaginatedTable
          :data="covarianceStore.tableData"
          :columns="tableColumns"
          :page-size="15"
          :page-size-options="[15, 50, 100, 200]"
          :max-items-before-pagination="15"
        />
      </div>
    </div>

    <!-- 空状态 -->
    <el-empty v-else description="请输入参数并点击查询按钮" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import * as echarts from "echarts";
import { useCompanyAssetCovarianceStore, useCompanyDashboardStore } from "@/stores/company";
import PaginatedTable from "@/components/common/PaginatedTable.vue";

// 使用 Pinia store
const dashboardStore = useCompanyDashboardStore();
const covarianceStore = useCompanyAssetCovarianceStore();

// 图表类型选择
const activeChartType = ref<'heatmap' | 'scatter' | 'network' | 'bar'>('heatmap');
const activeBarTab = ref<'covariance' | 'correlation'>('covariance');

// 图表类型选项
const chartTypeOptions = [
  {
    value: 'heatmap',
    label: '热力图'
  },
  {
    value: 'scatter',
    label: '散点图'
  },
  {
    value: 'network',
    label: '网络图'
  },
  {
    value: 'bar',
    label: '条形图'
  }
];

// 图表相关
const covarianceChartRef = ref<HTMLElement | null>(null);
const correlationChartRef = ref<HTMLElement | null>(null);
const heatmapChartRef = ref<HTMLElement | null>(null);
const scatterChartRef = ref<HTMLElement | null>(null);
const networkChartRef = ref<HTMLElement | null>(null);

let covarianceChart: echarts.ECharts | null = null;
let correlationChart: echarts.ECharts | null = null;
let heatmapChart: echarts.ECharts | null = null;
let scatterChart: echarts.ECharts | null = null;
let networkChart: echarts.ECharts | null = null;

// 渲染协方差图表
const renderCovarianceChart = () => {
  if (!covarianceChartRef.value || !covarianceStore.assetCovarianceData) return;

  if (covarianceChart) {
    covarianceChart.dispose();
  }

  covarianceChart = echarts.init(covarianceChartRef.value);

  const { assets, covariance } = covarianceStore.assetCovarianceData;

  // 按协方差值排序
  const sortedData = assets.map((asset: string) => ({
    asset,
    value: covariance[asset]
  })).sort((a: any, b: any) => Math.abs(b.value) - Math.abs(a.value));

  // 取前20个资产
  const topAssets = sortedData.slice(0, 20);

  const option = {
    title: {
      text: `${dashboardStore.selectedCompany} 与其他资产的协方差`,
      left: "center"
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      },
      formatter: function(params: any) {
        const asset = params[0].name;
        const value = params[0].value;
        return `${asset}: ${value.toFixed(6)}`;
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "value",
      name: "协方差",
      axisLabel: {
        formatter: function(value: number) {
          return value.toExponential(2);
        }
      }
    },
    yAxis: {
      type: "category",
      data: topAssets.map((item: any) => item.asset),
      axisLabel: {
        interval: 0,
        rotate: 0
      }
    },
    visualMap: {
      orient: "horizontal",
      left: "center",
      min: -0.001,
      max: 0.001,
      text: ["高", "低"],
      dimension: 0,
      inRange: {
        color: ["#f36c6c", "#e0e0e0", "#6cacf3"]
      }
    },
    series: [
      {
        name: "协方差",
        type: "bar",
        data: topAssets.map((item: any) => item.value)
      }
    ]
  };

  covarianceChart.setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", () => {
    covarianceChart?.resize();
  });
};

// 渲染相关性图表
const renderCorrelationChart = () => {
  if (!correlationChartRef.value || !covarianceStore.assetCovarianceData) return;

  if (correlationChart) {
    correlationChart.dispose();
  }

  correlationChart = echarts.init(correlationChartRef.value);

  const { assets, correlation } = covarianceStore.assetCovarianceData;

  // 按相关系数绝对值排序
  const sortedData = assets.map((asset: string) => ({
    asset,
    value: correlation[asset]
  })).sort((a: any, b: any) => Math.abs(b.value) - Math.abs(a.value));

  // 取前20个资产
  const topAssets = sortedData.slice(0, 20);

  const option = {
    title: {
      text: `${dashboardStore.selectedCompany} 与其他资产的相关系数`,
      left: "center"
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      },
      formatter: function(params: any) {
        const asset = params[0].name;
        const value = params[0].value;
        return `${asset}: ${value.toFixed(4)}`;
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "value",
      name: "相关系数",
      min: -1,
      max: 1
    },
    yAxis: {
      type: "category",
      data: topAssets.map((item: any) => item.asset),
      axisLabel: {
        interval: 0,
        rotate: 0
      }
    },
    visualMap: {
      orient: "horizontal",
      left: "center",
      min: -1,
      max: 1,
      text: ["正相关", "负相关"],
      dimension: 0,
      inRange: {
        color: ["#f36c6c", "#e0e0e0", "#6cacf3"]
      }
    },
    series: [
      {
        name: "相关系数",
        type: "bar",
        data: topAssets.map((item: any) => item.value)
      }
    ]
  };

  correlationChart.setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", () => {
    correlationChart?.resize();
  });
};

// 渲染热力图
const renderHeatmapChart = () => {
  if (!heatmapChartRef.value || !covarianceStore.assetCovarianceData) return;

  if (heatmapChart) {
    heatmapChart.dispose();
  }

  heatmapChart = echarts.init(heatmapChartRef.value);

  const { assets, correlation } = covarianceStore.assetCovarianceData;

  // 按相关系数绝对值排序
  const sortedData = assets.map((asset: string) => ({
    asset,
    value: correlation[asset]
  })).sort((a: any, b: any) => Math.abs(b.value) - Math.abs(a.value));

  // 取前30个资产
  const topAssets = sortedData.slice(0, 30).map((item: any) => item.asset);

  // 准备热力图数据
  const data: [string, string, number][] = [];
  const selectedCompany = dashboardStore.selectedCompany;

  // 添加所有资产与选中公司的相关性
  topAssets.forEach((asset: string) => {
    data.push([selectedCompany, asset, correlation[asset]]);
  });

  const option = {
    title: {
      text: `${selectedCompany} 与其他资产的相关性热力图`,
      left: 'center'
    },
    tooltip: {
      position: 'top',
      formatter: function (params: any) {
        return `${params.data[0]} 与 ${params.data[1]} 的相关系数: ${params.data[2].toFixed(4)}`;
      }
    },
    grid: {
      left: '3%',
      right: '7%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [selectedCompany],
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: topAssets,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: -1,
      max: 1,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '0%',
      text: ['正相关', '负相关'],
      inRange: {
        color: ['#f36c6c', '#e0e0e0', '#6cacf3']
      }
    },
    series: [
      {
        name: '相关系数',
        type: 'heatmap',
        data: data,
        label: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  heatmapChart.setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", () => {
    heatmapChart?.resize();
  });
};

// 渲染散点图
const renderScatterChart = () => {
  if (!scatterChartRef.value || !covarianceStore.assetCovarianceData) return;

  if (scatterChart) {
    scatterChart.dispose();
  }

  scatterChart = echarts.init(scatterChartRef.value);

  const { assets, covariance, correlation } = covarianceStore.assetCovarianceData;

  // 准备散点图数据
  const data = assets.map((asset: string) => [
    covariance[asset],
    correlation[asset],
    asset
  ]);

  const option = {
    title: {
      text: `${dashboardStore.selectedCompany} 与其他资产的协方差-相关性散点图`,
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: function (params: any) {
        return `资产: ${params.data[2]}<br/>协方差: ${params.data[0].toExponential(4)}<br/>相关系数: ${params.data[1].toFixed(4)}`;
      }
    },
    grid: {
      left: '3%',
      right: '7%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '协方差',
      axisLabel: {
        formatter: function(value: number) {
          return value.toExponential(2);
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '相关系数',
      min: -1,
      max: 1
    },
    visualMap: {
      min: -1,
      max: 1,
      dimension: 1,
      orient: 'horizontal',
      left: 'center',
      bottom: '0%',
      text: ['正相关', '负相关'],
      inRange: {
        color: ['#f36c6c', '#e0e0e0', '#6cacf3']
      }
    },
    series: [
      {
        name: '协方差-相关性',
        type: 'scatter',
        symbolSize: 10,
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  scatterChart.setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", () => {
    scatterChart?.resize();
  });
};

// 渲染网络图
const renderNetworkChart = () => {
  if (!networkChartRef.value || !covarianceStore.assetCovarianceData) return;

  if (networkChart) {
    networkChart.dispose();
  }

  networkChart = echarts.init(networkChartRef.value);

  const { assets, correlation } = covarianceStore.assetCovarianceData;
  const selectedCompany = dashboardStore.selectedCompany;

  // 按相关系数绝对值排序
  const sortedData = assets.map((asset: string) => ({
    asset,
    value: correlation[asset]
  })).sort((a: any, b: any) => Math.abs(b.value) - Math.abs(a.value));

  // 取前20个资产
  const topAssets = sortedData.slice(0, 20);

  // 准备节点数据
  const nodes = [
    {
      name: selectedCompany,
      symbolSize: 50,
      category: 0,
      itemStyle: {
        color: '#5470c6'
      }
    },
    ...topAssets.map((item: any) => ({
      name: item.asset,
      symbolSize: 30 + Math.abs(item.value) * 20,
      category: item.value > 0 ? 1 : 2,
      itemStyle: {
        color: item.value > 0 ? '#6cacf3' : '#f36c6c'
      }
    }))
  ];

  // 准备边数据
  const links = topAssets.map((item: any) => ({
    source: selectedCompany,
    target: item.asset,
    value: Math.abs(item.value),
    lineStyle: {
      width: Math.abs(item.value) * 5,
      color: item.value > 0 ? '#6cacf3' : '#f36c6c',
      curveness: 0.3
    }
  }));

  const option = {
    title: {
      text: `${selectedCompany} 与其他资产的相关性网络图`,
      left: 'center'
    },
    tooltip: {
      formatter: function (params: any) {
        if (params.dataType === 'node') {
          return `资产: ${params.data.name}`;
        } else {
          return `${params.data.source} 与 ${params.data.target} 的相关系数: ${correlation[params.data.target].toFixed(4)}`;
        }
      }
    },
    legend: {
      data: ['中心资产', '正相关资产', '负相关资产'],
      orient: 'horizontal',
      left: 'center',
      bottom: '0%'
    },
    series: [
      {
        name: '相关性网络',
        type: 'graph',
        layout: 'force',
        data: nodes,
        links: links,
        categories: [
          { name: '中心资产' },
          { name: '正相关资产' },
          { name: '负相关资产' }
        ],
        roam: true,
        label: {
          show: true,
          position: 'right',
          formatter: '{b}'
        },
        force: {
          repulsion: 100,
          edgeLength: [50, 100]
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 10
          }
        }
      }
    ]
  };

  networkChart.setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", () => {
    networkChart?.resize();
  });
};

// 根据当前选择的图表类型渲染对应的图表
const renderCurrentChart = () => {
  if (!covarianceStore.assetCovarianceData) return;

  setTimeout(() => {
    if (activeChartType.value === 'heatmap') {
      renderHeatmapChart();
    } else if (activeChartType.value === 'scatter') {
      renderScatterChart();
    } else if (activeChartType.value === 'network') {
      renderNetworkChart();
    } else if (activeChartType.value === 'bar') {
      if (activeBarTab.value === 'covariance') {
        renderCovarianceChart();
      } else {
        renderCorrelationChart();
      }
    }
  }, 0);
};

// 监听图表类型变化
watch(activeChartType, () => {
  renderCurrentChart();
});

// 监听条形图标签页变化
watch(activeBarTab, () => {
  if (activeChartType.value === 'bar') {
    renderCurrentChart();
  }
});

// 监听公司代码变化
watch(() => dashboardStore.selectedCompany, (newVal) => {
  if (newVal && covarianceStore.formData.date) {
    covarianceStore.fetchAssetCovarianceData();
  }
});

// 监听数据变化，重新渲染图表
watch(() => covarianceStore.assetCovarianceData, (newVal) => {
  if (newVal) {
    renderCurrentChart();
  }
});

// 清理图表资源
const cleanupCharts = () => {
  if (covarianceChart) {
    covarianceChart.dispose();
    covarianceChart = null;
  }
  if (correlationChart) {
    correlationChart.dispose();
    correlationChart = null;
  }
  if (heatmapChart) {
    heatmapChart.dispose();
    heatmapChart = null;
  }
  if (scatterChart) {
    scatterChart.dispose();
    scatterChart = null;
  }
  if (networkChart) {
    networkChart.dispose();
    networkChart = null;
  }
};

onMounted(() => {
  console.log("CompanyAssetCovariance component mounted");

  // 如果已经有数据，则重新渲染图表
  if (covarianceStore.assetCovarianceData) {
    renderCurrentChart();
  }
});

// 表格列定义
const tableColumns = computed(() => [
  {
    prop: 'asset',
    label: '资产',
    minWidth: 120,
    fixed: true,
    sortable: true,
  },
  {
    prop: 'covariance',
    label: '协方差',
    minWidth: 120,
    sortable: true,
  },
  {
    prop: 'correlation',
    label: '相关系数',
    minWidth: 120,
    sortable: true,
  }
]);

// 对外暴露方法
defineExpose({
  renderCovarianceChart,
  renderCorrelationChart,
  renderHeatmapChart,
  renderScatterChart,
  renderNetworkChart,
  cleanupCharts
});
</script>

<style lang="scss">
.company-asset-covariance-component {
  .asset-covariance-form {
    margin-bottom: 20px;
  }

  .loading-container {
    padding: 20px;
  }

  .result-container {
    .chart-selector {
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
    }

    .section-container {
      margin-bottom: 30px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 20px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
      }

      .el-tabs {
        margin-top: -10px;
      }
    }

    .chart-container {
      height: 600px;
      width: 100%;
    }
  }
}
</style>
