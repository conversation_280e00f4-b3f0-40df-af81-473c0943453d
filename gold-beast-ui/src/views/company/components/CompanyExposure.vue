<template>
  <div class="company-exposure-component">
    <!-- 参数表单 -->
    <el-form :model="exposureStore.formData" label-width="120px" class="exposure-form" size="default">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYYMMDD"
              value-format="YYYYMMDD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="模型类型">
            <el-select v-model="exposureStore.formData.model_type" placeholder="请选择模型类型" style="width: 100%">
              <el-option label="MH" value="MH" />
              <el-option label="SH" value="SH" />
              <el-option label="MH-S" value="MH-S" />
              <el-option label="SH-S" value="SH-S" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="因子选择">
            <el-select
              v-model="exposureStore.formData.factors"
              multiple
              collapse-tags
              placeholder="请选择因子（可多选）"
              style="width: 100%"
            >
              <el-option
                v-for="factor in exposureStore.availableFactors"
                :key="factor"
                :label="factor"
                :value="factor"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-button type="primary" @click="exposureStore.fetchExposureData()" :loading="exposureStore.loading">
              查询
            </el-button>
            <el-button @click="exposureStore.resetForm()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 加载状态 -->
    <div v-if="exposureStore.loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 结果展示 -->
    <div v-else-if="exposureStore.exposureData" class="result-container">
      <!-- 图表展示 -->
      <div class="section-container">
        <h3 class="section-title">图表展示</h3>
        <div class="chart-container" ref="chartRef"></div>
      </div>

      <!-- 数据表格 -->
      <div class="section-container">
        <h3 class="section-title">数据表格</h3>
        <PaginatedTable
          :data="exposureStore.tableData"
          :columns="tableColumns"
          :page-size="15"
          :page-size-options="[15, 50, 100, 200]"
          :max-items-before-pagination="15"
        />
      </div>
    </div>

    <!-- 空状态 -->
    <el-empty v-else description="请输入参数并点击查询按钮" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import * as echarts from "echarts";
import { useCompanyExposureStore, useCompanyDashboardStore } from "@/stores/company";
import PaginatedTable from "@/components/common/PaginatedTable.vue";

// 使用 Pinia store
const dashboardStore = useCompanyDashboardStore();
const exposureStore = useCompanyExposureStore();

// 使用计算属性处理日期范围
const dateRange = computed({
  get: () => {
    // 如果开始日期和结束日期都有值，返回数组
    if (exposureStore.formData.start_date && exposureStore.formData.end_date) {
      return [exposureStore.formData.start_date, exposureStore.formData.end_date];
    }
    return null;
  },
  set: (val) => {
    if (val && Array.isArray(val) && val.length === 2) {
      exposureStore.formData.start_date = val[0];
      exposureStore.formData.end_date = val[1];
    } else {
      exposureStore.formData.start_date = "";
      exposureStore.formData.end_date = "";
    }
  },
});

// 图表相关
const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 渲染图表
const renderChart = () => {
  if (!chartRef.value || !exposureStore.exposureData) return;

  if (chart) {
    chart.dispose();
  }

  chart = echarts.init(chartRef.value);

  const { dates, factors, data } = exposureStore.exposureData;
  const series = factors.map((factor: string) => {
    return {
      name: factor,
      type: "line",
      symbol: "none",
      data: dates.map((date: string) => data[date][factor] || 0),
    };
  });

  const option = {
    title: {
      text: `${dashboardStore.selectedCompany} 因子暴露分析`,
      left: "center"
    },
    tooltip: {
      trigger: "axis",
      appendToBody: true
    },
    legend: {
      data: factors,
      type: "scroll",
      bottom: 0
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: dates
    },
    yAxis: {
      type: "value",
      name: "暴露值"
    },
    series
  };

  chart.setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", () => {
    chart?.resize();
  });
};

// 监听公司代码变化
watch(() => dashboardStore.selectedCompany, (newVal) => {
  if (newVal && exposureStore.formData.start_date && exposureStore.formData.end_date) {
    exposureStore.fetchExposureData();
  }
});

// 监听数据变化，重新渲染图表
watch(() => exposureStore.exposureData, (newVal) => {
  if (newVal) {
    setTimeout(() => {
      renderChart();
    }, 0);
  }
});

// 清理图表资源
const cleanupChart = () => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
};

onMounted(() => {
  console.log("CompanyExposure component mounted");

  // 如果已经有数据，则重新渲染图表
  if (exposureStore.exposureData) {
    setTimeout(() => {
      renderChart();
    }, 0);
  }
});

// 计算表格列
const tableColumns = computed(() => {
  if (!exposureStore.exposureData) return [];

  const columns = [
    {
      prop: 'date',
      label: '日期',
      minWidth: 120,
      fixed: true,
      sortable: true,
    }
  ];

  // 添加因子列
  exposureStore.exposureData.factors.forEach(factor => {
    columns.push({
      prop: factor,
      label: factor,
      minWidth: 120,
      sortable: true,
    });
  });

  return columns;
});

// 对外暴露方法
defineExpose({
  renderChart,
  cleanupChart
});
</script>

<style lang="scss">
.company-exposure-component {
  .exposure-form {
    margin-bottom: 20px;
  }

  .loading-container {
    padding: 20px;
  }

  .result-container {
    .section-container {
      margin-bottom: 30px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 20px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
      }
    }

    .chart-container {
      height: 400px;
      width: 100%;
    }
  }
}
</style>
