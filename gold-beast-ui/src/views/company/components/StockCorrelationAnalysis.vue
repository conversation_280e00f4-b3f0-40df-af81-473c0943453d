<template>
  <div class="stock-correlation-container">
    <!-- 股票选择器 -->
    <div class="stock-selector-section">
      <CompanyMultiSelect
        v-model="selectedStocks"
        placeholder="搜索并选择股票..."
        @change="handleStockSelectionChange"
      />

      <div class="stock-count-info">
        已选择 {{ selectedStocks.length }}/10 只股票
      </div>
    </div>

    <!-- 时间范围和分析按钮 -->
    <div class="analysis-controls">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="时间范围">
            <el-select v-model="timeRange">
              <el-option label="30天" :value="30" />
              <el-option label="90天" :value="90" />
              <el-option label="6个月" :value="180" />
              <el-option label="1年" :value="365" />
              <el-option label="2年" :value="730" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-button
            type="primary"
            @click="handleAnalyzeStocks"
            :loading="analyzing"
            :disabled="selectedStocks.length < 2"
          >
            {{ analyzing ? "分析中..." : "分析相关性" }}
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 状态显示 -->
    <div class="status-section">
      <el-alert
        :title="analysisStatus"
        :type="getStatusType()"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 分析结果显示 -->
    <div v-if="analysisResult" class="analysis-results">
      <el-row :gutter="16">
        <!-- 股票表现概览 -->
        <el-col :span="24">
          <el-card class="performance-card">
            <template #header>
              <h5>股票表现概览</h5>
            </template>
            <el-table :data="performanceTableData" border>
              <el-table-column prop="name" label="股票">
                <template #default="scope">
                  <div>
                    {{ scope.row.name }}
                  </div>
                  <div>
                    {{ scope.row.symbol }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="return" label="收益率" align="center">
                <template #default="scope">
                  <span
                    :style="{
                      color: scope.row.return > 0 ? '#67c23a' : '#f56c6c',
                      fontWeight: 'bold',
                    }"
                  >
                    {{ (scope.row.return * 100).toFixed(2) }}%
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="volatility" label="波动率" align="center">
                <template #default="scope">
                  {{ (scope.row.volatility * 100).toFixed(2) }}%
                </template>
              </el-table-column>
              <el-table-column
                prop="sharpe_ratio"
                label="夏普比率"
                align="center"
              >
                <template #default="scope">
                  <span
                    :style="{
                      color:
                        scope.row.sharpe_ratio > 1
                          ? '#67c23a'
                          : scope.row.sharpe_ratio > 0
                          ? '#e6a23c'
                          : '#f56c6c',
                      fontWeight:
                        scope.row.sharpe_ratio > 1 ? 'bold' : 'normal',
                    }"
                  >
                    {{ scope.row.sharpe_ratio.toFixed(3) }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <!-- 相关性矩阵 -->
        <el-col :span="24">
          <el-card class="correlation-card">
            <template #header>
              <h5>股票相关性矩阵</h5>
            </template>
            <el-table :data="correlationTableData" border>
              <el-table-column prop="symbol" label="股票">
                <template #default="scope">
                  <div>
                    {{ scope.row.symbol }}
                  </div>
                  <div>
                    {{ companiesStore.companiesMap.get(scope.row.symbol) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                v-for="symbol in selectedStocks"
                :key="symbol"
                :label="symbol"
                align="center"
              >
                <template #default="scope">
                  <span
                    :class="getCorrelationClass(scope.row.correlations[symbol])"
                    class="correlation-value"
                  >
                    {{ scope.row.correlations[symbol].toFixed(3) }}
                  </span>
                </template>
              </el-table-column>
            </el-table>

            <!-- 说明文字 -->
            <div class="correlation-legend">
              <el-tag type="success">强正相关 (>0.5)</el-tag>
              <el-tag type="danger">强负相关 (<-0.5)</el-tag>
              <el-tag type="primary">自相关 (1.0)</el-tag>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useCompaniesStore } from "@/stores/companies";
import CompanyMultiSelect from "@/components/CompanyMultiSelect.vue";

// 初始化 companies store
const companiesStore = useCompaniesStore();

// 响应式数据
const selectedStocks = ref<string[]>(["AAPL", "MSFT", "GOOGL"]);
const timeRange = ref(365);
const analyzing = ref(false);
const analysisStatus = ref("准备分析");
const analysisResult = ref<any>(null);

// 股票数据 - 不再需要，因为使用 CompanyMultiSelect 组件

// 计算属性
const performanceTableData = computed(() => {
  if (!analysisResult.value?.performance_data) return [];

  return selectedStocks.value.map((symbol) => ({
    symbol,
    name: getStockName(symbol),
    return: analysisResult.value.performance_data[symbol]?.return || 0,
    volatility: analysisResult.value.performance_data[symbol]?.volatility || 0,
    sharpe_ratio:
      analysisResult.value.performance_data[symbol]?.sharpe_ratio || 0,
  }));
});

const correlationTableData = computed(() => {
  if (!analysisResult.value?.correlation_matrix) return [];

  return selectedStocks.value.map((symbol) => ({
    symbol,
    correlations: analysisResult.value.correlation_matrix[symbol] || {},
  }));
});

// 方法
const getStockName = (symbol: string): string => {
  return companiesStore.getCompanyName(symbol);
};

const getStatusType = () => {
  if (analyzing.value) return "info";
  if (analysisStatus.value.includes("✅")) return "success";
  if (analysisStatus.value.includes("❌")) return "error";
  return "info";
};

const getCorrelationClass = (correlation: number) => {
  if (Math.abs(correlation - 1) < 0.001) return "correlation-self";
  if (correlation > 0.5) return "correlation-strong-positive";
  if (correlation < -0.5) return "correlation-strong-negative";
  return "correlation-weak";
};

const handleStockSelectionChange = () => {
  // 检查股票数量限制
  if (selectedStocks.value.length > 10) {
    ElMessage.warning("最多只能选择10只股票");
    selectedStocks.value = selectedStocks.value.slice(0, 10);
  }
  // 清除之前的分析结果
  analysisResult.value = null;
};

const handleAnalyzeStocks = async () => {
  if (selectedStocks.value.length < 2) {
    ElMessage.warning("至少需要选择2只股票进行分析");
    return;
  }

  analyzing.value = true;
  analysisStatus.value = "🔄 正在获取真实股票数据...";
  analysisResult.value = null;

  try {
    const response = await fetch("/gbs-api/v1/analyze-stocks-simple", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        symbols: selectedStocks.value,
        days: timeRange.value,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.detail || "分析失败");
    }

    analysisResult.value = data;
    analysisStatus.value = `✅ 真实股票数据分析完成！数据点: ${data.data_points}`;
    ElMessage.success("股票相关性分析完成");
  } catch (error: any) {
    console.error("股票分析失败:", error);
    analysisStatus.value = `❌ 分析失败: ${error.message}`;
    ElMessage.error(`分析失败: ${error.message}`);
  } finally {
    analyzing.value = false;
  }
};

onMounted(async () => {
  // CompanyMultiSelect 组件会自动加载公司数据，这里不需要额外操作
});
</script>

<style lang="scss" scoped>
.stock-correlation-container {
  .stock-selector-section {
    margin-bottom: 16px;

    .stock-count-info {
      margin-top: 8px;
      color: #909399;
    }
  }

  .analysis-controls {
    margin-bottom: 16px;
  }

  .status-section {
    margin-bottom: 16px;
  }

  .analysis-results {
    .performance-card,
    .correlation-card {
      margin-bottom: 16px;

      h5 {
        margin: 0;
        font-size: 14px;
        color: #303133;
      }
    }

    :deep(.correlation-value) {
      font-weight: bold;
      padding: 2px 4px;
      border-radius: 3px;

      &.correlation-self {
        background-color: #409eff;
        color: white;
      }

      &.correlation-strong-positive {
        background-color: #67c23a;
        color: white;
      }

      &.correlation-strong-negative {
        background-color: #f56c6c;
        color: white;
      }

      &.correlation-weak {
        background-color: #f0f0f0;
        color: #606266;
      }
    }

    .correlation-legend {
      display: flex;
      gap: 6px;
      flex-wrap: wrap;
      justify-content: center;
      margin-top: 12px;
    }
  }
}
</style>
