<template>
  <div class="run-detail-container">
    <el-card class="run-detail-card">
      <template #header>
        <div class="card-header">
          <h3>{{ displayName || runDetail?.experiment_name || '策略运行详情' }}</h3>
          <div class="actions">
            <el-button type="primary" @click="backToHistory">返回历史列表</el-button>
          </div>
        </div>
      </template>
      <div class="card-content">
        <div v-if="isLoading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>
        <div v-else-if="error" class="error-container">
          <el-result icon="error" :title="error" sub-title="请稍后重试">
            <template #extra>
              <el-button type="primary" @click="fetchRunDetail">重新加载</el-button>
            </template>
          </el-result>
        </div>
        <div v-else class="run-detail-content">
          <el-descriptions title="运行信息" :column="3" border>
            <el-descriptions-item label="策略名称">{{ displayName || runDetail?.experiment_name }}</el-descriptions-item>
            <el-descriptions-item label="运行ID">{{ runDetail?.run_id }}</el-descriptions-item>
            <el-descriptions-item label="实验ID">{{ runDetail?.experiment_id }}</el-descriptions-item>
            <el-descriptions-item label="开始时间">
              {{ runDetail?.start_time }}
              <el-tag v-if="runDetail?.start_time" style="margin-left: 12px;" type="primary"
                ><TimeConsuming v-model="runDetail.start_time"
              /></el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="结束时间">{{ runDetail?.end_time }}</el-descriptions-item>
            <el-descriptions-item label="运行时长">{{ formatDuration(runDetail?.duration || 0) }}</el-descriptions-item>
          </el-descriptions>

          <el-tabs v-model="activeTab" class="run-detail-tabs">
            <el-tab-pane label="分析结果" name="analysis">
              <div class="analysis-results-container">
                <AnalysisReport :analysis_results="runDetail?.analysis_results || []" />
              </div>
            </el-tab-pane>

            <el-tab-pane label="参数" name="params">
              <div class="params-container" v-if="Object.keys(runDetail?.params || {}).length > 0">
                <el-table :data="paramsTableData" border style="width: 100%">
                  <el-table-column prop="key" label="参数名" width="250" />
                  <el-table-column prop="value" label="参数值" />
                </el-table>
              </div>
              <el-empty v-else description="无参数数据" />
            </el-tab-pane>

            <el-tab-pane label="标签" name="tags">
              <div class="tags-container" v-if="Object.keys(runDetail?.tags || {}).length > 0">
                <el-table :data="tagsTableData" border style="width: 100%">
                  <el-table-column prop="key" label="标签名" width="250" />
                  <el-table-column prop="value" label="标签值" />
                </el-table>
              </div>
              <el-empty v-else description="无标签数据" />
            </el-tab-pane>

            <el-tab-pane label="指标" name="metrics">
              <div class="metrics-container" v-if="Object.keys(runDetail?.metrics || {}).length > 0">
                <el-table :data="metricsTableData" border style="width: 100%">
                  <el-table-column prop="key" label="指标名" width="250" />
                  <el-table-column prop="value" label="指标值" />
                </el-table>
              </div>
              <el-empty v-else description="无指标数据" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getStrategyRunDetail, type StrategyRunDetail } from "@/api/strategy";
import AnalysisReport from "./AnalysisReport.vue";
import TimeConsuming from "@/components/TimeConsuming.vue";

const router = useRouter();
const route = useRoute();
const runId = ref(route.params.run_id as string);
const experimentId = ref(route.params.experiment_id as string);
const runDetail = ref<StrategyRunDetail | null>(null);
const isLoading = ref(true);
const error = ref<string | null>(null);
const activeTab = ref('analysis');

// 实验名称到显示名称的映射
const experimentToDisplayMap: Record<string, string> = {
  "Qlib_Style_Workflow": "AIPM 策略",
  "Backtest_Only_Workflow": "回测策略",
  // 添加更多映射...
};

// 获取显示名称
const displayName = computed(() => {
  if (!runDetail.value) return '';
  return experimentToDisplayMap[runDetail.value.experiment_name] || runDetail.value.experiment_name;
});

// 转换参数为表格数据
const paramsTableData = computed(() => {
  if (!runDetail.value || !runDetail.value.params) return [];
  return Object.entries(runDetail.value.params).map(([key, value]) => ({
    key,
    value: typeof value === 'object' ? JSON.stringify(value) : String(value)
  }));
});

// 转换标签为表格数据
const tagsTableData = computed(() => {
  if (!runDetail.value || !runDetail.value.tags) return [];
  return Object.entries(runDetail.value.tags)
    .filter(([key]) => !key.startsWith('mlflow.')) // 过滤掉MLflow系统标签
    .map(([key, value]) => ({
      key,
      value: String(value)
    }));
});

// 转换指标为表格数据
const metricsTableData = computed(() => {
  if (!runDetail.value || !runDetail.value.metrics) return [];
  return Object.entries(runDetail.value.metrics).map(([key, value]) => ({
    key,
    value: typeof value === 'number' ? value.toFixed(4) : String(value)
  }));
});

// 获取运行详情
const fetchRunDetail = async () => {
  isLoading.value = true;
  error.value = null;

  try {
    // 获取运行详情
    runDetail.value = await getStrategyRunDetail(runId.value, experimentId.value);
  } catch (err) {
    console.error("Failed to fetch run detail:", err);
    error.value = "获取运行详情失败";
  } finally {
    isLoading.value = false;
  }
};

// 格式化运行时长
const formatDuration = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds.toFixed(1)}秒`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds.toFixed(0)}秒`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}小时${minutes}分钟`;
  }
};

// 返回历史列表
const backToHistory = () => {
  router.back();
};

// 组件挂载时获取运行详情
onMounted(() => {
  fetchRunDetail();
});
</script>

<style lang="scss">
.run-detail-container {
  padding: 20px;

  .run-detail-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .loading-container,
    .error-container {
      padding: 20px;
    }

    .run-detail-content {
      .run-detail-tabs {
        margin-top: 20px;

        .analysis-results-container,
        .params-container,
        .tags-container,
        .metrics-container {
          margin-top: 15px;
        }
      }
    }
  }
}
</style>
