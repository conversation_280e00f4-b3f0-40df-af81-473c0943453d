<template>
  <el-drawer
    v-model="drawerVisibleComputed"
    title="Backtest Analysis Report"
    size="90%"
    :with-header="true"
    direction="rtl"
  >
    <el-progress
      v-if="props.loading"
      :percentage="50"
      :show-text="false"
      :indeterminate="true"
    />
    <AnalysisReport :analysis_results="analysis_results" />
  </el-drawer>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import AnalysisReport from "@/components/AnalysisReport.vue";

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
  },
  analysis_results: {
    type: Array,
  },
});

const emit = defineEmits(["update:visible", "apply-params"]);

const drawerVisibleComputed = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});
</script>
