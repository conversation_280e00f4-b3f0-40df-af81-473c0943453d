<template>
  <div class="factor-system-container">
    <el-row :gutter="20">
      <el-col :span="4">
        <el-card class="factor-menu-card">
          <template #header>
            <div class="card-header">
              <h3>因子系统</h3>
            </div>
          </template>
          <el-menu router :default-active="activeMenu" class="factor-menu">
            <el-menu-item index="/factor/features">
              <el-icon><DataAnalysis /></el-icon>
              <span>因子特征分析</span>
            </el-menu-item>
            <el-menu-item index="/factor/covariance">
              <el-icon><Grid /></el-icon>
              <span>因子协方差分析</span>
            </el-menu-item>
            <el-menu-item index="/factor/returns">
              <el-icon><Money /></el-icon>
              <span>因子收益分析(计算) ❌</span>
            </el-menu-item>
            <el-menu-item index="/factor/read">
              <el-icon><Money /></el-icon>
              <span>因子收益分析(读取)</span>
            </el-menu-item>
          </el-menu>
        </el-card>
      </el-col>
      <el-col :span="20">
        <div v-if="showWelcome" class="welcome-container">
          <el-card class="welcome-card">
            <div class="welcome-content">
              <el-icon :size="80" class="welcome-icon">
                <DataBoard />
              </el-icon>
              <h2>欢迎使用因子系统</h2>
              <p class="welcome-description">
                因子系统提供了全面的因子分析工具，包括因子协方差分析、因子收益分析等功能。
                请从左侧菜单选择您需要的分析工具。
              </p>
            </div>
          </el-card>
        </div>
        <router-view v-else />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import { useRoute } from "vue-router";
import { DataBoard, Grid, Money, DataAnalysis } from "@element-plus/icons-vue";

const route = useRoute();

// 计算当前激活的菜单项
const activeMenu = computed(() => {
  return route.path;
});

// 计算是否显示欢迎页面
const showWelcome = computed(() => {
  return route.path === "/factor";
});

onMounted(() => {
  console.log("Factor System mounted");
});
</script>

<style lang="scss">
.factor-system-container {
  padding: 20px;

  .factor-menu-card {
    margin-bottom: 20px;
    position: sticky;
    top: 12px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .factor-menu {
      border-right: none;
    }
  }

  .welcome-container {
    .welcome-card {
      min-height: 500px;
      display: flex;
      align-items: center;
      justify-content: center;

      .welcome-content {
        text-align: center;
        padding: 40px;

        .welcome-icon {
          color: var(--el-color-primary);
          margin-bottom: 20px;
        }

        h2 {
          margin: 0 0 20px 0;
          font-size: 28px;
          color: var(--el-text-color-primary);
        }

        .welcome-description {
          margin: 0;
          font-size: 16px;
          color: var(--el-text-color-regular);
          line-height: 1.6;
          max-width: 600px;
        }
      }
    }
  }
}
</style>
