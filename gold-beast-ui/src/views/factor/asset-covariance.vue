<template>
  <div class="asset-covariance-container">
    <el-card class="asset-covariance-card">
      <template #header>
        <div class="card-header">
          <h3>资产协方差分析</h3>
        </div>
      </template>
      <div class="card-content">
        <!-- 参数表单 -->
        <el-form
          :model="formData"
          label-width="120px"
          class="asset-covariance-form"
          size="default"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="日期">
                <el-date-picker
                  v-model="formData.date"
                  type="date"
                  placeholder="选择日期"
                  format="YYYYMMDD"
                  value-format="YYYYMMDD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="股票代码">
                <CompanySelect
                  v-model="formData.ticker"
                  placeholder="请选择股票"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="模型类型">
                <el-select
                  v-model="formData.model_type"
                  placeholder="请选择模型类型"
                  style="width: 100%"
                >
                  <el-option label="MH" value="MH" />
                  <el-option label="SH" value="SH" />
                  <el-option label="MH-S" value="MH-S" />
                  <el-option label="SH-S" value="SH-S" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item>
                <el-button
                  type="primary"
                  @click="fetchAssetCovarianceData"
                  :loading="loading"
                >
                  查询
                </el-button>
                <el-button @click="resetForm">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="10" animated />
        </div>

        <!-- 结果展示 -->
        <div v-else-if="assetCovarianceData" class="result-container">
          <el-tabs v-model="activeTab" type="border-card">
            <el-tab-pane label="协方差图" name="covariance">
              <div class="chart-container" ref="covarianceChartRef"></div>
            </el-tab-pane>
            <el-tab-pane label="相关性图" name="correlation">
              <div class="chart-container" ref="correlationChartRef"></div>
            </el-tab-pane>
            <el-tab-pane label="数据表格" name="table">
              <el-table
                :data="tableData"
                border
                style="width: 100%"
                height="400"
                stripe
              >
                <el-table-column prop="asset" label="资产" width="120" />
                <el-table-column prop="covariance" label="协方差" width="120" />
                <el-table-column
                  prop="correlation"
                  label="相关系数"
                  width="120"
                />
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 空状态 -->
        <el-empty v-else description="请输入参数并点击查询按钮" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import { calculateAssetCovariance } from "@/api/factorService";
import * as echarts from "echarts";
import { ElMessage } from "element-plus";
import CompanySelect from '@/components/CompanySelect.vue';

// 表单数据
const formData = reactive({
  date: "",
  ticker: "",
  model_type: "MH",
});

const loading = ref(false);
const assetCovarianceData = ref<any>(null);
const activeTab = ref("covariance");
const covarianceChartRef = ref<HTMLElement | null>(null);
const correlationChartRef = ref<HTMLElement | null>(null);
let covarianceChart: echarts.ECharts | null = null;
let correlationChart: echarts.ECharts | null = null;
const tableData = ref<any[]>([]);

// 获取资产协方差数据
const fetchAssetCovarianceData = async () => {
  if (!formData.date || !formData.ticker) {
    ElMessage.warning("请填写必要的查询参数");
    return;
  }

  loading.value = true;
  try {
    const params = {
      date: formData.date,
      ticker: formData.ticker,
      model_type: formData.model_type,
    };

    const data = await calculateAssetCovariance(params);
    assetCovarianceData.value = data;

    // 转换数据为表格格式
    transformToTableData();

    // 在下一个tick渲染图表
    setTimeout(() => {
      renderCovarianceChart();
      renderCorrelationChart();
    }, 0);
  } catch (error) {
    console.error("获取资产协方差数据失败:", error);
    ElMessage.error("获取数据失败，请检查参数后重试");
  } finally {
    loading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  formData.date = "";
  formData.ticker = "";
  formData.model_type = "MH";
  assetCovarianceData.value = null;
  if (covarianceChart) {
    covarianceChart.dispose();
    covarianceChart = null;
  }
  if (correlationChart) {
    correlationChart.dispose();
    correlationChart = null;
  }
};

// 转换数据为表格格式
const transformToTableData = () => {
  if (!assetCovarianceData.value) return;

  const { assets, covariance, correlation } = assetCovarianceData.value;
  tableData.value = assets.map((asset: string) => ({
    asset,
    covariance: covariance[asset]?.toFixed(6) || "-",
    correlation: correlation[asset]?.toFixed(4) || "-",
  }));
};

// 渲染协方差图表
const renderCovarianceChart = () => {
  if (!covarianceChartRef.value || !assetCovarianceData.value) return;

  if (covarianceChart) {
    covarianceChart.dispose();
  }

  covarianceChart = echarts.init(covarianceChartRef.value);

  const { assets, covariance, ticker } = assetCovarianceData.value;

  // 按协方差值排序
  const sortedData = assets
    .map((asset: string) => ({
      asset,
      value: covariance[asset],
    }))
    .sort((a: any, b: any) => Math.abs(b.value) - Math.abs(a.value));

  // 取前20个资产
  const topAssets = sortedData.slice(0, 20);

  const option = {
    title: {
      text: `${ticker} 与其他资产的协方差`,
      left: "center",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params: any) {
        const asset = params[0].name;
        const value = params[0].value;
        return `${asset}: ${value.toFixed(6)}`;
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "value",
      name: "协方差",
      axisLabel: {
        formatter: function (value: number) {
          return value.toExponential(2);
        },
      },
    },
    yAxis: {
      type: "category",
      data: topAssets.map((item: any) => item.asset),
      axisLabel: {
        interval: 0,
        rotate: 0,
      },
    },
    visualMap: {
      orient: "horizontal",
      left: "center",
      min: -0.001,
      max: 0.001,
      text: ["高", "低"],
      dimension: 0,
      inRange: {
        color: ["#f36c6c", "#e0e0e0", "#6cacf3"],
      },
    },
    series: [
      {
        name: "协方差",
        type: "bar",
        data: topAssets.map((item: any) => item.value),
      },
    ],
  };

  covarianceChart.setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", () => {
    covarianceChart?.resize();
  });
};

// 渲染相关性图表
const renderCorrelationChart = () => {
  if (!correlationChartRef.value || !assetCovarianceData.value) return;

  if (correlationChart) {
    correlationChart.dispose();
  }

  correlationChart = echarts.init(correlationChartRef.value);

  const { assets, correlation, ticker } = assetCovarianceData.value;

  // 按相关系数绝对值排序
  const sortedData = assets
    .map((asset: string) => ({
      asset,
      value: correlation[asset],
    }))
    .sort((a: any, b: any) => Math.abs(b.value) - Math.abs(a.value));

  // 取前20个资产
  const topAssets = sortedData.slice(0, 20);

  const option = {
    title: {
      text: `${ticker} 与其他资产的相关系数`,
      left: "center",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params: any) {
        const asset = params[0].name;
        const value = params[0].value;
        return `${asset}: ${value.toFixed(4)}`;
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "value",
      name: "相关系数",
      min: -1,
      max: 1,
    },
    yAxis: {
      type: "category",
      data: topAssets.map((item: any) => item.asset),
      axisLabel: {
        interval: 0,
        rotate: 0,
      },
    },
    visualMap: {
      orient: "horizontal",
      left: "center",
      min: -1,
      max: 1,
      text: ["正相关", "负相关"],
      dimension: 0,
      inRange: {
        color: ["#f36c6c", "#e0e0e0", "#6cacf3"],
      },
    },
    series: [
      {
        name: "相关系数",
        type: "bar",
        data: topAssets.map((item: any) => item.value),
      },
    ],
  };

  correlationChart.setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", () => {
    correlationChart?.resize();
  });
};

// 监听activeTab变化，切换到对应图表时重新渲染
watch(activeTab, (newVal) => {
  if (newVal === "covariance" && assetCovarianceData.value) {
    setTimeout(() => {
      renderCovarianceChart();
    }, 0);
  } else if (newVal === "correlation" && assetCovarianceData.value) {
    setTimeout(() => {
      renderCorrelationChart();
    }, 0);
  }
});

onMounted(() => {
  console.log("Asset Covariance component mounted");
});
</script>

<style lang="scss">
.asset-covariance-container {
  .asset-covariance-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-content {
      min-height: 600px;

      .asset-covariance-form {
        margin-bottom: 20px;
      }

      .loading-container {
        padding: 20px;
      }

      .result-container {
        .chart-container {
          height: 500px;
          width: 100%;
        }
      }
    }
  }
}
</style>
