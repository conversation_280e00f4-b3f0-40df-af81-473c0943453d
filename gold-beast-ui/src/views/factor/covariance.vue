<template>
  <div class="factor-covariance-container">
    <el-card class="factor-covariance-card">
      <template #header>
        <div class="card-header">
          <h3>因子协方差分析</h3>
        </div>
      </template>
      <div class="card-content">
        <!-- 参数表单 -->
        <el-form
          :model="formData"
          label-width="120px"
          class="covariance-form"
          size="default"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="日期">
                <el-date-picker
                  v-model="formData.date"
                  type="date"
                  placeholder="选择日期"
                  format="YYYYMMDD"
                  value-format="YYYYMMDD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="模型类型">
                <el-select
                  v-model="formData.model_type"
                  placeholder="请选择模型类型"
                  style="width: 100%"
                >
                  <el-option label="MH" value="MH" />
                  <el-option label="SH" value="SH" />
                  <el-option label="MH-S" value="MH-S" />
                  <el-option label="SH-S" value="SH-S" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <el-button
                  type="primary"
                  @click="fetchCovarianceData"
                  :loading="loading"
                >
                  查询
                </el-button>
                <el-button @click="resetForm">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="10" animated />
        </div>

        <!-- 结果展示 -->
        <div v-else-if="covarianceData" class="result-container">
          <h4>因子协方差热力图</h4>
          <div class="chart-container" ref="heatmapRef"></div>

          <h4 class="section-title">因子相关性矩阵</h4>
          <div class="chart-container" ref="correlationRef"></div>

          <h4 class="section-title">因子协方差数据表</h4>
          <PaginatedTable
            :data="tableData"
            :columns="tableColumns"
            :page-size="15"
            :page-size-options="[15, 50, 100, 200]"
            :max-items-before-pagination="15"
          />
        </div>

        <!-- 空状态 -->
        <el-empty v-else description="请输入参数并点击查询按钮" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { getFactorCovariance } from "@/api/factorService";
import * as echarts from "echarts";
import { ElMessage } from "element-plus";
import PaginatedTable from "@/components/common/PaginatedTable.vue";

// 表单数据
const formData = reactive({
  date: "",
  model_type: "MH",
});

const loading = ref(false);
const covarianceData = ref<any>(null);
const heatmapRef = ref<HTMLElement | null>(null);
const correlationRef = ref<HTMLElement | null>(null);
let heatmapChart: echarts.ECharts | null = null;
let correlationChart: echarts.ECharts | null = null;
const tableData = ref<any[]>([]);

// 表格列定义
const tableColumns = computed(() => {
  if (!covarianceData.value) return [];

  const columns = [
    {
      prop: 'factor',
      label: '因子',
      minWidth: 120,
      fixed: true,
      sortable: true,
    }
  ];

  // 添加因子列
  covarianceData.value.factors.forEach((factor: string) => {
    columns.push({
      prop: factor,
      label: factor,
      minWidth: 120,
      sortable: true,
    });
  });

  return columns;
});

// 获取因子协方差数据
const fetchCovarianceData = async () => {
  if (!formData.date) {
    ElMessage.warning("请选择日期");
    return;
  }

  loading.value = true;
  try {
    const params = {
      date: formData.date,
      model_type: formData.model_type,
    };

    const data = await getFactorCovariance(params);
    covarianceData.value = data;

    // 转换数据为表格格式
    transformToTableData();

    // 在下一个tick渲染图表
    setTimeout(() => {
      renderHeatmap();
      renderCorrelation();
    }, 0);
  } catch (error) {
    console.error("获取因子协方差数据失败:", error);
    ElMessage.error("获取数据失败，请检查参数后重试");
  } finally {
    loading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  formData.date = "";
  formData.model_type = "MH";
  covarianceData.value = null;
  if (heatmapChart) {
    heatmapChart.dispose();
    heatmapChart = null;
  }
  if (correlationChart) {
    correlationChart.dispose();
    correlationChart = null;
  }
};

// 转换数据为表格格式
const transformToTableData = () => {
  if (!covarianceData.value) return;

  const { factors, data } = covarianceData.value;
  tableData.value = factors.map((factor: string) => {
    const row: any = { factor };
    factors.forEach((f: string) => {
      row[f] = data[factor][f]?.toFixed(4) || "-";
    });
    return row;
  });
};

// 渲染热力图
const renderHeatmap = () => {
  if (!heatmapRef.value || !covarianceData.value) return;

  if (heatmapChart) {
    heatmapChart.dispose();
  }

  heatmapChart = echarts.init(heatmapRef.value);

  const { factors, data } = covarianceData.value;

  // 准备热力图数据
  const heatmapData: any[] = [];
  factors.forEach((rowFactor: string, i: number) => {
    factors.forEach((colFactor: string, j: number) => {
      heatmapData.push([i, j, data[rowFactor][colFactor]]);
    });
  });

  const option = {
    title: {
      text: `因子协方差矩阵 (${formData.date})`,
      left: "center",
    },
    tooltip: {
      position: "top",
      formatter: function (params: any) {
        return `${factors[params.data[0]]} - ${
          factors[params.data[1]]
        }: ${params.data[2].toFixed(4)}`;
      },
    },
    grid: {
      height: "76%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: factors,
      splitArea: {
        show: true,
      },
    },
    yAxis: {
      type: "category",
      data: factors,
      splitArea: {
        show: true,
      },
    },
    visualMap: {
      min: -0.1,
      max: 0.1,
      calculable: true,
      orient: "horizontal",
      left: "center",
      bottom: "5%",
    },
    series: [
      {
        name: "协方差",
        type: "heatmap",
        data: heatmapData,
        label: {
          show: false,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };

  heatmapChart.setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", () => {
    heatmapChart?.resize();
  });
};

// 渲染相关性矩阵
const renderCorrelation = () => {
  if (!correlationRef.value || !covarianceData.value) return;

  if (correlationChart) {
    correlationChart.dispose();
  }

  correlationChart = echarts.init(correlationRef.value);

  const { factors, data } = covarianceData.value;

  // 计算相关性矩阵
  const correlationData: any[] = [];
  factors.forEach((rowFactor: string, i: number) => {
    factors.forEach((colFactor: string, j: number) => {
      // 简化计算，实际应该使用协方差除以标准差的乘积
      const correlation =
        data[rowFactor][colFactor] /
        Math.sqrt(
          Math.abs(data[rowFactor][rowFactor] * data[colFactor][colFactor])
        );
      correlationData.push([i, j, correlation]);
    });
  });

  const option = {
    title: {
      text: `因子相关性矩阵 (${formData.date})`,
      left: "center",
    },
    tooltip: {
      position: "top",
      formatter: function (params: any) {
        return `${factors[params.data[0]]} - ${
          factors[params.data[1]]
        }: ${params.data[2].toFixed(4)}`;
      },
    },
    grid: {
      height: "76%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: factors,
      splitArea: {
        show: true,
      },
    },
    yAxis: {
      type: "category",
      data: factors,
      splitArea: {
        show: true,
      },
    },
    visualMap: {
      min: -1,
      max: 1,
      calculable: true,
      orient: "horizontal",
      left: "center",
      bottom: "5%",
      inRange: {
        color: [
          "#313695",
          "#4575b4",
          "#74add1",
          "#abd9e9",
          "#e0f3f8",
          "#ffffbf",
          "#fee090",
          "#fdae61",
          "#f46d43",
          "#d73027",
          "#a50026",
        ],
      },
    },
    series: [
      {
        name: "相关性",
        type: "heatmap",
        data: correlationData,
        label: {
          show: false,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };

  correlationChart.setOption(option);

  // 响应窗口大小变化
  window.addEventListener("resize", () => {
    correlationChart?.resize();
  });
};

// 不再需要监听标签页变化

onMounted(() => {
  console.log("Factor Covariance component mounted");
});
</script>

<style lang="scss">
.factor-covariance-container {
  .factor-covariance-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-content {
      min-height: 600px;

      .covariance-form {
        margin-bottom: 20px;
      }

      .loading-container {
        padding: 20px;
      }

      .result-container {
        h4 {
          margin: 20px 0 15px;
          font-weight: 500;
        }

        .section-title {
          margin-top: 40px;
        }

        .chart-container {
          height: 700px;
          width: 100%;
          margin-bottom: 20px;
        }
      }
    }
  }
}
</style>
