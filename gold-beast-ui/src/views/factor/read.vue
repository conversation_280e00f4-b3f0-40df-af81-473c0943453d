<template>
  <div class="factor-read-container">
    <el-card class="factor-read-card">
      <template #header>
        <div class="card-header">
          <h3>因子收益分析(读取)</h3>
        </div>
      </template>
      <div class="card-content">
        <!-- 参数表单 -->
        <el-form
          :model="formData"
          label-width="120px"
          class="read-form"
          size="default"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="模型类型">
                <el-select
                  v-model="formData.model_type"
                  placeholder="请选择模型类型"
                  style="width: 100%"
                >
                  <el-option label="MH" value="MH" />
                  <el-option label="SH" value="SH" />
                  <el-option label="MH-S" value="MH-S" />
                  <el-option label="SH-S" value="SH-S" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="日期范围">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYYMMDD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="16">
              <el-form-item>
                <el-button type="primary" @click="fetchData" :loading="loading"
                  >查询</el-button
                >
                <el-button @click="resetForm">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <!-- 加载中 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="10" animated />
        </div>

        <!-- 结果展示 -->
        <div v-else-if="factorReturnsData" class="result-container">
          <h4>因子收益率趋势图</h4>
          <div class="factor-filter-container">
            <div class="factor-filter">
              <span class="filter-label">筛选因子:</span>
              <el-select
                v-model="selectedFactors"
                multiple
                placeholder="请选择因子"
                style="width: 70%"
                size="small"
              >
                <el-option
                  v-for="factor in factorOptions"
                  :key="factor.value"
                  :label="factor.label"
                  :value="factor.value"
                />
              </el-select>
              <el-select
                v-model="factorSortMethod"
                placeholder="排序方式"
                style="width: 150px; margin-left: 10px"
                size="small"
              >
                <el-option label="收益率(高到低)" value="returnDesc" />
                <el-option label="收益率(低到高)" value="returnAsc" />
                <el-option label="波动率(高到低)" value="volatilityDesc" />
                <el-option label="名称排序" value="nameAsc" />
              </el-select>
            </div>
          </div>
          <div class="chart-container" ref="chartRef"></div>

          <h4 class="table-title">因子收益率数据表</h4>
          <PaginatedTable
            :data="tableData"
            :columns="tableColumns"
            :page-size="15"
            :page-size-options="[15, 50, 100, 200]"
            :max-items-before-pagination="15"
          />
        </div>

        <!-- 无数据提示 -->
        <el-empty v-else description="暂无数据，请设置参数并查询" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from "vue";
import { readFactorReturns } from "@/api/factorService";
import * as echarts from "echarts";
import { ElMessage } from "element-plus";
import { useEchartResize } from "@/composables/useEchartResize";
import PaginatedTable from "@/components/common/PaginatedTable.vue";

// 表单数据
const formData = reactive({
  model_type: "MH",
  start_date: "",
  end_date: "",
});

// 使用计算属性处理日期范围
const dateRange = computed({
  get: () => {
    // 如果开始日期和结束日期都有值，返回数组
    if (formData.start_date && formData.end_date) {
      return [formData.start_date, formData.end_date];
    }
    return null;
  },
  set: (val) => {
    if (val && Array.isArray(val) && val.length === 2) {
      formData.start_date = val[0];
      formData.end_date = val[1];
    } else {
      formData.start_date = "";
      formData.end_date = "";
    }
  },
});

// 加载状态
const loading = ref(false);

// 因子收益率数据
const factorReturnsData = ref(null);

// 表格数据
const tableData = ref([]);

// 图表实例
let chart = null;
let chartResize = null;

// 图表容器引用
const chartRef = ref(null);

// 表格列定义
const tableColumns = computed(() => {
  if (!factorReturnsData.value || selectedFactors.value.length === 0) return [];

  const columns = [
    {
      prop: 'date',
      label: '日期',
      minWidth: 120,
      fixed: true,
      sortable: true,
    }
  ];

  // 添加选中的因子列
  selectedFactors.value.forEach(factor => {
    columns.push({
      prop: factor,
      label: factor,
      minWidth: 120,
      sortable: true,
    });
  });

  return columns;
});

// 选中的因子
const selectedFactors = ref([]);

// 因子排序方式
const factorSortMethod = ref("returnDesc");

// 因子选项列表
const factorOptions = ref([]);

// 计算因子统计数据
const calculateFactorStats = (data, dates) => {
  const stats = {};

  // 遍历所有因子
  factorReturnsData.value.factors.forEach((factor) => {
    const returns = dates.map((date) => data[date][factor]);

    // 计算平均收益率
    const avgReturn =
      returns.reduce((sum, val) => sum + val, 0) / returns.length;

    // 计算波动率 (标准差)
    const variance =
      returns.reduce((sum, val) => sum + Math.pow(val - avgReturn, 2), 0) /
      returns.length;
    const volatility = Math.sqrt(variance);

    // 计算最大和最小收益率
    const maxReturn = Math.max(...returns);
    const minReturn = Math.min(...returns);

    stats[factor] = {
      avgReturn,
      volatility,
      maxReturn,
      minReturn,
    };
  });

  return stats;
};

// 获取数据
const fetchData = async () => {
  if (!formData.start_date || !formData.end_date) {
    ElMessage.warning("请选择开始日期和结束日期");
    return;
  }

  loading.value = true;
  try {
    const params = {
      model_type: formData.model_type,
      start_date: formData.start_date,
      end_date: formData.end_date,
    };

    const data = await readFactorReturns(params);
    factorReturnsData.value = data;

    // 计算因子统计数据
    const factorStats = calculateFactorStats(data.data, data.dates);

    // 生成因子选项列表
    factorOptions.value = data.factors.map((factor) => ({
      label: factor,
      value: factor,
      stats: factorStats[factor],
    }));

    // 根据选择的排序方式对因子进行排序
    sortFactors();

    // 转换数据为表格格式
    transformToTableData();

    // 在下一个tick渲染图表
    setTimeout(() => {
      renderChart();
    }, 0);
  } catch (error) {
    console.error("获取因子收益率数据失败:", error);
    ElMessage.error("获取数据失败，请检查参数后重试");
  } finally {
    loading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  formData.model_type = "MH";
  formData.start_date = "";
  formData.end_date = "";
  factorReturnsData.value = null;
  selectedFactors.value = [];
  tableData.value = [];
  if (chart) {
    chart.dispose();
    chart = null;
    chartResize = null;
  }
};

// 转换数据为表格格式
const transformToTableData = () => {
  if (!factorReturnsData.value) return;

  const { data, dates } = factorReturnsData.value;

  tableData.value = dates.map((date) => {
    const row = { date };
    selectedFactors.value.forEach((factor) => {
      row[factor] = (data[date][factor] * 100).toFixed(4) + "%";
    });
    return row;
  });
};

// 渲染图表
const renderChart = () => {
  if (
    !chartRef.value ||
    !factorReturnsData.value ||
    selectedFactors.value.length === 0
  )
    return;

  if (chart) {
    chart.dispose();
    chartResize = null;
  }

  chart = echarts.init(chartRef.value);
  // 应用 useEchartResize
  chartResize = useEchartResize(chart);

  const { data, dates } = factorReturnsData.value;

  // 准备系列数据
  const series = selectedFactors.value.map((factor) => {
    return {
      name: factor,
      type: "line",
      symbol: "none",
      data: dates.map((date) =>
        parseFloat((data[date][factor] * 100).toFixed(4))
      ),
    };
  });

  const option = {
    title: {
      text: "因子收益率趋势",
      left: "center",
    },
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        let result = params[0].axisValue + "<br/>";
        params.forEach((param) => {
          result +=
            param.marker +
            param.seriesName +
            ": " +
            param.data.toFixed(4) +
            "%<br/>";
        });
        return result;
      },
    },
    legend: {
      data: selectedFactors.value,
      type: "scroll",
      bottom: 0,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: dates,
      axisLabel: {
        interval: "auto",
        rotate: 45,
      },
    },
    yAxis: {
      type: "value",
      name: "收益率 (%)",
      axisLabel: {
        formatter: "{value}%",
      },
    },
    series,
  };

  chart.setOption(option);
};

// 排序因子
const sortFactors = () => {
  if (!factorOptions.value.length) return;

  const sortedOptions = [...factorOptions.value];

  switch (factorSortMethod.value) {
    case "returnDesc":
      sortedOptions.sort((a, b) => b.stats.avgReturn - a.stats.avgReturn);
      break;
    case "returnAsc":
      sortedOptions.sort((a, b) => a.stats.avgReturn - b.stats.avgReturn);
      break;
    case "volatilityDesc":
      sortedOptions.sort((a, b) => b.stats.volatility - a.stats.volatility);
      break;
    case "nameAsc":
      sortedOptions.sort((a, b) => a.label.localeCompare(b.label));
      break;
  }

  factorOptions.value = sortedOptions;

  // 每次排序后都更新选中的因子为前10个
  selectedFactors.value = sortedOptions
    .slice(0, 10)
    .map((option) => option.value);

  // 打印日志以便调试
  console.log(`排序方式: ${factorSortMethod.value}`);
  console.log("选中的因子:", selectedFactors.value);
  console.log("前5个因子及其统计数据:");
  sortedOptions.slice(0, 5).forEach((option) => {
    console.log(
      `${option.label}: 收益率=${option.stats.avgReturn.toFixed(
        4
      )}, 波动率=${option.stats.volatility.toFixed(4)}`
    );
  });
};

// 监听选中因子变化，更新表格和图表
watch(
  selectedFactors,
  () => {
    transformToTableData();
    renderChart();
  },
  { deep: true }
);

// 监听排序方式变化
watch(factorSortMethod, (newValue) => {
  console.log(`排序方式变更为: ${newValue}`);
  sortFactors();
});

// 组件挂载时初始化
onMounted(() => {
  console.log("Factor Read Component mounted");
});
</script>

<style lang="scss">
.factor-read-container {
  .factor-read-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .read-form {
      margin-bottom: 20px;
    }

    .loading-container {
      padding: 20px;
    }

    .result-container {
      .factor-filter-container {
        margin: 15px 0;
        width: 100%;
      }

      .factor-filter {
        display: flex;
        align-items: center;
        width: 100%;
      }

      .filter-label {
        margin-right: 10px;
        font-size: 14px;
        color: #606266;
      }

      .chart-container {
        height: 400px;
        width: 100%;
        margin-bottom: 30px;
      }

      h4 {
        margin: 0;
        font-weight: 500;
      }

      .table-title {
        margin-top: 30px;
        margin-bottom: 15px;
      }
    }
  }
}
</style>
