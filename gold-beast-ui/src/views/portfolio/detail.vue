<template>
  <div class="portfolio-container">
    <el-card class="portfolio-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <h3>投资组合详情: {{ currentPortfolio?.name }}</h3>
            <el-tag v-if="currentPortfolio?.model_type" size="small">
              {{ currentPortfolio.model_type }}
            </el-tag>
          </div>
          <div class="header-actions">
            <el-button @click="goBack">返回列表</el-button>
            <el-button type="warning" @click="showEditDialog">编辑</el-button>
            <el-button
              type="primary"
              v-run-portfolio="{
                portfolio_id: portfolioId,
                skip_configuration: false,
              }"
            >
              运行回测
            </el-button>
          </div>
        </div>
      </template>
      <div class="card-content">
        <!-- 投资组合信息 -->
        <div class="portfolio-info">
          <div class="portfolio-stocks">
            <h4>股票列表</h4>
            <el-table :data="portfolio" style="width: 100%" border>
              <el-table-column prop="ticker" label="股票代码" />
              <el-table-column prop="weight" label="权重">
                <template #default="scope">
                  {{ (scope.row.weight * 100).toFixed(2) }}%
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 回测详情 -->
        <div v-if="backtestLoading" class="loading-container">
          <el-skeleton :rows="10" animated />
        </div>

        <div v-else-if="latestBacktestData" class="backtest-results">
          <AnalysisReport
            :analysis_results="latestBacktestData.analysis_results || []"
            :model-type="latestBacktestData.params?.model_type || 'MH'"
            :factors-dir="latestBacktestData.params?.factors_dir || 'factors'"
            class="analysis-report"
          />
        </div>

        <div v-else class="empty-state">
          <el-empty description="暂无回测数据">
            <template #description>
              <div>
                <p>暂无回测数据</p>
                <p style="color: #999; font-size: 12px">
                  请先运行投资组合回测以查看分析结果
                </p>
              </div>
            </template>
          </el-empty>
        </div>
      </div>
    </el-card>

    <!-- 投资组合对话框 -->
    <PortfolioDialog
      v-model:visible="dialogVisible"
      v-model:portfolio="editingPortfolio"
      :loading="saveLoading"
      @update="handleUpdate"
      @closed="handleDialogClosed"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import PortfolioDialog from "@/components/portfolio/PortfolioDialog.vue";
import AnalysisReport from "@/components/AnalysisReport.vue";
import {
  getPortfolio,
  updatePortfolio as apiUpdatePortfolio,
  type Portfolio,
  type PortfolioCreateUpdate,
  type PortfolioStock,
} from "@/api/portfolioService";
import {
  getStrategyRunDetail,
  type StrategyRunDetail,
} from "@/api/strategy";

// 路由
const route = useRoute();
const router = useRouter();

// 状态
const currentPortfolio = ref<Portfolio | null>(null);
const loading = ref(false);
const backtestLoading = ref(false);
const latestBacktestData = ref<StrategyRunDetail | null>(null);
const portfolio = ref<PortfolioStock[]>([]);

// 对话框状态
const dialogVisible = ref(false);
const editingPortfolio = ref<Portfolio | null>(null);
const saveLoading = ref(false);

const portfolioId = computed(() => route.params.id as string);

// 初始化
onMounted(async () => {
  console.log("Detail component mounted");
  const portfolioId = route.params.id as string;

  if (!portfolioId) {
    console.warn("No portfolio ID provided, redirecting to portfolio list");
    router.push("/portfolio");
    return;
  }

  await fetchPortfolioData(portfolioId);
  await fetchLatestBacktestData();
});

// 获取投资组合数据
const fetchPortfolioData = async (portfolioId: string) => {
  loading.value = true;
  try {
    console.log(`Fetching portfolio with ID: ${portfolioId}`);
    // 直接调用 API 获取投资组合详情
    const data = await getPortfolio(portfolioId);
    console.log("Portfolio data fetched:", data);

    currentPortfolio.value = data;
    portfolio.value = data.stocks;
  } catch (error) {
    console.error("Error fetching portfolio:", error);
    ElMessage.error("获取投资组合详情失败");
  } finally {
    loading.value = false;
  }
};

// 获取最新回测数据
const fetchLatestBacktestData = async () => {
  if (!currentPortfolio.value) {
    console.warn("No portfolio data available");
    return;
  }

  backtestLoading.value = true;
  try {
    console.log("Fetching latest backtest data for portfolio:", currentPortfolio.value.name);

    // 使用 backtest_group_id 查询最新的回测记录，这样更稳定
    // 格式为 portfolio_{portfolio_id}，与后端设置的标签一致
    const backtestGroupId = `portfolio_${currentPortfolio.value.id}`;
    console.log("Using backtest_group_id:", backtestGroupId);

    // 直接调用 MLflow API，使用 backtest_group_id 参数查询
    const response = await fetch(`/gbs-api/v1/mlflows/history?backtest_group_id=${encodeURIComponent(backtestGroupId)}&limit=1`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    const runs = data.runs || [];

    if (runs && runs.length > 0) {
      const latestRun = runs[0];
      console.log("Found latest run:", latestRun);

      // 获取详细的回测结果
      const runDetail = await getStrategyRunDetail(latestRun.run_id, latestRun.experiment_id);
      console.log("Run detail fetched:", runDetail);

      latestBacktestData.value = runDetail;
    } else {
      console.log("No backtest runs found for portfolio ID:", currentPortfolio.value.id);
      latestBacktestData.value = null;
    }
  } catch (error) {
    console.error("Error fetching latest backtest data:", error);
    ElMessage.error("获取回测数据失败");
    latestBacktestData.value = null;
  } finally {
    backtestLoading.value = false;
  }
};



// 返回列表
const goBack = () => {
  router.push("/portfolio");
};

// 显示编辑对话框
const showEditDialog = () => {
  if (!currentPortfolio.value) {
    ElMessage.warning("投资组合数据不可用，无法编辑");
    return;
  }

  editingPortfolio.value = currentPortfolio.value;
  dialogVisible.value = true;
};

// 处理更新
const handleUpdate = async (portfolio: PortfolioCreateUpdate) => {
  if (!currentPortfolio.value) {
    ElMessage.error("更新失败：投资组合数据不可用");
    return;
  }

  saveLoading.value = true;
  try {
    // 直接调用 API 更新投资组合
    const updatedPortfolio = await apiUpdatePortfolio(
      currentPortfolio.value.id,
      portfolio
    );
    currentPortfolio.value = updatedPortfolio;
    dialogVisible.value = false;

    // 重新获取最新回测数据
    await fetchLatestBacktestData();
    ElMessage.success("投资组合更新成功");
  } catch (error) {
    console.error("Error updating portfolio:", error);
    ElMessage.error("更新投资组合失败");
  } finally {
    saveLoading.value = false;
  }
};

// 处理对话框关闭
const handleDialogClosed = () => {
  editingPortfolio.value = null;
};


</script>

<style lang="scss">
.portfolio-container {
  padding: 20px;
  height: 100%;
  overflow: hidden;

  .portfolio-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        display: flex;
        align-items: center;

        h3 {
          margin-right: 10px;
        }
      }
    }

    .el-card__body {
      flex: 1 1 0;
      overflow: auto;
      position: relative;
    }

    .card-content {
      padding: 20px 0;
    }

    .portfolio-info {
      margin-bottom: 30px;
    }

    .portfolio-stocks {
      h4 {
        margin: 0 0 15px;
        font-weight: 500;
      }
    }

    .loading-container {
      padding: 20px;
    }

    .backtest-results {
      .analysis-report {
        margin-top: 20px;
      }
    }

    .empty-state {
      padding: 40px;
      text-align: center;
    }
  }
}
</style>
