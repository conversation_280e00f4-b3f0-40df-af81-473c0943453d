<template>
  <div class="asset-dashboard-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon class="title-icon"><TrendCharts /></el-icon>
            市场资产相关性分析
          </h1>
          <p class="page-subtitle">分析不同资产类别之间的相关性，洞察市场联动关系</p>
        </div>
      </div>
    </div>

    <!-- 参数配置卡片 -->
    <el-card class="config-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><Setting /></el-icon>
          <span class="header-title">分析参数配置</span>
        </div>
      </template>

      <el-form :model="selectedAssets" label-position="top" class="config-form">
        <!-- 资产选择器 -->
        <div class="asset-selector-grid">
          <div class="asset-group">
            <div class="asset-group-header">
              <el-icon class="group-icon" style="color: #67c23a;"><Money /></el-icon>
              <span class="group-title">外汇货币</span>
            </div>
            <el-select
              v-model="selectedAssets.forex"
              placeholder="选择货币对"
              class="asset-select"
              clearable
            >
              <el-option label="欧元/美元 (EUR/USD)" value="EURUSD">
                <span class="option-label">EUR/USD</span>
                <span class="option-desc">欧元/美元</span>
              </el-option>
              <el-option label="英镑/美元 (GBP/USD)" value="GBPUSD">
                <span class="option-label">GBP/USD</span>
                <span class="option-desc">英镑/美元</span>
              </el-option>
              <el-option label="美元/日元 (USD/JPY)" value="USDJPY">
                <span class="option-label">USD/JPY</span>
                <span class="option-desc">美元/日元</span>
              </el-option>
            </el-select>
          </div>

          <div class="asset-group">
            <div class="asset-group-header">
              <el-icon class="group-icon" style="color: #409eff;"><Document /></el-icon>
              <span class="group-title">债券市场</span>
            </div>
            <el-select
              v-model="selectedAssets.bonds"
              placeholder="选择债券"
              class="asset-select"
              clearable
            >
              <el-option label="10年期美国国债" value="10Y">
                <span class="option-label">10Y Treasury</span>
                <span class="option-desc">10年期美国国债</span>
              </el-option>
              <el-option label="2年期美国国债" value="2Y">
                <span class="option-label">2Y Treasury</span>
                <span class="option-desc">2年期美国国债</span>
              </el-option>
              <el-option label="30年期美国国债" value="30Y">
                <span class="option-label">30Y Treasury</span>
                <span class="option-desc">30年期美国国债</span>
              </el-option>
            </el-select>
          </div>

          <div class="asset-group">
            <div class="asset-group-header">
              <el-icon class="group-icon" style="color: #e6a23c;"><TrendCharts /></el-icon>
              <span class="group-title">股票指数</span>
            </div>
            <el-select
              v-model="selectedAssets.equities"
              placeholder="选择股权指数"
              class="asset-select"
              clearable
            >
              <el-option label="标普500指数" value="^GSPC">
                <span class="option-label">S&P 500</span>
                <span class="option-desc">标普500指数</span>
              </el-option>
              <el-option label="道琼斯指数" value="^DJI">
                <span class="option-label">Dow Jones</span>
                <span class="option-desc">道琼斯指数</span>
              </el-option>
              <el-option label="纳斯达克指数" value="^IXIC">
                <span class="option-label">NASDAQ</span>
                <span class="option-desc">纳斯达克指数</span>
              </el-option>
            </el-select>
          </div>

          <div class="asset-group">
            <div class="asset-group-header">
              <el-icon class="group-icon" style="color: #f56c6c;"><Coin /></el-icon>
              <span class="group-title">大宗商品</span>
            </div>
            <el-select
              v-model="selectedAssets.commodities"
              placeholder="选择商品"
              class="asset-select"
              clearable
            >
              <el-option label="黄金期货" value="GC=F">
                <span class="option-label">Gold</span>
                <span class="option-desc">黄金期货</span>
              </el-option>
              <el-option label="原油期货" value="CL=F">
                <span class="option-label">Crude Oil</span>
                <span class="option-desc">原油期货</span>
              </el-option>
              <el-option label="白银期货" value="SI=F">
                <span class="option-label">Silver</span>
                <span class="option-desc">白银期货</span>
              </el-option>
            </el-select>
          </div>
        </div>

        <!-- 时间范围和分析按钮 -->
        <div class="analysis-controls">
          <div class="time-range-section">
            <div class="control-group">
              <label class="control-label">
                <el-icon><Calendar /></el-icon>
                分析时间范围
              </label>
              <el-select
                v-model="timeRange"
                placeholder="选择时间范围"
                class="time-select"
              >
                <el-option label="最近30天" :value="30" />
                <el-option label="最近90天" :value="90" />
                <el-option label="最近6个月" :value="180" />
                <el-option label="最近1年" :value="365" />
                <el-option label="最近2年" :value="730" />
              </el-select>
            </div>
          </div>

          <div class="action-section">
            <el-button
              type="primary"
              size="large"
              @click="handleAnalyzeAssets"
              :loading="analyzing"
              class="analyze-button"
              :disabled="!canAnalyze"
            >
              <el-icon v-if="!analyzing"><DataAnalysis /></el-icon>
              {{ analyzing ? '正在分析数据...' : '开始相关性分析' }}
            </el-button>
          </div>
        </div>
      </el-form>
    </el-card>

    <!-- 分析结果显示 -->
    <div v-if="analysisResult" class="analysis-results">
      <el-card class="result-card" shadow="hover">
        <template #header>
          <div class="result-header">
            <div class="header-left">
              <el-icon class="result-icon"><DataAnalysis /></el-icon>
              <div class="header-text">
                <h3 class="result-title">相关性分析结果</h3>
                <p class="result-subtitle">
                  数据点数量：{{ analysisResult.data_points }} |
                  时间范围：{{ analysisResult.time_range }}天
                </p>
              </div>
            </div>
            <div class="header-right">
              <el-button size="small" @click="exportResults">
                <el-icon><Download /></el-icon>
                导出结果
              </el-button>
            </div>
          </div>
        </template>

        <!-- 相关性矩阵表格 -->
        <div class="table-section">
          <h4 class="section-title">
            <el-icon><Grid /></el-icon>
            相关性矩阵
          </h4>
          <el-table
            :data="correlationTableData"
            border
            class="correlation-table"
            :header-cell-style="{ background: '#f8f9fa', fontWeight: 'bold' }"
          >
            <el-table-column prop="asset" label="资产" width="180" fixed>
              <template #default="scope">
                <div class="asset-cell">
                  <el-icon class="asset-icon">
                    <component :is="getAssetIcon(scope.row.asset)" />
                  </el-icon>
                  <strong>{{ scope.row.assetName }}</strong>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-for="asset in analysisResult.assets"
              :key="asset"
              :label="getAssetDisplayName(asset)"
              align="center"
              width="120"
            >
              <template #default="scope">
                <div
                  :class="getCorrelationClass(scope.row.correlations[asset])"
                  class="correlation-cell"
                  :title="`相关系数: ${scope.row.correlations[asset].toFixed(4)}`"
                >
                  <span class="correlation-value">
                    {{ scope.row.correlations[asset].toFixed(3) }}
                  </span>
                  <div class="correlation-bar" :style="getCorrelationBarStyle(scope.row.correlations[asset])"></div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 说明文字 -->
        <div class="correlation-legend">
          <div class="legend-title">相关性强度说明：</div>
          <div class="legend-items">
            <el-tag class="legend-tag strong-positive" size="small">
              <el-icon><TrendCharts /></el-icon>
              强正相关 (>0.5)
            </el-tag>
            <el-tag class="legend-tag strong-negative" size="small">
              <el-icon><Bottom /></el-icon>
              强负相关 (<-0.5)
            </el-tag>
            <el-tag class="legend-tag self-correlation" size="small">
              <el-icon><Aim /></el-icon>
              自相关 (1.0)
            </el-tag>
            <el-tag class="legend-tag weak-correlation" size="small">
              <el-icon><Minus /></el-icon>
              弱相关 (-0.5~0.5)
            </el-tag>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  TrendCharts,
  Setting,
  Money,
  Document,
  Coin,
  Calendar,
  DataAnalysis,
  Download,
  Grid,
  Bottom,
  Aim,
  Minus
} from '@element-plus/icons-vue'

// 响应式数据
const selectedAssets = ref({
  forex: 'EURUSD',
  bonds: '10Y',
  equities: '^GSPC',
  commodities: 'GC=F'
})

const timeRange = ref(365)
const analyzing = ref(false)
const analysisStatus = ref('准备分析')
const analysisResult = ref<any>(null)

// 资产显示名称映射
const assetDisplayNames: Record<string, string> = {
  'EURUSD': '欧元/美元',
  'GBPUSD': '英镑/美元',
  'USDJPY': '美元/日元',
  '10Y': '10年期美国国债',
  '2Y': '2年期美国国债',
  '30Y': '30年期美国国债',
  '^GSPC': 'S&P 500',
  '^DJI': '道琼斯指数',
  '^IXIC': '纳斯达克指数',
  'GC=F': '黄金期货',
  'CL=F': '原油期货',
  'SI=F': '白银期货'
}

// 计算属性
const correlationTableData = computed(() => {
  if (!analysisResult.value) return []

  return analysisResult.value.assets.map((asset: string) => ({
    asset,
    assetName: getAssetDisplayName(asset),
    correlations: analysisResult.value.correlation_matrix[asset]
  }))
})

const canAnalyze = computed(() => {
  return selectedAssets.value.forex &&
         selectedAssets.value.bonds &&
         selectedAssets.value.equities &&
         selectedAssets.value.commodities &&
         timeRange.value
})

// 方法
const getAssetDisplayName = (asset: string): string => {
  return assetDisplayNames[asset] || asset
}

const getAssetIcon = (asset: string) => {
  const iconMap: Record<string, any> = {
    'EURUSD': Money,
    'GBPUSD': Money,
    'USDJPY': Money,
    '10Y': Document,
    '2Y': Document,
    '30Y': Document,
    '^GSPC': TrendCharts,
    '^DJI': TrendCharts,
    '^IXIC': TrendCharts,
    'GC=F': Coin,
    'CL=F': Coin,
    'SI=F': Coin
  }
  return iconMap[asset] || DataAnalysis
}

const getCorrelationClass = (correlation: number) => {
  if (Math.abs(correlation - 1) < 0.001) return 'correlation-self'
  if (correlation > 0.5) return 'correlation-strong-positive'
  if (correlation < -0.5) return 'correlation-strong-negative'
  return 'correlation-weak'
}

const getCorrelationBarStyle = (correlation: number) => {
  const absCorr = Math.abs(correlation)
  const width = absCorr * 100
  const color = correlation > 0.5 ? '#67c23a' :
                correlation < -0.5 ? '#f56c6c' :
                Math.abs(correlation - 1) < 0.001 ? '#409eff' : '#e4e7ed'

  return {
    width: `${width}%`,
    backgroundColor: color,
    opacity: 0.3
  }
}

const exportResults = () => {
  if (!analysisResult.value) return

  const data = {
    analysis_date: new Date().toISOString(),
    time_range: analysisResult.value.time_range,
    data_points: analysisResult.value.data_points,
    assets: analysisResult.value.assets.map((asset: string) => ({
      symbol: asset,
      name: getAssetDisplayName(asset)
    })),
    correlation_matrix: analysisResult.value.correlation_matrix
  }

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `asset_correlation_analysis_${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success('分析结果已导出')
}

const handleAnalyzeAssets = async () => {
  analyzing.value = true
  analysisStatus.value = '🔄 正在获取真实FMP数据...'
  analysisResult.value = null

  try {
    const params = new URLSearchParams({
      days: timeRange.value.toString(),
      forex: selectedAssets.value.forex,
      commodity: selectedAssets.value.commodities,
      equity: selectedAssets.value.equities,
      bond: selectedAssets.value.bonds
    })

    const response = await fetch(`/gbs-api/v1/analyze-assets-simple?${params}`)
    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.detail || '分析失败')
    }

    // 转换相关性矩阵的键
    const assetTypeToSymbol: Record<string, string> = {
      'forex': selectedAssets.value.forex,
      'commodities': selectedAssets.value.commodities,
      'equities': selectedAssets.value.equities,
      'bonds': selectedAssets.value.bonds
    }

    const convertedMatrix: Record<string, Record<string, number>> = {}
    Object.keys(data.correlation_matrix).forEach(type1 => {
      const symbol1 = assetTypeToSymbol[type1]
      convertedMatrix[symbol1] = {}
      Object.keys(data.correlation_matrix[type1]).forEach(type2 => {
        const symbol2 = assetTypeToSymbol[type2]
        convertedMatrix[symbol1][symbol2] = data.correlation_matrix[type1][type2]
      })
    })

    analysisResult.value = {
      correlation_matrix: convertedMatrix,
      data_points: data.data_points,
      time_range: data.time_range,
      assets: Object.values(assetTypeToSymbol)
    }

    analysisStatus.value = `✅ 真实FMP数据分析完成！数据点: ${data.data_points}`
    ElMessage.success('资产相关性分析完成')
  } catch (error: any) {
    console.error('资产分析失败:', error)
    analysisStatus.value = `❌ 分析失败: ${error.message}`
    ElMessage.error(`分析失败: ${error.message}`)
  } finally {
    analyzing.value = false
  }
}

onMounted(() => {
  console.log('Asset Dashboard mounted')
})
</script>

<style lang="scss" scoped>
.asset-dashboard-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;

  // 页面标题
  .page-header {
    margin-bottom: 32px;

    .header-content {
      .title-section {
        text-align: center;

        .page-title {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          margin: 0 0 12px 0;
          font-size: 32px;
          font-weight: 600;
          color: #2c3e50;

          .title-icon {
            font-size: 36px;
            color: #409eff;
          }
        }

        .page-subtitle {
          margin: 0;
          font-size: 16px;
          color: #7f8c8d;
          font-weight: 400;
        }
      }
    }
  }

  // 配置卡片
  .config-card {
    margin-bottom: 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    :deep(.el-card__header) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 16px 16px 0 0;

      .card-header {
        display: flex;
        align-items: center;
        gap: 12px;

        .header-icon {
          font-size: 20px;
        }

        .header-title {
          font-size: 18px;
          font-weight: 600;
        }
      }
    }

    .config-form {
      .asset-selector-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 24px;
        margin-bottom: 32px;

        .asset-group {
          .asset-group-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;

            .group-icon {
              font-size: 18px;
            }

            .group-title {
              font-size: 16px;
              font-weight: 600;
              color: #2c3e50;
            }
          }
        }
      }

      .analysis-controls {
        display: flex;
        align-items: end;
        justify-content: space-between;
        gap: 24px;
        padding: 24px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;

        .time-range-section {
          flex: 1;

          .control-group {
            .control-label {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;
              font-size: 14px;
              font-weight: 600;
              color: #495057;
            }

            .time-select {
              width: 200px;

              :deep(.el-select__wrapper) {
                border-radius: 8px;
              }
            }
          }
        }

        .action-section {
          .analyze-button {
            padding: 12px 32px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;

            &:hover:not(:disabled) {
              transform: translateY(-2px);
              box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }

  // 结果卡片
  .analysis-results {
    .result-card {
      border-radius: 16px;
      border: none;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

      :deep(.el-card__header) {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 16px 16px 0 0;

        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .header-left {
            display: flex;
            align-items: center;
            gap: 16px;

            .result-icon {
              font-size: 24px;
            }

            .header-text {
              .result-title {
                margin: 0 0 4px 0;
                font-size: 20px;
                font-weight: 600;
              }

              .result-subtitle {
                margin: 0;
                font-size: 14px;
                opacity: 0.9;
              }
            }
          }

          .header-right {
            .el-button {
              background: rgba(255, 255, 255, 0.2);
              border: 1px solid rgba(255, 255, 255, 0.3);
              color: white;

              &:hover {
                background: rgba(255, 255, 255, 0.3);
              }
            }
          }
        }
      }

      .table-section {
        margin-bottom: 32px;

        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin: 0 0 16px 0;
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;

          .el-icon {
            color: #667eea;
          }
        }
      }

      .correlation-table {
        border-radius: 8px;
        overflow: hidden;

        .asset-cell {
          display: flex;
          align-items: center;
          gap: 8px;

          .asset-icon {
            font-size: 16px;
            color: #667eea;
          }
        }

        .correlation-cell {
          position: relative;
          padding: 8px;
          text-align: center;
          cursor: pointer;
          transition: all 0.2s ease;

          .correlation-value {
            position: relative;
            z-index: 2;
            font-weight: 600;
            font-size: 13px;
          }

          .correlation-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            border-radius: 2px;
          }

          &.correlation-self {
            background: linear-gradient(135deg, #409eff, #66b3ff);
            color: white;

            .correlation-value {
              color: white;
            }
          }

          &.correlation-strong-positive {
            background: linear-gradient(135deg, #67c23a, #85ce61);
            color: white;

            .correlation-value {
              color: white;
            }
          }

          &.correlation-strong-negative {
            background: linear-gradient(135deg, #f56c6c, #f78989);
            color: white;

            .correlation-value {
              color: white;
            }
          }

          &.correlation-weak {
            background: #f8f9fa;
            color: #495057;
          }
        }
      }

      .correlation-legend {
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;

        .legend-title {
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 16px;
        }

        .legend-items {
          display: flex;
          justify-content: flex-start;
          gap: 16px;
          flex-wrap: wrap;

          .legend-tag {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 500;

            &.strong-positive {
              background: #67c23a;
              color: white;
              border: none;
            }

            &.strong-negative {
              background: #f56c6c;
              color: white;
              border: none;
            }

            &.self-correlation {
              background: #409eff;
              color: white;
              border: none;
            }

            &.weak-correlation {
              background: #e4e7ed;
              color: #606266;
              border: none;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .asset-dashboard-container {
    padding: 16px;

    .page-header {
      .title-section {
        .page-title {
          font-size: 24px;
          flex-direction: column;
          gap: 8px;
        }
      }
    }

    .config-card {
      .config-form {
        .asset-selector-grid {
          grid-template-columns: 1fr;
          gap: 16px;
        }

        .analysis-controls {
          flex-direction: column;
          align-items: stretch;
          gap: 16px;

          .time-range-section {
            .control-group {
              .time-select {
                width: 100%;
              }
            }
          }
        }
      }
    }

    .analysis-results {
      .result-card {
        :deep(.el-card__header) {
          .result-header {
            flex-direction: column;
            gap: 16px;
            align-items: flex-start;
          }
        }



        .correlation-table {
          font-size: 12px;

          .correlation-cell {
            padding: 4px;

            .correlation-value {
              font-size: 11px;
            }
          }
        }

        .correlation-legend {
          .legend-items {
            gap: 8px;

            .legend-tag {
              padding: 6px 12px;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
</style>