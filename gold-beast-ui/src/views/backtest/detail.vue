<template>
  <div class="backtest-detail-container">
    <el-card class="backtest-detail-card">
      <template #header>
        <div class="card-header">
          <h3>回测详情</h3>
          <div class="header-buttons">
            <el-button @click="goBack" type="primary">返回列表</el-button>
          </div>
        </div>
      </template>
      <div v-if="isLoading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>
      <div v-else-if="error" class="error-container">
        <el-alert :title="error" type="error" :closable="false" show-icon />
      </div>
      <div v-else class="backtest-detail-content">
        <el-descriptions title="运行信息" :column="3" border>
          <el-descriptions-item label="策略名称">{{
            displayName || runDetail?.experiment_name
          }}</el-descriptions-item>
          <el-descriptions-item label="运行ID">{{
            runDetail?.run_id
          }}</el-descriptions-item>
          <el-descriptions-item label="实验ID">{{
            runDetail?.experiment_id
          }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ runDetail?.start_time }}
            <el-tag
              v-if="runDetail?.start_time"
              style="margin-left: 12px"
              type="primary"
              ><TimeConsuming v-model="runDetail.start_time"
            /></el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">{{
            runDetail?.end_time
          }}</el-descriptions-item>
          <el-descriptions-item label="运行时长">{{
            formatDuration(runDetail?.duration || 0)
          }}</el-descriptions-item>
        </el-descriptions>

        <AnalysisReport
          :analysis_results="runDetail?.analysis_results || []"
          :model-type="runDetail?.params?.model_type || 'MH'"
          :factors-dir="runDetail?.params?.factors_dir || 'factors'"
          class="analysis-report"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getStrategyRunDetail, getStrategyHistory } from "@/api/strategy";
import type { StrategyRunDetail, StrategyRunInfo } from "@/api/strategy";
import AnalysisReport from "@/components/AnalysisReport.vue";
import TimeConsuming from "@/components/TimeConsuming.vue";

const router = useRouter();
const route = useRoute();
const runId = ref(route.params.run_id as string);
const experimentId = ref(route.params.experiment_id as string);
const runDetail = ref<StrategyRunDetail | null>(null);
const isLoading = ref(true);
const error = ref<string | null>(null);

// 实验名称到显示名称的映射
const experimentToDisplayMap: Record<string, string> = {
  Qlib_Style_Workflow: "AIPM 策略",
  Backtest_Only_Workflow: "回测策略",
  // 添加更多映射...
};

// 获取显示名称
const displayName = computed(() => {
  if (!runDetail.value) return "";
  return (
    experimentToDisplayMap[runDetail.value.experiment_name] ||
    runDetail.value.experiment_name
  );
});

// 格式化持续时间
const formatDuration = (duration: number): string => {
  const seconds = Math.floor(duration % 60);
  const minutes = Math.floor((duration / 60) % 60);
  const hours = Math.floor(duration / 3600);

  const parts = [];
  if (hours > 0) parts.push(`${hours}小时`);
  if (minutes > 0) parts.push(`${minutes}分钟`);
  if (seconds > 0 || parts.length === 0) parts.push(`${seconds}秒`);

  return parts.join(" ");
};

// 返回列表页
const goBack = () => {
  router.push({ name: "BacktestSystem" });
};

// 获取运行详情
const fetchRunDetail = async () => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!runId.value || !experimentId.value) {
      throw new Error("运行ID或实验ID不能为空");
    }

    const detail = await getStrategyRunDetail(runId.value, experimentId.value);
    runDetail.value = detail;
  } catch (err) {
    console.error("Failed to fetch run detail:", err);
    error.value = "获取运行详情失败，请稍后重试";
  } finally {
    isLoading.value = false;
  }
};

// 页面加载时获取运行详情
onMounted(() => {
  fetchRunDetail();
});
</script>

<style lang="scss">
.backtest-detail-container {
  padding: 20px;

  .backtest-detail-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .loading-container,
    .error-container {
      padding: 20px;
    }

    .backtest-detail-content {
      .analysis-report {
        margin-top: 20px;
      }
      .run-detail-tabs {
        margin-top: 20px;

        .analysis-results-container,
        .params-container,
        .tags-container,
        .metrics-container {
          margin-top: 15px;
        }
      }
    }
  }
}
</style>
