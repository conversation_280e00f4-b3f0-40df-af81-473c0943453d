import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "path";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ["vue", "vue-router", "pinia"],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
  ],

  css: {
    preprocessorOptions: {
      scss: {
        api: "modern", // or "modern"
      },
    },
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
  server: {
    port: 3002,
    host: "0.0.0.0",
    open: true,
    proxy: {
      "/gbs-api/v1": {
        target: "http://localhost:8000",
        changeOrigin: true,
      },
      "/api/companies": {
        target: "http://localhost:8000",
        changeOrigin: true,
      },
      "/api/factor-model/v1": {
        target: "http://localhost:8001",
        changeOrigin: true,
      },
    },
  },
});
