FROM python:3.10.2-alpine3.15
LABEL name="pycommon" author="samuelchen.net"
ARG WORKSPACE=/opt/samuelchen.net/lab-pycommon

# api source
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories 
#&& apk update && apk add python3

# pip source
COPY _deploy/pip.conf /root/.pip/pip.conf

# change time zone
COPY _deploy/asia-shanghai.alpine-tzinfo /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone

# copy dependents
COPY * $WORKSPACE
ENV PYTHONPATH=$WORKSPACE:$PYTHONPATH

# python packages
RUN pip install -r $WORKSPACE/requirements.txt
