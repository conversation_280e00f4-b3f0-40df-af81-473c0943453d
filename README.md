# Gold Beast System

**PROPRIETARY AND CONFIDENTIAL**

© 2025 Onwish, Inc. All Rights Reserved.

A quantitative trading system framework designed for algorithmic trading strategy development, backtesting, and execution.

## Overview

Gold Beast System is a modular and extensible framework for developing, testing, and deploying quantitative trading strategies. The system provides components for data collection, feature engineering, model training, strategy implementation, risk management, execution, and backtesting.

## Project Structure

```
gold-beast-system/                      # Project root directory
├── api/                                # API server
│   ├── routers/                        # API route handlers
│   │   ├── axioma_router.py            # Axioma attribution API
│   │   ├── backtrader_router.py        # Backtrader API
│   │   ├── brinson_router.py           # Brinson attribution API
│   │   └── quantstats_router.py        # QuantStats API
│   ├── utils/                          # API utilities
│   │   └── visualization.py            # Visualization utilities
│   ├── main.py                         # FastAPI application
│   └── run_server.py                   # Server entry point
├── gbs/                                # Main package
│   ├── backtest_system/                # Backtesting system
│   │   ├── conf/                       # Configuration files
│   │   ├── data/                       # Backtesting data
│   │   ├── engine/                     # Backtesting engines
│   │   │   └── backtrader_engine.py    # Backtrader engine implementation
│   │   ├── examples/                   # Backtest examples
│   │   ├── scripts/                    # Backtesting scripts
│   │   └── signal/                     # Signal generators
│   ├── core/                           # Core tools and configuration
│   │   ├── conf/                       # Configuration files
│   │   ├── monitoring/                 # Monitoring tools
│   │   ├── scripts/                    # Utility scripts
│   │   └── utils/                      # Utility functions
│   │       ├── report_utils.py         # Report generation utilities
│   │       └── viz_utils.py            # Visualization utilities
│   ├── data_system/                    # Data system
│   │   ├── base/                       # Base classes
│   │   ├── dataset/                    # Dataset classes
│   │   │   └── portfolio_dataset.py    # Portfolio dataset implementation
│   │   ├── filters/                    # Data filters
│   │   ├── handlers/                   # Data handlers
│   │   │   └── portfolio_handler.py    # Portfolio data handler
│   │   ├── loaders/                    # Data loaders
│   │   └── processors/                 # Data processors
│   │       └── portfolio_processor.py  # Portfolio data processor
│   ├── eval_system/                    # Evaluation system
│   │   ├── analysis/                   # Analysis tools
│   │   │   └── analyzer/               # Analyzers
│   │   │       ├── base_analyzer.py    # Base analyzer class
│   │   │       ├── brinson_analyzer.py # Brinson attribution analyzer
│   │   │       └── portfolio_analysis_manager.py # Analysis manager
│   │   ├── monitoring/                 # Monitoring tools
│   │   ├── outputs/                    # Analysis outputs
│   │   └── storage/                    # Storage utilities
│   ├── model_system/                   # Model system
│   │   ├── base/                       # Base classes
│   │   ├── conf/                       # Configuration files
│   │   ├── outputs/                    # Model outputs
│   │   ├── utils/                      # Model utilities
│   │   └── zoo/                        # Model implementations
│   │       └── portfolio_transformer.py # Portfolio transformer model
│   ├── trading_system/                 # Trading system
│   │   ├── execution/                  # Order execution
│   │   ├── risk/                       # Risk management
│   │   └── strategies/                 # Trading strategies
│   │       └── bt_portfolio_strategy.py # Backtrader portfolio strategy
│   └── workflow/                       # Workflow system
│       ├── task/                       # Task definitions
│       └── mlruns/                     # MLflow experiment metadata
├── examples/                           # Example scripts
│   ├── aipm/                           # AI Portfolio Management examples
│   ├── stock_cnn/                      # Stock CNN examples
│   └── workflow_demo/                  # Workflow examples
│       ├── config.yaml                 # Configuration file
│       ├── run_experiment.py           # Experiment runner
│       └── run_experiment_with_workflow.py # Workflow-based experiment runner
├── setup.py                            # Installation script
├── requirements.txt                    # Project dependencies
└── README.md                           # This file
```

## Key Components

1. **Strategies**: Define trading logic through the `BaseStrategy` abstract class. Implement the `on_bar()` method to create trading signals based on market data and model predictions. The system includes:
   - `BaseStrategy`: Abstract base class defining the strategy interface
   - `PortfolioStrategy`: Implementation for portfolio optimization models
   - `DailyStrategy` and `WeeklyStrategy`: Frequency-specific implementations

2. **Models**: Machine learning models for prediction, with implementations for:
   - LightGBM: Tree-based models for classification and regression
   - PyTorch: Deep learning models with neural networks
   - Ensemble: Combining multiple models for improved performance
   - Transformer: Advanced models for portfolio optimization

3. **Data System**: Components for collecting and processing market data from various sources, including:
   - Data Collectors: Retrieve data from APIs, databases, and files
   - Data Processors: Clean, transform, and normalize data
   - Feature Engineering: Extract features from raw data
   - Datasets: Store and manage data

4. **Features**: Transform raw market data into features suitable for model training and prediction, with:
   - Technical indicators (moving averages, RSI, MACD, etc.)
   - Fundamental data processing
   - Feature normalization and scaling

5. **Risk Management**: Filter trading signals based on risk parameters to manage exposure, including:
   - Position size limits
   - Stop-loss mechanisms
   - Exposure controls

6. **Execution**: Interface with brokers to execute orders in the market.

7. **Backtesting**: Test strategies on historical data to evaluate performance, with:
   - Portfolio performance metrics (returns, volatility, Sharpe ratio, drawdowns)
   - Visualization tools for equity curves and performance metrics
   - Support for different trading frequencies
   - Transaction cost modeling
   - Backtrader integration for advanced backtesting capabilities

8. **Analysis**: Analyze portfolio performance and attribution, with:
   - Portfolio performance metrics calculation
   - Brinson attribution analysis for sector-based attribution
   - Axioma multi-factor attribution analysis for factor-based attribution
   - QuantStats integration for advanced portfolio analytics
   - Unified analysis interface for managing multiple analyzers

9. **API Server**: FastAPI-based server providing endpoints for analysis and backtesting:
   - JSON API endpoints for all analysis components
   - SVG chart generation for visualization
   - Modular router architecture for extensibility
   - Utility functions for common operations

10. **Frontend Application**: Vue.js-based application for visualizing analysis results:
    - Component-based architecture for reusability
    - Dynamic report rendering based on JSON data
    - Interactive charts and tables
    - Form-based parameter input for analysis configuration

11. **Experiment Tracking**: MLflow integration for tracking model training runs, parameters, metrics, and artifacts.

## Getting Started

### Prerequisites

- Python 3.8+
- Virtual environment (recommended and must be activated before running Python scripts)

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd gold-beast-system
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Install the package in development mode:
   ```bash
   pip install -e .
   ```

5. Create necessary output directories (these are gitignored):
   ```bash
   mkdir -p gbs/model_system/outputs/experiments
   mkdir -p gbs/model_system/outputs/artifacts
   mkdir -p gbs/backtest_system/outputs
   mkdir -p gbs/eval_system/outputs
   mkdir -p logs
   ```

### Starting the API Server

The system includes a FastAPI server that provides API endpoints for portfolio analysis, attribution analysis, and backtesting. To start the server:

```bash
python api/run_server.py
```

This will start the server at http://localhost:8000. You can access the API documentation at http://localhost:8000/docs.

### API Endpoints

The API provides the following endpoints:

- **Brinson Attribution Analysis**:
  - `/api/brinson/report-json`: Generate Brinson attribution analysis report in JSON format

- **Axioma Attribution Analysis**:
  - `/api/axioma/report-json`: Generate Axioma multi-factor attribution analysis report in JSON format

- **Backtrader Backtesting**:
  - `/api/backtrader/report-json`: Generate backtest results report in JSON format

- **QuantStats Analysis**:
  - `/api/quantstats/report-json`: Generate QuantStats portfolio analysis report in JSON format

### Starting the Frontend Application

The system includes a Vue.js-based frontend application for visualizing analysis results. To start the frontend:

```bash
cd gold-beast-ui
npm install  # Only needed the first time
npm run dev
```

This will start the frontend application at http://localhost:5173. The frontend will automatically connect to the API server running at http://localhost:8000.

### Usage

#### Running Experiments

Use the workflow demo example to train and evaluate models:

```bash
# Activate the virtual environment
source venv/bin/activate

# Run an experiment with a specific configuration
python examples/workflow_demo/run_experiment_with_workflow.py --config examples/workflow_demo/config.yaml
```

This will:
1. Load data according to the configuration
2. Prepare features using the portfolio processor
3. Train the Portfolio Transformer model with the specified parameters
4. Save the model and metrics

#### Using Small Datasets for Testing

For faster testing and development, you can use a small dataset:

```bash
python examples/workflow_demo/run_experiment_with_workflow.py --config examples/workflow_demo/config.yaml --small_dataset
```

You can also limit the number of stocks:

```bash
python examples/workflow_demo/run_experiment_with_workflow.py --config examples/workflow_demo/config.yaml --limit_stocks 50
```

#### Training with All Features

By default, the system uses feature selection. To use all features:

```bash
# Modify the config.yaml file to disable feature selection
# Or use the command line option (if implemented)
python examples/workflow_demo/run_experiment_with_workflow.py --config examples/workflow_demo/config.yaml --use_all_features
```

#### Viewing Experiment Results

Start the MLflow UI server to view experiment results:

```bash
# Start MLflow UI with the tracking URI pointing to the mlruns directory
mlflow ui --backend-store-uri file:///path/to/gold-beast-system/model_system/outputs/mlruns
```

Or use the provided script:

```bash
python core/scripts/start_mlflow_ui.py
```

Then open http://localhost:5000 in your browser to view:
- Experiment runs and metrics
- Parameter comparisons
- Artifact visualizations
- Registered models
- Performance charts and plots

#### Implementing Trading Strategies

1. Implement your trading strategy by extending the `BaseStrategy` class.
2. Train a model using one of the provided model classes or implement your own.
3. Configure risk parameters in the `RiskManager`.
4. Backtest your strategy using the `BacktestEngine`.
5. For live trading, set up the `ExecutionEngine` with your broker interface.

Example:
```python
from trading_system.strategies.base_strategy import BaseStrategy
from trading_system.strategies.portfolio_strategy import PortfolioStrategy
from model_system.models.base.lightgbm_model import LightGBMModel
from trading_system.risk.risk_manager import RiskManager
from evaluation_system.backtesting.backtest_engine import BacktestEngine
from data_system.collectors.data_collector import DataCollector
from core.utils.backtest_utils import prepare_historical_data

# Create components
model = LightGBMModel(params={'objective': 'regression'})
risk_manager = RiskManager(max_position=1000, stop_loss=0.05)

# Collect data
data_collector = DataCollector(source='yahoo', symbols=['AAPL', 'MSFT', 'GOOG'])
historical_data = data_collector.collect_historical('2020-01-01', '2023-01-01')

# Create strategy
strategy = PortfolioStrategy(
    model=model,
    risk_manager=risk_manager,
    universe=['AAPL', 'MSFT', 'GOOG'],
    rebalance_threshold=0.05,  # Trigger rebalance when weights change by 5%
    min_position_size=10,      # Minimum number of shares to trade
    frequency="monthly",       # Monthly rebalancing
    initial_capital=1_000_000
)

# Prepare data for backtesting
backtest_data = prepare_historical_data(historical_data)

# Backtest
backtest_engine = BacktestEngine(initial_capital=1_000_000, commission=0.0003)
backtest_result = backtest_engine.run(strategy, backtest_data)

# Evaluate performance
metrics = backtest_engine.evaluate_performance(
    backtest_result,
    risk_free_rate=0.0,
    periods_per_year=12
)

# Visualize results
from evaluation_system.backtesting.visualization import plot_results
results = {
    'returns': backtest_result.returns,
    'dates': backtest_result.equity_curve.index,
    'weights': backtest_result.weights_history,
    'metrics': metrics,
    'equity_curve': backtest_result.equity_curve
}
plot_results(results)
```

## Extending the System

### Adding a New Strategy

Create a new file in the `trading_system/strategies` directory that extends `BaseStrategy`:

```python
from trading_system.strategies.base_strategy import BaseStrategy, Order
import pandas as pd
from typing import List

class MyCustomStrategy(BaseStrategy):
    def on_bar(self, market_data: pd.DataFrame) -> List[Order]:
        # Implement your trading logic here
        signals = []

        for sym in self.universe:
            # Get prediction from model
            pred = self.model.predict(market_data[market_data['symbol'] == sym])

            # Generate buy/sell signals based on prediction
            if pred > 0.6:  # Strong buy signal
                signals.append(Order(
                    symbol=sym,
                    action="BUY",
                    quantity=100,
                    price=market_data[market_data['symbol'] == sym]['close'].iloc[0]
                ))
            elif pred < 0.4:  # Strong sell signal
                signals.append(Order(
                    symbol=sym,
                    action="SELL",
                    quantity=100,
                    price=market_data[market_data['symbol'] == sym]['close'].iloc[0]
                ))

        return signals
```

### Using the DataCollector with Different Data Sources

```python
from data_system.collectors.data_collector import DataCollector

# Using Yahoo Finance (default)
collector = DataCollector(source='yahoo')
data = collector.collect_data(['AAPL', 'MSFT'], '2020-01-01', '2023-01-01')

# Using CSV files
collector = DataCollector(source='csv', config={'csv_path': 'data_system/datasets/processed_data/stocks.csv'})
data = collector.collect_data(['AAPL', 'MSFT'], '2020-01-01', '2023-01-01')

# Using a database (MySQL)
db_config = {
    'type': 'mysql',
    'host': 'localhost',
    'user': 'username',
    'password': 'password',
    'database': 'stock_data',
    'table': 'stocks'
}
collector = DataCollector(source='database', config={'db_config': db_config})
data = collector.collect_data(['AAPL', 'MSFT'], '2020-01-01', '2023-01-01')

# Using an API (Alpha Vantage)
api_config = {
    'type': 'alpha_vantage',
    'api_key': 'YOUR_API_KEY'
}
collector = DataCollector(source='api', config={'api_config': api_config})
data = collector.collect_data(['AAPL', 'MSFT'], '2020-01-01', '2023-01-01')
```

### Adding a New Model

Create a new file in the `model_system/models` directory that extends `BaseModel`:

```python
from model_system.models.base.base_model import BaseModel
import pandas as pd

class MyCustomModel(BaseModel):
    def train(self, X: pd.DataFrame, y: pd.Series):
        # Implement training logic
        pass

    def predict(self, X: pd.DataFrame):
        # Implement prediction logic
        pass

    def save(self, path: str):
        # Implement model saving
        pass

    def load(self, path: str):
        # Implement model loading
        pass
```

### Using MLflow for Experiment Tracking

The system integrates MLflow for experiment tracking. Here's how to use it:

1. **Training with MLflow**:
   ```bash
   python scripts/train_new_model.py --model_type lightgbm --version v1 --config config/models/lightgbm_config.yaml --use_mlflow
   ```

2. **Viewing Experiments**:
   ```bash
   python scripts/start_mlflow_ui.py
   ```
   Then open http://localhost:5000 in your browser.

3. **Programmatic Access to MLflow**:
   ```python
   from core.utils.mlflow_utils import setup_mlflow, log_metrics_to_mlflow

   # Start a run
   run_id = setup_mlflow('lightgbm', 'v1', config)

   # Log metrics
   metrics = {'accuracy': 0.95, 'loss': 0.05}
   log_metrics_to_mlflow(metrics)
   ```

## Recent Updates

### Project Reorganization

The project structure has been reorganized for better modularity and clarity:

- **Package Structure**: Moved all code into the `gbs` package for better organization and installation
- **Core Module**: Centralized common utilities in `gbs/core/utils/` with standardized logging and configuration
- **Workflow System**: Added a new workflow system for managing end-to-end experiments
- **Data System**: Enhanced data system with improved processors, handlers, and dataset classes
- **Configuration Management**: Improved configuration management with YAML-based configs

### Enhanced Backtrader System

The Backtrader system has been significantly improved with the following enhancements:

- **Detailed Logging**: Added comprehensive logging during backtest execution to monitor progress
- **Portfolio Strategy Integration**: Implemented `bt_portfolio_strategy.py` for use in backtest configurations
- **Model Loading**: Improved model loading mechanism with better error handling
- **Multiple Data Sources**: Enhanced support for multiple data feeds including feature data sources
- **Performance Optimization**: Implemented streaming mode with memory-saving options
- **Dynamic Stock Universe**: Support for varying number of stocks by day to reflect real market conditions

### Portfolio Transformer Model Improvements

- **Feature Handling**: Added support for using all features without feature selection
- **Data Organization**: Features are now organized by market cross-section per day with varying stock numbers
- **Portfolio Processor**: Implemented efficient data preparation with the portfolio processor
- **Collate Function**: Added custom collate function to handle inputs of different sizes
- **Small Dataset Support**: Added options for testing with small datasets

### Data System Enhancements

- **Portfolio Dataset**: Implemented `PortfolioDataset` class for handling portfolio data
- **Data Handlers**: Created `PortfolioDataHandler` for managing data flow
- **Data Processors**: Enhanced `PortfolioProcessor` for efficient data preparation
- **Configuration Options**: Added more configuration options for data processing

### Workflow System

- **Task-based Workflow**: Implemented a task-based workflow system for running experiments
- **Experiment Tracking**: Added support for tracking experiments with MLflow
- **Configuration Management**: Enhanced configuration management with YAML-based configs
- **Command-line Interface**: Added CLI support for running workflows

### Analysis System

The analysis system has been improved with a modular architecture:

- **BaseAnalyzer**: Abstract base class for all analyzers with common functionality
- **Portfolio Analysis Manager**: Unified interface for managing multiple analyzers
- **Brinson Attribution**: Enhanced Brinson attribution analysis
- **QuantStats Integration**: Integration with QuantStats for advanced portfolio analytics
- **Standardized Visualization**: Common visualization utilities for consistent reporting

### Example Workflows

Added example workflows for common use cases:

- **Workflow Demo**: Complete example of training, prediction, and backtesting workflow
- **AIPM**: AI Portfolio Management examples
- **Stock CNN**: Stock price prediction with CNN examples


## Contributing

Contributions to this project are welcome from authorized Onwish, Inc. team members only.
