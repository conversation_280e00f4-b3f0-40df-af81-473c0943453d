# cocktail

## Dev setup
- environment check
  - if you are in China, set 🪜 proxy for your shell first
  - `python -V`, `3.11.x` is fine as Django==4.2.2 is not compatible with python 3.12
    - if it's `python3 -V` satisfy version, you can use `python3` instead of `python` in the following commands
- python -m venv venv
- source venv/bin/activate
- pip install -r requirements.txt
- cp .env.template .env
  - fill in all the api token etc. in `.env` file (Ask Rui)
  - fill other properties in `.env`, such as `Basic Settings` and `AIRFLOW_HOME=/user_home_path/airflow`
  - Run `python set_env_vars.py` to set `.env` vars in teminal. Recommend to also paste `.env` directly into your `.bashrc`/`.zshrc`
- python app/manage.py createcachetable cache_default

- Initialize Django data models (Currently Django is just for admin purpose)
  * python app/manage.py migrate
    * use `unset all_proxy && unset ALL_PROXY` to resolve `ImportError: Using SOCKS proxy, but the 'socksio' package is not installed. Make sure to install httpx using pip install httpx[socks]`.
  * python app/manage.py createsuperuser (this is used to loging Admin)

### /admin Dashboard FE component
first, install node.js LTS from [Node.js LTS](https://nodejs.org/en)
- cd fe
- npm install
- npm run dev
- npm run build (Only when ready for deployment)

## To run
1. To run telegram server
   - First go to telegram BotFather to create a new bot, get the access token and bot url. Use the access token in .env file
   - python app/manage.py telegram
2. To run Django server
   - python app/manage.py runserver
   - If you want to test chat on the web, you need to start the frontend project.
     - clone [cocktail-fe](https://github.com/onwish/cocktail-fe)
     - install [Node.js LTS](https://nodejs.org/en)
     - ```npm install --registry=https://registry.npmmirror.com```
     - ```npm run dev```


 Try some example query and it should looks like this:
<img width="962" alt="image" src="https://github.com/onwish/cocktail/assets/54370652/01125db6-b5a1-4733-9179-bd0723eefe6f">


## Deploy

### prepare

1. python -m venv venv
2. source venv/bin/activate
3. pip install -r requirements.txt
4. cd fe && npm install && npm run build

Please refer to the [doc](https://www.notion.so/How-to-Deploy-Manually-1c99740853aa4f42b22545fa009ad59d) for more details.



## Test
python app/manage.py test agent
