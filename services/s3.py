import json
import os
from io import Buffered<PERSON><PERSON>er
from logging import get<PERSON>ogger
from typing import Any

import boto3
from botocore.exceptions import ClientError

logger = getLogger(__name__)

ERR_PREFIX = "S3:"


class S3Exception(Exception):
    pass


class S3Client:
    __access_key_id: str = ""
    __secret_access_key: str = ""
    __region_name: str = ""

    verbose: bool = False

    def __init__(self, aws_access_key_id: str, aws_secret_access_key: str, aws_region: str):
        self.__client = boto3.client(
            "s3",
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            region_name=aws_region,
        )

    def get_object(self, bucket: str, key: str) -> Any:
        cli = self.__client
        try:
            resp = cli.get_object(Bucket=bucket, Key=key)
        except ClientError as e:
            if e.response["Error"]["Code"] == "NoSuchKey":
                logger.debug(f"{ERR_PREFIX} No such key {key}") if self.verbose else None
                return ""
            else:
                logger.exception(e)
                raise
        data = resp["Body"].read()
        return data

    def get_str_object(self, bucket: str, key: str) -> str:
        res = self.get_object(bucket=bucket, key=key)
        return res.decode("utf-8") if res else ""

    def get_json_object(self, bucket: str, key: str) -> dict:
        s = self.get_str_object(bucket, key)
        return {} if not s else json.loads(s)

    def get_object_reader(self, bucket: str, key: str) -> BufferedReader | None:
        cli = self.__client
        try:
            resp = cli.get_object(Bucket=bucket, Key=key)
        except ClientError as e:
            if e.response["Error"]["Code"] == "NoSuchKey":
                logger.debug(f"{ERR_PREFIX} No such key {key}") if self.verbose else None
                return None
            else:
                logger.exception(e)
                raise

        return resp["Body"]

    def exists_object(self, bucket: str, object_key: str) -> bool:
        cli = self.__client
        try:
            cli.head_object(Bucket=bucket, Key=object_key)
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                return False
            else:
                raise
        return True

    def del_object(self, bucket: str, object_key: str) -> bool:
        cli = self.__client
        try:
            cli.delete_object(Bucket=bucket, Key=object_key)
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                return False
            else:
                raise
        return True

    def put_str_object(self, bucket: str, key: str, data: str) -> None:
        return self.put_object(bucket=bucket, key=key, data=data, mime_type="text/plain")

    def put_object(self, bucket: str, key: str, data: Any, mime_type: str = "application/octet-stream") -> None:
        cli = self.__client
        resp = cli.put_object(Bucket=bucket, Key=key, Body=data, ContentType=mime_type)

        if resp["ResponseMetadata"]["HTTPStatusCode"] != 200 or resp["ResponseMetadata"]["HTTPStatusCode"] != 200:
            msg = 'Failed to persist object {key}. {resp["ResponseMetadata"]}'
            logger.error(f"{ERR_PREFIX} {msg}")
            raise S3Exception(msg)

    def put_object_from_buffer(
        self,
        bucket: str,
        key: str,
        buffer: BufferedReader,
        mime_type: str = "application/octet-stream",
    ) -> None:
        cli = self.__client
        cli.upload_fileobj(buffer, Bucket=bucket, Key=key)
        # print(json.dumps(resp, indent=2, default=str))

        # if (
        #     resp["ResponseMetadata"]["HTTPStatusCode"] != 200
        #     or resp["ResponseMetadata"]["HTTPStatusCode"] != 200
        # ):
        #     msg = 'Failed to persist object {key}. {resp["ResponseMetadata"]}'
        #     logger.error(f"{ERR_PREFIX} {msg}")
        #     raise S3Exception(msg)

    def gen_pre_signed_url_put(
        self,
        bucket: str,
        key: str,
        content_type: str = "application/octet-stream",
        expiration: int = 900,
    ) -> str:
        return self._gen_pre_signed_url(
            bucket=bucket,
            key=key,
            method="put_object",
            content_type=content_type,
            expiration=expiration,
        )

    def gen_pre_signed_url_get(
        self,
        bucket: str,
        key: str,
        content_type: str = "application/octet-stream",
        expiration: int = 900,
    ) -> str:
        return self._gen_pre_signed_url(
            bucket=bucket,
            key=key,
            method="get_object",
            content_type=content_type,
            expiration=expiration,
        )

    def _gen_pre_signed_url(
        self,
        bucket: str,
        key: str,
        method: str = "put_object",
        content_type: str = "application/octet-stream",
        expiration: int = 900,
    ) -> str:
        """
        Generate a presigned URL for uploading a file to S3 within a user-specific folder.

        :param bucket: S3 bucket name
        :param key: S3 key
        :param method: operation name to be signed
        :param content_type: content type
        :param expiration: Expiration time in seconds (default is 1800 seconds, i.e., 30 minutes)
        :return: pre-signed URL as a string
        """
        cli = self.__client
        try:
            url = cli.generate_presigned_url(
                method,
                Params={"Bucket": bucket, "Key": key, "ContentType": content_type},
                ExpiresIn=expiration,
            )
            return url
        except ClientError as e:
            msg = f"Error generating pre-signed URL for {bucket}/{key}"
            logger.exception(f"{ERR_PREFIX} {msg}: {e}")
            raise S3Exception(f"{msg}: {e}")

    def put_json(self, bucket: str, key: str, data: str) -> None:
        cli = self.__client
        resp = cli.put_object(Bucket=bucket, Key=key, Body=data, ContentType="application/json")
        if resp["ResponseMetadata"]["HTTPStatusCode"] != 200 or resp["ResponseMetadata"]["HTTPStatusCode"] != 200:
            msg = 'Failed to persist object {key}. {resp["ResponseMetadata"]}'
            logger.error(f"{ERR_PREFIX} {msg}")
            raise S3Exception(msg)


def get_s3_client() -> S3Client:
    return S3Client(
        aws_access_key_id=os.environ["AWS_S3_ACCESS_KEY"],
        aws_secret_access_key=os.environ["AWS_S3_SECRET_ACCESS_KEY"],
        aws_region=os.environ["AWS_DEFAULT_REGION"],
    )
