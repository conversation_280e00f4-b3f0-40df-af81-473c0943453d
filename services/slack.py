import os
import platform
from datetime import datetime, timed<PERSON>ta
from logging import getLogger

from slack_sdk import WebClient as Slack<PERSON>lient
from slack_sdk.errors import SlackClientError

from common.utils.confutil import get_list_env

ALERT_ENABLED_ENV = get_list_env("ALERT_ENABLED_ENV") or []

DEFAULT_CHANNEL = "C0660KV8Z8D"
TEST_CHANNEL = "C06EYTT5RPA"  # my test channel
DEFAULT_ALERT_CHANNEL = DEFAULT_CHANNEL  # "C0652H1RFNH"

DEFAULT_ALERT_INTERVAL = 10 * 60  # seconds
DEFAULT_ALERT_THRESHOLD = 3  # occurring times during interval that we will alert

logger = getLogger(__name__)
hostname = platform.node()

_alerts: dict[str, list] = {}  # simple solution


def send_alert(
    e: str | Exception,
    ex: str | None = None,
    channel: str | None = None,
    force: bool = False,
) -> None:
    # selected emoji - ":firefighter: :fire_engine: :octagonal_sign: :ambulance:"
    #                - ":helmet_with_white_cross: :warning: :fire_extinguisher: :fire:"

    env = os.environ.get("ENV", hostname).lower()
    if env not in ["prod", "app.onwish.ai"] + ALERT_ENABLED_ENV:
        logger.warning(
            f"DISMISS non-prod alert `{e}` from `{env}` to `{channel}` \n({ex})"
        )
        return

    e = str(e)

    # handle same type of errors
    e_alerts = _alerts.get(e, [])
    e_alerts.append((datetime.now(), ex))
    _alerts[e] = e_alerts

    if not force:
        # check interval & threshold
        e_started_at = e_alerts[0][0]
        if len(e_alerts) < DEFAULT_ALERT_THRESHOLD:
            return
        if datetime.now() - e_started_at > timedelta(seconds=DEFAULT_ALERT_INTERVAL):
            del e_alerts[0]
            return

    channel = channel or DEFAULT_ALERT_CHANNEL
    icon = ":fire:" if env == "prod" else ":helmet_with_white_cross:"
    msg_ts = send_message(
        f"{icon} Alert from `{env}` {icon} ```\n{e}\n```", channel=channel
    )
    if msg_ts:
        reply(
            msg_ts,
            [f"{t}\n```\n{ex}\n```" for t, ex in e_alerts],
            channel=channel,
        )

    _alerts.pop(e)


def send_message(message: str, channel: str | None = None) -> str:
    try:
        cli = SlackClient(token=os.environ["SLACK_BOT_TOKEN"])
        result = cli.chat_postMessage(channel=channel or DEFAULT_CHANNEL, text=message)
        if result and "ok" in result and result["ok"] is True:
            return result["ts"]
    except SlackClientError as e:
        logger.error(e)
    return ""


def reply(message_ts: str, messages: list[str], channel: str | None = None) -> None:
    try:
        cli = SlackClient(token=os.environ["SLACK_BOT_TOKEN"])
        for msg in messages:
            cli.chat_postMessage(
                channel=channel or DEFAULT_CHANNEL, thread_ts=message_ts, text=msg
            )
    except SlackClientError as e:
        logger.error(e)


def send_file(title: str, message: str, fpath: str, channel: str | None = None) -> None:
    try:
        cli = SlackClient(token=os.environ["SLACK_BOT_TOKEN"])
        cli.files_upload_v2(
            channel=channel or DEFAULT_CHANNEL,
            file=fpath,
            title=title,
            initial_comment=message,
        )
    except SlackClientError as e:
        logger.error(e)
