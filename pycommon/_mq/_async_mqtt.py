"""
MQTT Client for server-client communication
"""

from typing import Optional
import random
from paho.mqtt import client as mqtt_client
import asyncio
import socket


from logwrapper import getLogger

log = getLogger("common.mqtt")
log.debug = print

# broker = 'broker.emqx.io'
BROKER = "test.mosquitto.org"
PORT = 1883
TOPIC_PREFIX = "/sam/cm"
TOPIC_SERVER_COMMAND = f"{TOPIC_PREFIX}/1"
TOPIC_SERVER_PUSH = f"{TOPIC_PREFIX}/2"
TOPIC_CLIENT_REPORT = f"{TOPIC_PREFIX}/3"


class AsyncMQTTClient(object):
    def __init__(self, client):
        self.loop = asyncio.get_event_loop()
        self.client = client
        self.client.on_socket_open = self.on_socket_open
        self.client.on_socket_close = self.on_socket_close
        self.client.on_socket_register_write = self.on_socket_register_write
        self.client.on_socket_unregister_write = self.on_socket_unregister_write

    def on_socket_open(self, client, userdata, sock):
        log.debug("Socket opened")

        def cb():
            log.debug("Socket is readable, calling loop_read")
            client.loop_read()

        self.loop.add_reader(sock, cb)
        self.misc = self.loop.create_task(self.misc_loop())

    def on_socket_close(self, client, userdata, sock):
        log.debug("Socket closed")
        self.loop.remove_reader(sock)
        self.misc.cancel()

    def on_socket_register_write(self, client, userdata, sock):
        log.debug("Watching socket for writability.")

        def cb():
            log.debug("Socket is writable, calling loop_write")
            client.loop_write()

        self.loop.add_writer(sock, cb)

    def on_socket_unregister_write(self, client, userdata, sock):
        log.debug("Stop watching socket for writability.")
        self.loop.remove_writer(sock)

    async def misc_loop(self):
        log.debug("misc_loop started")
        while self.client.loop_misc() == mqtt_client.MQTT_ERR_SUCCESS:
            try:
                await asyncio.sleep(1)
            except asyncio.CancelledError:
                break
        log.debug("misc_loop finished")


class CommClient(object):
    # generate client ID with pub prefix randomly
    client_id = f"sam-cm-{random.randint(0, 100)}"

    def __init__(self):
        self.loop = asyncio.get_event_loop()

    def on_connect(self, client, userdata, flags, rc):
        log.debug("Broker connected.")
        client.subscribe(TOPIC_SERVER_COMMAND)

    def on_message(self, client, userdata, msg):
        if not self.got_message:
            print("Got unexpected message: {}".format(msg.decode()))
        else:
            self.got_message.set_result(msg.payload)

    def on_disconnect(self, client, userdata, rc):
        self.disconnected.set_result(rc)

    def listen(self, topic: str, on_message: callable):
        pass

    def send(self, topic: str, message: str):
        pass

    async def start(self):
        self.disconnected = self.loop.create_future()
        self.got_message = None

        self.client = mqtt_client.Client(client_id=self.client_id)
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_disconnect = self.on_disconnect

        aioh = AsyncMQTTClient(self.client)

        self.client.connect(BROKER, PORT, 60)
        self.client.socket().setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 2048)

        for c in range(3):
            await asyncio.sleep(5)
            print("Publishing")
            self.got_message = self.loop.create_future()
            self.client.publish(TOPIC_SERVER_COMMAND, b"Hello" * 40000, qos=1)
            msg = await self.got_message
            print("Got response with {} bytes".format(len(msg)))
            self.got_message = None

        await self.stop()

    async def stop(self):
        self.client.disconnect()
        print("Disconnected: {}".format(await self.disconnected))


if __name__ == "__main__":
    print("Starting")
    loop = asyncio.get_event_loop()
    loop.run_until_complete(CommClient().start())
    loop.close()
    print("Finished")
