"""system utilities"""

import os
import sys
import platform
import subprocess
from datetime import timezone, timedelta, datetime

# import asyncio
from ..types import _File

# from pycommon.types import _File

from .terminal import warn


__is_windows = None
__is_macos = None
__is_linux = None

UTC = timezone.utc
CST = timezone(timedelta(hours=8), "China Standard Time")


def is_windows():
    if __is_windows is None:
        __is_windows = sys.platform.startswith("win")
    return __is_windows


def is_macos():
    if __is_macos is None:
        __is_macos = sys.platform.startswith("darwin")
    return __is_macos


def is_linux():
    if __is_linux is None:
        __is_linux = sys.platform.startswith("linux")
    return __is_linux


def get_arch():
    arch = platform.uname().machine
    if arch == "x86_64":
        return "amd64"
    elif arch == "aarch_64":
        return "arm64"
    else:
        return arch


def get_user_home():
    """get user home path on different sys_platform"""
    path = ""
    if is_windows():
        path = os.getenv("USERPROFILE")
    elif is_macos():
        path = os.getenv("HOME", "~")
    elif is_linux():
        path = os.getenv("HOME", "~")
    else:
        warn('Unsupported OS %s. Use current path "." as home.' % sys.platform)
        path = os.abspath(".")

    return path


def get_local_time(dt: datetime = None, tz: timezone = CST):
    """convert given datetime to local datetime with given timezone information.

    :param dt: datetime with timezone information.
    :param tz: target timezone information.
    :return: converted datetime object with target timezone information.
    """
    if not dt:
        dt = datetime.utcnow()
    assert isinstance(dt, datetime)
    dt = dt.replace(tzinfo=UTC)
    return dt.astimezone(tz)


def get_thread_id(thread):
    # TODO: add get_tname func
    if sys.version_info.major >= 3 and sys.version_info.minor >= 8:
        return thread.native_id
    else:
        # return thread.ident
        return thread.getName()


def run_cmd(
    *commands: str, stdin: _File = None, timeout: int = 30, cwd: str = None, with_shell: bool = False, env: dict = None
) -> tuple[int, str, str]:
    """run linux shell, window cmd/ps command scripts in a sub-process.
    args:
        *commands :str - command list.
    keyword args:
        timeout :int [default:30] - how many *seconds* the command will timeout.
        cwd :str [default:None] - set current working directory
        with_shell :str [default:False] - whether use *shell* to run the comands.
                    **consider security** https://docs.python.org/zh-cn/3.11/library/subprocess.html#security-considerations.
        env :dict [default:None] - environment variables for the new process.
    returns:
        int - the return code
        str - the error message
        str - the output message
    raises:
        TimeoutError - if exceed the time paased in `timeout` (unit seconds).
    """

    try:
        proc = subprocess.run(
            commands,
            stdin=stdin,
            timeout=timeout,
            cwd=cwd,
            shell=with_shell,
            text=True,
            capture_output=True,
        )
    except subprocess.TimeoutExpired as e:
        raise TimeoutError(e.output)

    return proc.returncode, proc.stderr, proc.stdout


# def async_run_cmd(*commands: str, stdin: _File = None, timeout: int = 30,
#                   cwd: str = None, with_shell: bool = False, env: dict = None,
#                   callback: callable = None) -> tuple[int, str, str]:
#     '''Asynchronize run linux shell, window cmd/ps command scripts in a sub-process.
#     args:
#         *commands :str - command list.
#     keyword args:
#         timeout :int [default:30] - how many *seconds* the command will timeout.
#         cwd :str [default:None] - set current working directory
#         with_shell :str [default:False] - whether use *shell* to run the comands.
#                     **consider security** https://docs.python.org/zh-cn/3.11/library/subprocess.html#security-considerations.
#         env :dict [default:None] - environment variables for the new process.
#         callback :callable [default:None] - callback function accepts int(recurn code), str(error), str(output), Exception(if)
#     returns:
#         int - the return code
#         str - the error message
#         str - the output message
#     raises:
#         TimeoutError - if exceed the time paased in `timeout` (unit seconds).

#     example callback:
#         def cmd_finished(rc: int, error: str, output: str, ex: Exception):
#             if ex is not None:
#                 # handle the exception with output.
#             else:
#                 # handle rc / error/ output.
#     '''
#     print('a')
#     asyncio.run(_async_run_cmd(*commands, stdin=stdin,
#                                timeout=timeout, cwd=cwd, with_shell=with_shell, env=env))
#     print('b')

# async def _async_run_cmd(*commands: str, stdin: _File = None, timeout: int = 30,
#                          cwd: str = None, with_shell: bool = False, env: dict = None) -> tuple[int, str, str]:
#     print('1')
#     try:
#         proc = subprocess.run(commands, stdin=stdin, timeout=timeout, text=True,
#                               cwd=cwd, shell=with_shell, capture_output=True)
#     except subprocess.TimeoutExpired as e:
#         raise TimeoutError(e)
#     print('2')
#     return proc.returncode, proc.stderr, proc.stdout


# if __name__ == '__main__':
#     a = async_run_cmd('sleep 2;', timeout=1, with_shell=True)
#     # print(dir(a))
#     print(type(a), a)
