"""string utilities"""

import locale
import re
import unicodedata
from hashlib import sha256
from logging import getLogger
from typing import Any
from urllib.parse import urlparse, urlunparse


logger = getLogger(__name__)


def ellipse(s: str, size: int = 50) -> str:
    return s[:size] + "..." if s and len(s) > size else str(s)


def quick_hash(s: str) -> str:
    """quickly generate hash for str"""
    from xxhash import xxh128

    h = xxh128()
    h.update(s)
    return h.hexdigest()


def hash_sha256(s: str | bytes) -> str:
    """generate sha256 hash for str/bytes"""
    if isinstance(s, str):
        s = s.encode("utf-8")
    return sha256(s).hexdigest()


def normalize_uri(uri: str) -> str:
    """normalize uri(path, filename) as safe str. remove the schema if it's url."""
    parsed_url = urlparse(uri)
    no_scheme_url = parsed_url._replace(scheme="")
    return urlunparse(no_scheme_url)


def slugify(value: Any, allow_unicode: bool = True) -> str:
    """
    Taken from https://github.com/django/django/blob/master/django/utils/text.py
    Convert to ASCII if 'allow_unicode' is False. Convert spaces or repeated
    dashes to single dashes. Remove characters that aren't alphanumerics,
    underscores, or hyphens. Convert to lowercase. Also strip leading and
    trailing whitespace, dashes, and underscores.
    """
    value = str(value)
    if allow_unicode:
        value = unicodedata.normalize("NFKC", value)
    else:
        value = unicodedata.normalize("NFKD", value).encode("ascii", "ignore").decode("ascii")
    value = re.sub(r"[^\w\s-]", "", value.lower())
    return re.sub(r"[-\s]+", "-", value).strip("-_")


def parse_all_numbers(
    s: str | int | float | None, allow_none: bool = False, _locale: str = "en_US.UTF-8"
) -> list[float | None]:
    """Parse a string into float."""
    if not s:
        return [None] if allow_none else [0]

    if isinstance(s, (int, float)):
        return [s]

    results = re.findall(r"[-+]?[.]?[\d]+(?:,\d\d\d)*[\.]?\d*(?:[eE][-+]?\d+)?", s)
    locale.setlocale(locale.LC_ALL, _locale)
    results = [locale.atof(r) for r in results]
    return results


def parse_number(s: str | int | float | None, allow_none: bool = False, _locale: str = "en_US.UTF-8") -> float | None:
    return parse_all_numbers(s, allow_none=allow_none, _locale=_locale)[0]


def parse_int(s: str | int | float | None, allow_none: bool = False, _locale: str = "en_US.UTF-8") -> int | None:
    n = parse_number(s, allow_none=allow_none, _locale=_locale)
    return n if n is None else int(n)


def parse_float(s: str | int | float | None, allow_none: bool = False, _locale: str = "en_US.UTF-8") -> float | None:
    n = parse_number(s, allow_none=allow_none, _locale=_locale)
    return n if n is None else float(n)


__emoji_to_str_set = {
    "🟢": {"open", "active", "enabled"},
    "🔴": {"closed", "inactive", "disabled"},
    "✅": {"correct"},
    "❎": {"incorrect", "wrong", "cancelled"},
    "❌": {"deleted", "removed", "retired"},
    "🚫": {"prohibit", "prohibited", "deny"},
    "❓": {"?", "question"},
    "🛑": {"stop"},
    "⏳": {"progress", "in-progress"},
    "🔄": {"refund", "refunded", "recycle"},
    "🧾": {"receipt"},
    "💰": {"wallet", "payment", "pay", "cash"},
    "💵": {"money"},
    "$": {"usd", "hk$"},
    "💲": {"$", "dollar"},
    "¥": {"rmb", "cny"},
    "⭐": {"star", "favorite"},
    "🆓": {"free"},
    "🎁": {"gift", "present"},
    "🏆": {"winner", "achievement"},
    "🏅": {"medal", "reward"},
    "🎟️": {"ticket", "member", "subscriber"},
    "💎": {"diamond", "vip"},
}
__str_to_emoji = {}


def get_emoji(s: str, my_str_to_emoji: dict = {}) -> str:
    """return an emoji by given string, if not found, return origin string

    args:
        my_str_to_emoji (dict) - your override mapping(str:emoji)
    """

    def __load_emoji() -> None:
        if not __str_to_emoji:
            for k, str_set in __emoji_to_str_set.items():
                for em in str_set:
                    if em in __str_to_emoji:
                        logger.info(f"{em} is mapping to {__str_to_emoji[em]}, will be replaced by {k}")
                    __str_to_emoji[em] = k

    __load_emoji()
    return my_str_to_emoji.get(s) or __str_to_emoji.get(s, s)


def to_pinyin(s: str, initial_only: bool = False) -> str:
    """convert given string to pinyin

    1. If s is English, combine all parts of names in lower case.
    2. If s is Chinese, combine all Pinyin of each word.

    Args:
        s: the given string to convert
        initial_only: if True, only keep first char of each word

    Returns:
        A converted pinyin
    """
    from pypinyin import pinyin
    from pypinyin import Style

    if not s:
        return s

    # Check if the name contains Chinese characters
    has_chinese = bool(re.search(r"[\u4e00-\u9fff]", s))

    if has_chinese:
        # Convert Chinese characters to pinyin
        py_list = pinyin(s, style=Style.NORMAL)
        if initial_only:
            r = "".join(py[0][0] for py in py_list)
        else:
            r = "".join(py[0] for py in py_list)
    else:
        # For English, convert to lowercase and remove special characters
        if initial_only:
            # Split by any non-alphanumeric characters and take first char of each word
            words = re.findall(r"[a-z0-9]+", s)
            r = "".join(word[0] for word in words if word)
        else:
            r = re.sub(r"[^a-zA-Z0-9]", "", s.lower())

    return r
