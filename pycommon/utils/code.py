"""code utilities"""

import subprocess
from pathlib import Path
from datetime import datetime
from os import linesep

from .terminal import warn, debug, info, tip, error, success


def git_get_commit(code_path: str = None) -> str:
    """get current git comit. (slow system invoking)"""

    p = Path(code_path) if code_path else Path.cwd()
    code_path = p.absolute() if p.is_dir() else p.parent.absolute()

    try:
        # `git rev-parse HEAD`
        proc = subprocess.run(["git", "rev-parse", "HEAD"], timeout=3, cwd=code_path, capture_output=True)
    except subprocess.TimeoutExpired as e:
        warn(f"{__name__} {e}")
        return None

    if proc.returncode == 0 and proc.stdout is not None:
        commit = proc.stdout.strip().decode()
        commit = commit.strip(linesep)
        # debug(f'Got commit "{commit}" for {code_path}')
        return commit

    return None


def git_get_tag(code_path: str = None) -> str:
    """get latest git tag. (slow system invoking)"""

    p = Path(code_path) if code_path else Path.cwd()
    code_path = p.absolute() if p.is_dir() else p.parent.absolute()

    try:
        # `git describe --tags $(git rev-list --tags --max-count=1)`
        proc = subprocess.run(
            ["git", "describe", "--tags", "$(git rev-list --tags --max-count=1)"],
            timeout=3,
            cwd=code_path,
            capture_output=True,
        )
    except subprocess.TimeoutExpired as e:
        warn(f"{__name__} {e}")
        return None

    if proc.returncode == 0 and proc.stdout is not None:
        tag = proc.stdout.strip().decode()
        tag = tag.strip(linesep)
        # debug(f'Got tag "{tag}" for {code_path}')
        return tag

    return None


def get_build(code_path: str = None) -> str:
    """get current build"""
    # p = Path(code_path) if code_path else Path.cwd()
    # d = datetime.fromtimestamp(p.stat().st_mtime)
    # return f'{d.year}{d.month:0>2}{d.day:0>2}'

    build = git_get_tag(code_path=code_path)
    # debug(f'Got build "{build}" for {code_path}')
    return build


class Version:
    """Class used to define app/module version"""

    major: int = 0
    minor: int = 0
    build: str = "0"
    commit: str = ""

    def __init__(self, major: int = 0, minor: int = 0, build: int = 0, code_path=None) -> None:
        self.major = major
        self.minor = minor
        self.build = get_build(code_path=code_path) or str(build)
        self.commit = git_get_commit(code_path=code_path)

    def __str__(self) -> str:
        ver = f"{self.major}.{self.minor}.{self.build}"
        if self.commit:
            ver += f".{self.commit}"
        return ver
