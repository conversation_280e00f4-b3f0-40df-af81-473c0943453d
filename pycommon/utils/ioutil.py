import zlib
from pathlib import Path
from urllib.parse import urlparse, quote_plus

import requests
from fake_useragent import User<PERSON><PERSON>


def load_url(url: str, with_cache: bool = True) -> str:
    """load content locally, from cache or remote by url"""

    cache_path = Path("/tmp/quick_load_cache")

    parse_result = urlparse(url)
    fpath = parse_result.path if parse_result.scheme in ["", "file"] else quote_plus(url)

    p = Path(fpath)
    cache_file = Path(Path.joinpath(cache_path, Path(fpath)))

    try:
        if p.exists():
            if p.suffix in [".zip", ".gz", ".gzip"]:
                buf = p.read_bytes()
                txt = zlib.decompress(buf).decode()
            else:
                txt = p.read_text()
        else:
            if with_cache and cache_file.exists():
                buf = cache_file.read_bytes()
                txt = zlib.decompress(buf).decode()
            else:
                headers = {"User-Agent": UserAgent().random}
                r = requests.get(url=url, headers=headers)
                r.raise_for_status()
                txt = r.text
                if with_cache:
                    cache_path.mkdir(mode=0o777, parents=True, exist_ok=True)
                    cache_file.write_bytes(zlib.compress(r.content))
    except IOError:
        txt = ""
    return txt
