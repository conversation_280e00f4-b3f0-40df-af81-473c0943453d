import asyncio
import functools
import traceback
from asyncio import Task
from logging import getLogger
from typing import (
    Awaitable,
    Callable,
    Coroutine,
    Dict,
    Iterable,
    List,
    Optional,
    Tuple,
    TypeVar,
    Any,
)

from tqdm.asyncio import tqdm

logger = getLogger(__name__)


T = TypeVar("T")
R = TypeVar("R")


async def run_concurrent_tasks(
    method: Callable[..., Awaitable[R]],
    args_list: List[Tuple],
    kwargs_list: Optional[List[Dict]] = None,
    concurrency: int = 8,
    show_progress: bool = True,
) -> List[Optional[R]]:
    """Run a method with some args concurrently with a concurrency limit and progress tracking.

    Args:
        method: The asynchronous method to run.
        args_list: The list of positional arguments tuples to pass to the method.
        kwargs_list: The list of keyword arguments dictionaries to pass to the method.
        concurrency: The concurrency limit.
        show_progress: Whether to show progress using tqdm.

    Returns:
        A list of results from the method.
    """
    semaphore = asyncio.Semaphore(concurrency)
    kwargs_list = kwargs_list or [{} for _ in args_list]

    async def call_with_semaphore(args: Tuple, kwargs: Dict) -> Optional[R]:
        async with semaphore:
            return await method(*args, **kwargs)

    futures = [asyncio.ensure_future(call_with_semaphore(args, kwargs)) for args, kwargs in zip(args_list, kwargs_list)]

    results = []
    for future in tqdm(asyncio.as_completed(futures), total=len(args_list), disable=not show_progress):
        res = await future
        results.append(res)

    return results


def with_concurrency_limit(coroutines: Iterable[Coroutine], limit: int) -> list[Coroutine]:
    """Decorate coroutines to limit concurrency.
    Enforces a limit on the number of coroutines that can run concurrently in higher
    level asyncio-compatible concurrency managers like asyncio.gather(coroutines) and
    asyncio.as_completed(coroutines).
    """
    sem = asyncio.Semaphore(limit)

    async def _limit_coroutine(coroutine: Coroutine) -> Coroutine:
        async with sem:
            return await coroutine

    return [_limit_coroutine(coroutine) for coroutine in coroutines]


async def gather_with_error(
    *coroutines: Coroutine,
    log_errors: bool = True,
    log_title: str = "",
    log_with_traceback: bool = False,
    split_result_errors: bool = True,
    log_level: str = "error",
    log_exclude_exceptions: Optional[Iterable[type[BaseException]]] = None,
) -> tuple[list, list]:
    """run asyncio.gather() and returns list of succeed and list of exceptions"""
    results = await asyncio.gather(*coroutines, return_exceptions=True)
    succeed = []
    errs = []

    for r in results:
        if isinstance(r, BaseException):
            errs.append(r)
        else:
            succeed.append(r)

    func = getattr(logger, log_level) or logger.error

    if log_errors and len(errs) > 0:
        try:
            from common.utils.lang import get_caller_loc
        except ImportError:
            get_caller_loc = lambda: "<line of code>"

        logs = []
        exclude_exceptions = tuple(exc for exc in log_exclude_exceptions) if log_exclude_exceptions else None
        for e in errs:
            if exclude_exceptions and isinstance(e, exclude_exceptions):
                continue

            if log_with_traceback:
                logs.append("  " + "".join(traceback.format_exception(e, limit=5)))
            else:
                logs.append(f"  {e}")
        if len(logs) > 0:
            logs.insert(0, f"{log_title}: {len(logs)} errors - {get_caller_loc()}")
            for msg in logs:
                func(msg)

    return (succeed, errs) if split_result_errors else (results, [])


class RunningTasksManager:
    """
    A singleton class to manage running async tasks safely.
    """

    _instance: Optional["RunningTasksManager"] = None
    _lock: asyncio.Lock
    running_tasks: Dict[str, Task]

    def __new__(cls) -> "RunningTasksManager":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.running_tasks = {}
            cls._instance._lock = asyncio.Lock()
        return cls._instance

    async def add_task(self, key: str, task: Task) -> None:
        async with self._lock:
            self.running_tasks[key] = task

    async def get_task(self, key: str) -> Optional[Task]:
        async with self._lock:
            return self.running_tasks.get(key)

    async def remove_task(self, key: str) -> None:
        async with self._lock:
            if key in self.running_tasks:
                del self.running_tasks[key]

    async def get_all_tasks(self) -> Dict[str, Task]:
        async with self._lock:
            return dict(self.running_tasks)


def is_async_callable(obj: Any) -> bool:
    while isinstance(obj, functools.partial):
        obj = obj.func

    return asyncio.iscoroutinefunction(obj) or (callable(obj) and asyncio.iscoroutinefunction(obj.__call__))
