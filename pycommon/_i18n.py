"""
Convenience class for contexture i18n transtation invoking.
"""

from gettext import gettext, pgettext, ngettext
from functools import partial


class ContextureI18nMeta(type):
    """
    Meta class to define contexture i18n classes.
    Define your context in memebers and it will bind context with pgettext func.

    kwargs:
    :type gettext: callable
    :param gettext: gettext like func such as django.utils.translation.gettext (default is gettext.gettext).

    :type pgettext: callable
    :param pgettext: pgettext like func such as django.utils.translation.pgettext (default is gettext.pgettext).

    :type ngettext: callable
    :param ngettext: ngettext like func such as django.utils.translation.ngettext (default is gettext.ngettext).

    Example::
        ```
        from django.utils.translation import pgettext as django_pgettext

        class DjangoI18N(object, metaclass=ContextureI18nMeta, pgettext=django_pgettext):
            model_name = 'model name'
            model_f_name = 'model field name'
            model_f_help = 'model field help'

        class MyModel(models.Model):
            id = models.AutoField('ID', primary_key=True, help_text=DjangoI18N.model_f_help('unique identifier'))

        ```

    """

    def __new__(mcs, name, bases, namespace, **kwargs):
        instance = super().__new__(mcs, name, bases, namespace)

        gt = kwargs.get("gettext", gettext)
        pgt = kwargs.get("pgettext", pgettext)
        ngt = kwargs.get("ngettext", ngettext)

        for f in [gt, pgt, ngt]:
            if not callable(f):
                raise ValueError(f'"{f}" must be a function.')

        for attr, val in namespace.items():
            if attr.startswith("_"):
                continue
            else:
                setattr(instance, attr, partial(pgt, val))

        instance._ = gt
        instance._p = pgt
        instance._n = ngt

        return instance


# class I18N(object, metaclass=ContextureI18nMeta):
# """Quck access contexture transtaltion method by context
# e.g.  I18N.docstr("class doc bla bla bla") same as pgettext("doc string", "class doc bla bla bla")
# """
