import json
import tempfile
import unittest
from pathlib import Path

from pycommon.observation import (
    StoreObserver,
    TerminalLogObserver,
    Notification,
    PythonLogObserver,
    ObserverManager,
    Observable,
)
from pycommon.store import FileStore
from pycommon.utils.strutil import slugify


class TestObserver(unittest.TestCase):

    def setUp(self):
        self.ob: Observable = ObserverManager()

    def tearDown(self):
        self.ob.clean_observers()

    def test_register(self) -> None:
        assert isinstance(self.ob, ObserverManager)

        ob1 = TerminalLogObserver()
        self.assertEquals(0, len(self.ob.observers))
        self.ob.register_observer(PythonLogObserver())
        self.assertEquals(1, len(self.ob.observers))
        self.ob.register_observer(PythonLogObserver(), ob1)
        self.assertEquals(3, len(self.ob.observers))
        self.ob.unregister_observer(TerminalLogObserver())
        self.assertEquals(3, len(self.ob.observers))
        self.ob.unregister_observer(ob1)
        self.assertEquals(2, len(self.ob.observers))
        self.ob.pop_observer()
        self.assertEquals(1, len(self.ob.observers))
        self.ob.clean_observers()
        self.assertEquals(0, len(self.ob.observers))

    def test_store_tracker(self) -> None:
        with tempfile.TemporaryDirectory() as tmpdir:
            store = FileStore(root_path=str(tmpdir))
            self.ob.register_observer(StoreObserver(store=store))

            msg = "Hello, Samuel."
            self.ob.observe(message=msg)

            key = slugify(msg)
            with open(Path(tmpdir) / key, "r") as fp:
                value = fp.read()

        d = json.loads(value)
        note = Notification(**d)
        assert note.data is None
        assert note.message == msg

    def test_log_tracker(self) -> None:
        self.ob.register_observer(PythonLogObserver())
        self.ob.observe(message="Hello, Samuel.")
        self.ob.observe(message="Nice to meet you.", data={"level": "info"})
        self.ob.observe(message="How are you?", data={"level": "warning"})
        self.ob.observe(message="Goodbye", data={"level": "error"})

    def test_terminal_tracker(self) -> None:
        self.ob.register_observer(TerminalLogObserver())
        self.ob.observe(message="Hello, Samuel.", data={"level": "tip"})
        self.ob.observe(message="Goodbye, Samuel.")

    def test_subject(self):
        with tempfile.TemporaryDirectory() as tmpdir:
            store = FileStore(root_path=str(tmpdir))
            self.ob.register_observer(PythonLogObserver(), StoreObserver(store=store), TerminalLogObserver())

            msgs = [
                ("This message should be logged.", ["log"]),
                ("This message should be output in terminal.", ["terminal"]),
                ("This message should be stored in store", ["store"]),
                ("This message should be stored in store and output in terminal", ["store", "terminal"]),
            ]
            for msg, subjects in msgs:
                self.ob.observe(message=msg, subjects=subjects)

            p = Path(tmpdir)
            for c in p.iterdir():
                print(c)

            key = slugify(msgs[2][0])
            with open(p / key, "r") as fp:
                value2 = fp.read()

            key = slugify(msgs[3][0])
            with open(p / key, "r") as fp:
                value3 = fp.read()

        d = json.loads(value2)
        note = Notification(**d)
        assert note.data is None
        assert note.message == msgs[2][0]

        d = json.loads(value3)
        note = Notification(**d)
        assert note.data is None
        assert note.message == msgs[3][0]
