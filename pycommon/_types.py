"""
Custom type definitions
"""

from abc import ABC, abstractclassmethod, abstractmethod
from uuid import UUID
from typing import Union, NewType
from pydantic import (
    BaseModel as BaseObject,
    Field,
    parse_obj_as,
    parse_raw_as,
    validator,
    constr,
)
from typing import IO, Any, Union
from typing_extensions import TypeAlias

# _File: TypeAlias = int | IO[Any] | None
_File = Union[int, IO[Any], None]

UniqueIdentity = Union[int, str, UUID]


# ----- data object types


class AbstractBasePO(object):
    """PersistentObject base, used for DB models so that can be used here"""

    pass


class AbstractBaseObject(ABC, BaseObject):
    """Object base, used for data types"""

    id: int

    class Config:
        # pydantic config
        allow_population_by_field_name = True
        orm_mode = True

    @staticmethod
    def parse_obj_as(obj_type: type, obj_data: object):
        """Parse an object as a type.wrapper pydantic parse_obj_as func"""
        return parse_obj_as(obj_type, obj_data)

    @staticmethod
    def parse_raw_as(obj_type: type, obj_str: str):
        """wrapper pydantic parse_raw_as func"""
        return parse_raw_as(obj_type, obj_str)

    @classmethod
    def from_po(cls, po: AbstractBasePO):
        return cls.from_orm(po)

    # make object hash-able
    def __hash__(self):
        return hash((type(self), self.id))

    # ----- uid related -----
    # This "uid" is used to identify unique object.
    # It's not same as "pk"/"primary key" in Django/other DB query filter.
    # Although they are same at most moments.

    uid_field = "id"

    @property
    def uid(self) -> UniqueIdentity:
        return self.id

    @uid.setter
    def uid(self, v: UniqueIdentity):
        self.id = v


# ----- type helpers


def export_all(*args) -> list[str]:
    """use `__all__=export_all(TypeA, TypeB, Func1, Func2, Var1)` to export __all__ symbols"""
    return [kls.__name__ for kls in (args)]
