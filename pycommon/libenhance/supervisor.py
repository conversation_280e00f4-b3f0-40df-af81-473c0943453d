import logging
import os
from logging import LogRecord


class SupervisorProcessFilter(logging.Filter):
    def filter(self, record: LogRecord) -> bool:
        supervisor_proc_name = os.environ.get("SUPERVISOR_PROCESS_NAME", "")
        cur_proc_name = record.processName or ""
        if supervisor_proc_name:
            if cur_proc_name == "MainProces":
                record.processName = supervisor_proc_name
            else:
                record.processName = supervisor_proc_name + "/" + cur_proc_name
        return True
