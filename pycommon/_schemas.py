"""
Objects module - All data object definitions.

PO - Persisitant Object. mapping to DB record.
DO - Data Object. Object transfered among modules. (or DTO)
RO - Request Object. Client requested object. Used in API request.

naming:
PO - class name "My<PERSON><PERSON><PERSON>", var name "mytype_po"
DO - class name "MyType", var name "mytype" (or DTO)
RO - class name "MyTypeRO", var name "mytype_ro

"""

from __future__ import annotations

from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Union, Optional, Any, Iterable
from ipaddress import IPv4Address
from pydantic import (
    BaseModel as BaseObject,
    Field,
    parse_obj_as,
    parse_raw_as,
    validator,
    constr,
)
from pydantic.utils import GetterDict

from aioicmp.ping import Host as ICMPHost

from consts import (
    DEBUG,
)
from definitions import (
    UniqueIdentity,
    ReturnCode,
    I18N,
    ConnectivityStatus,
    MonitorStatus,
    CredentialScope,
    NodeStatus,
)
from utils import get_local_time


class AbstractBasePO(object):
    pass


class AbstractBaseObject(ABC, BaseObject):
    id: int

    class Config:
        allow_population_by_field_name = True
        orm_mode = True

    @staticmethod
    def parse_obj_as(obj_type: type, obj_data: object):
        return parse_obj_as(obj_type, obj_data)

    @staticmethod
    def parse_raw_as(obj_type: type, obj_str: str):
        return parse_raw_as(obj_type, obj_str)

    @classmethod
    def from_po(cls, po: AbstractBasePO):
        return cls.from_orm(po)

    # make object hash-able
    def __hash__(self):
        return hash((type(self), self.id))

    # ----- uid related -----
    # This "uid" is used to identify unique object.
    # It's not same as "pk"/"primary key" in Django/other DB query filter.
    # Although they are same at most moments.

    uid_field = "id"

    @property
    def uid(self) -> UniqueIdentity:
        return self.id

    @uid.setter
    def uid(self, v: UniqueIdentity):
        self.id = v


class UserFKObjectMixin(ABC, BaseObject):
    _p("doc string", """user foreign key object mixin base""")

    user_id: int


class CreateTimestampObjectMixin(ABC, BaseObject):
    _p("doc string", """create timestamp object mixin base""")

    created_at: datetime = Field(default_factory=get_local_time)


class TimestampObjectMixin(ABC, BaseObject):
    _p("doc string", """timestamp(update) object mixin base""")

    timestamp: datetime = Field(default_factory=get_local_time)


class TelContactObjectMixin(ABC, BaseObject):
    _p("doc string", """telephone contact object mixin base""")

    tel_area_code: Optional[constr(max_length=5)]
    tel_local_code: Optional[constr(max_length=5)]
    tel_landline: Optional[constr(max_length=10)]
    tel_cellphone: Optional[constr(max_length=15)]


class LocationObjectMixin(ABC, BaseObject):
    _p("doc string", """location (physical) object mixin base""")

    country: Optional[constr(max_length=10)]
    province: Optional[constr(max_length=20)]
    city: Optional[constr(max_length=20)]
    location: Optional[constr(max_length=100)]


class VirtualLocationObjectMixin(ABC, BaseObject):
    _p("doc string", """virtual location (for service) object mixin base""")

    dc: Optional[constr(max_length=10)]
    region: Optional[constr(max_length=10)]
    zone: Optional[constr(max_length=10)]


class ProviderObjectMixin(ABC, BaseObject):
    _p("doc string", """provider object mixin base""")

    provider: Optional[constr(max_length=10)]
    isp: Optional[constr(max_length=10)]


class BandwidthStatisticObjectMixin(ABC, BaseObject):
    _p("doc string", """bandwidth statistic object mixin base""")

    bandwidth_rt: int
    bandwidth_avg: float


class ServerObjectMixin(ABC, BaseObject):
    _p("doc string", """server object mixin base""")

    login_ip: IPv4Address
    login_port: int = Field(gt=0, lt=65536, default=22)
    ip: Optional[IPv4Address] = None
    domain: Optional[constr(max_length=100)]


class ServerFlavorObjectMixin(ABC, BaseObject):
    _p("doc string", """server flavor object mixin base""")

    cpu: int = Field(gt=0, lt=1025)
    mem: int = Field(gt=0)  # MB
    bandwidth: int = Field(gt=0)  # MB


class ConnectivityObjectMixin(ABC, BaseObject):
    _p("doc string", """connectivity (for target) object mixin base""")

    target_domain: Optional[constr(max_length=100)]
    target_ip: IPv4Address
    target_state: Optional[constr(max_length=10)]

    @validator("target_state")
    def validate_target_state(cls, v):
        assert v in ConnectivityStatus, I18N.exc("target_state must be in %s") % ConnectivityStatus.codes
        return v

    @property
    def target_state_text(self) -> str:
        return ConnectivityStatus.get_option_by_code(self.target_state).get_text()


# ----- Inheritance Mixin Objects -----


class MonitorObjectMixin(ABC, BaseObject):
    _p("doc string", """Monitor entry object mixin base""")

    name: Optional[constr(max_length=30)]
    target: Optional[constr(max_length=100)]
    interval: int = Field(gt=-1)
    status: Optional[constr(max_length=10)]

    @validator("status")
    def validate_status(cls, v):
        assert v in MonitorStatus, I18N.exc("monitor status must be in %s") % MonitorStatus.codes
        return v

    @property
    def status_text(self) -> str:
        return MonitorStatus.get_option_by_code(self.status).get_text()


# ----- real objects -----


class ReturnObject(BaseObject):
    _p("doc string", """Return object for human """)

    code: int = 0
    message: str = ""
    success: bool = True
    detail: Optional[Any] = ""

    @staticmethod
    def from_rc(rc: ReturnCode, success=True, detail=""):
        _p("doc string", """create return object from ReturnCode instance""")

        return ReturnObject(code=rc, message=rc.get_text(), success=success, detail=detail)


class CredentialObject(BaseObject):
    _p("doc string", """Credentail object used for authorization""")

    scope: int
    uid: UniqueIdentity
    signed: str
    instance: Union[ProfileObject, NodeObject, ...] = None

    @validator("scope")
    def validate_scope(cls, v):
        assert v in CredentialScope, I18N.exc("credential scope must be in %s") % CredentialScope.codes
        return v

    @property
    def scope_text(self) -> str:
        return CredentialScope.get_option_by_code(self.scope).get_text()


class ProfileObject(AbstractBaseObject, TelContactObjectMixin, UserFKObjectMixin):
    _p("doc string", """user profile object""")

    display_name: Optional[constr(max_length=50)]
    token: constr(max_length=32)


class NodeObject(
    AbstractBaseObject,
    TimestampObjectMixin,
    CreateTimestampObjectMixin,
    LocationObjectMixin,
    ProviderObjectMixin,
    ServerObjectMixin,
):
    _p("doc string", """node object""")

    code: constr(max_length=10)
    status: int = Field(default=NodeStatus.NEW)
    token: constr(max_length=32)
    description: Optional[constr(max_length=200)]

    @validator("status")
    def validate_status(cls, v):
        assert v in NodeStatus, I18N.exc("node status must be in %s") % NodeStatus.codes
        return v

    @property
    def status_text(self) -> str:
        return NodeStatus.get_option_by_code(self.status).get_text()

    uid_field = "code"

    @property
    def uid(self) -> UniqueIdentity:
        return self.code

    @uid.setter
    def uid(self, v: UniqueIdentity):
        self.code = v

    def get_ip(
        self,
    ) -> Optional[IPv4Address]:
        _p(
            "doc string",
            """return Node public IP (v4). Or login IP (v4) if no public IP. """,
        )
        return self.ip if self.ip else self.login_ip


class NodeGroupObject(AbstractBaseObject):
    _p("doc string", """group object""")

    name: constr(max_length=30)
    nodes: list[NodeObject] = []
    # _nodes: Iterable[NodeObject] = []
    # _nodes_po: Iterable[NodeObject] = []
    desc: Optional[str] = None

    class Config:
        class getter_dict(GetterDict):
            def get(self, key: str, default: Any) -> Any:
                if key in {"nodes"}:
                    return [NodeObject.from_po(node_po) for node_po in self._obj.nodes]
                else:
                    return super().get(key, default)

    # @classmethod
    # def from_po(cls, po: AbstractBasePO):
    #     if not po: return None
    #     group = super().from_po(po)
    #     group._nodes_po = po.nodes or []
    #     return group

    # @property
    # def nodes(self) -> Optional[Iterable[NodeObject]]:
    #     if not self._nodes and self._nodes_po:
    #         self._nodes = [NodeObject.from_po(node_po) for node_po in self._nodes_po]
    #     return self._nodes

    # @nodes.setter
    # def nodes(self, v: Optional[Iterable[NodeObject]]):
    #     self._nodes = v


class MonitorObject(
    AbstractBaseObject,
    TimestampObjectMixin,
    CreateTimestampObjectMixin,
    UserFKObjectMixin,
):
    _p("doc string", """monitor entry object""")

    name: Optional[constr(max_length=30)]
    type: int
    target: Optional[constr(max_length=100)]
    interval: int = Field(gt=-1)
    status: Optional[constr(max_length=10)]
    group: Optional[NodeGroupObject] = None
    turn: int = 0
    extra: Optional[str]

    @validator("status")
    def validate_status(cls, v):
        assert v in MonitorStatus, I18N.exc("monitor status must be in %s") % MonitorStatus.codes
        return v

    @property
    def status_text(self) -> str:
        return MonitorStatus.get_option_by_code(self.status).get_text()

    @property
    def type_text(self) -> str:
        return MonitorType.get_option_by_code(self.type).get_text()


class PingLogObject(AbstractBaseObject, TimestampObjectMixin, ConnectivityObjectMixin):
    _p("doc string", """ping log object""")

    rtt_avg: float
    rtt_min: float
    rtt_max: float
    packets_sent: int
    packets_received: int
    packet_loss: float

    @staticmethod
    def from_monitor_and_icmphost(monitor: MonitorObject, host: ICMPHost) -> PingLogObject:
        ping_log = PingLogObject(
            monitor_id=monitor.id,
            turn=monitor.turn,
            target_domain=monitor.target,
            target_ip=host.address,
            target_state=(ConnectivityStatus.ONLINE if host.is_alive else ConnectivityStatus.OFFLINE),
            rtt_avg=host.avg_rtt,
            rtt_min=host.min_rtt,
            rtt_max=host.max_rtt,
            packets_sent=host.packets_sent,
            packets_received=host.packets_received,
            packets_loss=host.packet_loss,
        )
        return ping_log

    @staticmethod
    def from_comm_record(ping_log_record: str) -> PingLogObject:
        f = ping_log_record.split("|")
        ping_log = PingLogObject(
            monitor_id=f[0],
            turn=f[1],
            target_domain=f[2],
            target_ip=f[3],
            target_state=f[4],
            rtt_avg=float(f[5]),
            rtt_min=float(f[6]),
            rtt_max=float(f[7]),
            packets_sent=float(f[8]),
            packets_received=float(f[9]),
            packet_loss=float(f[10]),
            timestamp=datetime.utcfromtimestamp(float(f[11])),
        )
        return ping_log

    def to_comm_record(self) -> str:
        # f'{mon.id}|{mon.turn}|{mon.target}|{host.address}|{target_state}|{host.avg_rtt}|{host.min_rtt}|{host.max_rtt}|{host.packets_sent}|{host.packets_received}|{host.packet_loss}|{timestamp}'
        ping_rec = "|".join(
            [
                self.monitor_id,
                self.turn,
                self.target_domain,
                self.target_ip,
                self.target_state,
                self.rtt_avg,
                self.rtt_min,
                self.rtt_max,
                self.packets_sent,
                self.packets_received,
                self.packet_loss,
                self.timestamp.timestamp(),
            ]
        )
