from abc import abstractmethod, <PERSON>
from datetime import datetime, UTC
from io import BufferedRead<PERSON>
from logging import getLogger
from pathlib import Path
from typing import TypeVar, Any, Protocol

from asgiref.sync import sync_to_async

StoreKeyT = TypeVar("StoreKeyT")
StoreDataT = TypeVar("StoreDataT")

logger = getLogger(__name__)


class Storable(Protocol[StoreKeyT, StoreDataT]):
    """Protocol for object that can be stored"""

    @abstractmethod
    def get_storable_key(self) -> StoreKeyT:
        """return the key of data to be stored in storage"""

    @abstractmethod
    def get_storable_data(self) -> StoreDataT:
        """return the data to be stored in storage"""

    @abstractmethod
    def set_storable_data(self, data: StoreDataT) -> None:
        """set the data of the storable object"""


class SupportsStorable(Protocol):
    """Protocol for object that supports to operate on Storable objects such as persist and load."""

    @abstractmethod
    def persist(self, data: Storable) -> None:
        """persist `storable` data into storage"""

    @abstractmethod
    def load(self, data: Storable) -> None:
        """load `storable` data from storage"""


class StoreLike(Protocol[StoreKeyT, StoreDataT]):
    """Protocol for object supports operations like a storage"""

    @abstractmethod
    def exist(self, key: StoreKeyT) -> bool:
        """check if a object is existed in store by given key"""

    @abstractmethod
    def get_data(self, key: StoreKeyT) -> StoreDataT:
        """directly download data from store by key"""

    @abstractmethod
    def put_data(self, key: StoreKeyT, data: StoreDataT) -> None:
        """directly upload data to store for key"""

    @abstractmethod
    def get_data_reader(self, key: StoreKeyT) -> BufferedReader | None:
        """ger a streaming reader to data"""

    @abstractmethod
    def put_buffer_data(self, key: str, buffer: BufferedReader) -> None:
        """put streaming data from buffer to store"""

    @abstractmethod
    def del_data(self, key: StoreKeyT) -> bool:
        """delete data from store by key"""


class Lockable(Protocol[StoreKeyT]):
    """Protocol for object supports lock operations"""

    @abstractmethod
    def lock(self, key: StoreKeyT) -> bool:
        """create a lock on store"""

    @abstractmethod
    def unlock(self, key: StoreKeyT) -> bool:
        """delete a lock on store"""

    @abstractmethod
    def is_locked(self, key: StoreKeyT) -> bool:
        """check if a lock of the key is existed"""


class Store(SupportsStorable, StoreLike[StoreKeyT, StoreDataT], Lockable[StoreKeyT], ABC):
    def persist(self, data: Storable) -> None:
        """persist `storable` data into storage"""
        key = data.get_storable_key()
        value = data.get_storable_data()
        self.put_data(key, value)

    async def apersist(self, data: Storable) -> None:
        """persist `storable` data into storage"""
        return await sync_to_async(self.persist)(data)

    def load(self, data: Storable) -> None:
        """load `storable` data from storage"""
        key = data.get_storable_key()
        value = self.get_data(key)
        data.set_storable_data(value)

    async def aload(self, data: Storable) -> None:
        """load `storable` data from storage"""
        return await sync_to_async(self.load)(data)

    @abstractmethod
    def exist(self, key: StoreKeyT) -> bool:
        """check if a object is existed in store by given key"""

    async def aexist(self, key: StoreKeyT) -> bool:
        """check if a object is existed in store by given key"""
        return await sync_to_async(self.exist)(key)

    @abstractmethod
    def get_data_reader(self, key: StoreKeyT) -> BufferedReader | None:
        """ger a streaming reader to data"""

    @abstractmethod
    def put_buffer_data(self, key: str, buffer: BufferedReader) -> None:
        """streaming store data"""

    async def aput_data_from_buffer(self, key: str, buffer: BufferedReader) -> None:
        """streaming store data"""
        return await sync_to_async(self.put_buffer_data)(key, buffer)

    @abstractmethod
    def get_data(self, key: StoreKeyT) -> StoreDataT:
        """directly download data from store by key"""

    async def aget_data(self, key: StoreKeyT) -> StoreDataT:
        """directly download data from store by key"""
        return await sync_to_async(self.get_data)(key)

    @abstractmethod
    def put_data(self, key: StoreKeyT, data: StoreDataT) -> None:
        """directly upload data to store for key"""

    async def aput_data(self, key: StoreKeyT, data: StoreDataT) -> None:
        """directly upload data to store for key"""
        return await sync_to_async(self.put_data)(key, data)

    @abstractmethod
    def del_data(self, key: StoreKeyT) -> bool:
        """delete data from store by key"""

    async def adel_data(self, key: StoreKeyT) -> bool:
        """delete data from store by key"""
        return await sync_to_async(self.del_data)(key)

    # TODO(data): make lock related to use redis.

    @abstractmethod
    def lock(self, key: StoreKeyT) -> bool:
        """create a lock on store"""

    async def alock(self, key: StoreKeyT) -> bool:
        return await sync_to_async(self.lock)(key)

    @abstractmethod
    def unlock(self, key: StoreKeyT) -> bool:
        """delete a lock on store"""

    async def aunlock(self, key: StoreKeyT) -> bool:
        return await sync_to_async(self.unlock)(key)

    def is_locked(self, key: StoreKeyT) -> bool:
        """check if a lock of the key is existed"""
        return self.exist(key)

    async def ais_locked(self, key: StoreKeyT) -> bool:
        return await self.aexist(key)


# =====  S3 Store  =====
class S3Store(Store[str, Any]):
    """S3 like store with key of string type"""

    bucket: str = ""
    prefix: str = ""

    __s3cli = None

    @property
    def s3(self) -> Any:
        if not self.__s3cli:
            # avoid none-s3-store to import
            from services.s3 import get_s3_client

            self.__s3cli = get_s3_client()
        return self.__s3cli

    def get_full_key(self, key: str) -> str:

        return "/".join(
            [
                self.prefix.removeprefix("/").removesuffix("/"),
                key.removeprefix("/").removesuffix("/"),
            ]
        )

    def exist(self, key: str) -> bool:
        key = self.get_full_key(key)
        return self.s3.exists_object(self.bucket, key)

    def get_data_reader(self, key: str) -> BufferedReader | None:
        key = self.get_full_key(key)
        return self.s3.get_object_reader(self.bucket, key)

    def get_data(self, key: str) -> Any:
        key = self.get_full_key(key)
        return self.s3.get_object(self.bucket, key)

    def put_buffer_data(self, key: str, buffer: BufferedReader) -> None:
        key = self.get_full_key(key)
        self.s3.put_object_from_buffer(self.bucket, key, buffer)

    def put_data(self, key: str, data: Any) -> None:
        key = self.get_full_key(key)
        self.s3.put_object(self.bucket, key, data)

    def del_data(self, key: str) -> bool:
        key = self.get_full_key(key)
        return self.s3.del_object(self.bucket, key)

    def lock(self, key: str) -> bool:
        key = self.get_full_key(key)
        if self.exist(key):
            logger.info(f"{self.__class__.__name__} {key} was locked.")
            return False
        try:
            self.s3.put_str_object(self.bucket, key, datetime.now(tz=UTC).isoformat())
            # TODO(data): expire lock
            return True
        except Exception as e:
            logger.error(e)
            return False

    def unlock(self, key: str) -> bool:
        key = self.get_full_key(key)
        try:
            self.del_data(key)
            return True
        except Exception as e:
            logger.error(e)
            return False

    def gen_secret_put_url(
        self,
        key: str,
        content_type: str = "",
        expiration: int = 900,
    ) -> str:
        key = self.get_full_key(key)
        put_url = self.s3.gen_pre_signed_url_put(self.bucket, key, content_type=content_type, expiration=expiration)
        return put_url

    async def agen_secret_put_url(
        self,
        key: str,
        content_type: str = "",
        expiration: int = 900,
    ) -> str:
        return await sync_to_async(self.gen_secret_put_url)(key, content_type, expiration)

    def gen_secret_get_url(
        self,
        key: str,
        content_type: str = "",
        expiration: int = 1800,
    ) -> str:
        key = self.get_full_key(key)
        get_url = self.s3.gen_pre_signed_url_get(self.bucket, key, content_type=content_type, expiration=expiration)
        return get_url

    async def agen_secret_get_url(
        self,
        key: str,
        content_type: str = "",
        expiration: int = 900,
    ) -> str:
        return await sync_to_async(self.gen_secret_get_url)(key, content_type, expiration)


class StrS3Store(S3Store):
    """S3 store with string type data"""

    def get_data(self, key: str) -> str:
        key = self.get_full_key(key)
        value = self.s3.get_str_object(self.bucket, key)
        return value

    def put_data(self, key: str, data: str) -> None:
        key = self.get_full_key(key)
        self.s3.put_str_object(self.bucket, key, data)


# =====  File Store  =====


class FileStore(Store[str, bytes]):
    """File system store with string type key"""

    root_path: str = ""
    prefix: str = ""

    def __init__(self, root_path: str, prefix: str = ""):
        super().__init__()
        assert root_path
        self.root_path = root_path
        self.prefix = prefix
        p = self.get_full_path("")
        p.mkdir(parents=True, exist_ok=True)

    def get_full_path(self, key: str) -> Path:
        return Path(self.root_path) / self.prefix.removeprefix("/") / key.removeprefix("/")

    def exist(self, key: str) -> bool:
        p = self.get_full_path(key)
        return p.exists()

    def get_data_reader(self, key: str) -> BufferedReader | None:
        p = self.get_full_path(key)
        buffer = p.open("rb")
        return buffer

    def get_data(self, key: str) -> bytes:
        p = self.get_full_path(key)
        return p.read_bytes()

    def put_buffer_data(self, key: str, buffer: BufferedReader) -> None:
        p = self.get_full_path(key)
        p.write_bytes(buffer.read())

    def put_data(self, key: str, data: bytes) -> None:
        p = self.get_full_path(key)
        p.write_bytes(data)

    def del_data(self, key: str) -> bool:
        p = self.get_full_path(key)
        if p.is_dir():
            p.rmdir()
        else:
            p.unlink()
        return True

    def lock(self, key: str) -> bool:
        if self.exist(key):
            logger.info(f"{self.__class__.__name__} {key} was locked.")
            return False
        try:
            self.put_data(key, datetime.now(tz=UTC).isoformat().encode())
            # TODO(data): expire lock
            return True
        except Exception as e:
            logger.error(e)
            return False

    def unlock(self, key: str) -> bool:
        try:
            self.del_data(key)
            return True
        except Exception as e:
            logger.error(e)
            return False
