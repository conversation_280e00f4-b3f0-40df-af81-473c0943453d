import logging
import traceback
from abc import ABC, abstractmethod
from logging import get<PERSON>ogger
from typing import overload, Iterable, Self, Any, Protocol, ClassVar, runtime_checkable

from .store import StoreLike
from .utils.lang import json_dumps, DictMixin
from .utils.strutil import slugify

logger = getLogger(__name__)


# =====  Notification =====


class Notification(DictMixin):
    """The entity to be notified"""

    # what message is being notified
    message: str

    # data attached to this message
    data: Any = None

    # which subjects will this notification be sent into.
    subjects: set[str] = set()

    # who send this notification
    sender: str = ""

    # which line of code is the notification sent from. for logging, trace purpose.
    loc: str = ""

    def __init__(
        self,
        message: str,
        data: Any = None,
        subjects: Iterable[str] = (),
        sender: str = "",
        loc: str = "",
    ):
        self.message = message
        self.data = data
        self.subjects = set(subjects)
        self.sender = sender
        self.loc = loc

    def __str__(self) -> str:
        sender_name = self.sender or "unknown"
        return (
            f'<{self.__class__.__name__} "{self.message}", sender="{sender_name}", '
            f"subjects={self.subjects}, data={json_dumps(self.data)}>"
        )

    def to(self, *subjects: str) -> Self:
        """add extrac subject to be sent into to this notification"""
        self.subjects.update(subjects)
        return self


# =====  Observer =====


@runtime_checkable
class Observer(Protocol):
    """Protocol for object supports to observe data."""

    @abstractmethod
    def observe(self, note: Notification) -> None:
        """to observe a notification depends on its subject"""

    @classmethod
    @abstractmethod
    def get_subject(cls) -> str:
        """the subject of the observer."""


class ObserverBase(Observer, ABC):
    """Abstract base Observer which implements Observable"""

    def observe(self, note: Notification) -> None:
        """to observe a notification depends on its subject"""
        return self._observe(note)

    @abstractmethod
    def _observe(self, note: Notification) -> None:
        """subclass implementation for specific subject notification"""

    @staticmethod
    def collect_all_subjects() -> set[str]:
        subjects: set[str] = set()
        for cls in Observer.__subclasses__():
            if hasattr(cls, "get_subject"):
                subjects.add(cls.get_subject())
        return subjects

    def _mask_sensitive_message(self, message: Any) -> Any:
        # TODO: use regular expression ?
        sensitive_keys = {
            "api_key",
            "api_token",
            "token",
            "authorization",
            "password",
            "passwd",
            "credential",
            "credentials",
        }

        def __inner_mask(_msg: dict | object) -> dict:
            _d = {}
            _changed = False
            if not isinstance(_msg, dict):
                _msg = _msg.__dict__
            for k, v in _msg.items():
                if k.lower() in sensitive_keys:
                    _d[k] = "********"
                    _changed = True
                else:
                    _d[k] = v
            if not _changed:
                return _msg
            return _d

        if isinstance(message, list):
            new_data = []
            for d in message:
                new_data.append(self._mask_sensitive_message(d))
        # elif isinstance(data, set):
        #     new_data = {}
        #     for d in data:
        #         new_data.add(self._mask_sensitive_data(d))
        else:
            new_data = __inner_mask(message)
        return new_data


@runtime_checkable
class Observable(Protocol):
    """Protocol for object that can be observed"""

    @abstractmethod
    def register_observer(self, *observers: Observer) -> None:
        """register observers to current object"""

    @abstractmethod
    def unregister_observer(self, *observers: Observer) -> None:
        """unregister observers from current object"""

    @abstractmethod
    def pop_observer(self) -> Observer:
        """pop up last registered observer"""

    @abstractmethod
    def clean_observers(self) -> None:
        """clean all registered observers"""

    @overload
    def observe(self, note: Notification) -> None:
        """observe a notification by all subjected observers"""

    @overload
    def observe(
        self,
        message: str,
        data: Any = None,
        subjects: Iterable[str] = (),
        sender: str = "",
        loc: str = "",
    ) -> None:
        """overloaded implements"""

    @abstractmethod
    def observe(self, *args: Any, **kwargs: Any) -> None:
        """"""


class ObservableBase(Observable, ABC):
    """abstract base for object wants to be observed"""

    # observers must be ordered
    observers: list[Observer] = []
    __max_observers: ClassVar[int] = 10

    def register_observer(self, *observers: Observer) -> None:
        for observer in observers:
            if len(self.observers) > self.__max_observers:
                logger.warning(f"reach max observer count {self.__max_observers}")
                return
            if observer in self.observers:
                logger.warning(f"observer {observer} already registered")
                continue
            self.observers.append(observer)

    def unregister_observer(self, *observers: Observer) -> None:
        for observer in observers:
            try:
                self.observers.remove(observer)
            except Exception:
                pass

    def pop_observer(self) -> Observer:
        return self.observers.pop()

    def clean_observers(self) -> None:
        self.observers.clear()

    def observe(self, *args: Any, **kwargs: Any) -> None:
        """overloaded implements"""

        if len(args) == 1 and isinstance(args[0], Notification):
            note = args[0]
        elif len(args) == 0 and "note" in kwargs and isinstance(kwargs["note"], Notification):
            note = kwargs["note"]
        else:
            arg_names = ["message", "data", "subjects", "sender", "loc"]
            d = {}
            for i, arg in enumerate(args):
                k = arg_names[i]
                d[k] = arg

            for k in set(arg_names) - set(d.keys()):
                v = kwargs.get(k)
                if v:
                    d[k] = v

            note = Notification(**d)

        if not note.sender:
            note.sender = self.name_observable

        if not note.loc:
            stack = traceback.StackSummary.extract(traceback.walk_stack(None), limit=2, capture_locals=True)
            t = stack[1]
            note.loc = f"{t.filename}:{t.lineno}"  # .{t.name}

        for observer in self.observers:
            if not note.subjects or observer.get_subject() in note.subjects:
                observer.observe(note)

    @property
    def name_observable(self) -> str:
        """the name be used by observer to identify the sender"""
        return str(self)


class ObserverManager(ObservableBase):

    def __init__(self, *ob: Observer) -> None:
        self.register_observer(*ob)


# =====  Predefined =====


class LogObserver(ObserverBase, ABC):
    """to observe data as log."""

    @classmethod
    def get_subject(cls) -> str:
        return "log"


class CostObserver(ObserverBase, ABC):
    """to observe data as costs"""

    @classmethod
    def get_subject(cls) -> str:
        return "cost"


class PerfObserver(ObserverBase, ABC):
    """to observe data as performance"""

    @classmethod
    def get_subject(cls) -> str:
        return "perf"


class StatObserver(ObserverBase, ABC):
    """to observe data as states"""

    @classmethod
    def get_subject(cls) -> str:
        return "stat"


class StoreObserver(ObserverBase):
    """to store observed data"""

    __store: StoreLike

    @classmethod
    def get_subject(cls) -> str:
        return "store"

    def __init__(self, store: StoreLike) -> None:
        super().__init__()
        self.__store = store

    @property
    def store(self) -> StoreLike:
        return self.__store

    def _observe(self, note: Notification) -> None:
        key = slugify(note.message)
        value = note.to_json().encode()
        self.store.put_data(key, value)


class PythonLogObserver(LogObserver):
    """to output message or data with python logging as specified logging level"""

    _level = logging.INFO
    __name_to_level: ClassVar[dict[str, int]] = logging.getLevelNamesMapping()
    __levels: ClassVar[set[int]] = set(__name_to_level.values())

    def __init__(self, *args: Any, level: str | int | None = None, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)

        self._level = self._get_level(level) or logging.INFO

    @staticmethod
    def _get_level(level: str | int | None) -> int | None:
        """get integer level value from string or integer"""
        if level is None:
            return None

        lvl = PythonLogObserver.__name_to_level.get(level.upper()) if isinstance(level, str) else level
        if lvl not in PythonLogObserver.__levels:
            logger.warning(f"{level} is not available logging level.")
            return None
        return lvl

    def _observe(self, note: Notification) -> None:
        # level = (
        #     self._get_level(
        #         getattr(note.data, "level", None)
        #         or (note.data.get("level") if isinstance(note.data, dict) else note.data)
        #     )
        #     or self._level
        # )
        logger.log(level=level, msg=f"{'<'+note.loc+'>' if note.loc else ''} {note.message}")


class TerminalLogObserver(LogObserver):
    """to output message or data with terminal stdout/stderr
    "level" is from "data"
    """

    def _observe(self, note: Notification) -> None:
        try:
            from pycommon.utils import terminal

            level = getattr(note.data, "level", "") or (
                note.data.get("level") if isinstance(note.data, dict) else "info"
            )
            func_name = str(level)
            func = getattr(terminal, func_name, print)
        except Exception as e:
            logger.debug(e)
            func = print

        func(note.message)


ALL_OBSERVER_SUBJECTS_SET = ObserverBase.collect_all_subjects()
