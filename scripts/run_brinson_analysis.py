#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Run Brinson Attribution Analysis

This script uses data collected from Financial Model Prep API to perform
Brinson attribution analysis using GBS's BrinsonAttributionAnalyzer.

Usage:
    python run_brinson_analysis.py --data_dir gbs/data/brinson --output_dir gbs/data/brinson/reports

Note:
    To collect data first, use the FMP data collector:
    cd scripts/data_collector/fmp && python collector.py download_data --api_key YOUR_API_KEY --portfolio AAPL,MSFT,GOOGL,AMZN,META --source_dir /home/<USER>/gold-beast-system/gbs/data/brinson
"""

import os
import argparse
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from datetime import datetime
import glob

# Import GBS's BrinsonAttributionAnalyzer
from gbs.eval_system.analysis import BrinsonAttributionAnalyzer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_latest_files(data_dir):
    """
    Get the latest data files from the data directory.

    Parameters
    ----------
    data_dir : str
        Directory containing the data files

    Returns
    -------
    dict
        Dictionary with paths to the latest data files
    """
    data_dir = Path(data_dir)

    # Find the latest files for each data type
    latest_files = {}

    for data_type in ['prices', 'sectors', 'benchmark', 'portfolio']:
        pattern = f"{data_type}_*.csv"
        files = sorted(data_dir.glob(pattern))

        if not files:
            logger.error(f"No {data_type} files found in {data_dir}")
            return None

        latest_files[data_type] = files[-1]
        logger.info(f"Using {latest_files[data_type]} for {data_type}")

    return latest_files

def prepare_brinson_data(data_files, analysis_date=None):
    """
    Prepare data for Brinson attribution analysis.

    Parameters
    ----------
    data_files : dict
        Dictionary with paths to the data files
    analysis_date : str, optional
        Date for the analysis in format 'YYYY-MM-DD'. If None, the latest date is used.

    Returns
    -------
    pandas.DataFrame
        DataFrame prepared for Brinson analysis
    """
    # Load data files
    prices_df = pd.read_csv(data_files['prices'])
    sectors_df = pd.read_csv(data_files['sectors'])
    benchmark_df = pd.read_csv(data_files['benchmark'])
    portfolio_df = pd.read_csv(data_files['portfolio'])

    # Convert date column to datetime
    prices_df['date'] = pd.to_datetime(prices_df['date'])

    # If analysis_date is not provided, use the latest date
    if analysis_date is None:
        analysis_date = prices_df['date'].max()
    else:
        analysis_date = pd.to_datetime(analysis_date)

    logger.info(f"Preparing Brinson data for date: {analysis_date}")

    # Filter prices for the analysis date
    prices_on_date = prices_df[prices_df['date'] == analysis_date]

    if prices_on_date.empty:
        logger.error(f"No price data found for date: {analysis_date}")
        return None

    # Create a DataFrame for Brinson analysis
    brinson_data = []

    # Add portfolio data
    for _, row in portfolio_df.iterrows():
        symbol = row['symbol']
        price_row = prices_on_date[prices_on_date['symbol'] == symbol]

        if not price_row.empty:
            brinson_data.append({
                'date': analysis_date,
                'symbol': symbol,
                'sector': row['sector'],
                'w_portfolio': row['weight'],
                'r_portfolio': price_row['return'].values[0],
                'w_benchmark': 0.0,  # Will be updated for benchmark symbols
                'r_benchmark': 0.0   # Will be updated for benchmark symbols
            })

    # Add benchmark data
    for _, row in benchmark_df.iterrows():
        symbol = row['symbol']
        price_row = prices_on_date[prices_on_date['symbol'] == symbol]

        if not price_row.empty:
            # Check if symbol is already in brinson_data (part of portfolio)
            existing_row = next((item for item in brinson_data if item['symbol'] == symbol), None)

            if existing_row:
                # Update existing row with benchmark data
                existing_row['w_benchmark'] = row['weight']
                existing_row['r_benchmark'] = price_row['return'].values[0]
            else:
                # Add new row for benchmark-only symbol
                sector_row = sectors_df[sectors_df['symbol'] == symbol]
                sector = sector_row['sector'].values[0] if not sector_row.empty else 'Unknown'

                brinson_data.append({
                    'date': analysis_date,
                    'symbol': symbol,
                    'sector': sector,
                    'w_portfolio': 0.0,  # Not in portfolio
                    'r_portfolio': 0.0,   # Not in portfolio
                    'w_benchmark': row['weight'],
                    'r_benchmark': price_row['return'].values[0]
                })

    # Create DataFrame
    brinson_df = pd.DataFrame(brinson_data)

    # Handle NaN values
    brinson_df = brinson_df.fillna(0)

    return brinson_df

def run_brinson_analysis(brinson_df, output_dir):
    """
    Run Brinson attribution analysis.

    Parameters
    ----------
    brinson_df : pandas.DataFrame
        DataFrame prepared for Brinson analysis
    output_dir : str
        Directory to save the analysis results

    Returns
    -------
    str
        Path to the generated report
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info("Running Brinson attribution analysis")

    # Create BrinsonAttributionAnalyzer
    analyzer = BrinsonAttributionAnalyzer()

    # Load data
    analyzer.load_data_from_dataframe(brinson_df)

    # Calculate attribution results
    attribution_results = analyzer.calculate_attribution()
    logger.info(f"Attribution results: {attribution_results}")

    # Calculate sector attribution
    sector_attribution = analyzer.calculate_sector_attribution()
    logger.info(f"Sector attribution calculated")

    # Calculate period attribution
    period_attribution = analyzer.calculate_period_attribution()
    logger.info(f"Period attribution calculated")

    # Generate report
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_title = f"Brinson Attribution Analysis - {timestamp}"
    report_path = analyzer.create_report(
        output_dir=str(output_dir),
        title=report_title
    )

    logger.info(f"Report generated: {report_path}")

    # Save attribution results
    # Convert attribution_results to a proper DataFrame if it's not already
    if isinstance(attribution_results, pd.DataFrame):
        attribution_results.to_csv(output_dir / f"attribution_results_{timestamp}.csv", index=False)
    else:
        logger.warning(f"Attribution results not saved: unexpected type {type(attribution_results)}")

    # Save sector attribution
    sector_attribution.to_csv(output_dir / f"sector_attribution_{timestamp}.csv", index=False)

    # Save period attribution
    period_attribution.to_csv(output_dir / f"period_attribution_{timestamp}.csv", index=False)

    return report_path

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run Brinson attribution analysis')
    parser.add_argument('--data_dir', default='gbs/data/brinson', help='Directory containing the data files')
    parser.add_argument('--output_dir', default='gbs/data/brinson/reports', help='Directory to save the analysis results')
    parser.add_argument('--analysis_date', help='Date for the analysis in format YYYY-MM-DD')

    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()

    # Get the latest data files
    data_files = get_latest_files(args.data_dir)

    if not data_files:
        logger.error("Failed to get data files")
        return

    # Prepare data for Brinson analysis
    brinson_df = prepare_brinson_data(data_files, args.analysis_date)

    if brinson_df is None or brinson_df.empty:
        logger.error("Failed to prepare data for Brinson analysis")
        return

    # Run Brinson analysis
    report_path = run_brinson_analysis(brinson_df, args.output_dir)

    if report_path:
        logger.info(f"Brinson analysis completed successfully. Report: {report_path}")
    else:
        logger.error("Brinson analysis failed")

if __name__ == "__main__":
    main()
