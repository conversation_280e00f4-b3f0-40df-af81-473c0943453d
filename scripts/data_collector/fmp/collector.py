#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FMP Data Collector

This module provides functionality to collect data from Financial Model Prep API
for financial analysis, including Brinson attribution analysis.

Usage:
    python collector.py download_data --api_key YOUR_API_KEY --portfolio AAPL,MSFT,GOOGL,AMZN,META
    python collector.py normalize_data --source_dir source --normalize_dir normalize
"""

import os
import sys
import argparse
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import logging
import time
import concurrent.futures

# Add parent directory to path to import base classes
CUR_DIR = Path(__file__).resolve().parent
sys.path.append(str(CUR_DIR.parent.parent))

# Import base classes
from data_collector.base import BaseCollector, BaseNormalize, BaseRun, Normalize

# Add GBS to Python path
sys.path.insert(0, "/home/<USER>/gold-beast-system")

# Import GBS utilities
from gbs.data_system.base.utils import code_to_fname
from gbs.core.utils.loki_logger import get_loki_logger

# Set up logging
logger = get_loki_logger(__name__).logger

class FMPCollector(BaseCollector):
    """
    Class for collecting data from Financial Model Prep API for financial analysis.
    """

    BASE_URL = "https://financialmodelingprep.com/api/v3"

    def __init__(
        self,
        save_dir: [str, Path],
        api_key: str,
        start=None,
        end=None,
        interval="1d",
        max_workers=1,
        max_collector_count=2,
        delay=0.5,
        check_data_length: int = None,
        limit_nums: int = None,
        portfolio_symbols=None,
        benchmark="S&P 500",
        portfolio_weights=None,
    ):
        """
        Initialize the FMP data collector.

        Parameters
        ----------
        save_dir : str or Path
            Directory to save the collected data
        api_key : str
            Your Financial Model Prep API key
        start : str, optional
            Start date in format 'YYYY-MM-DD'
        end : str, optional
            End date in format 'YYYY-MM-DD'
        interval : str
            Data interval, default "1d"
        max_workers : int
            Maximum number of workers for concurrent downloads, default 1
        max_collector_count : int
            Maximum number of collection attempts, default 2
        delay : float
            Delay between API requests, default 0.5
        check_data_length : int, optional
            Check data length, default None
        limit_nums : int, optional
            Limit number of symbols to process, default None
        portfolio_symbols : list, optional
            List of portfolio symbols, default None
        benchmark : str
            Benchmark index name, default "S&P 500"
        portfolio_weights : dict, optional
            Dictionary mapping symbols to weights, default None
        """
        # Initialize attributes before calling super().__init__
        self.api_key = api_key
        self.portfolio_symbols = portfolio_symbols or []
        self.benchmark = benchmark
        self.portfolio_weights = portfolio_weights

        super(FMPCollector, self).__init__(
            save_dir=save_dir,
            start=start,
            end=end,
            interval=interval,
            max_workers=max_workers,
            max_collector_count=max_collector_count,
            delay=delay,
            check_data_length=check_data_length,
            limit_nums=limit_nums,
        )

    def get_instrument_list(self):
        """
        Get the list of instruments to collect data for.

        Returns
        -------
        list
            List of instrument symbols
        """
        # 如果提供了portfolio_symbols，只下载这些符号，不下载benchmark成分股
        if self.portfolio_symbols:
            logger.info(f"Using provided portfolio symbols: {self.portfolio_symbols}")
            return self.portfolio_symbols

        # 如果没有提供portfolio_symbols，则下载benchmark成分股
        logger.warning("No portfolio symbols provided, using benchmark constituents only")
        benchmark_df = self.get_benchmark_constituents(self.benchmark)
        if benchmark_df is None:
            return []
        return benchmark_df['symbol'].tolist()

    def normalize_symbol(self, symbol):
        """
        Normalize the symbol for file naming.

        Parameters
        ----------
        symbol : str
            Symbol to normalize

        Returns
        -------
        str
            Normalized symbol
        """
        return symbol

    def get_data(
        self, symbol: str, interval: str, start_datetime: pd.Timestamp, end_datetime: pd.Timestamp
    ) -> pd.DataFrame:
        """
        Get data for a specific symbol.

        Parameters
        ----------
        symbol : str
            Symbol to get data for
        interval : str
            Data interval
        start_datetime : pd.Timestamp
            Start date
        end_datetime : pd.Timestamp
            End date

        Returns
        -------
        pandas.DataFrame
            DataFrame with historical data
        """
        logger.info(f"Getting data for {symbol} from {start_datetime} to {end_datetime}")

        # Convert timestamps to string format
        start_date = start_datetime.strftime('%Y-%m-%d')
        end_date = end_datetime.strftime('%Y-%m-%d')

        # Get historical prices
        endpoint = f"historical-price-full/{symbol}"
        params = {
            'from': start_date,
            'to': end_date
        }

        response = self._make_request(endpoint, params)

        if not response or 'historical' not in response:
            logger.warning(f"No data found for {symbol}")
            return pd.DataFrame()

        # Create DataFrame from response
        df = pd.DataFrame(response['historical'])
        df['symbol'] = symbol

        # Convert date column to datetime
        df['date'] = pd.to_datetime(df['date'])

        # Calculate daily returns
        df['return'] = df['close'].pct_change()

        # Get sector classification
        sector_info = self.get_sector_classification(symbol)
        if sector_info:
            df['sector'] = sector_info.get('sector', 'Unknown')
            df['industry'] = sector_info.get('industry', 'Unknown')

        return df

    def _make_request(self, endpoint, params=None):
        """
        Make a request to the FMP API.

        Parameters
        ----------
        endpoint : str
            API endpoint to request
        params : dict, optional
            Additional parameters for the request

        Returns
        -------
        dict or list
            JSON response from the API
        """
        if params is None:
            params = {}

        params['apikey'] = self.api_key
        url = f"{self.BASE_URL}/{endpoint}"

        try:
            response = requests.get(url, params=params)
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error making request to {url}: {e}")
            return None

    def get_sector_classification(self, symbol):
        """
        Get sector classification for a symbol.

        Parameters
        ----------
        symbol : str
            Symbol to get sector classification for

        Returns
        -------
        dict
            Dictionary with sector information
        """
        endpoint = f"profile/{symbol}"
        response = self._make_request(endpoint)

        if response and isinstance(response, list) and len(response) > 0:
            return {
                'sector': response[0].get('sector', 'Unknown'),
                'industry': response[0].get('industry', 'Unknown')
            }

        return None

    def get_benchmark_constituents(self, benchmark="S&P 500"):
        """
        Get constituents of a benchmark index.

        Parameters
        ----------
        benchmark : str
            Name of the benchmark index. Options: "S&P 500", "NASDAQ", "DOW"

        Returns
        -------
        pandas.DataFrame
            DataFrame with benchmark constituents
        """
        logger.info(f"Getting constituents for {benchmark}")

        if benchmark == "S&P 500":
            endpoint = "sp500_constituent"
        elif benchmark == "NASDAQ":
            endpoint = "nasdaq_constituent"
        elif benchmark == "DOW":
            endpoint = "dowjones_constituent"
        else:
            logger.error(f"Unsupported benchmark: {benchmark}")
            return None

        response = self._make_request(endpoint)

        if not response:
            logger.error(f"No constituent data found for {benchmark}")
            return None

        # Create DataFrame from constituent data
        constituents_df = pd.DataFrame(response)

        # FMP API doesn't provide weights, so we'll create equal weights
        constituents_df['weight'] = 1.0 / len(constituents_df)

        return constituents_df

    def get_sector_classification(self, symbol):
        """
        Get sector and industry classification for a symbol.

        Parameters
        ----------
        symbol : str
            Stock symbol

        Returns
        -------
        dict
            Dictionary containing sector and industry information
        """
        endpoint = f"profile/{symbol}"

        response = self._make_request(endpoint)

        if not response or len(response) == 0:
            logger.warning(f"No profile data found for {symbol}")
            return {"sector": "Unknown", "industry": "Unknown"}

        # Extract sector and industry information
        profile = response[0]
        sector = profile.get("sector", "Unknown")
        industry = profile.get("industry", "Unknown")

        return {"sector": sector, "industry": industry}

    def download_index_data(self):
        """
        Download index data.
        This method is required by BaseCollector but not used for FMP.
        """
        pass


class FMPNormalize(BaseNormalize):
    """
    Normalize data from Financial Model Prep API.
    """

    COLUMNS = ["open", "close", "high", "low", "volume"]

    def normalize(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Normalize the data.

        Parameters
        ----------
        df : pd.DataFrame
            DataFrame to normalize

        Returns
        -------
        pd.DataFrame
            Normalized DataFrame
        """
        if df.empty:
            return df

        # Ensure date is in datetime format
        df[self._date_field_name] = pd.to_datetime(df[self._date_field_name])

        # Sort by date
        df = df.sort_values(by=self._date_field_name)

        # Rename columns to match standard format if needed
        rename_map = {
            'date': self._date_field_name,
            'symbol': self._symbol_field_name,
        }
        df = df.rename(columns={k: v for k, v in rename_map.items() if k in df.columns and k != v})

        # Ensure all required columns exist
        for col in self.COLUMNS:
            if col not in df.columns:
                logger.warning(f"Column {col} not found in data, adding with NaN values")
                df[col] = np.nan

        # Calculate returns if not already present
        if 'return' not in df.columns:
            df['return'] = df['close'].pct_change()

        # Fill missing values
        df = df.fillna(method='ffill')

        # Drop duplicates
        df = df.drop_duplicates([self._date_field_name])

        # Adjust prices if 'adjclose' is available
        if 'adjclose' in df.columns:
            df['factor'] = df['adjclose'] / df['close']
            df['factor'] = df['factor'].fillna(1)

            for col in self.COLUMNS:
                if col == 'volume':
                    df[col] = df[col] / df['factor']
                else:
                    df[col] = df[col] * df['factor']

        return df

    def _get_calendar_list(self) -> list:
        """
        Get calendar list.

        Returns
        -------
        list
            List of calendar dates
        """
        # For FMP, we don't have a predefined calendar
        # We'll use the dates in the data
        return []


class Run(BaseRun):
    """
    Run class for FMP data collection.
    """

    def __init__(
        self,
        source_dir=None,
        normalize_dir=None,
        max_workers=1,
        interval="1d",
        region="US",
        api_key=None,
    ):
        """
        Initialize the Run class.

        Parameters
        ----------
        source_dir : str, optional
            Directory to save source data, default None
        normalize_dir : str, optional
            Directory to save normalized data, default None
        max_workers : int
            Maximum number of workers, default 1
        interval : str
            Data interval, default "1d"
        region : str
            Region code, default "US"
        api_key : str, optional
            FMP API key, default None
        """
        super(Run, self).__init__(source_dir, normalize_dir, max_workers, interval)
        self.region = region
        self.api_key = api_key

    @property
    def collector_class_name(self):
        """
        Get the collector class name.

        Returns
        -------
        str
            Collector class name
        """
        return "FMPCollector"

    @property
    def normalize_class_name(self):
        """
        Get the normalize class name.

        Returns
        -------
        str
            Normalize class name
        """
        return "FMPNormalize"

    @property
    def default_base_dir(self) -> Path:
        """
        Get the default base directory.

        Returns
        -------
        Path
            Default base directory
        """
        return CUR_DIR

    def download_data(
        self,
        api_key=None,
        portfolio=None,
        benchmark="S&P 500",
        max_collector_count=2,
        delay=0.5,
        start=None,
        end=None,
        check_data_length=None,
        limit_nums=None,
    ):
        """
        Download data from FMP API.

        Parameters
        ----------
        api_key : str, optional
            FMP API key, default None
        portfolio : str, optional
            Comma-separated list of portfolio symbols, default None
        benchmark : str
            Benchmark index, default "S&P 500"
        max_collector_count : int
            Maximum collector count, default 2
        delay : float
            Delay between requests, default 0.5
        start : str, optional
            Start date, default None
        end : str, optional
            End date, default None
        check_data_length : int, optional
            Check data length, default None
        limit_nums : int, optional
            Limit number of symbols, default None
        """
        api_key = api_key or self.api_key
        if not api_key:
            raise ValueError("API key is required")

        portfolio_symbols = []
        if portfolio:
            portfolio_symbols = [symbol.strip() for symbol in portfolio.split(',')]

        _class = getattr(self._cur_module, self.collector_class_name)
        collector = _class(
            save_dir=self.source_dir,
            api_key=api_key,
            start=start,
            end=end,
            interval=self.interval,
            max_workers=self.max_workers,
            max_collector_count=max_collector_count,
            delay=delay,
            check_data_length=check_data_length,
            limit_nums=limit_nums,
            portfolio_symbols=portfolio_symbols,
            benchmark=benchmark,
        )
        collector.collector_data()

    def update_data_to_bin(
        self,
        qlib_data_1d_dir: str,
        end_date: str = None,
        region: str = "US",
        interval: str = "1d",
        exists_skip: bool = False,
    ):
        """
        Update data to bin format.

        Parameters
        ----------
        qlib_data_1d_dir : str
            Directory containing the 1d qlib data
        end_date : str, optional
            End date in format YYYY-MM-DD, default None
        region : str
            Region code, default "US"
        interval : str
            Data interval, default "1d"
        exists_skip : bool
            Skip if data already exists, default False
        """
        if self.interval.lower() != "1d":
            logger.warning(f"Currently only supports 1d data updates: --interval 1d")
            return

        # Import necessary modules
        sys.path.insert(0, str(Path(__file__).resolve().parent.parent.parent))
        from scripts.dump_bin import DumpDataStreamUpdate

        # Ensure qlib_data_1d_dir exists
        qlib_data_1d_dir = str(Path(qlib_data_1d_dir).expanduser().resolve())
        Path(qlib_data_1d_dir).mkdir(parents=True, exist_ok=True)

        # Check if calendar file exists, if not create it
        calendar_dir = Path(qlib_data_1d_dir).joinpath("calendars")
        calendar_dir.mkdir(parents=True, exist_ok=True)
        calendar_file = calendar_dir.joinpath("day.txt")

        if not calendar_file.exists() or not exists_skip:
            # Get all dates from normalized data
            all_dates = set()

            # Print the normalize directory path for debugging
            normalize_dir_path = Path(self.normalize_dir)
            logger.info(f"Looking for CSV files in: {normalize_dir_path.absolute()}")

            # List all files in the normalize directory
            csv_files = list(normalize_dir_path.glob("*.csv"))
            logger.info(f"Found {len(csv_files)} CSV files")

            # Process each CSV file
            for csv_file in csv_files:
                logger.info(f"Processing file: {csv_file}")
                try:
                    # Read the CSV file
                    df = pd.read_csv(csv_file)
                    logger.info(f"File {csv_file.name} has {len(df)} rows")

                    # Print the first few rows for debugging
                    logger.info(f"First 3 rows of {csv_file.name}:\n{df.head(3)}")

                    # Check for date column with various possible names
                    date_col = None
                    for col_name in ['date', 'Date', 'DATE', 'time', 'Time', 'TIME']:
                        if col_name in df.columns:
                            date_col = col_name
                            break

                    if date_col:
                        logger.info(f"Found date column '{date_col}' in {csv_file}")
                        # Convert to datetime and handle errors
                        try:
                            dates = pd.to_datetime(df[date_col]).dt.strftime('%Y-%m-%d').tolist()
                            logger.info(f"Sample dates: {dates[:5]}")
                            all_dates.update(dates)
                            logger.info(f"Added {len(dates)} dates from {csv_file}")
                        except Exception as e:
                            logger.warning(f"Error converting dates in {csv_file}: {e}")
                    else:
                        logger.warning(f"No date column found in {csv_file}")
                        # Print column names for debugging
                        logger.warning(f"Available columns: {df.columns.tolist()}")
                except Exception as e:
                    logger.warning(f"Error reading {csv_file}: {e}")

            # Sort dates and save to calendar file
            sorted_dates = sorted(all_dates)
            if sorted_dates:
                with open(calendar_file, 'w') as f:
                    f.write('\n'.join(sorted_dates))
                logger.info(f"Created calendar file with {len(sorted_dates)} dates")
            else:
                logger.warning("No dates found in normalized data")
                return

        # Create instruments directory if it doesn't exist
        instruments_dir = Path(qlib_data_1d_dir).joinpath("instruments")
        instruments_dir.mkdir(parents=True, exist_ok=True)

        # Dump data to bin format using stream update for better memory efficiency
        logger.info(f"Dumping data from {self.normalize_dir} to {qlib_data_1d_dir}")
        dumper = DumpDataStreamUpdate(
            csv_path=self.normalize_dir,
            qlib_dir=qlib_data_1d_dir,
            exclude_fields="symbol,date",
            max_workers=self.max_workers,
        )
        dumper.dump()

        logger.info(f"Successfully updated data to bin format in {qlib_data_1d_dir}")


def main():
    """Main function to parse command line arguments and execute commands."""
    parser = argparse.ArgumentParser(description="FMP Data Collector")
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # download_data command
    download_parser = subparsers.add_parser("download_data", help="Download data from Financial Model Prep API")
    download_parser.add_argument("--api_key", required=True, help="Your Financial Model Prep API key")
    download_parser.add_argument("--portfolio", help="Comma-separated list of portfolio symbols")
    download_parser.add_argument("--source_dir", default=None, help="Directory to save the collected data")
    download_parser.add_argument("--benchmark", default="S&P 500", choices=["S&P 500", "NASDAQ", "DOW"], help="Benchmark index")
    download_parser.add_argument("--start", help="Start date in format YYYY-MM-DD")
    download_parser.add_argument("--end", help="End date in format YYYY-MM-DD")
    download_parser.add_argument("--delay", type=float, default=0.5, help="Delay between API requests in seconds")
    download_parser.add_argument("--max_collector_count", type=int, default=2, help="Maximum number of collection attempts")
    download_parser.add_argument("--check_data_length", type=int, help="Check data length")
    download_parser.add_argument("--limit_nums", type=int, help="Limit number of symbols to process")

    # normalize_data command
    normalize_parser = subparsers.add_parser("normalize_data", help="Normalize data for analysis")
    normalize_parser.add_argument("--source_dir", default=None, help="Directory containing the source data files")
    normalize_parser.add_argument("--normalize_dir", default=None, help="Directory to save the normalized data")
    normalize_parser.add_argument("--date_field_name", default="date", help="Name of the date field in the data files")
    normalize_parser.add_argument("--symbol_field_name", default="symbol", help="Name of the symbol field in the data files")

    # update_data_to_bin command
    update_parser = subparsers.add_parser("update_data_to_bin", help="Update data to bin format")
    update_parser.add_argument("--qlib_data_1d_dir", required=True, help="Directory containing the 1d qlib data")
    update_parser.add_argument("--normalize_dir", help="Directory containing the normalized data files")
    update_parser.add_argument("--end_date", help="End date in format YYYY-MM-DD")
    update_parser.add_argument("--region", default="CN", choices=["CN", "US"], help="Region code")
    update_parser.add_argument("--interval", default="1d", help="Data interval")
    update_parser.add_argument("--exists_skip", action="store_true", help="Skip if data already exists")

    args = parser.parse_args()

    run = Run(
        source_dir=getattr(args, "source_dir", None),
        normalize_dir=getattr(args, "normalize_dir", None),
        interval="1d",
        api_key=getattr(args, "api_key", None),
    )

    if args.command == "download_data":
        run.download_data(
            api_key=args.api_key,
            portfolio=args.portfolio,
            benchmark=args.benchmark,
            max_collector_count=args.max_collector_count,
            delay=args.delay,
            start=args.start,
            end=args.end,
            check_data_length=args.check_data_length,
            limit_nums=args.limit_nums,
        )
    elif args.command == "normalize_data":
        run.normalize_data(
            date_field_name=args.date_field_name,
            symbol_field_name=args.symbol_field_name,
        )
    elif args.command == "update_data_to_bin":
        # Update normalize_dir if provided
        if args.normalize_dir:
            run.normalize_dir = args.normalize_dir

        run.update_data_to_bin(
            qlib_data_1d_dir=args.qlib_data_1d_dir,
            end_date=args.end_date,
            region=args.region,
            interval=args.interval,
            exists_skip=args.exists_skip
        )
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
