#!/bin/bash

# Activate virtual environment
source ~/venv/bin/activate

# Calculate dates
START_DATE=$(date -d "2 years ago" +%Y-%m-%d)
END_DATE=$(date +%Y-%m-%d)

# Read API key from file
API_KEY=$(cat api_key.txt)

# Define directories
SOURCE_DIR="../../../gbs/data/fmp_data"
NORMALIZE_DIR="../../../gbs/data/fmp_data/normalize"
BIN_DIR="../../../gbs/data/gbs_data/us_data"

# Create directories
mkdir -p "$SOURCE_DIR"
mkdir -p "$NORMALIZE_DIR"
mkdir -p "$BIN_DIR"

echo "Step 1: Downloading data for specific stocks from $START_DATE to $END_DATE"
python collector.py download_data \
    --api_key "$API_KEY" \
    --portfolio "AAPL,MSFT,GOOGL,AMZN,META" \
    --source_dir "$SOURCE_DIR" \
    --start "$START_DATE" \
    --end "$END_DATE" \
    --delay 1.0

echo "Step 2: Normalizing data"
python collector.py normalize_data \
    --source_dir "$SOURCE_DIR" \
    --normalize_dir "$NORMALIZE_DIR"

echo "Step 3: Converting to binary format"
python collector.py update_data_to_bin \
    --qlib_data_1d_dir "$BIN_DIR" \
    --region "US" \
    --interval "1d"

echo "Data processing complete. Data saved to:"
echo "  - Source data: $SOURCE_DIR"
echo "  - Normalized data: $NORMALIZE_DIR"
echo "  - Binary data: $BIN_DIR"
