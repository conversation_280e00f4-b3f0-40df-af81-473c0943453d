# Brinson Attribution Analysis with Financial Model Prep API

This directory contains scripts for performing Brinson attribution analysis using data from the Financial Model Prep (FMP) API. The scripts collect stock prices, sector classifications, and benchmark constituents, and then use GBS's `BrinsonAttributionAnalyzer` to perform the analysis.

## Prerequisites

1. Financial Model Prep API key (sign up at [financialmodelingprep.com](https://financialmodelingprep.com/))
2. Python 3.6+
3. Required packages: `requests`, `pandas`, `numpy`, `matplotlib`

## Scripts

### 1. `fmp_brinson_data.py`

This script collects data from the FMP API for Brinson attribution analysis.

#### Usage

```bash
python fmp_brinson_data.py --api_key YOUR_API_KEY --portfolio AAPL,MSFT,GOOGL,AMZN,META --benchmark "S&P 500" --output_dir gbs/data/brinson
```

#### Arguments

- `--api_key`: Your Financial Model Prep API key (required)
- `--portfolio`: Comma-separated list of portfolio symbols (required)
- `--benchmark`: Benchmark index (default: "S&P 500", options: "S&P 500", "NASDAQ", "DOW")
- `--start_date`: Start date in format YYYY-MM-DD (default: 1 year ago)
- `--end_date`: End date in format YYYY-MM-DD (default: today)
- `--output_dir`: Directory to save the collected data (default: gbs/data/brinson)

### 2. `run_brinson_analysis.py`

This script uses the data collected by `fmp_brinson_data.py` to perform Brinson attribution analysis.

#### Usage

```bash
python run_brinson_analysis.py --data_dir gbs/data/brinson --output_dir gbs/data/brinson/reports
```

#### Arguments

- `--data_dir`: Directory containing the data files (default: gbs/data/brinson)
- `--output_dir`: Directory to save the analysis results (default: gbs/data/brinson/reports)
- `--analysis_date`: Date for the analysis in format YYYY-MM-DD (default: latest date in the data)

## Example Workflow

1. Collect data from FMP API:

```bash
python fmp_brinson_data.py --api_key YOUR_API_KEY --portfolio AAPL,MSFT,GOOGL,AMZN,META
```

2. Run Brinson attribution analysis:

```bash
python run_brinson_analysis.py
```

3. View the generated report in the output directory.

## Data Files

The `fmp_brinson_data.py` script generates the following CSV files:

- `prices_TIMESTAMP.csv`: Historical stock prices and returns
- `sectors_TIMESTAMP.csv`: Sector classifications for all symbols
- `benchmark_TIMESTAMP.csv`: Benchmark constituents and weights
- `portfolio_TIMESTAMP.csv`: Portfolio symbols, weights, and sector classifications

## Analysis Results

The `run_brinson_analysis.py` script generates the following files:

- `attribution_results_TIMESTAMP.csv`: Overall attribution results
- `sector_attribution_TIMESTAMP.csv`: Attribution results by sector
- `period_attribution_TIMESTAMP.csv`: Attribution results by period
- HTML report with visualizations

## Limitations

1. FMP API does not provide benchmark constituent weights, so equal weights are used by default.
2. Portfolio weights are set to equal weights by default. For a more accurate analysis, you should provide your actual portfolio weights.
3. The free tier of FMP API has rate limits, so the scripts include delays to avoid hitting these limits.

## Customization

To use your actual portfolio weights, you can modify the `fmp_brinson_data.py` script to include your weights:

```python
# Example portfolio weights
portfolio_weights = {
    'AAPL': 0.25,
    'MSFT': 0.25,
    'GOOGL': 0.20,
    'AMZN': 0.15,
    'META': 0.15
}

# Pass portfolio_weights to prepare_brinson_data
data = collector.prepare_brinson_data(
    portfolio_symbols=portfolio_symbols,
    benchmark=args.benchmark,
    start_date=args.start_date,
    end_date=args.end_date,
    portfolio_weights=portfolio_weights
)
```

## Notes

- The scripts handle missing data by filling with zeros, which may affect the accuracy of the analysis.
- For a more accurate analysis, consider using a premium data provider that offers benchmark constituent weights.
- The analysis is performed for a single date by default. For a multi-period analysis, you can modify the `run_brinson_analysis.py` script.
