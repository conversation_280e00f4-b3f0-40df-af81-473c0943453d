/**
 * publish and subscribe
 */

import { Subject } from "rxjs";
import { share, filter } from "rxjs/operators";

export const event$ = new Subject();

export function on$(types: string | string[], callback: (event: any) => void) {
  if (!Array.isArray(types)) {
    types = [types];
  }
  return event$
    .pipe(
      share(),
      filter((message: any) => {
        return types.indexOf(message.type) > -1;
      })
    )
    .subscribe(callback);
}
