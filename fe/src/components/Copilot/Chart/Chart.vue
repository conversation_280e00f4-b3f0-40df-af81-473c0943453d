<template>
  <div
    class="time-series-component"
    ref="root"
    :class="{
      show: props.show,
    }"
  >
    <div ref="node" class="line-chart-mode"></div>
    <div class="event-container" v-if="keyEvent?.title">
      <h1>{{ keyEvent.title }}</h1>
      <p class="publish_date">{{ keyEvent.publish_date }}</p>
      <p>{{ keyEvent.snippet }}</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as echarts from "echarts/core";
import { LineChart } from "echarts/charts";
import dayjs from "dayjs";

import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  MarkAreaComponent,
  DataZoomComponent,
  LegendComponent,
  MarkLineComponent,
  MarkPointComponent,
} from "echarts/components";

import { LabelLayout, UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
import { EChartsOption } from "echarts";
import { Ref, computed, onBeforeUnmount, onMounted, ref, watch } from "vue";
import {
  IChartMessage,
  IEventChartItem,
  ILineChartItem,
  ChartTypeEnum,
} from "../../../conversation/interface";
import { getCopilotMessageByUnionId } from "../../../conversation/function";
import { getSeriesStyle, theme_config_key } from "./theme";
import { debounce } from "lodash-es";
import { on$ } from "../../../subject";

let myChart: echarts.ECharts;

const props = defineProps<{
  msg: IChartMessage;
  show: boolean;
}>();

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  MarkAreaComponent,
  MarkLineComponent,
  TransformComponent,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
  DataZoomComponent,
  LegendComponent,
  LineChart,
  MarkPointComponent,
]);

const node = ref(null);
const root = ref() as Ref<HTMLDivElement>;
const elOption: Ref<any> = ref({});

const keyEvent: Ref<IEventChartItem["data"][0] | null> = ref(null);

function convertToQuarter(dateString: string) {
  const date = dayjs(dateString, "YYYY-MM-DD");
  const quarter = Math.ceil((date.month() + 1) / 3);
  return `${date.year()}-Q${quarter}`;
}

const chartTitle = computed(() => {
  for (let i = 0; i < props.msg.data.length; i++) {
    let item = props.msg.data[i];
    if (item.title) {
      return item.title;
    }
  }
  return "";
});

/*
 * store all data for easy retrieval later
 */
const dataMap: Map<string, number> = new Map();
watch(
  () => props.msg.data,
  () => {
    dataMap.clear();
    props.msg.data.forEach((item) => {
      (item as ILineChartItem).data.forEach((obj) => {
        if (item.type === ChartTypeEnum.LINE) {
          dataMap.set(
            `${item.metric_display_name}-${obj.display_name}`,
            obj.value
          );
        }
      });
    });
  },
  { immediate: true, deep: true }
);

function render(): EChartsOption {
  const yAxis: { name: string; id: string }[] = [];
  props.msg.data.forEach((item) => {
    let find = yAxis.find((obj) => obj.id === `${item.axis_index}`);
    if (find) {
      find.name = find.name + "," + item.metric;
      return;
    }
    yAxis.push({
      name: item.metric,
      id: `${item.axis_index}`,
    });
  });

  const minumDate = props.msg.data
    .filter((item) => item.data.length > 0)
    .map((item) => {
      let data = item.data[0];
      return {
        ...data,
        timestamp: dayjs(data.name, "YYYY-MM-DD").toDate().getTime(),
      };
    })
    .sort((a, b) => {
      return a.timestamp - b.timestamp;
    })[0];

  const maxDate = props.msg.data
    .filter((item) => item.data.length > 0)
    .map((item) => {
      let data = item.data[item.data.length - 1];
      return {
        ...data,
        timestamp: dayjs(data.name, "YYYY-MM-DD").toDate().getTime(),
      };
    })
    .sort((a, b) => {
      return b.timestamp - a.timestamp;
    })[0];

  const xAxis: { id: string; data: string[]; name: string }[] = [];
  props.msg.data.forEach((obj) => {
    const item = obj as ILineChartItem;
    let find = xAxis.find((obj) => obj.id === `${item.axis_index}`);
    if (find) {
      find.name = find.name + "," + item.metric;
      return;
    }
    xAxis.push({
      id: `${item.axis_index}`,
      name: item.metric,
      data: item.data.map((item) => item.display_name || item.name),
    });
  });

  const dateArray: string[] = [];
  if (minumDate && maxDate && minumDate.timestamp < maxDate.timestamp) {
    let step: dayjs.Dayjs = dayjs(minumDate.name, "YYYY-MM-DD");
    let max = dayjs(maxDate.name, "YYYY-MM-DD");
    let count = 0;
    while (true) {
      count++;
      // avoid too many loop
      if (count > 5000) {
        break;
      }
      if (step.isAfter(max)) {
        break;
      }
      let str = step.format("YYYY-MM-DD");
      dateArray.push(str);
      step = step.add(1, "day");
    }
  }

  const option: EChartsOption = {
    title: {
      text: chartTitle.value,
      left: 20,
      textStyle: {
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: "axis",
      show: true,
      appendToBody: true,
      renderMode: "html",
      confine: false,
      axisPointer: {
        type: "cross",
        snap: true,
      },
      formatter: (params: any) => {
        const map: Map<
          string,
          {
            name: string;
            seriesName: string;
            seriesIndex: number;
            date: string;
            value: number;
            marker: string;
          }[]
        > = new Map();

        params
          .sort((a: any, b: any) => {
            return a.name.length - b.name.length;
          })
          .forEach((param: any) => {
            const name = param.name;
            const seriesName = param.seriesName;
            const seriesIndex = param.seriesIndex;
            const date = dateArray[param.dataIndex];
            const marker = param.marker;
            const value = param.value;
            if (map.has(name)) {
              map
                .get(name)
                ?.push({ name, date, seriesName, seriesIndex, marker, value });
            } else {
              map.set(name, [
                { name, date, seriesName, seriesIndex, marker, value },
              ]);
            }
          });
        return [...map]
          .map((item) => {
            return `<div class="echart-tooltip">
              <div class="title">${item[0]}</div>
              <div class="item">
                ${item[1]
                  .map((obj) => {
                    const isQuarter = /q\d$/i.test(item[0]);
                    let value;
                    if (isNaN(obj.value) && isQuarter) {
                      value =
                        dataMap.get(`${obj.seriesName}-${item[0]}`) || "-";
                    } else {
                      value = obj.value;
                    }
                    if (typeof value === "number" && isNaN(value)) {
                      value = "-";
                    }
                    return `<div class="data-item">
                      <span class="marker">${obj.marker}</span>
                      <span class="series-name">${obj.seriesName}</span>
                      <em class="value">${value}</em>
                    </div>`;
                  })
                  .join("")}
              </div>
            </div>`;
          })
          .join("");
      },
    },
    legend: {
      show: true,
      bottom: 0,
    },
    grid: {
      left: 80,
      right: 80,
    },
    yAxis: yAxis.map((item) => {
      return {
        name: item.name,
        id: `${item.id}`,
        type: "value",
        scale: true,
        nameGap: 50,
        nameRotate: 90,
        nameLocation: "middle",
        max: function (value) {
          return value.max + value.max * 0.15;
        },
        min: function (value) {
          return value.min;
        },
      };
    }),
    xAxis: xAxis.map((item) => {
      return {
        type: "category",
        name: "",
        id: `${item.id}`,
        data: dateArray.map((yyyy_mm_dd_str) => {
          if (/q\d$/i.test(item.data[0])) {
            return convertToQuarter(yyyy_mm_dd_str);
          }
          return yyyy_mm_dd_str;
        }),
        boundaryGap: false,
        axisLine: { onZero: false },
        splitLine: { show: false },
        min: "dataMin",
        max: "dataMax",
      };
    }),
    series: props.msg.data
      .filter((item: ILineChartItem | IEventChartItem) => {
        return item.type === ChartTypeEnum.LINE;
      })
      .map((obj, index) => {
        const item = obj as ILineChartItem;
        let style = getSeriesStyle(item.theme as theme_config_key);
        if (style === null) {
          style = getSeriesStyle(`none${index}` as theme_config_key);
        }
        return {
          name: item.metric_display_name,
          type: "line",
          smooth: false,
          connectNulls: true,
          data: dateArray.map((date_str) => {
            let value = obj.data.find(
              (v) => v.name === date_str
            ) as ILineChartItem["data"][0];
            if (value) {
              return value.value;
            }
            return NaN;
          }),
          yAxisId: `${item.axis_index}`,
          xAxisId: `${item.axis_index}`,
          ...style,
        };
      }),
    preventDefaultMouseMove: true,
  };

  const point = props.msg.data.find(
    (item) => item.type === ChartTypeEnum.EVENT
  ) as IEventChartItem;

  if (point) {
    const markLine = point.data.map((item) => {
      const color =
        keyEvent.value?.name === item.name ? "#3053fa" : "rgba(0,0,0,.5)";
      return {
        silent: false,
        xAxis: item.name,
        label: {
          show: true,
          position: "start",
          distance: 30,
          color,
          formatter: item.title.substring(0, 10) + "...",
        },
        lineStyle: {
          type: keyEvent.value?.name === item.name ? "solid" : "dotted",
          color,
          width: 1,
        },
        data: {
          name: item.name,
          title: item.title,
          snippet: item.snippet,
          publish_date: item.publish_date,
        },
      };
    });
    if (Array.isArray(option.series)) {
      const serie = option.series.find(
        (item: any) => item.yAxisId === `${point.axis_index}`
      ) as any;
      if (serie) {
        serie.markLine = {
          symbolSize: 12,
          symbol: ["circle", "none"],

          emphasis: {
            disabled: true,
            lineStyle: {
              color: "blue",
              width: 1.5,
            },
          },
          data: markLine,
        };
      }
    }
  }

  elOption.value = option;
  return option;
}

const resize = debounce(() => {
  myChart?.resize();
}, 100);

function setDataUrl() {
  const copilot = getCopilotMessageByUnionId(
    props.msg.union_id
  ) as IChartMessage;

  root.value.dataset.echarts_instance = JSON.stringify(myChart.getOption());

  if (copilot) {
    copilot.data_uri = myChart?.getDataURL({
      type: "png",
      pixelRatio: 1,
    });
  }
}

onMounted(async () => {
  myChart = echarts.init(node.value);
  const resizeSub = on$("COPILOT_WWRAPPER_RESIZE", resize);
  onBeforeUnmount(() => {
    resizeSub.unsubscribe();
  });

  const option = render();
  myChart.setOption(option);
  myChart.resize();

  myChart.on("click", "series.line", function (params: any) {
    keyEvent.value = params.data.data;
    const option = render();
    myChart?.setOption(option);
  });

  myChart.on("rendered", setDataUrl);
});
</script>

<style lang="scss">
.time-series-component {
  height: 0;
  overflow: hidden;
  &.show {
    height: auto;
    overflow: visible;
  }
  .line-chart-mode {
    height: 400px;
    width: 100%;
    box-sizing: border-box;
    margin-top: 1em;
  }
  > .markpoint-tofu {
    overflow: auto;
    height: 80px;
    display: flex;
    gap: 12px;
    margin: 12px;
    position: relative;
    > .item {
      border: solid 2px #eee;
      border-radius: 5px;
      box-sizing: border-box;
      flex: 0 0 150px;
      height: 80px;
      padding: 5px;
      &.current {
        border: solid 2px var(--el-color-primary);
      }
      h1 {
        margin: 0;
        padding: 0;
        font-size: 12px;
        font-weight: normal;
      }
      p.date {
        font-size: 10px;
      }
    }
  }
  .markpoint-detail {
    margin: 0 12px;
    h1 {
      font-weight: normal;
      font-size: 18px;
    }
    p {
      font-size: 14px;
    }
  }
  > .filter-wrapper {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    > .filter {
      display: flex;
      gap: 10px;
      margin-bottom: 10px;
      padding: 10px;
      > label {
        font-size: 12px;
        border: radius 5px;
        cursor: pointer;
        &:hover b {
          background-color: #f2f2f2;
        }
        b {
          font-weight: normal;
          padding: 5px 16px 0 16px;
          display: inline-flex;
          flex-direction: column;
          align-items: center;
          background-color: #f9f9f9;
          &::after {
            content: "";
            width: 100%;
            height: 2px;
            background-color: transparent;
            border-radius: 5px;
            margin-top: 3px;
            transition: all 0.2s;
          }
        }
        input {
          opacity: 0;
          position: absolute;
          left: 0;
          top: 0;
          &:focus + b::after {
            background-color: var(--el-color-primary);
            opacity: 0.5;
          }
          &:checked + b::after {
            background-color: var(--el-color-primary);
            opacity: 1;
          }
        }
      }
    }
  }
  .event-container {
    margin: 1em;
    border-top: solid 1px #eee;
    padding-top: 1em;
    h1 {
      font-weight: normal;
      font-size: 110%;
      margin: 0;
      padding: 0;
    }
    p.publish_date {
      font-size: 80%;
      color: rgba(0, 0, 0, 0.5);
    }
  }
}

.echart-tooltip {
  min-width: 230px;
  padding: 0;
  font-size: 12px;
  .title {
    font-weight: bold;
  }
  .data-item {
    display: flex;
    width: 100%;
    .value {
      flex: 1 1 0;
      text-align: right;
      font-weight: bold;
      font-style: normal;
    }
  }
}
</style>
