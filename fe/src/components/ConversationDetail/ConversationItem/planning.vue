<template>
  <div
    class="conversation-item conversation-item-reference J-planning-container"
    :class="{
      isShow: props.msg.show,
    }"
    ref="root"
  >
    <div class="container">
      <div class="steps-folding" @click="foldingHandler">
        <div class="title">
          <img src="./icon/section/planning.svg" />Planning
        </div>
        <div class="info" v-if="allCompleted">
          <span>{{ props.msg.steps.length }} planning completed </span>
          <ArrowDown />
        </div>
      </div>
      <div class="ul-wrapper" :style="{ height: height }">
        <ul ref="ul" :class="{ show: props.msg.show }">
          <li
            v-for="(step, idx) in props.msg.steps"
            :key="idx"
            :status="step.status"
          >
            <div class="icon">
              <Transition name="planning-slide-up" mode="out-in">
                <span
                  v-if="step.status === EPLANNING_STEP_STATUS.DOING"
                  class="status"
                  ><img src="./icon/planning-loading.svg"
                /></span>
                <span
                  v-else-if="step.status === EPLANNING_STEP_STATUS.DONE"
                  class="status"
                >
                  <img
                    v-if="step.text.includes(`[${PlanningTypeEnum.SEARCH}]`)"
                    src="./icon/search.svg"
                  />
                  <img
                    v-if="step.text.includes(`[${PlanningTypeEnum.THOUGHTS}]`)"
                    src="./icon/thoughts.svg"
                  />
                  <img
                    v-if="step.text.includes(`[${PlanningTypeEnum.SOURCE}]`)"
                    src="./icon/source.svg"
                  />
                </span>
              </Transition>
            </div>
            <div class="text">
              {{ clean(step.text) }}
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Ref, computed, onBeforeUnmount, onMounted, ref, watch } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";

import {
  IPlanningMessage,
  EPLANNING_STEP_STATUS,
  EMessageRole,
  PlanningTypeEnum,
} from "../../../conversation/interface";
import { conversations } from "../../../conversation/store";

const props = defineProps<{
  msg: IPlanningMessage;
}>();

const allCompleted = computed(() => {
  return props.msg.steps.every(
    (step) => step.status === EPLANNING_STEP_STATUS.DONE
  );
});

function foldingHandler() {
  if (false === allCompleted.value) {
    return;
  }
  const conv = conversations.value.find(
    (conv) =>
      conv.role === EMessageRole.PLANNING &&
      conv.chat_turn_id === props.msg.chat_turn_id
  ) as IPlanningMessage;
  conv.show = !conv.show;
}

const ul = ref() as Ref<HTMLUListElement>;
const ulHeight = ref(0);
const height = ref(props.msg.show ? "auto" : "0px");

function clean(str: string) {
  return str.replace(/\[.*?\]/g, "");
}

onMounted(() => {
  const observer = new ResizeObserver(() => {
    ulHeight.value = ul.value.offsetHeight;
    if (props.msg.show) {
      height.value = ulHeight.value + "px";
    }
  });
  observer.observe(ul.value);
  onBeforeUnmount(() => {
    observer.disconnect();
  });
});

watch(
  () => props.msg.show,
  () => {
    if (props.msg.show) {
      height.value = ulHeight.value + "px";
    } else {
      height.value = 0 + "px";
    }
  },
  {
    immediate: true,
  }
);
</script>

<style lang="scss">
.planning-slide-up-enter-active,
.planning-slide-up-leave-active {
  transition: all 0.25s ease-out;
}
.planning-slide-up-enter-from {
  opacity: 0;
  transform: scale(1);
}
.planning-slide-up-leave-to {
  opacity: 0;
  transform: scale(0);
}

.conversation-item-reference {
  font-size: 14px;
  border-radius: 8px;
  transition: all 0.2s;
  border: solid 1px #e2e2e2;
  > .container {
    > .steps-folding {
      display: flex;
      align-items: center;
      justify-content: space-between;
      user-select: none;
      transition: all 0.2s;
      padding: 1em;

      > .title {
        font-weight: bold;
        font-size: 16px;
        transition: all 0.2s;
        font-weight: bold;
        display: flex;
        align-items: center;
        img {
          width: 18px;
          margin-right: 8px;
        }
      }
      > .info {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #666;
        cursor: pointer;
        svg {
          width: 14px;
          height: 14px;
          margin-left: 0.5em;
          transform: rotate(0deg);
          transition: all 0.2s;
        }
      }
    }
    ul,
    li {
      list-style: none;
      margin: 0;
      padding: 0;
    }
    .ul-wrapper {
      transition: all 0.2s;
      overflow: hidden;
      ul {
        background-color: #f7f9fb;
        padding: 0.5em 1em;
        li {
          line-height: 100%;
          margin: 4px auto;
          display: flex;
          align-items: flex-start;
          padding: 0.2em 0;
          > .icon {
            margin-right: 0.3em;
            position: relative;
            top: 2px;
            > .status {
              color: #000;
              border-radius: 50% 50%;
              width: 1em;
              display: inline-flex;
              justify-content: center;
              align-items: center;
              width: 1.5em;
              font-size: 12px;
              vertical-align: middle;
              position: relative;
              img {
                max-width: 16px;
                max-height: 16px;
              }
            }
            &.doing {
              font-weight: bold;
            }
          }
          > .text {
            line-height: 150%;
            img {
              width: 1em;
              height: 1em;
            }
          }
        }
      }
    }
  }
  &.isShow {
    overflow: hidden;
    > .container {
      > .steps-folding {
        > .info {
          svg {
            transform: rotate(180deg);
          }
        }
      }
      .ul-wrapper {
        ul {
          border-radius: 5px;
          overflow: hidden;
        }
      }
    }
  }
}
</style>
