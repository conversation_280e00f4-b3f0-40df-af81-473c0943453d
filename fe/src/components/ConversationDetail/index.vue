<template>
  <div class="conversations-list-wrapper" ref="rootNode">
    <div
      class="conversation-list J-conversation-list"
      ref="conversationList"
      :style="`flex: 0 0  ${flex}%`"
    >
      <div
        v-for="msg in list"
        :key="msg.key"
        :data-chat-id="msg.chat_id"
        :data-chat-turn-id="msg.chat_turn_id"
        :data-role="msg.role"
        class="item"
      >
        <ConversationUserItem
          :msg="msg"
          v-if="msg.role === EMessageRole.USER"
        />
        <ConversationAssistantItem
          :msg="msg"
          v-if="msg.role === EMessageRole.ASSISTANT"
        />
        <ConversationSources
          :msg="msg"
          v-if="msg.role === EMessageRole.SOURCES"
        />
        <PlanningItem :msg="msg" v-if="msg.role === EMessageRole.PLANNING" />
        <ConversationTypingtem v-if="msg.role === EMessageRole.TYPING" />
      </div>
    </div>
    <div
      class="resize-width"
      @mousedown="on"
      @contextmenu="off"
      @dblclick="flex = 50"
      :style="{
        flex: `0 0 ${resizeTriggerWidth}px`,
      }"
      ref="resizeTrigger"
    ></div>
    <div class="side-pannel J-side-pannel">
      <CopilotVue />
    </div>
  </div>
  <AssistantLinkPopover />
</template>

<script lang="ts" setup>
import ConversationUserItem from "./ConversationItem/user.vue";
import ConversationAssistantItem from "./ConversationItem/assistant.vue";
import ConversationTypingtem from "./ConversationItem/typing.vue";
import PlanningItem from "./ConversationItem/planning.vue";
import ConversationSources from "./ConversationItem/sources.vue";
import { EMessageRole } from "../../conversation/interface";
import { Ref, computed, onMounted, ref, watch } from "vue";
import CopilotVue from "./Copilot.vue";
import AssistantLinkPopover from "./AssistantLinkPopover/popover.vue";
import { conversations } from "../../conversation/store";

const list = computed(() => {
  return conversations.value.filter((item) => {
    return [
      EMessageRole.ASSISTANT,
      EMessageRole.USER,
      EMessageRole.SOURCES,
      EMessageRole.PLANNING,
      EMessageRole.TYPING,
    ].includes(item.role);
  });
});

const resizeTrigger: Ref<HTMLDivElement | null> = ref(null);
const rootNode: Ref<HTMLDivElement | null> = ref(null);
const conversationList: Ref<HTMLDivElement | null> = ref(null);
const flex = ref(50);
const resizeTriggerWidth = ref(10);

let initX = 0;
let offsetX = 0;
let rootWidth = 0;
let conversationListWidth = 0;

function handleMouseMove(event: MouseEvent) {
  offsetX = event.pageX - initX;
  if (offsetX === 0) {
    return 0;
  }

  const newFlex = (conversationListWidth + offsetX) / rootWidth;
  if (newFlex < 0.2) {
    return 0.2 * 100;
  }
  if (newFlex > 0.8) {
    return 0.8 * 100;
  }
  flex.value = newFlex * 100;
}

function selectStartHandler(ev: any) {
  ev.preventDefault();
}

function off() {
  document.removeEventListener("mousemove", handleMouseMove);
  document.removeEventListener("selectstart", selectStartHandler);
  document.removeEventListener("mouseup", off);
}

function on(event: MouseEvent) {
  off();
  initX = event.pageX;
  conversationListWidth =
    (conversationList.value?.offsetWidth || 0) + resizeTriggerWidth.value / 2;
  rootWidth = rootNode.value?.offsetWidth || 0;
  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("selectstart", selectStartHandler);
  document.addEventListener("mouseup", off);
}

const flex_key = "CONVERSATION_FLEX_KEY";
watch(
  () => flex.value,
  () => {
    localStorage.setItem(flex_key, flex.value.toString());
  }
);

onMounted(() => {
  const flexValue = localStorage.getItem(flex_key);
  if (flexValue) {
    flex.value = Number(flexValue);
  }
});
</script>

<style lang="scss">
.conversations-list-wrapper {
  padding: 0 12px 12px 0;
  display: flex;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  > .conversation-list {
    overflow-y: auto;
    background-color: #fff;
    overflow-x: hidden;
    > .item {
      margin: 2em 0 2em 0;
      padding: 0 20px 0 20px;
      &:last-child {
        padding-bottom: 4em;
      }
      &[data-role="user"] {
        border-top: solid 12px #f5f5f6;
        padding-top: 2em;
        margin-top: 2em;
        position: relative;
        &:first-child {
          border-top: none;
          margin-top: 0;
        }
      }
    }
  }
  > .resize-width {
    cursor: ew-resize;
    display: flex;
    align-items: center;
    justify-content: center;
    &::after {
      transition: all 0.2s;
      display: block;
      content: "";
      height: 50px;
      width: 100%;
      background: url("../assets/resize.svg") no-repeat center center;
      background-size: 12px auto;
      opacity: 0;
    }
    &:hover {
      &::after {
        opacity: 1;
      }
    }
  }
  > .side-pannel {
    flex: 1 1 0;
    background-color: #fff;
    overflow: hidden;
  }
}
</style>
