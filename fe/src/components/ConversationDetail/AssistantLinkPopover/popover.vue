<template>
  <el-popover
    trigger="hover"
    placement="top"
    :virtual-ref="linkPopoverRef"
    virtual-triggering
    width="360px"
    transition="none"
    :popper-class="`assistant-link-popover ${conv?.role || ''}`"
  >
    <div v-if="conv">
      <CitationVue :msg="conv" v-if="conv.role === EMessageRole.CITATION" />
    </div>
    <div v-else>No reference found</div>
  </el-popover>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { ElPopover } from "element-plus";
import CitationVue from "./citation.vue";
import { linkPopoverRef, popoverRefUnionId } from "./store";
import { EMessageRole } from "../../../conversation/interface";
import { conversations } from "../../../conversation/store";

const conv = computed(() => {
  return conversations.value.find((item) => {
    return (
      item.role === EMessageRole.CITATION &&
      item.union_id === popoverRefUnionId.value
    );
  });
});
</script>
../../../conversation/interface../../../conversation/store
