<template>
  <div class="dashboard-question-statistics">
    <div class="calendar-container">
      <el-calendar v-model="value" @input="handleCalendarChange">
        <template #date-cell="{ data }">
          <div :class="`J-dashboard-${data.day}`">
            {{ data.date.getDate() }}
            <div class="question-count" v-if="count_map[data.day]">
              <span
                ><b>{{ count_map[data.day] }}</b
                >Qs</span
              >
            </div>
          </div>
        </template>
      </el-calendar>
    </div>
    <div class="table-wrapper">
      <el-table
        :data="gridData"
        v-loading="loading"
        border
        max-height="360px"
        size="small"
      >
        <el-table-column
          property="user_msg"
          show-overflow-tooltip
          label="Message"
        >
          <template #default="{ row }">
            <a
              href="javascript:void(0)"
              @click="showDialog(row.chat_turn__chat__id)"
              >{{ row.chat_turn__user_msg }}</a
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>

  <el-dialog
    title="Chat Details"
    class="chat_details_dialog"
    v-model="dialogVisible"
    width="90%"
  >
    <div>
      <ConversationDetail />
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { Ref, onMounted, ref } from "vue";
import { ElCalendar, ElTable, ElTableColumn, ElDialog } from "element-plus";
import request from "../request";
import dayjs from "dayjs";
import ConversationDetail from "../components/ConversationDetail/index.vue";
import {
  loadConversation,
  conversationScrollToTop,
  switchCopilot,
} from "../conversation/function";
import { conversations } from "../conversation/store";
import { EMessageRole, ICopilotMessage } from "../conversation/interface";
import { switchChildCitation } from "../conversation/citation";

const gridData = ref([]);
const dialogVisible = ref(false);

const chat_id = ref(-1);
async function showDialog(chat_id_value: number) {
  dialogVisible.value = true;
  chat_id.value = chat_id_value;
  await loadConversation(chat_id_value);
  await redirectFirstCopilot();
  conversationScrollToTop();
}

function findFirstCopilot(): ICopilotMessage | undefined {
  return conversations.value.find((item) => {
    return (
      item.role === EMessageRole.CHART || item.role === EMessageRole.CITATION
    );
  }) as ICopilotMessage;
}

async function redirectFirstCopilot() {
  const copilot = findFirstCopilot();
  if (copilot) {
    await redirectToCopilot(copilot);
  }
}

/** redirect to the citation of the current chat turn */
async function redirectToCopilot(citation: ICopilotMessage) {
  if (citation) {
    await switchCopilot(citation).catch(() => {});
    if (
      citation.role === EMessageRole.CITATION &&
      citation.citations &&
      citation.citations[0]
    ) {
      switchChildCitation(citation, citation.citations[0].number);
    }
  }
}

const count_map: Ref<{ [key in string]: number }> = ref({});
onMounted(() => {
  request
    .get("/api/v1/dashboard/chat_turns_per_day_counts")
    .then((res: any) => {
      res.data.data.forEach((item: any) => {
        count_map.value[item.date] = item.count;
      });
    });
  handleCalendarChange(new Date());
});

const value = ref(new Date());

const loading = ref(false);
async function handleCalendarChange(date: Date) {
  loading.value = true;
  await request
    .get(`/api/v1/dashboard/chat_turns/${dayjs(date).format("YYYY-MM-DD")}`)
    .then((res: any) => {
      gridData.value = res.data.data;
    })
    .catch(() => {
      loading.value = false;
    });
  loading.value = false;
}
</script>

<style lang="scss">
.dashboard-question-statistics {
  display: flex;
  gap: 12px;
  > .calendar-container {
    flex: 0 0 60%;
  }
  > .table-wrapper {
    flex: 1 1 0;
    overflow: hidden;
  }
  .el-calendar {
    --el-calendar-cell-width: 40px;
    .el-calendar__body {
      padding: 12px 0;
    }
    .el-calendar__header {
      padding: 0 0 0 0;
      border-bottom: none;
    }
    .el-calendar-day {
      position: relative;
      padding: 0;
      .question-count {
        position: absolute;
        right: 0;
        bottom: 0;
        span {
          b {
            font-size: 16px;
          }
        }
      }
    }
  }
}

.chat_details_dialog {
  .el-dialog__body {
    padding: 0;
  }
}
</style>
