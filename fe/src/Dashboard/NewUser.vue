<template>
  <div v-loading="loading" class="line-charts-container" ref="root">
    <div class="canvas" ref="chart"></div>
  </div>
</template>

<script lang="ts" setup>
import { Ref, onBeforeMount, onMounted, ref, watch } from "vue";
import request from "../request";
import * as echarts from "echarts/core";
import { LineChart } from "echarts/charts";

import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  MarkAreaComponent,
  DataZoomComponent,
  LegendComponent,
  MarkLineComponent,
  MarkPointComponent,
} from "echarts/components";

import { LabelLayout, UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
import { useResize } from "./useResize";
import dayjs from "dayjs";

const chart = ref() as Ref<HTMLDivElement>;
let myChart: echarts.ECharts;

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  MarkAreaComponent,
  MarkLineComponent,
  TransformComponent,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
  DataZoomComponent,
  LegendComponent,
  LineChart,
  MarkPointComponent,
]);

const dau: Ref<
  {
    label: string;
    value: number;
  }[]
> = ref([]);

function getOption() {
  return {
    title: {
      text: "New User",
      subtext: "from database",
    },
    tooltip: {
      trigger: "axis",
    },
    xAxis: {
      type: "category",
      data: dau.value.map((item) => {
        return dayjs(item.label).format("YYYY-MM-DD");
      }),
      axisLine: { onZero: false },
      splitLine: { show: false },
    },
    yAxis: {
      type: "value",
      axisLine: { onZero: false },
      splitLine: { show: false },
    },
    series: [
      {
        data: dau.value.map((item) => {
          return item.value;
        }),
        name: "dau",
        type: "line",
        smooth: true,
        showSymbol: false,
      },
    ],
  };
}

const loading = ref(false);
async function load() {
  const res = await request({
    method: "get",
    maxBodyLength: Infinity,
    url: `/api/v1/dashboard/new_users`,
  });
  dau.value = res.data.data;
}

onBeforeMount(async () => {
  load();
});

watch(
  () => [dau.value],
  () => {
    const option = getOption();
    myChart.setOption(option);
  }
);

useResize(() => {
  myChart.resize();
});

onMounted(async () => {
  myChart = echarts.init(chart.value);
});
</script>

<style lang="scss">
.line-charts-container {
  height: 100%;
  overflow: hidden;
  > .canvas {
    height: 100%;
  }
}
</style>
