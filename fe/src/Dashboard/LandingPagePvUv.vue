<template>
  <dl class="metrics" v-loading="loading">
    <dt>
      LandingPage PV/UV
      <el-tooltip
        class="box-item"
        effect="dark"
        placement="top"
        :raw-content="true"
      >
        <template #content>
          Use the session_start to track pv/uv, refer to the doc:<br />
          <a
            href="https://help.amplitude.com/hc/en-us/articles/115002323627-Track-sessions"
            target="_blank"
            style="color: var(--primary)"
            >https://help.amplitude.com/hc/en-us/articles/115002323627-Track-sessions</a
          >
        </template>
        <el-icon><WarningFilled /></el-icon
      ></el-tooltip>
    </dt>
    <dd>
      <strong style="margin-right: 10px">{{ pv }}/{{ uv }}</strong>
    </dd>
  </dl>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import request from "../request";
import dayjs from "dayjs";
import { ElIcon, ElTooltip } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";

const props = defineProps<{
  date_range: [Date, Date];
}>();

const loading = ref(false);
const pv = ref(0);
const uv = ref(0);

function loadPv() {
  return request
    .get(`/api/v1/dashboard/amplitude-proxy/api/2/events/segmentation`, {
      params: {
        platform: "landing_page",
        e: '{"event_type":"session_start"}',
        m: "totals",
        start: dayjs(props.date_range[0]).format("YYYYMMDD"),
        end: dayjs(props.date_range[1]).format("YYYYMMDD"),
      },
    })
    .then((res: any) => {
      pv.value = res.data.data.seriesCollapsed[0][0].value;
    });
}

function loadUv() {
  return request
    .get(`/api/v1/dashboard/amplitude-proxy/api/2/events/segmentation`, {
      params: {
        platform: "landing_page",
        e: '{"event_type":"session_start"}',
        m: "uniques",
        start: dayjs(props.date_range[0]).format("YYYYMMDD"),
        end: dayjs(props.date_range[1]).format("YYYYMMDD"),
      },
    })
    .then((res: any) => {
      uv.value = res.data.data.seriesCollapsed[0][0].value;
    });
}

async function loadData() {
  loading.value = true;
  try {
    await Promise.all([loadPv(), loadUv()]);
  } catch {}
  loading.value = false;
}

onMounted(() => {
  loadData();
});

watch(
  () => props.date_range,
  () => {
    loadData();
  },
  { deep: true }
);
</script>
