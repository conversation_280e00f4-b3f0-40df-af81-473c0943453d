import { getCurrentInstance, onBeforeUnmount, onMounted, ref } from "vue";

export function useResize(callback: () => void) {
  const vm = getCurrentInstance();
  onMounted(() => {
    if (!vm) {
      return;
    }
    const resizeObserver = new ResizeObserver(() => {
      callback();
    });
    resizeObserver.observe(vm.refs.root as HTMLDivElement);
    onBeforeUnmount(() => {
      resizeObserver.disconnect();
    });
  });
}

const darkMode = ref(false);

const darkModeMediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
const setDarkModeClass = (e: MediaQueryListEvent) => {
  if (e.matches) {
    darkMode.value = true;
  } else {
    darkMode.value = false;
  }
};

darkModeMediaQuery.addEventListener("change", setDarkModeClass);
setDarkModeClass(darkModeMediaQuery as unknown as MediaQueryListEvent);

export function useDarkMode() {
  function toggleDarkMode() {
    darkMode.value = !darkMode.value;
  }
  return { darkMode, toggleDarkMode };
}
