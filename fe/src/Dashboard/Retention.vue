<template>
  <div
    style="grid-column: 1 / span 2"
    v-loading="loading"
    class="retention-container"
    ref="root"
  >
    <h2>Retention</h2>
    <table>
      <thead>
        <tr>
          <th width="120">Cohort</th>
          <th>Size</th>
          <th>Week 0</th>
          <th>Week 1</th>
          <th>Week 2</th>
          <th>Week 3</th>
          <th>Week 4</th>
          <th>Week 5</th>
          <th>Week 6</th>
          <th>Week 7</th>
          <th>Week 8</th>
          <th>Week 9</th>
          <th>Week 10</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="item in data" :key="item.cohort_start">
          <td>
            <b
              >{{ dayjs(item.cohort_start).format("MMM D") }} to
              {{ dayjs(item.cohort_end).format("MMM D") }}</b
            >
          </td>
          <td>
            <div class="size">{{ item.size }}</div>
          </td>
          <td v-for="week in item.weekly_active_users">
            <div class="retention" v-if="week">
              <div class="week">{{ week }}</div>
              <span class="percentage"
                >{{ ((week / item.size) * 100).toFixed(0) }}%</span
              >
            </div>
            <span v-else></span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script lang="ts" setup>
import request from "../request/index";
import { Ref, onBeforeMount, ref } from "vue";
import dayjs from "dayjs";

const data: Ref<
  {
    cohort_start: string;
    cohort_end: string;
    size: number;
    weekly_active_users: number;
  }[]
> = ref([]);
const loading = ref(false);

async function load() {
  const res = await request({
    method: "get",
    maxBodyLength: Infinity,
    url: `/api/v1/dashboard/retention`,
  });
  data.value = res.data.data
    .filter((item: any) => item.size > 0)
    .map((item: any) => {
      // padding empty weeks
      Array.from({ length: 11 }).forEach((_, i) => {
        item.weekly_active_users[i] = item.weekly_active_users[i] || "";
      });
      return item;
    });
}

onBeforeMount(async () => {
  load();
});
</script>

<style lang="scss">
.retention-container {
  margin-bottom: 16px;
  h2 {
    font-size: 130%;
    margin-bottom: 16px;
  }
  table {
    table-layout: fixed;
    width: 100%;
    td {
      border: none;
      padding: 18px 12px;
      vertical-align: top;
      .size {
        font-size: 120%;
      }
      .retention {
        .week {
          font-size: 120%;
        }
        .percentage {
          opacity: 0.5;
          font-size: 90%;
        }
      }
    }
  }
}
</style>
