/**
 * 
 * 
 ```typescript

// before: [ ' behalf', ',', ' [1]engaging[3', ']in[', '4] [', 'b]with' ]
//  after: [ ' behalf, [1]', 'engaging[3]', 'in[4]', ' [b]', 'with' ]

let mock_data = [ ' behalf', ',', ' [1]engaging[3', ']in[', '4] [', 'b]with' ]
let test_array: string[] = [];
mock_data.forEach((item) => {
  mergeAssistantMessage(test_array, item);
});

console.log("unit_test", mock_data.join("") === test_array.join(""));
```
 */

export function mergeAssistantMessage(array: string[], item: string): void {
  const last = array[array.length - 1];
  if (item.includes("]")) {
    let arr = item.split("]");
    array[array.length - 1] = last + arr[0] + "]";
    arr.forEach((item, idx, arr) => {
      if (idx === 0) {
        return;
      }
      if (idx === arr.length - 1) {
        array.push(item);
      } else {
        array[array.length - 1] = array[array.length - 1] + item + "]";
      }
    });
  } else {
    if (last === undefined) {
      array.push(item);
    } else {
      array[array.length - 1] = last + item;
    }
  }
}
