import request from "../request";
import { EMessageRole, ICitationMessage } from "./interface";
import { conversations } from "./store";
import { openDB } from "idb";
import { nextTick } from "vue";

export async function switchToCitation(
  citation_ref: ICitationMessage
): Promise<ICitationMessage> {
  // citation maybe a props value, so we need to find the citation from the store
  const citation = conversations.value.find(
    (item) =>
      item.role === EMessageRole.CITATION &&
      item.union_id === citation_ref.union_id
  ) as ICitationMessage;

  if (citation.real_text_load_failed) {
    citation.parsed_dom_node = undefined;
  }

  citation.is_new = false;
  if (citation.real_text) {
    return citation;
  }


  citation.real_text_loading = true;

  try {
    // find from indexedDB
    const db = await openDB("onwish", 4, {
      upgrade(db, oldVersion, newVersion) {
        if (newVersion && newVersion > oldVersion) {
          if (db.objectStoreNames.contains("citations")) {
            db.deleteObjectStore("citations");
          }
          db.createObjectStore("citations");
        }
      },
    }).catch((e) => {
      console.error("open indexedDB error", e);
      return null;
    });

    if (db) {
      const cached = await db
        .get("citations", citation.original_doc_ref)
        .catch((e) => {
          console.error(
            "get citation cache error",
            citation.original_doc_ref,
            e
          );
          return null;
        });

      if (cached) {
        citation.real_text_loading = false;
        citation.real_text = cached;
        console.log("get citation cache", citation.original_doc_ref);
        return citation;
      }
    }

    await request
      .get(`/api/v1/store/o${citation.original_doc_ref}`)
      .then(async (res) => {
        citation.real_text = res.data;
        citation.real_text_load_failed = false;
        if (db) {
          await db
            .put("citations", res.data, citation.original_doc_ref)
            .then(() => {
              console.log("put citation cache", citation.original_doc_ref);
            })
            .catch((e) => {
              console.error(
                "put citation cache error",
                citation.original_doc_ref,
                e
              );
            });
        }
      })
      .catch(() => {
        citation.real_text_load_failed = true;
        citation.real_text = "";
      })
      .finally(() => {
        citation.real_text_loading = false;
      });
  } catch (e) {
    console.error("switchToCitation error", e);
  } finally {
    citation.real_text_loading = false;
  }
  return citation;
}

export function getCitationByNumber(
  chat_id: number,
  chat_turn_id: number,
  citation_number: number
) {
  return conversations.value.find(
    (item) =>
      item.role === EMessageRole.CITATION &&
      item.chat_id === chat_id &&
      item.chat_turn_id === chat_turn_id &&
      item.citations?.some((item) => item.number === citation_number)
  ) as ICitationMessage;
}

export function addComments(
  str: string,
  highlight_spans: {
    number: number;
    range: number[][];
  }[]
) {
  // Flatten the ranges and sort them in reverse order
  let ranges = highlight_spans
    .flatMap((span, index) =>
      span.range
        .map((range) => ({
          index,
          number: span.number,
          start: range[0],
          end: range[1],
          isStart: true,
        }))
        .concat(
          span.range.map((range) => ({
            index,
            number: span.number,
            start: range[1],
            end: range[0],
            isStart: false,
          }))
        )
    )
    .sort((a, b) => b.start - a.start);

  // Insert the comments
  for (let range of ranges) {
    let comment = range.isStart
      ? `<!--START-${range.number}-->`
      : `<!--END-${range.number}-->`;
    str = str.slice(0, range.start) + comment + str.slice(range.start);
  }

  return str;
}

function getAllDescendants(node: HTMLElement) {
  let descendants: any[] = [];
  for (let i = 0; i < node.childNodes.length; i++) {
    let child = node.childNodes[i];
    descendants.push(child);
    let childDescendants = getAllDescendants(child as HTMLElement);
    for (let j = 0; j < childDescendants.length; j++) {
      descendants.push(childDescendants[j]);
    }
  }
  return descendants;
}

export function converCommentToHtmlTag(
  str: string,
  comment_arr: {
    range: number[][];
    number: number;
  }[]
) {
  let parser = new DOMParser();
  let doc = parser.parseFromString(`<div>${str}</div>`, "text/html");
  comment_arr.forEach((comment) => {
    const allChild = getAllDescendants(doc.body);
    // Traverse all nodes between start and end, if it is a text node, wrap it with <span class="highlight-index"> tag
    let isStart = false;
    let isEnd = false;
    let count = 0;
    for (let i = 0; i < allChild.length; i++) {
      const node = allChild[i];
      if (
        node.nodeType === Node.COMMENT_NODE &&
        node.nodeValue === `START-${comment.number}`
      ) {
        isStart = true;
      }
      if (
        node.nodeType === Node.COMMENT_NODE &&
        node.nodeValue === `END-${comment.number}`
      ) {
        isEnd = true;
      }
      if (isStart && isEnd) {
        isStart = false;
        isEnd = false;
        continue;
      }
      if (
        isStart &&
        node.nodeType === Node.TEXT_NODE &&
        node.nodeValue.trim()
      ) {
        const b = document.createElement("quotes");
        b.setAttribute("data-number", `${comment.number}`);
        b.setAttribute("data-count", `${count}`);
        b.innerHTML = node.nodeValue;
        node.parentNode.replaceChild(b, node);
        count++;
      }
    }
  });

  return doc.body.childNodes[0] as HTMLDivElement;
}

export function switchChildCitation(
  citation: ICitationMessage,
  highlight_number: number
) {
  const node = document.querySelector(".J-citation-content") as HTMLDivElement;
  if (citation.parsed_dom_node) {
    node.innerHTML = "";
    node.appendChild(citation.parsed_dom_node);
    jumpToQuoteTag(citation.union_id, highlight_number);
    return;
  }

  const comment_arr = citation.citations?.map((item) => {
    return {
      range: item.highlight_spans,
      number: item.number,
    };
  });

  if (comment_arr) {
    let str = addComments(citation.real_text || "", comment_arr);
    let parsed_dom_node = converCommentToHtmlTag(str, comment_arr);
    parsed_dom_node = converLink(citation, parsed_dom_node);
    citation.parsed_dom_node = parsed_dom_node;
    node.innerHTML = "";
    node.appendChild(parsed_dom_node);
    jumpToQuoteTag(citation.union_id, highlight_number);
  }
}

function converLink(citation: ICitationMessage, node: HTMLDivElement) {
  const base = citation.source.split("/").slice(0, -1).join("/") + "/";
  const allNode = node.getElementsByTagName("*");

  // Avoid generating variables with the same name in the global scope
  for (let i = 0; i < allNode.length; i++) {
    const node = allNode[i];
    const id = node.getAttribute("id");
    if (id) {
      node.setAttribute("id", `mock-citation-id-${id}`);
    }

    if (node.nodeName === "A") {
      const href = node.getAttribute("href") || "";
      if (href.startsWith("#")) {
        node.setAttribute("href", `#mock-citation-id-${href.substring(1)}`);
      } else if (href.length > 0 && false === href.startsWith("http")) {
        const url = new URL(href, base);
        node.setAttribute("rel", "noopener noreferrer");
        node.setAttribute("target", "_blank");
        node.setAttribute("href", url.toString());
      }
    }

    if (node.nodeName === "IMG") {
      const src = node.getAttribute("src") || "";
      if (src.startsWith("http")) {
        continue;
      }
      const url = new URL(src, base);
      node.setAttribute("rel", "noopener noreferrer");
      node.setAttribute("src", url.toString());
    }
  }
  return node;
}

export function jumpToQuoteTag(union_id: string, highlight_number: number) {
  nextTick(() => {
    const node = document.querySelector(
      '.J-side-pannel [data-citation-id="' + union_id + '"]'
    ) as HTMLDivElement;
    if (!node) {
      return;
    }
    const parent = document.querySelector(
      ".J-copilot-wrapper"
    ) as HTMLDivElement;
    const quotes = node.getElementsByTagName("quotes");

    for (let i = 0; i < quotes.length; i++) {
      const quote = quotes[i];
      quote.classList.remove("highlight");
      if (quote.getAttribute("data-number") === `${highlight_number}`) {
        quote.classList.add("highlight");
      }
    }

    const quote = parent.querySelector("quotes.highlight") as HTMLElement;

    if (!quote) {
      parent.scrollTop = 0;
    } else {
      redirectToQuote(quote);
    }
  });
}

export function redirectToQuote(quote: HTMLElement) {
  const parent = document.querySelector(".J-copilot-wrapper") as HTMLDivElement;
  const rect = quote.getBoundingClientRect();
  const contentRect = parent.getBoundingClientRect();
  parent.scrollTop = rect.top - contentRect.top + parent.scrollTop - 50;
}
