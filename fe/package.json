{"name": "django-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cross-env endpoint=http://127.0.0.1:8000/ vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.7", "cross-env": "^7.0.3", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "^2.5.3", "gsap": "^3.12.5", "idb": "^8.0.0", "rxjs": "^7.8.1", "unplugin-element-plus": "^0.8.0", "vue": "^3.4.15"}, "devDependencies": {"@types/node": "^20.11.10", "@vitejs/plugin-vue": "^5.0.3", "sass": "^1.70.0", "sass-loader": "^14.1.0", "typescript": "^5.2.2", "vite": "^5.0.12", "vite-svg-loader": "^5.1.0", "vue-tsc": "^1.8.27"}}