"""
Model system module
- Provides model training and evaluation functionality
- Supports different types of models
- Handles model training, evaluation, and deployment
"""

# Import from data_system for backward compatibility
from gbs.data_system.dataset.portfolio_dataset import PortfolioDataset

# For backward compatibility
from gbs.model_system.utils import slice_df

# Export functions
from_config = PortfolioDataset.from_config
DataModule = PortfolioDataset
prepare_window_data = slice_df