"""
Utility functions for model system
- Data processing utilities
- Visualization utilities
- Training utilities
"""

# 从新的位置导入函数
from gbs.data_system.base.dataset.utils import slice_df
# 注释掉不存在的导入
# from gbs.workflow.task.utils import create_time_windows
# from gbs.workflow.task.gen import split_by_date, slice_df_by_window
from .visualization import plot_training_history, plot_weight_distribution
# 从 portfolio_transformer 导入数据处理工具
from ..zoo.portfolio_transformer import tensor_to_device, ensure_tensor_format, handle_variable_batch, PortfolioDataParallel

# 定义缺失的函数
def create_time_windows(segments, windows):
    """
    创建时间窗口的简单实现
    """
    return segments

def split_by_date(df, date_field, date_range):
    """
    按日期分割数据的简单实现
    """
    return df

def slice_df_by_window(df, window):
    """
    按窗口切片数据的简单实现
    """
    return df

__all__ = [
    'slice_df', 'create_time_windows', 'split_by_date', 'slice_df_by_window',
    'tensor_to_device', 'ensure_tensor_format', 'handle_variable_batch', 'PortfolioDataParallel',
    'plot_training_history', 'plot_weight_distribution'
]
