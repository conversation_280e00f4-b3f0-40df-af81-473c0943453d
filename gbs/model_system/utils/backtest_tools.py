#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测工具模块 - 包含从portfolio_transformer移出的回测方法
- 包括回测、权重分析和结果保存功能
"""

import os
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from gbs.core.utils.loki_logger import get_loki_logger

# 获取日志记录器
log = get_loki_logger(__name__).logger

def backtest_portfolio_model(model, test_loader, return_weights=False):
    """
    对投资组合模型进行回测

    Args:
        model: 已训练的模型
        test_loader: 测试数据加载器
        return_weights: 是否返回权重

    Returns:
        portfolio_returns: 投资组合收益
        test_returns: 测试收益
        turnovers: 换手率
        detailed_weights: 详细权重（如果return_weights=True）
    """
    if not hasattr(model, 'is_fitted') or not model.is_fitted:
        raise ValueError("模型尚未训练，请先调用fit方法")

    device = model.device if hasattr(model, 'device') else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    portfolio_returns = []
    test_returns = []
    turnovers = []
    detailed_weights = []

    with torch.no_grad():
        prev_weights = None

        for batch_idx, (X_batch, R_batch) in enumerate(test_loader):
            # 将数据移动到设备
            X_batch = X_batch.to(device)
            R_batch_device = R_batch.to(device)
            R_batch_cpu = R_batch.cpu().numpy()

            # 前向传播
            weights = model(X_batch)

            # 计算投资组合收益
            portfolio_returns_batch = torch.sum(weights * R_batch_device, dim=1)
            batch_returns = portfolio_returns_batch.mean().item()

            # 计算换手率
            weights_np = weights.cpu().numpy()
            if prev_weights is not None:
                batch_turnover = np.sum(np.abs(weights_np - prev_weights)) / 2.0
                turnovers.append(batch_turnover * 100.0)  # 转换为百分比

            # 更新前一个权重
            prev_weights = weights_np

            # 记录收益
            portfolio_returns.append(batch_returns)

            # 记录测试收益
            test_returns.append(R_batch_cpu.mean())

            # 如果请求，记录权重
            if return_weights:
                detailed_weights.append(weights_np)

    # 返回结果
    if return_weights:
        return portfolio_returns, test_returns, turnovers, detailed_weights
    else:
        return portfolio_returns, test_returns, turnovers


def analyze_weights(model, sample_size=100):
    """
    分析模型的权重分布

    Args:
        model: 已训练的模型
        sample_size: 用于测试的样本大小

    Returns:
        dict: 包含权重分析结果的字典
    """
    try:
        # 确保模型在评估模式
        model.eval()

        # 获取设备
        device = model.device if hasattr(model, 'device') else torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 创建一个随机样本进行测试
        input_dim = 127 if not hasattr(model, 'input_dim') or model.input_dim is None else model.input_dim
        X = torch.randn(sample_size, input_dim).to(device)

        # 前向传播
        with torch.no_grad():
            weights = model(X)

        # 转换为numpy进行分析
        if isinstance(weights, list):
            weights = weights[0]  # 使用第一个样本
        weights = weights.cpu().numpy()

        # 排序权重
        sorted_weights = np.sort(weights)[::-1]  # 降序排序

        # 计算统计数据
        top10_weights = sorted_weights[:10]
        top10_sum = np.sum(top10_weights)
        max_weight = np.max(sorted_weights)
        min_weight = np.min(sorted_weights)

        # 计算集中度（赫芬达尔指数）
        herfindahl = np.sum(sorted_weights**2)

        # 创建前5个权重的列表
        top5_weights = []
        for i in range(5):
            if i < len(sorted_weights):
                top5_weights.append({
                    'rank': i+1,
                    'weight': sorted_weights[i],
                    'percentage': sorted_weights[i]*100
                })

        # 打印结果
        log.info("\n权重分布分析:")
        log.info(f"  前10只股票权重总和: {top10_sum:.4f} ({top10_sum*100:.2f}%)")
        log.info(f"  最大权重: {max_weight:.4f} ({max_weight*100:.2f}%)")
        log.info(f"  最小权重: {min_weight:.4f} ({min_weight*100:.2f}%)")
        log.info(f"  权重集中度 (赫芬达尔指数): {herfindahl:.4f}")

        # 打印前5个权重
        log.info("  前5个权重:")
        for i in range(5):
            if i < len(sorted_weights):
                log.info(f"    #{i+1}: {sorted_weights[i]:.6f} ({sorted_weights[i]*100:.2f}%)")

        # 返回分析结果
        return {
            'top10_sum': top10_sum,
            'max_weight': max_weight,
            'min_weight': min_weight,
            'herfindahl': herfindahl,
            'top5_weights': top5_weights
        }
    except Exception as error:
        log.error(f"分析权重时出错: {error}")
        return None


def run_backtest(model, test_data, config, save_artifacts=False, plots_dir=None, compare_full_portfolio=False):
    """
    运行回测并返回结果

    Args:
        model: 已训练的模型
        test_data: 测试数据
        config: 配置字典
        save_artifacts: 是否保存结果
        plots_dir: 图表保存目录
        compare_full_portfolio: 是否与完整投资组合比较

    Returns:
        backtest_result: 回测结果
        metrics: 指标字典
        results: 详细结果字典
    """
    log.info("开始回测...")

    # 创建测试数据加载器
    test_loader = torch.utils.data.DataLoader(
        test_data,
        batch_size=config.get('batch_size', 32),
        shuffle=False
    )

    # 运行回测
    portfolio_returns, test_returns, turnovers, weights = backtest_portfolio_model(
        model,
        test_loader,
        return_weights=True
    )

    # 计算指标
    avg_portfolio_return = np.mean(portfolio_returns)
    portfolio_std = np.std(portfolio_returns)
    sharpe_ratio = avg_portfolio_return / portfolio_std if portfolio_std > 0 else 0
    avg_turnover = np.mean(turnovers) if turnovers else 0

    # 分析权重
    weight_analysis = analyze_weights(model)

    # 创建指标字典
    metrics = {
        'mean_return': avg_portfolio_return,
        'std_return': portfolio_std,
        'sharpe_ratio': sharpe_ratio,
        'avg_turnover': avg_turnover,
        'max_weight': weight_analysis.get('max_weight', 0) if weight_analysis else 0,
        'min_weight': weight_analysis.get('min_weight', 0) if weight_analysis else 0,
        'top10_weight_sum': weight_analysis.get('top10_sum', 0) if weight_analysis else 0,
        'herfindahl_index': weight_analysis.get('herfindahl', 0) if weight_analysis else 0
    }

    # 打印结果
    log.info(f"平均投资组合收益: {avg_portfolio_return:.6f}")
    log.info(f"投资组合标准差: {portfolio_std:.6f}")
    log.info(f"夏普比率: {sharpe_ratio:.6f}")
    log.info(f"平均换手率: {avg_turnover:.2f}%")

    # 绘制累积收益图
    if save_artifacts and plots_dir:
        # 计算累积收益
        portfolio_cumulative = np.cumprod(1 + np.array(portfolio_returns)) - 1
        market_cumulative = np.cumprod(1 + np.array(test_returns)) - 1

        # 创建图表
        plt.figure(figsize=(10, 6))
        plt.plot(portfolio_cumulative, label=f'Portfolio (Sharpe: {sharpe_ratio:.2f})')
        plt.plot(market_cumulative, label='Market')
        plt.title('Cumulative Returns')
        plt.xlabel('Trading Days')
        plt.ylabel('Cumulative Return')
        plt.legend()
        plt.grid(True)

        # 保存图表
        returns_path = os.path.join(plots_dir, 'cumulative_returns.png')
        plt.savefig(returns_path)
        plt.close()
        log.info(f"累积收益图已保存到: {returns_path}")

    # 返回结果
    results = {
        'portfolio_returns': portfolio_returns,
        'test_returns': test_returns,
        'turnovers': turnovers,
        'weights': weights,
        'weight_analysis': weight_analysis
    }

    return portfolio_returns, metrics, results


def save_backtest_artifacts(model, metrics, detailed_weights, experiments_dir, artifacts_dir, timestamp):
    """
    保存回测结果和模型

    Args:
        model: 已训练的模型
        metrics: 指标字典
        detailed_weights: 详细权重
        experiments_dir: 实验目录
        artifacts_dir: 部署目录
        timestamp: 时间戳
    """
    try:
        # 保存模型
        model_path = os.path.join(artifacts_dir, 'model.pt')
        torch.save(model.state_dict(), model_path)
        log.info(f"模型已保存到: {model_path}")

        # 保存指标
        metrics_path = os.path.join(experiments_dir, 'metrics', 'backtest_metrics.csv')
        metrics_df = pd.DataFrame([metrics])
        metrics_df.to_csv(metrics_path, index=False)
        log.info(f"回测指标已保存到: {metrics_path}")

        # 保存样本权重
        if detailed_weights and len(detailed_weights) > 0:
            # 取第一个批次的权重作为样本
            sample_weights = detailed_weights[0]
            weights_df = pd.DataFrame(sample_weights)
            weights_path = os.path.join(experiments_dir, 'sample_weights.csv')
            weights_df.to_csv(weights_path, index=False)
            log.info(f"样本权重已保存到: {weights_path}")

        return True
    except Exception as e:
        log.error(f"保存回测结果时出错: {e}")
        return False
