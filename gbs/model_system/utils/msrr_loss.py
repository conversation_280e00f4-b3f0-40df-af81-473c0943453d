"""
Maximum Sharpe Ratio Regression (MSRR) loss functions implementation
Based on <PERSON> et al. "Artificial Intelligence Asset Pricing Models"

These loss functions directly optimize the portfolio's Sharpe ratio, rather than simply predicting returns.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class MSRRLoss(nn.Module):
    """
    Maximum Sharpe Ratio Regression (MSRR) loss function

    Based on <PERSON> et al.'s theory, the loss function takes the form:
    L(Θ) = E_t[(1 - w(X_t; Θ)′ R_t+1)^2] + z * ||w||²

    where:
    - w(X_t; Θ) is the portfolio weights generated by the model
    - R_t+1 is the vector of asset returns
    - z is the regularization strength parameter
    """

    def __init__(self, reg_strength=0.01, eps=1e-8):
        """
        Initialize MSRR loss function

        Args:
            reg_strength: Regularization strength (z)
            eps: Small constant to prevent numerical instability
        """
        super(MSRRLoss, self).__init__()
        self.reg_strength = reg_strength
        self.eps = eps
        # Register a dummy parameter to get the device
        self.dummy_param = nn.Parameter(torch.zeros(1))

    def forward(self, weights, returns):
        """
        Calculate MSRR loss

        Args:
            weights: Portfolio weights generated by the model, shape [batch_size, num_assets]
            returns: Asset returns, shape [batch_size, num_assets]

        Returns:
            loss: MSRR loss value
        """
        # Handle batch and non-batch cases
        if not isinstance(weights, list) and not isinstance(returns, list):
            # Standard batch processing
            return self._compute_loss(weights, returns)

        # Handle variable-length sequences (different number of stocks)
        total_loss = 0.0
        valid_batches = 0

        # Print debug info
        print(f"MSRRLoss - weights type: {type(weights)}, length: {len(weights) if isinstance(weights, list) else 'N/A'}")
        print(f"MSRRLoss - returns type: {type(returns)}, length: {len(returns) if isinstance(returns, list) else 'N/A'}")

        # Handle the case where weights is a list but returns is not
        if isinstance(weights, list) and not isinstance(returns, list):
            # Convert returns to a list with the same length as weights
            returns = [returns] * len(weights)
            print(f"MSRRLoss - Converted returns to list with length {len(returns)}")

        # Handle the case where returns is a list but weights is not
        if not isinstance(weights, list) and isinstance(returns, list):
            # Convert weights to a list with the same length as returns
            weights = [weights] * len(returns)
            print(f"MSRRLoss - Converted weights to list with length {len(weights)}")

        # Ensure both are lists
        if not isinstance(weights, list):
            weights = [weights]
            print(f"MSRRLoss - Converted weights to list with length 1")
        if not isinstance(returns, list):
            returns = [returns]
            print(f"MSRRLoss - Converted returns to list with length 1")

        # Get the minimum length
        batch_size = min(len(weights), len(returns))
        print(f"MSRRLoss - Using batch_size = {batch_size}")

        for i in range(batch_size):
            w_i = weights[i]
            r_i = returns[i]

            # Handle the case where w_i is a list
            if isinstance(w_i, list):
                try:
                    # Check if the list contains tensors
                    if len(w_i) > 0 and isinstance(w_i[0], torch.Tensor):
                        # If it's a list of tensors, use the first one
                        device = returns[0].device if hasattr(returns[0], 'device') else 'cpu'
                        w_i = w_i[0].to(device)
                    else:
                        # Try to convert list to tensor
                        w_i = torch.tensor(w_i, device=returns[0].device if hasattr(returns[0], 'device') else 'cpu')
                except Exception as e:
                    print(f"Warning: Could not convert weights list to tensor: {e}")
                    continue

            # Handle the case where r_i is a list
            if isinstance(r_i, list):
                try:
                    # Check if the list contains tensors
                    if len(r_i) > 0 and isinstance(r_i[0], torch.Tensor):
                        # If it's a list of tensors, use the first one
                        device = w_i.device if hasattr(w_i, 'device') else 'cpu'
                        r_i = r_i[0].to(device)
                    else:
                        # Try to convert list to tensor
                        r_i = torch.tensor(r_i, device=w_i.device if hasattr(w_i, 'device') else 'cpu')
                except Exception as e:
                    print(f"Warning: Could not convert returns list to tensor: {e}")
                    continue

            # Skip if either is not a tensor
            if not isinstance(w_i, torch.Tensor) or not isinstance(r_i, torch.Tensor):
                print(f"Warning: Skipping non-tensor inputs. w_i: {type(w_i)}, r_i: {type(r_i)}")
                continue

            # Ensure dimensions match
            if w_i.dim() != r_i.dim():
                print(f"MSRRLoss - Dimension mismatch. w_i: {w_i.dim()}, r_i: {r_i.dim()}")
                # Handle different dimension cases
                if w_i.dim() == 2 and r_i.dim() == 1:
                    # If w_i is [batch_size, num_assets] and r_i is [num_assets]
                    if w_i.shape[1] == r_i.shape[0]:
                        # Create copies of r_i for each batch item
                        r_i = r_i.unsqueeze(0).expand(w_i.shape[0], -1)
                    else:
                        # If dimensions don't match, try to use common dimensions
                        min_dim = min(w_i.shape[1], r_i.shape[0])
                        w_i = w_i[:, :min_dim]
                        r_i = r_i[:min_dim].unsqueeze(0).expand(w_i.shape[0], -1)
                elif w_i.dim() == 1 and r_i.dim() == 2:
                    # If w_i is [num_assets] and r_i is [batch_size, num_assets]
                    if w_i.shape[0] == r_i.shape[1]:
                        # Create copies of w_i for each batch item
                        w_i = w_i.unsqueeze(0).expand(r_i.shape[0], -1)
                    else:
                        # If dimensions don't match, try to use common dimensions
                        min_dim = min(w_i.shape[0], r_i.shape[1])
                        w_i = w_i[:min_dim].unsqueeze(0).expand(r_i.shape[0], -1)
                        r_i = r_i[:, :min_dim]
                else:
                    # For other cases, use standard approach
                    if w_i.dim() == 1:
                        w_i = w_i.unsqueeze(0)
                    if r_i.dim() == 1:
                        r_i = r_i.unsqueeze(0)

            # May need to adjust shapes for batch dimension matching
            if w_i.shape[0] != r_i.shape[0]:
                # Try to broadcast to match batch dimensions
                if w_i.shape[0] == 1:
                    w_i = w_i.expand(r_i.shape[0], -1)
                elif r_i.shape[0] == 1:
                    r_i = r_i.expand(w_i.shape[0], -1)
                else:
                    # If can't broadcast, use minimum batch size
                    min_batch = min(w_i.shape[0], r_i.shape[0])
                    w_i = w_i[:min_batch]
                    r_i = r_i[:min_batch]

            # Adjust feature dimensions if needed
            if w_i.shape[1] != r_i.shape[1]:
                min_dim = min(w_i.shape[1], r_i.shape[1])
                w_i = w_i[:, :min_dim]
                r_i = r_i[:, :min_dim]

            try:
                loss_i = self._compute_loss(w_i, r_i)
                total_loss += loss_i
                valid_batches += 1
            except Exception as e:
                print(f"Warning: Error computing loss for batch {i}: {e}")

        # Return average loss, or a default value if no valid batches
        if valid_batches > 0:
            return total_loss / valid_batches
        else:
            print("Warning: No valid batches for loss computation")
            # Return a default loss value that can be backpropagated
            return torch.tensor(1.0, requires_grad=True, device=self.dummy_param.device)

    def _compute_loss(self, weights, returns):
        """
        Compute MSRR loss for a single batch

        Args:
            weights: Portfolio weights [batch_size, num_assets]
            returns: Asset returns [batch_size, num_assets]
        """
        # Ensure correct dimensions
        if weights.dim() == 1:
            weights = weights.unsqueeze(0)
        if returns.dim() == 1:
            returns = returns.unsqueeze(0)

        # Calculate portfolio returns w'R
        portfolio_returns = torch.sum(weights * returns, dim=1)

        # Calculate SDF: (1 - w'R)
        sdf = 1.0 - portfolio_returns

        # Calculate pricing error term: E[(1 - w'R)²]
        pricing_error = torch.mean(sdf**2)

        # Calculate weight regularization term: z * ||w||²
        weight_reg = self.reg_strength * torch.mean(torch.sum(weights**2, dim=1))

        # Total loss
        loss = pricing_error + weight_reg

        return loss

    def compute_metrics(self, weights, returns):
        """
        Calculate and return key metrics for the portfolio

        Args:
            weights: Portfolio weights [batch_size, num_assets]
            returns: Asset returns [batch_size, num_assets]

        Returns:
            metrics: Dictionary containing Sharpe ratio, returns, and volatility
        """
        # Handle batch and non-batch cases
        if isinstance(weights, list) or isinstance(returns, list):
            # Simplify by only calculating for the first element
            weights = weights[0] if isinstance(weights, list) else weights
            returns = returns[0] if isinstance(returns, list) else returns

        # Ensure correct dimensions
        if weights.dim() == 1:
            weights = weights.unsqueeze(0)
        if returns.dim() == 1:
            returns = returns.unsqueeze(0)

        # Calculate portfolio returns
        portfolio_returns = torch.sum(weights * returns, dim=1)

        # Calculate mean return
        mean_return = torch.mean(portfolio_returns)

        # Calculate volatility (standard deviation)
        std_return = torch.std(portfolio_returns) + self.eps

        # Calculate Sharpe ratio (simplified, without subtracting risk-free rate)
        sharpe_ratio = mean_return / std_return

        # Calculate maximum allocation weight (measure of concentration)
        max_weight = torch.max(torch.abs(weights))

        # Calculate weight concentration (inverse of Herfindahl index)
        weight_concentration = torch.mean(torch.sum(weights**2, dim=1))

        return {
            'sharpe_ratio': sharpe_ratio.item(),
            'mean_return': mean_return.item(),
            'volatility': std_return.item(),
            'max_weight': max_weight.item(),
            'weight_concentration': weight_concentration.item()
        }


# Constrained MSRR loss function
class ConstrainedMSRRLoss(MSRRLoss):
    """
    MSRR loss function with diversification constraints

    Adds to the basic MSRR loss function:
    1. Soft constraint for weights summing to 1
    2. Non-negative weights constraint
    3. Concentration penalty
    """

    def __init__(self, reg_strength=0.01, sum_penalty=1.0,
                 neg_penalty=1.0, concentration_penalty=0.1, eps=1e-8):
        """
        Initialize constrained MSRR loss function

        Args:
            reg_strength: L2 regularization strength for weights
            sum_penalty: Weight sum constraint penalty strength
            neg_penalty: Negative weight penalty strength
            concentration_penalty: Concentration penalty strength
            eps: Small constant for numerical stability
        """
        super(ConstrainedMSRRLoss, self).__init__(reg_strength=reg_strength, eps=eps)
        self.sum_penalty = sum_penalty
        self.neg_penalty = neg_penalty
        self.concentration_penalty = concentration_penalty

    def _compute_loss(self, weights, returns):
        """
        Compute constrained MSRR loss
        """
        # Base MSRR loss
        base_loss = super()._compute_loss(weights, returns)

        # Soft constraint for weights summing to 1: (sum(w) - 1)²
        sum_constraint = self.sum_penalty * torch.mean((torch.sum(weights, dim=1) - 1.0)**2)

        # Non-negative constraint: sum(ReLU(-w))
        non_negative = self.neg_penalty * torch.mean(torch.sum(F.relu(-weights), dim=1))

        # Concentration penalty (based on Herfindahl index): sum(w²)
        # Higher values indicate higher concentration, lower values indicate diversification
        concentration = self.concentration_penalty * torch.mean(torch.sum(weights**2, dim=1))

        # Total loss
        total_loss = base_loss + sum_constraint + non_negative + concentration

        return total_loss


# Additional: Risk-averse MSRR loss function
class RiskAverseMSRRLoss(MSRRLoss):
    """
    MSRR loss function with risk aversion parameter

    Allows adjusting risk-return preference:
    L(Θ) = E_t[(1 - w(X_t; Θ)′ R_t+1)^2] + z * ||w||² + gamma * Var(w'R)
    """

    def __init__(self, reg_strength=0.01, risk_aversion=1.0, eps=1e-8):
        """
        Initialize risk-averse MSRR loss function

        Args:
            reg_strength: Regularization strength
            risk_aversion: Risk aversion coefficient (gamma)
            eps: Numerical stability constant
        """
        super(RiskAverseMSRRLoss, self).__init__(reg_strength=reg_strength, eps=eps)
        self.risk_aversion = risk_aversion

    def _compute_loss(self, weights, returns):
        """
        Compute risk-averse MSRR loss
        """
        # Base MSRR loss
        base_loss = super()._compute_loss(weights, returns)

        # Calculate portfolio returns
        portfolio_returns = torch.sum(weights * returns, dim=1)

        # Calculate portfolio return variance
        portfolio_variance = torch.var(portfolio_returns) + self.eps

        # Risk aversion term
        risk_penalty = self.risk_aversion * portfolio_variance

        # Total loss
        total_loss = base_loss + risk_penalty

        return total_loss
