"""
Visualization utilities for model training
- Training history plotting
- Weight distribution visualization
"""

import matplotlib.pyplot as plt
import numpy as np
import os
from typing import List, Dict, Any, Optional, Tuple

def plot_training_history(
    train_losses: List[float], 
    val_losses: Optional[List[float]] = None,
    batch_losses: Optional[List[float]] = None,
    gradient_norms: Optional[List[float]] = None,
    clipped_gradient_norms: Optional[List[float]] = None,
    top_weights_history: Optional[List[Dict[str, Any]]] = None,
    save_dir: Optional[str] = None
) -> None:
    """
    Plot training history
    
    Args:
        train_losses: Training losses per epoch
        val_losses: Validation losses per epoch
        batch_losses: Batch losses
        gradient_norms: Gradient norms per batch
        clipped_gradient_norms: Clipped gradient norms per batch
        top_weights_history: History of top weights
        save_dir: Directory to save plots
    """
    # Create save directory if needed
    if save_dir and not os.path.exists(save_dir):
        os.makedirs(save_dir, exist_ok=True)
    
    # Create figure
    plt.figure(figsize=(12, 8))
    
    # Plot training and validation loss
    plt.subplot(2, 2, 1)
    plt.plot(train_losses, label='Train Loss')
    if val_losses:
        plt.plot(val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)
    
    # Plot batch loss
    if batch_losses:
        plt.subplot(2, 2, 2)
        plt.plot(batch_losses)
        plt.xlabel('Batch')
        plt.ylabel('Loss')
        plt.title('Batch Loss')
        plt.grid(True)
    
    # Plot gradient norms
    if gradient_norms and clipped_gradient_norms:
        plt.subplot(2, 2, 3)
        plt.plot(gradient_norms, label='Original')
        plt.plot(clipped_gradient_norms, label='Clipped')
        plt.xlabel('Batch')
        plt.ylabel('Gradient Norm')
        plt.title('Gradient Norms')
        plt.legend()
        plt.grid(True)
    
    # Plot top weights history
    if top_weights_history:
        plt.subplot(2, 2, 4)
        epochs = [entry['epoch'] for entry in top_weights_history]
        top_weights = np.array([entry['weights'][0] for entry in top_weights_history])
        plt.plot(epochs, top_weights)
        plt.xlabel('Epoch')
        plt.ylabel('Top Weight')
        plt.title('Top Weight Evolution')
        plt.grid(True)
    
    plt.tight_layout()
    
    # Save or show plot
    if save_dir:
        plt.savefig(os.path.join(save_dir, 'training_history.png'))
        plt.close()
    else:
        plt.show()

def plot_weight_distribution(
    weights: np.ndarray,
    title: str = 'Weight Distribution',
    save_path: Optional[str] = None
) -> None:
    """
    Plot weight distribution
    
    Args:
        weights: Portfolio weights
        title: Plot title
        save_path: Path to save plot
    """
    # Flatten weights if multi-dimensional
    if weights.ndim > 1:
        weights = weights.flatten()
    
    # Sort weights in descending order
    sorted_weights = np.sort(weights)[::-1]
    
    # Create figure
    plt.figure(figsize=(10, 6))
    
    # Plot weight distribution
    plt.bar(range(len(sorted_weights)), sorted_weights)
    plt.xlabel('Asset Rank')
    plt.ylabel('Weight')
    plt.title(title)
    plt.grid(True, axis='y')
    
    # Add statistics as text
    stats_text = (
        f"Max Weight: {np.max(weights):.4f}\n"
        f"Min Weight: {np.min(weights):.4f}\n"
        f"Mean Weight: {np.mean(weights):.4f}\n"
        f"Std Dev: {np.std(weights):.4f}\n"
        f"Top 10 Sum: {np.sum(sorted_weights[:10]):.4f}"
    )
    plt.figtext(0.15, 0.15, stats_text, bbox=dict(facecolor='white', alpha=0.8))
    
    # Save or show plot
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()
