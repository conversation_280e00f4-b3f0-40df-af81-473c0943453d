#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
集成模型实现
- 简单集成模型
- 堆叠集成模型
"""

import numpy as np
import pandas as pd
import torch
from typing import Dict, List, Union, Tuple, Optional, Any, Callable

from .base.base_model import BaseModel

class EnsembleModel(BaseModel):
    """
    集成模型基类
    """
    
    def __init__(self, name: str, models: List[BaseModel], weights: Optional[List[float]] = None, **model_params):
        """
        初始化集成模型
        
        Args:
            name: 模型名称
            models: 基础模型列表
            weights: 模型权重列表（如果为None，则使用平均权重）
            **model_params: 其他模型参数
        """
        super().__init__(name, **model_params)
        self.models = models
        
        # 设置权重
        if weights is None:
            self.weights = [1.0 / len(models)] * len(models)
        else:
            if len(weights) != len(models):
                raise ValueError(f"权重数量 ({len(weights)}) 必须与模型数量 ({len(models)}) 相同")
            # 归一化权重
            total = sum(weights)
            self.weights = [w / total for w in weights]
    
    def fit(self, X: Union[pd.DataFrame, List], y: Optional[Union[pd.Series, pd.DataFrame, List]] = None, **kwargs) -> 'EnsembleModel':
        """
        训练集成模型
        
        Args:
            X: 特征或训练数据
            y: 目标值
            **kwargs: 额外训练参数
            
        Returns:
            self: 训练后的模型实例
        """
        # 训练每个基础模型
        for i, model in enumerate(self.models):
            print(f"训练模型 {i+1}/{len(self.models)}: {model.name}")
            model.fit(X, y, **kwargs)
        
        self.is_fitted = True
        return self
    
    def predict(self, X: Union[pd.DataFrame, List, np.ndarray]) -> Union[np.ndarray, pd.Series, pd.DataFrame]:
        """
        使用集成模型进行预测
        
        Args:
            X: 特征或测试数据
            
        Returns:
            预测结果
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit方法")
        
        # 获取每个模型的预测结果
        predictions = []
        for model in self.models:
            pred = model.predict(X)
            
            # 转换为numpy数组
            if isinstance(pred, pd.Series) or isinstance(pred, pd.DataFrame):
                pred = pred.values
            
            predictions.append(pred)
        
        # 加权平均预测结果
        weighted_sum = np.zeros_like(predictions[0], dtype=float)
        for i, pred in enumerate(predictions):
            weighted_sum += self.weights[i] * pred
        
        return weighted_sum
    
    def save(self, path: str) -> None:
        """
        保存集成模型
        
        Args:
            path: 保存路径
        """
        import os
        import pickle
        
        # 创建目录
        os.makedirs(os.path.dirname(os.path.abspath(path)), exist_ok=True)
        
        # 保存模型信息
        model_info = {
            'name': self.name,
            'weights': self.weights,
            'model_params': self.model_params,
            'is_fitted': self.is_fitted
        }
        
        # 保存每个基础模型
        model_paths = []
        for i, model in enumerate(self.models):
            model_path = f"{os.path.splitext(path)[0]}_model_{i}.pt"
            model.save(model_path)
            model_paths.append(model_path)
        
        model_info['model_paths'] = model_paths
        
        # 保存模型信息
        with open(path, 'wb') as f:
            pickle.dump(model_info, f)
    
    @classmethod
    def load(cls, path: str, **kwargs) -> 'EnsembleModel':
        """
        加载集成模型
        
        Args:
            path: 模型路径
            **kwargs: 额外参数
            
        Returns:
            加载的模型实例
        """
        import pickle
        from .model_factory import load_model_from_file
        
        # 加载模型信息
        with open(path, 'rb') as f:
            model_info = pickle.load(f)
        
        # 加载每个基础模型
        models = []
        for model_path in model_info['model_paths']:
            # 从文件名推断模型类型
            if 'ImprovedPortfolioTransformer' in model_path:
                model_type = 'ImprovedPortfolioTransformer'
            elif 'Linear' in model_path:
                model_type = 'Linear'
            else:
                model_type = kwargs.get('model_type', 'ImprovedPortfolioTransformer')
            
            model = load_model_from_file(model_type, model_path)
            models.append(model)
        
        # 创建集成模型实例
        ensemble = cls(
            name=model_info['name'],
            models=models,
            weights=model_info['weights'],
            **model_info['model_params']
        )
        
        ensemble.is_fitted = model_info['is_fitted']
        return ensemble

class StackingModel(BaseModel):
    """
    堆叠集成模型
    """
    
    def __init__(self, name: str, base_models: List[BaseModel], meta_model: BaseModel, **model_params):
        """
        初始化堆叠集成模型
        
        Args:
            name: 模型名称
            base_models: 基础模型列表
            meta_model: 元模型（用于组合基础模型的预测）
            **model_params: 其他模型参数
        """
        super().__init__(name, **model_params)
        self.base_models = base_models
        self.meta_model = meta_model
        self.is_fitted = False
    
    def fit(self, X: Union[pd.DataFrame, List], y: Optional[Union[pd.Series, pd.DataFrame, List]] = None, **kwargs) -> 'StackingModel':
        """
        训练堆叠集成模型
        
        Args:
            X: 特征或训练数据
            y: 目标值
            **kwargs: 额外训练参数，包括:
                - cv: 交叉验证折数（默认为5）
                - use_features: 是否在元模型中使用原始特征（默认为False）
            
        Returns:
            self: 训练后的模型实例
        """
        from sklearn.model_selection import KFold
        
        if y is None:
            raise ValueError("堆叠模型训练需要目标值y")
        
        # 获取参数
        cv = kwargs.get('cv', 5)
        use_features = kwargs.get('use_features', False)
        
        # 转换为numpy数组
        if isinstance(X, pd.DataFrame):
            X_values = X.values
        else:
            X_values = np.array(X)
            
        if isinstance(y, (pd.Series, pd.DataFrame)):
            y_values = y.values
        else:
            y_values = np.array(y)
        
        # 创建交叉验证折叠
        kf = KFold(n_splits=cv, shuffle=True, random_state=42)
        
        # 为元模型准备训练数据
        meta_features = np.zeros((X_values.shape[0], len(self.base_models)))
        
        # 训练每个基础模型并生成元特征
        for i, model in enumerate(self.base_models):
            print(f"训练基础模型 {i+1}/{len(self.base_models)}: {model.name}")
            
            # 使用交叉验证生成元特征
            for train_idx, val_idx in kf.split(X_values):
                # 分割数据
                X_train, X_val = X_values[train_idx], X_values[val_idx]
                y_train = y_values[train_idx]
                
                # 转换为DataFrame/Series（如果原始输入是DataFrame/Series）
                if isinstance(X, pd.DataFrame):
                    X_train_df = pd.DataFrame(X_train, columns=X.columns)
                    X_val_df = pd.DataFrame(X_val, columns=X.columns)
                else:
                    X_train_df, X_val_df = X_train, X_val
                    
                if isinstance(y, (pd.Series, pd.DataFrame)):
                    y_train_series = pd.Series(y_train, name=y.name if hasattr(y, 'name') else None)
                else:
                    y_train_series = y_train
                
                # 训练模型
                model.fit(X_train_df, y_train_series, **kwargs)
                
                # 生成验证集预测
                val_pred = model.predict(X_val_df)
                
                # 存储预测结果
                if isinstance(val_pred, (pd.Series, pd.DataFrame)):
                    val_pred = val_pred.values
                
                meta_features[val_idx, i] = val_pred.flatten()
        
        # 使用全部数据重新训练基础模型
        for i, model in enumerate(self.base_models):
            print(f"使用全部数据重新训练基础模型 {i+1}/{len(self.base_models)}: {model.name}")
            model.fit(X, y, **kwargs)
        
        # 准备元模型的训练数据
        if use_features:
            # 将原始特征与元特征组合
            if isinstance(X, pd.DataFrame):
                meta_X = pd.DataFrame(
                    np.hstack([X_values, meta_features]),
                    columns=list(X.columns) + [f'model_{i}' for i in range(len(self.base_models))]
                )
            else:
                meta_X = np.hstack([X_values, meta_features])
        else:
            # 仅使用元特征
            if isinstance(X, pd.DataFrame):
                meta_X = pd.DataFrame(
                    meta_features,
                    columns=[f'model_{i}' for i in range(len(self.base_models))]
                )
            else:
                meta_X = meta_features
        
        # 训练元模型
        print(f"训练元模型: {self.meta_model.name}")
        self.meta_model.fit(meta_X, y, **kwargs)
        
        self.is_fitted = True
        return self
    
    def predict(self, X: Union[pd.DataFrame, List, np.ndarray]) -> Union[np.ndarray, pd.Series, pd.DataFrame]:
        """
        使用堆叠集成模型进行预测
        
        Args:
            X: 特征或测试数据
            
        Returns:
            预测结果
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit方法")
        
        # 转换为numpy数组
        if isinstance(X, pd.DataFrame):
            X_values = X.values
        else:
            X_values = np.array(X)
        
        # 获取每个基础模型的预测结果
        meta_features = np.zeros((X_values.shape[0], len(self.base_models)))
        
        for i, model in enumerate(self.base_models):
            pred = model.predict(X)
            
            # 转换为numpy数组
            if isinstance(pred, (pd.Series, pd.DataFrame)):
                pred = pred.values
            
            meta_features[:, i] = pred.flatten()
        
        # 准备元模型的预测数据
        use_features = self.model_params.get('use_features', False)
        
        if use_features:
            # 将原始特征与元特征组合
            if isinstance(X, pd.DataFrame):
                meta_X = pd.DataFrame(
                    np.hstack([X_values, meta_features]),
                    columns=list(X.columns) + [f'model_{i}' for i in range(len(self.base_models))]
                )
            else:
                meta_X = np.hstack([X_values, meta_features])
        else:
            # 仅使用元特征
            if isinstance(X, pd.DataFrame):
                meta_X = pd.DataFrame(
                    meta_features,
                    columns=[f'model_{i}' for i in range(len(self.base_models))]
                )
            else:
                meta_X = meta_features
        
        # 使用元模型进行预测
        return self.meta_model.predict(meta_X)
    
    def save(self, path: str) -> None:
        """
        保存堆叠集成模型
        
        Args:
            path: 保存路径
        """
        import os
        import pickle
        
        # 创建目录
        os.makedirs(os.path.dirname(os.path.abspath(path)), exist_ok=True)
        
        # 保存模型信息
        model_info = {
            'name': self.name,
            'model_params': self.model_params,
            'is_fitted': self.is_fitted
        }
        
        # 保存每个基础模型
        base_model_paths = []
        for i, model in enumerate(self.base_models):
            model_path = f"{os.path.splitext(path)[0]}_base_{i}.pt"
            model.save(model_path)
            base_model_paths.append(model_path)
        
        # 保存元模型
        meta_model_path = f"{os.path.splitext(path)[0]}_meta.pt"
        self.meta_model.save(meta_model_path)
        
        model_info['base_model_paths'] = base_model_paths
        model_info['meta_model_path'] = meta_model_path
        
        # 保存模型信息
        with open(path, 'wb') as f:
            pickle.dump(model_info, f)
    
    @classmethod
    def load(cls, path: str, **kwargs) -> 'StackingModel':
        """
        加载堆叠集成模型
        
        Args:
            path: 模型路径
            **kwargs: 额外参数
            
        Returns:
            加载的模型实例
        """
        import pickle
        from .model_factory import load_model_from_file
        
        # 加载模型信息
        with open(path, 'rb') as f:
            model_info = pickle.load(f)
        
        # 加载每个基础模型
        base_models = []
        for model_path in model_info['base_model_paths']:
            # 从文件名推断模型类型
            if 'ImprovedPortfolioTransformer' in model_path:
                model_type = 'ImprovedPortfolioTransformer'
            elif 'Linear' in model_path:
                model_type = 'Linear'
            else:
                model_type = kwargs.get('model_type', 'ImprovedPortfolioTransformer')
            
            model = load_model_from_file(model_type, model_path)
            base_models.append(model)
        
        # 加载元模型
        meta_model_path = model_info['meta_model_path']
        if 'ImprovedPortfolioTransformer' in meta_model_path:
            meta_model_type = 'ImprovedPortfolioTransformer'
        elif 'Linear' in meta_model_path:
            meta_model_type = 'Linear'
        else:
            meta_model_type = kwargs.get('meta_model_type', 'Linear')
        
        meta_model = load_model_from_file(meta_model_type, meta_model_path)
        
        # 创建堆叠模型实例
        stacking = cls(
            name=model_info['name'],
            base_models=base_models,
            meta_model=meta_model,
            **model_info['model_params']
        )
        
        stacking.is_fitted = model_info['is_fitted']
        return stacking
