#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模型解释性接口
- 提供特征重要性分析
- 提供预测解释
- 支持可视化
"""

from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Union, Tuple, Optional, Any

class ModelInterpreter(ABC):
    """
    模型解释器基类
    """
    
    @abstractmethod
    def get_feature_importance(self) -> pd.Series:
        """
        获取特征重要性
        
        Returns:
            特征重要性，索引为特征名称，值为重要性分数
        """
        pass
    
    @abstractmethod
    def explain_prediction(self, X: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        解释特定预测
        
        Args:
            X: 输入特征
            **kwargs: 额外参数
            
        Returns:
            解释结果，包含各种解释信息
        """
        pass
    
    def plot_feature_importance(self, top_n: int = 10, figsize: Tuple[int, int] = (10, 6)) -> plt.Figure:
        """
        绘制特征重要性图
        
        Args:
            top_n: 显示前N个重要特征
            figsize: 图形大小
            
        Returns:
            matplotlib图形对象
        """
        importance = self.get_feature_importance()
        
        # 排序并获取前N个特征
        importance = importance.sort_values(ascending=False)
        if top_n > 0:
            importance = importance.head(top_n)
            
        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)
        importance.plot(kind='barh', ax=ax)
        ax.set_title('特征重要性')
        ax.set_xlabel('重要性分数')
        ax.set_ylabel('特征')
        plt.tight_layout()
        
        return fig
    
    def plot_prediction_explanation(self, explanation: Dict[str, Any], figsize: Tuple[int, int] = (12, 8)) -> plt.Figure:
        """
        绘制预测解释图
        
        Args:
            explanation: 解释结果（来自explain_prediction方法）
            figsize: 图形大小
            
        Returns:
            matplotlib图形对象
        """
        # 检查解释结果中是否包含特征贡献
        if 'feature_contributions' not in explanation:
            raise ValueError("解释结果中必须包含'feature_contributions'")
            
        contributions = explanation['feature_contributions']
        
        # 排序并获取最重要的特征贡献
        if isinstance(contributions, pd.Series):
            contributions = contributions.sort_values(key=abs, ascending=False)
            
        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)
        
        # 绘制瀑布图
        if isinstance(contributions, pd.Series):
            # 计算基准值和最终预测值
            base_value = explanation.get('base_value', 0)
            final_value = base_value + contributions.sum()
            
            # 创建瀑布图数据
            values = [base_value] + list(contributions) + [final_value]
            labels = ['基准值'] + list(contributions.index) + ['预测值']
            colors = ['#1f77b4'] + ['#d62728' if v < 0 else '#2ca02c' for v in contributions] + ['#ff7f0e']
            
            # 绘制瀑布图
            ax.bar(range(len(values)), values, width=0.8, color=colors)
            ax.set_xticks(range(len(values)))
            ax.set_xticklabels(labels, rotation=45, ha='right')
            ax.set_title('预测解释')
            ax.set_ylabel('预测值贡献')
            plt.tight_layout()
        else:
            # 如果不是Series，简单绘制条形图
            ax.bar(range(len(contributions)), contributions)
            ax.set_title('预测解释')
            ax.set_ylabel('预测值贡献')
            plt.tight_layout()
            
        return fig
