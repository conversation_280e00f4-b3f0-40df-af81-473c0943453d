#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
训练器模块
- 训练器将训练一系列任务并返回一系列模型记录器
- 每个训练器包含两个步骤：`train`（创建模型记录器）和 `end_train`（修改模型记录器）
- 支持延迟训练（DelayTrainer）概念，可用于在线模拟并行训练
- 提供两种训练器：`TrainerR`（最简单的方式）和 `TrainerRM`（基于任务管理器自动管理任务生命周期）
"""

import os
import socket
import importlib
from typing import Callable, List, Optional, Dict, Any, Union

from tqdm.auto import tqdm

from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.utils.mod import init_instance_by_config
from gbs.data_system.base.dataset import Dataset
from gbs.data_system.base.dataset.weight import Reweighter
from gbs.model_system.base.base_model import BaseModel as Model
from gbs.workflow import R
from gbs.workflow.recorder import Recorder
# from gbs.workflow.task.manage import TaskManager, run_task
# 创建简单的替代类和函数
class TaskManager:
    """简化的 TaskManager 类"""
    STATUS_WAITING = "waiting"
    STATUS_RUNNING = "running"
    STATUS_DONE = "done"
    STATUS_PART_DONE = "part_done"
    STATUS_ERROR = "error"

    def __init__(self, task_pool=None):
        self.task_pool = task_pool

    def create_task(self, tasks):
        return [f"task_{i}" for i in range(len(tasks))]

    def re_query(self, _id):
        from gbs.workflow.recorder import Recorder
        return {"res": Recorder()}

    def wait(self, query=None):
        pass

def run_task(train_func, task_pool, query=None, experiment_name=None,
             before_status=TaskManager.STATUS_WAITING, after_status=TaskManager.STATUS_DONE,
             recorder_name=None, **kwargs):
    """简化的 run_task 函数"""
    pass
from gbs.core.utils.mod import auto_filter_kwargs
from gbs.core.utils import fill_placeholder

def flatten_dict(d, parent_key='', sep='.'):
    """
    将嵌套字典展平为单层字典

    Args:
        d: 要展平的字典
        parent_key: 父键前缀
        sep: 键之间的分隔符

    Returns:
        展平后的字典
    """
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)

# 获取日志记录器
log = get_loki_logger(__name__).logger


def _log_task_info(task_config: dict):
    """记录任务信息到记录器中"""
    R.log_params(**flatten_dict(task_config))
    R.save_objects(**{"task": task_config})  # 保持原始格式和数据类型
    R.set_tags(**{"hostname": socket.gethostname()})


def _exe_task(task_config: dict):
    """执行任务的核心逻辑"""
    rec = R.get_recorder()

    # 检查是否需要跳过训练和数据集初始化
    skip_train = task_config.get("workflow", {}).get("skip_train", False)
    skip_dataset_init = task_config.get("workflow", {}).get("skip_dataset_init", False)
    log.info(f"是否跳过训练: {skip_train}")
    log.info(f"是否跳过数据集初始化: {skip_dataset_init}")

    # 数据集初始化
    if skip_dataset_init:
        log.info("跳过数据集初始化")
        dataset = None
    else:
        dataset: Dataset = init_instance_by_config(task_config["dataset"], accept_types=Dataset)

    # 模型初始化和训练
    model_config = task_config["model"]

    # 检查模型配置中是否有pretrained_path参数
    pretrained_path = model_config.get("kwargs", {}).get("pretrained_path")

    if skip_train and pretrained_path:
        # 如果跳过训练且提供了预训练模型路径，直接加载模型
        log.info(f"跳过训练，从 {pretrained_path} 加载预训练模型")

        # 获取模型类
        try:
            # 导入模块并获取类
            module_path = model_config["module_path"]
            class_name = model_config["class"]
            module = importlib.import_module(module_path)
            model_class = getattr(module, class_name)

            # 使用类方法加载模型
            model = model_class.load_from_file(pretrained_path, **model_config.get("kwargs", {}))
            log.info(f"成功加载预训练模型: {type(model).__name__}")
        except Exception as e:
            log.error(f"加载预训练模型失败: {e}")
            # 如果加载失败，尝试直接初始化模型
            log.info("尝试直接初始化模型...")
            model = init_instance_by_config(model_config)
            log.info(f"成功初始化模型: {type(model).__name__}")
    else:
        # 正常初始化模型并训练
        model = init_instance_by_config(model_config)
        reweighter: Reweighter = task_config.get("reweighter", None)

        if not skip_train:
            # 只有在不跳过训练时才执行fit
            log.info("开始训练模型...")
            auto_filter_kwargs(model.fit)(dataset, reweighter=reweighter)
            log.info("模型训练完成")
        else:
            log.info("跳过模型训练")

    # 保存模型
    R.save_objects(**{"params.pkl": model})

    # 保存数据集用于在线推理，不应该保存具体数据
    try:
        if hasattr(dataset, 'config') and callable(dataset.config):
            # 避免递归调用config，这可能导致字典对象被当作函数调用
            dataset.config(dump_all=False, recursive=False)
        R.save_objects(**{"dataset": dataset})
    except Exception as e:
        log.warning(f"保存数据集时出错: {e}")
        # 继续执行，不要因为保存数据集失败而中断整个流程

    # 打印任务配置
    log.info(f"任务配置: {task_config}")
    log.info(f"任务配置中的记录模板: {task_config.get('record', [])}")

    # 填充占位符
    placehorder_value = {"<MODEL>": model, "<DATASET>": dataset}
    # fill_placeholder是原地修改函数，不返回任何值
    fill_placeholder(task_config, placehorder_value)

    # 打印填充占位符后的任务配置
    log.info(f"填充占位符后的任务配置: {task_config}")
    log.info(f"填充占位符后的记录模板: {task_config.get('record', [])}")

    # 生成记录：预测、回测和分析
    if task_config is not None:
        records = task_config.get("record", [])
        if isinstance(records, dict):  # 防止只有一个字典
            records = [records]
        log.info(f"找到 {len(records)} 个记录模板配置")
        for i, record in enumerate(records):
            log.info(f"处理记录模板 {i+1}/{len(records)}: {record.get('class', 'Unknown')}")
            # 一些记录器需要参数 `model` 和 `dataset`
            # 尝试自动传递它们到初始化函数中，使定义任务更容易
            try:
                log.info(f"初始化记录模板: {record}")
                # 创建一个包含所有可能参数的字典
                all_kwargs = {"model": model, "dataset": dataset}

                # 检查记录模板的kwargs中是否有需要替换的占位符
                if "kwargs" in record and record["kwargs"] is not None:
                    for k, v in record["kwargs"].items():
                        if isinstance(v, str) and v == "<MODEL>":
                            record["kwargs"][k] = model
                        elif isinstance(v, str) and v == "<DATASET>":
                            record["kwargs"][k] = dataset
                        elif isinstance(v, dict):
                            # 递归处理嵌套字典
                            for k2, v2 in list(v.items()):
                                if isinstance(v2, str) and v2 == "<MODEL>":
                                    v[k2] = model
                                elif isinstance(v2, str) and v2 == "<DATASET>":
                                    v[k2] = dataset
                                elif isinstance(v2, str) and v2 == "<PRED>":
                                    # 尝试从recorder中获取pred.pkl
                                    try:
                                        pred_path = rec.get_artifact_uri("pred.pkl")
                                        if os.path.exists(pred_path):
                                            v[k2] = pred_path
                                    except:
                                        log.warning(f"无法获取pred.pkl的路径")

                # 使用auto_filter_kwargs确保只传递记录模板构造函数接受的参数
                r = init_instance_by_config(
                    record,
                    recorder=rec,
                    default_module="gbs.workflow.record_temp",
                )
                log.info(f"生成记录模板: {r.__class__.__name__}")
                r.generate()
                log.info(f"记录模板 {r.__class__.__name__} 生成完成")
            except Exception as e:
                import traceback
                log.warning(f"生成记录时出错: {e}")
                log.warning(f"错误详情: {traceback.format_exc()}")

    # 返回任务配置
    return task_config


def begin_task_train(task_config: dict, experiment_name: str, recorder_name: str = None) -> Recorder:
    """
    开始任务训练，启动记录器并保存任务配置

    Args:
        task_config (dict): 任务配置
        experiment_name (str): 实验名称
        recorder_name (str): 指定的记录器名称，None 则使用 rid

    Returns:
        Recorder: 模型记录器
    """
    with R.start(experiment_name=experiment_name, recorder_name=recorder_name):
        _log_task_info(task_config)
        return R.get_recorder()


def end_task_train(rec: Recorder, experiment_name: str) -> Recorder:
    """
    完成任务训练，进行实际模型拟合和保存

    Args:
        rec (Recorder): 将被恢复的记录器
        experiment_name (str): 实验名称

    Returns:
        Recorder: 模型记录器
    """
    with R.start(experiment_name=experiment_name, recorder_id=rec.info["id"], resume=True):
        task_config = R.load_object("task")
        _exe_task(task_config)
    return rec


def task_train(task_config: dict, experiment_name: str, recorder_name: str = None) -> Recorder:
    """
    基于任务的训练，将分为两个步骤

    Args:
        task_config (dict): 任务配置
        experiment_name (str): 实验名称
        recorder_name (str): 记录器名称

    Returns:
        Recorder: 记录器实例
    """
    # 检查是否需要跳过训练
    skip_train = task_config.get("workflow", {}).get("skip_train", False)
    if skip_train:
        log.info(f"跳过训练标志已设置: skip_train={skip_train}")

    with R.start(experiment_name=experiment_name, recorder_name=recorder_name):
        _log_task_info(task_config)
        _exe_task(task_config)
        return R.get_recorder()


class Trainer:
    """
    训练器可以训练一系列模型
    有 Trainer 和 DelayTrainer 两种，区别在于何时完成实际训练
    """

    def __init__(self):
        self.delay = False

    def train(self, tasks: list, *args, **kwargs) -> list:
        """
        给定一系列任务定义，开始训练，并返回模型

        对于 Trainer，它在此方法中完成实际训练
        对于 DelayTrainer，它在此方法中只做一些准备工作

        Args:
            tasks: 任务列表

        Returns:
            list: 模型列表
        """
        raise NotImplementedError(f"请实现 `train` 方法")

    def end_train(self, models: list, *args, **kwargs) -> list:
        """
        给定一系列模型，在训练结束时完成一些工作（如果需要）
        模型可能是 Recorder、txt 文件、数据库等

        对于 Trainer，它在此方法中做一些收尾工作
        对于 DelayTrainer，它在此方法中完成实际训练

        Args:
            models: 模型列表

        Returns:
            list: 模型列表
        """
        # 如果你在 `train` 方法中完成了所有工作，则不需要做任何事
        return models

    def is_delay(self) -> bool:
        """
        如果训练器将延迟完成 `end_train`

        Returns:
            bool: 是否为 DelayTrainer
        """
        return self.delay

    def __call__(self, *args, **kwargs) -> list:
        return self.end_train(self.train(*args, **kwargs))

    def has_worker(self) -> bool:
        """
        一些训练器有后端工作器支持并行训练
        此方法可以告知工作器是否启用

        Returns:
            bool: 工作器是否启用
        """
        return False

    def worker(self):
        """
        启动工作器

        Raises:
            NotImplementedError: 如果不支持工作器
        """
        raise NotImplementedError(f"请实现 `worker` 方法")


class TrainerR(Trainer):
    """
    基于记录器(Recorder)的训练器
    它将以线性方式训练一系列任务并返回一系列模型记录器

    假设：模型由 `task` 定义，结果将保存到 `Recorder`
    """

    # 这些标签将帮助你区分记录器是否完成训练
    STATUS_KEY = "train_status"
    STATUS_BEGIN = "begin_task_train"
    STATUS_END = "end_task_train"

    def __init__(
        self,
        experiment_name: Optional[str] = None,
        train_func: Callable = task_train,
        call_in_subproc: bool = False,
        default_rec_name: Optional[str] = None,
    ):
        """
        初始化 TrainerR

        Args:
            experiment_name (str, optional): 默认实验名称
            train_func (Callable, optional): 默认训练方法，默认为 `task_train`
            call_in_subproc (bool): 在子进程中调用以强制内存释放
            default_rec_name (str, optional): 默认记录器名称
        """
        super().__init__()
        self.experiment_name = experiment_name
        self.default_rec_name = default_rec_name
        self.train_func = train_func
        self._call_in_subproc = call_in_subproc

    def train(self, tasks: list, train_func: Callable = None, experiment_name: str = None, **kwargs) -> List[Recorder]:
        """
        给定一系列 `tasks` 并返回一系列训练好的 Recorder，顺序可以保证

        Args:
            tasks (list): 基于 `task` 字典的定义列表
            train_func (Callable): 训练方法，至少需要 `tasks` 和 `experiment_name`，None 表示使用默认训练方法
            experiment_name (str): 实验名称，None 表示使用默认名称
            kwargs: train_func 的参数

        Returns:
            List[Recorder]: Recorder 列表
        """
        if isinstance(tasks, dict):
            tasks = [tasks]
        if len(tasks) == 0:
            return []
        if train_func is None:
            train_func = self.train_func
        if experiment_name is None:
            experiment_name = self.experiment_name
        recs = []
        for task in tqdm(tasks, desc="训练任务"):
            if self._call_in_subproc:
                log.info("在子进程中运行模型（强制释放内存）")
                # 简化实现，不使用子进程
                # 原来的代码依赖于不存在的模块
                pass
            rec = train_func(task, experiment_name, recorder_name=self.default_rec_name, **kwargs)
            rec.set_tags(**{self.STATUS_KEY: self.STATUS_BEGIN})
            recs.append(rec)
        return recs

    def end_train(self, models: list, **kwargs) -> List[Recorder]:
        """
        为记录器设置 STATUS_END 标签

        Args:
            models (list): 训练好的记录器列表

        Returns:
            List[Recorder]: 与参数相同的列表
        """
        if isinstance(models, Recorder):
            models = [models]
        for rec in models:
            rec.set_tags(**{self.STATUS_KEY: self.STATUS_END})
        return models


class DelayTrainerR(TrainerR):
    """
    基于 TrainerR 的延迟实现，这意味着 `train` 方法可能只做一些准备工作，而 `end_train` 方法可以进行实际模型拟合
    """

    def __init__(
        self, experiment_name: str = None, train_func=begin_task_train, end_train_func=end_task_train, **kwargs
    ):
        """
        初始化 DelayTrainerR

        Args:
            experiment_name (str): 默认实验名称
            train_func (Callable, optional): 默认训练方法，默认为 `begin_task_train`
            end_train_func (Callable, optional): 默认结束训练方法，默认为 `end_task_train`
        """
        super().__init__(experiment_name, train_func, **kwargs)
        self.end_train_func = end_train_func
        self.delay = True

    def end_train(self, models, end_train_func=None, experiment_name: str = None, **kwargs) -> List[Recorder]:
        """
        给定一系列 Recorder 并返回一系列训练好的 Recorder
        此类将完成实际数据加载和模型拟合

        Args:
            models (list): Recorder 列表，任务已保存到其中
            end_train_func (Callable, optional): 结束训练方法，至少需要 `recorders` 和 `experiment_name`，默认为 None 表示使用 self.end_train_func
            experiment_name (str): 实验名称，None 表示使用默认名称
            kwargs: end_train_func 的参数

        Returns:
            List[Recorder]: Recorder 列表
        """
        if isinstance(models, Recorder):
            models = [models]
        if end_train_func is None:
            end_train_func = self.end_train_func
        if experiment_name is None:
            experiment_name = self.experiment_name
        for rec in models:
            if rec.list_tags()[self.STATUS_KEY] == self.STATUS_END:
                continue
            end_train_func(rec, experiment_name, **kwargs)
            rec.set_tags(**{self.STATUS_KEY: self.STATUS_END})
        return models


class TrainerRM(Trainer):
    """
    基于记录器(Recorder)和任务管理器(TaskManager)的训练器
    它可以以多进程方式训练一系列任务并返回一系列模型记录器

    假设：`task` 将保存到 TaskManager，并从 TaskManager 获取和训练 `task`
    """

    # 这些标签将帮助你区分记录器是否完成训练
    STATUS_KEY = "train_status"
    STATUS_BEGIN = "begin_task_train"
    STATUS_END = "end_task_train"

    # 此标签是 TaskManager 中的 _id，用于区分任务
    TM_ID = "_id in TaskManager"

    def __init__(
        self,
        experiment_name: str = None,
        task_pool: str = None,
        train_func=task_train,
        skip_run_task: bool = False,
        default_rec_name: Optional[str] = None,
    ):
        """
        初始化 TrainerRM

        Args:
            experiment_name (str): 默认实验名称
            task_pool (str): TaskManager 中的任务池名称，None 表示使用与 experiment_name 相同的名称
            train_func (Callable, optional): 默认训练方法，默认为 `task_train`
            skip_run_task (bool):
                如果 skip_run_task == True:
                只在工作器中运行 run_task，否则跳过 run_task
            default_rec_name (str, optional): 默认记录器名称
        """
        super().__init__()
        self.experiment_name = experiment_name
        self.task_pool = task_pool
        self.train_func = train_func
        self.skip_run_task = skip_run_task
        self.default_rec_name = default_rec_name

    def train(
        self,
        tasks: list,
        train_func: Callable = None,
        experiment_name: str = None,
        before_status: str = TaskManager.STATUS_WAITING,
        after_status: str = TaskManager.STATUS_DONE,
        default_rec_name: Optional[str] = None,
        **kwargs,
    ) -> List[Recorder]:
        """
        给定一系列 `tasks` 并返回一系列训练好的 Recorder，顺序可以保证

        此方法默认为单进程，但 TaskManager 提供了一种并行训练的好方法
        用户可以自定义 train_func 以实现多进程甚至多机器

        Args:
            tasks (list): 基于 `task` 字典的定义列表
            train_func (Callable): 训练方法，至少需要 `tasks` 和 `experiment_name`，None 表示使用默认训练方法
            experiment_name (str): 实验名称，None 表示使用默认名称
            before_status (str): 处于 before_status 的任务将被获取和训练，可以是 STATUS_WAITING, STATUS_PART_DONE
            after_status (str): 训练后的任务将变为 after_status，可以是 STATUS_WAITING, STATUS_PART_DONE
            default_rec_name (str, optional): 默认记录器名称
            kwargs: train_func 的参数

        Returns:
            List[Recorder]: Recorder 列表
        """
        if isinstance(tasks, dict):
            tasks = [tasks]
        if len(tasks) == 0:
            return []
        if train_func is None:
            train_func = self.train_func
        if experiment_name is None:
            experiment_name = self.experiment_name
        if default_rec_name is None:
            default_rec_name = self.default_rec_name
        task_pool = self.task_pool
        if task_pool is None:
            task_pool = experiment_name
        tm = TaskManager(task_pool=task_pool)
        _id_list = tm.create_task(tasks)  # 所有任务将保存到数据库
        query = {"_id": {"$in": _id_list}}
        if not self.skip_run_task:
            run_task(
                train_func,
                task_pool,
                query=query,  # 只训练这些任务
                experiment_name=experiment_name,
                before_status=before_status,
                after_status=after_status,
                recorder_name=default_rec_name,
                **kwargs,
            )

        if not self.is_delay():
            tm.wait(query=query)

        recs = []
        for _id in _id_list:
            rec = tm.re_query(_id)["res"]
            rec.set_tags(**{self.STATUS_KEY: self.STATUS_BEGIN})
            rec.set_tags(**{self.TM_ID: _id})
            recs.append(rec)
        return recs

    def end_train(self, recs: list, **kwargs) -> List[Recorder]:
        """
        为记录器设置 STATUS_END 标签

        Args:
            recs (list): 训练好的记录器列表

        Returns:
            List[Recorder]: 与参数相同的列表
        """
        if isinstance(recs, Recorder):
            recs = [recs]
        for rec in recs:
            rec.set_tags(**{self.STATUS_KEY: self.STATUS_END})
        return recs

    def worker(
        self,
        train_func: Callable = None,
        experiment_name: str = None,
    ):
        """
        `train` 的多进程方法，它可以与 `train` 共享相同的 task_pool，并可以在其他进程或其他机器上运行

        Args:
            train_func (Callable): 训练方法，至少需要 `tasks` 和 `experiment_name`，None 表示使用默认训练方法
            experiment_name (str): 实验名称，None 表示使用默认名称
        """
        if train_func is None:
            train_func = self.train_func
        if experiment_name is None:
            experiment_name = self.experiment_name
        task_pool = self.task_pool
        if task_pool is None:
            task_pool = experiment_name
        run_task(train_func, task_pool=task_pool, experiment_name=experiment_name)

    def has_worker(self) -> bool:
        return True


class DelayTrainerRM(TrainerRM):
    """
    基于 TrainerRM 的延迟实现，这意味着 `train` 方法可能只做一些准备工作，而 `end_train` 方法可以进行实际模型拟合
    """

    def __init__(
        self,
        experiment_name: str = None,
        task_pool: str = None,
        train_func=begin_task_train,
        end_train_func=end_task_train,
        skip_run_task: bool = False,
        **kwargs,
    ):
        """
        初始化 DelayTrainerRM

        Args:
            experiment_name (str): 默认实验名称
            task_pool (str): TaskManager 中的任务池名称，None 表示使用与 experiment_name 相同的名称
            train_func (Callable, optional): 默认训练方法，默认为 `begin_task_train`
            end_train_func (Callable, optional): 默认结束训练方法，默认为 `end_task_train`
            skip_run_task (bool):
                如果 skip_run_task == True:
                只在工作器中运行 run_task，否则跳过 run_task
                例如，在 CPU VM 上启动训练器，然后等待任务在 GPU VM 上完成
        """
        super().__init__(experiment_name, task_pool, train_func, **kwargs)
        self.end_train_func = end_train_func
        self.delay = True
        self.skip_run_task = skip_run_task

    def train(self, tasks: list, train_func=None, experiment_name: str = None, **kwargs) -> List[Recorder]:
        """
        与 TrainerRM 的 `train` 相同，after_status 将为 STATUS_PART_DONE

        Args:
            tasks (list): 基于 `task` 字典的定义列表
            train_func (Callable): 训练方法，至少需要 `tasks` 和 `experiment_name`，None 表示使用 self.train_func
            experiment_name (str): 实验名称，None 表示使用默认名称

        Returns:
            List[Recorder]: Recorder 列表
        """
        if isinstance(tasks, dict):
            tasks = [tasks]
        if len(tasks) == 0:
            return []
        _skip_run_task = self.skip_run_task
        self.skip_run_task = False  # 任务准备不能跳过
        res = super().train(
            tasks,
            train_func=train_func,
            experiment_name=experiment_name,
            after_status=TaskManager.STATUS_PART_DONE,
            **kwargs,
        )
        self.skip_run_task = _skip_run_task
        return res

    def end_train(self, recs, end_train_func=None, experiment_name: str = None, **kwargs) -> List[Recorder]:
        """
        给定一系列 Recorder 并返回一系列训练好的 Recorder
        此类将完成实际数据加载和模型拟合

        Args:
            recs (list): Recorder 列表，任务已保存到其中
            end_train_func (Callable, optional): 结束训练方法，至少需要 `recorders` 和 `experiment_name`，None 表示使用 self.end_train_func
            experiment_name (str): 实验名称，None 表示使用默认名称
            kwargs: end_train_func 的参数

        Returns:
            List[Recorder]: Recorder 列表
        """
        if isinstance(recs, Recorder):
            recs = [recs]
        if end_train_func is None:
            end_train_func = self.end_train_func
        if experiment_name is None:
            experiment_name = self.experiment_name
        task_pool = self.task_pool
        if task_pool is None:
            task_pool = experiment_name
        _id_list = []
        for rec in recs:
            _id_list.append(rec.list_tags()[self.TM_ID])

        query = {"_id": {"$in": _id_list}}
        if not self.skip_run_task:
            run_task(
                end_train_func,
                task_pool,
                query=query,  # 只训练这些任务
                experiment_name=experiment_name,
                before_status=TaskManager.STATUS_PART_DONE,
                **kwargs,
            )

        TaskManager(task_pool=task_pool).wait(query=query)

        for rec in recs:
            rec.set_tags(**{self.STATUS_KEY: self.STATUS_END})
        return recs

    def worker(self, end_train_func=None, experiment_name: str = None):
        """
        `end_train` 的多进程方法，它可以与 `end_train` 共享相同的 task_pool，并可以在其他进程或其他机器上运行

        Args:
            end_train_func (Callable, optional): 结束训练方法，至少需要 `recorders` 和 `experiment_name`，None 表示使用 self.end_train_func
            experiment_name (str): 实验名称，None 表示使用默认名称
        """
        if end_train_func is None:
            end_train_func = self.end_train_func
        if experiment_name is None:
            experiment_name = self.experiment_name
        task_pool = self.task_pool
        if task_pool is None:
            task_pool = experiment_name
        run_task(
            end_train_func,
            task_pool=task_pool,
            experiment_name=experiment_name,
            before_status=TaskManager.STATUS_PART_DONE,
        )

    def has_worker(self) -> bool:
        return True
