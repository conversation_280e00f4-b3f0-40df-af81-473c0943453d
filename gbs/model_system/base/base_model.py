# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
import abc
import os
import pickle
from typing import Text, Union, Type, Optional, Dict, Any
from gbs.core.utils.serial import Serializable
from gbs.data_system.base.dataset import Dataset
from gbs.data_system.base.dataset.weight import Reweighter
from gbs.core.utils.loki_logger import get_loki_logger

# 获取日志记录器
log = get_loki_logger(__name__).logger


class BaseModel(Serializable, metaclass=abc.ABCMeta):
    """Modeling things"""

    @abc.abstractmethod
    def predict(self, *args, **kwargs) -> object:
        """Make predictions after modeling things"""

    def __call__(self, *args, **kwargs) -> object:
        """leverage Python syntactic sugar to make the models' behaviors like functions"""
        return self.predict(*args, **kwargs)


class Model(BaseModel):
    """Learnable Models"""

    @classmethod
    def load_from_file(cls, path: str, **kwargs) -> 'Model':
        """
        从文件加载模型

        Args:
            path: 模型文件路径
            **kwargs: 额外参数，用于初始化模型

        Returns:
            加载的模型实例
        """
        log.info(f"从文件加载模型: {path}")

        # 检查文件是否存在
        if not os.path.exists(path):
            raise FileNotFoundError(f"模型文件不存在: {path}")

        # 根据文件扩展名选择加载方法
        if path.endswith('.pkl'):
            # 使用pickle加载
            with open(path, 'rb') as f:
                loaded_obj = pickle.load(f)

            # 如果加载的是模型实例，直接返回
            if isinstance(loaded_obj, cls):
                log.info(f"成功加载模型实例，类型: {type(loaded_obj).__name__}")
                return loaded_obj

            # 如果加载的是状态字典或其他对象，创建新实例并加载状态
            log.info(f"加载的对象类型: {type(loaded_obj).__name__}，尝试创建新实例并加载状态")
            model = cls(**kwargs)

            # 尝试加载状态字典
            if hasattr(model, 'load_state_dict') and hasattr(loaded_obj, 'state_dict'):
                model.load_state_dict(loaded_obj.state_dict())
                log.info("成功加载模型状态字典")
            elif hasattr(model, 'load_state_dict'):
                # 假设loaded_obj本身就是状态字典
                try:
                    model.load_state_dict(loaded_obj)
                    log.info("成功加载模型状态字典")
                except Exception as e:
                    log.warning(f"加载状态字典失败: {e}")
            else:
                log.warning(f"无法加载状态，模型类 {cls.__name__} 不支持 load_state_dict 方法")

            # 设置模型为已训练状态
            if hasattr(model, 'is_fitted'):
                model.is_fitted = True

            return model

        elif path.endswith('.pt') or path.endswith('.pth'):
            # 使用torch加载
            import torch
            state_dict = torch.load(path)

            # 创建新实例
            model = cls(**kwargs)

            # 加载状态字典
            if hasattr(model, 'load_state_dict'):
                model.load_state_dict(state_dict)
                log.info("成功加载PyTorch模型状态字典")
            else:
                log.warning(f"无法加载状态，模型类 {cls.__name__} 不支持 load_state_dict 方法")

            # 设置模型为已训练状态
            if hasattr(model, 'is_fitted'):
                model.is_fitted = True

            # 设置为评估模式
            if hasattr(model, 'eval'):
                model.eval()

            return model
        else:
            raise ValueError(f"不支持的文件格式: {path}")

    def fit(self, dataset: Dataset, reweighter: Reweighter):
        """
        Learn model from the base model

        .. note::

            The attribute names of learned model should `not` start with '_'. So that the model could be
            dumped to disk.

        The following code example shows how to retrieve `x_train`, `y_train` and `w_train` from the `dataset`:

            .. code-block:: Python

                # get features and labels
                df_train, df_valid = dataset.prepare(
                    ["train", "valid"], col_set=["feature", "label"], data_key=DataHandlerLP.DK_L
                )
                x_train, y_train = df_train["feature"], df_train["label"]
                x_valid, y_valid = df_valid["feature"], df_valid["label"]

                # get weights
                try:
                    wdf_train, wdf_valid = dataset.prepare(["train", "valid"], col_set=["weight"],
                                                           data_key=DataHandlerLP.DK_L)
                    w_train, w_valid = wdf_train["weight"], wdf_valid["weight"]
                except KeyError as e:
                    w_train = pd.DataFrame(np.ones_like(y_train.values), index=y_train.index)
                    w_valid = pd.DataFrame(np.ones_like(y_valid.values), index=y_valid.index)

        Parameters
        ----------
        dataset : Dataset
            dataset will generate the processed data from model training.

        """
        raise NotImplementedError()

    @abc.abstractmethod
    def predict(self, dataset: Dataset, segment: Union[Text, slice] = "test") -> object:
        """give prediction given Dataset

        Parameters
        ----------
        dataset : Dataset
            dataset will generate the processed dataset from model training.

        segment : Text or slice
            dataset will use this segment to prepare data. (default=test)

        Returns
        -------
        Prediction results with certain type such as `pandas.Series`.
        """
        raise NotImplementedError()


class ModelFT(Model):
    """Model (F)ine(t)unable"""

    @abc.abstractmethod
    def finetune(self, dataset: Dataset):
        """finetune model based given dataset

        A typical use case of finetuning model with qlib.workflow.R

        .. code-block:: python

            # start exp to train init model
            with R.start(experiment_name="init models"):
                model.fit(dataset)
                R.save_objects(init_model=model)
                rid = R.get_recorder().id

            # Finetune model based on previous trained model
            with R.start(experiment_name="finetune model"):
                recorder = R.get_recorder(recorder_id=rid, experiment_name="init models")
                model = recorder.load_object("init_model")
                model.finetune(dataset, num_boost_round=10)


        Parameters
        ----------
        dataset : Dataset
            dataset will generate the processed dataset from model training.
        """
        raise NotImplementedError()
