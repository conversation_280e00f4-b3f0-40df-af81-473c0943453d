"""
信号接口模块，提供统一的信号接口

参考qlib的设计，将模型预测与策略执行分离
"""

import abc
import pandas as pd
import numpy as np
from typing import Union, Tuple, List, Dict, Text, Any, Optional

from gbs.core.utils.date_utils import get_month_end, find_closest_date


class Signal(metaclass=abc.ABCMeta):
    """
    信号接口
    
    交易策略基于预测信号做决策，这些信号可能来自不同的源（如预处理数据、模型在线预测等）
    该接口提供了统一的接口，用于获取不同来源的信号
    """

    @abc.abstractmethod
    def get_signal(self, start_time: pd.Timestamp, end_time: pd.Timestamp) -> Union[pd.Series, pd.DataFrame, None]:
        """
        获取决策步骤结束时的信号（从start_time到end_time）
        
        Returns
        -------
        Union[pd.Series, pd.DataFrame, None]:
            如果特定日期没有信号，则返回None
        """


class SignalWCache(Signal):
    """
    带缓存的信号
    
    将准备好的信号存储为属性，并根据输入查询提供相应的信号
    """

    def __init__(self, signal: Union[pd.Series, pd.DataFrame]) -> None:
        """
        初始化带缓存的信号
        
        Parameters
        ----------
        signal : Union[pd.Series, pd.DataFrame]
            信号的预期格式如下（索引的顺序不重要，可以自动调整）
            
                instrument datetime
                SH600000   2008-01-02  0.079704
                           2008-01-03  0.120125
                           2008-01-04  0.878860
                           2008-01-07  0.505539
                           2008-01-08  0.395004
        """
        # 确保信号有正确的索引格式
        if isinstance(signal, pd.Series) or isinstance(signal, pd.DataFrame):
            # 如果信号是Series或DataFrame，但没有MultiIndex
            if not isinstance(signal.index, pd.MultiIndex):
                # 假设信号的索引是股票代码
                signal = signal.copy()
                signal.name = "score"
                signal = signal.reset_index()
                signal.columns = ["instrument", "score"]
                signal["datetime"] = pd.Timestamp.now().normalize()  # 使用当前日期
                signal = signal.set_index(["instrument", "datetime"])["score"]
            
            # 确保datetime是索引的一个级别
            if "datetime" not in signal.index.names:
                raise ValueError("信号必须包含'datetime'作为索引级别")
        else:
            raise TypeError("信号必须是pandas Series或DataFrame")
        
        self.signal_cache = signal

    def get_signal(self, start_time: pd.Timestamp, end_time: pd.Timestamp) -> Union[pd.Series, pd.DataFrame, None]:
        """
        获取指定时间范围内的信号
        
        Parameters
        ----------
        start_time : pd.Timestamp
            开始时间
        end_time : pd.Timestamp
            结束时间
            
        Returns
        -------
        Union[pd.Series, pd.DataFrame, None]:
            指定时间范围内的信号
        """
        # 信号的频率可能与策略的决策频率不一致
        # 因此需要从数据中重新采样
        # 使用最新的信号，因为它利用了更近期的数据
        
        # 从缓存中获取信号
        if self.signal_cache is None or self.signal_cache.empty:
            return None
        
        # 获取时间范围内的信号
        try:
            # 尝试使用索引的datetime级别进行过滤
            signal = self.signal_cache.loc[(slice(None), slice(start_time, end_time)), :]
            if signal.empty:
                # 如果没有找到精确匹配，尝试找最接近的日期
                all_dates = self.signal_cache.index.get_level_values("datetime").unique()
                closest_date = find_closest_date(end_time, all_dates)
                if closest_date is not None:
                    signal = self.signal_cache.loc[(slice(None), closest_date), :]
        except Exception as e:
            print(f"获取信号时出错: {e}")
            return None
        
        if signal.empty:
            return None
        
        return signal


class ModelSignal(SignalWCache):
    """
    模型信号
    
    使用模型和数据集生成信号
    """

    def __init__(self, model, dataset) -> None:
        """
        初始化模型信号
        
        Parameters
        ----------
        model : 模型对象
            用于预测的模型
        dataset : 数据集对象
            用于提供模型输入的数据集
        """
        self.model = model
        self.dataset = dataset
        
        # 使用模型预测生成信号
        pred_scores = self.model.predict(dataset)
        
        # 确保预测结果是pandas Series或DataFrame
        if not isinstance(pred_scores, (pd.Series, pd.DataFrame)):
            # 如果是numpy数组，转换为pandas Series
            if isinstance(pred_scores, np.ndarray):
                if pred_scores.ndim == 1:
                    # 一维数组
                    pred_scores = pd.Series(pred_scores, name="score")
                else:
                    # 多维数组，取第一列
                    pred_scores = pd.Series(pred_scores[:, 0], name="score")
            else:
                raise TypeError("模型预测结果必须是pandas Series、DataFrame或numpy数组")
        
        # 如果是DataFrame，取第一列
        if isinstance(pred_scores, pd.DataFrame):
            pred_scores = pred_scores.iloc[:, 0]
        
        super().__init__(pred_scores)


def create_signal_from(
    obj: Union[Signal, Tuple, List, Dict, Text, pd.Series, pd.DataFrame],
) -> Signal:
    """
    从多种信息创建信号
    
    根据obj的类型选择正确的方法创建信号
    
    Parameters
    ----------
    obj : Union[Signal, Tuple, List, Dict, Text, pd.Series, pd.DataFrame]
        信号源
        
    Returns
    -------
    Signal:
        创建的信号对象
    """
    if isinstance(obj, Signal):
        return obj
    elif isinstance(obj, (tuple, list)) and len(obj) >= 2:
        return ModelSignal(obj[0], obj[1])
    elif isinstance(obj, (dict, str)):
        # 这里应该使用配置初始化实例，简化实现
        raise NotImplementedError("从配置创建信号尚未实现")
    elif isinstance(obj, (pd.DataFrame, pd.Series)):
        return SignalWCache(signal=obj)
    else:
        raise NotImplementedError(f"不支持的信号类型: {type(obj)}")
