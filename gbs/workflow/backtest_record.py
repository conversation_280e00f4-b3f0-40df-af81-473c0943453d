#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测记录模块

提供用于生成和记录回测结果的类
"""

from typing import Dict, Any, Optional
import pandas as pd
import numpy as np
import copy
import time
import os
import pickle
import shutil
import traceback
import types

from .record_temp import ACRecordTemp
from .signal_record import SignalRecord
from .utils import load_and_convert_prediction
from ..core.utils.loki_logger import get_loki_logger


class BacktestRecord(ACRecordTemp):
    """
    回测记录类

    用于生成和记录回测结果，包括回测报告、持仓、风险分析等
    """

    artifact_path = "backtest_analysis"  # 工件路径
    depend_cls = SignalRecord  # 依赖于信号记录

    def __init__(
        self,
        recorder,
        config=None,
        risk_analysis_freq="day",
        skip_existing=False,
        trade_record_exp_id=None,
        trade_record_run_id=None,
        **kwargs
    ):
        """
        初始化回测记录类

        Args:
            recorder: 记录器对象
            config: 回测配置
            risk_analysis_freq: 风险分析频率
            skip_existing: 如果结果已存在，是否跳过生成
            trade_record_exp_id: TradeRecord的实验ID，用于直接从TradeRecord加载目标仓位
            trade_record_run_id: TradeRecord的运行ID，用于直接从TradeRecord加载目标仓位
            **kwargs: 额外参数
        """
        super().__init__(recorder=recorder, skip_existing=skip_existing)

        # 保存TradeRecord的实验ID和运行ID
        self.trade_record_exp_id = trade_record_exp_id
        self.trade_record_run_id = trade_record_run_id

        # 默认配置
        if config is None:
            config = {
                "strategy": {
                    "class": "BtPortfolioStrategy",
                    "module_path": "gbs.trading_system.strategies.bt_portfolio_strategy",
                    "kwargs": {
                        "signal": "<PRED>",  # 使用预测信号
                        "top_pct": 0.1,  # 选择排名前10%的股票
                        "rebalance_threshold": 0.0,  # 再平衡阈值
                    },
                },
                "backtest": {
                    "start_time": None,  # 将从预测数据中自动提取
                    "end_time": None,  # 将从预测数据中自动提取
                    "initial_capital": 1000000,  # 初始资金
                    "commission": 0.0003,  # 交易佣金
                    "instruments": None,  # 将从预测数据中自动提取
                    "fields": ["open", "high", "low", "close", "volume"],
                    "freq": "day",
                },
            }

        # 深拷贝配置，避免修改外部配置
        self.config = copy.deepcopy(config)
        self.strategy_config = self.config.get("strategy", {})
        self.backtest_config = self.config.get("backtest", {})

        # 设置风险分析频率
        self.risk_analysis_freq = risk_analysis_freq

    def load_trade_positions(self):
        """
        从TradeRecord加载目标仓位数据

        Returns:
            目标仓位DataFrame，如果加载失败则返回None
        """
        from ..core.utils.loki_logger import get_loki_logger
        import os
        import pandas as pd

        log = get_loki_logger(__name__).logger

        if self.trade_record_exp_id is None or self.trade_record_run_id is None:
            log.warning("未指定TradeRecord的实验ID或运行ID，无法加载目标仓位数据")
            return None

        try:
            # 直接构建目标仓位文件的本地路径
            file_path = f"outputs/mlruns/{self.trade_record_exp_id}/{self.trade_record_run_id}/artifacts/trade_analysis/target_positions.csv"

            log.info(f"尝试从 {file_path} 加载目标仓位数据")

            if not os.path.exists(file_path):
                log.warning(f"目标仓位文件不存在: {file_path}")
                return None

            # 加载目标仓位数据
            positions_df = pd.read_csv(file_path)
            log.info(f"成功加载目标仓位数据，形状: {positions_df.shape}")

            # 检查必要的列是否存在
            required_cols = ['datetime', 'weight']
            missing_cols = [col for col in required_cols if col not in positions_df.columns]
            if missing_cols:
                log.warning(f"目标仓位数据缺少必要的列: {missing_cols}")
                return None

            # 检查股票标识符列是否存在
            if 'symbol' not in positions_df.columns and 'instrument' in positions_df.columns:
                log.info("将'instrument'列重命名为'symbol'")
                positions_df = positions_df.rename(columns={'instrument': 'symbol'})
            elif 'symbol' not in positions_df.columns:
                log.warning("目标仓位数据缺少股票标识符列(symbol或instrument)")
                return None

            # 将datetime列转换为日期类型
            positions_df['datetime'] = pd.to_datetime(positions_df['datetime'])

            # 创建多级索引的DataFrame
            positions_df = positions_df.set_index(['datetime', 'symbol'])

            return positions_df

        except Exception as e:
            log.error(f"加载目标仓位数据时出错: {str(e)}")
            import traceback
            log.error(traceback.format_exc())
            return None

    def _generate(self, **kwargs):
        """
        生成回测结果

        Args:
            **kwargs: 额外参数

        Returns:
            生成的工件字典
        """
        import traceback
        import time
        from ..core.utils.loki_logger import get_loki_logger
        from ..backtest_system.engine.backtrader_engine import BacktraderEngine

        log = get_loki_logger(__name__).logger

        # 记录开始时间
        start_time = time.time()

        # 确保回测分析目录存在
        empty_info = {"info": "Backtest analysis directory"}
        self.save(**{"info.json": empty_info})

        log.info("开始生成回测结果...")

        # 检查是否需要从TradeRecord加载目标仓位数据
        if (self.trade_record_exp_id is not None and self.trade_record_run_id is not None and
            self.strategy_config.get("kwargs", {}).get("signal_from_trade_record", False)):
            log.info("尝试从TradeRecord加载目标仓位数据...")
            pred = self.load_trade_positions()

            if pred is not None:
                log.info("成功从TradeRecord加载目标仓位数据")
                # 将signal_from_trade_record设置为True，以便策略知道使用目标仓位数据
                self.strategy_config["kwargs"]["signal_from_trade_record"] = True
            else:
                log.warning("从TradeRecord加载目标仓位数据失败，尝试加载预测信号...")
                # 如果从TradeRecord加载失败，尝试加载预测信号
                try:
                    # 首先尝试加载转换后的预测信号
                    pred = self.load("pred_df.pkl")
                except Exception:
                    # 如果失败，尝试加载原始预测信号
                    pred = self.load("pred.pkl")

                    # 检查预测信号格式并转换
                    if isinstance(pred, dict):
                        # 尝试转换为DataFrame格式
                        if 'weights' in pred and 'dates' in pred and 'stock_ids' in pred:
                            weights = pred['weights']
                            dates = pred['dates']
                            stock_ids = pred['stock_ids']

                            # 创建多级索引的 DataFrame
                            data = []
                            index_tuples = []

                            for i, (weight_array, date, ids) in enumerate(zip(weights, dates, stock_ids)):
                                for j, (weight, stock_id) in enumerate(zip(weight_array, ids)):
                                    # 创建索引元组 (datetime, instrument)
                                    index_tuples.append((date, stock_id))
                                    # 添加权重数据
                                    data.append([weight])

                            # 创建多级索引
                            index = pd.MultiIndex.from_tuples(index_tuples, names=['datetime', 'instrument'])

                            # 创建 DataFrame
                            pred = pd.DataFrame(data, index=index, columns=['weight'])

                            # 保存转换后的预测信号
                            self.save(**{"pred_df.pkl": pred})
                        else:
                            raise ValueError("预测信号缺少必要的键: weights, dates, stock_ids")
        else:
            # 如果不需要从TradeRecord加载，直接加载预测信号
            try:
                # 首先尝试加载转换后的预测信号
                pred = self.load("pred_df.pkl")
            except Exception:
                # 如果失败，尝试加载原始预测信号
                pred = self.load("pred.pkl")

                # 检查预测信号格式并转换
                if isinstance(pred, dict):
                    # 尝试转换为DataFrame格式
                    if 'weights' in pred and 'dates' in pred and 'stock_ids' in pred:
                        weights = pred['weights']
                        dates = pred['dates']
                        stock_ids = pred['stock_ids']

                        # 创建多级索引的 DataFrame
                        data = []
                        index_tuples = []

                        for i, (weight_array, date, ids) in enumerate(zip(weights, dates, stock_ids)):
                            for j, (weight, stock_id) in enumerate(zip(weight_array, ids)):
                                # 创建索引元组 (datetime, instrument)
                                index_tuples.append((date, stock_id))
                                # 添加权重数据
                                data.append([weight])

                        # 创建多级索引
                        index = pd.MultiIndex.from_tuples(index_tuples, names=['datetime', 'instrument'])

                        # 创建 DataFrame
                        pred = pd.DataFrame(data, index=index, columns=['weight'])

                        # 保存转换后的预测信号
                        self.save(**{"pred_df.pkl": pred})
                    else:
                        raise ValueError("预测信号缺少必要的键: weights, dates, stock_ids")

        # 替换策略配置中的<PRED>占位符
        if isinstance(self.strategy_config.get("kwargs", {}).get("signal"), str) and self.strategy_config["kwargs"]["signal"] == "<PRED>":
            self.strategy_config["kwargs"]["signal"] = pred

        # 如果回测时间范围未设置，从预测数据中提取
        if self.backtest_config.get("start_time") is None or self.backtest_config.get("end_time") is None:
            if hasattr(pred, "index") and hasattr(pred.index, "get_level_values"):
                try:
                    dt_values = pred.index.get_level_values("datetime")
                    if self.backtest_config.get("start_time") is None:
                        self.backtest_config["start_time"] = dt_values.min().strftime("%Y-%m-%d")
                    if self.backtest_config.get("end_time") is None:
                        self.backtest_config["end_time"] = dt_values.max().strftime("%Y-%m-%d")
                except (AttributeError, KeyError):
                    pass

        # 如果instruments未设置，从预测数据中提取
        if self.backtest_config.get("instruments") is None:
            if hasattr(pred, "index") and hasattr(pred.index, "get_level_values"):
                try:
                    instruments = pred.index.get_level_values("instrument").unique().tolist()
                    self.backtest_config["instruments"] = instruments
                except (AttributeError, KeyError):
                    pass

        # 创建回测引擎
        backtest_engine = BacktraderEngine(
            initial_capital=self.backtest_config.get("initial_capital", 1000000),
            commission=self.backtest_config.get("commission", 0.0003)
        )

        # 导入策略类
        strategy_module = __import__(self.strategy_config.get("module_path"), fromlist=[self.strategy_config.get("class")])
        strategy_class = getattr(strategy_module, self.strategy_config.get("class"))

        # 运行回测
        backtest_result = backtest_engine.run(
            strategy_class=strategy_class,
            strategy_params=self.strategy_config.get("kwargs", {}),
            instruments=self.backtest_config.get("instruments"),
            fields=self.backtest_config.get("fields"),
            freq=self.backtest_config.get("freq", "day"),
            start_date=self.backtest_config.get("start_time"),
            end_date=self.backtest_config.get("end_time"),
            num_threads=self.backtest_config.get("num_threads", 0)
        )

        # 评估性能
        metrics = backtest_engine.evaluate_performance(
            result=backtest_result,
            risk_free_rate=self.backtest_config.get("risk_free_rate", 0.0),
            periods_per_year=self.backtest_config.get("periods_per_year", 252)
        )

        # 记录指标
        try:
            log.info(f"记录指标: {metrics}")

            # 检查指标是否为空
            if not metrics:
                log.warning("指标为空，尝试从回测结果中提取")

                # 尝试从回测结果中提取指标
                if hasattr(backtest_result, 'metrics') and backtest_result.metrics:
                    metrics = backtest_result.metrics
                    log.info(f"从回测结果中提取到指标: {metrics}")
                else:
                    log.warning("回测结果中也没有指标，无法获取真实指标")
                    # 只添加警告指标
                    metrics = {
                        "warning": 1.0
                    }

            # 记录指标到MLflow
            log.info(f"记录指标到MLflow: {metrics}")
            self.recorder.log_metrics(**metrics)

            # 确保指标被正确记录
            # 将指标保存到单独的文件中
            self.save(**{"metrics.json": metrics})

            # 检查指标是否已正确记录
            try:
                recorded_metrics = self.recorder.list_metrics()
                log.info(f"已记录的指标: {recorded_metrics}")

                # 检查是否所有指标都已记录
                missing_metrics = set(metrics.keys()) - set(recorded_metrics.keys())
                if missing_metrics:
                    log.warning(f"以下指标未被记录: {missing_metrics}")

                    # 尝试再次记录缺失的指标
                    missing_metrics_dict = {k: metrics[k] for k in missing_metrics if k in metrics}
                    if missing_metrics_dict:
                        log.info(f"尝试再次记录缺失的指标: {missing_metrics_dict}")
                        self.recorder.log_metrics(**missing_metrics_dict)
            except Exception as e:
                log.error(f"检查已记录指标时出错: {str(e)}")
                import traceback
                log.error(traceback.format_exc())
        except Exception as e:
            log.error(f"记录指标时出错: {str(e)}")
            import traceback
            log.error(traceback.format_exc())

        # 准备保存的工件
        try:
            # 使用BacktraderResult类的__getstate__方法创建可序列化的状态
            log.info("使用BacktraderResult.__getstate__方法创建可序列化的状态...")
            try:
                # 确保回测结果对象有metrics属性
                if not hasattr(backtest_result, 'metrics') or not backtest_result.metrics:
                    log.warning("回测结果对象没有metrics属性或metrics为空，设置metrics")
                    backtest_result.metrics = metrics

                # 获取状态
                state = backtest_result.__getstate__()
                log.info(f"成功获取状态: {list(state.keys())}")

                # 检查状态是否包含metrics
                if 'metrics' not in state or not state['metrics']:
                    log.warning("状态不包含metrics，添加metrics")
                    state['metrics'] = metrics

                # 检查状态是否包含交易记录
                if 'trades' in state and state['trades']:
                    log.info(f"状态包含 {len(state['trades'])} 条交易记录")
                else:
                    log.warning("状态不包含交易记录")

                    # 尝试从策略中提取交易记录
                    if hasattr(backtest_result, 'strategy') and backtest_result.strategy is not None:
                        if hasattr(backtest_result.strategy, 'trades'):
                            state['trades'] = backtest_result.strategy.trades
                            log.info(f"从策略中提取到 {len(state['trades'])} 条交易记录")

                # 创建一个新的BacktraderResult对象并恢复状态
                from gbs.backtest_system.backtrader_result import BacktraderResult
                log.info("创建新的BacktraderResult对象...")
                serializable_result = BacktraderResult(cerebro=None,
                                                      start_date=backtest_result.start_date,
                                                      end_date=backtest_result.end_date)
                log.info("设置状态到新对象...")
                serializable_result.__setstate__(state)

                # 确保serializable_result有metrics属性
                if not hasattr(serializable_result, 'metrics') or not serializable_result.metrics:
                    log.warning("serializable_result没有metrics属性或metrics为空，设置metrics")
                    serializable_result.metrics = metrics

                # 尝试序列化新对象
                import pickle
                log.info("尝试序列化新对象...")
                pickle_data = pickle.dumps(serializable_result)
                log.info(f"成功序列化对象，数据大小: {len(pickle_data)} 字节")
                log.info("成功创建可序列化的回测结果对象")

                # 直接将metrics记录到MLflow
                log.info(f"直接将metrics记录到MLflow: {metrics}")
                for key, value in metrics.items():
                    if isinstance(value, (int, float)) and not isinstance(value, bool):
                        self.recorder.log_metrics(**{key: value})
            except Exception as e:
                log.error(f"创建可序列化对象时出错: {e}")
                import traceback
                log.error(traceback.format_exc())
                raise

            # 保存可序列化的回测结果对象
            artifact_objects = {
                "backtest_result.pkl": serializable_result,
                "metrics.json": metrics,
                "equity_curve.csv": backtest_result.equity_curve,  # 直接保存DataFrame，而不是调用to_csv()
            }

            # 立即尝试保存backtest_result.pkl，确保它被正确保存
            try:
                log.info("尝试立即保存backtest_result.pkl...")
                with open("/tmp/backtest_result.pkl", "wb") as f:
                    import pickle
                    pickle.dump(serializable_result, f)
                log.info("成功保存backtest_result.pkl到临时文件")

                # 如果成功保存到临时文件，复制到目标位置
                import shutil
                import os

                # 确保目标目录存在
                # 检查recorder是否有run_id属性，如果没有则使用id属性
                run_id = getattr(self.recorder, 'run_id', getattr(self.recorder, 'id', None))
                if run_id is None:
                    log.warning("recorder既没有run_id属性也没有id属性，无法确定目标目录")
                    return

                os.makedirs(os.path.dirname(f"outputs/mlruns/{self.recorder.experiment_id}/{run_id}/artifacts/backtest_analysis/backtest_result.pkl"), exist_ok=True)

                # 复制文件
                shutil.copy("/tmp/backtest_result.pkl", f"outputs/mlruns/{self.recorder.experiment_id}/{run_id}/artifacts/backtest_analysis/backtest_result.pkl")
                log.info(f"成功复制backtest_result.pkl到目标目录", {
                    "target_path": f"outputs/mlruns/{self.recorder.experiment_id}/{run_id}/artifacts/backtest_analysis/"
                })
            except Exception as e:
                log.error(f"保存backtest_result.pkl时出错: {e}")
                import traceback
                log.error(traceback.format_exc())
        except Exception as e:
            # 如果保存失败，尝试创建一个简化的可序列化对象
            log.warning(f"创建可序列化的回测结果对象失败: {e}")
            import traceback
            log.warning(traceback.format_exc())  # 记录完整的堆栈跟踪
            log.info("尝试创建简化的可序列化对象")

            # 创建一个简化的回测结果对象
            import types
            simplified_result = types.SimpleNamespace()

            # 复制必要的属性
            simplified_result.equity_curve = backtest_result.equity_curve
            simplified_result.start_date = backtest_result.start_date if hasattr(backtest_result, 'start_date') else None
            simplified_result.end_date = backtest_result.end_date if hasattr(backtest_result, 'end_date') else None
            simplified_result.benchmark_returns = backtest_result.benchmark_returns if hasattr(backtest_result, 'benchmark_returns') else None
            simplified_result.weights_history = backtest_result.weights_history if hasattr(backtest_result, 'weights_history') else []

            # 保存策略中的交易记录
            if hasattr(backtest_result, 'strategy') and backtest_result.strategy is not None:
                try:
                    # 如果策略有交易记录，保存它
                    if hasattr(backtest_result.strategy, 'trades'):
                        simplified_result.trades = backtest_result.strategy.trades
                    else:
                        simplified_result.trades = []
                except Exception as e:
                    print(f"保存交易记录时出错: {e}")
                    traceback.print_exc()
                    simplified_result.trades = []
            else:
                simplified_result.trades = []

            # 尝试使用pickle保存简化的回测结果对象
            try:
                artifact_objects = {
                    "backtest_result.pkl": simplified_result,
                    "metrics.json": metrics,
                    "equity_curve.csv": backtest_result.equity_curve,  # 直接保存DataFrame，而不是调用to_csv()
                }
            except Exception as e2:
                log.warning(f"保存简化回测结果对象失败: {e2}")
                log.warning(traceback.format_exc())
                log.info("只保存必要的回测数据")
                artifact_objects = {
                    "metrics.json": metrics,
                    "equity_curve.csv": backtest_result.equity_curve,  # 直接保存DataFrame，而不是调用to_csv()
                }

        # 如果有权重历史，也保存
        if backtest_result.weights_history and len(backtest_result.weights_history) > 0:
            # 将权重历史转换为DataFrame
            # 检查权重历史的格式
            if isinstance(backtest_result.weights_history[0], list) and len(backtest_result.weights_history[0]) > 0 and isinstance(backtest_result.weights_history[0][0], dict):
                # 新格式：包含日期、ticker、持仓数量和权重百分比的字典列表
                weights_data = []
                for date_weights in backtest_result.weights_history:
                    for stock_data in date_weights:
                        weights_data.append(stock_data)
                weights_df = pd.DataFrame(weights_data)
            else:
                # 旧格式：简单的权重数组
                weights_df = pd.DataFrame(backtest_result.weights_history)

            artifact_objects["weights_history.csv"] = weights_df  # 直接保存DataFrame，而不是调用to_csv()

        # 如果有交易记录，也保存
        if hasattr(backtest_result, "trades") and backtest_result.trades is not None:
            artifact_objects["trades.csv"] = backtest_result.trades  # 直接保存DataFrame，而不是调用to_csv()

        # 保存回测配置
        artifact_objects["backtest_config.json"] = self.backtest_config

        # 处理策略配置，确保可以序列化为JSON
        strategy_config_copy = copy.deepcopy(self.strategy_config)

        # 处理kwargs中的signal参数
        if 'kwargs' in strategy_config_copy and 'signal' in strategy_config_copy['kwargs']:
            signal = strategy_config_copy['kwargs']['signal']

            # 如果signal是DataFrame，将其替换为字符串描述
            if isinstance(signal, pd.DataFrame):
                strategy_config_copy['kwargs']['signal'] = f"DataFrame(shape={signal.shape}, columns={list(signal.columns)})"
            # 如果signal是其他不可序列化的对象，也替换为字符串描述
            elif not isinstance(signal, (str, int, float, bool, list, dict, type(None))):
                strategy_config_copy['kwargs']['signal'] = f"{type(signal).__name__}(id={id(signal)})"

        artifact_objects["strategy_config.json"] = strategy_config_copy

        # 记录结束时间和总耗时
        end_time = time.time()
        elapsed_time = end_time - start_time
        log.info(f"回测结果生成完成，总耗时: {elapsed_time:.2f}秒")

        # 保存执行信息
        exec_info = {
            "status": "success",
            "start_time": start_time,
            "end_time": end_time,
            "elapsed_time": elapsed_time,
            "backtest_config": self.backtest_config
        }
        self.save(**{"exec_info.json": exec_info})

        return artifact_objects

    def list(self):
        """
        列出支持的工件

        Returns:
            支持的工件列表
        """
        return [
            "backtest_result.pkl",
            "metrics.json",
            "equity_curve.csv",
            "weights_history.csv",
            "trades.csv",
            "backtest_config.json",
            "strategy_config.json"
        ]

    def check(self, include_self: bool = False, parents: bool = True):
        """
        重写检查方法，使其能够处理 pred.pkl 或 pred_df.pkl，或者从TradeRecord加载目标仓位数据

        Args:
            include_self: 是否包括由self生成的文件
            parents: 是否检查父类

        Raises:
            FileNotFoundError: 如果记录未正确存储
        """
        # 如果指定了从TradeRecord加载目标仓位数据，则跳过父类检查
        if hasattr(self, 'trade_record_exp_id') and self.trade_record_exp_id is not None and \
           hasattr(self, 'trade_record_run_id') and self.trade_record_run_id is not None:
            from ..core.utils.loki_logger import get_loki_logger
            logger = get_loki_logger(__name__).logger
            logger.info(f"从TradeRecord加载目标仓位数据，跳过父类检查: exp_id={self.trade_record_exp_id}, run_id={self.trade_record_run_id}")
            return

        if parents and self.depend_cls is not None:
            # 使用class_casting临时将自身转换为依赖类
            from ..core.utils.mod import class_casting
            with class_casting(self, self.depend_cls):
                # 尝试检查 pred.pkl 或 pred_df.pkl 是否存在
                artifacts = {}

                def _get_arts(dirn):
                    if dirn not in artifacts:
                        artifacts[dirn] = self.recorder.list_artifacts(dirn)
                    return artifacts[dirn]

                # 检查 pred.pkl 或 pred_df.pkl 是否存在
                pred_found = False
                for pred_file in ["pred.pkl", "pred_df.pkl"]:
                    ps = self.get_path(pred_file).split("/")
                    dirn = "/".join(ps[:-1])
                    if self.get_path(pred_file) in _get_arts(dirn):
                        pred_found = True
                        break

                if not pred_found:
                    raise FileNotFoundError(f"Neither pred.pkl nor pred_df.pkl found")

        if include_self:
            super().check(include_self=True, parents=False)
