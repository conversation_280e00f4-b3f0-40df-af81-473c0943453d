#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易记录类

用于生成和记录目标仓位和交易订单，基于模型的预测结果
"""

import os
import pandas as pd
import numpy as np
import json
import traceback
from typing import Dict, List, Any, Optional
from datetime import datetime

from gbs.workflow.record_temp import ACRecordTemp
from gbs.workflow.signal_record import SignalRecord


class TradeRecord(ACRecordTemp):
    """
    交易记录类

    用于生成和记录目标仓位和交易订单，基于模型的预测结果
    """

    artifact_path = "trade_analysis"  # 工件路径
    depend_cls = SignalRecord  # 依赖于信号记录

    def __init__(self, recorder, top_pct=0.1, initial_capital=1000000, skip_existing=False, **kwargs):
        """
        初始化交易记录类

        Args:
            recorder: 记录器对象
            top_pct: 选择排名前多少比例的股票，默认为0.1（即前10%）
            initial_capital: 初始资金，默认为1000000
            skip_existing: 如果结果已存在，是否跳过生成
            **kwargs: 额外参数
        """
        super().__init__(recorder=recorder, skip_existing=skip_existing)
        self.top_pct = top_pct
        self.initial_capital = initial_capital

    def _generate(self, **kwargs):
        """
        生成交易记录

        Returns:
            dict: 包含交易记录的字典
        """
        from gbs.core.utils.loki_logger import get_loki_logger
        log = get_loki_logger(__name__).logger

        # 确保交易分析目录存在
        empty_info = {"info": "Trade analysis directory"}
        self.save(**{"info.json": empty_info})

        # 从SignalRecord加载预测结果
        try:
            log.info("尝试从SignalRecord加载预测结果...")
            # 首先尝试加载转换后的预测信号
            try:
                # 尝试从signal/signal目录加载
                pred = self.recorder.load_object("signal/signal/pred_df.pkl")
                log.info(f"成功从signal/signal目录加载pred_df.pkl，形状: {pred.shape}")
            except Exception as e1:
                log.warning(f"从signal/signal目录加载pred_df.pkl失败: {e1}")
                try:
                    # 尝试直接从依赖类加载
                    pred = self.load("signal/pred_df.pkl", depend_cls=SignalRecord)
                    log.info(f"成功从依赖类加载pred_df.pkl，形状: {pred.shape}")
                except Exception as e2:
                    log.warning(f"从依赖类加载pred_df.pkl失败: {e2}")
                    try:
                        # 尝试从signal/signal目录加载原始预测信号
                        pred = self.recorder.load_object("signal/signal/pred.pkl")
                        log.info(f"成功从signal/signal目录加载pred.pkl，类型: {type(pred)}")
                    except Exception as e3:
                        log.warning(f"从signal/signal目录加载pred.pkl失败: {e3}")
                        # 如果失败，尝试从依赖类加载原始预测信号
                        pred = self.load("signal/pred.pkl", depend_cls=SignalRecord)
                        log.info(f"成功从依赖类加载pred.pkl，类型: {type(pred)}")

                # 如果pred是字典格式，转换为DataFrame
                if isinstance(pred, dict):
                    log.info("预测结果是字典格式，尝试转换为DataFrame...")
                    if 'weights' in pred and 'dates' in pred and 'stock_ids' in pred:
                        weights = pred['weights']
                        dates = pred['dates']
                        stock_ids = pred['stock_ids']

                        # 创建多级索引的DataFrame
                        data = []
                        index_tuples = []

                        for i, (weight_array, date, ids) in enumerate(zip(weights, dates, stock_ids)):
                            for j, (weight, stock_id) in enumerate(zip(weight_array, ids)):
                                # 创建索引元组 (datetime, instrument)
                                index_tuples.append((date, stock_id))
                                # 添加权重数据
                                data.append([weight])

                        # 创建多级索引
                        index = pd.MultiIndex.from_tuples(index_tuples, names=['datetime', 'instrument'])

                        # 创建DataFrame
                        pred = pd.DataFrame(data, index=index, columns=['weight'])
                        log.info(f"转换后的DataFrame形状: {pred.shape}")
                    else:
                        raise ValueError("预测信号缺少必要的键: weights, dates, stock_ids")
        except Exception as e:
            log.error(f"无法加载预测结果: {e}")
            log.error(traceback.format_exc())
            return {"error.json": {"error": f"无法加载预测结果: {e}"}}

        # 生成目标仓位
        try:
            target_positions = self._generate_target_positions(pred)
            log.info(f"生成目标仓位成功，形状: {target_positions.shape}")
        except Exception as e:
            log.error(f"生成目标仓位时出错: {e}")
            log.error(traceback.format_exc())
            return {"error.json": {"error": f"生成目标仓位时出错: {e}"}}

        # 生成交易订单
        try:
            trade_orders = self._generate_trade_orders(target_positions)
            log.info(f"生成交易订单成功，共 {len(trade_orders)} 笔订单")
        except Exception as e:
            log.error(f"生成交易订单时出错: {e}")
            log.error(traceback.format_exc())
            return {"error.json": {"error": f"生成交易订单时出错: {e}"}}

        # 生成交易摘要
        trade_summary = self._generate_trade_summary(trade_orders)

        # 保存交易记录
        artifact_objects = {
            "target_positions.csv": target_positions,
            "trade_orders.csv": pd.DataFrame(trade_orders),
            "trade_summary.json": trade_summary
        }

        return artifact_objects

    def _generate_target_positions(self, pred):
        """
        根据预测结果生成目标仓位

        Args:
            pred: 预测结果DataFrame

        Returns:
            pd.DataFrame: 目标仓位DataFrame
        """
        # 确保pred是DataFrame
        if not isinstance(pred, pd.DataFrame):
            raise TypeError(f"预测结果类型错误，期望DataFrame，实际为{type(pred)}")

        # 创建一个新的DataFrame来存储目标仓位
        target_positions = pd.DataFrame(index=pred.index)

        # 添加预测值列
        target_positions['pred_value'] = pred.iloc[:, 0]

        # 按日期分组处理
        positions_list = []

        for date, group in pred.groupby(level='datetime'):
            # 按预测值排序
            sorted_group = group.sort_values(by=group.columns[0], ascending=False)

            # 计算每个日期的股票数量
            stock_count = len(sorted_group)
            top_count = max(1, int(stock_count * self.top_pct))

            # 选择排名靠前的股票
            top_stocks = sorted_group.iloc[:top_count]

            # 计算权重（等权重）
            weight = 1.0 / top_count

            # 创建该日期的仓位DataFrame
            date_positions = pd.DataFrame(index=sorted_group.index)
            date_positions['pred_value'] = sorted_group.iloc[:, 0]
            date_positions['rank'] = range(1, stock_count + 1)
            date_positions['selected'] = date_positions['rank'] <= top_count
            date_positions['weight'] = 0.0
            date_positions.loc[top_stocks.index, 'weight'] = weight
            date_positions['target_value'] = date_positions['weight'] * self.initial_capital

            positions_list.append(date_positions)

        # 合并所有日期的仓位
        if positions_list:
            target_positions = pd.concat(positions_list)

        return target_positions

    def _generate_trade_orders(self, target_positions):
        """
        根据目标仓位生成交易订单

        Args:
            target_positions: 目标仓位DataFrame

        Returns:
            list: 交易订单列表
        """
        # 初始化交易订单列表
        trade_orders = []

        # 按日期分组处理
        for date, group in target_positions.groupby(level='datetime'):
            # 只处理被选中的股票
            selected_stocks = group[group['selected']]

            for idx, row in selected_stocks.iterrows():
                # 提取股票代码
                try:
                    # 尝试从多级索引中提取股票代码
                    if isinstance(idx, tuple) and len(idx) == 2:
                        instrument, _ = idx
                    else:
                        # 如果索引不是预期的格式，使用行索引作为备用
                        instrument = idx

                    # 创建交易订单
                    order = {
                        'datetime': date,
                        'symbol': instrument,  # 使用正确的股票代码
                        'action': 'buy',  # 简化处理，只考虑买入
                        'target_weight': row['weight'],
                        'target_value': row['target_value'],
                        'pred_value': row['pred_value'],
                        'rank': row['rank']
                    }
                except Exception as e:
                    from gbs.core.utils.loki_logger import get_loki_logger
                    log = get_loki_logger(__name__).logger
                    log.warning(f"处理索引 {idx} 时出错: {e}")
                    # 创建带有错误信息的订单
                    order = {
                        'datetime': date,
                        'symbol': str(idx),  # 将整个索引转换为字符串
                        'action': 'buy',
                        'target_weight': row['weight'],
                        'target_value': row['target_value'],
                        'pred_value': row['pred_value'],
                        'rank': row['rank'],
                        'error': str(e)
                    }

                trade_orders.append(order)

        return trade_orders

    def _generate_trade_summary(self, trade_orders):
        """
        生成交易摘要

        Args:
            trade_orders: 交易订单列表

        Returns:
            dict: 交易摘要字典
        """
        from gbs.core.utils.loki_logger import get_loki_logger
        log = get_loki_logger(__name__).logger

        if not trade_orders:
            log.warning("交易订单为空，无法生成摘要")
            return {"warning": "No trade orders found"}

        # 将交易订单列表转换为DataFrame
        if isinstance(trade_orders, list):
            orders_df = pd.DataFrame(trade_orders)
        else:
            orders_df = trade_orders

        # 计算交易统计
        try:
            # 按日期统计
            date_stats = {}
            for date, group in orders_df.groupby('datetime'):
                # 将日期转换为字符串格式
                if hasattr(date, 'strftime'):
                    date_str = date.strftime('%Y-%m-%d')
                else:
                    date_str = str(date)

                date_stats[date_str] = {
                    "total_orders": len(group),
                    "total_target_value": float(group['target_value'].sum()),
                    "avg_target_value": float(group['target_value'].mean()),
                    "stock_count": len(group['symbol'].unique())
                }

            # 按股票代码分组统计
            symbol_stats = {}
            for symbol, group in orders_df.groupby('symbol'):
                symbol_stats[symbol] = {
                    "total_orders": len(group),
                    "avg_rank": float(group['rank'].mean()),
                    "avg_target_weight": float(group['target_weight'].mean()),
                    "avg_target_value": float(group['target_value'].mean()),
                    "avg_pred_value": float(group['pred_value'].mean())
                }

            # 将 date_stats 和 symbol_stats 转换为 JSON 可序列化的格式
            # 确保所有键都是字符串类型
            date_stats_json = {}
            for date_str, stats in date_stats.items():
                date_stats_json[str(date_str)] = stats

            symbol_stats_json = {}
            for symbol, stats in symbol_stats.items():
                symbol_stats_json[str(symbol)] = stats

            # 总体统计
            summary = {
                "total_orders": len(orders_df),
                "unique_dates": len(orders_df['datetime'].unique()),
                "unique_symbols": len(orders_df['symbol'].unique()),
                "total_target_value": float(orders_df['target_value'].sum()),
                "avg_target_value": float(orders_df['target_value'].mean()),
                "avg_target_weight": float(orders_df['target_weight'].mean()),
                "date_stats": date_stats_json,
                "symbol_stats": symbol_stats_json
            }

            log.info(f"生成交易摘要成功，共 {summary['total_orders']} 笔订单")
            return summary
        except Exception as e:
            log.error(f"生成交易摘要时出错: {e}")
            log.error(traceback.format_exc())
            return {"error": str(e)}

    def list(self):
        """
        列出支持的工件

        Returns:
            list: 支持的工件列表
        """
        return [
            "target_positions.csv",
            "trade_orders.csv",
            "trade_summary.json"
        ]

    def check(self, include_self: bool = False, parents: bool = True):
        """
        重写检查方法，使其能够处理 pred.pkl 或 pred_df.pkl

        Args:
            include_self: 是否包括由self生成的文件
            parents: 是否检查父类

        Raises:
            FileNotFoundError: 如果记录未正确存储
        """
        if parents:
            # 直接检查signal/signal目录下是否有预测文件
            artifacts = {}

            def _get_arts(dirn):
                if dirn not in artifacts:
                    artifacts[dirn] = self.recorder.list_artifacts(dirn)
                return artifacts[dirn]

            # 检查signal/signal目录下是否有pred.pkl或pred_df.pkl
            pred_found = False

            # 检查signal/signal目录
            signal_dir = "signal/signal"
            signal_arts = _get_arts(signal_dir)

            for pred_file in ["pred.pkl", "pred_df.pkl"]:
                if f"{signal_dir}/{pred_file}" in signal_arts:
                    pred_found = True
                    from gbs.core.utils.loki_logger import get_loki_logger
                    log = get_loki_logger(__name__).logger
                    log.info(f"在{signal_dir}目录下找到{pred_file}")
                    break

            # 如果在signal/signal目录下没有找到，检查signal目录
            if not pred_found:
                signal_dir = "signal"
                signal_arts = _get_arts(signal_dir)

                for pred_file in ["pred.pkl", "pred_df.pkl"]:
                    if f"{signal_dir}/{pred_file}" in signal_arts:
                        pred_found = True
                        from gbs.core.utils.loki_logger import get_loki_logger
                        log = get_loki_logger(__name__).logger
                        log.info(f"在{signal_dir}目录下找到{pred_file}")
                        break

            # 如果在signal目录下也没有找到，检查根目录
            if not pred_found and self.depend_cls is not None:
                # 使用class_casting临时将自身转换为依赖类
                from ..core.utils.mod import class_casting
                with class_casting(self, self.depend_cls):
                    for pred_file in ["pred.pkl", "pred_df.pkl"]:
                        ps = self.get_path(pred_file).split("/")
                        dirn = "/".join(ps[:-1])
                        if self.get_path(pred_file) in _get_arts(dirn):
                            pred_found = True
                            from gbs.core.utils.loki_logger import get_loki_logger
                            log = get_loki_logger(__name__).logger
                            log.info(f"在{self.get_path(pred_file)}找到{pred_file}")
                            break

            if not pred_found:
                from gbs.core.utils.loki_logger import get_loki_logger
                log = get_loki_logger(__name__).logger
                log.error(f"在任何目录下都没有找到pred.pkl或pred_df.pkl")
                raise FileNotFoundError(f"在任何目录下都没有找到pred.pkl或pred_df.pkl")

        if include_self:
            super().check(include_self=True, parents=False)
