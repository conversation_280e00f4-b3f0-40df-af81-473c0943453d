#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
记录模板模块

提供用于生成和记录实验结果的模板类
"""

from typing import Dict, Optional
from ..core.utils.exceptions import LoadObjectError


class RecordTemp:
    """
    记录模板基类

    用于生成和记录实验结果的模板类，如模型预测、回测结果等
    """

    artifact_path = None  # 工件路径，用于组织记录
    depend_cls = None  # 依赖的记录类，该记录将依赖于由depend_cls生成的结果

    @classmethod
    def get_path(cls, path=None):
        """
        获取完整路径

        Args:
            path: 额外的路径

        Returns:
            完整路径
        """
        names = []
        if cls.artifact_path is not None:
            names.append(cls.artifact_path)

        if path is not None:
            names.append(path)

        return "/".join(names)

    def __init__(self, recorder):
        """
        初始化记录模板

        Args:
            recorder: 记录器对象
        """
        self._recorder = recorder

    @property
    def recorder(self):
        """获取记录器"""
        if self._recorder is None:
            raise ValueError("This RecordTemp did not set recorder yet.")
        return self._recorder

    def save(self, **kwargs):
        """
        保存对象

        与self.recorder.save_objects行为相同，但提供了更简单的接口
        用户不必关心get_path和artifact_path

        Args:
            **kwargs: 要保存的对象，格式为name=value
        """
        art_path = self.get_path()
        if art_path == "":
            art_path = None

        # 确保至少有一个对象要保存
        if not kwargs:
            # 如果没有对象要保存，创建一个空的info.json文件
            empty_info = {"info": "No data available"}
            kwargs["info.json"] = empty_info

        self.recorder.save_objects(artifact_path=art_path, **kwargs)

    def generate(self, **kwargs):
        """
        生成记录并保存

        Args:
            **kwargs: 额外参数

        Returns:
            生成的记录
        """
        raise NotImplementedError(f"Please implement the `generate` method.")

    def load(self, name: str, parents: bool = True):
        """
        加载对象

        与self.recorder.load_object行为相同，但提供了更简单的接口
        用户不必关心get_path和artifact_path

        Args:
            name: 要加载的文件名
            parents: 是否递归查找父类路径

        Returns:
            加载的对象
        """
        path = self.get_path(name)

        try:
            return self.recorder.load_object(path)
        except LoadObjectError as e:
            if parents:
                if self.depend_cls is not None:
                    # 使用class_casting临时将自身转换为依赖类
                    from ..core.utils.mod import class_casting
                    with class_casting(self, self.depend_cls):
                        return self.load(name, parents=True)
            raise e

    def list(self):
        """
        列出支持的工件

        Returns:
            支持的工件列表
        """
        return []

    def check(self, include_self: bool = False, parents: bool = True):
        """
        检查记录是否正确生成和保存

        在以下情况下很有用：
        - 在生成新内容前检查依赖文件是否完整
        - 检查最终文件是否完整

        Args:
            include_self: 是否包括由self生成的文件
            parents: 是否检查父类

        Raises:
            FileNotFoundError: 如果记录未正确存储
        """
        if include_self:
            artifacts = {}

            def _get_arts(dirn):
                if dirn not in artifacts:
                    artifacts[dirn] = self.recorder.list_artifacts(dirn)
                return artifacts[dirn]

            for item in self.list():
                ps = self.get_path(item).split("/")
                dirn = "/".join(ps[:-1])
                if self.get_path(item) not in _get_arts(dirn):
                    raise FileNotFoundError(f"File {self.get_path(item)} not found")
        if parents:
            if self.depend_cls is not None:
                # 使用class_casting临时将自身转换为依赖类
                from ..core.utils.mod import class_casting
                with class_casting(self, self.depend_cls):
                    self.check(include_self=True)


class ACRecordTemp(RecordTemp):
    """
    自动检查记录模板

    在生成前自动检查依赖，如果依赖不满足则跳过生成
    """

    def __init__(self, recorder, skip_existing=False):
        """
        初始化自动检查记录模板

        Args:
            recorder: 记录器对象
            skip_existing: 如果结果已存在，是否跳过生成
        """
        self.skip_existing = skip_existing
        super().__init__(recorder=recorder)

    def generate(self, *args, **kwargs):
        """
        自动检查文件然后运行具体的生成任务

        Args:
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            生成的工件字典
        """
        from ..core.utils.loki_logger import get_loki_logger
        log = get_loki_logger(__name__).logger

        if self.skip_existing:
            try:
                self.check(include_self=True, parents=False)
                # 结果已存在，跳过生成
                return
            except FileNotFoundError:
                pass  # 继续生成

        # 确保目录存在，即使依赖数据不存在
        empty_info = {"info": f"{self.__class__.__name__} directory"}
        self.save(**{"info.json": empty_info})

        try:
            self.check()
        except FileNotFoundError:
            # 依赖数据不存在，跳过生成
            log.warning(f"依赖数据不存在，跳过生成 {self.__class__.__name__}")
            return

        artifact_dict = self._generate(*args, **kwargs)
        if isinstance(artifact_dict, dict):
            self.save(**artifact_dict)
        return artifact_dict

    def _generate(self, *args, **kwargs) -> Dict[str, object]:
        """
        运行具体的生成任务，返回生成结果的字典

        调用方法将结果保存到记录器

        Args:
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            生成的工件字典
        """
        raise NotImplementedError(f"Please implement the `_generate` method")







