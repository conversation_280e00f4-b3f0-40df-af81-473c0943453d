#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Utility functions for workflow management.
"""

import atexit
import os
import sys
import traceback
import yaml
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional, Text, Union, Callable
from datetime import datetime

from . import R
from ..core.utils.loki_logger import get_loki_logger
from ..core.utils.serial import Serializable
from ..core.utils.mod import init_instance_by_config
from .recorder import Recorder

logger = get_loki_logger("workflow").logger

def transform_end_date(date):
    """
    Transform end date to datetime format.

    Parameters
    ----------
    date : str or datetime
        End date

    Returns
    -------
    datetime
        End date in datetime format
    """
    if isinstance(date, str):
        date = pd.Timestamp(date)
    elif isinstance(date, pd.Timestamp):
        date = date.to_pydatetime()
    return date

def experiment_exit_handler():
    """
    This function will be called when python program exits.
    It will clean up the experiment and recorder.
    """
    from . import R

    if R._provider is not None:
        if getattr(R._provider, "exp_manager", None) is not None:
            if getattr(R._provider.exp_manager, "active_experiment", None) is not None:
                logger.info("Experiment is not closed properly. Cleaning up...")
                R.end_exp("FAILED")


atexit.register(experiment_exit_handler)


def experiment_exception_hook(exc_type, value, tb):
    """
    处理未捕获异常并将实验状态设置为"失败"

    Args:
        exc_type: 异常类型
        value: 异常值
        tb: 异常的traceback
    """
    logger.error(f"发生未捕获的异常[{exc_type.__name__}: {value}]")
    # 与原始格式相同
    traceback.print_tb(tb)
    print(f"{exc_type.__name__}: {value}")
    R.end_exp(recorder_status=Recorder.STATUS_FA)


def init_exp_manager(uri: Optional[Text] = None, default_exp_name: Optional[Text] = "Experiment"):
    """
    Initialize the experiment manager.

    Parameters
    ----------
    uri : str
        The tracking uri of the experiment, where all the artifacts/metrics etc. will be stored.
    default_exp_name : str
        The default experiment name.
    """
    from . import R, GbsRecorder
    from .expm import MLflowExpManager
    from ..core.utils.paths import MLFLOW_DIR

    if uri is None:
        # 默认使用项目根目录下的 mlruns 作为 tracking URI
        uri = f"file:{MLFLOW_DIR}"

    # Create the experiment manager
    exp_manager = MLflowExpManager(uri=uri, default_exp_name=default_exp_name)

    # Create the GBS recorder
    gbs_recorder = GbsRecorder(exp_manager)

    # Register the GBS recorder
    R.register(gbs_recorder)

    logger.info(f"Experiment manager initialized with uri: {uri}")


def load_config(config_path: Union[Text, Path]) -> Dict[Text, Any]:
    """
    Load config from yaml file.

    Parameters
    ----------
    config_path : str or Path
        The path to the config file.

    Returns
    -------
    dict
        The config dictionary.
    """
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)
    return config


def load_and_convert_prediction(loader_func: Callable, log=None):
    """
    加载并转换预测信号

    Args:
        loader_func: 加载函数，接受文件名并返回加载的对象
        log: 日志记录器，如果为None则不记录日志

    Returns:
        转换后的预测信号DataFrame
    """
    try:
        # 首先尝试加载转换后的预测信号
        pred = loader_func("pred_df.pkl")
        if log: log.info("成功加载pred_df.pkl")
        return pred
    except Exception:
        if log: log.info("加载pred_df.pkl失败，尝试加载pred.pkl")
        # 如果失败，尝试加载原始预测信号
        pred = loader_func("pred.pkl")

        # 检查预测信号格式并转换
        if isinstance(pred, dict):
            # 尝试转换为DataFrame格式
            if 'weights' in pred and 'dates' in pred and 'stock_ids' in pred:
                weights = pred['weights']
                dates = pred['dates']
                stock_ids = pred['stock_ids']

                # 创建多级索引的DataFrame
                data = []
                index_tuples = []

                for i, (weight_array, date, ids) in enumerate(zip(weights, dates, stock_ids)):
                    for j, (weight, stock_id) in enumerate(zip(weight_array, ids)):
                        # 创建索引元组 (datetime, instrument)
                        index_tuples.append((date, stock_id))
                        # 添加权重数据
                        data.append([weight])

                # 创建多级索引
                index = pd.MultiIndex.from_tuples(index_tuples, names=['datetime', 'instrument'])

                # 创建DataFrame
                pred_df = pd.DataFrame(data, index=index, columns=['weight'])
                if log: log.info(f"成功将字典转换为DataFrame，形状: {pred_df.shape}")
                return pred_df
            else:
                raise ValueError("预测信号缺少必要的键: weights, dates, stock_ids")
        elif hasattr(pred, '__class__') and pred.__class__.__name__ == 'Series':
            # 如果是Series，转换为DataFrame
            pred_df = pred.to_frame("weight")
            if log: log.info(f"成功将Series转换为DataFrame，形状: {pred_df.shape}")
            return pred_df
        elif hasattr(pred, '__class__') and pred.__class__.__name__ == 'DataFrame':
            # 如果是DataFrame，确保有正确的列名
            if 'weight' not in pred.columns and len(pred.columns) > 0:
                # 如果没有weight列，但有其他列，将第一列重命名为weight
                pred_df = pred.copy()
                pred_df.columns = ['weight'] + list(pred.columns[1:])
                if log: log.info("将DataFrame的第一列重命名为'weight'")
            else:
                pred_df = pred
            return pred_df
        else:
            # 其他格式，尝试转换
            if log: log.warning(f"无法处理的预测信号类型: {type(pred)}")
            return pred
