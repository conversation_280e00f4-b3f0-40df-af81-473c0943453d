#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
信号分析记录模块

提供用于生成和记录信号分析结果的类
"""

from typing import Optional
import pandas as pd
import numpy as np

from .record_temp import ACRecordTemp
from .signal_record import SignalRecord


class SignalAnalysisRecord(ACRecordTemp):
    """
    信号分析记录类

    用于生成和记录信号分析结果，包括IC、Rank IC等指标
    """

    artifact_path = "signal_analysis"  # 工件路径
    depend_cls = SignalRecord  # 依赖于信号记录

    def __init__(
        self,
        recorder,
        ana_long_short=False,
        ann_scaler=252,
        label_col=0,
        skip_existing=False,
        **kwargs
    ):
        """
        初始化信号分析记录类

        Args:
            recorder: 记录器对象
            ana_long_short: 是否分析多空收益
            ann_scaler: 年化因子
            label_col: 标签列索引
            skip_existing: 如果结果已存在，是否跳过生成
            **kwargs: 额外参数
        """
        super().__init__(recorder=recorder, skip_existing=skip_existing)
        self.ana_long_short = ana_long_short
        self.ann_scaler = ann_scaler
        self.label_col = label_col

    def _generate(self, label: Optional[pd.DataFrame] = None, **kwargs):
        """
        生成信号分析结果

        Args:
            label: 标签数据，如果为None则从依赖中加载
            **kwargs: 额外参数

        Returns:
            生成的工件字典
        """
        from ..eval_system.analysis.metrics import calc_long_short_return, calc_long_short_precision

        # 确保信号分析目录存在
        # 创建一个空的info.json文件，确保目录被创建
        empty_info = {"info": "Signal analysis directory"}
        self.save(**{"info.json": empty_info})

        # 加载预测和标签
        try:
            # 首先尝试加载转换后的预测信号
            pred = self.load("pred_df.pkl")
        except Exception:
            # 如果失败，尝试加载原始预测信号
            pred = self.load("pred.pkl")

            # 检查预测信号格式并转换
            if isinstance(pred, dict):
                # 尝试转换为DataFrame格式
                if 'weights' in pred and 'dates' in pred and 'stock_ids' in pred:
                    weights = pred['weights']
                    dates = pred['dates']
                    stock_ids = pred['stock_ids']

                    # 创建多级索引的 DataFrame
                    data = []
                    index_tuples = []

                    for i, (weight_array, date, ids) in enumerate(zip(weights, dates, stock_ids)):
                        for j, (weight, stock_id) in enumerate(zip(weight_array, ids)):
                            # 创建索引元组 (datetime, instrument)
                            index_tuples.append((date, stock_id))
                            # 添加权重数据
                            data.append([weight])

                    # 创建多级索引
                    index = pd.MultiIndex.from_tuples(index_tuples, names=['datetime', 'instrument'])

                    # 创建 DataFrame
                    pred = pd.DataFrame(data, index=index, columns=['weight'])

                    # 保存转换后的预测信号
                    self.save(**{"pred_df.pkl": pred})
                else:
                    raise ValueError("预测信号缺少必要的键: weights, dates, stock_ids")

        if label is None:
            from ..core.utils.loki_logger import get_loki_logger
            log = get_loki_logger(__name__).logger

            try:
                # 首先尝试从当前目录加载label.pkl
                label = self.load("label.pkl")
                log.info(f"成功从当前目录加载标签数据，形状: {label.shape}")
            except Exception as e1:
                log.warning(f"无法从当前目录加载label.pkl: {str(e1)}")

                try:
                    # 尝试从label目录加载label.pkl
                    # 使用相对路径访问label目录
                    label_path = "../label/label.pkl"
                    label = self.load(label_path)
                    log.info(f"成功从label目录加载标签数据，形状: {label.shape}")
                except Exception as e2:
                    log.warning(f"无法从label目录加载label.pkl: {str(e2)}")

                    try:
                        # 尝试直接从recorder的artifacts中加载
                        # 获取recorder的artifact路径
                        artifact_uri = self.recorder.get_artifact_uri()
                        if artifact_uri.startswith("file:"):
                            from urllib.parse import urlparse
                            parsed_uri = urlparse(artifact_uri)
                            artifact_path = parsed_uri.path
                        else:
                            artifact_path = artifact_uri

                        import os
                        label_file_path = os.path.join(artifact_path, "label", "label.pkl")

                        if os.path.exists(label_file_path):
                            from ..core.utils.serial import Serializable
                            label = Serializable.general_load(label_file_path)
                            log.info(f"成功从绝对路径加载标签数据，形状: {label.shape}")
                        else:
                            log.error(f"标签文件不存在: {label_file_path}")
                            raise FileNotFoundError(f"标签文件不存在: {label_file_path}")

                    except Exception as e3:
                        log.error(f"所有加载标签数据的尝试都失败了")
                        log.error(f"尝试1 (当前目录): {str(e1)}")
                        log.error(f"尝试2 (label目录): {str(e2)}")
                        log.error(f"尝试3 (绝对路径): {str(e3)}")
                        raise FileNotFoundError(f"无法从任何位置加载标签数据")

        # 如果标签为空或全为NaN，记录警告并返回空结果
        if label is None or label.empty or label["label"].isna().all():
            warning_msg = "标签数据不可用或全为NaN，无法进行完整的信号分析。将只生成基本信息。"
            self.recorder.log_metrics(**{"warning": 1})
            self.save(**{"warning.json": {"warning": warning_msg}})
            return {"warning.json": {"warning": warning_msg}}

        # 计算IC和Rank IC
        from ..core.utils.loki_logger import get_loki_logger
        log = get_loki_logger(__name__).logger
        log.info("开始计算IC和Rank IC...")

        # 提取预测值和标签值为Series
        try:
            # 提取预测值
            if isinstance(pred, pd.DataFrame):
                pred_series = pred.iloc[:, 0]
            elif isinstance(pred, pd.Series):
                pred_series = pred
            else:
                print(f"无法处理的预测值类型: {type(pred)}")
                pred_series = None

            # 提取标签值
            if isinstance(label, pd.DataFrame):
                if "label" in label.columns:
                    label_series = label["label"]
                elif label.shape[1] > self.label_col:
                    label_series = label.iloc[:, self.label_col]
                else:
                    print(f"标签列索引 {self.label_col} 超出范围，标签DataFrame形状: {label.shape}")
                    label_series = None
            elif isinstance(label, pd.Series):
                label_series = label
            else:
                print(f"无法处理的标签值类型: {type(label)}")
                label_series = None

            # 检查是否成功提取了预测值和标签值
            if pred_series is None or label_series is None:
                print("无法提取预测值或标签值，使用默认值")
                ic, ric = 0, 0
            else:
                print(f"预测值Series形状: {pred_series.shape}")
                print(f"标签值Series形状: {label_series.shape}")

                # 查找共同索引
                common_idx = pred_series.index.intersection(label_series.index)
                print(f"找到 {len(common_idx)} 个共同索引")

                if len(common_idx) > 0:
                    # 使用共同索引计算相关系数
                    ic = pred_series.loc[common_idx].corr(label_series.loc[common_idx])
                    ric = pred_series.loc[common_idx].rank().corr(label_series.loc[common_idx].rank())
                    print(f"使用共同索引计算IC: {ic}, Rank IC: {ric}")
                else:
                    # 如果没有共同索引，但长度相同，尝试直接计算
                    if len(pred_series) == len(label_series):
                        print("没有共同索引，但长度相同，尝试直接计算")
                        ic = np.corrcoef(pred_series, label_series)[0, 1]
                        ric = np.corrcoef(pred_series.rank(), label_series.rank())[0, 1]
                        print(f"直接计算IC: {ic}, Rank IC: {ric}")
                    else:
                        print("没有共同索引且长度不同，无法计算IC")
                        ic, ric = 0, 0
        except Exception as e:
            print(f"计算IC时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            ic, ric = 0, 0

        # 计算基本指标
        try:
            # 如果ic是Series，计算均值和标准差
            if isinstance(ic, pd.Series):
                metrics = {
                    "IC": ic.mean(),
                    "ICIR": ic.mean() / ic.std() if ic.std() != 0 else 0,
                    "Rank IC": ric.mean(),
                    "Rank ICIR": ric.mean() / ric.std() if ric.std() != 0 else 0,
                }
            # 如果ic是标量，直接使用
            elif isinstance(ic, (int, float)):
                metrics = {
                    "IC": ic,
                    "ICIR": 0,  # 无法计算ICIR
                    "Rank IC": ric,
                    "Rank ICIR": 0,  # 无法计算Rank ICIR
                }
            else:
                # 如果ic是其他类型，使用默认值
                log.warning(f"无法计算指标，ic类型: {type(ic)}")
                metrics = {
                    "IC": 0,
                    "ICIR": 0,
                    "Rank IC": 0,
                    "Rank ICIR": 0,
                }
        except Exception as e:
            log.error(f"计算指标时出错: {str(e)}")
            metrics = {
                "IC": 0,
                "ICIR": 0,
                "Rank IC": 0,
                "Rank ICIR": 0,
            }

        # 准备保存的工件
        artifact_objects = {
            "ic.pkl": ic,
            "ric.pkl": ric,
        }

        # 如果需要分析多空收益
        if self.ana_long_short:
            try:
                # 提取预测值和标签值为Series
                if isinstance(pred, pd.DataFrame):
                    pred_series = pred.iloc[:, 0]
                elif isinstance(pred, pd.Series):
                    pred_series = pred
                else:
                    print(f"无法处理的预测值类型: {type(pred)}")
                    pred_series = None

                # 提取标签值
                if isinstance(label, pd.DataFrame):
                    if "label" in label.columns:
                        label_series = label["label"]
                    elif label.shape[1] > self.label_col:
                        label_series = label.iloc[:, self.label_col]
                    else:
                        print(f"标签列索引 {self.label_col} 超出范围，标签DataFrame形状: {label.shape}")
                        label_series = None
                elif isinstance(label, pd.Series):
                    label_series = label
                else:
                    print(f"无法处理的标签值类型: {type(label)}")
                    label_series = None

                # 检查是否成功提取了预测值和标签值
                if pred_series is None or label_series is None:
                    print("无法提取预测值或标签值，跳过多空收益分析")
                    # 创建空的结果
                    long_short_r = pd.Series()
                    long_avg_r = pd.Series()
                    long_pre = pd.Series()
                    short_pre = pd.Series()
                else:
                    # 查找共同索引
                    common_idx = pred_series.index.intersection(label_series.index)
                    print(f"多空收益分析：找到 {len(common_idx)} 个共同索引")

                    if len(common_idx) > 0:
                        # 使用共同索引计算多空收益
                        long_short_r, long_avg_r = calc_long_short_return(
                            pred_series.loc[common_idx], label_series.loc[common_idx]
                        )

                        # 计算多空精度
                        long_pre, short_pre = calc_long_short_precision(
                            pred_series.loc[common_idx], label_series.loc[common_idx], is_alpha=True
                        )
                    else:
                        print("没有共同索引，跳过多空收益分析")
                        # 创建空的结果
                        long_short_r = pd.Series()
                        long_avg_r = pd.Series()
                        long_pre = pd.Series()
                        short_pre = pd.Series()
            except Exception as e:
                print(f"计算多空收益时出错: {str(e)}")
                import traceback
                print(traceback.format_exc())
                # 创建空的结果
                long_short_r = pd.Series()
                long_avg_r = pd.Series()
                long_pre = pd.Series()
                short_pre = pd.Series()

            # 添加多空指标
            try:
                # 检查Series是否为空
                if len(long_short_r) > 0 and len(long_avg_r) > 0 and len(long_pre) > 0 and len(short_pre) > 0:
                    metrics.update({
                        "Long-Short Ann Return": long_short_r.mean() * self.ann_scaler,
                        "Long-Short Ann Sharpe": long_short_r.mean() / long_short_r.std() * self.ann_scaler**0.5 if long_short_r.std() != 0 else 0,
                        "Long-Avg Ann Return": long_avg_r.mean() * self.ann_scaler,
                        "Long-Avg Ann Sharpe": long_avg_r.mean() / long_avg_r.std() * self.ann_scaler**0.5 if long_avg_r.std() != 0 else 0,
                        "Long Precision": long_pre.mean(),
                        "Short Precision": short_pre.mean(),
                    })
                else:
                    log.warning("多空收益Series为空，使用默认值")
                    metrics.update({
                        "Long-Short Ann Return": 0,
                        "Long-Short Ann Sharpe": 0,
                        "Long-Avg Ann Return": 0,
                        "Long-Avg Ann Sharpe": 0,
                        "Long Precision": 0,
                        "Short Precision": 0,
                    })
            except Exception as e:
                log.error(f"计算多空指标时出错: {str(e)}")
                log.error(traceback.format_exc())
                metrics.update({
                    "Long-Short Ann Return": 0,
                    "Long-Short Ann Sharpe": 0,
                    "Long-Avg Ann Return": 0,
                    "Long-Avg Ann Sharpe": 0,
                    "Long Precision": 0,
                    "Short Precision": 0,
                })

            # 添加多空工件
            artifact_objects.update({
                "long_short_r.pkl": long_short_r,
                "long_avg_r.pkl": long_avg_r,
                "long_pre.pkl": long_pre,
                "short_pre.pkl": short_pre,
            })

        # 记录指标
        self.recorder.log_metrics(**metrics)

        return artifact_objects

    def list(self):
        """
        列出支持的工件

        Returns:
            支持的工件列表
        """
        paths = ["ic.pkl", "ric.pkl"]
        if self.ana_long_short:
            paths.extend([
                "long_short_r.pkl",
                "long_avg_r.pkl",
                "long_pre.pkl",
                "short_pre.pkl"
            ])
        return paths

    def check(self, include_self: bool = False, parents: bool = True):
        """
        重写检查方法，使其能够处理 pred.pkl 或 pred_df.pkl

        Args:
            include_self: 是否包括由self生成的文件
            parents: 是否检查父类

        Raises:
            FileNotFoundError: 如果记录未正确存储
        """
        if parents and self.depend_cls is not None:
            # 使用class_casting临时将自身转换为依赖类
            from ..core.utils.mod import class_casting
            with class_casting(self, self.depend_cls):
                # 尝试检查 pred.pkl 或 pred_df.pkl 是否存在
                artifacts = {}

                def _get_arts(dirn):
                    if dirn not in artifacts:
                        artifacts[dirn] = self.recorder.list_artifacts(dirn)
                    return artifacts[dirn]

                # 检查 pred.pkl 或 pred_df.pkl 是否存在
                pred_found = False
                for pred_file in ["pred.pkl", "pred_df.pkl"]:
                    ps = self.get_path(pred_file).split("/")
                    dirn = "/".join(ps[:-1])
                    if self.get_path(pred_file) in _get_arts(dirn):
                        pred_found = True
                        break

                if not pred_found:
                    raise FileNotFoundError(f"Neither pred.pkl nor pred_df.pkl found")

        if include_self:
            super().check(include_self=True, parents=False)


