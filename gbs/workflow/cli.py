#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Command-line interface for running workflow configurations.
Similar to qlib's qrun command.
"""

import logging
import os
import sys
from pathlib import Path

import fire
import yaml

import gbs
from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.utils.mod import init_instance_by_config
from gbs.model_system.base.trainer import task_train
from gbs.workflow.utils import load_config, init_exp_manager
from gbs.workflow import R

logger = get_loki_logger("gbsrun").logger

def get_path_list(path):
    """Convert a path or list of paths to a list."""
    if isinstance(path, str):
        return [path]
    else:
        return list(path)

def sys_config(config, config_path):
    """
    Configure the `sys` section of the config.

    Parameters
    ----------
    config : dict
        Configuration of the workflow.
    config_path : str
        Path of the configuration file.
    """
    sys_config = config.get("sys", {})

    # absolute paths
    for p in get_path_list(sys_config.get("path", [])):
        sys.path.append(p)

    # relative paths to config path
    for p in get_path_list(sys_config.get("rel_path", [])):
        sys.path.append(str(Path(config_path).parent.resolve().absolute() / p))

def workflow(config_path, experiment_name=None, recorder_name=None, uri_folder="mlruns"):
    """
    GBS workflow CLI entrance.
    Run the whole quant research workflow defined by a configuration file.

    Parameters
    ----------
    config_path : str
        Path to the configuration file.
    experiment_name : str, optional
        Name of the experiment. If None, use the name from the config or a default.
    recorder_name : str, optional
        Name of the recorder. If None, use the name from the config or a default.
    uri_folder : str, optional
        Folder to store MLflow tracking data if using file-based tracking.
    """
    # Load the configuration file
    config = load_config(config_path)

    # Check for BASE_CONFIG_PATH
    if "BASE_CONFIG_PATH" in config:
        base_config_path = config.pop("BASE_CONFIG_PATH")
        # Try to resolve the path
        path = Path(base_config_path)
        if not path.is_absolute():
            # Try relative to the config file
            path = Path(config_path).parent / base_config_path
            if not path.exists():
                # Try relative to the current directory
                path = Path(os.getcwd()) / base_config_path

        if not path.exists():
            logger.error(f"BASE_CONFIG_PATH not found: {base_config_path}")
            return

        # Load the base config
        base_config = load_config(path)

        # Update base config with current config
        base_config.update(config)
        config = base_config
        logger.info(f"Loaded BASE_CONFIG_PATH: {path}")

    # Configure the sys section
    sys_config(config, config_path)

    # Initialize GBS if gbs_init is present
    if "gbs_init" in config:
        gbs_init = config.get("gbs_init", {})
        # If exp_manager is not in gbs_init, set up a default one
        if "exp_manager" not in gbs_init:
            from gbs.workflow.expm import MLflowExpManager
            exp_manager = {
                "class": "MLflowExpManager",
                "module_path": "gbs.workflow.expm",
                "kwargs": {
                    "uri": f"file:{uri_folder}",
                    "default_exp_name": experiment_name or "workflow"
                }
            }
            gbs_init["exp_manager"] = exp_manager

        # Initialize GBS
        gbs.init(**gbs_init)
    else:
        # 如果没有gbs_init，使用默认配置初始化
        logger.warning("No gbs_init configuration found, using default initialization")

    # 获取实验管理器URI
    # 从gbs_init中获取exp_manager配置
    if 'gbs_init' in locals():
        exp_manager_config = gbs_init.get("exp_manager", {})
        uri = exp_manager_config.get("kwargs", {}).get("uri", None)
        default_exp_name = exp_manager_config.get("kwargs", {}).get("default_exp_name", "Experiment")
    else:
        # 使用默认配置
        exp_manager_config = {}
        uri = None
        default_exp_name = "Experiment"

    logger.info(f"Using experiment manager URI: {uri}, default experiment name: {default_exp_name}")

    # 初始化实验管理器
    init_exp_manager(uri, default_exp_name)

    # 获取任务配置
    task_config = config.get("task", {})

    # Override experiment_name if provided
    if experiment_name is not None:
        config["experiment"] = config.get("experiment", {})
        config["experiment"]["name"] = experiment_name
        experiment_name = experiment_name
    else:
        experiment_name = config.get("experiment", {}).get("name", task_config.get("experiment_name", default_exp_name))

    # 确保task_config包含必要的字段
    if "model" not in task_config:
        task_config["model"] = config.get("model", {})

    if "dataset" not in task_config:
        task_config["dataset"] = config.get("dataset", {})

    # 添加workflow配置到task_config
    if "workflow" not in task_config:
        task_config["workflow"] = config.get("workflow", {})

    # 添加记录配置，用于模型评估
    if "record" not in task_config:
        task_config["record"] = []

    # 确保记录配置是列表
    if not isinstance(task_config["record"], list):
        task_config["record"] = [task_config["record"]]

    # 添加信号记录和信号分析记录
    signal_record_exists = False
    signal_analysis_exists = False

    for record in task_config["record"]:
        if record.get("class") == "SignalRecord":
            signal_record_exists = True
        if record.get("class") == "SignalAnalysisRecord":
            signal_analysis_exists = True

    if not signal_record_exists:
        task_config["record"].append({
            "class": "SignalRecord",
            "module_path": "gbs.workflow.record_temp",
            "kwargs": {
                "model": "<MODEL>",
                "dataset": "<DATASET>"
            }
        })

    if not signal_analysis_exists:
        task_config["record"].append({
            "class": "SignalAnalysisRecord",
            "module_path": "gbs.workflow.record_temp",
            "kwargs": {
                "ana_long_short": True,
                "ann_scaler": 252
            }
        })

    # 执行任务训练
    recorder = task_train(task_config, experiment_name=experiment_name, recorder_name=recorder_name)

    # 保存配置
    recorder.save_objects(config=config)

    logger.info(f"Workflow completed successfully with recorder ID: {recorder.info['id']}")

    return recorder

def run():
    """Entry point for the gbsrun command."""
    fire.Fire(workflow)

if __name__ == "__main__":
    run()
