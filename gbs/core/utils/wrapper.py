#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
包装器模块
提供包装器类和注册函数
"""

class Wrapper:
    """
    包装器类，用于在初始化时设置的任何内容
    """

    def __init__(self):
        self._provider = None

    def register(self, provider):
        self._provider = provider

    def __repr__(self):
        return "{name}(provider={provider})".format(name=self.__class__.__name__, provider=self._provider)

    def __getattr__(self, key):
        if self.__dict__.get("_provider", None) is None:
            raise AttributeError("请先初始化系统")
        return getattr(self._provider, key)


def register_wrapper(wrapper, cls_or_obj, module_path=None):
    """
    注册包装器

    Args:
        wrapper: 包装器
        cls_or_obj: 类、类名或对象实例
        module_path: 模块路径
    """
    if isinstance(cls_or_obj, str):
        from .mod import get_module_by_module_path
        module = get_module_by_module_path(module_path)
        cls_or_obj = getattr(module, cls_or_obj)
    obj = cls_or_obj() if isinstance(cls_or_obj, type) else cls_or_obj
    wrapper.register(obj)
