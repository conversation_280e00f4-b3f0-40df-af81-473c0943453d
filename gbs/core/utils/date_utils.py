#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日期和时间处理工具模块
- 提供日期格式转换和映射功能
- 提供时间处理和转换功能
- 包括日历生成、时间对齐等
"""

import bisect
import functools
import re
import pandas as pd
import numpy as np
from typing import Dict, Union, List, Optional, Tuple
from datetime import datetime, time, date, timedelta

# 全局日期映射缓存
_DATE_MAPPING_CACHE = {}

# 定义市场时间常量
CN_TIME = [
    datetime.strptime("9:30", "%H:%M"),
    datetime.strptime("11:30", "%H:%M"),
    datetime.strptime("13:00", "%H:%M"),
    datetime.strptime("15:00", "%H:%M"),
]
US_TIME = [datetime.strptime("9:30", "%H:%M"), datetime.strptime("16:00", "%H:%M")]
TW_TIME = [
    datetime.strptime("9:00", "%H:%M"),
    datetime.strptime("13:30", "%H:%M"),
]

# 定义区域常量
REG_CN = "cn"
REG_US = "us"
REG_TW = "tw"

def convert_to_datetime(date: Union[str, datetime, pd.Timestamp, None]) -> Optional[pd.Timestamp]:
    """
    Convert a date to pandas Timestamp.

    Args:
        date: Date to convert, can be string, datetime, or Timestamp.

    Returns:
        Converted pandas Timestamp or None if input is None.
    """
    if date is None:
        return None

    if isinstance(date, pd.Timestamp):
        return date

    if isinstance(date, datetime.datetime):
        return pd.Timestamp(date)

    if isinstance(date, str):
        return pd.Timestamp(date)

    raise TypeError(f"Unsupported date type: {type(date)}")

def get_month_end(date: Union[str, pd.Timestamp, datetime]) -> pd.Timestamp:
    """
    获取给定日期所在月份的最后一天

    Args:
        date: 输入日期，可以是字符串、Timestamp或datetime对象

    Returns:
        pd.Timestamp: 月末日期
    """
    # 转换为pandas Timestamp
    if not isinstance(date, pd.Timestamp):
        date = pd.Timestamp(date)

    # 获取月末日期
    month_end = pd.Timestamp(date.year, date.month, 1) + pd.offsets.MonthEnd(1)
    return month_end

def create_date_mapping(dates: List[Union[str, pd.Timestamp, datetime]]) -> Dict[str, pd.Timestamp]:
    """
    创建日期到月末日期的映射

    Args:
        dates: 日期列表

    Returns:
        Dict: 日期到月末日期的映射字典
    """
    mapping = {}
    for date in dates:
        # 转换为pandas Timestamp
        if not isinstance(date, pd.Timestamp):
            date = pd.Timestamp(date)

        # 获取月末日期
        month_end = get_month_end(date)

        # 存储映射
        date_str = date.strftime('%Y-%m-%d')
        mapping[date_str] = month_end

    return mapping

def align_dates(daily_dates: List[Union[str, pd.Timestamp]],
                monthly_dates: List[Union[str, pd.Timestamp]],
                cache_key: Optional[str] = None) -> Dict[str, pd.Timestamp]:
    """
    将日期列表与月末日期列表对齐，创建映射关系

    Args:
        daily_dates: 日期列表（通常是OHLCV数据的日期）
        monthly_dates: 月末日期列表（通常是特征数据的日期）
        cache_key: 缓存键，如果提供则使用缓存

    Returns:
        Dict: 日期到最近月末日期的映射字典
    """
    # 检查缓存
    if cache_key and cache_key in _DATE_MAPPING_CACHE:
        return _DATE_MAPPING_CACHE[cache_key]

    # 转换为pandas Timestamp
    daily_dates = [pd.Timestamp(d) if not isinstance(d, pd.Timestamp) else d for d in daily_dates]
    monthly_dates = [pd.Timestamp(d) if not isinstance(d, pd.Timestamp) else d for d in monthly_dates]

    # 排序月末日期
    monthly_dates = sorted(monthly_dates)

    # 创建映射
    mapping = {}
    for date in daily_dates:
        # 找到小于等于当前日期的最大月末日期
        valid_months = [m for m in monthly_dates if m <= date]
        if valid_months:
            closest_month_end = valid_months[-1]
        else:
            # 如果没有找到，使用第一个月末日期
            closest_month_end = monthly_dates[0] if monthly_dates else None

        # 存储映射
        date_str = date.strftime('%Y-%m-%d')
        mapping[date_str] = closest_month_end

    # 缓存结果
    if cache_key:
        _DATE_MAPPING_CACHE[cache_key] = mapping

    return mapping

def find_closest_date(target_date: Union[str, pd.Timestamp],
                     available_dates: List[Union[str, pd.Timestamp]]) -> Optional[pd.Timestamp]:
    """
    在可用日期列表中找到最接近目标日期的日期

    Args:
        target_date: 目标日期
        available_dates: 可用日期列表

    Returns:
        pd.Timestamp: 最接近的日期，如果没有找到则返回None
    """
    if not available_dates:
        return None

    # 转换为pandas Timestamp
    if not isinstance(target_date, pd.Timestamp):
        target_date = pd.Timestamp(target_date)

    available_dates = [pd.Timestamp(d) if not isinstance(d, pd.Timestamp) else d for d in available_dates]

    # 计算日期差异
    date_diffs = [(abs((d - target_date).days), d) for d in available_dates]

    # 找到差异最小的日期
    closest_date = min(date_diffs, key=lambda x: x[0])[1]

    return closest_date

def preprocess_dates(df: pd.DataFrame, date_column: str = 'date') -> pd.DataFrame:
    """
    预处理DataFrame中的日期列，确保为pandas Timestamp类型

    Args:
        df: 输入DataFrame
        date_column: 日期列名

    Returns:
        pd.DataFrame: 处理后的DataFrame
    """
    if date_column in df.columns:
        df[date_column] = pd.to_datetime(df[date_column])
    return df

def clear_date_cache():
    """
    清除日期映射缓存
    """
    global _DATE_MAPPING_CACHE
    _DATE_MAPPING_CACHE = {}


@functools.lru_cache(maxsize=240)
def get_min_cal(shift: int = 0, region: str = REG_CN) -> List[time]:
    """
    获取日内分钟级别日历

    Args:
        shift: 偏移量，方向类似于pandas的shift
        region: 区域，例如 "cn", "us"

    Returns:
        时间列表
    """
    cal = []

    if region == REG_CN:
        for ts in list(
            pd.date_range(CN_TIME[0], CN_TIME[1] - timedelta(minutes=1), freq="1min") - pd.Timedelta(minutes=shift)
        ) + list(
            pd.date_range(CN_TIME[2], CN_TIME[3] - timedelta(minutes=1), freq="1min") - pd.Timedelta(minutes=shift)
        ):
            cal.append(ts.time())
    elif region == REG_TW:
        for ts in list(
            pd.date_range(TW_TIME[0], TW_TIME[1] - timedelta(minutes=1), freq="1min") - pd.Timedelta(minutes=shift)
        ):
            cal.append(ts.time())
    elif region == REG_US:
        for ts in list(
            pd.date_range(US_TIME[0], US_TIME[1] - timedelta(minutes=1), freq="1min") - pd.Timedelta(minutes=shift)
        ):
            cal.append(ts.time())
    else:
        raise ValueError(f"{region} 不支持")
    return cal


def is_single_value(start_time, end_time, freq, region: str = REG_CN):
    """
    判断是否只有一个数据点

    Args:
        start_time: 闭区间开始时间
        end_time: 闭区间结束时间
        freq: 频率
        region: 区域，例如 "cn", "us"

    Returns:
        是否只有一个数据点
    """
    if region == REG_CN:
        if end_time - start_time < freq:
            return True
        if start_time.hour == 11 and start_time.minute == 29 and start_time.second == 0:
            return True
        if start_time.hour == 14 and start_time.minute == 59 and start_time.second == 0:
            return True
        return False
    elif region == REG_TW:
        if end_time - start_time < freq:
            return True
        if start_time.hour == 13 and start_time.minute >= 25 and start_time.second == 0:
            return True
        return False
    elif region == REG_US:
        if end_time - start_time < freq:
            return True
        if start_time.hour == 15 and start_time.minute == 59 and start_time.second == 0:
            return True
        return False
    else:
        raise NotImplementedError(f"请为 {region} 实现 is_single_value 函数")


class Freq:
    """
    频率类
    """
    NORM_FREQ_MONTH = "month"
    NORM_FREQ_WEEK = "week"
    NORM_FREQ_DAY = "day"
    NORM_FREQ_MINUTE = "min"  # 使用min而不是minute，以与数据文件名对齐
    SUPPORT_CAL_LIST = [NORM_FREQ_MINUTE, NORM_FREQ_DAY]

    def __init__(self, freq: Union[str, "Freq"]) -> None:
        """
        初始化频率对象

        Args:
            freq: 频率字符串或Freq对象
        """
        if isinstance(freq, str):
            self.count, self.base = self.parse(freq)
        elif isinstance(freq, Freq):
            self.count, self.base = freq.count, freq.base
        else:
            raise NotImplementedError(f"不支持此类型的输入")

    def __eq__(self, freq):
        """
        比较两个频率是否相等

        Args:
            freq: 要比较的频率

        Returns:
            是否相等
        """
        freq = Freq(freq)
        return freq.count == self.count and freq.base == self.base

    def __str__(self):
        """
        返回频率的字符串表示

        Returns:
            频率字符串
        """
        # 尝试与Qlib的文件名对齐: day, 30min, 5min, 1min...
        return f"{self.count if self.count != 1 or self.base != 'day' else ''}{self.base}"

    def __repr__(self) -> str:
        """
        返回频率的表示形式

        Returns:
            表示形式
        """
        return f"{self.__class__.__name__}({str(self)})"

    @staticmethod
    def parse(freq: str) -> Tuple[int, str]:
        """
        解析频率为统一格式

        Args:
            freq: 原始频率，支持的频率应匹配正则表达式 '^([0-9]*)(month|mon|week|w|day|d|minute|min)$'

        Returns:
            统一格式的频率，包括频率计数和统一的频率单位

        示例:
            >>> print(Freq.parse("day"))
            (1, "day" )
            >>> print(Freq.parse("2mon"))
            (2, "month")
            >>> print(Freq.parse("10w"))
            (10, "week")
        """
        freq = freq.lower()
        match_obj = re.match("^([0-9]*)(month|mon|week|w|day|d|minute|min)$", freq)
        if match_obj is None:
            raise ValueError(
                "不支持的频率格式，频率应该类似于 (n)month/mon, (n)week/w, (n)day/d, (n)minute/min"
            )
        _count = int(match_obj.group(1)) if match_obj.group(1) else 1
        _freq = match_obj.group(2)
        _freq_format_dict = {
            "month": Freq.NORM_FREQ_MONTH,
            "mon": Freq.NORM_FREQ_MONTH,
            "week": Freq.NORM_FREQ_WEEK,
            "w": Freq.NORM_FREQ_WEEK,
            "day": Freq.NORM_FREQ_DAY,
            "d": Freq.NORM_FREQ_DAY,
            "minute": Freq.NORM_FREQ_MINUTE,
            "min": Freq.NORM_FREQ_MINUTE,
        }
        return _count, _freq_format_dict[_freq]

    @staticmethod
    def get_timedelta(n: int, freq: str) -> pd.Timedelta:
        """
        获取pd.Timedelta对象

        Args:
            n: 数量
            freq: 频率

        Returns:
            时间差对象
        """
        return pd.Timedelta(f"{n}{freq}")

    @staticmethod
    def get_min_delta(left_frq: str, right_freq: str):
        """
        计算频率差异

        Args:
            left_frq: 左侧频率
            right_freq: 右侧频率

        Returns:
            分钟差异
        """
        minutes_map = {
            Freq.NORM_FREQ_MINUTE: 1,
            Freq.NORM_FREQ_DAY: 60 * 24,
            Freq.NORM_FREQ_WEEK: 7 * 60 * 24,
            Freq.NORM_FREQ_MONTH: 30 * 7 * 60 * 24,
        }
        left_freq = Freq(left_frq)
        left_minutes = left_freq.count * minutes_map[left_freq.base]
        right_freq = Freq(right_freq)
        right_minutes = right_freq.count * minutes_map[right_freq.base]
        return left_minutes - right_minutes

    @staticmethod
    def get_recent_freq(base_freq: Union[str, "Freq"], freq_list: List[Union[str, "Freq"]]) -> Optional["Freq"]:
        """
        从freq_list中获取最接近base_freq的频率

        Args:
            base_freq: 基准频率
            freq_list: 频率列表

        Returns:
            如果找到最近的频率，则返回Freq对象，否则返回None
        """
        base_freq = Freq(base_freq)
        # 使用最接近且大于0的频率
        min_freq = None
        for _freq in freq_list:
            _min_delta = Freq.get_min_delta(base_freq, _freq)
            if _min_delta < 0:
                continue
            if min_freq is None:
                min_freq = (_min_delta, str(_freq))
                continue
            min_freq = min_freq if min_freq[0] <= _min_delta else (_min_delta, _freq)
        return min_freq[1] if min_freq else None


def time_to_day_index(time_obj: Union[str, datetime], region: str = REG_CN):
    """
    将时间转换为日内索引

    Args:
        time_obj: 时间对象或字符串
        region: 区域

    Returns:
        日内索引
    """
    if isinstance(time_obj, str):
        time_obj = datetime.strptime(time_obj, "%H:%M")

    if region == REG_CN:
        if CN_TIME[0] <= time_obj < CN_TIME[1]:
            return int((time_obj - CN_TIME[0]).total_seconds() / 60)
        elif CN_TIME[2] <= time_obj < CN_TIME[3]:
            return int((time_obj - CN_TIME[2]).total_seconds() / 60) + 120
        else:
            raise ValueError(f"{time_obj} 不是 {region} 股票市场的开市时间")
    elif region == REG_US:
        if US_TIME[0] <= time_obj < US_TIME[1]:
            return int((time_obj - US_TIME[0]).total_seconds() / 60)
        else:
            raise ValueError(f"{time_obj} 不是 {region} 股票市场的开市时间")
    elif region == REG_TW:
        if TW_TIME[0] <= time_obj < TW_TIME[1]:
            return int((time_obj - TW_TIME[0]).total_seconds() / 60)
        else:
            raise ValueError(f"{time_obj} 不是 {region} 股票市场的开市时间")
    else:
        raise ValueError(f"{region} 不支持")


def get_day_min_idx_range(start: str, end: str, freq: str, region: str) -> Tuple[int, int]:
    """
    获取给定时间范围内的分钟索引范围

    Args:
        start: 开始时间，例如 "9:30"
        end: 结束时间，例如 "14:30"
        freq: 频率，例如 "1min"
        region: 区域

    Returns:
        开始和结束索引的元组，左右都是闭区间
    """
    start = pd.Timestamp(start).time()
    end = pd.Timestamp(end).time()
    freq = Freq(freq)
    in_day_cal = get_min_cal(region=region)[:: freq.count]
    left_idx = bisect.bisect_left(in_day_cal, start)
    right_idx = bisect.bisect_right(in_day_cal, end) - 1
    return left_idx, right_idx


def concat_date_time(date_obj: date, time_obj: time) -> pd.Timestamp:
    """
    连接日期和时间

    Args:
        date_obj: 日期对象
        time_obj: 时间对象

    Returns:
        连接后的时间戳
    """
    return pd.Timestamp(
        datetime(
            date_obj.year,
            month=date_obj.month,
            day=date_obj.day,
            hour=time_obj.hour,
            minute=time_obj.minute,
            second=time_obj.second,
            microsecond=time_obj.microsecond,
        )
    )


def cal_sam_minute(x: pd.Timestamp, sam_minutes: int, region: str = REG_CN) -> pd.Timestamp:
    """
    将分钟级数据对齐到降采样日历

    例如，在5分钟级别将10:38对齐到10:35（在10分钟级别对齐到10:30）

    Args:
        x: 要对齐的日期时间
        sam_minutes: 对齐到的分钟级别
        region: 区域，例如 "cn", "us"

    Returns:
        对齐后的日期时间
    """
    cal = get_min_cal(0, region)[::sam_minutes]
    idx = bisect.bisect_right(cal, x.time()) - 1
    _date, new_time = x.date(), cal[idx]
    return concat_date_time(_date, new_time)


def epsilon_change(date_time: pd.Timestamp, direction: str = "backward") -> pd.Timestamp:
    """
    通过无穷小量改变时间

    Args:
        date_time: 原始时间
        direction: 时间变化的方向
            - "backward" 表示向过去
            - "forward" 表示向未来

    Returns:
        移动后的时间
    """
    if direction == "backward":
        return date_time - pd.Timedelta(seconds=1)
    elif direction == "forward":
        return date_time + pd.Timedelta(seconds=1)
    else:
        raise ValueError("输入错误")


def transform_end_date(end_date=None, freq="day"):
    """
    处理各种格式的结束日期

    如果end_date为-1、None或大于最大交易日，则返回最后一个交易日。
    否则，返回end_date

    Args:
        end_date: 结束交易日期
        freq: 频率

    Returns:
        处理后的结束日期
    """
    # 这里需要导入数据模块，但为避免循环导入，我们使用一个简单的实现
    from gbs.data_system.base.calendar import get_calendar

    last_date = get_calendar(freq=freq)[-1]
    if end_date is None or (str(end_date) == "-1") or (pd.Timestamp(last_date) < pd.Timestamp(end_date)):
        print(
            f"\n信息: 配置文件中的end_date是 {end_date}, "
            f"因此使用默认的最后日期 {last_date}。"
        )
        end_date = last_date
    return end_date


def time_to_slc_point(t: Union[None, str, pd.Timestamp]) -> Union[None, pd.Timestamp]:
    """
    将时间转换为切片点

    在Qlib或Pandas中，时间切片是一个常用操作。
    然而，用户经常输入各种数据格式来表示时间。
    此函数将帮助用户将这些输入转换为对时间切片友好的统一格式。

    Args:
        t: 原始时间

    Returns:
        转换后的时间
    """
    if t is None:
        # None在Qlib或Pandas中表示无界（例如df.loc[slice(None, "20210303")]）
        return t
    else:
        return pd.Timestamp(t)
