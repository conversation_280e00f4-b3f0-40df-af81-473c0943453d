#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
对象管理工具模块
- 提供对象存储和管理功能
- 包括对象的保存、加载和删除
"""

import os
import pickle
import tempfile
from pathlib import Path
from typing import List, Any, Optional

from gbs.core.utils.paths import ROOT


class ObjManager:
    """
    对象管理器基类
    """

    def save_obj(self, obj: Any, name: str):
        """
        保存对象
        
        Args:
            obj: 要保存的对象
            name: 对象名称
        """
        raise NotImplementedError("请实现 `save_obj` 方法")

    def save_objs(self, obj_name_l: List[tuple]):
        """
        保存多个对象
        
        Args:
            obj_name_l: (对象, 名称) 元组列表
        """
        for obj, name in obj_name_l:
            self.save_obj(obj, name)

    def load_obj(self, name: str) -> Any:
        """
        加载对象
        
        Args:
            name: 对象名称
            
        Returns:
            加载的对象
        """
        raise NotImplementedError("请实现 `load_obj` 方法")

    def exists(self, name: str) -> bool:
        """
        检查对象是否存在
        
        Args:
            name: 对象名称
            
        Returns:
            对象是否存在
        """
        raise NotImplementedError("请实现 `exists` 方法")

    def list(self) -> List[str]:
        """
        列出所有对象
        
        Returns:
            对象列表
        """
        raise NotImplementedError("请实现 `list` 方法")

    def remove(self, fname: Optional[str] = None):
        """
        删除对象
        
        Args:
            fname: 如果提供了文件名，则删除特定文件，否则删除所有对象
        """
        raise NotImplementedError("请实现 `remove` 方法")


class FileManager(ObjManager):
    """
    使用文件系统管理对象
    """

    def __init__(self, path=None):
        """
        初始化文件管理器
        
        Args:
            path: 文件路径，如果为None则创建临时路径
        """
        if path is None:
            self.path = Path(self.create_path())
        else:
            self.path = Path(path).resolve()

    def create_path(self) -> str:
        """
        创建路径
        
        Returns:
            创建的路径
        """
        try:
            temp_dir = ROOT / "temp"
            os.makedirs(temp_dir, exist_ok=True)
            return tempfile.mkdtemp(prefix=str(temp_dir) + os.sep)
        except AttributeError as attribute_e:
            raise NotImplementedError(
                "如果未提供path，则应实现 `create_path` 函数"
            ) from attribute_e

    def save_obj(self, obj, name):
        """
        保存对象
        
        Args:
            obj: 要保存的对象
            name: 对象名称
        """
        with (self.path / name).open("wb") as f:
            pickle.dump(obj, f, protocol=4)  # 使用协议版本4

    def load_obj(self, name):
        """
        加载对象
        
        Args:
            name: 对象名称
            
        Returns:
            加载的对象
        """
        with (self.path / name).open("rb") as f:
            return pickle.load(f)

    def exists(self, name):
        """
        检查对象是否存在
        
        Args:
            name: 对象名称
            
        Returns:
            对象是否存在
        """
        return (self.path / name).exists()

    def list(self):
        """
        列出所有对象
        
        Returns:
            对象列表
        """
        return list(self.path.iterdir())

    def remove(self, fname=None):
        """
        删除对象
        
        Args:
            fname: 如果提供了文件名，则删除特定文件，否则删除所有对象
        """
        if fname is None:
            for fp in self.path.glob("*"):
                fp.unlink()
            self.path.rmdir()
        else:
            (self.path / fname).unlink()
