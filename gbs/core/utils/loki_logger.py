#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Loki 日志模块
- 提供结构化日志记录功能
- 支持系统输入/输出记录
- 集成 Loki 日志系统
"""

import json
import logging
import sys
import time
import inspect
import functools
import traceback
import contextlib
from pathlib import Path
from typing import Optional, Dict, Any, Callable, List, Union, TypeVar, cast
from datetime import datetime
from functools import wraps

from .paths import LOG_DIR

# 确保日志目录存在
LOG_DIR.mkdir(parents=True, exist_ok=True)

# 定义系统类型
SYSTEM_TYPES = ["data_system", "model_system", "backtest_system", "eval_system"]

# 日志格式
DEFAULT_LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
SIMPLE_LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"

# 类型变量定义
F = TypeVar('F', bound=Callable[..., Any])

class LokiLogger:
    """Loki 日志记录器"""

    def __init__(
        self,
        name: str,
        system: str = None,
        level: int = logging.INFO,
        log_file: Optional[str] = None,
        log_format: str = DEFAULT_LOG_FORMAT
    ):
        """
        初始化 Loki 日志记录器

        Args:
            name: 日志记录器名称
            system: 系统名称 (data_system, model_system, backtest_system, eval_system)
            level: 日志级别
            log_file: 日志文件路径，如果为None则只输出到控制台
            log_format: 日志格式
        """
        self.name = name
        self.system = system

        # 验证系统类型
        if system and system not in SYSTEM_TYPES:
            raise ValueError(f"系统类型必须是以下之一: {', '.join(SYSTEM_TYPES)}")

        # 创建基础日志记录器
        self.logger = self._create_logger(name, level, log_file, log_format)

        # 如果指定了系统，创建系统特定的日志文件
        if system:
            system_log_file = f"{system}.log"
            system_log_path = LOG_DIR / system_log_file

            # 创建文件处理器
            file_handler = logging.FileHandler(system_log_path, encoding='utf-8')
            formatter = logging.Formatter(log_format)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)

    def _create_logger(
        self,
        name: str,
        level: int = logging.INFO,
        log_file: Optional[str] = None,
        log_format: str = SIMPLE_LOG_FORMAT
    ) -> logging.Logger:
        """
        创建日志记录器

        Args:
            name: 日志记录器名称
            level: 日志级别
            log_file: 日志文件路径，如果为None则只输出到控制台
            log_format: 日志格式

        Returns:
            logging.Logger: 日志记录器
        """
        # 创建日志记录器
        logger = logging.getLogger(name)

        # 如果已经有处理器，则直接返回
        if logger.handlers:
            return logger

        # 设置日志级别
        logger.setLevel(level)

        # 创建格式化器
        formatter = logging.Formatter(log_format)

        # 创建控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        # 如果指定了日志文件，则创建文件处理器
        if log_file:
            # 如果是相对路径，则相对于LOG_DIR
            log_path = Path(log_file)
            if not log_path.is_absolute():
                log_path = LOG_DIR / log_path

            # 确保日志文件所在目录存在
            log_path.parent.mkdir(parents=True, exist_ok=True)

            # 创建文件处理器
            file_handler = logging.FileHandler(log_path, encoding='utf-8')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        return logger

    def _format_structured_log(self, message: str, metadata: Dict[str, Any] = None) -> str:
        """
        格式化结构化日志消息

        Args:
            message: 日志消息
            metadata: 元数据字典

        Returns:
            str: 格式化后的日志消息
        """
        if not metadata:
            return message

        # 添加系统信息
        if self.system and "system" not in metadata:
            metadata["system"] = self.system

        # 添加时间戳
        if "timestamp" not in metadata:
            metadata["timestamp"] = datetime.now().isoformat()

        # 格式化为 JSON
        try:
            json_metadata = json.dumps(metadata, ensure_ascii=False)
            return f"{message} | {json_metadata}"
        except Exception:
            return f"{message} | {metadata}"

    def debug(self, message: str, metadata: Dict[str, Any] = None):
        """记录调试级别日志"""
        self.logger.debug(self._format_structured_log(message, metadata))

    def info(self, message: str, metadata: Dict[str, Any] = None):
        """记录信息级别日志"""
        self.logger.info(self._format_structured_log(message, metadata))

    def warning(self, message: str, metadata: Dict[str, Any] = None):
        """记录警告级别日志"""
        self.logger.warning(self._format_structured_log(message, metadata))

    def error(self, message: str, metadata: Dict[str, Any] = None):
        """记录错误级别日志"""
        self.logger.error(self._format_structured_log(message, metadata))

    def critical(self, message: str, metadata: Dict[str, Any] = None):
        """记录严重错误级别日志"""
        self.logger.critical(self._format_structured_log(message, metadata))

    def log_input(self, func_name: str, args: List[Any] = None, kwargs: Dict[str, Any] = None):
        """
        记录函数输入

        Args:
            func_name: 函数名称
            args: 位置参数
            kwargs: 关键字参数
        """
        metadata = {
            "type": "INPUT",
            "function": func_name,
        }

        # 添加参数信息（简化版本，避免过大的日志）
        if args:
            metadata["args_count"] = len(args)
            metadata["args_types"] = [type(arg).__name__ for arg in args]

        if kwargs:
            metadata["kwargs"] = list(kwargs.keys())

        self.info(f"INPUT: {func_name}", metadata)

    def log_output(self, func_name: str, result: Any, execution_time: float = None):
        """
        记录函数输出

        Args:
            func_name: 函数名称
            result: 函数返回值
            execution_time: 执行时间（秒）
        """
        metadata = {
            "type": "OUTPUT",
            "function": func_name,
        }

        # 添加结果信息（简化版本，避免过大的日志）
        if result is not None:
            metadata["result_type"] = type(result).__name__

            # 对于常见的数据类型，记录一些基本信息
            if isinstance(result, (list, tuple)):
                metadata["result_length"] = len(result)
            elif isinstance(result, dict):
                metadata["result_keys"] = list(result.keys())
            elif hasattr(result, 'shape'):  # 对于 numpy 数组或 pandas DataFrame
                metadata["result_shape"] = str(result.shape)

        if execution_time is not None:
            metadata["execution_time"] = execution_time

        self.info(f"OUTPUT: {func_name}", metadata)

    def log_exception(self, func_name: str, exception: Exception):
        """
        记录异常信息

        Args:
            func_name: 函数名称
            exception: 异常对象
        """
        metadata = {
            "type": "EXCEPTION",
            "function": func_name,
            "exception_type": type(exception).__name__,
            "exception_message": str(exception),
            "traceback": traceback.format_exc()
        }

        self.error(f"EXCEPTION: {func_name} - {type(exception).__name__}: {str(exception)}", metadata)


def get_loki_logger(
    name: str,
    system: str = None,
    level: int = logging.INFO,
    log_file: Optional[str] = None
) -> LokiLogger:
    """
    获取 Loki 日志记录器

    Args:
        name: 日志记录器名称
        system: 系统名称 (data_system, model_system, backtest_system, eval_system)
        level: 日志级别
        log_file: 日志文件路径，如果为None则只输出到控制台

    Returns:
        LokiLogger: Loki 日志记录器
    """
    return LokiLogger(name, system, level, log_file)


def log_io(logger: LokiLogger = None, log_args: bool = True, log_result: bool = True) -> Callable[[F], F]:
    """
    记录函数输入和输出的装饰器

    Args:
        logger: Loki 日志记录器，如果为 None，则使用函数模块名创建
        log_args: 是否记录参数
        log_result: 是否记录结果

    Returns:
        装饰后的函数
    """
    def decorator(func: F) -> F:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 如果没有提供日志记录器，则创建一个
            nonlocal logger
            if logger is None:
                module_name = func.__module__
                system = None

                # 尝试从模块名确定系统类型
                for sys_type in SYSTEM_TYPES:
                    if sys_type in module_name:
                        system = sys_type
                        break

                logger = get_loki_logger(module_name, system)

            # 记录输入
            if log_args:
                logger.log_input(func.__name__, args, kwargs)

            # 执行函数并记录时间
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time

                # 记录输出
                if log_result:
                    logger.log_output(func.__name__, result, execution_time)

                return result
            except Exception as e:
                # 记录异常
                logger.log_exception(func.__name__, e)
                raise

        return cast(F, wrapper)

    return decorator




class TimeInspector:
    """
    Time inspector for measuring code execution time.
    Similar to qlib's TimeInspector.
    """
    timer_logger = get_loki_logger("timer").logger
    time_marks = []

    @classmethod
    def set_time_mark(cls):
        """
        Set a time mark with current time, and this time mark will push into a stack.

        Returns
        -------
        float
            A timestamp for current time.
        """
        _time = time.time()
        cls.time_marks.append(_time)
        return _time

    @classmethod
    def pop_time_mark(cls):
        """
        Pop last time mark from stack.

        Returns
        -------
        float
            The timestamp that was popped.
        """
        return cls.time_marks.pop()

    @classmethod
    def get_cost_time(cls):
        """
        Get last time mark from stack, calculate time diff with current time.

        Returns
        -------
        float
            Time diff calculated by last time mark with current time.
        """
        cost_time = time.time() - cls.time_marks.pop()
        return cost_time

    @classmethod
    def log_cost_time(cls, info="Done"):
        """
        Get last time mark from stack, calculate time diff with current time, and log time diff and info.

        Parameters
        ----------
        info : str
            Info that will be logged into stdout.
        """
        cost_time = time.time() - cls.time_marks.pop()
        cls.timer_logger.info("Time cost: {0:.3f}s | {1}".format(cost_time, info))

    @classmethod
    @contextlib.contextmanager
    def logt(cls, name="", show_start=False):
        """
        Log the time of the inside code.

        Parameters
        ----------
        name : str
            Name of the code block.
        show_start : bool
            Whether to show start message.
        """
        if show_start:
            cls.timer_logger.info(f"{name} Begin")
        cls.set_time_mark()
        try:
            yield None
        finally:
            pass
        cls.log_cost_time(info=f"{name} Done")

    # 保留原有的timer装饰器，以便向后兼容
    @classmethod
    def timer(cls, func):
        """
        Decorator for measuring execution time of a function.

        Parameters
        ----------
        func : callable
            The function to measure.

        Returns
        -------
        callable
            The wrapped function.
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            cls.set_time_mark()
            result = func(*args, **kwargs)
            cls.log_cost_time(info=f"{func.__name__} Done")
            return result
        return wrapper
