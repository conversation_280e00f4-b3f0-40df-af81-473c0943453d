#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重采样工具模块
- 提供数据重采样功能
- 包括时间序列数据的重采样
"""

import numpy as np
import pandas as pd
from functools import partial
from typing import Union, Callable

from gbs.core.utils.data import lazy_sort_index
from gbs.core.utils.date_utils import Freq, cal_sam_minute


def resam_calendar(calendar: Union[list, pd.DatetimeIndex], freq: str) -> pd.DatetimeIndex:
    """
    重采样日历

    Args:
        calendar: 原始日历
        freq: 频率

    Returns:
        重采样后的日历
    """
    if isinstance(calendar, list):
        calendar = pd.DatetimeIndex(calendar)

    freq_obj = Freq(freq)
    if freq_obj.base == Freq.NORM_FREQ_DAY:
        return calendar[::freq_obj.count]

    # 对于分钟级别的数据，需要特殊处理
    if freq_obj.base == Freq.NORM_FREQ_MINUTE:
        # 创建一个新的日历
        new_calendar = []
        for date in calendar:
            new_calendar.append(cal_sam_minute(date, freq_obj.count))
        return pd.DatetimeIndex(new_calendar)

    raise ValueError(f"不支持的频率: {freq}")


def pd_resam(df: pd.DataFrame, freq: str, resam_func: Callable = None) -> pd.DataFrame:
    """
    使用pandas重采样数据

    Args:
        df: 输入数据
        freq: 频率
        resam_func: 重采样函数

    Returns:
        重采样后的数据
    """
    if resam_func is None:
        resam_func = partial(pd.DataFrame.resample, rule=freq)

    # 确保索引已排序
    df = lazy_sort_index(df)

    # 处理多级索引
    if isinstance(df.index, pd.MultiIndex):
        # 获取第一级索引的唯一值
        idx_level_0 = df.index.get_level_values(0).unique()

        # 对每个唯一值进行重采样
        dfs = []
        for idx in idx_level_0:
            sub_df = df.loc[idx]
            sub_df = resam_func(sub_df).mean()
            sub_df.index = pd.MultiIndex.from_product([[idx], sub_df.index])
            dfs.append(sub_df)

        # 合并结果
        df = pd.concat(dfs)
    else:
        df = resam_func(df).mean()

    return df
