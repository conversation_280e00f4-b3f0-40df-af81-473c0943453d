#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模块和初始化工具
- 提供模块和类的操作功能
- 包括导入模块、查找类等
- 提供对象初始化功能
"""

import contextlib
import importlib
import inspect
import os
import pkgutil
import re
import sys
import warnings
from types import ModuleType
from typing import Any, Dict, List, Tuple, Union, Type, Optional

from gbs.core.utils.loki_logger import get_loki_logger

# 获取日志记录器
log = get_loki_logger(__name__).logger


def get_module_by_module_path(module_path: str) -> ModuleType:
    """
    通过模块路径获取模块

    Args:
        module_path: 模块路径

    Returns:
        导入的模块
    """
    module = importlib.import_module(module_path)
    return module


def split_module_path(module_path: str) -> Tuple[str, str]:
    """
    分割模块路径为模块和属性

    Args:
        module_path: 模块路径，例如 "gbs.core.utils.mod.split_module_path"

    Returns:
        模块路径和属性名的元组
    """
    *module_path_list, attr = module_path.split(".")
    return ".".join(module_path_list), attr


def get_callable_kwargs(func):
    """
    获取可调用对象的参数

    Args:
        func: 可调用对象

    Returns:
        参数字典
    """
    signature = inspect.signature(func)
    return {
        k: v.default
        for k, v in signature.parameters.items()
        if v.default is not inspect.Parameter.empty
    }


def get_cls_kwargs(cls, exclude_init=True):
    """
    获取类的参数

    Args:
        cls: 类对象
        exclude_init: 是否排除__init__方法的参数

    Returns:
        参数字典
    """
    if exclude_init:
        return {}

    # 获取__init__方法的参数
    init_func = cls.__init__
    return get_callable_kwargs(init_func)


@contextlib.contextmanager
def class_casting(obj: Any, cls: Type):
    """
    将对象临时转换为指定类型的上下文管理器

    Args:
        obj: 要转换的对象
        cls: 目标类型

    Yields:
        转换后的对象
    """
    if obj.__class__ == cls:
        yield obj
        return

    # 保存原始类
    original_class = obj.__class__

    # 修改对象的类
    obj.__class__ = cls

    try:
        # 在上下文中使用修改后的对象
        yield obj
    finally:
        # 恢复原始类
        obj.__class__ = original_class


def find_all_classes(module_path: Union[str, ModuleType], cls: type) -> List[type]:
    """
    递归查找给定模块中继承自指定类的所有类
    - 包括cls本身

    示例:
    >>> from gbs.data_system.dataset.handler import DataHandler
    >>> find_all_classes("gbs.data_system.dataset.handler", DataHandler)

    Args:
        module_path: 模块路径或模块对象
        cls: 基类

    Returns:
        类列表
    """
    if isinstance(module_path, ModuleType):
        mod = module_path
    else:
        mod = importlib.import_module(module_path)

    cls_list = []

    def _append_cls(obj):
        # 利用闭包技巧重用代码
        if isinstance(obj, type) and issubclass(obj, cls) and obj not in cls_list:
            cls_list.append(obj)

    for attr in dir(mod):
        _append_cls(getattr(mod, attr))

    if hasattr(mod, "__path__"):
        # 如果模块是一个包
        for _, modname, _ in pkgutil.iter_modules(mod.__path__):
            try:
                sub_mod = importlib.import_module(f"{mod.__package__}.{modname}")
                for m_cls in find_all_classes(sub_mod, cls):
                    _append_cls(m_cls)
            except ImportError as e:
                log.warning(f"导入模块 {mod.__package__}.{modname} 时出错: {e}")
                continue

    return cls_list


def init_instance_by_config(
    config: Union[Dict[str, Any], Any],
    accept_types: Optional[Union[Type, List[Type], Tuple[Type, ...]]] = None,
    default_module: Optional[str] = None,
    **kwargs
) -> Any:
    """
    从配置初始化实例

    Args:
        config: 配置字典或实例
        accept_types: 接受的类型
        default_module: 未在配置中指定时的默认模块路径
        **kwargs: 用于覆盖config['kwargs']的额外参数

    Returns:
        初始化的实例
    """
    # 如果config已经是一个实例，直接返回
    if not isinstance(config, dict):
        if accept_types is not None:
            if not isinstance(accept_types, (list, tuple)):
                accept_types = [accept_types]
            if not any(isinstance(config, t) for t in accept_types):
                raise TypeError(f"Expected {accept_types}, got {type(config)}")
        return config

    # 获取类名和模块路径
    class_name = config.get("class")
    module_path = config.get("module_path")

    if class_name is None:
        raise ValueError("Config must contain 'class' key")

    # 如果未指定module_path，使用default_module
    if module_path is None and default_module is not None:
        module_path = default_module
        log.debug(f"Using default module path: {module_path}")

    # 导入模块并获取类
    if module_path is not None:
        try:
            module = importlib.import_module(module_path)
            cls = getattr(module, class_name)
        except (ImportError, AttributeError) as e:
            log.error(f"Failed to import {class_name} from {module_path}: {e}")
            raise
    else:
        # 尝试在当前模块中查找类
        frame = inspect.currentframe()
        try:
            # 获取调用者的globals
            caller_globals = frame.f_back.f_globals
            cls = caller_globals.get(class_name)
            if cls is None:
                raise ValueError(f"Class {class_name} not found in the current module")
        finally:
            del frame

    # 检查类是否为预期类型
    if accept_types is not None:
        if not isinstance(accept_types, (list, tuple)):
            accept_types = [accept_types]
        if not any(issubclass(cls, t) for t in accept_types):
            raise TypeError(f"Expected {accept_types}, got {cls}")

    # 获取构造函数参数
    init_kwargs = config.get("kwargs", {})
    if init_kwargs is not None:
        init_kwargs = init_kwargs.copy()  # 创建副本以避免修改原始对象
    else:
        init_kwargs = {}
    init_kwargs.update(kwargs)

    # 初始化实例
    try:
        instance = cls(**init_kwargs)
        log.debug(f"Successfully initialized {class_name} from {module_path}")
    except Exception as e:
        log.error(f"Failed to initialize {class_name} with kwargs {init_kwargs}: {e}")
        raise

    return instance


def auto_filter_kwargs(func: callable, warning=True) -> callable:
    """
    自动过滤关键字参数

    装饰后的函数将忽略不可接受的参数并给出警告

    例如，如果你有一个函数`f`可能会选择性地使用关键字参数`bar`，
    那么你可以通过`auto_filter_kwargs(f)(bar=3)`调用它，当f不需要bar时会自动过滤掉

    Args:
        func: 原始函数
        warning: 是否在过滤参数时发出警告

    Returns:
        新的可调用函数
    """
    def _func(*args, **kwargs):
        spec = inspect.getfullargspec(func)
        new_kwargs = {}
        for k, v in kwargs.items():
            # 如果`func`不接受可变关键字参数如`**kwargs`且没有相应的命名参数
            if spec.varkw is None and k not in spec.args:
                if warning:
                    log.warning(f"参数 `{k}` 的值 `{v}` 被忽略。")
            else:
                new_kwargs[k] = v
        return func(*args, **new_kwargs)

    return _func
