#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
异步调用器模块
提供异步执行函数的功能
"""

import threading
import queue
import time
from typing import Callable, Any, Dict, List, Optional, Tuple

from gbs.core.utils.loki_logger import get_loki_logger

logger = get_loki_logger("async_caller").logger

class AsyncCaller:
    """
    异步调用器
    用于异步执行函数，避免阻塞主线程
    """

    def __init__(self, max_workers: int = 5, max_queue_size: int = 100):
        """
        初始化异步调用器

        Args:
            max_workers: 最大工作线程数
            max_queue_size: 最大队列大小
        """
        self.max_workers = max_workers
        self.task_queue = queue.Queue(maxsize=max_queue_size)
        self.workers = []
        self.running = True
        self._start_workers()

    @staticmethod
    def async_dec(ac_attr="async_caller"):
        """
        异步装饰器

        Args:
            ac_attr: 异步调用器属性名

        Returns:
            装饰器函数
        """
        def decorator(func):
            def wrapper(self, *args, **kwargs):
                if hasattr(self, ac_attr) and getattr(self, ac_attr) is not None:
                    async_caller = getattr(self, ac_attr)
                    async_caller.call(func, self, *args, **kwargs)
                else:
                    return func(self, *args, **kwargs)
            return wrapper
        return decorator

    def _start_workers(self):
        """启动工作线程"""
        for _ in range(self.max_workers):
            worker = threading.Thread(target=self._worker_loop)
            worker.daemon = True
            worker.start()
            self.workers.append(worker)

    def _worker_loop(self):
        """工作线程循环"""
        while self.running:
            try:
                # 从队列中获取任务，如果队列为空，等待1秒
                try:
                    func, args, kwargs = self.task_queue.get(timeout=1)
                except queue.Empty:
                    continue

                # 执行任务
                try:
                    func(*args, **kwargs)
                except Exception as e:
                    logger.error(f"执行异步任务时出错: {e}")
                finally:
                    self.task_queue.task_done()
            except Exception as e:
                logger.error(f"工作线程循环出错: {e}")

    def call(self, func: Callable, *args, **kwargs):
        """
        异步调用函数

        Args:
            func: 要调用的函数
            *args: 位置参数
            **kwargs: 关键字参数
        """
        if not self.running:
            logger.warning("AsyncCaller已停止，无法添加新任务")
            return

        try:
            self.task_queue.put((func, args, kwargs), block=False)
        except queue.Full:
            logger.warning("任务队列已满，丢弃任务")

    def wait(self):
        """
        等待所有任务完成
        """
        self.task_queue.join()

    def stop(self, wait: bool = True):
        """
        停止异步调用器

        Args:
            wait: 是否等待所有任务完成
        """
        if wait:
            self.wait()

        self.running = False

        # 等待所有工作线程结束
        for worker in self.workers:
            if worker.is_alive():
                worker.join(timeout=1)

        self.workers = []
