#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自定义异常模块
- 提供系统中使用的自定义异常类
"""

class GBSException(Exception):
    """
    Gold Beast System 基础异常类
    所有自定义异常都应继承自此类
    """
    pass


class ExpAlreadyExistError(GBSException):
    """
    当尝试创建已存在的实验时引发
    """
    pass


class RecorderInitializationError(GBSException):
    """
    当初始化记录器出错时引发
    """
    pass


class LoadObjectError(GBSException):
    """
    当加载对象出错时引发
    """
    pass


class DataNotFoundError(GBSException):
    """
    当请求的数据不存在时引发
    """
    pass


class InvalidConfigError(GBSException):
    """
    当配置无效时引发
    """
    pass
