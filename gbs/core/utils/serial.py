"""
序列化工具模块

提供统一的序列化和反序列化接口，用于保存和加载对象
参考qlib的设计，整合了serial.py, serializable.py和serialization.py的功能
"""

import pickle
import dill
import json
import os
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Union, Any, Type, Dict, Optional, List

from gbs.core.utils.loki_logger import get_loki_logger

# 获取日志记录器
log = get_loki_logger(__name__).logger


class Serializable:
    """
    可序列化基类

    提供统一的序列化和反序列化接口，用于保存和加载对象

    特性:
    - 支持pickle/dill序列化
    - 支持JSON序列化和反序列化
    - 支持CSV序列化和反序列化（对于DataFrame和Series）
    - 支持配置序列化行为
    - 支持字典转换
    """

    pickle_backend = "pickle"  # 另一个可选值是"dill"，可以序列化更多Python对象
    dump_protocol_version = pickle.HIGHEST_PROTOCOL
    default_dump_all = False  # 是否默认保存所有属性（包括以_开头的私有属性）
    config_attr = ["_include", "_exclude"]
    exclude_attr = []  # 排除属性列表，优先级低于self._exclude
    include_attr = []  # 包含属性列表，优先级低于self._include
    FLAG_KEY = "_gbs_serial_flag"

    def __init__(self, **kwargs):
        """初始化可序列化对象"""
        self._dump_all = self.default_dump_all
        self._exclude = None  # 此属性优先级高于exclude_attr
        self._include = None  # 此属性优先级高于include_attr

    def _is_kept(self, key):
        """
        判断属性是否应该被保存

        规则（优先级从高到低）:
        - 在config_attr列表中 -> 总是排除
        - 在include属性列表中 -> 总是包含
        - 在exclude属性列表中 -> 总是排除
        - 不以_开头的名称 -> 包含
        - 以_开头的名称 -> 如果dump_all为true则包含，否则排除

        Args:
            key: 属性名

        Returns:
            bool: 是否保留该属性
        """
        if key in self.config_attr:
            return False
        if key in self._get_attr_list("include"):
            return True
        if key in self._get_attr_list("exclude"):
            return False
        return self.dump_all or not key.startswith("_")

    def _get_attr_list(self, attr_type):
        """
        获取属性列表

        Args:
            attr_type: 属性类型，"include"或"exclude"

        Returns:
            list: 属性列表
        """
        if attr_type == "include":
            obj_attr = getattr(self, "_include", None)
            cls_attr = self.include_attr
        elif attr_type == "exclude":
            obj_attr = getattr(self, "_exclude", None)
            cls_attr = self.exclude_attr
        else:
            raise ValueError(f"未知的属性类型: {attr_type}")

        if obj_attr is None:
            return cls_attr
        else:
            return obj_attr

    def __getstate__(self) -> dict:
        """
        获取对象状态，用于pickle序列化

        Returns:
            dict: 对象状态字典
        """
        return {k: v for k, v in self.__dict__.items() if self._is_kept(k)}

    def __setstate__(self, state: dict):
        """
        设置对象状态，用于pickle反序列化

        Args:
            state: 对象状态字典
        """
        self.__dict__.update(state)

    @property
    def dump_all(self):
        """
        是否保存所有属性

        Returns:
            bool: 是否保存所有属性
        """
        return getattr(self, "_dump_all", False)

    def config(self, recursive=False, **kwargs):
        """
        配置序列化对象

        Args:
            recursive: 是否递归配置
            **kwargs: 配置参数，可能包含以下键:
                dump_all: 是否保存所有属性
                exclude: 排除属性列表
                include: 包含属性列表
        """
        keys = {"dump_all", "exclude", "include"}
        for k, v in kwargs.items():
            if k in keys:
                attr_name = f"_{k}"
                setattr(self, attr_name, v)
            else:
                raise KeyError(f"未知参数: {k}")

        if recursive:
            for obj in self.__dict__.values():
                # 设置标志以防止无限循环
                self.__dict__[self.FLAG_KEY] = True
                if isinstance(obj, Serializable) and self.FLAG_KEY not in obj.__dict__:
                    # 只对Serializable对象调用config方法
                    obj.config(recursive=True, **kwargs)
                elif hasattr(obj, 'config') and callable(obj.config) and not isinstance(obj, dict):
                    # 对有config方法的非字典对象调用config方法
                    try:
                        obj.config(recursive=True, **kwargs)
                    except Exception as e:
                        # 忽略错误，继续处理其他对象
                        log.warning(f"调用对象的config方法时出错: {e}")
                if self.FLAG_KEY in self.__dict__:
                    del self.__dict__[self.FLAG_KEY]

    def to_pickle(self, path: Union[Path, str], **kwargs):
        """
        将对象保存到pickle文件

        Args:
            path: 保存路径
            **kwargs: 配置参数，可能包含以下键:
                dump_all: 是否保存所有属性
                exclude: 排除属性列表
                include: 包含属性列表
        """
        # 检查是否有config方法
        if hasattr(self, 'config') and callable(self.config):
            self.config(**kwargs)
        path = Path(path)

        # 创建目录（如果不存在）
        os.makedirs(path.parent, exist_ok=True)

        with path.open("wb") as f:
            self.get_backend().dump(self, f, protocol=self.dump_protocol_version)

        log.info(f"对象已保存到 {path}")

    def save(self, path: Union[Path, str], **kwargs):
        """
        保存对象到文件

        根据文件扩展名自动选择序列化格式:
        - .json: JSON格式
        - .csv: CSV格式（仅适用于DataFrame和Series）
        - 其他: pickle格式

        Args:
            path: 保存路径
            **kwargs: 额外参数
        """
        path = Path(path)

        # 创建目录（如果不存在）
        os.makedirs(path.parent, exist_ok=True)

        # 根据扩展名确定文件格式
        if path.suffix == ".json":
            self.to_json(path, **kwargs)
        elif path.suffix == ".csv":
            if hasattr(self, "to_csv"):
                self.to_csv(path, **kwargs)
            else:
                raise TypeError(f"对象类型 {type(self)} 不支持CSV序列化")
        else:
            # 默认使用pickle序列化
            self.to_pickle(path, **kwargs)

    @classmethod
    def load(cls, filepath: Union[Path, str], **kwargs) -> 'Serializable':
        """
        从文件加载对象

        根据文件扩展名自动选择反序列化格式:
        - .json: JSON格式
        - .csv: CSV格式
        - 其他: pickle格式

        Args:
            filepath: 文件路径
            **kwargs: 额外参数

        Returns:
            Serializable: 加载的对象

        Raises:
            TypeError: 如果加载的对象不是cls类型
            FileNotFoundError: 如果文件不存在
        """
        filepath = Path(filepath)

        # 检查文件是否存在
        if not filepath.exists():
            raise FileNotFoundError(f"文件不存在: {filepath}")

        # 根据扩展名确定文件格式
        if filepath.suffix == ".json":
            return cls.from_json(path=filepath, **kwargs)
        elif filepath.suffix == ".csv":
            # 处理CSV反序列化
            data = pd.read_csv(filepath, index_col=0, **kwargs)
            if hasattr(cls, "from_csv"):
                return cls.from_csv(data)
            else:
                return data
        else:
            # 默认使用pickle反序列化
            with open(filepath, "rb") as f:
                obj = cls.get_backend().load(f)

            if isinstance(obj, cls):
                log.info(f"对象已从 {filepath} 加载")
                return obj
            else:
                raise TypeError(f"加载的对象类型 {type(obj)} 不是有效的 {cls} 类型!")

    @classmethod
    def get_backend(cls):
        """
        获取序列化后端

        Returns:
            module: pickle或dill模块
        """
        if cls.pickle_backend == "pickle":
            return pickle
        elif cls.pickle_backend == "dill":
            return dill
        else:
            raise ValueError("未知的pickle后端，请使用'pickle'或'dill'")

    def to_dict(self) -> Dict[str, Any]:
        """
        将对象转换为字典

        Returns:
            Dict[str, Any]: 字典表示
        """
        # 获取所有不以'_'开头的属性
        return {k: v for k, v in self.__dict__.items() if not k.startswith("_")}

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Serializable':
        """
        从字典创建对象

        Args:
            data: 字典表示

        Returns:
            Serializable: 创建的对象
        """
        obj = cls()
        for k, v in data.items():
            setattr(obj, k, v)
        return obj

    def to_json(self, path: Optional[Union[str, Path]] = None, **kwargs) -> Optional[str]:
        """
        将对象转换为JSON

        Args:
            path: 保存路径（如果为None，则返回JSON字符串）
            **kwargs: 额外参数

        Returns:
            Optional[str]: JSON字符串（如果path为None）
        """
        # 转换为字典
        data = self.to_dict()

        # 转换为JSON
        if path is None:
            return json.dumps(data, **kwargs)
        else:
            path = Path(path)
            os.makedirs(path.parent, exist_ok=True)
            with open(path, "w") as f:
                json.dump(data, f, indent=4, default=self._json_default, **kwargs)
            log.info(f"对象已保存到 {path}")

    @classmethod
    def from_json(cls, json_str: Optional[str] = None, path: Optional[Union[str, Path]] = None, **kwargs) -> 'Serializable':
        """
        从JSON创建对象

        Args:
            json_str: JSON字符串（如果path为None）
            path: 加载路径（如果json_str为None）
            **kwargs: 额外参数

        Returns:
            Serializable: 创建的对象

        Raises:
            ValueError: 如果json_str和path都为None
        """
        if json_str is None and path is None:
            raise ValueError("必须提供json_str或path参数")

        if path is not None:
            path = Path(path)
            with open(path, "r") as f:
                data = json.load(f, **kwargs)
        else:
            data = json.loads(json_str, **kwargs)

        return cls.from_dict(data)

    @staticmethod
    def _json_default(obj):
        """
        自定义JSON序列化器，用于处理默认json代码无法序列化的对象

        Args:
            obj: 要序列化的对象

        Returns:
            object: JSON可序列化版本的对象

        Raises:
            TypeError: 如果对象无法序列化为JSON
        """
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, pd.Timestamp):
            return obj.isoformat()
        if isinstance(obj, pd.core.arrays.datetimes.DatetimeArray):
            # 处理pandas DatetimeArray
            return [ts.isoformat() if pd.notna(ts) else None for ts in obj]
        if isinstance(obj, pd.DatetimeIndex):
            # 处理pandas DatetimeIndex
            return [ts.isoformat() if pd.notna(ts) else None for ts in obj]
        if isinstance(obj, pd.Series):
            # 处理pandas Series
            if pd.api.types.is_datetime64_any_dtype(obj):
                return [ts.isoformat() if pd.notna(ts) else None for ts in obj]
            else:
                return obj.tolist()
        if hasattr(obj, "to_dict"):
            return obj.to_dict()
        raise TypeError(f"对象类型 {type(obj)} 不可JSON序列化")

    @staticmethod
    def general_dump(obj: Any, path: Union[Path, str]):
        """
        通用对象保存方法

        根据文件扩展名和对象类型自动选择序列化格式

        Args:
            obj: 要保存的对象
            path: 保存路径
        """
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)

        # 确定文件格式
        if path.suffix == ".json":
            # 处理JSON序列化
            if isinstance(obj, (pd.DataFrame, pd.Series)):
                obj_dict = obj.to_dict()
                with open(path, "w") as f:
                    json.dump(obj_dict, f, indent=4, default=Serializable._json_default)
            elif hasattr(obj, "to_json"):
                obj.to_json(path)
            else:
                with open(path, "w") as f:
                    json.dump(obj, f, indent=4, default=Serializable._json_default)
        elif path.suffix == ".csv":
            # 处理CSV序列化
            if isinstance(obj, pd.DataFrame):
                obj.to_csv(path, index=True)
            elif isinstance(obj, pd.Series):
                obj.to_csv(path, header=True)
            else:
                raise TypeError(f"无法将类型为 {type(obj)} 的对象保存为CSV")
        else:
            # 默认使用pickle序列化
            if isinstance(obj, Serializable):
                try:
                    obj.to_pickle(path)
                except Exception as e:
                    log.warning(f"使用to_pickle保存对象失败: {e}，尝试使用pickle直接保存")
                    with path.open("wb") as f:
                        pickle.dump(obj, f, protocol=Serializable.dump_protocol_version)
            else:
                with path.open("wb") as f:
                    pickle.dump(obj, f, protocol=Serializable.dump_protocol_version)

        log.info(f"对象已保存到 {path}")

    @staticmethod
    def general_load(path: Union[Path, str], cls: Type = None) -> Any:
        """
        通用对象加载方法

        根据文件扩展名自动选择反序列化格式

        Args:
            path: 加载路径
            cls: 对象类型，如果提供，将检查加载的对象是否为该类型

        Returns:
            Any: 加载的对象

        Raises:
            TypeError: 如果cls不为None且加载的对象不是cls类型
            FileNotFoundError: 如果文件不存在
        """
        path = Path(path)

        # 检查文件是否存在
        if not path.exists():
            raise FileNotFoundError(f"文件不存在: {path}")

        # 确定文件格式
        if path.suffix == ".json":
            # 处理JSON反序列化
            with open(path, "r") as f:
                obj = json.load(f)
        elif path.suffix == ".csv":
            # 处理CSV反序列化
            obj = pd.read_csv(path, index_col=0)
        else:
            # 默认使用pickle反序列化
            with open(path, "rb") as f:
                obj = pickle.load(f)

        if cls is not None and not isinstance(obj, cls):
            raise TypeError(f"加载的对象类型 {type(obj)} 不是有效的 {cls} 类型!")

        log.info(f"对象已从 {path} 加载")
        return obj
