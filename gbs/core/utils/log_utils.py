#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志工具模块

提供增强的日志记录功能，特别是结构化日志和上下文管理
"""

import time
import functools
import traceback
from typing import Any, Dict, Optional, Callable, TypeVar, cast
from contextlib import contextmanager

from .loki_logger import get_loki_logger

# 类型定义
F = TypeVar('F', bound=Callable[..., Any])
LoggerFunc = Callable[..., None]


def log_execution_time(logger_func: LoggerFunc) -> Callable[[F], F]:
    """
    记录函数执行时间的装饰器

    Args:
        logger_func: 用于记录的日志函数，如logger.info

    Returns:
        装饰器函数
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 记录执行时间
            logger_func(
                f"{func.__name__} 执行完成",
                {
                    "execution_time_seconds": execution_time,
                    "function": func.__name__,
                    "module": func.__module__
                }
            )
            return result
        return cast(F, wrapper)
    return decorator


@contextmanager
def log_context(logger_func: LoggerFunc, context_name: str, metadata: Optional[Dict[str, Any]] = None):
    """
    用于记录上下文执行的上下文管理器

    Args:
        logger_func: 用于记录的日志函数，如logger.info
        context_name: 上下文名称
        metadata: 附加元数据

    Yields:
        上下文管理器
    """
    metadata = metadata or {}
    start_time = time.time()
    
    # 记录开始
    logger_func(f"开始 {context_name}", metadata)
    
    try:
        yield
        # 记录成功完成
        end_time = time.time()
        execution_time = end_time - start_time
        logger_func(
            f"完成 {context_name}",
            {
                **metadata,
                "status": "success",
                "execution_time_seconds": execution_time
            }
        )
    except Exception as e:
        # 记录异常
        end_time = time.time()
        execution_time = end_time - start_time
        logger_func(
            f"{context_name} 失败: {str(e)}",
            {
                **metadata,
                "status": "error",
                "error": str(e),
                "traceback": traceback.format_exc(),
                "execution_time_seconds": execution_time
            }
        )
        raise


def create_structured_logger(name: str, system: Optional[str] = None):
    """
    创建增强的结构化日志记录器

    Args:
        name: 记录器名称
        system: 系统名称

    Returns:
        增强的日志记录器
    """
    # 获取基础日志记录器
    base_logger = get_loki_logger(name, system=system).logger
    
    class StructuredLogger:
        """增强的结构化日志记录器"""
        
        @staticmethod
        def debug(message: str, metadata: Optional[Dict[str, Any]] = None):
            """记录DEBUG级别日志"""
            if metadata:
                base_logger.debug(message, metadata)
            else:
                base_logger.debug(message)
        
        @staticmethod
        def info(message: str, metadata: Optional[Dict[str, Any]] = None):
            """记录INFO级别日志"""
            if metadata:
                base_logger.info(message, metadata)
            else:
                base_logger.info(message)
        
        @staticmethod
        def warning(message: str, metadata: Optional[Dict[str, Any]] = None):
            """记录WARNING级别日志"""
            if metadata:
                base_logger.warning(message, metadata)
            else:
                base_logger.warning(message)
        
        @staticmethod
        def error(message: str, metadata: Optional[Dict[str, Any]] = None):
            """记录ERROR级别日志"""
            if metadata:
                base_logger.error(message, metadata)
            else:
                base_logger.error(message)
        
        @staticmethod
        def critical(message: str, metadata: Optional[Dict[str, Any]] = None):
            """记录CRITICAL级别日志"""
            if metadata:
                base_logger.critical(message, metadata)
            else:
                base_logger.critical(message)
        
        @staticmethod
        def log_execution_time(func: F) -> F:
            """记录函数执行时间的装饰器"""
            return log_execution_time(base_logger.info)(func)
        
        @staticmethod
        @contextmanager
        def context(context_name: str, metadata: Optional[Dict[str, Any]] = None):
            """创建日志上下文"""
            with log_context(base_logger.info, context_name, metadata):
                yield
        
        @staticmethod
        @contextmanager
        def critical_context(context_name: str, metadata: Optional[Dict[str, Any]] = None):
            """创建关键日志上下文，失败时记录CRITICAL级别日志"""
            metadata = metadata or {}
            start_time = time.time()
            
            # 记录开始
            base_logger.info(f"开始关键操作: {context_name}", metadata)
            
            try:
                yield
                # 记录成功完成
                end_time = time.time()
                execution_time = end_time - start_time
                base_logger.info(
                    f"完成关键操作: {context_name}",
                    {
                        **metadata,
                        "status": "success",
                        "execution_time_seconds": execution_time
                    }
                )
            except Exception as e:
                # 记录异常
                end_time = time.time()
                execution_time = end_time - start_time
                base_logger.critical(
                    f"关键操作失败: {context_name} - {str(e)}",
                    {
                        **metadata,
                        "status": "error",
                        "error": str(e),
                        "traceback": traceback.format_exc(),
                        "execution_time_seconds": execution_time
                    }
                )
                raise
    
    return StructuredLogger()


# 使用示例
if __name__ == "__main__":
    # 创建结构化日志记录器
    logger = create_structured_logger("example", system="data_system")
    
    # 基本日志
    logger.info("这是一条信息日志")
    logger.warning("这是一条警告日志", {"reason": "磁盘空间不足", "available_mb": 100})
    
    # 使用上下文管理器
    with logger.context("数据处理", {"dataset": "example_data", "records": 1000}):
        # 处理数据的代码
        logger.info("处理数据中...")
        # 如果出现异常，会被上下文管理器捕获并记录
    
    # 使用装饰器记录执行时间
    @logger.log_execution_time
    def process_data(data_size):
        """处理数据的函数"""
        logger.info(f"处理 {data_size} 条数据")
        time.sleep(1)  # 模拟处理时间
        return data_size * 2
    
    result = process_data(100)
    logger.info(f"处理结果: {result}")
