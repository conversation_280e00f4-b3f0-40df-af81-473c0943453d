version: '3'

services:
  loki:
    image: grafana/loki:latest
    ports:
      - "3100:3100"
    volumes:
      - ./config/loki-config.yaml:/etc/loki/local-config.yaml
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - monitoring

  promtail:
    image: grafana/promtail:latest
    volumes:
      - ./config/promtail-config.yaml:/etc/promtail/config.yaml
      - /home/<USER>/gold-beast-system/outputs/logs:/home/<USER>/gold-beast-system/outputs/logs
    command: -config.file=/etc/promtail/config.yaml
    networks:
      - monitoring
    depends_on:
      - loki

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_HTTP_ADDR=0.0.0.0
      - GF_INSTALL_PLUGINS=yesoreyeram-infinity-datasource
    volumes:
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - grafana-data:/var/lib/grafana
    networks:
      - monitoring
    depends_on:
      - loki

networks:
  monitoring:
    driver: bridge

volumes:
  grafana-data:
