server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: system
    static_configs:
      - targets:
          - localhost
        labels:
          job: gold-beast-system
          __path__: /home/<USER>/gold-beast-system/outputs/logs/*.log

  - job_name: data_system
    static_configs:
      - targets:
          - localhost
        labels:
          job: gold-beast-system
          system: data_system
          __path__: /home/<USER>/gold-beast-system/outputs/logs/data_system*.log

  - job_name: model_system
    static_configs:
      - targets:
          - localhost
        labels:
          job: gold-beast-system
          system: model_system
          __path__: /home/<USER>/gold-beast-system/outputs/logs/model_system*.log

  - job_name: backtest_system
    static_configs:
      - targets:
          - localhost
        labels:
          job: gold-beast-system
          system: backtest_system
          __path__: /home/<USER>/gold-beast-system/outputs/logs/backtest_system*.log

  - job_name: eval_system
    static_configs:
      - targets:
          - localhost
        labels:
          job: gold-beast-system
          system: eval_system
          __path__: /home/<USER>/gold-beast-system/outputs/logs/eval_system*.log
