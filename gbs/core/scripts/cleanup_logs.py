#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
清理冗余日志文件脚本
- 将所有日志文件集中到outputs/logs目录
- 删除其他位置的冗余日志文件
"""

import os
import sys
import shutil
import argparse
from pathlib import Path
import logging

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入项目组件
from gbs.core.utils.paths import LOG_DIR, PROJECT_ROOT

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("cleanup_logs")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='清理冗余日志文件')
    parser.add_argument('--dry-run', action='store_true', help='仅显示将要执行的操作，不实际执行')
    parser.add_argument('--force', action='store_true', help='强制执行，不提示确认')
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 确保目标日志目录存在
    LOG_DIR.mkdir(parents=True, exist_ok=True)
    
    # 需要清理的日志目录
    redundant_dirs = [
        PROJECT_ROOT / 'logs',
        Path('/var/log/gold-beast-system'),
        Path('/var/log/gold-beast-system-logs')
    ]
    
    # 项目根目录下的日志文件
    root_log_files = list(PROJECT_ROOT.glob('*.log'))
    
    # 显示将要执行的操作
    logger.info(f"目标日志目录: {LOG_DIR}")
    logger.info("将要执行以下操作:")
    
    # 检查冗余目录
    for dir_path in redundant_dirs:
        if dir_path.exists():
            logger.info(f"- 将目录 {dir_path} 中的日志文件移动到 {LOG_DIR}")
            if not args.dry_run:
                # 移动日志文件
                for log_file in dir_path.glob('*.log'):
                    target_file = LOG_DIR / log_file.name
                    if target_file.exists():
                        # 如果目标文件已存在，合并内容
                        logger.info(f"  - 合并 {log_file} 到 {target_file}")
                        with open(log_file, 'r') as src, open(target_file, 'a') as dst:
                            dst.write(f"\n# 合并自 {log_file} - {'-'*40}\n")
                            dst.write(src.read())
                    else:
                        # 如果目标文件不存在，直接复制
                        logger.info(f"  - 复制 {log_file} 到 {target_file}")
                        shutil.copy2(log_file, target_file)
    
    # 检查根目录下的日志文件
    for log_file in root_log_files:
        logger.info(f"- 将文件 {log_file} 移动到 {LOG_DIR}")
        if not args.dry_run:
            target_file = LOG_DIR / log_file.name
            if target_file.exists():
                # 如果目标文件已存在，合并内容
                logger.info(f"  - 合并 {log_file} 到 {target_file}")
                with open(log_file, 'r') as src, open(target_file, 'a') as dst:
                    dst.write(f"\n# 合并自 {log_file} - {'-'*40}\n")
                    dst.write(src.read())
            else:
                # 如果目标文件不存在，直接复制
                logger.info(f"  - 复制 {log_file} 到 {target_file}")
                shutil.copy2(log_file, target_file)
    
    # 如果是dry-run模式，到此结束
    if args.dry_run:
        logger.info("这是dry-run模式，未执行实际操作")
        return
    
    # 确认是否删除冗余文件
    if not args.force:
        confirm = input("是否删除冗余日志文件? [y/N] ")
        if confirm.lower() != 'y':
            logger.info("操作已取消")
            return
    
    # 删除冗余文件
    logger.info("删除冗余日志文件:")
    
    # 删除冗余目录中的日志文件
    for dir_path in redundant_dirs:
        if dir_path.exists():
            for log_file in dir_path.glob('*.log'):
                logger.info(f"- 删除 {log_file}")
                log_file.unlink()
    
    # 删除根目录下的日志文件
    for log_file in root_log_files:
        logger.info(f"- 删除 {log_file}")
        log_file.unlink()
    
    logger.info("清理完成")

if __name__ == '__main__':
    main()
