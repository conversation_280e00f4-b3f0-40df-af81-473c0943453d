#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
启动监控系统脚本
- 启动 Loki, Promtail 和 Grafana
- 提供日志和监控功能
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入项目组件
from gbs.core.utils.loki_logger import get_loki_logger

# 获取日志记录器
log = get_loki_logger(__name__).logger

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='启动监控系统')
    parser.add_argument('--detach', '-d', action='store_true', help='后台运行')
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()

    # 监控系统目录
    monitoring_dir = Path(project_root) / 'gbs' / 'core' / 'monitoring'
    print(f"监控系统目录: {monitoring_dir}")

    # 确保监控系统目录存在
    if not monitoring_dir.exists():
        print(f"错误: 监控系统目录不存在: {monitoring_dir}")
        print(f"当前工作目录: {os.getcwd()}")
        print(f"项目根目录: {project_root}")
        sys.exit(1)

    # 确保日志目录存在
    logs_dir = Path(project_root) / 'logs'
    logs_dir.mkdir(parents=True, exist_ok=True)

    # 构建 Docker Compose 命令
    cmd = ['sudo', 'docker-compose', '-f', str(monitoring_dir / 'docker-compose.yml'), 'up']

    if args.detach:
        cmd.append('-d')

    # 切换到监控系统目录
    os.chdir(monitoring_dir)

    # 启动监控系统
    log.info(f"启动监控系统: {' '.join(cmd)}")
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        log.error(f"启动监控系统失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        log.info("用户中断，停止监控系统")
        subprocess.run(['sudo', 'docker-compose', '-f', str(monitoring_dir / 'docker-compose.yml'), 'down'], check=True)

    log.info("监控系统已启动")
    print("Grafana 可通过 http://localhost:3000 访问")
    print("默认用户名/密码: admin/admin")

if __name__ == '__main__':
    main()
