#!/usr/bin/env python
"""
Script to start the MLflow UI server.

Usage:
    python start_mlflow_ui.py [--port PORT]
"""

import os
import sys
import argparse
import subprocess
from gbs.core.conf.mlflow_config import MLFLOW_TRACKING_URI

def parse_args():
    parser = argparse.ArgumentParser(description='Start MLflow UI server')
    parser.add_argument('--port', type=int, default=5000,
                        help='Port to run the server on (default: 5000)')
    return parser.parse_args()

def main():
    args = parse_args()

    # Create mlruns directory if it doesn't exist
    if not os.path.exists(MLFLOW_TRACKING_URI) and not MLFLOW_TRACKING_URI.startswith(('http://', 'https://', 'sqlite://')):
        os.makedirs(MLFLOW_TRACKING_URI, exist_ok=True)
        print(f"Created MLflow tracking directory: {MLFLOW_TRACKING_URI}")

    # Start MLflow UI server
    cmd = [
        "mlflow", "ui",
        "--backend-store-uri", MLFLOW_TRACKING_URI,
        "--port", str(args.port)
    ]

    print(f"Starting MLflow UI server on port {args.port}")
    print(f"Tracking URI: {MLFLOW_TRACKING_URI}")
    print("Press Ctrl+C to stop the server")

    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\nStopping MLflow UI server")
    except Exception as e:
        print(f"Error starting MLflow UI server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
