#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Example script for using the task module.
This is a simplified version that doesn't require MongoDB.
"""

import os
import sys
import numpy as np
import pandas as pd
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).resolve().parents[2]))

from gbs.workflow import R
from gbs.workflow.utils import init_exp_manager
from gbs.workflow.task import TaskGen, RollingGen, task_generator
from gbs.core.utils.loki_logger import get_loki_logger

logger = get_loki_logger("task_example").logger

# Define a simple task template
DEFAULT_TASK = {
    "model": {
        "class": "LinearRegression",
        "module_path": "sklearn.linear_model",
        "kwargs": {
            "fit_intercept": True,
        },
    },
    "dataset": {
        "class": "DatasetH",
        "module_path": "gbs.data_system.base.dataset",
        "kwargs": {
            "handler": {
                "class": "Alpha158",
                "module_path": "gbs.data_system.processors",
                "kwargs": {
                    "start_time": "2010-01-01",
                    "end_time": "2020-01-01",
                    "fit_start_time": "2010-01-01",
                    "fit_end_time": "2018-12-31",
                    "instruments": "sp500",
                },
            },
            "segments": {
                "train": ("2010-01-01", "2018-12-31"),
                "valid": ("2019-01-01", "2019-06-30"),
                "test": ("2019-07-01", "2020-01-01"),
            },
        },
    },
}

# Define a custom task generator
class ModelParamGen(TaskGen):
    def __init__(self, param_dict):
        self.param_dict = param_dict

    def generate(self, task: dict) -> list:
        tasks = []
        for param_name, param_values in self.param_dict.items():
            for value in param_values:
                new_task = task.copy()
                new_task["model"]["kwargs"][param_name] = value
                tasks.append(new_task)
        return tasks

def train_model(task_def):
    """
    Function to train a model based on task definition.

    Parameters
    ----------
    task_def : dict
        Task definition

    Returns
    -------
    dict
        Training results
    """
    logger.info(f"Training model with task: {task_def}")

    # In a real implementation, you would:
    # 1. Load the dataset
    # 2. Initialize the model
    # 3. Train the model
    # 4. Evaluate the model
    # 5. Return the results

    # For this example, we'll just simulate training
    import time
    time.sleep(2)  # Simulate training time

    # Generate some random metrics
    results = {
        "model": task_def["model"],
        "metrics": {
            "train_score": np.random.uniform(0.7, 0.9),
            "valid_score": np.random.uniform(0.6, 0.8),
            "test_score": np.random.uniform(0.5, 0.7),
        }
    }

    return results

def main():
    """
    Main function for the task example.
    """
    # Initialize the experiment manager
    init_exp_manager()

    # Create task generators
    rolling_gen = RollingGen(step=180, rtype=RollingGen.ROLL_SD)  # 6-month rolling window
    model_param_gen = ModelParamGen({
        "fit_intercept": [True, False],
        "positive": [True, False],
    })

    # Generate tasks
    tasks = task_generator(DEFAULT_TASK, [rolling_gen, model_param_gen])
    logger.info(f"Generated {len(tasks)} tasks")

    # Run tasks directly without TaskManager
    with R.start(experiment_name="task_example", recorder_name="model_training"):
        # Log the task configuration
        R.log_params(task_count=len(tasks))

        # Run tasks directly
        results = []
        for task in tasks:
            logger.info(f"Training model with task: {task}")
            result = train_model(task)
            results.append(result)

        # Log results
        R.log_params(completed_tasks=len(results))

        # Find the best model
        best_model = max(results, key=lambda x: x["metrics"]["valid_score"])
        R.log_metrics(
            best_train_score=best_model["metrics"]["train_score"],
            best_valid_score=best_model["metrics"]["valid_score"],
            best_test_score=best_model["metrics"]["test_score"],
        )

        # Save the best model
        R.save_objects(best_model=best_model)

        logger.info("Task example completed successfully")

if __name__ == "__main__":
    main()
