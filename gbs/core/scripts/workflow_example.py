#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Example script for using the workflow manager.
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).resolve().parents[2]))

from gbs.workflow import R
from gbs.workflow.utils import init_exp_manager
from gbs.core.utils.loki_logger import get_loki_logger

logger = get_loki_logger("workflow_example").logger

# Make sure the MLflow tracking URI is set correctly
from gbs.core.conf.mlflow_config import MLFLOW_TRACKING_URI
print(f"MLflow tracking URI: {MLFLOW_TRACKING_URI}")

# Define SimpleModel outside of main function to make it picklable
class SimpleModel:
    def __init__(self, learning_rate=0.01):
        self.learning_rate = learning_rate
        self.w = np.random.randn(1, 1)
        self.b = np.random.randn(1)
        self.losses = []

    def predict(self, X):
        return X @ self.w + self.b

    def train(self, X, y, epochs=100):
        for epoch in range(epochs):
            # Forward pass
            y_pred = self.predict(X)

            # Compute loss
            loss = np.mean((y_pred - y) ** 2)
            self.losses.append(loss)

            # Backward pass
            dw = 2 * X.T @ (y_pred - y) / len(X)
            db = 2 * np.mean(y_pred - y)

            # Update parameters
            self.w -= self.learning_rate * dw
            self.b -= self.learning_rate * db

            # Log metrics
            if epoch % 10 == 0:
                if 'R' in globals():  # Check if R is available (for standalone testing)
                    R.log_metrics(loss=float(loss), epoch=epoch)
                print(f"Epoch {epoch}, Loss: {loss:.4f}")

        return self.losses

def main():
    """
    Main function for the workflow example.
    """
    # Initialize the experiment manager
    init_exp_manager()

    # Start an experiment
    with R.start(experiment_name="workflow_example", recorder_name="example_run"):
        logger.info("Starting workflow example")

        # Log parameters
        R.log_params(
            learning_rate=0.01,
            batch_size=32,
            epochs=10,
            model_type="linear_regression"
        )

        # Generate some dummy data
        np.random.seed(42)
        X = np.random.rand(100, 1)
        y = 2 * X + 1 + 0.1 * np.random.randn(100, 1)

        # Train the model
        model = SimpleModel(learning_rate=0.1)
        losses = model.train(X, y, epochs=100)

        # Plot the losses
        plt.figure(figsize=(10, 6))
        plt.plot(losses)
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training Loss')
        plt.grid(True)

        # Save the plot
        plot_path = "loss_curve.png"
        plt.savefig(plot_path)
        plt.close()

        # Log the plot as an artifact
        R.log_artifact(plot_path)
        os.remove(plot_path)  # Clean up

        # Save the model
        R.save_objects(model=model)

        # Log final metrics
        R.log_metrics(
            final_loss=float(losses[-1]),
            weight=float(model.w[0][0]),
            bias=float(model.b[0])
        )

        # Log a summary
        R.set_tags(
            status="completed",
            description="Simple linear regression example"
        )

        logger.info("Workflow example completed successfully")

if __name__ == "__main__":
    main()
