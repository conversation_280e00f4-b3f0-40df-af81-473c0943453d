#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
停止监控系统脚本
- 停止 Loki, Promtail 和 Grafana
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入项目组件
from gbs.core.utils.loki_logger import get_loki_logger

# 获取日志记录器
log = get_loki_logger(__name__).logger

def main():
    """主函数"""
    # 监控系统目录
    monitoring_dir = Path(project_root) / 'gbs' / 'core' / 'monitoring'

    # 构建 Docker Compose 命令
    cmd = ['docker-compose', '-f', str(monitoring_dir / 'docker-compose.yml'), 'down']

    # 切换到监控系统目录
    os.chdir(monitoring_dir)

    # 停止监控系统
    log.info(f"停止监控系统: {' '.join(cmd)}")
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        log.error(f"停止监控系统失败: {e}")
        sys.exit(1)

    log.info("监控系统已停止")

if __name__ == '__main__':
    main()
