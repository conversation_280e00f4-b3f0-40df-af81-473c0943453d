#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Portfolio dataset
- Inherits from data_system's DatasetH
- Provides data interface needed for model training
- Responsible for data splitting and preparing data for models
- Provides unified data access for models
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union, Text
import torch

from gbs.data_system.base.dataset import DatasetH
from gbs.data_system.base.dataset.handler import <PERSON><PERSON><PERSON><PERSON>, DataHandlerLP
from gbs.data_system.handlers.portfolio_handler import PortfolioDataHandler
from gbs.core.utils.loki_logger import get_loki_logger

# Get logger
log = get_loki_logger(__name__).logger

class PortfolioDataset(DatasetH):
    """
    Portfolio dataset
    - Inherits from data_system's DatasetH
    - Provides data interface needed for model training
    - Responsible for data splitting and preparing data for models
    - Provides unified data access for models
    """

    def __init__(self, data_config=None, segments=None, features=None, batch_size=32, config=None, **kwargs):
        """
        Initialize the dataset with simplified configuration

        Args:
            data_config: Data configuration dictionary (path, columns, etc.)
            segments: Time segments for train/valid/test
            features: List of features to use
            batch_size: Batch size for data loaders
            config: Legacy configuration dictionary (for backward compatibility)
            **kwargs: Additional parameters
        """
        # Handle both new simplified config and legacy config
        if config is None:
            config = {}

        # Create a complete config from simplified parameters
        if data_config is not None:
            # Use the new simplified configuration
            complete_config = {
                'data': {
                    'data_path': data_config.get('data_path'),
                },
                'columns': data_config.get('columns', {})
            }

            # Add features if provided
            if features is not None:
                # Flatten nested lists if needed
                flat_features = []
                for feature in features:
                    if isinstance(feature, list):
                        flat_features.extend(feature)
                    else:
                        flat_features.append(feature)
                complete_config['data']['features'] = flat_features

            # Add batch size
            complete_config['training'] = {'batch_size': batch_size}

            # Merge with any existing config
            for key, value in config.items():
                if key not in complete_config:
                    complete_config[key] = value

            config = complete_config

        # 处理kwargs中的参数
        # 如果kwargs中有small_dataset或limit_stocks参数，将它们添加到config中
        if 'small_dataset' in kwargs:
            if 'data' not in config:
                config['data'] = {}
            config['data']['small_dataset'] = kwargs['small_dataset']
            log.info(f"从kwargs中获取small_dataset参数: {kwargs['small_dataset']}")

        if 'small_dataset_size' in kwargs:
            if 'data' not in config:
                config['data'] = {}
            config['data']['small_dataset_size'] = kwargs['small_dataset_size']
            log.info(f"从kwargs中获取small_dataset_size参数: {kwargs['small_dataset_size']}")

        if 'limit_stocks' in kwargs:
            if 'data' not in config:
                config['data'] = {}
            config['data']['limit_stocks'] = kwargs['limit_stocks']
            log.info(f"从kwargs中获取limit_stocks参数: {kwargs['limit_stocks']}")

        # Create data handler
        handler = PortfolioDataHandler(config)

        # Set up data segments based on new format or legacy format
        if segments is not None:
            # Convert new segment format to handler format
            handler_segments = {}
            for segment_name, date_range in segments.items():
                if isinstance(date_range, list) and len(date_range) == 2:
                    handler_segments[segment_name] = (date_range[0], date_range[1])
                else:
                    handler_segments[segment_name] = date_range
        else:
            # Use default segments
            handler_segments = {
                'train': ('train', None),
                'valid': ('valid', None),
                'test': ('test', None)
            }

        # Initialize base class
        super().__init__(
            handler=handler,
            segments=handler_segments,
            **kwargs
        )

        # Save configuration
        self.config = config
        self.batch_size = batch_size if batch_size is not None else config.get('training', {}).get('batch_size', 32)

        # Initialize data
        self.train_data = None
        self.val_data = None
        self.test_data = None

        # Split data
        self._split_data()

    def _split_data(self):
        """Split data into training, validation, and test sets"""
        # Get processed data
        processed_data = self.handler.get_processed_data()

        # Get split ratio
        data_config = self.config.get('data', {})
        train_val_ratio = data_config.get('train_val_ratio', 0.8)

        # Split data
        log.info("Splitting data into training, validation, and test sets...")
        train_data, val_data, test_data = self.handler.split_data(
            train_val_ratio=train_val_ratio
        )

        self.train_data = train_data
        self.val_data = val_data
        self.test_data = test_data

        log.info(f"Data loading complete. Training set: {len(train_data['features'])} time points, "
                f"Validation set: {len(val_data['features'])} time points, "
                f"Test set: {len(test_data['features'])} time points")

    def get_data_loader(self, segment='train', batch_size=None):
        """
        Get data loader

        Args:
            segment: Data segment, 'train', 'valid', or 'test'
            batch_size: Batch size, None means use default

        Returns:
            DataLoader: PyTorch data loader
        """
        if batch_size is None:
            batch_size = self.batch_size

        if segment == 'train':
            data = self.train_data
        elif segment in ['valid', 'val']:
            data = self.val_data
        elif segment == 'test':
            data = self.test_data
        else:
            raise ValueError(f"Unknown data segment: {segment}")

        return self.handler.create_data_loaders(data, batch_size=batch_size)

    def prepare(
        self,
        segments: Union[List[Text], Tuple[Text], Text, slice, pd.Index],
        col_set=DataHandler.CS_ALL,
        data_key=DataHandlerLP.DK_I,
        **kwargs,
    ) -> Union[List[pd.DataFrame], pd.DataFrame]:
        """
        Prepare data for learning and inference.

        Args:
            segments: Scope of the data to prepare, e.g., 'train' or ['train', 'valid']
            col_set: Column set to fetch
            data_key: Data key to fetch (e.g., DK_I for inference data)
            **kwargs: Additional arguments

        Returns:
            The prepared data
        """
        log.info(f"Preparing data for segments: {segments}")

        # Handle different types of segment specifications
        if isinstance(segments, str):
            if segments == 'train':
                return self.train_data
            elif segments in ['valid', 'val']:
                return self.val_data
            elif segments == 'test':
                return self.test_data
            elif segments in self.segments:
                # Use parent class implementation for other named segments
                return super().prepare(segments, col_set=col_set, data_key=data_key, **kwargs)
            else:
                raise ValueError(f"Unknown data segment: {segments}")

        if isinstance(segments, (list, tuple)):
            # If segments is a list/tuple of segment names, return a list of prepared data
            result = []
            for seg in segments:
                if seg == 'train':
                    result.append(self.train_data)
                elif seg in ['valid', 'val']:
                    result.append(self.val_data)
                elif seg == 'test':
                    result.append(self.test_data)
                elif seg in self.segments:
                    # Use parent class implementation for other named segments
                    result.append(super().prepare(seg, col_set=col_set, data_key=data_key, **kwargs))
                else:
                    raise ValueError(f"Unknown data segment: {seg}")
            return result

        # For other types of segments (slice, pd.Index), use parent class implementation
        return super().prepare(segments, col_set=col_set, data_key=data_key, **kwargs)

    def get_feature_dimension(self):
        """
        Get feature dimension

        Returns:
            int: Feature dimension
        """
        if self.train_data and 'feature_cols' in self.train_data:
            return len(self.train_data['feature_cols'])
        return 0

    @classmethod
    def from_config(cls, config):
        """
        Create dataset from configuration

        Args:
            config: Configuration dictionary

        Returns:
            PortfolioDataset: Dataset instance
        """
        return cls(config)
