#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
初始化数据系统

这个模块提供了初始化数据系统的功能，支持配置多数据源。
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Optional

from gbs.core.utils.loki_logger import get_loki_logger
from gbs.data_system.base.data import D
from gbs.core.utils.mod import init_instance_by_config
from gbs.core.conf.config import C

# 设置日志
logger = get_loki_logger(__name__).logger

def init_data_system(config_path: Optional[str] = None):
    """
    初始化数据系统
    
    Args:
        config_path: 配置文件路径，如果为None则使用默认配置
    """
    # 加载配置
    config = _load_config(config_path)
    
    # 获取提供者配置
    provider_config = config.get("provider_config", {})
    
    # 如果配置中有hybrid_feature_provider，则使用它
    if "hybrid_feature" in provider_config:
        # 添加数据库配置
        if "kwargs" not in provider_config["hybrid_feature"]:
            provider_config["hybrid_feature"]["kwargs"] = {}
        
        provider_config["hybrid_feature"]["kwargs"]["db_config"] = config.get("db_config", {})
        
        # 更新提供者配置
        provider_config["feature"] = provider_config["hybrid_feature"]
        del provider_config["hybrid_feature"]
    
    # 初始化数据提供者
    logger.info("初始化数据提供者")
    D.initialize(provider_config)
    
    logger.info("数据系统初始化完成")

def _load_config(config_path: Optional[str]) -> Dict:
    """
    加载配置
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Dict: 配置字典
    """
    if config_path is None:
        # 使用默认配置路径
        config_path = Path(__file__).parent.parent.parent.parent / "config" / "data_sources.yaml"
    
    if not os.path.exists(config_path):
        logger.warning(f"配置文件 {config_path} 不存在，使用默认配置")
        return {
            "provider_config": {
                "calendar": {"class": "LocalCalendarProvider", "module_path": "gbs.data_system.base.data"},
                "instrument": {"class": "LocalInstrumentProvider", "module_path": "gbs.data_system.base.data"},
                "feature": {"class": "LocalFeatureProvider", "module_path": "gbs.data_system.base.data"},
                "expression": {"class": "LocalExpressionProvider", "module_path": "gbs.data_system.base.data"},
                "dataset": {"class": "LocalDatasetProvider", "module_path": "gbs.data_system.base.data"}
            },
            "db_config": {
                "user": "postgres",
                "password": "onWISH#2024!DB",
                "host": "prod-onwish-app-cluster.cluster-cjuysus6851i.us-east-1.rds.amazonaws.com",
                "database": "algotrading",
                "port": "5432"
            }
        }
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    return config

# 更新配置文件
def update_data_sources_config():
    """
    更新数据源配置文件，添加混合特征提供者
    """
    config_path = Path(__file__).parent.parent.parent.parent / "config" / "data_sources.yaml"
    
    if not os.path.exists(config_path):
        logger.warning(f"配置文件 {config_path} 不存在，无法更新")
        return
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # 添加混合特征提供者
    if "provider_config" in config:
        config["provider_config"]["hybrid_feature"] = {
            "class": "HybridFeatureProvider",
            "module_path": "gbs.data_system.providers.hybrid_feature_provider",
            "kwargs": {
                "backend": {
                    "class": "FileFeatureStorage",
                    "module_path": "gbs.data_system.base.storage.file_storage"
                }
            }
        }
    
    # 保存更新后的配置
    with open(config_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    logger.info(f"已更新配置文件 {config_path}")

if __name__ == "__main__":
    # 更新配置文件
    update_data_sources_config()
    
    # 初始化数据系统
    init_data_system()
