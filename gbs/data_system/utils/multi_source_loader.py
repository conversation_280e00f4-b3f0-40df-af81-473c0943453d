#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
多数据源加载器

这个模块提供了从多个数据源加载数据的功能，包括：
1. 本地文件（如pkl文件）
2. 数据库（如PostgreSQL）
"""

import os
import yaml
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Union, Optional, Any
import logging
from sqlalchemy import create_engine

from gbs.core.utils.loki_logger import get_loki_logger
from gbs.data_system.base.data import D
from gbs.data_system.base.storage.db_storage import get_db_engine, DatabaseFeatureStorage
from gbs.core.conf.config import C

# 设置日志
logger = get_loki_logger(__name__).logger

class MultiSourceDataLoader:
    """
    多数据源数据加载器
    
    支持从多个数据源加载数据，包括：
    1. 本地文件（如pkl文件）
    2. 数据库（如PostgreSQL）
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化多数据源数据加载器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
        """
        self.config = self._load_config(config_path)
        self.db_engine = None
        self._init_db_connection()
        
    def _load_config(self, config_path: Optional[str]) -> Dict:
        """
        加载配置
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Dict: 配置字典
        """
        if config_path is None:
            # 使用默认配置路径
            config_path = Path(__file__).parent.parent.parent.parent / "config" / "data_sources.yaml"
        
        if not os.path.exists(config_path):
            logger.warning(f"配置文件 {config_path} 不存在，使用默认配置")
            return {
                "db_config": {
                    "user": "postgres",
                    "password": "onWISH#2024!DB",
                    "host": "prod-onwish-app-cluster.cluster-cjuysus6851i.us-east-1.rds.amazonaws.com",
                    "database": "algotrading",
                    "port": "5432"
                },
                "local_data": {
                    "jkp_data": "gbs/data/processed_data/jkp/full_usa.pkl"
                }
            }
        
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        return config
    
    def _init_db_connection(self):
        """
        初始化数据库连接
        """
        db_config = self.config.get("db_config", {})
        if db_config:
            try:
                self.db_engine = get_db_engine(db_config)
                logger.info(f"成功连接到数据库 {db_config.get('database', '')}")
            except Exception as e:
                logger.error(f"连接数据库时出错: {str(e)}")
                self.db_engine = None
    
    def load_jkp_data(self) -> pd.DataFrame:
        """
        加载JKP数据
        
        Returns:
            pd.DataFrame: JKP数据
        """
        jkp_path = self.config.get("local_data", {}).get("jkp_data")
        if not jkp_path or not os.path.exists(jkp_path):
            logger.error(f"JKP数据文件 {jkp_path} 不存在")
            return pd.DataFrame()
        
        try:
            logger.info(f"从 {jkp_path} 加载JKP数据")
            df = pd.read_pickle(jkp_path)
            logger.info(f"成功加载JKP数据，数据形状: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"加载JKP数据时出错: {str(e)}")
            return pd.DataFrame()
    
    def load_ohlcv_from_db(self, symbols: List[str], start_date: str, end_date: str) -> pd.DataFrame:
        """
        从数据库加载OHLCV数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期，格式为YYYY-MM-DD
            end_date: 结束日期，格式为YYYY-MM-DD
            
        Returns:
            pd.DataFrame: OHLCV数据
        """
        if self.db_engine is None:
            logger.error("数据库引擎未初始化")
            return pd.DataFrame()
        
        # 构建SQL查询
        symbols_str = "', '".join(symbols)
        query = f"""
        SELECT date, symbol, open, high, low, close, volume
        FROM daily_ohlcv
        WHERE symbol IN ('{symbols_str}')
        AND date BETWEEN '{start_date}' AND '{end_date}'
        ORDER BY date, symbol
        """
        
        try:
            logger.info(f"从数据库加载OHLCV数据: {len(symbols)}个股票，日期范围: {start_date} 至 {end_date}")
            df = pd.read_sql(query, self.db_engine)
            
            # 转换日期列为datetime类型
            df['date'] = pd.to_datetime(df['date'])
            
            # 设置多级索引
            df = df.set_index(['date', 'symbol'])
            
            logger.info(f"成功加载OHLCV数据，数据形状: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"从数据库加载OHLCV数据时出错: {str(e)}")
            return pd.DataFrame()
    
    def get_backtest_data(self, symbols: List[str], start_date: str, end_date: str, 
                          include_jkp: bool = True, include_ohlcv: bool = True) -> pd.DataFrame:
        """
        获取回测数据，合并JKP数据和OHLCV数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期，格式为YYYY-MM-DD
            end_date: 结束日期，格式为YYYY-MM-DD
            include_jkp: 是否包含JKP数据
            include_ohlcv: 是否包含OHLCV数据
            
        Returns:
            pd.DataFrame: 回测数据
        """
        dfs = []
        
        # 加载JKP数据
        if include_jkp:
            jkp_df = self.load_jkp_data()
            if not jkp_df.empty:
                # 过滤日期范围
                if isinstance(jkp_df.index, pd.MultiIndex):
                    date_idx = jkp_df.index.get_level_values(0)
                    jkp_df = jkp_df.loc[(date_idx >= start_date) & (date_idx <= end_date)]
                
                # 过滤股票
                if symbols and len(symbols) > 0:
                    if isinstance(jkp_df.index, pd.MultiIndex):
                        symbol_idx = jkp_df.index.get_level_values(1)
                        jkp_df = jkp_df.loc[symbol_idx.isin(symbols)]
                
                dfs.append(jkp_df)
        
        # 加载OHLCV数据
        if include_ohlcv:
            ohlcv_df = self.load_ohlcv_from_db(symbols, start_date, end_date)
            if not ohlcv_df.empty:
                dfs.append(ohlcv_df)
        
        # 合并数据
        if not dfs:
            logger.warning("没有可用的数据")
            return pd.DataFrame()
        
        if len(dfs) == 1:
            return dfs[0]
        
        # 合并多个数据源
        try:
            result = pd.concat(dfs, axis=1, join='outer')
            logger.info(f"成功合并数据，最终数据形状: {result.shape}")
            return result
        except Exception as e:
            logger.error(f"合并数据时出错: {str(e)}")
            return pd.DataFrame()

# 创建全局实例
data_loader = MultiSourceDataLoader()

def get_data_loader() -> MultiSourceDataLoader:
    """
    获取数据加载器实例
    
    Returns:
        MultiSourceDataLoader: 数据加载器实例
    """
    return data_loader

def load_backtest_data(symbols: List[str], start_date: str, end_date: str, 
                      include_jkp: bool = True, include_ohlcv: bool = True) -> pd.DataFrame:
    """
    加载回测数据
    
    Args:
        symbols: 股票代码列表
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD
        include_jkp: 是否包含JKP数据
        include_ohlcv: 是否包含OHLCV数据
        
    Returns:
        pd.DataFrame: 回测数据
    """
    return data_loader.get_backtest_data(symbols, start_date, end_date, include_jkp, include_ohlcv)
