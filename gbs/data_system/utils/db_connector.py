#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库连接器

这个模块提供了连接数据库并加载数据的功能。
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Union
from sqlalchemy import create_engine
from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.conf.config import C

# 设置日志
logger = get_loki_logger(__name__).logger

def get_db_engine(db_config=None):
    """
    获取数据库引擎
    
    Args:
        db_config: 数据库配置，如果为None则使用全局配置
        
    Returns:
        SQLAlchemy引擎
    """
    if db_config is None:
        db_config = C.get("db_config", {})
    
    if not db_config:
        logger.error("未找到数据库配置")
        return None
    
    user = db_config.get('user', 'postgres')
    password = db_config.get('password', '')
    host = db_config.get('host', 'localhost')
    database = db_config.get('database', 'gold_beast')
    port = db_config.get('port', '5432')
    
    connection_string = f"postgresql://{user}:{password}@{host}:{port}/{database}"
    
    try:
        engine = create_engine(connection_string, pool_size=5, max_overflow=10)
        logger.info(f"成功创建数据库引擎，连接到 {database}")
        return engine
    except Exception as e:
        logger.error(f"创建数据库引擎时出错: {str(e)}")
        return None

def load_ohlcv_from_db(symbols: List[str], start_date: str, end_date: str, db_config=None) -> pd.DataFrame:
    """
    从数据库加载OHLCV数据
    
    Args:
        symbols: 股票代码列表
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD
        db_config: 数据库配置，如果为None则使用全局配置
        
    Returns:
        pd.DataFrame: OHLCV数据
    """
    engine = get_db_engine(db_config)
    if engine is None:
        logger.error("无法连接到数据库")
        return pd.DataFrame()
    
    # 构建SQL查询
    symbols_str = "', '".join(symbols)
    query = f"""
    SELECT date, symbol, open, high, low, close, volume
    FROM daily_ohlcv
    WHERE symbol IN ('{symbols_str}')
    AND date BETWEEN '{start_date}' AND '{end_date}'
    ORDER BY date, symbol
    """
    
    try:
        logger.info(f"从数据库加载OHLCV数据: {len(symbols)}个股票，日期范围: {start_date} 至 {end_date}")
        df = pd.read_sql(query, engine)
        
        # 转换日期列为datetime类型
        df['date'] = pd.to_datetime(df['date'])
        
        # 设置多级索引
        df = df.set_index(['date', 'symbol'])
        
        logger.info(f"成功加载OHLCV数据，数据形状: {df.shape}")
        return df
    except Exception as e:
        logger.error(f"从数据库加载OHLCV数据时出错: {str(e)}")
        return pd.DataFrame()

def load_feature_from_db(instrument: str, field: str, start_date: str, end_date: str, db_config=None) -> pd.Series:
    """
    从数据库加载特定特征数据
    
    Args:
        instrument: 股票代码
        field: 特征字段
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD
        db_config: 数据库配置，如果为None则使用全局配置
        
    Returns:
        pd.Series: 特征数据
    """
    engine = get_db_engine(db_config)
    if engine is None:
        logger.error("无法连接到数据库")
        return pd.Series(dtype=np.float32)
    
    # 构建SQL查询
    query = f"""
    SELECT date, {field}
    FROM daily_ohlcv
    WHERE symbol = '{instrument}'
    AND date BETWEEN '{start_date}' AND '{end_date}'
    ORDER BY date
    """
    
    try:
        logger.info(f"从数据库加载特征数据: 股票={instrument}, 字段={field}, 日期范围: {start_date} 至 {end_date}")
        df = pd.read_sql(query, engine)
        
        # 检查结果
        if df.empty:
            logger.warning(f"数据库中没有找到股票 {instrument} 的 {field} 数据")
            return pd.Series(dtype=np.float32)
        
        # 转换为Series
        df['date'] = pd.to_datetime(df['date'])
        series = df.set_index('date')[field]
        
        logger.info(f"成功加载特征数据，数据点数: {len(series)}")
        return series
    except Exception as e:
        logger.error(f"从数据库加载特征数据时出错: {str(e)}")
        return pd.Series(dtype=np.float32)
