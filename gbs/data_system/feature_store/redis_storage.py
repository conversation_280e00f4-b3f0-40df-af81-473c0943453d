#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Redis Storage for Feature Store

This module provides Redis-based storage implementation for the Feature Store.
"""

import json
import pickle
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta

from gbs.core.utils.loki_logger import get_loki_logger

logger = get_loki_logger(__name__).logger

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("Redis not available. Install redis-py: pip install redis")


class RedisFeatureStorage:
    """
    Redis-based feature storage for high-performance caching and retrieval
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize Redis Feature Storage
        
        Args:
            config: Redis configuration
                {
                    'host': 'localhost',
                    'port': 6379,
                    'db': 0,
                    'password': None,
                    'decode_responses': False,
                    'socket_timeout': 5,
                    'socket_connect_timeout': 5,
                    'max_connections': 50,
                    'ttl': 3600,  # Default TTL in seconds
                    'key_prefix': 'jkp_features:'
                }
        """
        if not REDIS_AVAILABLE:
            raise ImportError("Redis is not available. Install redis-py: pip install redis")
        
        self.config = config or {}
        
        # Redis connection configuration
        self.redis_config = {
            'host': self.config.get('host', 'localhost'),
            'port': self.config.get('port', 6379),
            'db': self.config.get('db', 0),
            'password': self.config.get('password', None),
            'decode_responses': self.config.get('decode_responses', False),
            'socket_timeout': self.config.get('socket_timeout', 5),
            'socket_connect_timeout': self.config.get('socket_connect_timeout', 5),
            'max_connections': self.config.get('max_connections', 50)
        }
        
        # Storage configuration
        self.default_ttl = self.config.get('ttl', 3600)  # 1 hour default
        self.key_prefix = self.config.get('key_prefix', 'jkp_features:')
        
        # Initialize Redis connection
        self._redis_client = None
        self._connection_pool = None
        self._initialize_redis()
        
    def _initialize_redis(self):
        """Initialize Redis connection with connection pooling"""
        try:
            # Create connection pool
            self._connection_pool = redis.ConnectionPool(**self.redis_config)
            
            # Create Redis client
            self._redis_client = redis.Redis(connection_pool=self._connection_pool)
            
            # Test connection
            self._redis_client.ping()
            
            logger.info(f"Redis connection established: {self.redis_config['host']}:{self.redis_config['port']}")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    def _generate_key(self, feature_name: str, instrument: str = None) -> str:
        """Generate Redis key for feature data"""
        if instrument:
            return f"{self.key_prefix}{feature_name}:{instrument}"
        else:
            return f"{self.key_prefix}{feature_name}"
    
    def _serialize_data(self, data: pd.DataFrame) -> bytes:
        """Serialize DataFrame to bytes for Redis storage"""
        try:
            # Use pickle for efficient serialization
            return pickle.dumps(data, protocol=pickle.HIGHEST_PROTOCOL)
        except Exception as e:
            logger.error(f"Failed to serialize data: {e}")
            raise
    
    def _deserialize_data(self, data_bytes: bytes) -> pd.DataFrame:
        """Deserialize bytes to DataFrame"""
        try:
            return pickle.loads(data_bytes)
        except Exception as e:
            logger.error(f"Failed to deserialize data: {e}")
            raise
    
    def save_feature(self, feature_name: str, data: pd.DataFrame, 
                    ttl: Optional[int] = None, metadata: Dict[str, Any] = None) -> bool:
        """
        Save feature data to Redis
        
        Args:
            feature_name: Name of the feature
            data: Feature data with MultiIndex (instrument, datetime)
            ttl: Time to live in seconds (optional)
            metadata: Feature metadata (optional)
            
        Returns:
            bool: Success status
        """
        try:
            if data.empty:
                logger.warning(f"Empty data for feature {feature_name}")
                return False
            
            ttl = ttl or self.default_ttl
            
            # Group data by instrument for efficient storage
            if isinstance(data.index, pd.MultiIndex):
                instruments = data.index.get_level_values('instrument').unique()
                
                for instrument in instruments:
                    # Extract data for this instrument
                    instrument_data = data.xs(instrument, level='instrument')
                    
                    # Generate Redis key
                    redis_key = self._generate_key(feature_name, instrument)
                    
                    # Serialize and store
                    serialized_data = self._serialize_data(instrument_data)
                    
                    # Store in Redis with TTL
                    self._redis_client.setex(redis_key, ttl, serialized_data)
                    
                logger.info(f"Saved feature {feature_name} for {len(instruments)} instruments to Redis")
                
                # Save metadata if provided
                if metadata:
                    metadata_key = f"{self.key_prefix}metadata:{feature_name}"
                    metadata_json = json.dumps(metadata, default=str)
                    self._redis_client.setex(metadata_key, ttl, metadata_json)
                
                return True
            else:
                # Single instrument data
                redis_key = self._generate_key(feature_name)
                serialized_data = self._serialize_data(data)
                self._redis_client.setex(redis_key, ttl, serialized_data)
                
                logger.info(f"Saved feature {feature_name} to Redis")
                return True
                
        except Exception as e:
            logger.error(f"Failed to save feature {feature_name} to Redis: {e}")
            return False
    
    def get_feature(self, feature_name: str, instruments: List[str] = None,
                   start_time: Optional[pd.Timestamp] = None,
                   end_time: Optional[pd.Timestamp] = None) -> pd.DataFrame:
        """
        Get feature data from Redis
        
        Args:
            feature_name: Name of the feature
            instruments: List of instruments (optional)
            start_time: Start time filter (optional)
            end_time: End time filter (optional)
            
        Returns:
            pd.DataFrame: Feature data
        """
        try:
            result_data = []
            
            if instruments:
                # Get data for specific instruments
                for instrument in instruments:
                    redis_key = self._generate_key(feature_name, instrument)
                    
                    # Get data from Redis
                    data_bytes = self._redis_client.get(redis_key)
                    if data_bytes:
                        instrument_data = self._deserialize_data(data_bytes)
                        
                        # Apply time filter
                        if start_time or end_time:
                            instrument_data = self._filter_by_time(instrument_data, start_time, end_time)
                        
                        # Add instrument level to index
                        instrument_data.index = pd.MultiIndex.from_product(
                            [[instrument], instrument_data.index],
                            names=['instrument', 'datetime']
                        )
                        
                        result_data.append(instrument_data)
            else:
                # Get all instruments for this feature
                pattern = f"{self.key_prefix}{feature_name}:*"
                keys = self._redis_client.keys(pattern)
                
                for key in keys:
                    key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                    instrument = key_str.split(':')[-1]
                    
                    data_bytes = self._redis_client.get(key)
                    if data_bytes:
                        instrument_data = self._deserialize_data(data_bytes)
                        
                        # Apply time filter
                        if start_time or end_time:
                            instrument_data = self._filter_by_time(instrument_data, start_time, end_time)
                        
                        # Add instrument level to index
                        instrument_data.index = pd.MultiIndex.from_product(
                            [[instrument], instrument_data.index],
                            names=['instrument', 'datetime']
                        )
                        
                        result_data.append(instrument_data)
            
            # Combine all data
            if result_data:
                combined_data = pd.concat(result_data, axis=0)
                logger.info(f"Retrieved feature {feature_name} from Redis: {combined_data.shape}")
                return combined_data
            else:
                logger.warning(f"No data found for feature {feature_name} in Redis")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"Failed to get feature {feature_name} from Redis: {e}")
            return pd.DataFrame()
    
    def _filter_by_time(self, data: pd.DataFrame, start_time: Optional[pd.Timestamp],
                       end_time: Optional[pd.Timestamp]) -> pd.DataFrame:
        """Filter data by time range"""
        if start_time is None and end_time is None:
            return data
        
        try:
            if start_time:
                data = data[data.index >= start_time]
            if end_time:
                data = data[data.index <= end_time]
            return data
        except Exception as e:
            logger.warning(f"Time filtering failed: {e}")
            return data
    
    def list_features(self) -> List[str]:
        """List all available features in Redis"""
        try:
            pattern = f"{self.key_prefix}*"
            keys = self._redis_client.keys(pattern)
            
            features = set()
            for key in keys:
                key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                # Extract feature name (remove prefix and instrument part)
                if ':' in key_str:
                    parts = key_str.replace(self.key_prefix, '').split(':')
                    if len(parts) >= 1 and parts[0] != 'metadata':
                        features.add(parts[0])
            
            feature_list = list(features)
            logger.info(f"Found {len(feature_list)} features in Redis")
            return feature_list
            
        except Exception as e:
            logger.error(f"Failed to list features from Redis: {e}")
            return []
    
    def delete_feature(self, feature_name: str) -> bool:
        """Delete feature data from Redis"""
        try:
            pattern = f"{self.key_prefix}{feature_name}:*"
            keys = self._redis_client.keys(pattern)
            
            # Also delete metadata
            metadata_key = f"{self.key_prefix}metadata:{feature_name}"
            keys.append(metadata_key)
            
            if keys:
                deleted_count = self._redis_client.delete(*keys)
                logger.info(f"Deleted {deleted_count} keys for feature {feature_name}")
                return True
            else:
                logger.warning(f"No keys found for feature {feature_name}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to delete feature {feature_name} from Redis: {e}")
            return False
    
    def get_feature_metadata(self, feature_name: str) -> Dict[str, Any]:
        """Get feature metadata from Redis"""
        try:
            metadata_key = f"{self.key_prefix}metadata:{feature_name}"
            metadata_json = self._redis_client.get(metadata_key)
            
            if metadata_json:
                if isinstance(metadata_json, bytes):
                    metadata_json = metadata_json.decode('utf-8')
                return json.loads(metadata_json)
            else:
                return {}
                
        except Exception as e:
            logger.error(f"Failed to get metadata for feature {feature_name}: {e}")
            return {}
    
    def get_stats(self) -> Dict[str, Any]:
        """Get Redis storage statistics"""
        try:
            info = self._redis_client.info()
            
            # Count feature keys
            pattern = f"{self.key_prefix}*"
            feature_keys = self._redis_client.keys(pattern)
            
            stats = {
                'redis_version': info.get('redis_version', 'unknown'),
                'used_memory_human': info.get('used_memory_human', 'unknown'),
                'connected_clients': info.get('connected_clients', 0),
                'total_feature_keys': len(feature_keys),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'hit_rate': 0
            }
            
            # Calculate hit rate
            hits = stats['keyspace_hits']
            misses = stats['keyspace_misses']
            if hits + misses > 0:
                stats['hit_rate'] = hits / (hits + misses) * 100
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get Redis stats: {e}")
            return {}
    
    def close(self):
        """Close Redis connection"""
        try:
            if self._connection_pool:
                self._connection_pool.disconnect()
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")
