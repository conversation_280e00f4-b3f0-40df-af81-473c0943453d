#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Feature Store Core Abstractions

This module defines the core abstractions and interfaces for the Feature Store system.
"""

import abc
import pandas as pd
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum


class StorageType(Enum):
    """Storage backend types"""
    FILES = "files"
    DATABASE = "database" 
    REDIS = "redis"
    S3 = "s3"
    HDFS = "hdfs"


class FeatureType(Enum):
    """Feature data types"""
    NUMERICAL = "numerical"
    CATEGORICAL = "categorical"
    BOOLEAN = "boolean"
    TEXT = "text"
    EMBEDDING = "embedding"


@dataclass
class FeatureMetadata:
    """Feature metadata structure"""
    name: str
    description: str
    feature_type: FeatureType
    storage_type: StorageType
    version: str = "1.0.0"
    tags: List[str] = None
    owner: str = ""
    created_at: Optional[pd.Timestamp] = None
    updated_at: Optional[pd.Timestamp] = None
    schema: Dict[str, Any] = None
    quality_metrics: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.created_at is None:
            self.created_at = pd.Timestamp.now()
        if self.updated_at is None:
            self.updated_at = pd.Timestamp.now()
        if self.schema is None:
            self.schema = {}
        if self.quality_metrics is None:
            self.quality_metrics = {}


class FeatureStoreInterface(abc.ABC):
    """
    Abstract interface for Feature Store implementations
    
    This defines the contract that all Feature Store implementations must follow.
    """
    
    @abc.abstractmethod
    def register_feature(self, metadata: FeatureMetadata) -> bool:
        """Register a new feature with metadata"""
        pass
    
    @abc.abstractmethod
    def get_features(self, 
                    feature_names: List[str],
                    instruments: List[str],
                    start_time: Optional[pd.Timestamp] = None,
                    end_time: Optional[pd.Timestamp] = None,
                    **kwargs) -> pd.DataFrame:
        """Retrieve features for given instruments and time range"""
        pass
    
    @abc.abstractmethod
    def save_features(self, 
                     feature_name: str,
                     data: pd.DataFrame,
                     metadata: Optional[FeatureMetadata] = None) -> bool:
        """Save feature data to the store"""
        pass
    
    @abc.abstractmethod
    def delete_feature(self, feature_name: str) -> bool:
        """Delete a feature from the store"""
        pass
    
    @abc.abstractmethod
    def list_features(self, tags: Optional[List[str]] = None) -> List[str]:
        """List available features, optionally filtered by tags"""
        pass
    
    @abc.abstractmethod
    def feature_exists(self, feature_name: str) -> bool:
        """Check if a feature exists in the store"""
        pass
    
    @abc.abstractmethod
    def get_feature_metadata(self, feature_name: str) -> Optional[FeatureMetadata]:
        """Get metadata for a specific feature"""
        pass


class StorageBackendInterface(abc.ABC):
    """
    Abstract interface for storage backends
    """
    
    @abc.abstractmethod
    def read(self, 
            feature_name: str,
            instruments: List[str],
            start_time: Optional[pd.Timestamp] = None,
            end_time: Optional[pd.Timestamp] = None,
            **kwargs) -> pd.DataFrame:
        """Read feature data from storage"""
        pass
    
    @abc.abstractmethod
    def write(self, 
             feature_name: str,
             data: pd.DataFrame,
             **kwargs) -> bool:
        """Write feature data to storage"""
        pass
    
    @abc.abstractmethod
    def delete(self, feature_name: str) -> bool:
        """Delete feature data from storage"""
        pass
    
    @abc.abstractmethod
    def exists(self, feature_name: str) -> bool:
        """Check if feature data exists in storage"""
        pass


class MetadataStoreInterface(abc.ABC):
    """
    Abstract interface for metadata storage
    """
    
    @abc.abstractmethod
    def save_metadata(self, metadata: FeatureMetadata) -> bool:
        """Save feature metadata"""
        pass
    
    @abc.abstractmethod
    def get_metadata(self, feature_name: str) -> Optional[FeatureMetadata]:
        """Get feature metadata"""
        pass
    
    @abc.abstractmethod
    def list_features(self, tags: Optional[List[str]] = None) -> List[FeatureMetadata]:
        """List all features with optional tag filtering"""
        pass
    
    @abc.abstractmethod
    def delete_metadata(self, feature_name: str) -> bool:
        """Delete feature metadata"""
        pass
    
    @abc.abstractmethod
    def update_metadata(self, feature_name: str, updates: Dict[str, Any]) -> bool:
        """Update feature metadata"""
        pass


class CacheInterface(abc.ABC):
    """
    Abstract interface for caching layer
    """
    
    @abc.abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """Get data from cache"""
        pass
    
    @abc.abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set data in cache with optional TTL"""
        pass
    
    @abc.abstractmethod
    def delete(self, key: str) -> bool:
        """Delete data from cache"""
        pass
    
    @abc.abstractmethod
    def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern, return count deleted"""
        pass
    
    @abc.abstractmethod
    def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        pass


class DataQualityInterface(abc.ABC):
    """
    Abstract interface for data quality checking
    """
    
    @abc.abstractmethod
    def validate_data(self, 
                     feature_name: str,
                     data: pd.DataFrame,
                     metadata: FeatureMetadata) -> Dict[str, Any]:
        """Validate feature data quality"""
        pass
    
    @abc.abstractmethod
    def compute_statistics(self, 
                          feature_name: str,
                          data: pd.DataFrame) -> Dict[str, Any]:
        """Compute data quality statistics"""
        pass
    
    @abc.abstractmethod
    def detect_drift(self, 
                    feature_name: str,
                    new_data: pd.DataFrame,
                    reference_data: pd.DataFrame) -> Dict[str, Any]:
        """Detect data drift between new and reference data"""
        pass


class FeatureComputeInterface(abc.ABC):
    """
    Abstract interface for feature computation
    """
    
    @abc.abstractmethod
    def compute_feature(self, 
                       feature_name: str,
                       computation_config: Dict[str, Any],
                       input_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Compute a feature from input data"""
        pass
    
    @abc.abstractmethod
    def register_computation(self, 
                           feature_name: str,
                           computation_config: Dict[str, Any]) -> bool:
        """Register a feature computation pipeline"""
        pass
    
    @abc.abstractmethod
    def get_dependencies(self, feature_name: str) -> List[str]:
        """Get feature dependencies for computation"""
        pass
