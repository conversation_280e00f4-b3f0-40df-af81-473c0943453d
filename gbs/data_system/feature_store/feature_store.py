#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GBS Feature Store Core Implementation

This module provides the main Feature Store class that integrates with
the existing GBS data system architecture.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Union
from pathlib import Path

from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.utils.mod import init_instance_by_config
from gbs.data_system.base.cache import H, hash_args

logger = get_loki_logger(__name__).logger


class MetadataManager:
    """
    轻量级元数据管理器
    """

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self._metadata_cache = {}

    def register_feature(self, feature_name: str, metadata: Dict[str, Any]):
        """注册特征元数据"""
        self._metadata_cache[feature_name] = {
            **metadata,
            'created_at': pd.Timestamp.now().isoformat(),
            'updated_at': pd.Timestamp.now().isoformat()
        }

    def feature_exists(self, feature_name: str) -> bool:
        """检查特征是否存在"""
        return feature_name in self._metadata_cache

    def get_feature_metadata(self, feature_name: str) -> Optional[Dict[str, Any]]:
        """获取特征元数据"""
        return self._metadata_cache.get(feature_name)

    def list_all_features(self) -> List[str]:
        """列出所有特征"""
        return list(self._metadata_cache.keys())

    def delete_feature(self, feature_name: str):
        """删除特征元数据"""
        if feature_name in self._metadata_cache:
            del self._metadata_cache[feature_name]


class CacheManager:
    """
    缓存管理器，复用现有的 H 缓存系统
    """

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}

    def get(self, key: str) -> Optional[Any]:
        """从缓存获取数据"""
        return H["f"].get(key)

    def set(self, key: str, value: Any, ttl: int = 3600):
        """设置缓存数据"""
        H["f"][key] = value

    def delete_pattern(self, pattern: str):
        """删除匹配模式的缓存"""
        keys_to_delete = [k for k in H["f"].keys() if pattern.replace('*', '') in k]
        for key in keys_to_delete:
            del H["f"][key]


class GBSFeatureStore:
    """
    GBS Feature Store - Self-built feature storage and management system

    This class provides:
    1. Feature storage and retrieval
    2. Metadata management
    3. Intelligent caching
    4. Integration with existing GBS Storage backends
    """

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize GBS Feature Store

        Args:
            config: Feature Store configuration
                {
                    'cache_ttl': 3600,   # Default cache TTL in seconds
                    'batch_size': 1000,  # Batch processing size
                    'files': {...}       # File storage configuration
                }
        """
        self.config = config or {}

        # Initialize core components
        self.metadata_manager = MetadataManager(self.config.get('database', {}))
        self.cache_manager = CacheManager(self.config.get('cache', {}))

        # Configuration
        self.default_cache_ttl = self.config.get('cache_ttl', 3600)
        self.batch_size = self.config.get('batch_size', 1000)

        # Storage configuration
        self.storage_config = self.config

        # Initialize Redis storage if configured
        self._redis_storage = None
        if self.config.get('redis'):
            self._initialize_redis_storage()
            # Auto-discover and register Redis features
            self._auto_register_redis_features()

        logger.info("GBS Feature Store initialized successfully")

    def _initialize_redis_storage(self):
        """Initialize Redis storage"""
        try:
            from .redis_storage import RedisFeatureStorage

            redis_config = self.config.get('redis', {})
            self._redis_storage = RedisFeatureStorage(redis_config)

            logger.info("Redis storage initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Redis storage: {e}")
            self._redis_storage = None

    def _auto_register_redis_features(self):
        """Auto-discover and register features from Redis"""
        if not self._redis_storage:
            return

        try:
            # Get all features from Redis
            redis_features = self._redis_storage.list_features()
            logger.info(f"Found {len(redis_features)} features in Redis")

            # Register each feature with metadata
            for feature_name in redis_features:
                if not self.metadata_manager.feature_exists(feature_name):
                    # Create metadata for the feature
                    metadata = {
                        'name': feature_name,
                        'description': f'Auto-discovered feature from Redis: {feature_name}',
                        'storage_type': 'redis',
                        'data_type': 'numerical',
                        'source': 'redis_auto_discovery',
                        'tags': ['redis', 'auto_discovered']
                    }

                    # Add JKP-specific metadata if it's a JKP feature
                    if feature_name.startswith('jkp_'):
                        metadata.update({
                            'category': 'jkp_factor',
                            'factor_type': feature_name.replace('jkp_', ''),
                            'tags': ['jkp', 'redis', 'auto_discovered']
                        })

                    # Register the feature
                    self.metadata_manager.register_feature(feature_name, metadata)
                    logger.info(f"Auto-registered Redis feature: {feature_name}")

            logger.info(f"Auto-registered {len(redis_features)} features from Redis")

        except Exception as e:
            logger.warning(f"Failed to auto-register Redis features: {e}")

    def get_features(self,
                    instruments: List[str],
                    feature_names: List[str],
                    start_time: Optional[pd.Timestamp] = None,
                    end_time: Optional[pd.Timestamp] = None,
                    freq: str = 'day') -> pd.DataFrame:
        """
        Get features from Feature Store

        Args:
            instruments: List of instrument IDs
            feature_names: List of feature names to retrieve
            start_time: Start time for data retrieval
            end_time: End time for data retrieval
            freq: Data frequency

        Returns:
            pd.DataFrame: Feature data with MultiIndex(instrument, datetime)
        """
        logger.debug(f"Getting features: {feature_names} for instruments: {instruments}")

        results = []

        for feature_name in feature_names:
            # Check if feature exists
            if not self.metadata_manager.feature_exists(feature_name):
                logger.warning(f"Feature {feature_name} not found in Feature Store")
                continue

            # Get feature data
            feature_data = self._get_single_feature(
                feature_name, instruments, start_time, end_time, freq
            )

            if feature_data is not None and not feature_data.empty:
                results.append(feature_data)

        # Merge results
        if results:
            merged_data = pd.concat(results, axis=1)
            return self._ensure_gbs_format(merged_data)
        else:
            return pd.DataFrame()

    def save_features(self,
                     feature_name: str,
                     data: pd.DataFrame,
                     metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Save features to Feature Store

        Args:
            feature_name: Name of the feature
            data: Feature data (must have MultiIndex: instrument, datetime)
            metadata: Optional metadata for the feature

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Saving feature: {feature_name}")

            # Validate data format
            if not self._validate_data_format(data):
                raise ValueError("Data must have MultiIndex(instrument, datetime)")

            # Register feature metadata
            if metadata:
                # 如果 metadata 是 FeatureMetadata 对象，转换为字典
                if hasattr(metadata, '__dict__'):
                    metadata_dict = metadata.__dict__
                else:
                    metadata_dict = metadata
                self.metadata_manager.register_feature(feature_name, metadata_dict)

            # Save feature data
            self._save_feature_data(feature_name, data)

            # Update cache
            self._update_feature_cache(feature_name, data)

            logger.info(f"Feature {feature_name} saved successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to save feature {feature_name}: {e}")
            return False

    def _get_single_feature(self,
                           feature_name: str,
                           instruments: List[str],
                           start_time: Optional[pd.Timestamp],
                           end_time: Optional[pd.Timestamp],
                           freq: str) -> Optional[pd.DataFrame]:
        """
        Get single feature data with caching
        """
        # Build cache key
        cache_key = self._build_cache_key(
            feature_name, instruments, start_time, end_time, freq
        )

        # Try to get from cache
        cached_data = self.cache_manager.get(cache_key)
        if cached_data is not None:
            logger.debug(f"Cache hit for feature {feature_name}")
            return cached_data

        # Load from persistent storage
        feature_data = self._load_from_persistent_storage(
            feature_name, instruments, start_time, end_time, freq
        )

        # Update cache
        if feature_data is not None and not feature_data.empty:
            self.cache_manager.set(cache_key, feature_data, ttl=self.default_cache_ttl)

        return feature_data

    def _build_cache_key(self,
                        feature_name: str,
                        instruments: List[str],
                        start_time: Optional[pd.Timestamp],
                        end_time: Optional[pd.Timestamp],
                        freq: str) -> str:
        """
        Build cache key for feature data
        """
        instruments_str = ','.join(sorted(instruments))
        start_str = start_time.strftime('%Y%m%d') if start_time else 'None'
        end_str = end_time.strftime('%Y%m%d') if end_time else 'None'

        return f"feature:{feature_name}:{hash(instruments_str)}:{start_str}:{end_str}:{freq}"

    def _validate_data_format(self, data: pd.DataFrame) -> bool:
        """
        Validate that data has the correct GBS format
        """
        if not isinstance(data, pd.DataFrame):
            return False

        if not isinstance(data.index, pd.MultiIndex):
            return False

        if data.index.names != ['instrument', 'datetime']:
            return False

        return True

    def _ensure_gbs_format(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Ensure data is in GBS format (MultiIndex: instrument, datetime)
        """
        if not isinstance(data.index, pd.MultiIndex):
            raise ValueError("Data must have MultiIndex")

        if data.index.names != ['instrument', 'datetime']:
            raise ValueError("Index must be ['instrument', 'datetime']")

        return data.sort_index()

    def list_features(self) -> List[str]:
        """
        List all available features (from Redis and metadata)
        """
        features = set()

        # Get features from metadata
        metadata_features = self.metadata_manager.list_all_features()
        features.update(metadata_features)

        # Get features from Redis if available
        if self._redis_storage:
            try:
                redis_features = self._redis_storage.list_features()
                features.update(redis_features)
            except Exception as e:
                logger.warning(f"Failed to list features from Redis: {e}")

        return list(features)

    def feature_exists(self, feature_name: str) -> bool:
        """
        Check if feature exists in Feature Store
        """
        return self.metadata_manager.feature_exists(feature_name)

    def get_feature_metadata(self, feature_name: str) -> Optional[Dict[str, Any]]:
        """
        Get metadata for a specific feature
        """
        return self.metadata_manager.get_feature_metadata(feature_name)

    def delete_feature(self, feature_name: str) -> bool:
        """
        Delete a feature from Feature Store
        """
        try:
            # Delete from persistent storage
            self._delete_from_persistent_storage(feature_name)

            # Delete from cache
            self.cache_manager.delete_pattern(f"feature:{feature_name}:*")

            # Delete metadata
            self.metadata_manager.delete_feature(feature_name)

            logger.info(f"Feature {feature_name} deleted successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to delete feature {feature_name}: {e}")
            return False

    def _save_feature_data(self, feature_name: str, data: pd.DataFrame):
        """
        Save feature data to persistent storage (Redis first, then files)
        """
        # Try Redis first if available
        if self._redis_storage:
            try:
                success = self._redis_storage.save_feature(feature_name, data)
                if success:
                    logger.info(f"Feature {feature_name} saved to Redis")
                    return
                else:
                    logger.warning(f"Failed to save {feature_name} to Redis, falling back to files")
            except Exception as e:
                logger.warning(f"Redis save failed for {feature_name}: {e}, falling back to files")

        # Fallback to files
        self._save_to_files(feature_name, data)

    def _load_from_persistent_storage(self,
                                    feature_name: str,
                                    instruments: List[str],
                                    start_time: Optional[pd.Timestamp],
                                    end_time: Optional[pd.Timestamp],
                                    freq: str) -> Optional[pd.DataFrame]:
        """
        Load feature data from persistent storage (Redis first, then files)
        """
        # Try Redis first if available
        if self._redis_storage:
            try:
                redis_data = self._redis_storage.get_feature(
                    feature_name=feature_name,
                    instruments=instruments,
                    start_time=start_time,
                    end_time=end_time
                )
                if not redis_data.empty:
                    logger.info(f"Feature {feature_name} loaded from Redis: {redis_data.shape}")
                    return redis_data
                else:
                    logger.debug(f"Feature {feature_name} not found in Redis, trying files")
            except Exception as e:
                logger.warning(f"Redis load failed for {feature_name}: {e}, trying files")

        # Fallback to files
        return self._load_from_files(feature_name, instruments, start_time, end_time, freq)

    def _determine_storage_type(self, feature_name: str, data: pd.DataFrame) -> str:
        """
        Determine the best storage type for the feature
        """
        # Simple heuristic: use files for large data, database for small data
        data_size = data.memory_usage(deep=True).sum()

        if data_size > 10 * 1024 * 1024:  # > 10MB
            return 'files'
        else:
            return 'database'

    def _save_to_files(self, feature_name: str, data: pd.DataFrame):
        """
        Save feature data to files using existing FileFeatureStorage
        """

        # Create feature store directory
        base_path = self.config.get('files', {}).get('base_path', 'gbs_data/feature_store')
        feature_dir = Path(base_path) / feature_name
        feature_dir.mkdir(parents=True, exist_ok=True)

        # Save by instrument
        for instrument in data.index.get_level_values('instrument').unique():
            instrument_data = data.xs(instrument, level='instrument')

            # Use existing FileFeatureStorage directly
            from gbs.data_system.base.storage.file_storage import FileFeatureStorage

            storage = FileFeatureStorage(
                instrument=instrument,
                field=feature_name,
                freq='day',
                provider_uri={'day': str(feature_dir)}
            )

            # Save data using the correct method
            data_to_save = instrument_data[feature_name] if feature_name in instrument_data.columns else instrument_data.iloc[:, 0]
            storage.write(data_to_save.values)

    def _load_from_files(self,
                        feature_name: str,
                        instruments: List[str],
                        start_time: Optional[pd.Timestamp],
                        end_time: Optional[pd.Timestamp],
                        freq: str) -> Optional[pd.DataFrame]:
        """
        Load feature data from files
        """
        from gbs.core.utils.mod import init_instance_by_config

        base_path = self.config.get('files', {}).get('base_path', 'gbs_data/feature_store')
        feature_dir = Path(base_path) / feature_name

        if not feature_dir.exists():
            return None

        results = []

        for instrument in instruments:
            file_path = feature_dir / f'{instrument}.day.bin'
            if not file_path.exists():
                continue

            try:
                storage_config = {
                    'class': 'FileFeatureStorage',
                    'module_path': 'gbs.data_system.base.storage.file_storage',
                    'uri': str(file_path)
                }

                storage = init_instance_by_config(
                    storage_config,
                    instrument=instrument,
                    field=feature_name,
                    freq=freq
                )

                data = storage.data
                if data is not None and len(data) > 0:
                    # Apply time filter
                    if start_time or end_time:
                        data = data.loc[start_time:end_time]

                    # Convert to DataFrame with MultiIndex
                    df = data.to_frame(feature_name)
                    df.index = pd.MultiIndex.from_product(
                        [[instrument], df.index],
                        names=['instrument', 'datetime']
                    )
                    results.append(df)

            except Exception as e:
                logger.warning(f"Failed to load {feature_name} for {instrument}: {e}")
                continue

        return pd.concat(results, axis=0) if results else None

    def _update_feature_cache(self, feature_name: str, data: pd.DataFrame):
        """
        Update cache with new feature data
        """
        # Cache by instrument for efficient retrieval
        for instrument in data.index.get_level_values('instrument').unique():
            instrument_data = data.xs(instrument, level='instrument')

            cache_key = f"feature:{feature_name}:{instrument}:full"
            self.cache_manager.set(cache_key, instrument_data, ttl=self.default_cache_ttl)

    def _delete_from_persistent_storage(self, feature_name: str):
        """
        Delete feature data from persistent storage
        """
        # Delete from files
        base_path = self.config.get('files', {}).get('base_path', 'gbs_data/feature_store')
        feature_dir = Path(base_path) / feature_name

        if feature_dir.exists():
            import shutil
            shutil.rmtree(feature_dir)

        # Delete from database (if implemented)
        # self._delete_from_database(feature_name)
