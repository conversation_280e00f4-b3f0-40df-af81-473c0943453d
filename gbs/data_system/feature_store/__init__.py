#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GBS Feature Store Module

This module provides a comprehensive feature store solution for the GBS system.
It includes metadata management, storage routing, feature serving, and data quality monitoring.
"""

# Core abstractions
from .core import (
    FeatureStoreInterface,
    StorageBackendInterface,
    MetadataStoreInterface,
    CacheInterface,
    DataQualityInterface,
    FeatureComputeInterface,
    FeatureMetadata,
    FeatureType,
    StorageType
)

# Implementations
from .feature_store import GBSFeatureStore, MetadataManager, CacheManager
from .routing import StorageRouter
from .quality import DataQualityChecker
from .jkp_migrator import JKPDataMigrator

__all__ = [
    # Core interfaces
    'FeatureStoreInterface',
    'StorageBackendInterface',
    'MetadataStoreInterface',
    'CacheInterface',
    'DataQualityInterface',
    'FeatureComputeInterface',

    # Data structures
    'FeatureMetadata',
    'FeatureType',
    'StorageType',

    # Implementations
    'GBSFeatureStore',
    'MetadataManager',
    'CacheManager',
    'StorageRouter',
    'DataQualityChecker',

    # JKP specific tools
    'JKPDataMigrator',
]
