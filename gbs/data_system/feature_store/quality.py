#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Data Quality Management for Feature Store

This module provides comprehensive data quality checking, monitoring, and drift detection.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from .core import FeatureMetadata, FeatureType, DataQualityInterface
from gbs.core.utils.loki_logger import get_loki_logger

logger = get_loki_logger(__name__).logger


class DataQualityChecker(DataQualityInterface):
    """
    Comprehensive data quality checker for Feature Store
    
    Provides:
    - Data validation against schema
    - Statistical quality metrics
    - Data drift detection
    - Anomaly detection
    - Quality scoring
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize data quality checker
        
        Args:
            config: Quality checker configuration
                {
                    'drift_threshold': 0.1,
                    'anomaly_threshold': 3.0,
                    'missing_data_threshold': 0.05,
                    'enable_statistical_tests': True
                }
        """
        self.config = config or {}
        self.drift_threshold = self.config.get('drift_threshold', 0.1)
        self.anomaly_threshold = self.config.get('anomaly_threshold', 3.0)
        self.missing_data_threshold = self.config.get('missing_data_threshold', 0.05)
        self.enable_statistical_tests = self.config.get('enable_statistical_tests', True)
        
        # Store reference statistics for drift detection
        self._reference_stats = {}
    
    def validate_data(self, 
                     feature_name: str,
                     data: pd.DataFrame,
                     metadata: FeatureMetadata) -> Dict[str, Any]:
        """
        Comprehensive data validation
        
        Returns:
            Dict with validation results:
            {
                'is_valid': bool,
                'errors': List[str],
                'warnings': List[str],
                'quality_score': float,
                'statistics': Dict[str, Any]
            }
        """
        errors = []
        warnings = []
        
        # 1. Schema validation
        schema_errors = self._validate_schema(data, metadata)
        errors.extend(schema_errors)
        
        # 2. Data type validation
        type_errors = self._validate_data_types(data, metadata)
        errors.extend(type_errors)
        
        # 3. Missing data check
        missing_warnings = self._check_missing_data(data)
        warnings.extend(missing_warnings)
        
        # 4. Anomaly detection
        anomaly_warnings = self._detect_anomalies(data, metadata)
        warnings.extend(anomaly_warnings)
        
        # 5. Business logic validation
        business_errors = self._validate_business_rules(data, metadata)
        errors.extend(business_errors)
        
        # 6. Compute quality statistics
        statistics = self.compute_statistics(feature_name, data)
        
        # 7. Calculate quality score
        quality_score = self._calculate_quality_score(data, errors, warnings, statistics)
        
        result = {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings,
            'quality_score': quality_score,
            'statistics': statistics
        }
        
        logger.info(f"Data validation for {feature_name}: "
                   f"Valid={result['is_valid']}, Score={quality_score:.3f}")
        
        return result
    
    def compute_statistics(self, 
                          feature_name: str,
                          data: pd.DataFrame) -> Dict[str, Any]:
        """
        Compute comprehensive data statistics
        """
        stats = {
            'row_count': len(data),
            'column_count': len(data.columns),
            'missing_count': data.isnull().sum().sum(),
            'missing_percentage': (data.isnull().sum().sum() / data.size) * 100,
            'duplicate_count': data.duplicated().sum(),
            'memory_usage_mb': data.memory_usage(deep=True).sum() / (1024 * 1024)
        }
        
        # Per-column statistics
        column_stats = {}
        for col in data.columns:
            if pd.api.types.is_numeric_dtype(data[col]):
                column_stats[col] = self._compute_numeric_stats(data[col])
            else:
                column_stats[col] = self._compute_categorical_stats(data[col])
        
        stats['columns'] = column_stats
        
        # Time series specific statistics if datetime index
        if isinstance(data.index, pd.DatetimeIndex):
            stats['time_series'] = self._compute_time_series_stats(data)
        
        return stats
    
    def detect_drift(self, 
                    feature_name: str,
                    new_data: pd.DataFrame,
                    reference_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Detect data drift between new and reference data
        """
        drift_results = {
            'has_drift': False,
            'drift_score': 0.0,
            'column_drifts': {},
            'statistical_tests': {}
        }
        
        # Store reference statistics
        ref_stats = self.compute_statistics(f"{feature_name}_reference", reference_data)
        self._reference_stats[feature_name] = ref_stats
        
        # Compute new data statistics
        new_stats = self.compute_statistics(f"{feature_name}_new", new_data)
        
        # Compare statistics for each column
        total_drift_score = 0.0
        column_count = 0
        
        for col in new_data.columns:
            if col in reference_data.columns:
                column_drift = self._detect_column_drift(
                    new_data[col], reference_data[col], col
                )
                drift_results['column_drifts'][col] = column_drift
                total_drift_score += column_drift['drift_score']
                column_count += 1
        
        # Overall drift score
        if column_count > 0:
            drift_results['drift_score'] = total_drift_score / column_count
            drift_results['has_drift'] = drift_results['drift_score'] > self.drift_threshold
        
        # Statistical tests
        if self.enable_statistical_tests:
            drift_results['statistical_tests'] = self._run_statistical_tests(
                new_data, reference_data
            )
        
        logger.info(f"Drift detection for {feature_name}: "
                   f"Has drift={drift_results['has_drift']}, "
                   f"Score={drift_results['drift_score']:.3f}")
        
        return drift_results
    
    def _validate_schema(self, data: pd.DataFrame, metadata: FeatureMetadata) -> List[str]:
        """Validate data against schema"""
        errors = []
        
        if not metadata.schema:
            return errors
        
        # Check required columns
        required_columns = metadata.schema.get('required_columns', [])
        missing_columns = set(required_columns) - set(data.columns)
        if missing_columns:
            errors.append(f"Missing required columns: {missing_columns}")
        
        # Check column types
        column_types = metadata.schema.get('column_types', {})
        for col, expected_type in column_types.items():
            if col in data.columns:
                actual_type = str(data[col].dtype)
                if not self._is_compatible_type(actual_type, expected_type):
                    errors.append(f"Column {col} has type {actual_type}, expected {expected_type}")
        
        return errors
    
    def _validate_data_types(self, data: pd.DataFrame, metadata: FeatureMetadata) -> List[str]:
        """Validate data types"""
        errors = []
        
        # Check for mixed types in columns
        for col in data.columns:
            if data[col].dtype == 'object':
                # Check if column has mixed types
                types = data[col].apply(type).unique()
                if len(types) > 1:
                    errors.append(f"Column {col} has mixed types: {types}")
        
        return errors
    
    def _check_missing_data(self, data: pd.DataFrame) -> List[str]:
        """Check for excessive missing data"""
        warnings = []
        
        for col in data.columns:
            missing_pct = data[col].isnull().sum() / len(data)
            if missing_pct > self.missing_data_threshold:
                warnings.append(f"Column {col} has {missing_pct:.1%} missing data")
        
        return warnings
    
    def _detect_anomalies(self, data: pd.DataFrame, metadata: FeatureMetadata) -> List[str]:
        """Detect anomalies in data"""
        warnings = []
        
        for col in data.columns:
            if pd.api.types.is_numeric_dtype(data[col]):
                # Z-score based anomaly detection
                z_scores = np.abs((data[col] - data[col].mean()) / data[col].std())
                anomaly_count = (z_scores > self.anomaly_threshold).sum()
                
                if anomaly_count > 0:
                    anomaly_pct = anomaly_count / len(data)
                    warnings.append(f"Column {col} has {anomaly_count} ({anomaly_pct:.1%}) anomalies")
        
        return warnings
    
    def _validate_business_rules(self, data: pd.DataFrame, metadata: FeatureMetadata) -> List[str]:
        """Validate business-specific rules"""
        errors = []
        
        # Example business rules - can be extended
        for col in data.columns:
            if pd.api.types.is_numeric_dtype(data[col]):
                # Check for negative values where not expected
                if col.lower() in ['price', 'volume', 'amount'] and (data[col] < 0).any():
                    errors.append(f"Column {col} contains negative values")
                
                # Check for extreme values
                if data[col].max() > 1e10:
                    errors.append(f"Column {col} contains extremely large values")
        
        return errors
    
    def _compute_numeric_stats(self, series: pd.Series) -> Dict[str, Any]:
        """Compute statistics for numeric columns"""
        return {
            'count': series.count(),
            'mean': series.mean(),
            'std': series.std(),
            'min': series.min(),
            'max': series.max(),
            'median': series.median(),
            'q25': series.quantile(0.25),
            'q75': series.quantile(0.75),
            'skewness': series.skew(),
            'kurtosis': series.kurtosis(),
            'zeros_count': (series == 0).sum(),
            'negative_count': (series < 0).sum()
        }
    
    def _compute_categorical_stats(self, series: pd.Series) -> Dict[str, Any]:
        """Compute statistics for categorical columns"""
        return {
            'count': series.count(),
            'unique_count': series.nunique(),
            'most_frequent': series.mode().iloc[0] if not series.mode().empty else None,
            'most_frequent_count': series.value_counts().iloc[0] if not series.empty else 0,
            'value_counts': series.value_counts().head(10).to_dict()
        }
    
    def _compute_time_series_stats(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Compute time series specific statistics"""
        return {
            'start_date': data.index.min(),
            'end_date': data.index.max(),
            'frequency': pd.infer_freq(data.index),
            'gaps_count': len(pd.date_range(data.index.min(), data.index.max(), 
                                          freq=pd.infer_freq(data.index) or 'D')) - len(data),
            'duplicate_timestamps': data.index.duplicated().sum()
        }
    
    def _detect_column_drift(self, 
                           new_series: pd.Series,
                           ref_series: pd.Series,
                           column_name: str) -> Dict[str, Any]:
        """Detect drift for a single column"""
        drift_info = {
            'drift_score': 0.0,
            'has_drift': False,
            'drift_type': None,
            'details': {}
        }
        
        if pd.api.types.is_numeric_dtype(new_series):
            # Numeric drift detection
            drift_info = self._detect_numeric_drift(new_series, ref_series)
        else:
            # Categorical drift detection
            drift_info = self._detect_categorical_drift(new_series, ref_series)
        
        drift_info['has_drift'] = drift_info['drift_score'] > self.drift_threshold
        
        return drift_info
    
    def _detect_numeric_drift(self, new_series: pd.Series, ref_series: pd.Series) -> Dict[str, Any]:
        """Detect drift in numeric data"""
        # Statistical distance measures
        mean_diff = abs(new_series.mean() - ref_series.mean()) / ref_series.std()
        std_ratio = new_series.std() / ref_series.std()
        
        # Combine into drift score
        drift_score = (mean_diff + abs(np.log(std_ratio))) / 2
        
        return {
            'drift_score': drift_score,
            'drift_type': 'numeric',
            'details': {
                'mean_difference': mean_diff,
                'std_ratio': std_ratio,
                'new_mean': new_series.mean(),
                'ref_mean': ref_series.mean(),
                'new_std': new_series.std(),
                'ref_std': ref_series.std()
            }
        }
    
    def _detect_categorical_drift(self, new_series: pd.Series, ref_series: pd.Series) -> Dict[str, Any]:
        """Detect drift in categorical data"""
        # Compare value distributions
        new_counts = new_series.value_counts(normalize=True)
        ref_counts = ref_series.value_counts(normalize=True)
        
        # Calculate Jensen-Shannon divergence
        all_values = set(new_counts.index) | set(ref_counts.index)
        new_probs = [new_counts.get(v, 0) for v in all_values]
        ref_probs = [ref_counts.get(v, 0) for v in all_values]
        
        # Simple drift score based on distribution differences
        drift_score = sum(abs(n - r) for n, r in zip(new_probs, ref_probs)) / 2
        
        return {
            'drift_score': drift_score,
            'drift_type': 'categorical',
            'details': {
                'new_unique_count': new_series.nunique(),
                'ref_unique_count': ref_series.nunique(),
                'new_top_values': new_counts.head(5).to_dict(),
                'ref_top_values': ref_counts.head(5).to_dict()
            }
        }
    
    def _run_statistical_tests(self, 
                              new_data: pd.DataFrame,
                              ref_data: pd.DataFrame) -> Dict[str, Any]:
        """Run statistical tests for drift detection"""
        # Placeholder for statistical tests
        # Can implement KS test, Chi-square test, etc.
        return {
            'ks_test': 'not_implemented',
            'chi_square_test': 'not_implemented',
            'mann_whitney_test': 'not_implemented'
        }
    
    def _calculate_quality_score(self, 
                                data: pd.DataFrame,
                                errors: List[str],
                                warnings: List[str],
                                statistics: Dict[str, Any]) -> float:
        """Calculate overall data quality score (0-1)"""
        score = 1.0
        
        # Penalize errors heavily
        score -= len(errors) * 0.2
        
        # Penalize warnings moderately
        score -= len(warnings) * 0.05
        
        # Penalize missing data
        missing_pct = statistics.get('missing_percentage', 0) / 100
        score -= missing_pct * 0.3
        
        # Penalize duplicates
        if statistics.get('row_count', 0) > 0:
            duplicate_pct = statistics.get('duplicate_count', 0) / statistics['row_count']
            score -= duplicate_pct * 0.1
        
        return max(0.0, min(1.0, score))
    
    def _is_compatible_type(self, actual_type: str, expected_type: str) -> bool:
        """Check if actual type is compatible with expected type"""
        # Simple type compatibility check
        type_mappings = {
            'int64': ['int', 'integer', 'numeric'],
            'float64': ['float', 'numeric', 'decimal'],
            'object': ['string', 'text', 'categorical'],
            'bool': ['boolean', 'bool'],
            'datetime64[ns]': ['datetime', 'timestamp']
        }
        
        compatible_types = type_mappings.get(actual_type, [])
        return expected_type.lower() in compatible_types
