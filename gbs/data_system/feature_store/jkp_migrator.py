#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
JKP Data Migration Tool

This module provides tools to migrate JKP data from StaticDataLoader to Feature Store.
"""

import os
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Any
from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.utils.serial import Serializable
from .core import FeatureMetadata, FeatureType, StorageType
from .feature_store import GBSFeatureStore

logger = get_loki_logger(__name__).logger


class JKPDataMigrator:
    """
    JKP Data Migration Tool
    
    Migrates JKP data from pickle files to Feature Store with proper metadata management.
    """
    
    def __init__(self, feature_store: GBSFeatureStore, config: Dict[str, Any] = None):
        """
        Initialize JKP Data Migrator
        
        Args:
            feature_store: Target Feature Store instance
            config: Migration configuration
                {
                    'jkp_data_path': 'path/to/full_usa.pkl',
                    'batch_size': 1000,
                    'exclude_columns': ['eom', 'ticker', 'ret_exc_lead1m'],
                    'feature_prefix': 'jkp_',
                    'storage_type': 'files',
                    'validate_data': True
                }
        """
        self.feature_store = feature_store
        self.config = config or {}
        
        # Configuration
        self.jkp_data_path = self.config.get('jkp_data_path', 'gbs/data/processed_data/jkp/full_usa.pkl')
        self.batch_size = self.config.get('batch_size', 1000)
        self.exclude_columns = self.config.get('exclude_columns', ['eom', 'ticker', 'ret_exc_lead1m'])
        self.feature_prefix = self.config.get('feature_prefix', 'jkp_')
        self.storage_type = StorageType(self.config.get('storage_type', 'files'))
        self.validate_data = self.config.get('validate_data', True)
        
        # Migration state
        self._migration_stats = {
            'total_features': 0,
            'migrated_features': 0,
            'failed_features': 0,
            'total_data_points': 0
        }
    
    def migrate_jkp_data(self) -> Dict[str, Any]:
        """
        Migrate all JKP data to Feature Store
        
        Returns:
            Dict with migration results
        """
        logger.info("开始 JKP 数据迁移到 Feature Store")
        
        try:
            # 1. Load JKP data
            jkp_data = self._load_jkp_data()
            if jkp_data.empty:
                raise ValueError("JKP 数据为空")
            
            logger.info(f"JKP 数据加载成功: {jkp_data.shape}")
            
            # 2. Analyze data structure
            data_info = self._analyze_data_structure(jkp_data)
            logger.info(f"数据分析完成: {data_info}")
            
            # 3. Prepare features for migration
            features_to_migrate = self._prepare_features(jkp_data)
            logger.info(f"准备迁移 {len(features_to_migrate)} 个特征")
            
            # 4. Migrate features
            migration_results = self._migrate_features(features_to_migrate, jkp_data)
            
            # 5. Generate migration report
            report = self._generate_migration_report(migration_results)
            
            logger.info("JKP 数据迁移完成")
            return report
            
        except Exception as e:
            logger.error(f"JKP 数据迁移失败: {e}")
            raise
    
    def _load_jkp_data(self) -> pd.DataFrame:
        """Load JKP data from pickle file"""
        if not os.path.exists(self.jkp_data_path):
            raise FileNotFoundError(f"JKP 数据文件不存在: {self.jkp_data_path}")
        
        try:
            logger.info(f"从 {self.jkp_data_path} 加载 JKP 数据")
            
            # Try different loading methods
            try:
                # First try with Serializable.general_load
                data = Serializable.general_load(self.jkp_data_path)
            except Exception:
                # Fallback to pandas read_pickle
                data = pd.read_pickle(self.jkp_data_path)
            
            # Ensure proper MultiIndex structure
            data = self._ensure_multiindex_structure(data)
            
            return data
            
        except Exception as e:
            logger.error(f"加载 JKP 数据失败: {e}")
            raise
    
    def _ensure_multiindex_structure(self, data: pd.DataFrame) -> pd.DataFrame:
        """Ensure data has proper MultiIndex (instrument, datetime) structure"""
        if isinstance(data.index, pd.MultiIndex):
            # Already has MultiIndex
            if data.index.names != ['instrument', 'datetime']:
                # Rename index levels
                data.index.names = ['instrument', 'datetime']
            return data
        
        # Need to create MultiIndex
        if 'ticker' in data.columns and 'eom' in data.columns:
            # Use ticker and eom columns
            data = data.set_index(['ticker', 'eom'])
            data.index.names = ['instrument', 'datetime']
        elif 'permno' in data.columns and 'date' in data.columns:
            # Use permno and date columns
            data = data.set_index(['permno', 'date'])
            data.index.names = ['instrument', 'datetime']
        else:
            logger.warning("无法创建 MultiIndex，数据结构可能不正确")
        
        return data
    
    def _analyze_data_structure(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze JKP data structure"""
        analysis = {
            'shape': data.shape,
            'index_type': type(data.index).__name__,
            'index_names': data.index.names if hasattr(data.index, 'names') else None,
            'columns': list(data.columns),
            'numeric_columns': list(data.select_dtypes(include=[np.number]).columns),
            'categorical_columns': list(data.select_dtypes(include=['object', 'category']).columns),
            'missing_data_pct': (data.isnull().sum().sum() / data.size) * 100,
            'memory_usage_mb': data.memory_usage(deep=True).sum() / (1024 * 1024),
            'date_range': None,
            'unique_instruments': None
        }
        
        # Analyze time range
        if isinstance(data.index, pd.MultiIndex):
            datetime_level = data.index.get_level_values('datetime')
            if pd.api.types.is_datetime64_any_dtype(datetime_level):
                analysis['date_range'] = (datetime_level.min(), datetime_level.max())
            
            instrument_level = data.index.get_level_values('instrument')
            analysis['unique_instruments'] = instrument_level.nunique()
        
        return analysis
    
    def _prepare_features(self, data: pd.DataFrame) -> List[str]:
        """Prepare list of features to migrate"""
        # Get all numeric columns
        numeric_columns = data.select_dtypes(include=[np.number]).columns.tolist()
        
        # Exclude specified columns
        features_to_migrate = [
            col for col in numeric_columns 
            if col not in self.exclude_columns
        ]
        
        self._migration_stats['total_features'] = len(features_to_migrate)
        
        return features_to_migrate
    
    def _migrate_features(self, features: List[str], data: pd.DataFrame) -> Dict[str, Any]:
        """Migrate features to Feature Store"""
        results = {
            'successful': [],
            'failed': [],
            'details': {}
        }
        
        for i, feature_name in enumerate(features):
            try:
                logger.info(f"迁移特征 {i+1}/{len(features)}: {feature_name}")
                
                # Extract feature data
                feature_data = self._extract_feature_data(data, feature_name)
                
                # Create feature metadata
                metadata = self._create_feature_metadata(feature_name, feature_data)
                
                # Save to Feature Store
                success = self.feature_store.save_features(
                    feature_name=f"{self.feature_prefix}{feature_name}",
                    data=feature_data,
                    metadata=metadata
                )
                
                if success:
                    results['successful'].append(feature_name)
                    self._migration_stats['migrated_features'] += 1
                    self._migration_stats['total_data_points'] += len(feature_data)
                    
                    results['details'][feature_name] = {
                        'status': 'success',
                        'data_points': len(feature_data),
                        'memory_mb': feature_data.memory_usage(deep=True).sum() / (1024 * 1024)
                    }
                else:
                    results['failed'].append(feature_name)
                    self._migration_stats['failed_features'] += 1
                    results['details'][feature_name] = {'status': 'failed', 'reason': 'save_failed'}
                
            except Exception as e:
                logger.error(f"迁移特征 {feature_name} 失败: {e}")
                results['failed'].append(feature_name)
                self._migration_stats['failed_features'] += 1
                results['details'][feature_name] = {'status': 'failed', 'reason': str(e)}
        
        return results
    
    def _extract_feature_data(self, data: pd.DataFrame, feature_name: str) -> pd.DataFrame:
        """Extract single feature data in proper format"""
        # Extract the feature column
        feature_series = data[feature_name]
        
        # Convert to DataFrame with proper structure
        feature_df = feature_series.to_frame(name=feature_name)
        
        # Ensure proper index names
        if isinstance(feature_df.index, pd.MultiIndex):
            feature_df.index.names = ['instrument', 'datetime']
        
        # Remove NaN values
        feature_df = feature_df.dropna()
        
        return feature_df
    
    def _create_feature_metadata(self, feature_name: str, feature_data: pd.DataFrame) -> FeatureMetadata:
        """Create metadata for a feature"""
        # Determine feature type
        if pd.api.types.is_numeric_dtype(feature_data[feature_name]):
            feature_type = FeatureType.NUMERICAL
        elif pd.api.types.is_bool_dtype(feature_data[feature_name]):
            feature_type = FeatureType.BOOLEAN
        else:
            feature_type = FeatureType.CATEGORICAL
        
        # Create metadata
        metadata = FeatureMetadata(
            name=f"{self.feature_prefix}{feature_name}",
            description=f"JKP factor: {feature_name}",
            feature_type=feature_type,
            storage_type=self.storage_type,
            tags=['jkp', 'factor', 'financial'],
            owner='jkp_migrator',
            schema={
                'data_type': str(feature_data[feature_name].dtype),
                'min_value': float(feature_data[feature_name].min()) if feature_type == FeatureType.NUMERICAL else None,
                'max_value': float(feature_data[feature_name].max()) if feature_type == FeatureType.NUMERICAL else None,
                'unique_count': feature_data[feature_name].nunique()
            },
            quality_metrics={
                'completeness': 1.0 - (feature_data[feature_name].isnull().sum() / len(feature_data)),
                'data_points': len(feature_data),
                'memory_usage_mb': feature_data.memory_usage(deep=True).sum() / (1024 * 1024)
            }
        )
        
        return metadata
    
    def _generate_migration_report(self, migration_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive migration report"""
        report = {
            'migration_summary': self._migration_stats.copy(),
            'successful_features': migration_results['successful'],
            'failed_features': migration_results['failed'],
            'feature_details': migration_results['details'],
            'recommendations': []
        }
        
        # Add success rate
        total_features = self._migration_stats['total_features']
        if total_features > 0:
            success_rate = (self._migration_stats['migrated_features'] / total_features) * 100
            report['migration_summary']['success_rate'] = success_rate
        
        # Add recommendations
        if self._migration_stats['failed_features'] > 0:
            report['recommendations'].append("检查失败的特征，可能需要数据清理或格式调整")
        
        if self._migration_stats['total_data_points'] > 1000000:
            report['recommendations'].append("考虑使用数据分区或压缩来优化存储")
        
        return report
    
    def get_migration_stats(self) -> Dict[str, Any]:
        """Get current migration statistics"""
        return self._migration_stats.copy()
