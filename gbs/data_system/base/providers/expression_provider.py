#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Expression provider implementations

This module provides expression data for instruments.
"""

import abc
import pandas as pd
from typing import Dict

from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.utils.mod import init_instance_by_config


class ProviderBackendMixin:
    """
    Helper class to make providers based on storage backend more convenient

    It is not necessary to inherit this class if the provider doesn't rely on backend storage
    """

    def get_default_backend(self):
        """
        Get default backend configuration

        Returns:
            Dict: Default backend configuration
        """
        import re
        import copy
        backend = {}
        provider_name = re.findall("[A-Z][^A-Z]*", self.__class__.__name__)[-2]
        # Set default storage class
        backend.setdefault("class", f"File{provider_name}Storage")
        # Set default storage module
        backend.setdefault("module_path", "gbs.data_system.base.storage.file_storage")
        return backend

    def backend_obj(self, **kwargs):
        """
        Get backend object

        Args:
            **kwargs: Additional parameters for backend

        Returns:
            Object: Backend object
        """
        import copy
        backend = self.backend if hasattr(self, 'backend') and self.backend else self.get_default_backend()
        backend = copy.deepcopy(backend)
        backend.setdefault("kwargs", {}).update(**kwargs)
        return init_instance_by_config(backend)

# Set up logger
logger = get_loki_logger(__name__).logger


class ExpressionProvider(abc.ABC):
    """
    Expression provider base class

    Provides expression data for instruments
    """

    def __init__(self):
        """
        Initialize expression provider
        """
        self.expression_instance_cache = {}

    def get_expression_instance(self, field):
        """
        Get expression instance for a field

        Args:
            field: Expression field

        Returns:
            Object: Expression instance
        """
        try:
            if field in self.expression_instance_cache:
                expression = self.expression_instance_cache[field]
            else:
                from gbs.data_system.utils import parse_field
                base_field, ops = parse_field(field)

                # 如果没有操作符，直接使用基本字段
                if ops is None:
                    expression_str = base_field
                else:
                    # 否则，构建表达式字符串
                    expression_str = f"{base_field}__{ops}"

                # 确保表达式字符串是有效的
                if not isinstance(expression_str, str):
                    expression_str = str(expression_str)

                # 尝试评估表达式
                try:
                    expression = eval(expression_str)
                except (NameError, SyntaxError) as e:
                    # 如果评估失败，尝试使用基本字段
                    logger.warning(f"无法评估表达式 '{expression_str}': {str(e)}，尝试使用基本字段 '{base_field}'")
                    expression = base_field

                self.expression_instance_cache[field] = expression
        except NameError as e:
            logger.exception(
                f"ERROR: field [{field}] contains invalid operator/variable [{str(e).split()[1]}]"
            )
            raise
        except SyntaxError:
            logger.exception(f"ERROR: field [{field}] contains invalid syntax")
            raise
        except Exception as e:
            logger.exception(f"ERROR: failed to get expression instance for field [{field}]: {str(e)}")
            raise

        return expression

    @abc.abstractmethod
    def expression(self, instrument, field, start_time=None, end_time=None, freq="day") -> pd.Series:
        """
        Get expression data for an instrument

        Args:
            instrument: Instrument ID
            field: Expression field
            start_time: Start time
            end_time: End time
            freq: Frequency

        Returns:
            pd.Series: Expression data
        """
        raise NotImplementedError("Subclass of ExpressionProvider must implement `expression` method")


class LocalExpressionProvider(ExpressionProvider, ProviderBackendMixin):
    """
    Local expression provider

    Provides expression data from local storage
    """

    def __init__(self, name: str = None, backend: Dict = None, **kwargs):
        """
        Initialize local expression provider

        Args:
            name: Provider name
            backend: Backend configuration
            **kwargs: Additional parameters
        """
        super().__init__()
        self.name = name
        self.backend = backend

    def expression(self, instrument, field, start_time=None, end_time=None, freq="day") -> pd.Series:
        """
        Get expression data for an instrument

        Args:
            instrument: Instrument ID
            field: Expression field
            start_time: Start time
            end_time: End time
            freq: Frequency

        Returns:
            pd.Series: Expression data
        """
        try:
            # Get expression instance
            expression = self.get_expression_instance(field)

            # If expression is just a string (basic field), get data from backend
            if isinstance(expression, str):
                from gbs.data_system.utils import code_to_fname
                instrument_fname = code_to_fname(instrument)
                data = self.backend_obj(instrument=instrument_fname, field=expression, freq=freq).data

                # Filter by time range if specified
                if start_time is not None or end_time is not None:
                    if hasattr(data.index, 'dtype') and pd.api.types.is_datetime64_any_dtype(data.index):
                        if start_time is not None:
                            start_time_pd = pd.Timestamp(start_time)
                            data = data[data.index >= start_time_pd]
                        if end_time is not None:
                            end_time_pd = pd.Timestamp(end_time)
                            data = data[data.index <= end_time_pd]

                return data
            else:
                # For complex expressions, we would need to evaluate them
                # This is a simplified implementation
                logger.warning(f"Complex expression evaluation not implemented for: {field}")
                return pd.Series(dtype=float)

        except Exception as e:
            logger.error(f"Failed to get expression data for {instrument}, {field}: {str(e)}")
            return pd.Series(dtype=float)
