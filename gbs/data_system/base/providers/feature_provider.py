#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Feature provider implementations

This module provides feature data for instruments with intelligent routing support.
"""

import abc
import copy
import numpy as np
import pandas as pd
from typing import Dict, Optional

from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.utils.mod import init_instance_by_config


class ProviderBackendMixin:
    """
    Helper class to make providers based on storage backend more convenient

    It is not necessary to inherit this class if the provider doesn't rely on backend storage
    """

    def get_default_backend(self):
        """
        Get default backend configuration

        Returns:
            Dict: Default backend configuration
        """
        import re
        import copy
        backend = {}
        provider_name = re.findall("[A-Z][^A-Z]*", self.__class__.__name__)[-2]
        # Set default storage class
        backend.setdefault("class", f"File{provider_name}Storage")
        # Set default storage module
        backend.setdefault("module_path", "gbs.data_system.base.storage.file_storage")
        return backend

    def backend_obj(self, **kwargs):
        """
        Get backend object

        Args:
            **kwargs: Additional parameters for backend

        Returns:
            Object: Backend object
        """
        import copy
        backend = self.backend if hasattr(self, 'backend') and self.backend else self.get_default_backend()
        backend = copy.deepcopy(backend)
        backend.setdefault("kwargs", {}).update(**kwargs)
        return init_instance_by_config(backend)

# Set up logger
logger = get_loki_logger(__name__).logger


class FeatureProvider(abc.ABC):
    """
    Feature provider base class

    Provides feature data for instruments
    """

    @abc.abstractmethod
    def feature(self, instruments, fields, start_time=None, end_time=None, freq="day"):
        """
        Get feature data for instruments

        Args:
            instruments: Instrument ID or list of IDs
            fields: Feature field or list of fields
            start_time: Start time
            end_time: End time
            freq: Frequency

        Returns:
            pd.DataFrame: Feature data
        """
        raise NotImplementedError("Subclass of FeatureProvider must implement `feature` method")


class LocalFeatureProvider(FeatureProvider, ProviderBackendMixin):
    """
    Local feature provider with intelligent routing support

    Provides feature data from local storage with Feature Store intelligent routing
    """

    def __init__(self, name: str = None, backend: Dict = None, feature_store=None, **kwargs):
        """
        Initialize local feature provider

        Args:
            name: Provider name
            backend: Backend configuration (used as fallback when feature_store is disabled)
            feature_store: Optional feature store configuration or instance
                - If dict: Configuration for creating GBSFeatureStore
                - If FeatureStoreInterface: Direct feature store instance
                - If None: Use traditional storage only
            **kwargs: Additional parameters
        """
        super().__init__()
        self.name = name
        self.backend = backend

        # Initialize Feature Store
        self._feature_store = None
        if feature_store is not None:
            self._initialize_feature_store(feature_store)

        # Legacy support for simple feature store config
        self.feature_store_config = feature_store if isinstance(feature_store, dict) else None
        self._feature_metadata = {}  # Legacy metadata storage
        self._feature_store_storages = {}
        if self.feature_store_config:
            self._feature_store_storages = self.feature_store_config.get('storages', {})

    def _initialize_feature_store(self, feature_store):
        """Initialize Feature Store from config or instance"""
        try:
            # Import here to avoid circular imports
            from gbs.data_system.feature_store import FeatureStoreInterface, GBSFeatureStore

            if isinstance(feature_store, GBSFeatureStore):
                # Direct GBSFeatureStore instance
                self._feature_store = feature_store
                logger.info("Using provided GBSFeatureStore instance")
            elif isinstance(feature_store, FeatureStoreInterface):
                # Other Feature Store interface implementations
                self._feature_store = feature_store
                logger.info("Using provided Feature Store instance")
            elif isinstance(feature_store, dict):
                # Create GBSFeatureStore from config
                self._feature_store = GBSFeatureStore(feature_store)
                logger.info("Created GBSFeatureStore from configuration")
            else:
                logger.warning(f"Invalid feature_store type: {type(feature_store)}")

        except ImportError as e:
            logger.warning(f"Failed to import Feature Store: {e}")
        except Exception as e:
            logger.error(f"Failed to initialize Feature Store: {e}")

    def register_feature_metadata(self, feature_name: str, storage_type: str, **metadata):
        """
        注册特征元数据（简单版本）

        Args:
            feature_name: 特征名称
            storage_type: 存储类型 ('files', 'database', 'redis')
            **metadata: 其他元数据
        """
        self._feature_metadata[feature_name] = {
            'storage_type': storage_type,
            'created_at': pd.Timestamp.now().isoformat(),
            **metadata
        }
        logger.info(f"已注册特征元数据: {feature_name} -> {storage_type}")

    def _get_feature_storage_type(self, feature_name: str) -> Optional[str]:
        """
        获取特征的存储类型

        Args:
            feature_name: 特征名称

        Returns:
            存储类型或 None
        """
        metadata = self._feature_metadata.get(feature_name)
        return metadata.get('storage_type') if metadata else None

    def feature(self, instruments=None, fields=None, start_time=None, end_time=None, freq="day"):
        """
        Get feature data for instruments

        智能路由逻辑：
        1. 如果配置了新的 Feature Store，优先使用
        2. 如果配置了传统 Feature Store，使用智能路由
        3. 否则使用传统存储
        4. 保持完全向后兼容

        Args:
            instruments: Instrument ID or list of IDs
            fields: Feature field or list of fields
            start_time: Start time
            end_time: End time
            freq: Frequency

        Returns:
            pd.DataFrame: Feature data
        """
        # 处理参数
        if instruments is None:
            return pd.DataFrame()

        if not isinstance(instruments, (list, tuple, np.ndarray)):
            instruments = [instruments]

        # 保持原始大小写，因为 Redis 中的 JKP 数据使用大写股票代码
        # instruments = [inst.lower() if isinstance(inst, str) else inst for inst in instruments]

        if fields is None:
            fields = ["open", "high", "low", "close", "volume"]
        elif not isinstance(fields, (list, tuple, np.ndarray)):
            fields = [fields]

        # 优先使用新的 Feature Store
        if self._feature_store is not None:
            try:
                logger.info(f"Using Feature Store to get features: {fields} for instruments: {instruments}")
                data = self._feature_store.get_features(
                    instruments=instruments,
                    feature_names=fields,
                    start_time=pd.Timestamp(start_time) if start_time else None,
                    end_time=pd.Timestamp(end_time) if end_time else None,
                    freq=freq
                )
                logger.info(f"Feature Store returned data shape: {data.shape}, empty: {data.empty}")
                return data
            except Exception as e:
                logger.warning(f"Feature Store failed, falling back to traditional storage: {e}")
                import traceback
                traceback.print_exc()

        # 传统智能路由（向后兼容）
        if self.feature_store_config and self._feature_store_storages:
            return self._smart_route_features(instruments, fields, start_time, end_time, freq)
        else:
            # 传统方式
            return self._get_from_traditional_storage(instruments, fields, start_time, end_time, freq)

    def _smart_route_features(self, instruments, fields, start_time, end_time, freq):
        """
        智能路由：根据特征元数据路由到不同存储
        """
        result_data = []

        for field in fields:
            # 获取特征的存储类型
            storage_type = self._get_feature_storage_type(field)

            if storage_type and storage_type in self._feature_store_storages:
                # 使用 Feature Store 中配置的存储
                try:
                    storage_config = self._feature_store_storages[storage_type]
                    data = self._get_from_specific_storage(
                        instruments, field, start_time, end_time, freq, storage_config
                    )
                    if data is not None:
                        result_data.append(data)
                        continue
                except Exception as e:
                    logger.warning(f"从 {storage_type} 存储获取 {field} 失败: {e}，fallback 到传统存储")

            # Fallback 到传统存储
            try:
                data = self._get_field_from_traditional_storage(
                    instruments, field, start_time, end_time, freq
                )
                if data is not None:
                    result_data.append(data)
            except Exception as e:
                logger.error(f"从传统存储获取 {field} 失败: {e}")

        # 合并结果
        return self._merge_field_results(result_data)

    def _get_from_specific_storage(self, instruments, field, start_time, end_time, freq, storage_config):
        """
        从指定存储获取单个字段的数据
        """
        result_data = []

        for instrument in instruments:
            from gbs.data_system.utils import code_to_fname
            instrument_fname = code_to_fname(instrument)

            try:
                # 动态创建存储实例
                backend_config = copy.deepcopy(storage_config)
                backend_config.setdefault("kwargs", {}).update(
                    instrument=instrument_fname, field=field, freq=freq
                )
                storage_instance = init_instance_by_config(backend_config)

                # 获取数据
                data = storage_instance.data

                # 时间筛选
                data = self._filter_time_range(data, start_time, end_time, freq)

                # 格式化数据
                if isinstance(data, pd.Series):
                    data = data.to_frame(name=field)
                data['instrument'] = instrument
                data['field'] = field

                result_data.append(data)

            except Exception as e:
                logger.error(f"从 {storage_config.get('class')} 获取数据失败: {e}")
                raise

        return result_data

    def _filter_time_range(self, data, start_time, end_time, freq):
        """
        筛选时间范围
        """
        if start_time is None and end_time is None:
            return data

        try:
            if hasattr(data.index, 'dtype') and pd.api.types.is_datetime64_any_dtype(data.index):
                # 索引是datetime类型
                if start_time is not None:
                    start_time_pd = pd.Timestamp(start_time)
                    data = data[data.index >= start_time_pd]
                if end_time is not None:
                    end_time_pd = pd.Timestamp(end_time)
                    data = data[data.index <= end_time_pd]
            else:
                # 索引不是datetime类型，使用整数索引
                try:
                    from gbs.data_system.base.data import D
                    _, _, start_index, end_index = D.locate_index(start_time, end_time, freq=freq)
                    data = data.iloc[start_index:end_index + 1]
                except Exception as e2:
                    logger.warning(f"整数索引筛选失败: {str(e2)}，使用原始数据")
        except Exception as e:
            logger.warning(f"时间筛选失败: {str(e)}，使用原始数据")

        return data

    def _get_field_from_traditional_storage(self, instruments, field, start_time, end_time, freq):
        """
        从传统存储获取单个字段的数据
        """
        result_data = []

        for instrument in instruments:
            from gbs.data_system.utils import code_to_fname
            instrument_fname = code_to_fname(instrument)

            field_str = str(field)
            if field_str.startswith('$'):
                field_str = field_str[1:]

            try:
                # 获取数据
                data = self.backend_obj(instrument=instrument_fname, field=field_str, freq=freq).data

                # 时间筛选
                data = self._filter_time_range(data, start_time, end_time, freq)

                # 格式化数据
                if isinstance(data, pd.Series):
                    data = data.to_frame(name=field_str)
                data['instrument'] = instrument
                data['field'] = field_str

                result_data.append(data)

            except Exception as e:
                logger.error(f"获取特征数据失败: instrument={instrument}, field={field_str}, error={str(e)}")

        return result_data

    def _merge_field_results(self, result_data_list):
        """
        合并字段结果
        """
        if not result_data_list:
            return pd.DataFrame()

        # 展平嵌套列表
        all_data = []
        for field_data in result_data_list:
            if isinstance(field_data, list):
                all_data.extend(field_data)
            else:
                all_data.append(field_data)

        if not all_data:
            return pd.DataFrame()

        try:
            result = pd.concat(all_data, axis=0)
            # 设置多级索引
            result = result.reset_index()
            result = result.rename(columns={'index': 'datetime'})
            result = result.set_index(['instrument', 'datetime', 'field'])
            return result
        except Exception as e:
            logger.error(f"合并特征数据失败: {str(e)}")
            return pd.DataFrame()

    def _get_from_traditional_storage(self, instruments, fields, start_time, end_time, freq):
        """
        从传统存储获取特征数据（原有逻辑）
        """
        # 创建结果DataFrame
        result_data = []

        # 处理每个instrument和field
        for instrument in instruments:
            # Validate and prepare parameters
            # Convert instrument code if needed
            from gbs.data_system.utils import code_to_fname
            instrument_fname = code_to_fname(instrument)

            for field in fields:
                field_str = str(field)
                # 如果字段以$开头，则移除$前缀
                if field_str.startswith('$'):
                    field_str = field_str[1:]

                # Get data from backend
                try:
                    # 获取数据
                    data = self.backend_obj(instrument=instrument_fname, field=field_str, freq=freq).data

                    # 筛选时间范围
                    if start_time is not None or end_time is not None:
                        try:
                            # 检查索引类型
                            if hasattr(data.index, 'dtype') and pd.api.types.is_datetime64_any_dtype(data.index):
                                # 索引已经是datetime类型，直接筛选
                                if start_time is not None:
                                    start_time_pd = pd.Timestamp(start_time)
                                    data = data[data.index >= start_time_pd]
                                if end_time is not None:
                                    end_time_pd = pd.Timestamp(end_time)
                                    data = data[data.index <= end_time_pd]
                            else:
                                # 索引不是datetime类型，使用整数索引
                                logger.info(f"索引类型为 {type(data.index)}，使用整数索引进行时间筛选")
                                try:
                                    from gbs.data_system.base.data import D
                                    _, _, start_index, end_index = D.locate_index(start_time, end_time, freq=freq)
                                    data = data.iloc[start_index:end_index + 1]  # 包含end_index
                                except Exception as e2:
                                    logger.warning(f"整数索引筛选失败: {str(e2)}，使用原始数据")
                        except Exception as e:
                            logger.warning(f"时间筛选失败: {str(e)}，使用原始数据")

                    # 添加instrument和field信息
                    if isinstance(data, pd.Series):
                        data = data.to_frame(name=field_str)
                    data['instrument'] = instrument
                    data['field'] = field_str

                    # 添加到结果列表
                    result_data.append(data)
                except Exception as e:
                    logger.error(f"获取特征数据失败: instrument={instrument}, field={field_str}, error={str(e)}")

        # 合并结果
        if not result_data:
            return pd.DataFrame()

        try:
            result = pd.concat(result_data, axis=0)
            # 设置多级索引
            result = result.reset_index()
            result = result.rename(columns={'index': 'datetime'})
            result = result.set_index(['instrument', 'datetime', 'field'])
            return result
        except Exception as e:
            logger.error(f"合并特征数据失败: {str(e)}")
            return pd.DataFrame()

    def save_to_feature_store(self, feature_name: str, data: pd.DataFrame,
                             storage_type: str = 'files', **metadata) -> bool:
        """
        保存特征数据到 Feature Store（简化版本）

        Args:
            feature_name: 特征名称
            data: 特征数据 (MultiIndex: instrument, datetime)
            storage_type: 存储类型 ('files', 'database', 'redis')
            **metadata: 特征元数据

        Returns:
            bool: 保存是否成功
        """
        if not self.feature_store_config:
            logger.warning("Feature Store 未配置，无法保存特征数据")
            return False

        try:
            # 注册特征元数据
            self.register_feature_metadata(feature_name, storage_type, **metadata)

            # 获取对应的存储配置
            if storage_type not in self._feature_store_storages:
                logger.error(f"存储类型 {storage_type} 未配置")
                return False

            storage_config = self._feature_store_storages[storage_type]

            # 这里可以扩展实际的数据保存逻辑
            # 目前只是注册元数据
            logger.info(f"特征 {feature_name} 元数据已注册到 Feature Store ({storage_type})")
            return True

        except Exception as e:
            logger.error(f"保存特征到 Feature Store 失败: {e}")
            return False

