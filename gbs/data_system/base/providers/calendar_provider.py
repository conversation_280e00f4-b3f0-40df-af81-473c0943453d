#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Calendar provider implementations

This module provides calendar data for different time frequencies.
"""

import abc
import bisect
import numpy as np
import pandas as pd
from typing import Dict

from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.utils.mod import init_instance_by_config
from ..cache import H

# Set up logger
logger = get_loki_logger(__name__).logger


class ProviderBackendMixin:
    """
    Helper class to make providers based on storage backend more convenient

    It is not necessary to inherit this class if the provider doesn't rely on backend storage
    """

    def get_default_backend(self):
        """
        Get default backend configuration

        Returns:
            Dict: Default backend configuration
        """
        import re
        import copy
        backend = {}
        provider_name = re.findall("[A-Z][^A-Z]*", self.__class__.__name__)[-2]
        # Set default storage class
        backend.setdefault("class", f"File{provider_name}Storage")
        # Set default storage module
        backend.setdefault("module_path", "gbs.data_system.base.storage.file_storage")
        return backend

    def backend_obj(self, **kwargs):
        """
        Get backend object

        Args:
            **kwargs: Additional parameters for backend

        Returns:
            Object: Backend object
        """
        import copy
        backend = self.backend if hasattr(self, 'backend') and self.backend else self.get_default_backend()
        backend = copy.deepcopy(backend)
        backend.setdefault("kwargs", {}).update(**kwargs)
        return init_instance_by_config(backend)


class CalendarProvider(abc.ABC):
    """
    Calendar provider base class

    Provides calendar data for different time frequencies
    """

    def calendar(self, start_time=None, end_time=None, freq="day", future=False):
        """
        Get calendar for a specific time range and frequency

        Args:
            start_time: Start time
            end_time: End time
            freq: Frequency (day, week, month, etc.)
            future: Whether to include future dates

        Returns:
            List of calendar dates
        """
        _calendar, _calendar_index = self._get_calendar(freq, future)

        # Handle None values
        if start_time == "None":
            start_time = None
        if end_time == "None":
            end_time = None

        # Convert to timestamps
        if start_time:
            start_time = pd.Timestamp(start_time)
            if start_time > _calendar[-1]:
                return np.array([])
        else:
            start_time = _calendar[0]

        if end_time:
            end_time = pd.Timestamp(end_time)
            if end_time < _calendar[0]:
                return np.array([])
        else:
            end_time = _calendar[-1]

        # Get indices
        _, _, si, ei = self.locate_index(start_time, end_time, freq, future)
        return _calendar[si:ei + 1]

    def locate_index(self, start_time, end_time, freq="day", future=False):
        """
        Locate the start and end time indices in the calendar

        Args:
            start_time: Start time
            end_time: End time
            freq: Frequency
            future: Whether to include future dates

        Returns:
            Tuple: (start_time, end_time, start_index, end_index)
        """
        start_time = pd.Timestamp(start_time)
        end_time = pd.Timestamp(end_time)

        calendar, calendar_index = self._get_calendar(freq=freq, future=future)

        # Find start index
        if start_time not in calendar_index:
            try:
                start_time = calendar[bisect.bisect_left(calendar, start_time)]
            except IndexError as e:
                raise IndexError(
                    "Start time is beyond the available calendar range. "
                    "If you want to include future dates, use future=True"
                ) from e

        start_index = calendar_index[start_time]

        # Find end index
        if end_time not in calendar_index:
            end_time = calendar[bisect.bisect_right(calendar, end_time) - 1]

        end_index = calendar_index[end_time]

        return start_time, end_time, start_index, end_index

    def _get_calendar(self, freq, future):
        """
        Get calendar from cache or load it

        Args:
            freq: Frequency
            future: Whether to include future dates

        Returns:
            Tuple: (calendar, calendar_index)
        """
        flag = f"{freq}_future_{future}"
        if flag not in H["c"]:
            _calendar = np.array(self.load_calendar(freq, future))
            _calendar_index = {x: i for i, x in enumerate(_calendar)}
            H["c"][flag] = (_calendar, _calendar_index)

        return H["c"][flag]

    @abc.abstractmethod
    def load_calendar(self, freq, future):
        """
        Load calendar data from storage

        Args:
            freq: Frequency
            future: Whether to include future dates

        Returns:
            List of calendar dates
        """
        raise NotImplementedError("Subclass of CalendarProvider must implement `load_calendar` method")


class LocalCalendarProvider(CalendarProvider, ProviderBackendMixin):
    """
    Local calendar provider

    Provides calendar data from local storage
    """

    def __init__(self, name: str = None, backend: Dict = None, **kwargs):
        """
        Initialize local calendar provider

        Args:
            name: Provider name
            backend: Backend configuration
            **kwargs: Additional parameters
        """
        super().__init__()
        self.name = name
        self.backend = backend

    def load_calendar(self, freq, future=False):
        """
        Load calendar data from storage

        Args:
            freq: Frequency
            future: Whether to include future dates

        Returns:
            List of calendar dates
        """
        try:
            backend_obj = self.backend_obj(freq=freq, future=future).data
            return [pd.Timestamp(x) for x in backend_obj]
        except ValueError:
            if future:
                logger.warning(f"Load calendar error: freq={freq}, future={future}; return current calendar!")
                logger.warning("Future calendar is not available")
                # Try again without future dates
                try:
                    backend_obj = self.backend_obj(freq=freq, future=False).data
                    return [pd.Timestamp(x) for x in backend_obj]
                except Exception as e:
                    logger.error(f"Failed to load calendar: {str(e)}")
                    # Return a default calendar
                    return [pd.Timestamp('2000-01-01'), pd.Timestamp('2000-01-02'), pd.Timestamp('2000-01-03')]
            else:
                logger.error(f"Failed to load calendar: freq={freq}, future={future}")
                # Return a default calendar
                return [pd.Timestamp('2000-01-01'), pd.Timestamp('2000-01-02'), pd.Timestamp('2000-01-03')]
        except Exception as e:
            logger.error(f"Failed to load calendar: {str(e)}")
            # Return a default calendar
            return [pd.Timestamp('2000-01-01'), pd.Timestamp('2000-01-02'), pd.Timestamp('2000-01-03')]

    def calendar(self, start_time=None, end_time=None, freq="day", future=False):
        """
        Get calendar for a specific time range and frequency

        Args:
            start_time: Start time
            end_time: End time
            freq: Frequency (day, week, month, etc.)
            future: Whether to include future dates

        Returns:
            List of calendar dates
        """
        # Get calendar from cache or load it
        if f"{freq}_future_{future}" not in H["c"]:
            _calendar = np.array(self.load_calendar(freq, future))
            _calendar_index = {x: i for i, x in enumerate(_calendar)}
            H["c"][f"{freq}_future_{future}"] = (_calendar, _calendar_index)

        _calendar, _calendar_index = H["c"][f"{freq}_future_{future}"]

        # Handle None values
        if start_time == "None":
            start_time = None
        if end_time == "None":
            end_time = None

        # Convert to timestamps
        if start_time:
            start_time = pd.Timestamp(start_time)
            if start_time > _calendar[-1]:
                return np.array([])
        else:
            start_time = _calendar[0]

        if end_time:
            end_time = pd.Timestamp(end_time)
            if end_time < _calendar[0]:
                return np.array([])
        else:
            end_time = _calendar[-1]

        # Get indices
        _, _, si, ei = self.locate_index(start_time, end_time, freq, future)
        return _calendar[si:ei + 1]
