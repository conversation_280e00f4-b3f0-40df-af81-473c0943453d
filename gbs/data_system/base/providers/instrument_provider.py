#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Instrument provider implementations

This module provides instrument data for different markets.
"""

import abc
import numpy as np
import pandas as pd
from datetime import datetime
from typing import List, Union, Optional, Dict

from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.utils.mod import init_instance_by_config
from ..cache import H


class ProviderBackendMixin:
    """
    Helper class to make providers based on storage backend more convenient

    It is not necessary to inherit this class if the provider doesn't rely on backend storage
    """

    def get_default_backend(self):
        """
        Get default backend configuration

        Returns:
            Dict: Default backend configuration
        """
        import re
        import copy
        backend = {}
        provider_name = re.findall("[A-Z][^A-Z]*", self.__class__.__name__)[-2]
        # Set default storage class
        backend.setdefault("class", f"File{provider_name}Storage")
        # Set default storage module
        backend.setdefault("module_path", "gbs.data_system.base.storage.file_storage")
        return backend

    def backend_obj(self, **kwargs):
        """
        Get backend object

        Args:
            **kwargs: Additional parameters for backend

        Returns:
            Object: Backend object
        """
        import copy
        backend = self.backend if hasattr(self, 'backend') and self.backend else self.get_default_backend()
        backend = copy.deepcopy(backend)
        backend.setdefault("kwargs", {}).update(**kwargs)
        return init_instance_by_config(backend)

# Set up logger
logger = get_loki_logger(__name__).logger


class InstrumentProvider(abc.ABC):
    """
    Instrument provider base class

    Provides instrument data for different markets
    """

    @staticmethod
    def instruments(market: Union[List, str] = "all", filter_pipe: Union[List, None] = None):
        """
        Get instrument configuration

        Args:
            market: Market name or list of instruments
            filter_pipe: List of filters

        Returns:
            Dict or List: Instrument configuration
        """
        if isinstance(market, list):
            return market

        if filter_pipe is None:
            filter_pipe = []

        config = {"market": market, "filter_pipe": []}

        # Add filters
        for filter_t in filter_pipe:
            if isinstance(filter_t, dict):
                _config = filter_t
            else:
                from gbs.data_system import filter as F
                if hasattr(filter_t, 'to_config'):
                    _config = filter_t.to_config()
                else:
                    raise TypeError(f"Unsupported filter type: {type(filter_t)}")

            config["filter_pipe"].append(_config)

        return config

    @abc.abstractmethod
    def list_instruments(self, instruments, start_time=None, end_time=None, freq="day", as_list=False):
        """
        List instruments based on configuration

        Args:
            instruments: Instrument configuration
            start_time: Start time
            end_time: End time
            freq: Frequency
            as_list: Whether to return as list

        Returns:
            Dict or List: Instruments
        """
        raise NotImplementedError("Subclass of InstrumentProvider must implement `list_instruments` method")

    # Instrument types
    LIST = "LIST"
    DICT = "DICT"
    CONF = "CONF"

    @classmethod
    def get_inst_type(cls, inst):
        """
        Get instrument type

        Args:
            inst: Instrument

        Returns:
            str: Instrument type
        """
        if isinstance(inst, str):
            # 如果是字符串，将其视为市场名称
            return cls.CONF
        if isinstance(inst, dict) and "market" in inst:
            return cls.CONF
        if isinstance(inst, dict):
            return cls.DICT
        if isinstance(inst, (list, tuple, pd.Index, np.ndarray)):
            return cls.LIST
        raise ValueError(f"Unknown instrument type: {type(inst)}")


class LocalInstrumentProvider(InstrumentProvider, ProviderBackendMixin):
    """
    Local instrument provider

    Provides instrument data from local storage
    """

    def __init__(self, name: str = None, backend: Dict = None, **kwargs):
        """
        Initialize local instrument provider

        Args:
            name: Provider name
            backend: Backend configuration
            **kwargs: Additional parameters
        """
        super().__init__()
        self.name = name
        self.backend = backend

    def _load_instruments(self, market, freq):
        """
        Load instruments from storage

        Args:
            market: Market name
            freq: Frequency

        Returns:
            Dict of instruments
        """
        return self.backend_obj(market=market, freq=freq).data

    def list_instruments(self, instruments, start_time=None, end_time=None, freq="day", as_list=False):
        """
        List instruments based on configuration

        Args:
            instruments: Instrument configuration
            start_time: Start time
            end_time: End time
            freq: Frequency
            as_list: Whether to return as list

        Returns:
            Dict or list of instruments
        """
        # Convert instruments to standard format if it's a string
        if isinstance(instruments, str):
            instruments = {"market": instruments, "filter_pipe": []}

        # Ensure instruments is a dict with market key
        if not isinstance(instruments, dict) or "market" not in instruments:
            raise ValueError(f"Unsupported instruments type: {type(instruments)}")

        market = instruments["market"]

        # Check cache
        if market in H["i"]:
            _instruments = H["i"][market]
        else:
            _instruments = self._load_instruments(market, freq=freq)
            H["i"][market] = _instruments

        # Get calendar boundaries
        from gbs.data_system.base.data import D
        cal = D.calendar(freq=freq)
        start_time = pd.Timestamp(start_time or cal[0])
        end_time = pd.Timestamp(end_time or cal[-1])

        # Filter instruments by time range with robust error handling
        _instruments_filtered = {}
        for inst, spans in _instruments.items():
            valid_spans = []

            # 如果spans是空列表，使用默认的时间范围
            if not spans:
                valid_spans.append((start_time, end_time))
            else:
                # 处理每个时间范围
                for span in spans:
                    try:
                        # 安全地转换日期时间字符串
                        if isinstance(span, tuple) and len(span) >= 2:
                            span_start = pd.Timestamp(span[0]) if isinstance(span[0], (str, pd.Timestamp, datetime)) else start_time
                            span_end = pd.Timestamp(span[1]) if isinstance(span[1], (str, pd.Timestamp, datetime)) else end_time

                            # 检查日期是否有效
                            if span_start <= span_end:
                                valid_spans.append((max(start_time, span_start), min(end_time, span_end)))
                    except (ValueError, TypeError) as e:
                        logger.warning(f"处理instrument {inst}的时间范围时出错: {e}, 跳过此范围: {span}")
                        continue

            # 如果没有有效的时间范围，使用默认的时间范围
            if not valid_spans:
                valid_spans.append((start_time, end_time))

            _instruments_filtered[inst] = valid_spans

        # Remove empty spans
        _instruments_filtered = {key: value for key, value in _instruments_filtered.items() if value}

        # Apply filters
        filter_pipe = instruments.get("filter_pipe", [])
        for filter_config in filter_pipe:
            from data_system import filter as F
            filter_t = getattr(F, filter_config["filter_type"]).from_config(filter_config)
            _instruments_filtered = filter_t(_instruments_filtered, start_time, end_time, freq)

        # Return as list if requested
        if as_list:
            return list(_instruments_filtered)

        return _instruments_filtered
