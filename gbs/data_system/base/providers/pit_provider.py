#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Point-in-Time Provider

This module contains the PITProvider base class for point-in-time data access.
"""

import abc
from typing import Optional
import pandas as pd


class PITProvider(abc.ABC):
    """
    Point-in-Time provider base class

    Provides point-in-time data for instruments
    """

    @abc.abstractmethod
    def period_feature(
        self,
        instrument,
        field,
        start_index: int,
        end_index: int,
        cur_time: pd.Timestamp,
        period: Optional[int] = None,
    ) -> pd.Series:
        """
        Get historical periods data series

        Args:
            instrument: Instrument ID
            field: Feature field
            start_index: Start index relative to current time
            end_index: End index relative to current time
            cur_time: Current time
            period: Specific period to query

        Returns:
            pd.Series: Period feature data
        """
        raise NotImplementedError("Subclass of PITProvider must implement `period_feature` method")
