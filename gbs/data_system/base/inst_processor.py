#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
金融工具处理器模块

提供处理金融工具数据的接口
"""

import abc
import json
import pandas as pd

from gbs.core.utils.loki_logger import get_loki_logger

logger = get_loki_logger(__name__).logger

class InstProcessor:
    """金融工具处理器基类
    
    用于处理金融工具数据
    """
    
    @abc.abstractmethod
    def __call__(self, df: pd.DataFrame, instrument, *args, **kwargs):
        """处理数据
        
        注意: **处理器可能会原地更改 `df` 的内容！！！！**
        用户应该在外部保留数据的副本
        
        参数
        ----------
        df : pd.DataFrame
            处理器的原始数据或前一个处理器的结果
        instrument : str
            金融工具代码
        *args, **kwargs : 
            其他参数
        """
        pass
    
    def __str__(self):
        """字符串表示"""
        return f"{self.__class__.__name__}:{json.dumps(self.__dict__, sort_keys=True, default=str)}"


class DropnaProcessor(InstProcessor):
    """删除 NaN 值的处理器
    
    删除包含 NaN 值的行
    """
    
    def __call__(self, df: pd.DataFrame, instrument, *args, **kwargs):
        """处理数据
        
        参数
        ----------
        df : pd.DataFrame
            处理器的原始数据或前一个处理器的结果
        instrument : str
            金融工具代码
        *args, **kwargs : 
            其他参数
            
        返回
        -------
        pd.DataFrame
            处理后的数据
        """
        return df.dropna()


class FillnaProcessor(InstProcessor):
    """填充 NaN 值的处理器
    
    用指定的方法填充 NaN 值
    """
    
    def __init__(self, method="ffill"):
        """初始化
        
        参数
        ----------
        method : str
            填充方法，可选值：'ffill', 'bfill', 'mean', 'median', 'zero'
        """
        self.method = method
    
    def __call__(self, df: pd.DataFrame, instrument, *args, **kwargs):
        """处理数据
        
        参数
        ----------
        df : pd.DataFrame
            处理器的原始数据或前一个处理器的结果
        instrument : str
            金融工具代码
        *args, **kwargs : 
            其他参数
            
        返回
        -------
        pd.DataFrame
            处理后的数据
        """
        if self.method == "ffill":
            return df.fillna(method="ffill")
        elif self.method == "bfill":
            return df.fillna(method="bfill")
        elif self.method == "mean":
            return df.fillna(df.mean())
        elif self.method == "median":
            return df.fillna(df.median())
        elif self.method == "zero":
            return df.fillna(0)
        else:
            logger.warning(f"未知的填充方法: {self.method}，使用前向填充")
            return df.fillna(method="ffill")


class FilterProcessor(InstProcessor):
    """过滤处理器
    
    根据条件过滤数据
    """
    
    def __init__(self, condition):
        """初始化
        
        参数
        ----------
        condition : str
            过滤条件，例如："close > 10"
        """
        self.condition = condition
    
    def __call__(self, df: pd.DataFrame, instrument, *args, **kwargs):
        """处理数据
        
        参数
        ----------
        df : pd.DataFrame
            处理器的原始数据或前一个处理器的结果
        instrument : str
            金融工具代码
        *args, **kwargs : 
            其他参数
            
        返回
        -------
        pd.DataFrame
            处理后的数据
        """
        try:
            return df.query(self.condition)
        except Exception as e:
            logger.error(f"过滤条件错误: {e}")
            return df
