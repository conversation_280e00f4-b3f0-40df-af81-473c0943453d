#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database storage for data system

This module provides database-based storage for data.
"""

import os
import re
import pickle
import json
import time
import numpy as np
import pandas as pd
import psycopg2
from pathlib import Path
from typing import List, Dict, Any, Union, Optional, Tuple, Iterable
from sqlalchemy import create_engine, text

from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.conf.config import C
from .storage import CalendarStorage, InstrumentStorage, FeatureStorage, CalVT, InstVT, InstKT

# Set up logger
logger = get_loki_logger(__name__).logger


def get_db_engine(db_config):
    """
    Get SQLAlchemy database engine with connection pooling

    Args:
        db_config: Database configuration dictionary

    Returns:
        SQLAlchemy engine
    """
    user = db_config.get('user', 'postgres')
    password = db_config.get('password', '')
    host = db_config.get('host', 'localhost')
    database = db_config.get('database', 'gold_beast')
    port = db_config.get('port', '5432')

    connection_string = f"postgresql://{user}:{password}@{host}:{port}/{database}"
    return create_engine(connection_string, pool_size=5, max_overflow=10)


class DatabaseStorageMixin:
    """DatabaseStorageMixin, applicable to DatabaseXXXStorage

    Subclasses need to have connection_config, storage_name attributes
    """

    @property
    def connection_config(self):
        return getattr(self, "_connection_config", {})

    @property
    def db_type(self):
        return self.connection_config.get("type", "mongodb")

    @property
    def connection(self):
        """Get database connection"""
        if not hasattr(self, "_connection"):
            if self.db_type == "mongodb":
                import pymongo
                mongo_url = self.connection_config.get("url", "mongodb://localhost:27017")
                db_name = self.connection_config.get("db_name", "gold_beast")
                client = pymongo.MongoClient(mongo_url)
                self._connection = client[db_name]
            elif self.db_type in ["postgresql", "mysql", "sqlite"]:
                from sqlalchemy import create_engine
                db_type = self.connection_config.get("type", "postgresql")
                user = self.connection_config.get("user", "postgres")
                password = self.connection_config.get("password", "")
                host = self.connection_config.get("host", "localhost")
                port = self.connection_config.get("port", "5432")
                database = self.connection_config.get("database", "gold_beast")

                connection_string = f"{db_type}://{user}:{password}@{host}:{port}/{database}"
                self._connection = create_engine(connection_string)
            else:
                raise ValueError(f"Unsupported database type: {self.db_type}")
        return self._connection

    def check(self):
        """check database connection

        Raises
        -------
        ValueError
        """
        try:
            conn = self.connection
            if self.db_type == "mongodb":
                # Test MongoDB connection
                conn.command("ping")
            else:
                # Test SQL connection
                conn.connect()
            return True
        except Exception as e:
            raise ValueError(f"Database connection failed: {e}")


class DatabaseCalendarStorage(DatabaseStorageMixin, CalendarStorage):
    """Database storage for calendar data"""

    def __init__(self, freq: str = "day", future: bool = False, connection_config: dict = None, **kwargs):
        """
        Initialize calendar storage.

        Args:
            freq: Frequency
            future: Whether to include future dates
            connection_config: Database connection configuration
            **kwargs: Additional parameters
        """
        super(DatabaseCalendarStorage, self).__init__(freq, future, **kwargs)
        self._connection_config = connection_config or {}
        self.enable_read_cache = True  # TODO: make it configurable
        self.region = C.get("region", "CN")

    @property
    def collection_name(self) -> str:
        """Get collection name based on frequency and future flag"""
        return f"calendars_{self.freq.lower()}_{'future' if self.future else 'history'}"

    def _read_calendar(self) -> List[CalVT]:
        """Read calendar data from database"""
        self.check()

        if self.db_type == "mongodb":
            collection = self.connection[self.collection_name]
            data = list(collection.find({}, {"_id": 0, "date": 1}))
            return [item["date"] for item in data]
        else:
            # SQL implementation
            query = f"SELECT date FROM {self.collection_name} ORDER BY date"
            try:
                result = pd.read_sql(query, self.connection)
                return result["date"].tolist()
            except Exception as e:
                logger.error(f"Failed to read calendar from SQL database: {e}")
                return []

    def _write_calendar(self, values: Iterable[CalVT]) -> None:
        """Write calendar data to database"""
        self.check()

        if self.db_type == "mongodb":
            collection = self.connection[self.collection_name]

            # Convert data to MongoDB format
            documents = [{"date": date} for date in values]

            # Drop existing collection and insert new data
            collection.drop()
            if documents:
                collection.insert_many(documents)
        else:
            # SQL implementation
            df = pd.DataFrame({"date": values})

            # Create table if not exists
            create_table_query = f"""
            CREATE TABLE IF NOT EXISTS {self.collection_name} (
                date VARCHAR(20) PRIMARY KEY
            )
            """

            with self.connection.begin() as conn:
                conn.execute(create_table_query)

                # Delete existing data
                conn.execute(f"DELETE FROM {self.collection_name}")

                # Insert new data
                if not df.empty:
                    df.to_sql(self.collection_name, self.connection, if_exists="append", index=False)

    @property
    def data(self) -> List[CalVT]:
        """Get calendar data"""
        self.check()

        # If cache is enabled, then return cache directly
        if self.enable_read_cache:
            key = "db_calendar_" + self.collection_name
            if key not in C.mem_cache:
                C.mem_cache[key] = self._read_calendar()
            _calendar = C.mem_cache[key]
        else:
            _calendar = self._read_calendar()

        return _calendar

    def clear(self) -> None:
        """Clear calendar data"""
        self._write_calendar(values=[])

    def extend(self, values: Iterable[CalVT]) -> None:
        """Extend calendar with new values"""
        current = self._read_calendar()
        current.extend(values)
        self._write_calendar(current)

    def index(self, value: CalVT) -> int:
        """Get index of a value in calendar"""
        self.check()
        calendar = self._read_calendar()
        try:
            return calendar.index(value)
        except ValueError:
            raise ValueError(f"Value {value} not found in calendar")

    def insert(self, index: int, value: CalVT) -> None:
        """Insert value at index"""
        calendar = self._read_calendar()
        calendar.insert(index, value)
        self._write_calendar(values=calendar)

    def remove(self, value: CalVT) -> None:
        """Remove value from calendar"""
        self.check()
        calendar = self._read_calendar()
        try:
            calendar.remove(value)
            self._write_calendar(values=calendar)
        except ValueError:
            raise ValueError(f"Value {value} not found in calendar")

    def __setitem__(self, i: Union[int, slice], values: Union[CalVT, Iterable[CalVT]]) -> None:
        """Set item(s) at index/slice"""
        calendar = self._read_calendar()
        calendar[i] = values
        self._write_calendar(values=calendar)

    def __delitem__(self, i: Union[int, slice]) -> None:
        """Delete item(s) at index/slice"""
        self.check()
        calendar = self._read_calendar()
        del calendar[i]
        self._write_calendar(values=calendar)

    def __getitem__(self, i: Union[int, slice]) -> Union[CalVT, List[CalVT]]:
        """Get item(s) at index/slice"""
        self.check()
        return self._read_calendar()[i]

    def __len__(self) -> int:
        """Get calendar length"""
        return len(self.data)


class DatabaseInstrumentStorage(DatabaseStorageMixin, InstrumentStorage):
    """Database storage for instrument data"""

    INSTRUMENT_SEP = "\t"
    INSTRUMENT_START_FIELD = "start_datetime"
    INSTRUMENT_END_FIELD = "end_datetime"
    SYMBOL_FIELD_NAME = "instrument"

    def __init__(self, market: str = "all", freq: str = "day", connection_config: dict = None, **kwargs):
        """
        Initialize instrument storage.

        Args:
            market: Market name
            freq: Frequency
            connection_config: Database connection configuration
            **kwargs: Additional parameters
        """
        super(DatabaseInstrumentStorage, self).__init__(market, freq, **kwargs)
        self._connection_config = connection_config or {}

    @property
    def collection_name(self) -> str:
        """Get collection name based on market and frequency"""
        return f"instruments_{self.market.lower()}_{self.freq.lower()}"

    def _read_instrument(self) -> Dict[InstKT, InstVT]:
        """Read instrument data from database"""
        self.check()

        if self.db_type == "mongodb":
            collection = self.connection[self.collection_name]
            data = list(collection.find({}, {"_id": 0}))

            # Convert to the expected format
            result = {}
            for item in data:
                instrument_id = item.pop("instrument_id")
                result[instrument_id] = item

            return result
        else:
            # SQL implementation
            try:
                query = f"SELECT * FROM {self.collection_name}"
                df = pd.read_sql(query, self.connection)

                if df.empty:
                    return {}

                # Convert to the expected format
                result = {}
                for _, row in df.iterrows():
                    instrument_id = row.pop("instrument_id")
                    result[instrument_id] = row.to_dict()

                return result
            except Exception as e:
                logger.error(f"Failed to read instruments from SQL database: {e}")
                return {}

    def _write_instrument(self, data: Dict[InstKT, InstVT] = None) -> None:
        """Write instrument data to database"""
        self.check()

        if not data:
            data = {}

        if self.db_type == "mongodb":
            collection = self.connection[self.collection_name]

            # Convert data to MongoDB format
            documents = []
            for instrument_id, info in data.items():
                doc = {"instrument_id": instrument_id}
                doc.update(info)
                documents.append(doc)

            # Drop existing collection and insert new data
            collection.drop()
            if documents:
                collection.insert_many(documents)
        else:
            # SQL implementation
            # Convert data to DataFrame
            rows = []
            for instrument_id, info in data.items():
                row = {"instrument_id": instrument_id}
                row.update(info)
                rows.append(row)

            df = pd.DataFrame(rows) if rows else pd.DataFrame(columns=["instrument_id"])

            # Create table if not exists
            if not df.empty:
                columns = ", ".join([f"{col} TEXT" for col in df.columns])
                create_table_query = f"""
                CREATE TABLE IF NOT EXISTS {self.collection_name} (
                    {columns},
                    PRIMARY KEY (instrument_id)
                )
                """
            else:
                create_table_query = f"""
                CREATE TABLE IF NOT EXISTS {self.collection_name} (
                    instrument_id TEXT PRIMARY KEY
                )
                """

            with self.connection.begin() as conn:
                conn.execute(create_table_query)

                # Delete existing data
                conn.execute(f"DELETE FROM {self.collection_name}")

                # Insert new data
                if not df.empty:
                    df.to_sql(self.collection_name, self.connection, if_exists="append", index=False)

    @property
    def data(self) -> Dict[InstKT, InstVT]:
        """Get instrument data"""
        self.check()
        return self._read_instrument()

    def clear(self) -> None:
        """Clear instrument data"""
        self._write_instrument(data={})

    def update(self, *args, **kwargs) -> None:
        """Update instrument data"""
        if len(args) > 1:
            raise TypeError(f"update expected at most 1 arguments, got {len(args)}")

        inst = self._read_instrument()

        if args:
            other = args[0]  # type: dict
            if isinstance(other, dict):
                for key in other:
                    inst[key] = other[key]
            elif hasattr(other, "keys"):
                for key in other.keys():
                    inst[key] = other[key]
            else:
                for key, value in other:
                    inst[key] = value

        for key, value in kwargs.items():
            inst[key] = value

        self._write_instrument(inst)

    def __setitem__(self, k: InstKT, v: InstVT) -> None:
        """Set instrument data for key"""
        inst = self._read_instrument()
        inst[k] = v
        self._write_instrument(inst)

    def __delitem__(self, k: InstKT) -> None:
        """Delete instrument data for key"""
        self.check()
        inst = self._read_instrument()
        del inst[k]
        self._write_instrument(inst)

    def __getitem__(self, k: InstKT) -> InstVT:
        """Get instrument data for key"""
        self.check()
        return self._read_instrument()[k]

    def __len__(self) -> int:
        """Get number of instruments"""
        return len(self.data)


class DatabaseFeatureStorage(DatabaseStorageMixin, FeatureStorage):
    """Database storage for feature data"""

    def __init__(self, instrument: str, field: str, freq: str = "day", connection_config: dict = None, **kwargs):
        """
        Initialize feature storage.

        Args:
            instrument: Instrument ID
            field: Feature field
            freq: Frequency
            connection_config: Database connection configuration
            **kwargs: Additional parameters
        """
        super(DatabaseFeatureStorage, self).__init__(instrument, field, freq, **kwargs)
        self._connection_config = connection_config or {}

    @property
    def collection_name(self) -> str:
        """Get collection name based on frequency"""
        return f"features_{self.freq.lower()}"

    @property
    def table_name(self) -> str:
        """Get table name for SQL databases"""
        return self.collection_name

    def clear(self):
        """Clear feature data"""
        self.check()

        if self.db_type == "mongodb":
            collection = self.connection[self.collection_name]
            collection.delete_many({"instrument": self.instrument, "field": self.field})
        else:
            # SQL implementation
            query = f"""
            DELETE FROM {self.table_name}
            WHERE instrument = '{self.instrument}' AND field = '{self.field}'
            """

            with self.connection.begin() as conn:
                conn.execute(query)

    @property
    def data(self) -> pd.Series:
        """Get feature data"""
        return self[:]

    @property
    def start_index(self) -> Union[int, None]:
        """Get start index of feature data"""
        self.check()

        if self.db_type == "mongodb":
            collection = self.connection[self.collection_name]
            result = collection.find(
                {"instrument": self.instrument, "field": self.field},
                {"_id": 0, "index": 1}
            ).sort("index", 1).limit(1)

            result = list(result)
            if result:
                return result[0]["index"]
        else:
            # SQL implementation
            query = f"""
            SELECT MIN(idx) as min_idx
            FROM {self.table_name}
            WHERE instrument = '{self.instrument}' AND field = '{self.field}'
            """

            try:
                result = pd.read_sql(query, self.connection)
                if not result.empty and not pd.isna(result["min_idx"].iloc[0]):
                    return int(result["min_idx"].iloc[0])
            except Exception as e:
                logger.error(f"Failed to get start index: {e}")

        return None

    @property
    def end_index(self) -> Union[int, None]:
        """Get end index of feature data"""
        self.check()

        if self.db_type == "mongodb":
            collection = self.connection[self.collection_name]
            result = collection.find(
                {"instrument": self.instrument, "field": self.field},
                {"_id": 0, "index": 1}
            ).sort("index", -1).limit(1)

            result = list(result)
            if result:
                return result[0]["index"]
        else:
            # SQL implementation
            query = f"""
            SELECT MAX(idx) as max_idx
            FROM {self.table_name}
            WHERE instrument = '{self.instrument}' AND field = '{self.field}'
            """

            try:
                result = pd.read_sql(query, self.connection)
                if not result.empty and not pd.isna(result["max_idx"].iloc[0]):
                    return int(result["max_idx"].iloc[0])
            except Exception as e:
                logger.error(f"Failed to get end index: {e}")

        return None

    def write(self, data_array: Union[List, np.ndarray, Tuple], index: int = None) -> None:
        """Write data to feature storage"""
        if len(data_array) == 0:
            logger.info(
                "len(data_array) == 0, write"
                "if you need to clear the FeatureStorage, please execute: FeatureStorage.clear"
            )
            return

        self.check()

        # Determine start index
        if index is None:
            end_idx = self.end_index
            if end_idx is None:
                index = 0
            else:
                index = end_idx + 1

        # Create indices
        indices = range(index, index + len(data_array))

        if self.db_type == "mongodb":
            collection = self.connection[self.collection_name]

            # Convert data to MongoDB format
            documents = []
            for idx, value in zip(indices, data_array):
                doc = {
                    "instrument": self.instrument,
                    "field": self.field,
                    "index": idx,
                    "value": float(value)
                }
                documents.append(doc)

            # Insert new data
            if documents:
                # Delete any existing data with the same indices
                collection.delete_many({
                    "instrument": self.instrument,
                    "field": self.field,
                    "index": {"$in": list(indices)}
                })

                collection.insert_many(documents)
        else:
            # SQL implementation
            # Create table if not exists
            create_table_query = f"""
            CREATE TABLE IF NOT EXISTS {self.table_name} (
                instrument TEXT,
                field TEXT,
                idx INTEGER,
                value REAL,
                PRIMARY KEY (instrument, field, idx)
            )
            """

            # Convert data to DataFrame
            df = pd.DataFrame({
                "instrument": [self.instrument] * len(data_array),
                "field": [self.field] * len(data_array),
                "idx": indices,
                "value": data_array
            })

            with self.connection.begin() as conn:
                conn.execute(create_table_query)

                # Delete any existing data with the same indices
                idx_list = ", ".join(map(str, indices))
                if idx_list:
                    delete_query = f"""
                    DELETE FROM {self.table_name}
                    WHERE instrument = '{self.instrument}' AND field = '{self.field}'
                    AND idx IN ({idx_list})
                    """
                    conn.execute(delete_query)

                # Insert new data
                df.to_sql(self.table_name, self.connection, if_exists="append", index=False)

    def rewrite(self, data: Union[List, np.ndarray, Tuple], index: int):
        """overwrite all data in FeatureStorage with data"""
        self.clear()
        self.write(data, index)

    def __getitem__(self, i: Union[int, slice]) -> Union[Tuple[int, float], pd.Series]:
        """Get item(s) at index/slice"""
        self.check()

        if isinstance(i, int):
            if self.db_type == "mongodb":
                collection = self.connection[self.collection_name]
                result = collection.find_one(
                    {"instrument": self.instrument, "field": self.field, "index": i},
                    {"_id": 0, "index": 1, "value": 1}
                )

                if result:
                    return result["index"], result["value"]
                else:
                    return None, None
            else:
                # SQL implementation
                query = f"""
                SELECT idx, value
                FROM {self.table_name}
                WHERE instrument = '{self.instrument}' AND field = '{self.field}'
                AND idx = {i}
                """

                try:
                    result = pd.read_sql(query, self.connection)
                    if not result.empty:
                        return int(result["idx"].iloc[0]), float(result["value"].iloc[0])
                    else:
                        return None, None
                except Exception as e:
                    logger.error(f"Failed to get item: {e}")
                    return None, None
        elif isinstance(i, slice):
            start = i.start
            stop = i.stop

            # Get start and end indices if not provided
            if start is None:
                start_idx = self.start_index
                start = start_idx if start_idx is not None else 0
            if stop is None:
                end_idx = self.end_index
                stop = end_idx + 1 if end_idx is not None else 0

            if self.db_type == "mongodb":
                collection = self.connection[self.collection_name]
                result = collection.find(
                    {
                        "instrument": self.instrument,
                        "field": self.field,
                        "index": {"$gte": start, "$lt": stop}
                    },
                    {"_id": 0, "index": 1, "value": 1}
                ).sort("index", 1)

                result = list(result)
                if result:
                    indices = [item["index"] for item in result]
                    values = [item["value"] for item in result]
                    return pd.Series(values, index=indices)
                else:
                    return pd.Series(dtype=np.float32)
            else:
                # SQL implementation
                query = f"""
                SELECT idx, value
                FROM {self.table_name}
                WHERE instrument = '{self.instrument}' AND field = '{self.field}'
                AND idx >= {start} AND idx < {stop}
                ORDER BY idx
                """

                try:
                    result = pd.read_sql(query, self.connection)
                    if not result.empty:
                        return pd.Series(result["value"].values, index=result["idx"].values)
                    else:
                        return pd.Series(dtype=np.float32)
                except Exception as e:
                    logger.error(f"Failed to get items: {e}")
                    return pd.Series(dtype=np.float32)
        else:
            raise TypeError(f"type(i) = {type(i)}")

    def __len__(self) -> int:
        """Get length of feature data"""
        self.check()

        if self.db_type == "mongodb":
            collection = self.connection[self.collection_name]
            return collection.count_documents({"instrument": self.instrument, "field": self.field})
        else:
            # SQL implementation
            query = f"""
            SELECT COUNT(*) as count
            FROM {self.table_name}
            WHERE instrument = '{self.instrument}' AND field = '{self.field}'
            """

            try:
                result = pd.read_sql(query, self.connection)
                if not result.empty:
                    return int(result["count"].iloc[0])
                else:
                    return 0
            except Exception as e:
                logger.error(f"Failed to get length: {e}")
                return 0
