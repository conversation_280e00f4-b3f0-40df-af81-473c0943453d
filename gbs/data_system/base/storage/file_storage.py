#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
File storage for data system

This module provides file-based storage for data.
"""

import os
import re
import copy
import pickle
import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Union, Optional, Tuple, Iterable

from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.utils.paths import DATA_DIR
from gbs.core.conf.config import C, DEFAULT_FREQ
from .storage import CalendarStorage, InstrumentStorage, FeatureStorage, CalVT, InstVT, InstKT

# Set up logger
logger = get_loki_logger(__name__).logger


class FileStorageMixin:
    """FileStorageMixin, applicable to FileXXXStorage

    Subclasses need to have provider_uri, freq, storage_name, file_name attributes
    """

    # NOTE: provider_uri priority:
    # 1. self._provider_uri : if provider_uri is provided.
    # 2. provider_uri in config.C

    @property
    def provider_uri(self):
        return C["provider_uri"] if getattr(self, "_provider_uri", None) is None else self._provider_uri

    @property
    def dpm(self):
        from gbs.core.conf.config import DataPathManager

        # 获取mount_path，如果C中没有，则使用默认值
        mount_path = C.get("mount_path", {})
        if not isinstance(mount_path, dict):
            mount_path = {"day": mount_path}

        return (
            C.dpm
            if getattr(self, "_provider_uri", None) is None
            else DataPathManager(self._provider_uri, mount_path)
        )

    @property
    def support_freq(self) -> List[str]:
        _v = "_support_freq"
        if hasattr(self, _v):
            return getattr(self, _v)

        if len(self.provider_uri) == 1 and DEFAULT_FREQ in self.provider_uri:
            freq_l = filter(
                lambda _freq: not _freq.endswith("_future"),
                map(lambda x: x.stem, self.dpm.get_data_uri(DEFAULT_FREQ).joinpath("calendars").glob("*.json")),
            )
        else:
            freq_l = self.provider_uri.keys()

        freq_l = list(freq_l)
        setattr(self, _v, freq_l)
        return freq_l

    @property
    def uri(self) -> Path:
        if self.freq not in self.support_freq:
            raise ValueError(f"{self.storage_name}: {self.provider_uri} does not contain data for {self.freq}")
        return self.dpm.get_data_uri(self.freq).joinpath(f"{self.storage_name}s", self.file_name)

    def check(self):
        """check self.uri

        Raises
        -------
        ValueError
        """
        if not self.uri.exists():
            raise ValueError(f"{self.storage_name} not exists: {self.uri}")


class FileCalendarStorage(FileStorageMixin, CalendarStorage):
    """File storage for calendar data"""

    def __init__(self, freq: str = "day", future: bool = False, provider_uri: dict = None, **kwargs):
        """
        Initialize calendar storage.

        Args:
            freq: Frequency
            future: Whether to include future dates
            provider_uri: Provider URI
            **kwargs: Additional parameters
        """
        super(FileCalendarStorage, self).__init__(freq, future, **kwargs)
        self.future = future
        from gbs.core.conf.config import DataPathManager
        self._provider_uri = None if provider_uri is None else DataPathManager.format_provider_uri(provider_uri)
        self.enable_read_cache = True  # TODO: make it configurable
        self.region = C["region"]

    @property
    def file_name(self) -> str:
        """Get file name based on frequency and future flag"""
        return f"{self._freq_file}_future.json" if self.future else f"{self._freq_file}.json".lower()

    @property
    def _freq_file(self) -> str:
        """the freq to read from file"""
        if not hasattr(self, "_freq_file_cache"):
            freq = self.freq
            if freq not in self.support_freq:
                # NOTE: uri
                # 1. If `uri` does not exist
                # - Get the `min_uri` of the closest `freq` under the same "directory" as the `uri`
                # - Read data from `min_uri` and resample to `freq`
                freq = self.freq  # TODO: implement frequency resampling if needed
                if freq is None:
                    raise ValueError(f"can't find a freq from {self.support_freq} that can resample to {self.freq}!")
            self._freq_file_cache = freq
        return self._freq_file_cache

    def _read_calendar(self) -> List[CalVT]:
        """Read calendar data from file"""
        if not self.uri.exists():
            self._write_calendar(values=[])

        with self.uri.open("r") as fp:
            res = []
            try:
                res = json.load(fp)
            except Exception as e:
                logger.error(f"Failed to load calendar: {e}")
            return res

    def _write_calendar(self, values: Iterable[CalVT], mode: str = "w"):
        """Write calendar data to file"""
        os.makedirs(self.uri.parent, exist_ok=True)
        with self.uri.open(mode=mode) as fp:
            try:
                json.dump(values, fp)
            except Exception as e:
                logger.error(f"Failed to save calendar: {e}")

    @property
    def data(self) -> List[CalVT]:
        """Get calendar data"""
        self.check()

        # If cache is enabled, then return cache directly
        if self.enable_read_cache:
            key = "orig_file" + str(self.uri)
            if not hasattr(C, 'mem_cache'):
                C['mem_cache'] = {}
            if key not in C['mem_cache']:
                C['mem_cache'][key] = self._read_calendar()
            _calendar = C['mem_cache'][key]
        else:
            _calendar = self._read_calendar()

        # TODO: implement frequency resampling if needed
        return _calendar

    def clear(self) -> None:
        """Clear calendar data"""
        self._write_calendar(values=[])

    def extend(self, values: Iterable[CalVT]) -> None:
        """Extend calendar with new values"""
        current = self._read_calendar()
        current.extend(values)
        self._write_calendar(current)

    def index(self, value: CalVT) -> int:
        """Get index of a value in calendar"""
        self.check()
        calendar = self._read_calendar()
        try:
            return calendar.index(value)
        except ValueError:
            raise ValueError(f"Value {value} not found in calendar")

    def insert(self, index: int, value: CalVT) -> None:
        """Insert value at index"""
        calendar = self._read_calendar()
        calendar.insert(index, value)
        self._write_calendar(values=calendar)

    def remove(self, value: CalVT) -> None:
        """Remove value from calendar"""
        self.check()
        calendar = self._read_calendar()
        try:
            calendar.remove(value)
            self._write_calendar(values=calendar)
        except ValueError:
            raise ValueError(f"Value {value} not found in calendar")

    def __setitem__(self, i: Union[int, slice], values: Union[CalVT, Iterable[CalVT]]) -> None:
        """Set item(s) at index/slice"""
        calendar = self._read_calendar()
        calendar[i] = values
        self._write_calendar(values=calendar)

    def __delitem__(self, i: Union[int, slice]) -> None:
        """Delete item(s) at index/slice"""
        self.check()
        calendar = self._read_calendar()
        del calendar[i]
        self._write_calendar(values=calendar)

    def __getitem__(self, i: Union[int, slice]) -> Union[CalVT, List[CalVT]]:
        """Get item(s) at index/slice"""
        self.check()
        return self._read_calendar()[i]

    def __len__(self) -> int:
        """Get calendar length"""
        return len(self.data)


class FileInstrumentStorage(FileStorageMixin, InstrumentStorage):
    """File storage for instrument data"""

    INSTRUMENT_SEP = "\t"
    INSTRUMENT_START_FIELD = "start_datetime"
    INSTRUMENT_END_FIELD = "end_datetime"
    SYMBOL_FIELD_NAME = "instrument"

    def __init__(self, market: str = "all", freq: str = "day", provider_uri: dict = None, **kwargs):
        """
        Initialize instrument storage.

        Args:
            market: Market name
            freq: Frequency
            provider_uri: Provider URI
            **kwargs: Additional parameters
        """
        super(FileInstrumentStorage, self).__init__(market, freq, **kwargs)
        from gbs.core.conf.config import DataPathManager
        self._provider_uri = None if provider_uri is None else DataPathManager.format_provider_uri(provider_uri)

    @property
    def file_name(self) -> str:
        """Get file name based on market and frequency"""
        return f"{self.market.lower()}.txt"

    def _read_instrument(self) -> Dict[InstKT, InstVT]:
        """Read instrument data from file"""
        if not self.uri.exists():
            self._write_instrument()

        _instruments = dict()
        try:
            # 尝试读取txt文件
            with open(self.uri, "r") as f:
                for line in f:
                    line = line.strip()
                    if line:
                        # 处理行数据，支持多种格式
                        parts = line.split()
                        if len(parts) >= 1:
                            # 第一部分是股票代码
                            instrument_id = parts[0]

                            # 创建时间范围列表
                            time_spans = []

                            # 如果有日期范围信息（至少需要两个额外的部分）
                            if len(parts) >= 3:
                                try:
                                    # 尝试解析日期
                                    start_date = pd.Timestamp(parts[1])
                                    end_date = pd.Timestamp(parts[2])
                                    time_spans.append((start_date, end_date))
                                except ValueError:
                                    # 如果日期解析失败，记录警告但继续处理
                                    logger.warning(f"无法解析日期范围: {parts[1:3]} 对于股票 {instrument_id}")

                            # 保存股票信息
                            _instruments[instrument_id] = time_spans if time_spans else []

                return _instruments
        except Exception as e:
            logger.error(f"Failed to load instruments: {e}")
            return {}

    def _write_instrument(self, data: Dict[InstKT, InstVT] = None) -> None:
        """Write instrument data to file"""
        os.makedirs(self.uri.parent, exist_ok=True)

        if not data:
            with self.uri.open("w") as _:
                pass
            return

        try:
            with open(self.uri, "w") as f:
                for key in data:
                    f.write(f"{key}\n")
        except Exception as e:
            logger.error(f"Failed to save instruments: {e}")

    @property
    def data(self) -> Dict[InstKT, InstVT]:
        """Get instrument data"""
        self.check()
        return self._read_instrument()

    def clear(self) -> None:
        """Clear instrument data"""
        self._write_instrument(data={})

    def update(self, *args, **kwargs) -> None:
        """Update instrument data"""
        if len(args) > 1:
            raise TypeError(f"update expected at most 1 arguments, got {len(args)}")

        inst = self._read_instrument()

        if args:
            other = args[0]  # type: dict
            if isinstance(other, dict):
                for key in other:
                    inst[key] = other[key]
            elif hasattr(other, "keys"):
                for key in other.keys():
                    inst[key] = other[key]
            else:
                for key, value in other:
                    inst[key] = value

        for key, value in kwargs.items():
            inst[key] = value

        self._write_instrument(inst)

    def __setitem__(self, k: InstKT, v: InstVT) -> None:
        """Set instrument data for key"""
        inst = self._read_instrument()
        inst[k] = v
        self._write_instrument(inst)

    def __delitem__(self, k: InstKT) -> None:
        """Delete instrument data for key"""
        self.check()
        inst = self._read_instrument()
        del inst[k]
        self._write_instrument(inst)

    def __getitem__(self, k: InstKT) -> InstVT:
        """Get instrument data for key"""
        self.check()
        return self._read_instrument()[k]

    def __len__(self) -> int:
        """Get number of instruments"""
        return len(self.data)


class FileFeatureStorage(FileStorageMixin, FeatureStorage):
    """File storage for feature data"""

    def __init__(self, instrument: str, field: str, freq: str = "day", provider_uri: dict = None, **kwargs):
        """
        Initialize feature storage.

        Args:
            instrument: Instrument ID
            field: Feature field
            freq: Frequency
            provider_uri: Provider URI
            **kwargs: Additional parameters
        """
        super(FileFeatureStorage, self).__init__(instrument, field, freq, **kwargs)
        from gbs.core.conf.config import DataPathManager
        self._provider_uri = None if provider_uri is None else DataPathManager.format_provider_uri(provider_uri)

    @property
    def file_name(self) -> str:
        """Get file name based on instrument, field and frequency"""
        return f"{self.instrument.lower()}/{self.field.lower()}.{self.freq.lower()}.bin"

    def clear(self):
        """Clear feature data"""
        os.makedirs(self.uri.parent, exist_ok=True)
        with self.uri.open("wb") as _:
            pass

    @property
    def data(self) -> pd.Series:
        """Get feature data"""
        return self[:]

    @property
    def start_index(self) -> Union[int, None]:
        """Get start index of feature data"""
        if not self.uri.exists():
            return None

        try:
            # 尝试使用numpy读取.bin文件（qlib格式）
            try:
                # 使用numpy.fromfile读取二进制文件，指定dtype为小端浮点数（对应qlib中的"<f"）
                with open(self.uri, "rb") as f:
                    # 读取第一个值作为起始索引
                    start_index = int(np.frombuffer(f.read(4), dtype="<f")[0])
                    return start_index
            except Exception as bin_error:
                # 如果失败，尝试使用简单的方式读取
                logger.warning(f"使用qlib格式读取.bin文件失败: {bin_error}，尝试使用简单方式读取")

                # 使用numpy.fromfile读取二进制文件，指定dtype为float32
                data_array = np.fromfile(str(self.uri), dtype=np.float32)
                if len(data_array) > 0:
                    return 0  # 假设索引从0开始

                # 如果还是失败，尝试使用pickle读取.pkl文件
                if len(data_array) == 0:
                    logger.warning(f"使用简单方式读取.bin文件失败，尝试使用pickle读取")
                    with open(self.uri, "rb") as f:
                        data = pickle.load(f)
                        if isinstance(data, pd.Series) and len(data) > 0:
                            return data.index[0]
        except Exception as e:
            logger.error(f"Failed to get start index: {e}")

        return None

    @property
    def end_index(self) -> Union[int, None]:
        """Get end index of feature data"""
        if not self.uri.exists():
            return None

        try:
            # 尝试使用numpy读取.bin文件（qlib格式）
            try:
                # 使用numpy.fromfile读取二进制文件，指定dtype为小端浮点数（对应qlib中的"<f"）
                with open(self.uri, "rb") as f:
                    # 读取第一个值作为起始索引
                    start_index = int(np.frombuffer(f.read(4), dtype="<f")[0])

                    # 读取剩余数据
                    data = np.frombuffer(f.read(), dtype="<f")

                    # 计算结束索引
                    end_index = start_index + len(data) - 1
                    return end_index
            except Exception as bin_error:
                # 如果失败，尝试使用简单的方式读取
                logger.warning(f"使用qlib格式读取.bin文件失败: {bin_error}，尝试使用简单方式读取")

                # 使用numpy.fromfile读取二进制文件，指定dtype为float32
                data_array = np.fromfile(str(self.uri), dtype=np.float32)
                if len(data_array) > 0:
                    return len(data_array) - 1  # 假设索引从0开始

                # 如果还是失败，尝试使用pickle读取.pkl文件
                if len(data_array) == 0:
                    logger.warning(f"使用简单方式读取.bin文件失败，尝试使用pickle读取")
                    with open(self.uri, "rb") as f:
                        data = pickle.load(f)
                        if isinstance(data, pd.Series) and len(data) > 0:
                            return data.index[-1]
        except Exception as e:
            logger.error(f"Failed to get end index: {e}")

        return None

    def write(self, data_array: Union[List, np.ndarray, Tuple], index: int = None) -> None:
        """Write data to feature storage"""
        if len(data_array) == 0:
            logger.info(
                "len(data_array) == 0, write"
                "if you need to clear the FeatureStorage, please execute: FeatureStorage.clear"
            )
            return

        os.makedirs(self.uri.parent, exist_ok=True)

        if not self.uri.exists():
            # New file
            index = 0 if index is None else index
            indices = range(index, index + len(data_array))
            series = pd.Series(data_array, index=indices)
            with self.uri.open("wb") as f:
                pickle.dump(series, f)
        else:
            # Existing file
            with self.uri.open("rb") as f:
                series = pickle.load(f)

            if index is None:
                # Append
                if len(series) > 0:
                    index = series.index[-1] + 1
                else:
                    index = 0

            # Create new indices
            indices = range(index, index + len(data_array))
            new_series = pd.Series(data_array, index=indices)

            # Merge series
            series = pd.concat([series, new_series]).sort_index()

            # Remove duplicates (keep last)
            series = series[~series.index.duplicated(keep='last')]

            with self.uri.open("wb") as f:
                pickle.dump(series, f)

    def __getitem__(self, i: Union[int, slice]) -> Union[Tuple[int, float], pd.Series]:
        """Get item(s) at index/slice"""
        if not self.uri.exists():
            if isinstance(i, int):
                return None, None
            elif isinstance(i, slice):
                return pd.Series(dtype=np.float32)
            else:
                raise TypeError(f"type(i) = {type(i)}")

        try:
            # 使用numpy读取.bin文件（参考qlib的实现）
            try:
                # 使用numpy.fromfile读取二进制文件，指定dtype为小端浮点数（对应qlib中的"<f"）
                with open(self.uri, "rb") as f:
                    # 读取第一个值作为起始索引
                    start_index = int(np.frombuffer(f.read(4), dtype="<f")[0])

                    # 读取剩余数据
                    data = np.frombuffer(f.read(), dtype="<f")

                    # 创建索引（从start_index开始的连续整数）
                    indices = np.arange(start_index, start_index + len(data))

                    # 创建pandas Series
                    series = pd.Series(data, index=indices)

                    # 尝试将整数索引转换为日期索引
                    try:
                        from gbs.data_system.base.data import D
                        calendar = D.calendar(freq=self.freq)
                        # 将整数索引转换为日期索引
                        date_index = pd.DatetimeIndex([calendar[idx] for idx in indices if 0 <= idx < len(calendar)])
                        # 重新索引series
                        if len(date_index) == len(series):
                            series.index = date_index
                    except Exception as cal_error:
                        logger.warning(f"无法将整数索引转换为日期索引: {cal_error}，使用整数索引")

                    logger.info(f"成功使用numpy读取.bin文件: {self.uri}, 数据点数: {len(series)}")
            except Exception as bin_error:
                # 如果失败，尝试使用简单的方式读取
                logger.warning(f"使用qlib格式读取.bin文件失败: {bin_error}，尝试使用简单方式读取")

                # 使用numpy.fromfile读取二进制文件，指定dtype为float32
                data_array = np.fromfile(str(self.uri), dtype=np.float32)

                # 创建索引（假设索引是连续的整数）
                indices = np.arange(len(data_array))

                # 创建pandas Series
                series = pd.Series(data_array, index=indices)
                logger.info(f"成功使用简单方式读取.bin文件: {self.uri}, 数据点数: {len(series)}")

                # 如果还是失败，尝试使用pickle读取.pkl文件
                if len(series) == 0:
                    logger.warning(f"使用简单方式读取.bin文件失败，尝试使用pickle读取")
                    with open(self.uri, "rb") as f:
                        series = pickle.load(f)

            if isinstance(i, int):
                if i in series.index:
                    return i, series[i]
                else:
                    return None, None
            elif isinstance(i, slice):
                start = i.start
                stop = i.stop

                if start is None:
                    start = series.index[0] if len(series) > 0 else 0
                if stop is None:
                    stop = series.index[-1] + 1 if len(series) > 0 else 0

                # Get data in range
                mask = (series.index >= start) & (series.index < stop)
                return series[mask]
            else:
                raise TypeError(f"type(i) = {type(i)}")
        except Exception as e:
            logger.error(f"Failed to get item: {e}")
            if isinstance(i, int):
                return None, None
            elif isinstance(i, slice):
                return pd.Series(dtype=np.float32)
            else:
                raise TypeError(f"type(i) = {type(i)}")

    def __len__(self) -> int:
        """Get length of feature data"""
        if not self.uri.exists():
            return 0

        try:
            # 使用numpy读取.bin文件（参考qlib的实现）
            try:
                # 使用numpy.fromfile读取二进制文件，指定dtype为小端浮点数（对应qlib中的"<f"）
                with open(self.uri, "rb") as f:
                    # 跳过第一个值（起始索引）
                    f.read(4)

                    # 读取剩余数据
                    data = np.frombuffer(f.read(), dtype="<f")
                    return len(data)
            except Exception as bin_error:
                # 如果失败，尝试使用简单的方式读取
                logger.warning(f"使用qlib格式读取.bin文件失败: {bin_error}，尝试使用简单方式读取")

                # 使用numpy.fromfile读取二进制文件，指定dtype为float32
                data_array = np.fromfile(str(self.uri), dtype=np.float32)
                return len(data_array)
        except Exception as e:
            logger.error(f"Failed to get length: {e}")
            return 0
