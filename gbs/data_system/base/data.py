#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main data interface for data system

This module provides the main interface for accessing data in the system.
It follows the design pattern of qlib's data module.
"""

import abc
import re
import copy
import bisect
import numpy as np
import pandas as pd
from datetime import datetime
from typing import List, Union, Optional, Dict, Any, Tuple

from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.utils.mod import init_instance_by_config
from gbs.core.utils.date_utils import convert_to_datetime

from .expression import (
    Expression,
    Feature,
    ExpressionOps
)

from .cache import H, DiskDatasetCache

# Set up logger
logger = get_loki_logger(__name__).logger


class ProviderBackendMixin:
    """
    Helper class to make providers based on storage backend more convenient

    It is not necessary to inherit this class if the provider doesn't rely on backend storage
    """

    def get_default_backend(self):
        """
        Get default backend configuration

        Returns:
            Dict: Default backend configuration
        """
        backend = {}
        provider_name = re.findall("[A-Z][^A-Z]*", self.__class__.__name__)[-2]
        # Set default storage class
        backend.setdefault("class", f"File{provider_name}Storage")
        # Set default storage module
        backend.setdefault("module_path", "gbs.data_system.base.storage.file_storage")
        return backend

    def backend_obj(self, **kwargs):
        """
        Get backend object

        Args:
            **kwargs: Additional parameters for backend

        Returns:
            Object: Backend object
        """
        backend = self.backend if hasattr(self, 'backend') and self.backend else self.get_default_backend()
        backend = copy.deepcopy(backend)
        backend.setdefault("kwargs", {}).update(**kwargs)
        return init_instance_by_config(backend)


class CalendarProvider(abc.ABC):
    """
    Calendar provider base class

    Provides calendar data for different time frequencies
    """

    def calendar(self, start_time=None, end_time=None, freq="day", future=False):
        """
        Get calendar for a specific time range and frequency

        Args:
            start_time: Start time
            end_time: End time
            freq: Frequency (day, week, month, etc.)
            future: Whether to include future dates

        Returns:
            List of calendar dates
        """
        _calendar, _calendar_index = self._get_calendar(freq, future)

        # Handle None values
        if start_time == "None":
            start_time = None
        if end_time == "None":
            end_time = None

        # Convert to timestamps
        if start_time:
            start_time = pd.Timestamp(start_time)
            if start_time > _calendar[-1]:
                return np.array([])
        else:
            start_time = _calendar[0]

        if end_time:
            end_time = pd.Timestamp(end_time)
            if end_time < _calendar[0]:
                return np.array([])
        else:
            end_time = _calendar[-1]

        # Get indices
        _, _, si, ei = self.locate_index(start_time, end_time, freq, future)
        return _calendar[si:ei + 1]

    def locate_index(self, start_time, end_time, freq="day", future=False):
        """
        Locate the start and end time indices in the calendar

        Args:
            start_time: Start time
            end_time: End time
            freq: Frequency
            future: Whether to include future dates

        Returns:
            Tuple: (start_time, end_time, start_index, end_index)
        """
        start_time = pd.Timestamp(start_time)
        end_time = pd.Timestamp(end_time)

        calendar, calendar_index = self._get_calendar(freq=freq, future=future)

        # Find start index
        if start_time not in calendar_index:
            try:
                start_time = calendar[bisect.bisect_left(calendar, start_time)]
            except IndexError as e:
                raise IndexError(
                    "Start time is beyond the available calendar range. "
                    "If you want to include future dates, use future=True"
                ) from e

        start_index = calendar_index[start_time]

        # Find end index
        if end_time not in calendar_index:
            end_time = calendar[bisect.bisect_right(calendar, end_time) - 1]

        end_index = calendar_index[end_time]

        return start_time, end_time, start_index, end_index

    def _get_calendar(self, freq, future):
        """
        Get calendar from cache or load it

        Args:
            freq: Frequency
            future: Whether to include future dates

        Returns:
            Tuple: (calendar, calendar_index)
        """
        flag = f"{freq}_future_{future}"
        if flag not in H["c"]:
            _calendar = np.array(self.load_calendar(freq, future))
            _calendar_index = {x: i for i, x in enumerate(_calendar)}
            H["c"][flag] = (_calendar, _calendar_index)

        return H["c"][flag]

    @abc.abstractmethod
    def load_calendar(self, freq, future):
        """
        Load calendar data from storage

        Args:
            freq: Frequency
            future: Whether to include future dates

        Returns:
            List of calendar dates
        """
        raise NotImplementedError("Subclass of CalendarProvider must implement `load_calendar` method")


class InstrumentProvider(abc.ABC):
    """
    Instrument provider base class

    Provides instrument data for different markets
    """

    @staticmethod
    def instruments(market: Union[List, str] = "all", filter_pipe: Union[List, None] = None):
        """
        Get instrument configuration

        Args:
            market: Market name or list of instruments
            filter_pipe: List of filters

        Returns:
            Dict or List: Instrument configuration
        """
        if isinstance(market, list):
            return market

        if filter_pipe is None:
            filter_pipe = []

        config = {"market": market, "filter_pipe": []}

        # Add filters
        for filter_t in filter_pipe:
            if isinstance(filter_t, dict):
                _config = filter_t
            else:
                from gbs.data_system import filter as F
                if hasattr(filter_t, 'to_config'):
                    _config = filter_t.to_config()
                else:
                    raise TypeError(f"Unsupported filter type: {type(filter_t)}")

            config["filter_pipe"].append(_config)

        return config

    @abc.abstractmethod
    def list_instruments(self, instruments, start_time=None, end_time=None, freq="day", as_list=False):
        """
        List instruments based on configuration

        Args:
            instruments: Instrument configuration
            start_time: Start time
            end_time: End time
            freq: Frequency
            as_list: Whether to return as list

        Returns:
            Dict or List: Instruments
        """
        raise NotImplementedError("Subclass of InstrumentProvider must implement `list_instruments` method")

    # Instrument types
    LIST = "LIST"
    DICT = "DICT"
    CONF = "CONF"

    @classmethod
    def get_inst_type(cls, inst):
        """
        Get instrument type

        Args:
            inst: Instrument

        Returns:
            str: Instrument type
        """
        if isinstance(inst, str):
            # 如果是字符串，将其视为市场名称
            return cls.CONF
        if isinstance(inst, dict) and "market" in inst:
            return cls.CONF
        if isinstance(inst, dict):
            return cls.DICT
        if isinstance(inst, (list, tuple, pd.Index, np.ndarray)):
            return cls.LIST
        raise ValueError(f"Unknown instrument type: {type(inst)}")


class FeatureProvider(abc.ABC):
    """
    Feature provider base class

    Provides feature data for instruments
    """

    @abc.abstractmethod
    def feature(self, instruments, fields, start_time=None, end_time=None, freq="day"):
        """
        Get feature data for instruments

        Args:
            instruments: Instrument ID or list of IDs
            fields: Feature field or list of fields
            start_time: Start time
            end_time: End time
            freq: Frequency

        Returns:
            pd.DataFrame: Feature data
        """
        raise NotImplementedError("Subclass of FeatureProvider must implement `feature` method")


class PITProvider(abc.ABC):
    """
    Point-in-Time provider base class

    Provides point-in-time data for instruments
    """

    @abc.abstractmethod
    def period_feature(
        self,
        instrument,
        field,
        start_index: int,
        end_index: int,
        cur_time: pd.Timestamp,
        period: Optional[int] = None,
    ) -> pd.Series:
        """
        Get historical periods data series

        Args:
            instrument: Instrument ID
            field: Feature field
            start_index: Start index relative to current time
            end_index: End index relative to current time
            cur_time: Current time
            period: Specific period to query

        Returns:
            pd.Series: Period feature data
        """
        raise NotImplementedError("Subclass of PITProvider must implement `period_feature` method")


class ExpressionProvider(abc.ABC):
    """
    Expression provider base class

    Provides expression data for instruments
    """

    def __init__(self):
        """
        Initialize expression provider
        """
        self.expression_instance_cache = {}

    def get_expression_instance(self, field):
        """
        Get expression instance for a field

        Args:
            field: Expression field

        Returns:
            Object: Expression instance
        """
        try:
            if field in self.expression_instance_cache:
                expression = self.expression_instance_cache[field]
            else:
                from gbs.data_system.utils import parse_field
                base_field, ops = parse_field(field)

                # 如果没有操作符，直接使用基本字段
                if ops is None:
                    expression_str = base_field
                else:
                    # 否则，构建表达式字符串
                    expression_str = f"{base_field}__{ops}"

                # 确保表达式字符串是有效的
                if not isinstance(expression_str, str):
                    expression_str = str(expression_str)

                # 尝试评估表达式
                try:
                    expression = eval(expression_str)
                except (NameError, SyntaxError) as e:
                    # 如果评估失败，尝试使用基本字段
                    logger.warning(f"无法评估表达式 '{expression_str}': {str(e)}，尝试使用基本字段 '{base_field}'")
                    expression = base_field

                self.expression_instance_cache[field] = expression
        except NameError as e:
            logger.exception(
                f"ERROR: field [{field}] contains invalid operator/variable [{str(e).split()[1]}]"
            )
            raise
        except SyntaxError:
            logger.exception(f"ERROR: field [{field}] contains invalid syntax")
            raise
        except Exception as e:
            logger.exception(f"ERROR: failed to get expression instance for field [{field}]: {str(e)}")
            raise

        return expression

    @abc.abstractmethod
    def expression(self, instrument, field, start_time=None, end_time=None, freq="day") -> pd.Series:
        """
        Get expression data for an instrument

        Args:
            instrument: Instrument ID
            field: Expression field
            start_time: Start time
            end_time: End time
            freq: Frequency

        Returns:
            pd.Series: Expression data
        """
        raise NotImplementedError("Subclass of ExpressionProvider must implement `expression` method")


class DatasetProvider(abc.ABC):
    """
    Dataset provider base class

    Provides dataset data for multiple instruments and fields
    """

    @abc.abstractmethod
    def dataset(self, instruments, fields, start_time=None, end_time=None, freq="day", inst_processors=None):
        """
        Get dataset for multiple instruments and fields

        Args:
            instruments: List or dict of instruments
            fields: List of fields
            start_time: Start time
            end_time: End time
            freq: Frequency
            inst_processors: List of instrument processors

        Returns:
            pd.DataFrame: Dataset
        """
        raise NotImplementedError("Subclass of DatasetProvider must implement `dataset` method")

    @staticmethod
    def get_instruments_d(instruments, freq):
        """
        Get instruments dictionary

        Args:
            instruments: Instruments
            freq: Frequency

        Returns:
            Dict: Instruments dictionary
        """
        from gbs.data_system import D

        # Handle different types of instruments
        if isinstance(instruments, str):
            # If instruments is a string, convert it to a dict
            instruments = {"market": instruments, "filter_pipe": []}
            instruments_d = D.list_instruments(instruments=instruments, freq=freq)
        elif isinstance(instruments, dict) and "market" in instruments:
            # If instruments is a dict with market key, use it directly
            instruments_d = D.list_instruments(instruments=instruments, freq=freq)
        elif isinstance(instruments, (list, tuple, pd.Index, np.ndarray)):
            # If instruments is a list, use it directly
            instruments_d = instruments
        elif isinstance(instruments, dict):
            # If instruments is a dict without market key, use it directly
            instruments_d = instruments
        else:
            raise ValueError(f"Unknown instrument type: {type(instruments)}")

        return instruments_d

    @staticmethod
    def get_column_names(fields):
        """
        Get column names for fields

        Args:
            fields: Fields

        Returns:
            List: Column names
        """
        if fields is None:
            # 如果fields为None，返回默认字段列表
            return ["open", "high", "low", "close", "volume"]

        if isinstance(fields, list):
            return [str(f) for f in fields]

        if isinstance(fields, dict):
            return list(fields.values())

        raise ValueError(f"Unsupported fields type: {type(fields)}")


class LocalCalendarProvider(CalendarProvider, ProviderBackendMixin):
    """
    Local calendar provider

    Provides calendar data from local storage
    """

    def __init__(self, name: str = None, backend: Dict = None, **kwargs):
        """
        Initialize local calendar provider

        Args:
            name: Provider name
            backend: Backend configuration
            **kwargs: Additional parameters
        """
        super().__init__()
        self.name = name
        self.backend = backend

    def load_calendar(self, freq, future=False):
        """
        Load calendar data from storage

        Args:
            freq: Frequency
            future: Whether to include future dates

        Returns:
            List of calendar dates
        """
        try:
            backend_obj = self.backend_obj(freq=freq, future=future).data
            return [pd.Timestamp(x) for x in backend_obj]
        except ValueError:
            if future:
                logger.warning(f"Load calendar error: freq={freq}, future={future}; return current calendar!")
                logger.warning("Future calendar is not available")
                # Try again without future dates
                try:
                    backend_obj = self.backend_obj(freq=freq, future=False).data
                    return [pd.Timestamp(x) for x in backend_obj]
                except Exception as e:
                    logger.error(f"Failed to load calendar: {str(e)}")
                    # Return a default calendar
                    return [pd.Timestamp('2000-01-01'), pd.Timestamp('2000-01-02'), pd.Timestamp('2000-01-03')]
            else:
                logger.error(f"Failed to load calendar: freq={freq}, future={future}")
                # Return a default calendar
                return [pd.Timestamp('2000-01-01'), pd.Timestamp('2000-01-02'), pd.Timestamp('2000-01-03')]
        except Exception as e:
            logger.error(f"Failed to load calendar: {str(e)}")
            # Return a default calendar
            return [pd.Timestamp('2000-01-01'), pd.Timestamp('2000-01-02'), pd.Timestamp('2000-01-03')]

    def calendar(self, start_time=None, end_time=None, freq="day", future=False):
        """
        Get calendar for a specific time range and frequency

        Args:
            start_time: Start time
            end_time: End time
            freq: Frequency (day, week, month, etc.)
            future: Whether to include future dates

        Returns:
            List of calendar dates
        """
        # Get calendar from cache or load it
        if f"{freq}_future_{future}" not in H["c"]:
            _calendar = np.array(self.load_calendar(freq, future))
            _calendar_index = {x: i for i, x in enumerate(_calendar)}
            H["c"][f"{freq}_future_{future}"] = (_calendar, _calendar_index)

        _calendar, _calendar_index = H["c"][f"{freq}_future_{future}"]

        # Handle None values
        if start_time == "None":
            start_time = None
        if end_time == "None":
            end_time = None

        # Convert to timestamps
        if start_time:
            start_time = pd.Timestamp(start_time)
            if start_time > _calendar[-1]:
                return np.array([])
        else:
            start_time = _calendar[0]

        if end_time:
            end_time = pd.Timestamp(end_time)
            if end_time < _calendar[0]:
                return np.array([])
        else:
            end_time = _calendar[-1]

        # Find indices
        if start_time not in _calendar_index:
            try:
                start_time = _calendar[bisect.bisect_left(_calendar, start_time)]
            except IndexError:
                raise IndexError("Start time is beyond the available calendar range")

        start_index = _calendar_index[start_time]

        if end_time not in _calendar_index:
            end_time = _calendar[bisect.bisect_right(_calendar, end_time) - 1]

        end_index = _calendar_index[end_time]

        return _calendar[start_index:end_index + 1]


class LocalInstrumentProvider(InstrumentProvider, ProviderBackendMixin):
    """
    Local instrument provider

    Provides instrument data from local storage
    """

    def __init__(self, name: str = None, backend: Dict = None, **kwargs):
        """
        Initialize local instrument provider

        Args:
            name: Provider name
            backend: Backend configuration
            **kwargs: Additional parameters
        """
        super().__init__()
        self.name = name
        self.backend = backend

    def _load_instruments(self, market, freq):
        """
        Load instruments from storage

        Args:
            market: Market name
            freq: Frequency

        Returns:
            Dict of instruments
        """
        return self.backend_obj(market=market, freq=freq).data

    def list_instruments(self, instruments, start_time=None, end_time=None, freq="day", as_list=False):
        """
        List instruments based on configuration

        Args:
            instruments: Instrument configuration
            start_time: Start time
            end_time: End time
            freq: Frequency
            as_list: Whether to return as list

        Returns:
            Dict or list of instruments
        """
        # Convert instruments to standard format if it's a string
        if isinstance(instruments, str):
            instruments = {"market": instruments, "filter_pipe": []}

        # Ensure instruments is a dict with market key
        if not isinstance(instruments, dict) or "market" not in instruments:
            raise ValueError(f"Unsupported instruments type: {type(instruments)}")

        market = instruments["market"]

        # Check cache
        if market in H["i"]:
            _instruments = H["i"][market]
        else:
            _instruments = self._load_instruments(market, freq=freq)
            H["i"][market] = _instruments

        # Get calendar boundaries
        from gbs.data_system.base.data import D
        cal = D.calendar(freq=freq)
        start_time = pd.Timestamp(start_time or cal[0])
        end_time = pd.Timestamp(end_time or cal[-1])

        # Filter instruments by time range with robust error handling
        _instruments_filtered = {}
        for inst, spans in _instruments.items():
            valid_spans = []

            # 如果spans是空列表，使用默认的时间范围
            if not spans:
                valid_spans.append((start_time, end_time))
            else:
                # 处理每个时间范围
                for span in spans:
                    try:
                        # 安全地转换日期时间字符串
                        if isinstance(span, tuple) and len(span) >= 2:
                            span_start = pd.Timestamp(span[0]) if isinstance(span[0], (str, pd.Timestamp, datetime)) else start_time
                            span_end = pd.Timestamp(span[1]) if isinstance(span[1], (str, pd.Timestamp, datetime)) else end_time

                            # 检查日期是否有效
                            if span_start <= span_end:
                                valid_spans.append((max(start_time, span_start), min(end_time, span_end)))
                    except (ValueError, TypeError) as e:
                        logger.warning(f"处理instrument {inst}的时间范围时出错: {e}, 跳过此范围: {span}")
                        continue

            # 如果没有有效的时间范围，使用默认的时间范围
            if not valid_spans:
                valid_spans.append((start_time, end_time))

            _instruments_filtered[inst] = valid_spans

        # Remove empty spans
        _instruments_filtered = {key: value for key, value in _instruments_filtered.items() if value}

        # Apply filters
        filter_pipe = instruments.get("filter_pipe", [])
        for filter_config in filter_pipe:
            from data_system import filter as F
            filter_t = getattr(F, filter_config["filter_type"]).from_config(filter_config)
            _instruments_filtered = filter_t(_instruments_filtered, start_time, end_time, freq)

        # Return as list if requested
        if as_list:
            return list(_instruments_filtered)

        return _instruments_filtered


class LocalFeatureProvider(FeatureProvider, ProviderBackendMixin):
    """
    Local feature provider

    Provides feature data from local storage
    """

    def __init__(self, name: str = None, backend: Dict = None, **kwargs):
        """
        Initialize local feature provider

        Args:
            name: Provider name
            backend: Backend configuration
            **kwargs: Additional parameters
        """
        super().__init__()
        self.name = name
        self.backend = backend

    def feature(self, instruments=None, fields=None, start_time=None, end_time=None, freq="day"):
        """
        Get feature data for instruments

        Args:
            instruments: Instrument ID or list of IDs
            fields: Feature field or list of fields
            start_time: Start time
            end_time: End time
            freq: Frequency

        Returns:
            pd.DataFrame: Feature data
        """
        # 处理单个instrument和多个instruments的情况
        if instruments is None:
            return pd.DataFrame()

        # 确保instruments是列表
        if not isinstance(instruments, (list, tuple, np.ndarray)):
            instruments = [instruments]

        # 确保instruments中的股票代码都是小写
        instruments = [inst.lower() if isinstance(inst, str) else inst for inst in instruments]

        # 处理单个field和多个fields的情况
        if fields is None:
            fields = ["open", "high", "low", "close", "volume"]
        elif not isinstance(fields, (list, tuple, np.ndarray)):
            fields = [fields]

        # 创建结果DataFrame
        result_data = []

        # 处理每个instrument和field
        for instrument in instruments:
            # Validate and prepare parameters
            # Convert instrument code if needed
            from gbs.data_system.utils import code_to_fname
            instrument_fname = code_to_fname(instrument)

            for field in fields:
                field_str = str(field)
                # 如果字段以$开头，则移除$前缀
                if field_str.startswith('$'):
                    field_str = field_str[1:]

                # Get data from backend
                try:
                    # 获取数据
                    data = self.backend_obj(instrument=instrument_fname, field=field_str, freq=freq).data

                    # 筛选时间范围
                    if start_time is not None or end_time is not None:
                        try:
                            # 检查索引类型
                            if hasattr(data.index, 'dtype') and pd.api.types.is_datetime64_any_dtype(data.index):
                                # 索引已经是datetime类型，直接筛选
                                if start_time is not None:
                                    start_time_pd = pd.Timestamp(start_time)
                                    data = data[data.index >= start_time_pd]
                                if end_time is not None:
                                    end_time_pd = pd.Timestamp(end_time)
                                    data = data[data.index <= end_time_pd]
                            else:
                                # 索引不是datetime类型，使用整数索引
                                logger.info(f"索引类型为 {type(data.index)}，使用整数索引进行时间筛选")
                                try:
                                    from gbs.data_system.base.data import D
                                    _, _, start_index, end_index = D.locate_index(start_time, end_time, freq=freq)
                                    data = data.iloc[start_index:end_index + 1]  # 包含end_index
                                except Exception as e2:
                                    logger.warning(f"整数索引筛选失败: {str(e2)}，使用原始数据")
                        except Exception as e:
                            logger.warning(f"时间筛选失败: {str(e)}，使用原始数据")

                    # 添加instrument和field信息
                    if isinstance(data, pd.Series):
                        data = data.to_frame(name=field_str)
                    data['instrument'] = instrument
                    data['field'] = field_str

                    # 添加到结果列表
                    result_data.append(data)
                except Exception as e:
                    logger.error(f"获取特征数据失败: instrument={instrument}, field={field_str}, error={str(e)}")

        # 合并结果
        if not result_data:
            return pd.DataFrame()

        try:
            result = pd.concat(result_data, axis=0)
            # 设置多级索引
            result = result.reset_index()
            result = result.rename(columns={'index': 'datetime'})
            result = result.set_index(['instrument', 'datetime', 'field'])
            return result
        except Exception as e:
            logger.error(f"合并特征数据失败: {str(e)}")
            return pd.DataFrame()


class LocalExpressionProvider(ExpressionProvider):
    """
    Local expression provider

    Provides expression data from local storage
    """

    def __init__(self, time2idx: bool = True):
        """
        Initialize local expression provider

        Args:
            time2idx: Whether to convert time to index
        """
        super().__init__()
        self.time2idx = time2idx

    def expression(self, instrument, field, start_time=None, end_time=None, freq="day"):
        """
        Get expression data for an instrument

        Args:
            instrument: Instrument ID
            field: Expression field
            start_time: Start time
            end_time: End time
            freq: Frequency

        Returns:
            Expression data as pandas Series
        """
        try:
            # 尝试直接从特征文件加载数据
            from gbs.data_system.utils import get_feature_path, read_bin_file

            # 获取特征文件路径
            file_path = get_feature_path(instrument, field, freq)

            # 检查文件是否存在
            import os
            if os.path.exists(file_path):
                # 直接从文件加载数据
                series = read_bin_file(file_path, start_time, end_time)
                logger.info(f"从文件加载数据成功: {file_path}, 数据点数: {len(series)}")
                return series
            else:
                logger.warning(f"特征文件不存在: {file_path}")
        except Exception as e:
            logger.warning(f"直接从文件加载数据失败: {str(e)}，尝试使用表达式加载")

        # 如果直接加载失败，尝试使用表达式加载
        try:
            # Get expression instance
            expression = self.get_expression_instance(field)

            # 检查表达式是否是字符串
            if isinstance(expression, str):
                # 如果是字符串，尝试直接从特征文件加载数据
                from gbs.data_system.utils import get_feature_path, read_bin_file
                file_path = get_feature_path(instrument, expression, freq)
                if os.path.exists(file_path):
                    series = read_bin_file(file_path, start_time, end_time)
                    logger.info(f"从文件加载数据成功: {file_path}, 数据点数: {len(series)}")
                    return series
                else:
                    logger.warning(f"特征文件不存在: {file_path}")
                    # 返回空Series
                    return pd.Series(dtype=np.float32)

            # Convert time to index if needed
            if self.time2idx:
                from gbs.data_system.base.data import D
                _, _, start_index, end_index = D.locate_index(start_time, end_time, freq=freq)

                # Get extended window size for expressions that need look-back data
                try:
                    lft_etd, rght_etd = expression.get_extended_window_size()
                    query_start, query_end = max(0, start_index - lft_etd), end_index + rght_etd
                except (AttributeError, TypeError):
                    # 如果表达式没有get_extended_window_size方法，使用默认值
                    query_start, query_end = start_index, end_index
            else:
                start_index, end_index = query_start, query_end = start_time, end_time

            try:
                # Load expression data
                series = expression.load(instrument, query_start, query_end, freq)
            except Exception as e:
                logger.debug(
                    f"Loading expression error: "
                    f"instrument={instrument}, field=({field}), start_time={start_time}, end_time={end_time}, freq={freq}. "
                    f"error info: {str(e)}"
                )
                # 返回空Series
                return pd.Series(dtype=np.float32)

            # Ensure consistent data type
            try:
                series = series.astype(np.float32)
            except (ValueError, TypeError):
                pass

            # Slice to requested range
            if not series.empty:
                series = series.loc[start_index:end_index]

            return series
        except Exception as e:
            logger.error(f"加载表达式数据失败: {str(e)}")
            # 返回空Series
            return pd.Series(dtype=np.float32)


class LocalDatasetProvider(DatasetProvider):
    """
    Local dataset provider

    Provides dataset data from local storage
    """

    def __init__(self, align_time: bool = True):
        """
        Initialize local dataset provider

        Args:
            align_time: Whether to align time to calendar
        """
        super().__init__()
        self.align_time = align_time

    def dataset(self, instruments, fields, start_time=None, end_time=None, freq="day", inst_processors=None):
        """
        Get dataset for multiple instruments and fields

        Args:
            instruments: List or dict of instruments
            fields: List of fields
            start_time: Start time
            end_time: End time
            freq: Frequency
            inst_processors: List of instrument processors

        Returns:
            Dataset as pandas DataFrame
        """
        inst_processors = inst_processors or []
        instruments_d = self.get_instruments_d(instruments, freq)
        column_names = self.get_column_names(fields)

        if self.align_time:
            # Align to calendar
            from gbs.data_system.base.data import D
            cal = D.calendar(start_time, end_time, freq)
            if len(cal) == 0:
                return pd.DataFrame(
                    index=pd.MultiIndex.from_arrays([[], []], names=("instrument", "datetime")),
                    columns=column_names
                )

            start_time = cal[0]
            end_time = cal[-1]

        # Process data
        data = self.dataset_processor(instruments_d, column_names, start_time, end_time, freq, inst_processors)
        return data

    @staticmethod
    def dataset_processor(instruments_d, column_names, start_time, end_time, freq, inst_processors=None):
        """
        Process dataset data

        Args:
            instruments_d: Instruments dict
            column_names: Column names
            start_time: Start time
            end_time: End time
            freq: Frequency
            inst_processors: List of instrument processors

        Returns:
            Processed dataset
        """
        inst_processors = inst_processors or []

        # Normalize column names
        from gbs.core.utils.data import normalize_cache_fields
        normalize_column_names = normalize_cache_fields(column_names)

        # Determine number of workers
        try:
            # 尝试从正确的路径导入Config
            try:
                from gbs.core.conf.config import Config
            except ImportError:
                try:
                    from gbs.core.utils.config import Config
                except ImportError:
                    # 如果两个路径都导入失败，使用默认值
                    import multiprocessing
                    workers = max(min(multiprocessing.cpu_count(), len(instruments_d)), 1)
                    logger.warning(f"导入Config失败，使用默认工作线程数: {workers}")
                    return workers

            # 尝试使用get_kernels方法
            try:
                workers = max(min(Config.get_kernels(freq), len(instruments_d)), 1)
            except (AttributeError, TypeError):
                # 如果方法不存在或调用失败，使用默认值
                import multiprocessing
                workers = max(min(multiprocessing.cpu_count(), len(instruments_d)), 1)
                logger.info(f"Config.get_kernels方法不可用，使用默认工作线程数: {workers}")
        except Exception as e:
            # 捕获所有异常，确保不会中断程序
            import multiprocessing
            workers = max(min(multiprocessing.cpu_count(), len(instruments_d)), 1)
            logger.warning(f"确定工作线程数时出错: {str(e)}，使用默认工作线程数: {workers}")

        # Create iterator
        if isinstance(instruments_d, dict):
            it = instruments_d.items()
        else:
            it = zip(instruments_d, [None] * len(instruments_d))

        # Create tasks
        inst_l = []
        task_l = []

        for inst, spans in it:
            inst_l.append(inst)
            task_l.append(
                LocalDatasetProvider.inst_calculator(
                    inst, start_time, end_time, freq, normalize_column_names, spans, inst_processors
                )
            )

        # Process data
        try:
            # 尝试导入ParallelExt
            try:
                from gbs.core.utils.parallel import ParallelExt
                data = dict(zip(inst_l, ParallelExt(n_jobs=workers)(task_l)))
            except ImportError:
                # 如果导入失败，使用串行处理
                logger.warning("无法导入ParallelExt，使用串行处理")
                data = {}
                for i, inst in enumerate(inst_l):
                    data[inst] = task_l[i]()
        except Exception as e:
            # 捕获所有异常，确保不会中断程序
            logger.error(f"并行处理数据时出错: {str(e)}")
            # 返回空数据
            data = {}

        # Filter and sort data
        new_data = {}
        for inst in sorted(data.keys()):
            if len(data[inst]) > 0:
                new_data[inst] = data[inst]

        # Combine data
        if len(new_data) > 0:
            data = pd.concat(new_data, names=["instrument"], sort=False)
            data = DiskDatasetCache.cache_to_origin_data(data, column_names)
        else:
            data = pd.DataFrame(
                index=pd.MultiIndex.from_arrays([[], []], names=("instrument", "datetime")),
                columns=column_names,
                dtype=np.float32,
            )

        return data

    @staticmethod
    def inst_calculator(inst, start_time, end_time, freq, column_names, spans=None, inst_processors=None):
        """
        Calculate data for a single instrument

        Args:
            inst: Instrument ID
            start_time: Start time
            end_time: End time
            freq: Frequency
            column_names: Column names
            spans: Time spans
            inst_processors: List of instrument processors

        Returns:
            Instrument data
        """
        try:
            inst_processors = inst_processors or []

            # Get data for each field
            obj = {}
            for field in column_names:
                try:
                    from gbs.data_system.base.data import D
                    obj[field] = D.expression(inst, field, start_time, end_time, freq)
                except Exception as e:
                    logger.warning(f"获取表达式 '{field}' 数据时出错: {str(e)}")
                    # 创建一个空的Series作为替代
                    obj[field] = pd.Series(dtype=np.float32)

            # 检查是否所有字段都为空
            if all(len(series) == 0 for series in obj.values()):
                logger.warning(f"所有字段都为空，返回空DataFrame: instrument={inst}, fields={column_names}")
                return pd.DataFrame(columns=column_names)

            # Create DataFrame
            data = pd.DataFrame(obj)

            # Convert index to datetime if needed
            if not data.empty and not np.issubdtype(data.index.dtype, np.dtype("M")):
                try:
                    from gbs.data_system.base.data import D
                    _calendar = D.calendar(freq=freq)
                    data.index = _calendar[data.index.values.astype(int)]
                    data.index.names = ["datetime"]
                except Exception as e:
                    logger.warning(f"转换索引为日期时出错: {str(e)}")
                    # 如果转换失败，尝试重置索引
                    data = data.reset_index()
                    data['datetime'] = pd.date_range(start='2000-01-01', periods=len(data), freq='D')
                    data = data.set_index('datetime')

            # Filter by spans
            if not data.empty and spans is not None:
                try:
                    mask = np.zeros(len(data), dtype=bool)
                    for begin, end in spans:
                        mask |= (data.index >= begin) & (data.index <= end)
                    data = data[mask]
                except Exception as e:
                    logger.warning(f"按时间段过滤数据时出错: {str(e)}")

            # Apply instrument processors
            for _processor in inst_processors:
                if _processor:
                    try:
                        _processor_obj = init_instance_by_config(_processor)
                        data = _processor_obj(data, instrument=inst)
                    except Exception as e:
                        logger.warning(f"应用处理器 '{_processor}' 时出错: {str(e)}")

            return data
        except Exception as e:
            logger.error(f"计算instrument数据时出错: instrument={inst}, error={str(e)}")
            return pd.DataFrame(columns=column_names)


class D:
    """
    Global data interface

    This class provides a global interface for accessing data in the system.
    It initializes the providers and provides methods for accessing data.
    """

    # Provider instances
    _cal_provider = None
    _inst_provider = None
    _feat_provider = None
    _expr_provider = None
    _dataset_provider = None

    @classmethod
    def initialize(cls, provider_config=None):
        """
        Initialize data providers

        Args:
            provider_config: Provider configuration
        """
        if provider_config is None:
            # Default configuration
            provider_config = {
                "calendar": {"class": "LocalCalendarProvider"},
                "instrument": {"class": "LocalInstrumentProvider"},
                "feature": {"class": "LocalFeatureProvider"},
                "expression": {"class": "LocalExpressionProvider"},
                "dataset": {"class": "LocalDatasetProvider"},
            }

        # Initialize providers
        for provider_type, config in provider_config.items():
            # Add module path if not specified
            if isinstance(config, dict) and "module_path" not in config:
                config["module_path"] = "gbs.data_system.base.data"

            if provider_type == "calendar":
                cls._cal_provider = init_instance_by_config(config)
            elif provider_type == "instrument":
                cls._inst_provider = init_instance_by_config(config)
            elif provider_type == "feature":
                cls._feat_provider = init_instance_by_config(config)
            elif provider_type == "expression":
                cls._expr_provider = init_instance_by_config(config)
            elif provider_type == "dataset":
                cls._dataset_provider = init_instance_by_config(config)

    @classmethod
    def calendar(cls, start_time=None, end_time=None, freq="day", future=False):
        """
        Get calendar

        Args:
            start_time: Start time
            end_time: End time
            freq: Frequency
            future: Whether to include future dates

        Returns:
            Calendar data
        """
        if cls._cal_provider is None:
            cls.initialize()

        return cls._cal_provider.calendar(start_time, end_time, freq, future)

    @classmethod
    def instruments(cls, market="all", filter_pipe=None):
        """
        Get instruments

        Args:
            market: Market name or list of instruments
            filter_pipe: List of filters to apply

        Returns:
            Instruments data
        """
        if cls._inst_provider is None:
            cls.initialize()

        return cls._inst_provider.instruments(market, filter_pipe)

    @classmethod
    def list_instruments(cls, instruments=None, start_time=None, end_time=None, freq="day", as_list=False):
        """
        List instruments

        Args:
            instruments: Instruments configuration
            start_time: Start time
            end_time: End time
            freq: Frequency
            as_list: Whether to return as list

        Returns:
            List of instruments
        """
        if cls._inst_provider is None:
            cls.initialize()

        if instruments is None:
            instruments = cls.instruments()
        elif isinstance(instruments, str):
            # 如果instruments是字符串，将其转换为配置字典
            instruments = cls.instruments(market=instruments)

        return cls._inst_provider.list_instruments(instruments, start_time, end_time, freq, as_list)

    @classmethod
    def feature(cls, instruments=None, fields=None, start_time=None, end_time=None, freq="day"):
        """
        Get feature data

        Args:
            instruments: Instrument ID or list of IDs
            fields: Feature field or list of fields
            start_time: Start time
            end_time: End time
            freq: Frequency

        Returns:
            pd.DataFrame: Feature data
        """
        if cls._feat_provider is None:
            cls.initialize()

        return cls._feat_provider.feature(instruments, fields, start_time, end_time, freq)

    @classmethod
    def expression(cls, instrument, field, start_time=None, end_time=None, freq="day"):
        """
        Get expression data

        Args:
            instrument: Instrument ID
            field: Expression field
            start_time: Start time
            end_time: End time
            freq: Frequency

        Returns:
            Expression data
        """
        if cls._expr_provider is None:
            cls.initialize()

        return cls._expr_provider.expression(instrument, field, start_time, end_time, freq)

    @classmethod
    def dataset(cls, instruments, fields=None, start_time=None, end_time=None, freq="day", inst_processors=None):
        """
        Get dataset

        Args:
            instruments: List or dict of instruments
            fields: List of fields, defaults to None which will use default fields
            start_time: Start time
            end_time: End time
            freq: Frequency
            inst_processors: List of instrument processors

        Returns:
            Dataset data
        """
        if cls._dataset_provider is None:
            cls.initialize()

        # 如果fields为None，使用默认字段列表
        if fields is None:
            fields = ["open", "high", "low", "close", "volume"]
            logger.info(f"使用默认字段列表: {fields}")

        try:
            return cls._dataset_provider.dataset(instruments, fields, start_time, end_time, freq, inst_processors)
        except Exception as e:
            logger.error(f"获取数据集时出错: {str(e)}")
            # 返回一个空的DataFrame，保持与预期相同的结构
            return pd.DataFrame(
                index=pd.MultiIndex.from_arrays([[], []], names=("instrument", "datetime")),
                columns=fields,
                dtype=np.float32,
            )

    @classmethod
    def locate_index(cls, start_time=None, end_time=None, freq="day", future=False):
        """
        Locate index in calendar

        Args:
            start_time: Start time
            end_time: End time
            freq: Frequency
            future: Whether to include future dates

        Returns:
            Tuple of (start_time, end_time, start_index, end_index)
        """
        if cls._cal_provider is None:
            cls.initialize()

        # Get calendar
        calendar = cls.calendar(freq=freq, future=future)
        calendar_index = {x: i for i, x in enumerate(calendar)}

        # Handle None values
        if start_time is None:
            start_time = calendar[0]
            start_index = 0
        else:
            # Convert to timestamp
            start_time = pd.Timestamp(start_time)
            # Find index
            if start_time not in calendar_index:
                try:
                    start_time = calendar[bisect.bisect_left(calendar, start_time)]
                except IndexError:
                    # If beyond range, use the last available date
                    start_time = calendar[-1]
            start_index = calendar_index[start_time]

        if end_time is None:
            end_time = calendar[-1]
            end_index = len(calendar) - 1
        else:
            # Convert to timestamp
            end_time = pd.Timestamp(end_time)
            # Find index
            if end_time not in calendar_index:
                try:
                    end_time = calendar[bisect.bisect_right(calendar, end_time) - 1]
                except IndexError:
                    # If beyond range, use the first available date
                    end_time = calendar[0]
            end_index = calendar_index[end_time]

        return start_time, end_time, start_index, end_index

    @classmethod
    def features(cls, instruments, fields, freq="day", start_time=None, end_time=None):
        """
        Get features for instruments.

        This is a compatibility method for qlib-style data access.

        Parameters
        ----------
        instruments : list
            List of instruments.
        fields : list
            List of fields.
        freq : str
            Frequency.
        start_time : str or pd.Timestamp
            Start time.
        end_time : str or pd.Timestamp
            End time.

        Returns
        -------
        pandas.DataFrame
            DataFrame of features.
        """
        # Delegate to feature method
        return cls.feature(instruments=instruments, fields=fields, start_time=start_time, end_time=end_time, freq=freq)
