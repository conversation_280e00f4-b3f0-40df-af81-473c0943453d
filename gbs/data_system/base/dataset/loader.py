#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据加载模块
- 提供统一的数据加载接口
- 支持多种数据格式
- 支持数据缓存
- 支持Backtrader数据源构建
- 支持多股票并行处理
- 支持qlib风格的数据加载
- 支持从DataHandler加载数据
"""

import os
import time
import pandas as pd
import numpy as np
import concurrent.futures
import multiprocessing
import abc
import warnings
from typing import Optional, Dict, Any, Union, List, Tuple, Set, Literal
from pathlib import Path

# 尝试导入backtrader，如果不可用则忽略
try:
    import backtrader as bt
    BACKTRADER_AVAILABLE = True
except ImportError:
    BACKTRADER_AVAILABLE = False

from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.utils.date_utils import preprocess_dates
from gbs.core.utils.mod import init_instance_by_config


# 导入工具函数
from gbs.core.utils.data import sanitize_ohlcv, detect_date_col, add_return


# 获取日志记录器
log = get_loki_logger(__name__).logger


class DataLoader(abc.ABC):
    """
    DataLoader是为从原始数据源加载数据而设计的抽象基类。
    """

    @abc.abstractmethod
    def load(self, instruments=None, start_time=None, end_time=None) -> pd.DataFrame:
        """
        将数据加载为pd.DataFrame。

        数据示例（列的多级索引是可选的）：

            .. code-block:: text

                                        feature                                                             label
                                        $close     $volume     Ref($close, 1)  Mean($close, 3)  $high-$low  LABEL0
                datetime    instrument
                2010-01-04  SH600000    81.807068  17145150.0       83.737389        83.016739    2.741058  0.0032
                            SH600004    13.313329  11800983.0       13.313329        13.317701    0.183632  0.0042
                            SH600005    37.796539  12231662.0       38.258602        37.919757    0.970325  0.0289

        参数
        ----------
        instruments : str或dict
            可以是市场名称或由InstrumentProvider生成的instruments配置文件。
            如果instruments的值为None，则表示不进行过滤。
        start_time : str
            时间范围的开始。
        end_time : str
            时间范围的结束。

        返回
        -------
        pd.DataFrame:
            从底层源加载的数据

        异常
        -----
        KeyError:
            如果不支持instruments过滤器，则引发KeyError
        """
        pass


class DLWParser(DataLoader):
    """
    (D)ata(L)oader (W)ith (P)arser for features and names

    """

    def __init__(self, config: Union[list, tuple, dict]):
        """
        参数
        ----------
        config : Union[list, tuple, dict]
            配置将用于描述字段和列名

            .. code-block::

                <config> := {
                    "group_name1": <fields_info1>
                    "group_name2": <fields_info2>
                }
                or
                <config> := <fields_info>

                <fields_info> := ["expr", ...] | (["expr", ...], ["col_name", ...])
                # 注意：在解析时，列表或元组将被视为相同的东西
        """
        self.is_group = isinstance(config, dict)

        if self.is_group:
            self.fields = {grp: self._parse_fields_info(fields_info) for grp, fields_info in config.items()}
        else:
            self.fields = self._parse_fields_info(config)

    def _parse_fields_info(self, fields_info: Union[list, tuple]) -> Tuple[list, list]:
        if len(fields_info) == 0:
            raise ValueError("字段的大小必须大于0")

        if not isinstance(fields_info, (list, tuple)):
            raise TypeError("不支持的类型")

        if isinstance(fields_info[0], str):
            exprs = names = fields_info
        elif isinstance(fields_info[0], (list, tuple)):
            exprs, names = fields_info
        else:
            raise NotImplementedError(f"不支持此类型的输入")
        return exprs, names

    @abc.abstractmethod
    def load_group_df(
        self,
        instruments,
        exprs: list,
        names: list,
        start_time: Union[str, pd.Timestamp] = None,
        end_time: Union[str, pd.Timestamp] = None,
        gp_name: str = None,
    ) -> pd.DataFrame:
        """
        加载特定组的数据框

        参数
        ----------
        instruments :
            instruments。
        exprs : list
            描述数据内容的表达式。
        names : list
            数据的名称。

        返回
        -------
        pd.DataFrame:
            查询的数据框。
        """
        pass

    def load(self, instruments=None, start_time=None, end_time=None) -> pd.DataFrame:
        if self.is_group:
            df = pd.concat(
                {
                    grp: self.load_group_df(instruments, exprs, names, start_time, end_time, grp)
                    for grp, (exprs, names) in self.fields.items()
                },
                axis=1,
            )
        else:
            exprs, names = self.fields
            df = self.load_group_df(instruments, exprs, names, start_time, end_time)
        return df


class StaticDataLoader(DataLoader):
    """
    支持从Provider系统加载静态数据的DataLoader。
    支持多种数据源类型，如jkp、file、provider等。
    """

    def __init__(self, fields_groups=None, instruments=None, freq="day", join="outer",
                 data_source_type="provider", provider_uri=None, jkp_file=None, **kwargs):
        """
        参数
        ----------
        fields_groups : dict
            字段组配置，如 {'price': ['open', 'high', 'low', 'close'], 'volume': ['volume']}
        instruments : str或list
            要加载的instruments，可以是市场名称或instruments列表
        freq : str
            数据频率，如"day"、"min"等
        join : str
            如何对齐不同的数据框
        data_source_type : str
            数据源类型，支持"jkp"、"file"、"provider"等
        provider_uri : str
            数据提供者URI，如果为None则使用配置中的provider_uri
        jkp_file : str
            JKP数据文件名，仅当data_source_type为"jkp"时使用
        **kwargs : dict
            其他参数
        """
        # Initialize data to None first
        self._data = None

        # Check if fields_groups is a DataFrame (which can cause ambiguity in boolean context)
        if isinstance(fields_groups, pd.DataFrame):
            # Create a default fields_groups if fields_groups is a DataFrame
            self.fields_groups = {'ohlcv': ['open', 'high', 'low', 'close', 'volume']}
            # Store the DataFrame as raw_data
            self._data = fields_groups
        else:
            # Use the provided fields_groups or the default
            self.fields_groups = fields_groups or {'ohlcv': ['open', 'high', 'low', 'close', 'volume']}

        self.instruments = instruments
        self.freq = freq
        self.join = join
        self.data_source_type = data_source_type.lower() if data_source_type else "provider"
        self.provider_uri = provider_uri
        self.jkp_file = jkp_file
        self.kwargs = kwargs

        log.info(f"初始化StaticDataLoader: fields_groups={self.fields_groups}, instruments={instruments}, freq={freq}, data_source_type={self.data_source_type}, provider_uri={provider_uri}")

    def _maybe_load_raw_data(self):
        """
        如果尚未加载数据，则根据data_source_type从不同数据源加载数据
        支持"ALL"选项加载所有列
        """
        log.info(f"StaticDataLoader._maybe_load_raw_data() 被调用，self._data={self._data is not None}，fields_groups={self.fields_groups}, data_source_type={self.data_source_type}")

        if self._data is not None:
            log.info("数据已经加载，跳过加载过程")
            return

        # 检查是否有"ALL"选项
        has_all_option = False
        for group_name, fields in self.fields_groups.items():
            log.info(f"检查字段组 '{group_name}': {fields}")
            if len(fields) == 1 and fields[0] == "ALL":
                has_all_option = True
                log.info(f"检测到字段组 '{group_name}' 使用 'ALL' 选项，将加载所有列")
                break

        # 根据data_source_type选择加载方式
        if self.data_source_type == "jkp":
            # 从JKP文件加载数据
            self._load_jkp_data()
        elif self.data_source_type == "file":
            # 从文件加载数据
            self._load_file_data()
        elif has_all_option:
            # 使用直接加载方式获取所有列
            self._load_all_columns()
        else:
            # 从Provider系统加载所有字段组的数据
            self._load_from_provider()

    def _load_from_provider(self):
        """
        从Provider系统加载数据
        """
        from gbs.data_system.base.data import D

        # 处理instruments参数
        instruments_param = self.instruments
        if isinstance(self.instruments, str):
            # 如果instruments是字符串（如"us"），将其转换为正确的配置格式
            instruments_param = D.instruments(market=self.instruments)
            log.info(f"将市场名称 '{self.instruments}' 转换为instruments配置")

        all_dfs = {}
        for group_name, fields in self.fields_groups.items():
            try:
                log.info(f"从数据系统加载字段组 '{group_name}': instruments={instruments_param}, fields={fields}, freq={self.freq}")
                group_df = D.dataset(
                    instruments=instruments_param,
                    fields=fields,
                    freq=self.freq
                )
                all_dfs[group_name] = group_df
            except Exception as e:
                log.error(f"加载字段组 '{group_name}' 时出错: {str(e)}")

        # 合并所有字段组的数据
        if not all_dfs:
            raise ValueError("无法从Provider加载任何数据")

        if len(all_dfs) == 1:
            self._data = next(iter(all_dfs.values()))
        else:
            self._data = pd.concat(all_dfs, axis=1, join=self.join)
            self._data.sort_index(inplace=True)

    def _load_jkp_data(self):
        """
        从JKP文件加载数据
        """
        import os
        from pathlib import Path
        from gbs.core.utils.serial import Serializable
        from gbs.core.conf import C

        # 获取provider_uri路径
        provider_uri = self.provider_uri or C.get("provider_uri", "")
        log.info(f"获取到provider_uri: {provider_uri}")

        if not provider_uri:
            log.error("未设置provider_uri，无法加载数据")
            raise ValueError("未设置provider_uri，无法加载数据")

        # 处理provider_uri可能是字典的情况
        if isinstance(provider_uri, dict):
            # 使用默认频率
            from gbs.core.conf import DEFAULT_FREQ
            log.info(f"provider_uri是字典类型，使用默认频率: {DEFAULT_FREQ}")
            provider_uri = provider_uri.get(DEFAULT_FREQ, "")
            if not provider_uri:
                log.error(f"provider_uri字典中没有默认频率{DEFAULT_FREQ}的配置")
                raise ValueError(f"provider_uri字典中没有默认频率{DEFAULT_FREQ}的配置")

        # 获取JKP文件名
        jkp_file = self.jkp_file or "full_usa.pkl"
        jkp_path = os.path.join(provider_uri, jkp_file)
        log.info(f"构建JKP数据文件路径: {jkp_path}")

        if not os.path.exists(jkp_path):
            log.error(f"JKP数据文件 {jkp_path} 不存在")
            raise FileNotFoundError(f"JKP数据文件 {jkp_path} 不存在")

        try:
            log.info(f"从 {jkp_path} 加载JKP数据")
            df = Serializable.general_load(jkp_path)

            # 如果指定了instruments，过滤数据
            if self.instruments and self.instruments != "us":
                if isinstance(df.index, pd.MultiIndex):
                    # 如果是多级索引，按第二级（股票代码）过滤
                    symbol_idx = df.index.get_level_values(1)
                    df = df.loc[symbol_idx.isin(self.instruments)]
                else:
                    # 如果不是多级索引，尝试按permno列过滤
                    if 'permno' in df.columns:
                        df = df[df['permno'].isin(self.instruments)]
                    elif 'ticker' in df.columns:
                        df = df[df['ticker'].isin(self.instruments)]

            log.info(f"成功加载JKP数据，数据形状: {df.shape}")
            self._data = df
        except Exception as e:
            log.error(f"加载JKP数据时出错: {str(e)}")
            raise

    def _load_file_data(self):
        """
        从文件系统加载数据（.bin文件）
        """
        import os
        from pathlib import Path
        from gbs.core.conf import C
        from gbs.data_system.utils import load_features_from_bin

        # 获取provider_uri路径
        provider_uri = self.provider_uri or C.get("provider_uri", "")
        log.info(f"获取到provider_uri: {provider_uri}")

        if not provider_uri:
            log.error("未设置provider_uri，无法加载数据")
            raise ValueError("未设置provider_uri，无法加载数据")

        # 处理provider_uri可能是字典的情况
        if isinstance(provider_uri, dict):
            # 使用默认频率
            from gbs.core.conf import DEFAULT_FREQ
            log.info(f"provider_uri是字典类型，使用默认频率: {DEFAULT_FREQ}")
            provider_uri = provider_uri.get(DEFAULT_FREQ, "")
            if not provider_uri:
                log.error(f"provider_uri字典中没有默认频率{DEFAULT_FREQ}的配置")
                raise ValueError(f"provider_uri字典中没有默认频率{DEFAULT_FREQ}的配置")

        # 检查provider_uri是否存在
        if not os.path.exists(provider_uri):
            log.error(f"provider_uri路径不存在: {provider_uri}")
            raise FileNotFoundError(f"provider_uri路径不存在: {provider_uri}")

        # 提取所有字段
        all_fields = []
        for fields in self.fields_groups.values():
            all_fields.extend(fields)

        try:
            log.info(f"从文件系统加载数据: provider_uri={provider_uri}, instruments={self.instruments}, fields={all_fields}, freq={self.freq}")
            df = load_features_from_bin(
                instruments=self.instruments,
                fields=all_fields,
                freq=self.freq,
                provider_uri=provider_uri
            )
            log.info(f"成功加载文件数据，数据形状: {df.shape}")
            self._data = df
        except Exception as e:
            log.error(f"从文件系统加载数据时出错: {str(e)}")
            raise

    def _load_all_columns(self):
        """
        直接从JKP数据文件加载所有列
        """
        log.info("StaticDataLoader._load_all_columns() 被调用")

        import os
        from pathlib import Path
        from gbs.core.conf import C

        # 获取provider_uri路径
        provider_uri = C.get("provider_uri", "")
        log.info(f"获取到provider_uri: {provider_uri}")

        if not provider_uri:
            log.error("未设置provider_uri，无法加载数据")
            raise ValueError("未设置provider_uri，无法加载数据")

        # 处理provider_uri可能是字典的情况
        if isinstance(provider_uri, dict):
            # 使用默认频率
            from gbs.core.conf import DEFAULT_FREQ
            log.info(f"provider_uri是字典类型，使用默认频率: {DEFAULT_FREQ}")
            provider_uri = provider_uri.get(DEFAULT_FREQ, "")
            if not provider_uri:
                log.error(f"provider_uri字典中没有默认频率{DEFAULT_FREQ}的配置")
                raise ValueError(f"provider_uri字典中没有默认频率{DEFAULT_FREQ}的配置")

        # 构建full_usa.pkl的完整路径
        jkp_path = Path(provider_uri) / "full_usa.pkl"
        log.info(f"构建JKP数据文件路径: {jkp_path}")

        if not os.path.exists(jkp_path):
            log.error(f"JKP数据文件 {jkp_path} 不存在")
            raise FileNotFoundError(f"JKP数据文件 {jkp_path} 不存在")

        try:
            log.info(f"从 {jkp_path} 加载所有JKP数据列")
            df = pd.read_pickle(jkp_path)

            # 如果指定了instruments，过滤数据
            if self.instruments and self.instruments != "us":
                if isinstance(df.index, pd.MultiIndex):
                    # 如果是多级索引，按第二级（股票代码）过滤
                    symbol_idx = df.index.get_level_values(1)
                    df = df.loc[symbol_idx.isin(self.instruments)]
                else:
                    # 如果不是多级索引，尝试按permno列过滤
                    if 'permno' in df.columns:
                        df = df[df['permno'].isin(self.instruments)]

            log.info(f"成功加载JKP数据，数据形状: {df.shape}")
            self._data = df
        except Exception as e:
            log.error(f"加载JKP数据时出错: {str(e)}")
            raise

    def load(self, instruments=None, start_time=None, end_time=None) -> pd.DataFrame:
        """
        从Provider系统加载数据

        参数
        ----------
        instruments : str或list
            要过滤的instruments，如果为None则使用初始化时设置的instruments
        start_time : str或pd.Timestamp
            开始时间
        end_time : str或pd.Timestamp
            结束时间

        返回
        -------
        pd.DataFrame:
            加载的数据
        """
        # 如果instruments不同于初始化时的instruments，重新加载数据
        if instruments is not None and instruments != self.instruments:
            # 更新instruments并清除缓存的数据
            self.instruments = instruments
            self._data = None

        # 加载数据
        self._maybe_load_raw_data()

        # 如果是JKP数据，进行特殊处理
        if hasattr(self, 'data_source_type') and self.data_source_type == 'jkp' and self._data is not None:
            # 1. 先过滤掉ticker列为空的行
            if 'ticker' in self._data.columns and self._data['ticker'].isna().any():
                missing_count = self._data['ticker'].isna().sum()
                total_count = len(self._data)
                missing_pct = missing_count / total_count * 100
                log.warning(f"发现ticker列有{missing_count}行为空值 ({missing_pct:.2f}%)，将被过滤掉")
                self._data = self._data.dropna(subset=['ticker'])
                log.warning(f"过滤掉ticker空值后数据形状: {self._data.shape}")

            # 2. 如果有eom列，将其转换为datetime类型并设置为索引
            if 'eom' in self._data.columns:
                log.warning(f"发现eom列，将其转换为datetime类型并设置为索引")
                try:
                    # 确保eom列是datetime类型
                    if not pd.api.types.is_datetime64_any_dtype(self._data['eom']):
                        self._data['eom'] = pd.to_datetime(self._data['eom'])

                    # 按时间过滤（在设置索引前）
                    if start_time is not None:
                        start_timestamp = pd.Timestamp(start_time)
                        self._data = self._data[self._data['eom'] >= start_timestamp]
                        log.warning(f"按开始时间 {start_time} 过滤后数据形状: {self._data.shape}")

                    if end_time is not None:
                        end_timestamp = pd.Timestamp(end_time)
                        self._data = self._data[self._data['eom'] <= end_timestamp]
                        log.warning(f"按结束时间 {end_time} 过滤后数据形状: {self._data.shape}")

                    # 设置多级索引：ticker和eom
                    if 'ticker' in self._data.columns:
                        self._data = self._data.set_index(['ticker', 'eom'])
                        # 确保索引名称正确
                        self._data.index.names = ["instrument", "datetime"]
                        log.warning(f"设置[ticker, eom]为多级索引后数据形状: {self._data.shape}")
                    else:
                        # 如果没有ticker列但有permno列，使用permno
                        if 'permno' in self._data.columns:
                            self._data = self._data.set_index(['permno', 'eom'])
                            # 确保索引名称正确
                            self._data.index.names = ["instrument", "datetime"]
                            log.warning(f"设置[permno, eom]为多级索引后数据形状: {self._data.shape}")
                        else:
                            # 只设置eom为索引
                            self._data = self._data.set_index('eom')
                            # 确保索引名称正确
                            self._data.index.name = "datetime"
                            log.warning(f"设置eom为索引后数据形状: {self._data.shape}")
                except Exception as e:
                    log.warning(f"设置索引时出错: {str(e)}")

        # 获取数据副本，避免修改原始数据
        df = self._data.copy() if self._data is not None else pd.DataFrame()

        # 过滤时间范围
        if start_time is not None or end_time is not None:
            if isinstance(df.index, pd.MultiIndex):
                # 假设MultiIndex的第一级是datetime
                idx = df.index.get_level_values('datetime')
                mask = pd.Series(True, index=df.index)

                if start_time is not None:
                    try:
                        start_time = pd.Timestamp(start_time)
                        # 确保idx中的所有值都是datetime类型
                        if not pd.api.types.is_datetime64_any_dtype(idx):
                            log.warning(f"索引datetime级别不是datetime类型，尝试转换")
                            # 创建一个临时Series进行转换
                            temp_idx = pd.Series(idx)
                            temp_idx = pd.to_datetime(temp_idx, errors='coerce')
                            # 创建一个映射字典，将原始值映射到转换后的值
                            idx_map = dict(zip(idx, temp_idx))
                            # 使用映射创建新的mask
                            mask = mask & pd.Series([idx_map.get(x, pd.NaT) >= start_time for x in idx], index=df.index)
                        else:
                            mask = mask & (idx >= start_time)
                    except Exception as e:
                        log.error(f"过滤开始时间时出错: {str(e)}")
                        # 跳过开始时间过滤

                if end_time is not None:
                    try:
                        end_time = pd.Timestamp(end_time)
                        # 确保idx中的所有值都是datetime类型
                        if not pd.api.types.is_datetime64_any_dtype(idx):
                            log.warning(f"索引datetime级别不是datetime类型，尝试转换")
                            # 创建一个临时Series进行转换
                            temp_idx = pd.Series(idx)
                            temp_idx = pd.to_datetime(temp_idx, errors='coerce')
                            # 创建一个映射字典，将原始值映射到转换后的值
                            idx_map = dict(zip(idx, temp_idx))
                            # 使用映射创建新的mask
                            mask = mask & pd.Series([idx_map.get(x, pd.NaT) <= end_time for x in idx], index=df.index)
                        else:
                            mask = mask & (idx <= end_time)
                    except Exception as e:
                        log.error(f"过滤结束时间时出错: {str(e)}")
                        # 跳过结束时间过滤

                df = df.loc[mask]
            else:
                # 单级索引，假设是日期时间
                try:
                    if start_time is not None:
                        start_time = pd.Timestamp(start_time)
                    if end_time is not None:
                        end_time = pd.Timestamp(end_time)

                    # 检查索引类型
                    if not pd.api.types.is_datetime64_any_dtype(df.index):
                        log.warning(f"索引不是datetime类型，尝试转换")
                        # 创建一个临时DataFrame进行过滤
                        temp_df = df.copy()
                        temp_df.index = pd.to_datetime(temp_df.index, errors='coerce')
                        # 过滤
                        if start_time is not None:
                            temp_df = temp_df[temp_df.index >= start_time]
                        if end_time is not None:
                            temp_df = temp_df[temp_df.index <= end_time]
                        # 获取过滤后的索引
                        filtered_indices = temp_df.index
                        # 在原始DataFrame上应用过滤
                        df = df.loc[filtered_indices]
                    else:
                        # 索引已经是datetime类型，直接过滤
                        if start_time is not None and end_time is not None:
                            df = df.loc[start_time:end_time]
                        elif start_time is not None:
                            df = df.loc[df.index >= start_time]
                        elif end_time is not None:
                            df = df.loc[df.index <= end_time]
                except Exception as e:
                    log.error(f"过滤时间范围时出错: {str(e)}")
                    # 不进行过滤

        return df


class BacktraderDataLoader(DataLoader):
    """
    专为Backtrader设计的数据加载器，从Provider系统获取数据并转换为Backtrader数据源
    """

    def __init__(self, instruments=None, fields=None, freq="day", max_workers=None, batch_size=500, provider_uri=None):
        """
        初始化Backtrader数据加载器

        参数
        ----------
        instruments : str或list
            要加载的instruments，可以是市场名称或instruments列表
        fields : list
            要加载的字段列表，默认为OHLCV字段
        freq : str
            数据频率，如"day"、"min"等
        max_workers : int
            最大工作线程数，默认为CPU核心数的2倍（最多32个）
        batch_size : int
            批处理大小，每批处理的股票数量
        provider_uri : str
            数据提供者URI，如果为None则使用配置中的provider_uri
        """
        # 检查Backtrader是否可用
        if not BACKTRADER_AVAILABLE:
            raise ImportError("无法导入backtrader模块，请确保已安装")

        self.instruments = instruments
        self.fields = fields or ['open', 'high', 'low', 'close', 'volume']
        self.freq = freq
        self.provider_uri = provider_uri

        # 设置最大工作线程数
        if max_workers is None:
            self.max_workers = min(32, multiprocessing.cpu_count() * 2)
        else:
            self.max_workers = max_workers

        # 设置批处理大小
        self.batch_size = batch_size

        # 初始化FeedBuilder
        self.feed_builder = FeedBuilder(max_workers=self.max_workers, batch_size=self.batch_size)

        log.info(f"初始化BacktraderDataLoader: instruments={instruments}, fields={self.fields}, freq={freq}, max_workers={self.max_workers}, batch_size={self.batch_size}, provider_uri={provider_uri}")

    def load(self, instruments=None, start_time=None, end_time=None) -> List:
        """
        从Provider系统加载数据并转换为Backtrader数据源

        参数
        ----------
        instruments : str或list
            要过滤的instruments，如果为None则使用初始化时设置的instruments
        start_time : str或pd.Timestamp
            开始时间
        end_time : str或pd.Timestamp
            结束时间

        返回
        -------
        List:
            Backtrader数据源列表
        """
        # 使用传入的instruments或初始化时设置的instruments
        insts = instruments or self.instruments

        # 如果instruments为空列表，使用默认股票列表
        if isinstance(insts, list) and len(insts) == 0:
            log.warning("传入的instruments为空列表，使用默认股票列表: ['AAPL', 'MSFT', 'AMZN', 'GOOG', 'FB']")
            insts = ['AAPL', 'MSFT', 'AMZN', 'GOOG', 'FB']

        # 从数据系统加载数据
        log.info(f"从数据系统加载数据: instruments={insts}, fields={self.fields}, start_time={start_time}, end_time={end_time}, freq={self.freq}")
        try:
            # 首先尝试使用D.dataset加载数据
            try:
                from gbs.data_system.base.data import D

                # 处理instruments参数
                instruments_param = insts
                if isinstance(insts, str):
                    # 如果instruments是字符串（如"us"），将其转换为正确的配置格式
                    instruments_param = D.instruments(market=insts)
                    log.info(f"将市场名称 '{insts}' 转换为instruments配置")

                raw_data = D.dataset(
                    instruments=instruments_param,
                    fields=self.fields,
                    start_time=start_time,
                    end_time=end_time,
                    freq=self.freq
                )

                # 检查数据是否为空
                if raw_data.empty:
                    log.warning("从D.dataset加载的数据为空，尝试使用直接加载方式")
                    raise ValueError("从D.dataset加载的数据为空")

                log.info(f"从数据系统加载数据成功，数据形状: {raw_data.shape}")
            except Exception as e:
                log.warning(f"使用D.dataset加载数据失败: {str(e)}，尝试使用直接加载方式")

                # 如果D.dataset失败，使用直接加载方式
                from gbs.data_system.utils import load_features_from_bin
                from gbs.core.conf import C

                # 获取provider_uri
                provider_uri = self.provider_uri

                # 如果self.provider_uri为None，从配置中获取
                if provider_uri is None:
                    provider_uri = C.get("provider_uri", {})

                # 如果provider_uri为空，尝试使用默认路径
                if not provider_uri:
                    log.warning("provider_uri为空，尝试使用默认路径: /data/gold-beast-system/gbs/data/processed_data/jkp")
                    provider_uri = "/data/gold-beast-system/gbs/data/processed_data/jkp"

                # 直接从.bin文件加载数据
                try:
                    log.info(f"尝试从 {provider_uri} 加载数据，instruments={insts}, fields={self.fields}, freq={self.freq}")

                    # 检查provider_uri是否是字典
                    if isinstance(provider_uri, dict):
                        log.warning(f"provider_uri是字典: {provider_uri}，尝试获取默认频率的路径")
                        provider_uri = provider_uri.get(self.freq, provider_uri.get("day", provider_uri.get("__DEFAULT_FREQ", "")))
                        log.info(f"使用路径: {provider_uri}")

                    # 检查provider_uri是否存在
                    if not provider_uri:
                        log.error(f"provider_uri路径为空")
                        # 尝试使用默认路径
                        default_path = "/data/gold-beast-system/gbs/data/processed_data/jkp"
                        log.warning(f"尝试使用默认路径: {default_path}")
                        provider_uri = default_path

                        # 如果默认路径也不存在，报错
                        if not os.path.exists(provider_uri):
                            log.error(f"默认路径也不存在: {provider_uri}")
                            raise ValueError(f"无法找到有效的数据路径: {provider_uri}")

                    # 检查provider_uri是否存在
                    if os.path.exists(provider_uri):
                        log.info(f"provider_uri路径存在: {provider_uri}")

                        # 检查是否需要添加features目录
                        features_path = os.path.join(provider_uri, "features")
                        if os.path.exists(features_path):
                            log.info(f"找到features目录: {features_path}")
                            provider_uri = features_path

                        # 检查provider_uri是否包含.bin文件
                        bin_files = False
                        for root, dirs, files in os.walk(provider_uri):
                            if any(f.endswith('.bin') for f in files):
                                bin_files = True
                                break

                        if not bin_files:
                            log.warning(f"在 {provider_uri} 中没有找到.bin文件，尝试查找子目录")
                            # 检查是否有股票代码目录
                            stock_dirs = [d for d in os.listdir(provider_uri) if os.path.isdir(os.path.join(provider_uri, d))]
                            if stock_dirs:
                                log.info(f"找到股票目录: {len(stock_dirs)}个")
                                # 检查第一个股票目录是否包含.bin文件
                                first_stock_dir = os.path.join(provider_uri, stock_dirs[0])
                                if any(f.endswith('.bin') for f in os.listdir(first_stock_dir)):
                                    log.info(f"在股票目录中找到.bin文件，使用当前provider_uri: {provider_uri}")
                                else:
                                    log.warning(f"在股票目录中没有找到.bin文件，尝试使用默认路径")
                                    default_path = "/data/gold-beast-system/gbs/data/processed_data/jkp/features"
                                    if os.path.exists(default_path):
                                        log.info(f"使用默认路径: {default_path}")
                                        provider_uri = default_path
                    elif not os.path.exists(provider_uri):
                        log.error(f"provider_uri路径不存在: {provider_uri}")
                        raise ValueError(f"无法找到有效的数据路径: {provider_uri}")

                    # 不再将股票代码转换为小写，保持原始大小写
                    if insts is not None:
                        log.info(f"使用原始大小写的instruments: {insts}")

                    # 路径存在，尝试加载数据
                    raw_data = load_features_from_bin(
                        instruments=insts,
                        fields=self.fields,
                        start_time=start_time,
                        end_time=end_time,
                        freq=self.freq,
                        provider_uri=provider_uri
                    )

                    # 检查数据是否为空
                    if raw_data.empty:
                        log.error("使用直接加载方式也无法加载数据")
                        raise ValueError("无法从数据系统加载数据，数据为空")
                except Exception as e:
                    log.error(f"加载数据时出错: {str(e)}")
                    raise ValueError(f"无法从数据系统加载数据: {str(e)}")

                log.info(f"使用直接加载方式成功加载数据，数据形状: {raw_data.shape}")
        except Exception as e:
            log.error(f"从数据系统加载数据失败: {str(e)}")
            raise ValueError(f"无法从数据系统加载数据: {str(e)}")

        # 构建Backtrader数据源
        # 如果是多级索引，确保将股票代码传递给FeedBuilder
        if isinstance(raw_data.index, pd.MultiIndex) and raw_data.index.names[0] == 'instrument':
            log.info("数据有多级索引，尝试提取股票代码...")
            # 获取所有唯一的股票代码
            instruments = raw_data.index.get_level_values('instrument').unique()
            log.info(f"提取到的股票代码: {instruments}")

            # 为每个股票代码创建一个数据源
            data_feeds = []
            for instrument in instruments:
                # 获取该股票的数据
                instrument_data = raw_data.loc[instrument]
                log.info(f"处理股票 {instrument} 的数据，形状: {instrument_data.shape}")

                # 构建数据源
                feeds = self.feed_builder.build(instrument_data)
                if feeds:
                    # 设置数据源的名称
                    for feed in feeds:
                        if hasattr(feed, '_name') and (feed._name is None or feed._name.startswith('stock_')):
                            feed._name = instrument
                            log.info(f"设置数据源名称为: {instrument}")
                    data_feeds.extend(feeds)

            return data_feeds
        else:
            # 如果不是多级索引，直接构建数据源
            return self.feed_builder.build(raw_data)


class GbsDataLoader(DLWParser):
    """
    GBS数据加载器，类似于QlibDataLoader，但使用GBS自己的数据系统。

    该类支持：
    1. 从GBS数据系统加载数据
    2. 支持按字段组(field groups)组织数据
    3. 支持过滤器处理instruments
    4. 支持不同频率的数据加载
    5. 支持instruments处理器
    """

    def __init__(
        self,
        config: Union[list, tuple, dict],
        filter_pipe: List = None,
        swap_level: bool = True,
        freq: Union[str, dict] = "day",
        inst_processors: Union[dict, list] = None,
        data_dir: Union[str, Path] = None,
    ):
        """
        初始化GbsDataLoader

        参数
        ----------
        config : Union[list, tuple, dict]
            请参考DLWParser的文档
        filter_pipe : List, optional
            instruments的过滤管道
        swap_level : bool, optional
            是否交换MultiIndex的级别
        freq : Union[str, dict], optional
            如果type(config) == dict且type(freq) == str，使用freq加载config数据。
            如果type(config) == dict且type(freq) == dict，使用freq[<group_name>]加载config[<group_name>]数据。
        inst_processors : Union[dict, list], optional
            如果inst_processors不为None且type(config) == dict；使用inst_processors[<group_name>]加载config[<group_name>]数据
            如果inst_processors是列表，则将其应用于所有组。
        data_dir : Union[str, Path], optional
            数据目录，默认为DATA_DIR
        """
        self.filter_pipe = filter_pipe
        self.swap_level = swap_level
        self.freq = freq
        self.data_dir = DATA_DIR if data_dir is None else Path(data_dir)

        # 处理instruments处理器
        self.inst_processors = inst_processors if inst_processors is not None else {}
        assert isinstance(
            self.inst_processors, (dict, list)
        ), f"inst_processors(={self.inst_processors})必须是dict或list"

        super().__init__(config)

        if self.is_group:
            # 检查频率配置
            if isinstance(freq, dict):
                for _gp in config.keys():
                    if _gp not in freq:
                        raise ValueError(f"freq(={freq})缺少组(={_gp})")
                assert (
                    self.inst_processors
                ), f"freq(={self.freq}), inst_processors(={self.inst_processors})不能为None/空"

    def load_group_df(
        self,
        instruments,
        exprs: list,
        names: list,
        start_time: Union[str, pd.Timestamp] = None,
        end_time: Union[str, pd.Timestamp] = None,
        gp_name: str = None,
    ) -> pd.DataFrame:
        """
        加载特定组的数据框

        参数
        ----------
        instruments : str, list, or None
            要加载的instruments
        exprs : list
            描述数据内容的表达式
        names : list
            数据的列名
        start_time : Union[str, pd.Timestamp], optional
            开始时间
        end_time : Union[str, pd.Timestamp], optional
            结束时间
        gp_name : str, optional
            组名，用于在is_group=True时区分不同组

        返回
        -------
        pd.DataFrame:
            加载的数据框
        """
        # 处理instruments
        if instruments is None:
            warnings.warn("`instruments`未设置，将加载所有股票")
            instruments = self._get_all_instruments()
        elif isinstance(instruments, str):
            instruments = self._get_instruments_by_market(instruments)
        elif self.filter_pipe is not None:
            warnings.warn("`filter_pipe`不为None，但当`instruments`为列表时不会使用它")

        # 获取频率
        freq = self.freq[gp_name] if isinstance(self.freq, dict) else self.freq

        # 获取instruments处理器
        inst_processors = (
            self.inst_processors if isinstance(self.inst_processors, list)
            else self.inst_processors.get(gp_name, [])
        )

        # 加载特征数据
        df = self._load_features(instruments, exprs, start_time, end_time, freq, inst_processors)

        # 设置列名
        df.columns = names

        # 如果需要，交换MultiIndex的级别
        if self.swap_level and isinstance(df.index, pd.MultiIndex):
            df = df.swaplevel().sort_index()  # 注意：如果交换级别，返回<datetime, instrument>

        return df

    def _get_all_instruments(self) -> List[str]:
        """
        获取所有可用的instruments

        返回
        -------
        List[str]:
            所有可用的instruments列表
        """
        # 这里应该实现获取所有instruments的逻辑
        # 例如，从数据库或文件中读取所有股票代码
        log.info("获取所有可用的instruments")

        # 示例实现：从特定目录读取所有股票数据文件
        stock_files = list(self.data_dir.glob("stocks/*.csv"))
        instruments = [f.stem for f in stock_files]

        log.info(f"找到{len(instruments)}个instruments")
        return instruments

    def _get_instruments_by_market(self, market: str) -> List[str]:
        """
        根据市场名称获取instruments

        参数
        ----------
        market : str
            市场名称，如'all', 'csi300', 'nasdaq', 等

        返回
        -------
        List[str]:
            该市场的instruments列表
        """
        log.info(f"获取市场'{market}'的instruments")

        # 示例实现：根据市场名称获取instruments
        if market.lower() == 'all':
            return self._get_all_instruments()

        # 首先尝试从instruments目录加载
        instruments_file = self.data_dir / f"instruments/{market}.txt"

        if instruments_file.exists():
            log.info(f"从instruments目录加载市场'{market}'")
            with open(instruments_file, 'r') as f:
                # 解析instruments文件，格式为：symbol\tstart_date\tend_date
                instruments = []
                for line in f:
                    if line.strip():
                        parts = line.strip().split('\t')
                        if len(parts) >= 1:
                            instruments.append(parts[0])

            log.info(f"从instruments目录加载市场'{market}'，找到{len(instruments)}个instruments")

            # 应用过滤器
            if self.filter_pipe is not None:
                for filter_func in self.filter_pipe:
                    instruments = filter_func(instruments)
                log.info(f"过滤后剩余{len(instruments)}个instruments")

            return instruments

        # 如果instruments目录没有，尝试从markets目录加载
        market_file = self.data_dir / f"markets/{market}.txt"

        if not market_file.exists():
            raise ValueError(f"找不到市场'{market}'的定义文件，已检查路径：{instruments_file}和{market_file}")

        with open(market_file, 'r') as f:
            instruments = [line.strip() for line in f if line.strip()]

        log.info(f"市场'{market}'有{len(instruments)}个instruments")

        # 应用过滤器
        if self.filter_pipe is not None:
            for filter_func in self.filter_pipe:
                instruments = filter_func(instruments)
            log.info(f"过滤后剩余{len(instruments)}个instruments")

        return instruments

    def _load_features(
        self,
        instruments: List[str],
        exprs: List[str],
        start_time: Union[str, pd.Timestamp],
        end_time: Union[str, pd.Timestamp],
        freq: str,
        inst_processors: List
    ) -> pd.DataFrame:
        """
        从Provider系统加载特征数据

        参数
        ----------
        instruments : List[str]
            要加载的instruments列表
        exprs : List[str]
            要加载的表达式列表
        start_time : Union[str, pd.Timestamp]
            开始时间
        end_time : Union[str, pd.Timestamp]
            结束时间
        freq : str
            数据频率
        inst_processors : List
            instruments处理器列表

        返回
        -------
        pd.DataFrame:
            加载的特征数据
        """
        log.info(f"从Provider系统加载特征数据: {len(instruments)}个instruments, {len(exprs)}个表达式")

        # 转换时间格式
        if start_time is not None:
            start_time = pd.Timestamp(start_time)
        if end_time is not None:
            end_time = pd.Timestamp(end_time)

        # 使用Provider系统加载数据
        from gbs.data_system.base.data import D

        try:
            # 使用D.dataset加载数据
            df = D.dataset(
                instruments=instruments,
                fields=exprs,  # 使用表达式作为字段
                start_time=start_time,
                end_time=end_time,
                freq=freq
            )

            log.info(f"从Provider加载数据成功，数据形状: {df.shape if hasattr(df, 'shape') else 'unknown'}")
        except Exception as e:
            log.error(f"从Provider加载数据失败: {str(e)}")
            raise ValueError(f"无法从Provider加载数据: {str(e)}")

        # 应用instruments处理器
        for processor in inst_processors:
            df = processor(df)

        return df

    # _load_instrument_data 方法已被移除，因为我们现在直接从Provider系统加载数据


class NestedDataLoader(DataLoader):
    """
    组合多个DataLoader，每个DataLoader从Provider系统获取不同的数据。
    """
    # 导入StaticDataLoader类，以便在load方法中使用
    from gbs.data_system.base.dataset.loader import StaticDataLoader

    def __init__(self, dataloader_list: List[Union[DataLoader, Dict]], join="left") -> None:
        """
        参数
        ----------
        dataloader_list : List[Union[DataLoader, Dict]]
            数据加载器列表，例如：

            .. code-block:: python

                nd = NestedDataLoader(
                    dataloader_list=[
                        StaticDataLoader(fields_groups={'price': ['open', 'high', 'low', 'close']}),
                        BacktraderDataLoader(fields=['volume', 'returns_1d'])
                    ]
                )
        join :
            合并时将传递给pd.concat。
        """
        super().__init__()
        self.data_loader_list = []

        for dl in dataloader_list:
            if isinstance(dl, DataLoader):
                self.data_loader_list.append(dl)
            elif isinstance(dl, dict):
                # 尝试从配置初始化
                if 'class' not in dl:
                    raise ValueError("数据加载器配置必须包含'class'键")

                class_name = dl['class']
                kwargs = dl.get('kwargs', {})

                # 导入并实例化类
                try:
                    if '.' in class_name:
                        module_path, class_name = class_name.rsplit('.', 1)
                        module = __import__(module_path, fromlist=[class_name])
                        cls = getattr(module, class_name)
                    else:
                        # 尝试从当前模块获取类
                        cls = globals().get(class_name)
                        if cls is None:
                            raise ValueError(f"找不到类: {class_name}")

                    # 记录data_source_type
                    if 'data_source_type' in kwargs:
                        log.info(f"数据加载器 {class_name} 使用data_source_type: {kwargs['data_source_type']}")

                    # 确保provider_uri被正确传递
                    if 'provider_uri' in kwargs:
                        log.info(f"数据加载器 {class_name} 使用provider_uri: {kwargs['provider_uri']}")

                    # 实例化数据加载器
                    instance = cls(**kwargs)
                    self.data_loader_list.append(instance)
                except Exception as e:
                    log.error(f"初始化数据加载器时出错: {str(e)}")
                    raise
            else:
                raise TypeError(f"不支持的数据加载器类型: {type(dl)}")

        self.join = join
        log.info(f"初始化NestedDataLoader: {len(self.data_loader_list)}个数据加载器")

    def load(self, instruments=None, start_time=None, end_time=None) -> pd.DataFrame:
        """
        从所有数据加载器加载数据并合并

        参数
        ----------
        instruments : str或list
            要过滤的instruments列表或名称
        start_time : str或pd.Timestamp
            开始时间
        end_time : str或pd.Timestamp
            结束时间

        返回
        -------
        pd.DataFrame:
            合并后的数据
        """
        df_full = None
        log.info(f"NestedDataLoader.load() 被调用: instruments={instruments}, start_time={start_time}, end_time={end_time}")
        log.info(f"数据加载器列表长度: {len(self.data_loader_list)}")

        # 打印每个数据加载器的类型和配置
        for i, dl in enumerate(self.data_loader_list):
            if isinstance(dl, StaticDataLoader):
                log.info(f"数据加载器 {i+1}: StaticDataLoader, fields_groups={dl.fields_groups}, instruments={dl.instruments}")
            else:
                log.info(f"数据加载器 {i+1}: {dl.__class__.__name__}")

        for i, dl in enumerate(self.data_loader_list):
            try:
                log.info(f"开始加载数据加载器 {i+1}/{len(self.data_loader_list)}: {dl.__class__.__name__}")
                df_current = dl.load(instruments, start_time, end_time)

                # 如果返回的是列表（如BacktraderDataLoader），则跳过
                if isinstance(df_current, list):
                    log.warning(f"跳过数据加载器 {dl.__class__.__name__}，因为它返回列表而不是DataFrame")
                    continue

                log.info(f"数据加载器 {dl.__class__.__name__} 加载成功，数据形状: {df_current.shape if hasattr(df_current, 'shape') else 'unknown'}")

            except Exception as e:
                log.error(f"数据加载器 {dl.__class__.__name__} 加载失败: {str(e)}")
                continue

            if df_full is None:
                df_full = df_current
            else:
                # 合并数据框
                # 首先删除重复列
                current_columns = df_current.columns.tolist()
                full_columns = df_full.columns.tolist()
                columns_to_drop = [col for col in current_columns if col in full_columns]

                if columns_to_drop:
                    log.info(f"删除重复列: {columns_to_drop}")
                    df_current = df_current.drop(columns=columns_to_drop)

                # 然后合并
                try:
                    df_full = pd.merge(df_full, df_current, left_index=True, right_index=True, how=self.join)
                    log.info(f"合并后数据形状: {df_full.shape}")
                except Exception as e:
                    log.error(f"合并数据时出错: {str(e)}")

        if df_full is None:
            log.warning("所有数据加载器都加载失败，返回空DataFrame")
            return pd.DataFrame()  # 返回空DataFrame

        return df_full.sort_index(axis=1)


# 使用数据系统的辅助函数
def load_prices(
    instruments=None, start_time=None, end_time=None, freq="day"
) -> pd.DataFrame:
    """
    从数据系统加载价格数据

    Args:
        instruments: 要加载的instruments，可以是市场名称或instruments列表
        start_time: 开始时间
        end_time: 结束时间
        freq: 数据频率，如"day"、"min"等

    Returns:
        pd.DataFrame: 价格数据
    """
    from gbs.data_system.base.data import D

    fields = ['open', 'high', 'low', 'close', 'volume']

    try:
        log.info(f"从数据系统加载价格数据: instruments={instruments}, fields={fields}, start_time={start_time}, end_time={end_time}, freq={freq}")
        df = D.dataset(
            instruments=instruments,
            fields=fields,
            start_time=start_time,
            end_time=end_time,
            freq=freq
        )
        log.info(f"从数据系统加载价格数据成功，数据形状: {df.shape if hasattr(df, 'shape') else 'unknown'}")
        return df
    except Exception as e:
        log.error(f"从数据系统加载价格数据失败: {str(e)}")
        raise ValueError(f"无法从数据系统加载价格数据: {str(e)}")


def load_data(
    instruments=None, fields=None, start_time=None, end_time=None, freq="day"
) -> pd.DataFrame:
    """
    从数据系统加载数据

    Args:
        instruments: 要加载的instruments，可以是市场名称或instruments列表
        fields: 要加载的字段列表，默认为OHLCV字段
        start_time: 开始时间
        end_time: 结束时间
        freq: 数据频率，如"day"、"min"等

    Returns:
        pd.DataFrame: 加载的数据
    """
    from gbs.data_system.base.data import D

    fields = fields or ['open', 'high', 'low', 'close', 'volume']

    try:
        log.info(f"从数据系统加载数据: instruments={instruments}, fields={fields}, start_time={start_time}, end_time={end_time}, freq={freq}")
        df = D.dataset(
            instruments=instruments,
            fields=fields,
            start_time=start_time,
            end_time=end_time,
            freq=freq
        )
        log.info(f"从数据系统加载数据成功，数据形状: {df.shape if hasattr(df, 'shape') else 'unknown'}")
        return df
    except Exception as e:
        log.error(f"从数据系统加载数据失败: {str(e)}")
        raise ValueError(f"无法从数据系统加载数据: {str(e)}")

def prepare_data_for_backtest(df: pd.DataFrame) -> pd.DataFrame:
    """
    准备回测所需的数据格式

    Args:
        df: 原始 OHLCV 数据

    Returns:
        处理后的数据，适合回测引擎使用
    """
    if df.empty:
        log.warning("警告: 没有数据可处理")
        return df

    # 确保列名符合回测引擎的要求
    df.columns = [col.lower() for col in df.columns]

    # 确保日期列是 datetime 类型
    if 'date' in df.columns:
        df['date'] = pd.to_datetime(df['date'])

    # 添加收益率列
    df = add_return(df, 'symbol' if 'symbol' in df.columns else None)

    # 处理缺失值
    df = df.dropna()

    return df

def organize_features_by_date(data: pd.DataFrame, date_column: str, stock_id_column: str) -> pd.DataFrame:
    """
    按日期组织特征数据

    Args:
        data: 原始特征数据
        date_column: 日期列名
        stock_id_column: 股票标识符列名

    Returns:
        按日期组织的特征数据
    """
    log.info(f"开始按日期组织特征数据，原始数据形状: {data.shape}")

    # 获取特征列（排除日期列和股票标识符列）
    feature_columns = [col for col in data.columns if col not in [date_column, stock_id_column]]
    log.info(f"特征列数量: {len(feature_columns)}")

    # 获取唯一日期
    unique_dates = data[date_column].unique()
    log.info(f"共有 {len(unique_dates)} 个交易日")

    # 创建一个新的数据结构，按日期组织
    organized_data = pd.DataFrame()
    organized_data['date'] = unique_dates
    organized_data = organized_data.set_index('date')

    # 创建一个字典，存储每个日期的特征矩阵
    features_by_date = {}
    stocks_by_date = {}

    # 按日期分组处理
    group_start = time.time()
    grouped = data.groupby(date_column)
    log.info(f"数据分组耗时: {time.time() - group_start:.3f}秒")

    # 处理每个日期组
    for date, group in grouped:
        # 提取该日期的股票列表
        stocks = group[stock_id_column].values
        stocks_by_date[date] = stocks

        # 提取该日期的特征矩阵
        date_features = group[feature_columns].values

        # 创建股票到特征的映射
        stock_features = {}
        for i, stock in enumerate(stocks):
            stock_features[stock] = date_features[i]

        # 存储该日期的特征数据
        features_by_date[date] = stock_features

    # 将特征和股票列表添加到数据结构中
    organized_data['features'] = pd.Series(features_by_date)
    organized_data['stocks'] = pd.Series(stocks_by_date)

    log.info(f"按日期组织特征数据完成，新数据形状: {organized_data.shape}")
    return organized_data

# 这些函数已被删除，请使用 Provider 系统加载数据
# 参考上面的 load_prices 和 load_data 函数

# 这些函数已被移动到上面，避免重复


class FeedBuilder:
    """
    Backtrader数据源构建器
    - 支持单股票和多股票数据
    - 支持并行处理
    - 支持批量处理
    """

    def __init__(self, max_workers: int = None, batch_size: int = 500):
        """
        初始化数据源构建器

        Args:
            max_workers: 最大工作线程数，默认为CPU核心数的2倍（最多32个）
            batch_size: 批处理大小，每批处理的股票数量
        """
        # 检查Backtrader是否可用
        if not BACKTRADER_AVAILABLE:
            raise ImportError("无法导入backtrader模块，请确保已安装")

        # 设置最大工作线程数
        if max_workers is None:
            self.max_workers = min(32, multiprocessing.cpu_count() * 2)
        else:
            self.max_workers = max_workers

        # 设置批处理大小
        self.batch_size = batch_size

        log.info(f"初始化FeedBuilder: max_workers={self.max_workers}, batch_size={self.batch_size}")

    def build(self, data_source: pd.DataFrame) -> List:
        """
        构建Backtrader数据源

        Args:
            data_source: 历史市场数据DataFrame

        Returns:
            Backtrader数据源列表
        """
        total_start_time = time.time()

        try:
            # 确保输入是DataFrame
            if not isinstance(data_source, pd.DataFrame):
                log.error(f"data_source必须是pandas DataFrame，而不是{type(data_source)}")
                return []

            # 检查数据是否为空
            if data_source.empty:
                log.warning("输入数据为空，无法构建数据源")
                return []

            historical_data = data_source
            log.info(f"开始构建数据源，数据形状: {historical_data.shape}")

            # 检查数据格式
            if isinstance(historical_data.index, pd.MultiIndex):
                log.info("数据有多级索引，尝试处理...")
                # 如果是MultiIndex，检查第一级是否为datetime
                try:
                    if isinstance(historical_data.index.levels[0], pd.DatetimeIndex):
                        log.info("第一级索引是日期时间类型")
                        # 尝试处理多级索引数据
                        return self._process_one(historical_data)
                    else:
                        log.warning("多级索引的第一级不是日期时间类型，尝试重置索引...")
                        historical_data = historical_data.reset_index()
                except Exception as e:
                    log.warning(f"处理多级索引时出错: {str(e)}，尝试重置索引...")
                    historical_data = historical_data.reset_index()

            if isinstance(historical_data.index, pd.DatetimeIndex):
                log.info("数据已有日期索引")
                return self._process_one(historical_data)
            else:
                # 如果数据没有日期索引，查找日期列
                date_column = detect_date_col(historical_data.columns)

                if date_column is None:
                    log.error("历史数据必须包含日期列(如'date', 'eom'等)或使用日期索引")
                    return []

                # 预处理日期列，确保是pandas Timestamp类型
                try:
                    date_preprocess_start = time.time()
                    log.info("预处理日期列...")
                    historical_data = preprocess_dates(historical_data, date_column)
                    log.info(f"日期预处理耗时: {time.time() - date_preprocess_start:.3f}秒")
                except Exception as e:
                    log.error(f"预处理日期列时出错: {str(e)}")
                    return []

                # 检查是否有symbol列（多股票数据）
                if 'symbol' in historical_data.columns:
                    return self._multi_symbol(historical_data, date_column)
                else:
                    # 单股票数据处理
                    log.info("处理单股票数据...")
                    data = historical_data.copy()

                    # 设置日期索引
                    data = data.set_index(date_column)

                    # 确保索引是日期类型
                    try:
                        if not isinstance(data.index, pd.DatetimeIndex):
                            data.index = pd.to_datetime(data.index)
                    except Exception as e:
                        log.warning(f"转换索引为日期时出错: {e}")
                        return []

                    # 清洗数据
                    try:
                        data = sanitize_ohlcv(data)
                    except Exception as e:
                        log.error(f"清洗数据时出错: {str(e)}")
                        return []

                    # 创建Backtrader数据源
                    data_feed = self._make_feed(data)

                    if data_feed is not None:
                        log.info(f"数据源构建完成，耗时: {time.time() - total_start_time:.2f}秒")
                        return [data_feed]
                    else:
                        log.error("无法创建数据源")
                        return []
        except Exception as e:
            log.error(f"构建数据源时出错: {str(e)}")
            return []

    def _multi_symbol(self, historical_data: pd.DataFrame, date_column: str) -> List:
        """
        处理多股票数据

        Args:
            historical_data: 历史市场数据
            date_column: 日期列名

        Returns:
            Backtrader数据源列表
        """
        symbols_start_time = time.time()

        # 过滤掉NaN值的股票代码
        symbols = historical_data['symbol'].dropna().unique()
        log.info(f"发现 {len(symbols)} 个不同的股票代码")

        # 检查是否有NaN值的股票代码
        if historical_data['symbol'].isna().any():
            log.warning(f"发现 {historical_data['symbol'].isna().sum()} 行数据的股票代码为NaN，这些数据将被忽略")

        # 准备多线程处理
        log.info(f"使用 {self.max_workers} 个线程并行处理...")

        # 使用分组处理代替逐个处理
        log.info("开始分组处理股票数据...")
        grouped_start_time = time.time()
        grouped_data = historical_data.groupby('symbol')
        log.info(f"分组完成, 耗时: {time.time() - grouped_start_time:.2f}秒")

        data_feeds = []
        processed_count = 0
        total_count = len(symbols)

        # 使用线程池并行处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 分批处理
            for i in range(0, len(symbols), self.batch_size):
                batch_symbols = symbols[i:i+self.batch_size]
                log.info(f"提交批次 {i//self.batch_size + 1}, 处理 {len(batch_symbols)} 个股票")

                # 准备任务
                future_to_symbol = {}

                for symbol in batch_symbols:
                    # 提取单个股票的数据
                    symbol_data = grouped_data.get_group(symbol)
                    future = executor.submit(self._process_symbol, symbol_data, symbol, date_column)
                    future_to_symbol[future] = symbol

                # 等待当前批次完成
                for future in concurrent.futures.as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        data_feed = future.result()
                        if data_feed is not None:  # 只添加非空的数据源
                            data_feeds.append(data_feed)
                        processed_count += 1

                        # 每处理100个打印一次进度
                        if processed_count % 100 == 0:
                            log.info(f"已处理 {processed_count}/{total_count} 个股票")
                    except Exception as e:
                        log.error(f"处理股票 {symbol} 时出错: {e}")

        log.info(f"处理所有股票数据耗时: {time.time() - symbols_start_time:.2f}秒")
        log.info(f"总共创建了 {len(data_feeds)} 个数据源")
        return data_feeds

    def _process_symbol(self, symbol_data: pd.DataFrame, symbol_name: str, date_column: str) -> Optional[bt.feeds.PandasData]:
        """
        处理单个股票数据并创建数据源

        Args:
            symbol_data: 单个股票的数据
            symbol_name: 股票名称
            date_column: 日期列名

        Returns:
            Backtrader数据源
        """
        try:
            # 设置日期索引
            symbol_data = symbol_data.set_index(date_column)

            # 确保索引是日期类型
            if not isinstance(symbol_data.index, pd.DatetimeIndex):
                try:
                    symbol_data.index = pd.to_datetime(symbol_data.index)
                except Exception as e:
                    log.warning(f"股票 {symbol_name} 转换索引为日期时出错: {e}")

            # 检查数据是否为空
            if len(symbol_data) == 0:
                log.warning(f"股票 {symbol_name} 的数据为空, 跳过")
                return None

            # 清洗数据
            symbol_data = sanitize_ohlcv(symbol_data, symbol_name)

            # 创建Backtrader数据源
            return self._make_feed(symbol_data, symbol_name)
        except Exception as e:
            log.error(f"处理股票 {symbol_name} 时出错: {e}")
            return None

    def _process_one(self, data: pd.DataFrame) -> List:
        """
        处理单一数据集（已有日期索引）

        Args:
            data: 已有日期索引的数据

        Returns:
            Backtrader数据源列表
        """
        # 创建数据副本
        data = data.copy()

        # 清洗数据
        data = sanitize_ohlcv(data)

        log.info(f"数据日期范围: {data.index.min()} 至 {data.index.max()}")
        log.info(f"数据列: {list(data.columns)}")

        # 检查是否是多级索引，如果是，尝试提取股票代码
        symbol_name = None
        if isinstance(data.index, pd.MultiIndex) and len(data.index.names) > 1:
            # 假设第一级是instrument，第二级是datetime
            if data.index.names[0] == 'instrument':
                # 获取唯一的股票代码
                unique_instruments = data.index.get_level_values('instrument').unique()
                if len(unique_instruments) == 1:
                    symbol_name = unique_instruments[0]
                    log.info(f"从多级索引中提取到股票代码: {symbol_name}")
                else:
                    log.warning(f"多级索引中有多个股票代码: {unique_instruments}，无法确定使用哪一个")
            # 如果第一级不是instrument，尝试其他级别
            elif 'instrument' in data.index.names:
                # 获取instrument级别的索引
                instrument_level = data.index.names.index('instrument')
                # 获取唯一的股票代码
                unique_instruments = data.index.get_level_values(instrument_level).unique()
                if len(unique_instruments) == 1:
                    symbol_name = unique_instruments[0]
                    log.info(f"从多级索引中提取到股票代码: {symbol_name}")
                else:
                    log.warning(f"多级索引中有多个股票代码: {unique_instruments}，无法确定使用哪一个")

        # 创建Backtrader数据源
        data_feed = self._make_feed(data, symbol_name)

        if data_feed is not None:
            return [data_feed]
        else:
            log.error("无法创建数据源")
            return []

    # from_file 方法已被删除，请使用 Provider 系统加载数据

    def _make_feed(self, df: pd.DataFrame, symbol_name: Optional[str] = None) -> Optional[bt.feeds.PandasData]:
        """
        创建Backtrader数据源

        Args:
            df: 数据
            symbol_name: 股票名称

        Returns:
            Backtrader数据源
        """
        try:
            # 确保symbol_name不为空，如果为空则使用默认值
            if not symbol_name:
                symbol_name = f"stock_{id(df) % 1000}"  # 使用数据帧的ID作为唯一标识符
                log.warning(f"股票名称为空，使用默认值: {symbol_name}")

            # 打印股票名称，用于调试
            log.info(f"创建数据源，股票名称: {symbol_name}")

            data_feed = bt.feeds.PandasData(
                dataname=df,
                name=symbol_name,  # 始终使用股票名称
                datetime=None,  # 使用索引作为日期
                open='open',    # 使用列名而不是列索引
                high='high',
                low='low',
                close='close',
                volume='volume',
                openinterest=-1  # 不使用持仓量
            )
            return data_feed
        except Exception as e:
            log.error(f"创建股票 {symbol_name} 的数据源时出错: {e}")
            log.debug(f"数据类型: {df.dtypes}")
            return None


class DataLoaderDH(DataLoader):
    """
    基于DataHandler的数据加载器 (DataLoader based on DataHandler)

    该类设计用于从DataHandler加载数据，支持：
    1. 从单个或多个DataHandler加载数据
    2. 支持按组加载数据
    3. 支持不同的fetch参数配置

    注意：如果只想从单个DataHandler加载数据，可以直接使用DataHandler的fetch方法
    """

    def __init__(self, handler_config: dict, fetch_kwargs: dict = {}, is_group: bool = False):
        """
        初始化DataLoaderDH

        参数
        ----------
        handler_config : dict
            handler_config用于描述DataHandler

            .. code-block::

                := {
                    "group_name1": <handler_config1>,
                    "group_name2": <handler_config2>
                }
                or
                := <handler_config>

                <handler_config> := DataHandler实例 | DataHandler配置

        fetch_kwargs : dict
            fetch_kwargs用于描述fetch方法的不同参数，如col_set, squeeze, data_key等

        is_group : bool
            is_group用于描述handler_config的键是否为组名
        """
        from gbs.data_system.base.dataset.handler import DataHandler  # 避免循环导入

        if is_group:
            self.handlers = {
                grp: init_instance_by_config(config, accept_types=DataHandler)
                for grp, config in handler_config.items()
            }
        else:
            self.handlers = init_instance_by_config(handler_config, accept_types=DataHandler)

        self.is_group = is_group
        self.fetch_kwargs = {"col_set": DataHandler.CS_RAW}  # 默认使用原始数据
        self.fetch_kwargs.update(fetch_kwargs)

        log.info(f"初始化DataLoaderDH: is_group={is_group}, fetch_kwargs={self.fetch_kwargs}")

    def load(self, instruments=None, start_time=None, end_time=None) -> pd.DataFrame:
        """
        从DataHandler加载数据

        参数
        ----------
        instruments : str或list
            要加载的instruments，注意：此参数会被忽略，因为DataHandler已经包含了instruments信息
        start_time : str或pd.Timestamp
            开始时间
        end_time : str或pd.Timestamp
            结束时间

        返回
        -------
        pd.DataFrame:
            加载的数据
        """
        if instruments is not None:
            log.warning(f"instruments[{instruments}]参数被忽略，因为DataHandler已经包含了instruments信息")

        if self.is_group:
            # 创建一个字典来存储每个组的数据
            group_dfs = {}

            # 逐个加载每个组的数据
            for grp, dh in self.handlers.items():
                try:
                    log.info(f"从组 '{grp}' 加载数据...")
                    group_df = dh.fetch(
                        selector=slice(start_time, end_time),
                        level="datetime",
                        **self.fetch_kwargs
                    )

                    # 检查索引是否有重复值
                    if isinstance(group_df.index, pd.MultiIndex):
                        if group_df.index.duplicated().any():
                            log.warning(f"组 '{grp}' 的数据有重复的索引，正在删除重复项...")
                            group_df = group_df[~group_df.index.duplicated(keep='first')]
                            log.info(f"删除重复项后，组 '{grp}' 的数据形状: {group_df.shape}")

                    # 将处理后的数据添加到字典中
                    group_dfs[grp] = group_df
                    log.info(f"组 '{grp}' 的数据加载成功，形状: {group_df.shape}")
                except Exception as e:
                    log.error(f"加载组 '{grp}' 的数据时出错: {str(e)}")

            # 如果没有成功加载任何数据，返回空DataFrame
            if not group_dfs:
                log.warning("没有成功加载任何组的数据，返回空DataFrame")
                return pd.DataFrame()

            # 如果只有一个组，直接返回该组的数据
            if len(group_dfs) == 1:
                df = next(iter(group_dfs.values()))
                log.info(f"只有一个组的数据，直接返回，形状: {df.shape}")
            else:
                # 合并多个组的数据
                try:
                    log.info(f"合并 {len(group_dfs)} 个组的数据...")
                    df = pd.concat(group_dfs, axis=1)
                    log.info(f"合并后的数据形状: {df.shape}")
                except Exception as e:
                    log.error(f"合并组数据时出错: {str(e)}")
                    # 如果合并失败，返回第一个组的数据
                    df = next(iter(group_dfs.values()))
                    log.warning(f"合并失败，返回第一个组的数据，形状: {df.shape}")
        else:
            df = self.handlers.fetch(
                selector=slice(start_time, end_time),
                level="datetime",
                **self.fetch_kwargs
            )

        # 确保索引有正确的名称
        if df is not None and hasattr(df, 'index') and df.index.nlevels > 0:
            # 如果是多级索引，确保每个级别都有名称
            if isinstance(df.index, pd.MultiIndex):
                names = list(df.index.names)
                # 找到第一个包含日期时间的级别，并将其命名为"datetime"
                for i, level in enumerate(df.index.levels):
                    if pd.api.types.is_datetime64_any_dtype(level):
                        names[i] = "datetime"
                        break
                df.index.names = names
            # 如果是单级索引且包含日期时间，将其命名为"datetime"
            elif pd.api.types.is_datetime64_any_dtype(df.index):
                df.index.name = "datetime"

        log.info(f"从DataHandler加载数据成功，数据形状: {df.shape if hasattr(df, 'shape') else 'unknown'}")
        return df

