"""
Processor module for dataset
- Base Processor class
- Common processors for data preparation
- Data processing utilities
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Union, Optional, Tuple, Callable
from abc import ABC, abstractmethod
from torch.utils.data import DataLoader

from gbs.core.utils.loki_logger import get_loki_logger

# Get logger
log = get_loki_logger(__name__).logger


class Processor(ABC):
    """
    Base class for data processors.

    A processor is responsible for transforming data in a specific way,
    such as normalization, feature engineering, etc.
    """

    def __init__(self, **kwargs):
        """
        Initialize the processor.

        Args:
            **kwargs: Processor parameters
        """
        self.fitted = False

    def fit(self, df: pd.DataFrame):
        """
        Fit the processor to the data.

        This method should be implemented by subclasses to learn
        parameters from the data.

        Args:
            df: Input DataFrame
        """
        self.fitted = True

    @abstractmethod
    def __call__(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process the data.

        Args:
            df: Input DataFrame

        Returns:
            pd.DataFrame: Processed data
        """
        raise NotImplementedError("Subclasses must implement __call__")

    def is_for_infer(self) -> bool:
        """
        Check if the processor is usable for inference.

        Returns:
            bool: True if usable for inference
        """
        return True

    def readonly(self) -> bool:
        """
        Check if the processor is read-only.

        Returns:
            bool: True if read-only
        """
        return False

    def config(self, **kwargs):
        """
        Configure the processor.

        Args:
            **kwargs: Configuration parameters
        """
        for k, v in kwargs.items():
            setattr(self, k, v)


class DropnaProcessor(Processor):
    """
    Processor to drop rows with NaN values.
    """

    def __init__(self, subset: Optional[List[str]] = None):
        """
        Initialize the processor.

        Args:
            subset: Columns to check for NaN values
        """
        self.subset = subset
        super().__init__()

    def __call__(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Drop rows with NaN values.

        Args:
            df: Input DataFrame

        Returns:
            pd.DataFrame: DataFrame with NaN rows dropped
        """
        return df.dropna(subset=self.subset)

    def readonly(self) -> bool:
        """This processor is not read-only."""
        return False


class StandardScalerProcessor(Processor):
    """
    Processor to standardize features by removing the mean and scaling to unit variance.
    """

    def __init__(self, fields_group: Dict[str, List[str]] = None):
        """
        Initialize the processor.

        Args:
            fields_group: Groups of fields to standardize
                {group_name: [field1, field2, ...]}
        """
        self.fields_group = fields_group or {}
        self.mean = {}
        self.std = {}
        super().__init__()

    def fit(self, df: pd.DataFrame):
        """
        Fit the processor to the data.

        Args:
            df: Input DataFrame
        """
        for group, fields in self.fields_group.items():
            self.mean[group] = df[fields].mean()
            self.std[group] = df[fields].std()

        self.fitted = True

    def __call__(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Standardize the data.

        Args:
            df: Input DataFrame

        Returns:
            pd.DataFrame: Standardized DataFrame
        """
        if not self.fitted:
            self.fit(df)

        result = df.copy()

        for group, fields in self.fields_group.items():
            result[fields] = (result[fields] - self.mean[group]) / self.std[group]

        return result

    def readonly(self) -> bool:
        """This processor is not read-only."""
        return False


class MinMaxScalerProcessor(Processor):
    """
    Processor to scale features to a given range.
    """

    def __init__(
        self,
        fields_group: Dict[str, List[str]] = None,
        feature_range: tuple = (0, 1)
    ):
        """
        Initialize the processor.

        Args:
            fields_group: Groups of fields to scale
                {group_name: [field1, field2, ...]}
            feature_range: Range to scale to
        """
        self.fields_group = fields_group or {}
        self.feature_range = feature_range
        self.min = {}
        self.max = {}
        super().__init__()

    def fit(self, df: pd.DataFrame):
        """
        Fit the processor to the data.

        Args:
            df: Input DataFrame
        """
        for group, fields in self.fields_group.items():
            self.min[group] = df[fields].min()
            self.max[group] = df[fields].max()

        self.fitted = True

    def __call__(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Scale the data.

        Args:
            df: Input DataFrame

        Returns:
            pd.DataFrame: Scaled DataFrame
        """
        if not self.fitted:
            self.fit(df)

        result = df.copy()

        for group, fields in self.fields_group.items():
            min_val = self.min[group]
            max_val = self.max[group]

            # Handle the case where min == max
            scale = (max_val - min_val).replace(0, 1)

            # Scale to [0, 1]
            result[fields] = (result[fields] - min_val) / scale

            # Scale to feature_range
            min_range, max_range = self.feature_range
            result[fields] = result[fields] * (max_range - min_range) + min_range

        return result

    def readonly(self) -> bool:
        """This processor is not read-only."""
        return False


class FeatureSelectionProcessor(Processor):
    """
    Processor to select features.
    """

    def __init__(self, fields_to_select: List[str] = None):
        """
        Initialize the processor.

        Args:
            fields_to_select: Fields to select
        """
        self.fields_to_select = fields_to_select
        super().__init__()

    def __call__(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Select features.

        Args:
            df: Input DataFrame

        Returns:
            pd.DataFrame: DataFrame with selected features
        """
        if self.fields_to_select is None:
            return df

        return df[self.fields_to_select]

    def readonly(self) -> bool:
        """This processor is read-only."""
        return True


class LogReturnProcessor(Processor):
    """
    Processor to calculate log returns.
    """

    def __init__(self, fields: List[str] = None, inplace: bool = False):
        """
        Initialize the processor.

        Args:
            fields: Fields to calculate log returns for
            inplace: Whether to replace original fields
        """
        self.fields = fields
        self.inplace = inplace
        super().__init__()

    def __call__(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate log returns.

        Args:
            df: Input DataFrame

        Returns:
            pd.DataFrame: DataFrame with log returns
        """
        result = df.copy()

        for field in self.fields:
            log_field = f"log_{field}" if not self.inplace else field
            result[log_field] = np.log(result[field] / result[field].shift(1))

        return result

    def readonly(self) -> bool:
        """This processor is not read-only."""
        return False


class RankNormalizeProcessor(Processor):
    """
    Processor to rank normalize features.
    """

    def __init__(self, fields: List[str] = None, inplace: bool = False):
        """
        Initialize the processor.

        Args:
            fields: Fields to rank normalize
            inplace: Whether to replace original fields
        """
        self.fields = fields
        self.inplace = inplace
        super().__init__()

    def __call__(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Rank normalize features.

        Args:
            df: Input DataFrame

        Returns:
            pd.DataFrame: DataFrame with rank normalized features
        """
        result = df.copy()

        for field in self.fields:
            rank_field = f"rank_{field}" if not self.inplace else field

            # Get unique datetime values
            datetimes = df.index.get_level_values("datetime").unique()

            # Rank normalize by datetime
            for dt in datetimes:
                idx = df.index.get_level_values("datetime") == dt
                values = df.loc[idx, field].values

                # Skip if not enough values
                if len(values) <= 1:
                    continue

                # Rank normalize
                ranks = pd.Series(values).rank(pct=True).values
                result.loc[idx, rank_field] = ranks

        return result

    def readonly(self) -> bool:
        """This processor is not read-only."""
        return False


class BaseDataProcessor(ABC):
    """
    Abstract base class for data processors.

    This class provides a common interface for data processing
    in the data system.
    """

    def __init__(self, **kwargs):
        """
        Initialize the data processor.

        Args:
            **kwargs: Processor parameters
        """
        self.config = kwargs

    @abstractmethod
    def process_data(self, features: pd.DataFrame, targets: Optional[pd.DataFrame] = None, **kwargs) -> Dict[str, Any]:
        """
        Process data for model training or inference.

        Args:
            features: Feature data
            targets: Target data
            **kwargs: Additional processing parameters

        Returns:
            Dict[str, Any]: Processed data
        """
        pass

    @abstractmethod
    def create_data_loaders(self, processed_data: Dict[str, Any], batch_size: int = 32, **kwargs) -> Union[DataLoader, Tuple[DataLoader, ...]]:
        """
        Create data loaders from processed data.

        Args:
            processed_data: Processed data from process_data method
            batch_size: Batch size
            **kwargs: Additional parameters

        Returns:
            One or more data loaders
        """
        pass

    @abstractmethod
    def split_data(self, processed_data: Dict[str, Any], train_ratio: float = 0.7, val_ratio: float = 0.15, **kwargs) -> Tuple[Dict[str, Any], ...]:
        """
        Split processed data into train, validation, and test sets.

        Args:
            processed_data: Processed data
            train_ratio: Training set ratio
            val_ratio: Validation set ratio
            **kwargs: Additional split parameters

        Returns:
            Tuple of split datasets (train_data, val_data, test_data)
        """
        pass

    def normalize_features(self, features: pd.DataFrame, **kwargs) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Normalize features.

        Args:
            features: Feature data
            **kwargs: Additional normalization parameters

        Returns:
            Normalized features and normalization parameters
        """
        # Default implementation: Z-score normalization
        mean = features.mean()
        std = features.std()
        normalized_features = (features - mean) / std

        return normalized_features, {'mean': mean, 'std': std}

    def denormalize_features(self, normalized_features: pd.DataFrame, normalization_params: Dict[str, Any], **kwargs) -> pd.DataFrame:
        """
        Denormalize features.

        Args:
            normalized_features: Normalized features
            normalization_params: Normalization parameters
            **kwargs: Additional parameters

        Returns:
            Original scale features
        """
        # Default implementation: Inverse Z-score normalization
        mean = normalization_params['mean']
        std = normalization_params['std']
        original_features = normalized_features * std + mean

        return original_features

    def handle_missing_values(self, data: pd.DataFrame, strategy: str = 'mean', **kwargs) -> pd.DataFrame:
        """
        Handle missing values.

        Args:
            data: Data with missing values
            strategy: Handling strategy ('mean', 'median', 'mode', 'constant', 'drop')
            **kwargs: Additional parameters, like fill_value for 'constant' strategy

        Returns:
            Data with missing values handled
        """
        if strategy == 'drop':
            return data.dropna()

        result = data.copy()

        if strategy == 'mean':
            return result.fillna(result.mean())
        elif strategy == 'median':
            return result.fillna(result.median())
        elif strategy == 'mode':
            return result.fillna(result.mode().iloc[0])
        elif strategy == 'constant':
            fill_value = kwargs.get('fill_value', 0)
            return result.fillna(fill_value)
        else:
            raise ValueError(f"Unsupported missing value strategy: {strategy}")

    def handle_extreme_values(self, data: pd.DataFrame, method: str = 'winsorize', **kwargs) -> pd.DataFrame:
        """
        Handle extreme values.

        Args:
            data: Data with extreme values
            method: Handling method ('winsorize', 'clip', 'zscore', 'iqr', 'percentile')
            **kwargs: Additional parameters

        Returns:
            Data with extreme values handled
        """
        result = data.copy()
        numeric_cols = result.select_dtypes(include=['number']).columns

        if method == 'winsorize':
            # Winsorize: Replace extreme values with specified percentile values
            lower = kwargs.get('lower', 0.05)  # Default 5%
            upper = kwargs.get('upper', 0.95)  # Default 95%

            for col in numeric_cols:
                lower_bound = result[col].quantile(lower)
                upper_bound = result[col].quantile(upper)
                result[col] = result[col].clip(lower=lower_bound, upper=upper_bound)

        elif method == 'clip':
            # Clip: Clip based on absolute thresholds
            for col in numeric_cols:
                lower_bound = kwargs.get('lower_bound', result[col].mean() - 3 * result[col].std())
                upper_bound = kwargs.get('upper_bound', result[col].mean() + 3 * result[col].std())
                result[col] = result[col].clip(lower=lower_bound, upper=upper_bound)

        elif method == 'zscore':
            # Z-score: Remove values with Z-score above threshold
            threshold = kwargs.get('threshold', 3.0)  # Default 3 standard deviations
            for col in numeric_cols:
                mean = result[col].mean()
                std = result[col].std()
                z_scores = (result[col] - mean) / std
                result = result[(z_scores.abs() <= threshold)]

        elif method == 'iqr':
            # IQR: Based on interquartile range
            k = kwargs.get('k', 1.5)  # Default IQR coefficient
            for col in numeric_cols:
                q1 = result[col].quantile(0.25)
                q3 = result[col].quantile(0.75)
                iqr = q3 - q1
                lower_bound = q1 - k * iqr
                upper_bound = q3 + k * iqr
                result[col] = result[col].clip(lower=lower_bound, upper=upper_bound)

        elif method == 'percentile':
            # Percentile: Remove values outside specified percentile range
            lower = kwargs.get('lower', 0.01)  # Default 1%
            upper = kwargs.get('upper', 0.99)  # Default 99%
            for col in numeric_cols:
                lower_bound = result[col].quantile(lower)
                upper_bound = result[col].quantile(upper)
                result = result[(result[col] >= lower_bound) & (result[col] <= upper_bound)]
        else:
            raise ValueError(f"Unsupported extreme value handling method: {method}")

        return result

    def filter_data(self, data: pd.DataFrame, conditions: Dict[str, Any], **kwargs) -> pd.DataFrame:
        """
        Filter data based on conditions.

        Args:
            data: Data to filter
            conditions: Filter conditions {column: condition}
            **kwargs: Additional filter parameters

        Returns:
            Filtered data
        """
        filtered_data = data.copy()

        for column, condition in conditions.items():
            if column not in filtered_data.columns:
                continue

            if isinstance(condition, (list, tuple)):
                filtered_data = filtered_data[filtered_data[column].isin(condition)]
            elif callable(condition):
                filtered_data = filtered_data[condition(filtered_data[column])]
            else:
                filtered_data = filtered_data[filtered_data[column] == condition]

        return filtered_data

    def get_feature_importance(self, features: pd.DataFrame, targets: pd.Series, method: str = 'correlation', **kwargs) -> pd.Series:
        """
        Calculate feature importance.

        Args:
            features: Feature data
            targets: Target data
            method: Calculation method ('correlation', 'mutual_info', 'model_based')
            **kwargs: Additional parameters

        Returns:
            Feature importance scores
        """
        if method == 'correlation':
            # Calculate absolute correlation with target
            importance = features.apply(lambda x: abs(x.corr(targets)))
            return importance

        elif method == 'mutual_info':
            try:
                from sklearn.feature_selection import mutual_info_regression
                importance = pd.Series(
                    mutual_info_regression(features, targets),
                    index=features.columns
                )
                return importance
            except ImportError:
                raise ImportError("Computing mutual information requires scikit-learn")

        elif method == 'model_based':
            model = kwargs.get('model', None)
            if model is None:
                raise ValueError("'model_based' method requires a model parameter")

            try:
                # Try to get feature importances
                importance = pd.Series(
                    model.feature_importances_,
                    index=features.columns
                )
                return importance
            except:
                raise ValueError("Provided model does not support feature_importances_ attribute")

        else:
            raise ValueError(f"Unsupported feature importance method: {method}")

    def clean_data(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Perform complete data cleaning process.

        Args:
            data: Data to clean
            **kwargs: Cleaning parameters:
                - handle_missing: Whether to handle missing values (default: True)
                - missing_strategy: Missing value strategy (default: 'mean')
                - handle_outliers: Whether to handle outliers (default: True)
                - outlier_method: Outlier handling method (default: 'winsorize')
                - remove_duplicates: Whether to remove duplicates (default: True)
                - normalize: Whether to normalize data (default: False)

        Returns:
            Cleaned data
        """
        # Extract parameters
        handle_missing = kwargs.get('handle_missing', True)
        missing_strategy = kwargs.get('missing_strategy', 'mean')
        handle_outliers = kwargs.get('handle_outliers', True)
        outlier_method = kwargs.get('outlier_method', 'winsorize')
        remove_duplicates = kwargs.get('remove_duplicates', True)
        normalize = kwargs.get('normalize', False)

        # Initialize cleaned data
        cleaned_data = data.copy()

        # Remove duplicates
        if remove_duplicates:
            original_len = len(cleaned_data)
            cleaned_data = cleaned_data.drop_duplicates()
            dropped_rows = original_len - len(cleaned_data)
            if dropped_rows > 0:
                log.info(f"Removed {dropped_rows} duplicate rows ({dropped_rows/original_len:.2%})")

        # Handle missing values
        if handle_missing:
            cleaned_data = self.handle_missing_values(cleaned_data, strategy=missing_strategy)

        # Handle outliers
        if handle_outliers:
            cleaned_data = self.handle_extreme_values(cleaned_data, method=outlier_method)

        # Normalize data
        if normalize:
            numeric_cols = cleaned_data.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                normalized_data, _ = self.normalize_features(cleaned_data[numeric_cols])
                cleaned_data[numeric_cols] = normalized_data

        return cleaned_data
