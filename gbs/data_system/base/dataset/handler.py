"""
Data handler classes for dataset module
- Base DataHandler for data loading and processing
- DataHandlerLP with learnable processors
"""

import warnings
import pandas as pd
import numpy as np
from typing import Callable, Union, List, Tuple, Dict, Text, Optional, Iterator, Literal
from copy import copy, deepcopy
from inspect import getfullargspec

from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.utils.serial import Serializable
from gbs.core.utils.mod import init_instance_by_config
from .utils import fetch_df_by_index, fetch_df_by_col, lazy_sort_index

# Get logger
log = get_loki_logger(__name__).logger


class DataHandler(Serializable):
    """
    Base class for data handling.

    This class is responsible for:
    - Loading data from data sources
    - Providing a unified interface for data access
    - Supporting data filtering and selection
    """

    # Column set constants
    CS_ALL = "__all"  # Return all columns with single-level index
    CS_RAW = "__raw"  # Return raw data with multi-level index

    def __init__(
        self,
        instruments=None,
        start_time=None,
        end_time=None,
        data_loader=None,
        init_data=True,
        fetch_orig=True,
    ):
        """
        Initialize the data handler.

        Args:
            instruments: List of instruments to retrieve
            start_time: Start time of the data
            end_time: End time of the data
            data_loader: Data loader to load the data
            init_data: Whether to initialize data in constructor
            fetch_orig: Return original data instead of copy if possible
        """
        # Setup data loader
        assert data_loader is not None

        # Import here to avoid circular imports
        from gbs.data_system.base.dataset.loader import DataLoader
        from gbs.core.utils.mod import init_instance_by_config

        # Initialize data loader - handle both instance and config
        if isinstance(data_loader, dict) and 'class' in data_loader:
            self.data_loader = init_instance_by_config(data_loader, accept_types=DataLoader)
        else:
            self.data_loader = data_loader

        # Set data parameters
        self.instruments = instruments
        self.start_time = start_time
        self.end_time = end_time
        self.fetch_orig = fetch_orig

        # Initialize data if requested
        if init_data:
            self.setup_data()

        super().__init__()

    def config(self, **kwargs):
        """
        Configure the data handler.

        Args:
            **kwargs: Configuration parameters
        """
        # Update attributes
        attr_list = {"instruments", "start_time", "end_time"}
        for k, v in kwargs.items():
            if k in attr_list:
                setattr(self, k, v)

        # Remove processed attributes from kwargs
        for attr in attr_list:
            if attr in kwargs:
                kwargs.pop(attr)

        super().config(**kwargs)

    def setup_data(self, enable_cache: bool = False):
        """
        Set up the data.

        Args:
            enable_cache: Whether to enable caching of processed data
        """
        # Load data from data loader
        self._data = lazy_sort_index(
            self.data_loader.load(self.instruments, self.start_time, self.end_time)
        )

    def fetch(
        self,
        selector: Union[pd.Timestamp, slice, str, pd.Index] = slice(None, None),
        level: Union[str, int] = "datetime",
        col_set=CS_ALL,
        squeeze: bool = False,
        proc_func: Callable = None,
    ) -> pd.DataFrame:
        """
        Fetch data from the underlying data source.

        Args:
            selector: How to select data by index
            level: Which index level to select on
            col_set: Which set of columns to select
            squeeze: Whether to squeeze columns and index
            proc_func: Function to process data before fetching

        Returns:
            The fetched data
        """
        return self._fetch_data(
            data_storage=self._data,
            selector=selector,
            level=level,
            col_set=col_set,
            squeeze=squeeze,
            proc_func=proc_func,
        )

    def _fetch_data(
        self,
        data_storage,
        selector: Union[pd.Timestamp, slice, str, pd.Index] = slice(None, None),
        level: Union[str, int] = "datetime",
        col_set=CS_ALL,
        squeeze: bool = False,
        proc_func: Callable = None,
    ):
        """
        Internal method to fetch data.

        Args:
            data_storage: Data storage to fetch from
            selector: How to select data by index
            level: Which index level to select on
            col_set: Which set of columns to select
            squeeze: Whether to squeeze columns and index
            proc_func: Function to process data before fetching

        Returns:
            The fetched data
        """
        # Handle list/tuple selectors
        if isinstance(selector, (tuple, list)) and level is not None:
            try:
                selector = slice(*selector)
            except ValueError:
                log.info(f"Failed to convert query to slice. It will be used directly")

        # Handle different data storage types
        if isinstance(data_storage, pd.DataFrame):
            data_df = data_storage

            if proc_func is not None:
                # Process data with proc_func
                data_df = proc_func(
                    fetch_df_by_index(data_df, selector, level, fetch_orig=self.fetch_orig).copy()
                )
                data_df = fetch_df_by_col(data_df, col_set)
            else:
                # Fetch column first for better performance
                data_df = fetch_df_by_col(data_df, col_set)
                data_df = fetch_df_by_index(data_df, selector, level, fetch_orig=self.fetch_orig)
        else:
            raise TypeError(f"data_storage should be pd.DataFrame, not {type(data_storage)}")

        if squeeze:
            # Squeeze columns
            data_df = data_df.squeeze()

            # Squeeze index if selector is a single value
            if isinstance(selector, (str, pd.Timestamp)):
                data_df = data_df.reset_index(level=level, drop=True)

        return data_df

    def get_cols(self, col_set=CS_ALL) -> list:
        """
        Get the column names.

        Args:
            col_set: Which set of columns to get

        Returns:
            List of column names
        """
        df = self._data.head()
        df = fetch_df_by_col(df, col_set)
        return df.columns.to_list()

    def get_range_selector(self, cur_date: Union[pd.Timestamp, str], periods: int) -> slice:
        """
        Get range selector by number of periods.

        Args:
            cur_date: Current date
            periods: Number of periods

        Returns:
            Slice for the range
        """
        trading_dates = self._data.index.unique(level="datetime")
        cur_loc = trading_dates.get_loc(cur_date)
        pre_loc = cur_loc - periods + 1

        if pre_loc < 0:
            warnings.warn("`periods` is too large. The first date will be returned.")
            pre_loc = 0

        ref_date = trading_dates[pre_loc]
        return slice(ref_date, cur_date)

    def get_range_iterator(
        self, periods: int, min_periods: Optional[int] = None, **kwargs
    ) -> Iterator[Tuple[pd.Timestamp, pd.DataFrame]]:
        """
        Get an iterator of sliced data with given periods.

        Args:
            periods: Number of periods
            min_periods: Minimum periods for sliced dataframe
            **kwargs: Additional arguments for fetch

        Returns:
            Iterator of (date, data) tuples
        """
        trading_dates = self._data.index.unique(level="datetime")

        if min_periods is None:
            min_periods = periods

        for cur_date in trading_dates[min_periods:]:
            selector = self.get_range_selector(cur_date, periods)
            yield cur_date, self.fetch(selector, **kwargs)


class DataHandlerLP(DataHandler):
    """
    DataHandler with Learnable Processor.

    This handler produces three pieces of data:
    - DK_R / self._data: Raw data loaded from the loader
    - DK_I / self._infer: Data processed for inference
    - DK_L / self._learn: Data processed for learning models
    """

    # Data key types
    DATA_KEY_TYPE = Literal["raw", "infer", "learn"]

    # Data keys
    DK_R: DATA_KEY_TYPE = "raw"
    DK_I: DATA_KEY_TYPE = "infer"
    DK_L: DATA_KEY_TYPE = "learn"

    # Map data_key to attribute name
    ATTR_MAP = {DK_R: "_data", DK_I: "_infer", DK_L: "_learn"}

    # Process types
    PTYPE_I = "independent"  # Independent processing for infer and learn
    PTYPE_A = "append"       # Append learn processors to infer processors

    def __init__(
        self,
        instruments=None,
        start_time=None,
        end_time=None,
        data_loader=None,
        infer_processors: List = [],
        learn_processors: List = [],
        shared_processors: List = [],
        process_type=PTYPE_A,
        drop_raw=False,
        **kwargs,
    ):
        """
        Initialize the data handler with learnable processors.

        Args:
            instruments: List of instruments to retrieve
            start_time: Start time of the data
            end_time: End time of the data
            data_loader: Data loader to load the data
            infer_processors: Processors for inference data
            learn_processors: Processors for learning data
            shared_processors: Processors shared by both
            process_type: Type of processing (independent or append)
            drop_raw: Whether to drop raw data after processing
            **kwargs: Additional arguments
        """
        # Initialize processor lists
        self.infer_processors = []
        self.learn_processors = []
        self.shared_processors = []

        # Import processor module
        from gbs.data_system.base.dataset import processor as processor_module

        # Initialize processors
        for pname in ["infer_processors", "learn_processors", "shared_processors"]:
            for proc in locals()[pname]:
                getattr(self, pname).append(
                    init_instance_by_config(
                        proc, accept_types=processor_module.Processor
                    )
                )

        self.process_type = process_type
        self.drop_raw = drop_raw

        super().__init__(
            instruments, start_time, end_time, data_loader, **kwargs
        )

    def get_all_processors(self):
        """Get all processors."""
        return self.shared_processors + self.infer_processors + self.learn_processors

    def fit(self):
        """
        Fit processors without processing the data.
        """
        for proc in self.get_all_processors():
            proc.fit(self._data)

    def fit_process_data(self):
        """
        Fit and process data.

        The input of the `fit` will be the output of the previous processor.
        """
        self.process_data(with_fit=True)

    @staticmethod
    def _run_proc_l(
        df: pd.DataFrame, proc_l: List, with_fit: bool, check_for_infer: bool
    ) -> pd.DataFrame:
        """
        Run a list of processors on data.

        Args:
            df: Input data
            proc_l: List of processors
            with_fit: Whether to fit processors
            check_for_infer: Whether to check if processors are usable for inference

        Returns:
            Processed data
        """
        for proc in proc_l:
            if check_for_infer and not proc.is_for_infer():
                raise TypeError("Only processors usable for inference can be used in `infer_processors`")

            if with_fit:
                proc.fit(df)

            df = proc(df)

        return df

    @staticmethod
    def _is_proc_readonly(proc_l: List):
        """
        Check if all processors are read-only.

        Args:
            proc_l: List of processors

        Returns:
            True if all processors are read-only
        """
        for p in proc_l:
            if not p.readonly():
                return False
        return True

    def process_data(self, with_fit: bool = False):
        """
        Process data with processors.

        Args:
            with_fit: Whether to fit processors
        """
        # Process shared data
        _shared_df = self._data
        if not self._is_proc_readonly(self.shared_processors):
            _shared_df = _shared_df.copy()

        _shared_df = self._run_proc_l(
            _shared_df, self.shared_processors, with_fit=with_fit, check_for_infer=True
        )

        # Process inference data
        _infer_df = _shared_df
        if not self._is_proc_readonly(self.infer_processors):
            _infer_df = _infer_df.copy()

        _infer_df = self._run_proc_l(
            _infer_df, self.infer_processors, with_fit=with_fit, check_for_infer=True
        )

        self._infer = _infer_df

        # Process learning data
        if self.process_type == DataHandlerLP.PTYPE_I:
            _learn_df = _shared_df
        elif self.process_type == DataHandlerLP.PTYPE_A:
            _learn_df = _infer_df
        else:
            raise NotImplementedError(f"Process type {self.process_type} is not supported")

        if not self._is_proc_readonly(self.learn_processors):
            _learn_df = _learn_df.copy()

        _learn_df = self._run_proc_l(
            _learn_df, self.learn_processors, with_fit=with_fit, check_for_infer=False
        )

        self._learn = _learn_df

        # Free memory if requested
        if self.drop_raw:
            del self._data

    def config(self, processor_kwargs: dict = None, **kwargs):
        """
        Configure the data handler.

        Args:
            processor_kwargs: Configuration for processors
            **kwargs: Additional configuration parameters
        """
        super().config(**kwargs)

        if processor_kwargs is not None:
            for processor in self.get_all_processors():
                processor.config(**processor_kwargs)

    # Init types
    IT_FIT_SEQ = "fit_seq"  # Sequential fitting
    IT_FIT_IND = "fit_ind"  # Independent fitting
    IT_LS = "load_state"    # Load state

    def setup_data(self, init_type: str = IT_FIT_SEQ, **kwargs):
        """
        Set up the data.

        Args:
            init_type: Type of initialization
            **kwargs: Additional setup parameters
        """
        # Initialize raw data
        super().setup_data(**kwargs)

        # Process data based on init_type
        if init_type == DataHandlerLP.IT_FIT_IND:
            self.fit()
            self.process_data()
        elif init_type == DataHandlerLP.IT_LS:
            self.process_data()
        elif init_type == DataHandlerLP.IT_FIT_SEQ:
            self.fit_process_data()
        else:
            raise NotImplementedError(f"Init type {init_type} is not supported")

    def _get_df_by_key(self, data_key: DATA_KEY_TYPE = DK_I) -> pd.DataFrame:
        """
        Get DataFrame by data key.

        Args:
            data_key: Data key to get

        Returns:
            The corresponding DataFrame
        """
        if data_key == self.DK_R and self.drop_raw:
            raise AttributeError(
                "DataHandlerLP has no attribute _data, please set drop_raw = False if you want to use raw data"
            )

        df = getattr(self, self.ATTR_MAP[data_key])
        return df

    def fetch(
        self,
        selector: Union[pd.Timestamp, slice, str] = slice(None, None),
        level: Union[str, int] = "datetime",
        col_set=DataHandler.CS_ALL,
        data_key: DATA_KEY_TYPE = DK_I,
        squeeze: bool = False,
        proc_func: Callable = None,
    ) -> pd.DataFrame:
        """
        Fetch data from the underlying data source.

        Args:
            selector: How to select data by index
            level: Which index level to select on
            col_set: Which set of columns to select
            data_key: Which data to fetch (raw, infer, learn)
            squeeze: Whether to squeeze columns and index
            proc_func: Function to process data before fetching

        Returns:
            The fetched data
        """
        return self._fetch_data(
            data_storage=self._get_df_by_key(data_key),
            selector=selector,
            level=level,
            col_set=col_set,
            squeeze=squeeze,
            proc_func=proc_func,
        )

    def get_cols(self, col_set=DataHandler.CS_ALL, data_key: DATA_KEY_TYPE = DK_I) -> list:
        """
        Get the column names.

        Args:
            col_set: Which set of columns to get
            data_key: Which data to get columns from

        Returns:
            List of column names
        """
        df = self._get_df_by_key(data_key).head()
        df = fetch_df_by_col(df, col_set)
        return df.columns.to_list()

    @classmethod
    def cast(cls, handler: "DataHandlerLP") -> "DataHandlerLP":
        """
        Cast a handler to DataHandlerLP.

        This method allows sharing processed data without introducing
        package dependencies or complicated data processing logic.

        Args:
            handler: Handler to cast

        Returns:
            Converted DataHandlerLP
        """
        new_hd: DataHandlerLP = object.__new__(DataHandlerLP)
        new_hd.from_cast = True  # Mark as cast instance

        # Copy attributes
        for key in list(DataHandlerLP.ATTR_MAP.values()) + [
            "instruments",
            "start_time",
            "end_time",
            "fetch_orig",
            "drop_raw",
        ]:
            setattr(new_hd, key, getattr(handler, key, None))

        return new_hd

    @classmethod
    def from_df(cls, df: pd.DataFrame) -> "DataHandlerLP":
        """
        Create a DataHandlerLP from a DataFrame.

        This is useful for quick data handling without complex setup.

        Args:
            df: Input DataFrame

        Returns:
            DataHandlerLP instance
        """
        # Import here to avoid circular imports
        from data_system.base.dataset.loader import StaticDataLoader

        loader = StaticDataLoader(df)
        return cls(data_loader=loader)
