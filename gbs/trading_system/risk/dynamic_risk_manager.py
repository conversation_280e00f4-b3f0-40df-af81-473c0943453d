#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
动态风险管理器

根据市场状况动态调整风险度的风险管理器示例
"""

from typing import Dict, Any, Union, List, Optional
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from gbs.trading_system.risk.risk_manager import RiskManager
from gbs.trading_system.risk.order import Order


class DynamicRiskManager(RiskManager):
    """
    动态风险管理器
    
    根据市场状况动态调整风险度的风险管理器示例
    可以根据市场波动率、趋势等因素调整风险度
    """
    
    def __init__(self, 
                 max_position: int = 1000, 
                 stop_loss: float = 0.05, 
                 max_drawdown: float = 0.2, 
                 max_volatility: float = 0.15,
                 risk_degree: float = 0.95,
                 market_data: Optional[pd.DataFrame] = None,
                 volatility_window: int = 20,
                 min_risk_degree: float = 0.5,
                 max_risk_degree: float = 0.95):
        """
        初始化动态风险管理器
        
        Args:
            max_position: 最大持仓数量
            stop_loss: 止损比例
            max_drawdown: 最大回撤限制
            max_volatility: 最大波动率限制
            risk_degree: 基础风险度，表示投资总值的比例（0-1之间）
            market_data: 市场数据，用于计算波动率等指标
            volatility_window: 波动率计算窗口
            min_risk_degree: 最小风险度
            max_risk_degree: 最大风险度
        """
        super().__init__(
            max_position=max_position,
            stop_loss=stop_loss,
            max_drawdown=max_drawdown,
            max_volatility=max_volatility,
            risk_degree=risk_degree
        )
        
        self.market_data = market_data
        self.volatility_window = volatility_window
        self.min_risk_degree = min_risk_degree
        self.max_risk_degree = max_risk_degree
        
        # 缓存计算结果
        self._volatility_cache = {}
        self._risk_degree_cache = {}
    
    def get_risk_degree(self, trade_date=None) -> float:
        """
        获取风险度
        
        根据市场波动率动态调整风险度
        波动率高时降低风险度，波动率低时提高风险度
        
        Args:
            trade_date: 交易日期
            
        Returns:
            风险度（0-1之间的浮点数）
        """
        # 如果没有市场数据，返回基础风险度
        if self.market_data is None or trade_date is None:
            return self.risk_degree
        
        # 检查缓存
        if trade_date in self._risk_degree_cache:
            return self._risk_degree_cache[trade_date]
        
        # 计算市场波动率
        volatility = self._calculate_market_volatility(trade_date)
        
        # 根据波动率调整风险度
        # 波动率越高，风险度越低
        if volatility is None:
            adjusted_risk_degree = self.risk_degree
        else:
            # 将波动率标准化到[0,1]区间
            # 假设正常波动率范围是0-0.3
            normalized_vol = min(1.0, volatility / 0.3)
            
            # 线性调整风险度
            # 当波动率为0时，使用最大风险度
            # 当波动率为0.3或更高时，使用最小风险度
            adjusted_risk_degree = self.max_risk_degree - normalized_vol * (self.max_risk_degree - self.min_risk_degree)
        
        # 确保风险度在有效范围内
        adjusted_risk_degree = max(self.min_risk_degree, min(self.max_risk_degree, adjusted_risk_degree))
        
        # 缓存结果
        self._risk_degree_cache[trade_date] = adjusted_risk_degree
        
        return adjusted_risk_degree
    
    def _calculate_market_volatility(self, trade_date) -> Optional[float]:
        """
        计算市场波动率
        
        Args:
            trade_date: 交易日期
            
        Returns:
            市场波动率
        """
        # 检查缓存
        if trade_date in self._volatility_cache:
            return self._volatility_cache[trade_date]
        
        # 将trade_date转换为pandas Timestamp
        if not isinstance(trade_date, pd.Timestamp):
            try:
                pd_date = pd.Timestamp(trade_date)
            except:
                return None
        else:
            pd_date = trade_date
        
        # 获取历史数据
        try:
            # 找到当前日期在市场数据中的位置
            if pd_date in self.market_data.index:
                current_idx = self.market_data.index.get_loc(pd_date)
            else:
                # 找到最接近的日期
                closest_date = self.market_data.index[self.market_data.index <= pd_date]
                if len(closest_date) == 0:
                    return None
                current_idx = self.market_data.index.get_loc(closest_date[-1])
            
            # 获取窗口数据
            if current_idx < self.volatility_window:
                # 如果历史数据不足，使用所有可用数据
                window_data = self.market_data.iloc[:current_idx+1]
            else:
                window_data = self.market_data.iloc[current_idx-self.volatility_window+1:current_idx+1]
            
            # 计算波动率（标准差）
            if 'close' in window_data.columns:
                # 使用收盘价计算波动率
                returns = window_data['close'].pct_change().dropna()
                volatility = returns.std()
            elif 'return' in window_data.columns:
                # 使用收益率计算波动率
                volatility = window_data['return'].std()
            else:
                # 使用第一列计算波动率
                returns = window_data.iloc[:, 0].pct_change().dropna()
                volatility = returns.std()
            
            # 缓存结果
            self._volatility_cache[trade_date] = volatility
            
            return volatility
        
        except Exception as e:
            print(f"计算市场波动率时出错: {e}")
            return None
    
    def filter_signals(self, 
                       signals: Union[List[Order], Dict[str, float]], 
                       current_weights: Dict[str, float] = None,
                       trade_date=None) -> Union[List[Order], Dict[str, float]]:
        """
        过滤交易信号，应用风险管理规则
        
        除了应用风险度外，还可以实现其他风险控制规则
        例如，限制单个股票的最大权重，限制行业集中度等
        
        Args:
            signals: 交易信号，可以是订单列表或权重字典
            current_weights: 当前权重字典（如果 signals 是权重字典）
            trade_date: 交易日期
            
        Returns:
            过滤后的交易信号
        """
        # 首先应用基类的过滤方法
        filtered_signals = super().filter_signals(signals, current_weights, trade_date)
        
        # 如果是权重字典，应用额外的风险控制规则
        if isinstance(filtered_signals, dict) and len(filtered_signals) > 0:
            # 示例：限制单个股票的最大权重为20%
            max_weight = 0.2
            
            # 找出超过最大权重的股票
            over_weight_stocks = {k: v for k, v in filtered_signals.items() if v > max_weight}
            
            # 如果有超过最大权重的股票，调整权重
            if over_weight_stocks:
                # 计算需要减少的总权重
                total_excess = sum(v - max_weight for v in over_weight_stocks.values())
                
                # 计算其他股票的总权重
                other_stocks = {k: v for k, v in filtered_signals.items() if k not in over_weight_stocks}
                total_other_weight = sum(other_stocks.values())
                
                # 调整权重
                adjusted_signals = {}
                
                # 限制超重股票的权重
                for stock, weight in over_weight_stocks.items():
                    adjusted_signals[stock] = max_weight
                
                # 将多余的权重分配给其他股票
                if total_other_weight > 0:
                    for stock, weight in other_stocks.items():
                        # 按比例增加权重
                        adjusted_signals[stock] = weight + (weight / total_other_weight) * total_excess
                else:
                    # 如果没有其他股票，直接使用原始权重
                    adjusted_signals.update(other_stocks)
                
                return adjusted_signals
        
        return filtered_signals
