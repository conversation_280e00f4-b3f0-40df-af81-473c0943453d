#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
策略工厂模块，用于创建各种策略
"""

import os
import yaml
import importlib
from typing import Dict, Any, Optional, Tuple, Type, Union

# 导入 Backtrader 策略类
from gbs.trading_system.strategies.bt_portfolio_strategy import BtPortfolioStrategy

# 导入风险管理器
from gbs.trading_system.risk.risk_manager import RiskManager
from gbs.trading_system.risk.dynamic_risk_manager import DynamicRiskManager

# 导入 Backtrader
import backtrader as bt

# 导入初始化工具
from gbs.core.utils.mod import init_instance_by_config


class StrategyFactory:
    """
    策略工厂类，用于创建各种策略
    """

    @staticmethod
    def create_risk_manager_from_config(risk_config: Dict[str, Any]) -> Optional[RiskManager]:
        """
        从配置创建风险管理器

        支持两种方式：
        1. 使用预定义的风险管理器类型
        2. 使用自定义的风险管理器类（通过module_path和class指定）

        Args:
            risk_config: 风险管理配置

        Returns:
            风险管理器实例或None
        """
        if not risk_config:
            return None

        # 检查是否指定了风险管理器类型
        risk_type = risk_config.get('type')

        # 如果指定了module_path和class，使用init_instance_by_config
        if 'module_path' in risk_config and 'class' in risk_config:
            try:
                return init_instance_by_config(risk_config)
            except Exception as e:
                print(f"使用init_instance_by_config创建风险管理器失败: {e}")
                # 失败时继续尝试其他方法

        # 使用预定义的风险管理器类型
        if risk_type == 'standard':
            return RiskManager(
                max_position=risk_config.get('max_position', 1000),
                stop_loss=risk_config.get('stop_loss', 0.05),
                max_drawdown=risk_config.get('max_drawdown', 0.2),
                max_volatility=risk_config.get('max_volatility', 0.15),
                risk_degree=risk_config.get('risk_degree', 0.95)
            )
        elif risk_type == 'dynamic':
            # 获取市场数据
            market_data = None
            market_data_path = risk_config.get('market_data_path')
            if market_data_path and os.path.exists(market_data_path):
                try:
                    import pandas as pd
                    market_data = pd.read_csv(market_data_path, index_col=0, parse_dates=True)
                except Exception as e:
                    print(f"加载市场数据失败: {e}")

            return DynamicRiskManager(
                max_position=risk_config.get('max_position', 1000),
                stop_loss=risk_config.get('stop_loss', 0.05),
                max_drawdown=risk_config.get('max_drawdown', 0.2),
                max_volatility=risk_config.get('max_volatility', 0.15),
                risk_degree=risk_config.get('risk_degree', 0.95),
                market_data=market_data,
                volatility_window=risk_config.get('volatility_window', 20),
                min_risk_degree=risk_config.get('min_risk_degree', 0.5),
                max_risk_degree=risk_config.get('max_risk_degree', 0.95)
            )
        elif risk_type:
            # 尝试动态导入风险管理器类
            try:
                # 假设类型格式为 "module.submodule.ClassName"
                module_parts = risk_type.split('.')
                class_name = module_parts[-1]
                module_path = '.'.join(module_parts[:-1])

                # 导入模块
                module = importlib.import_module(module_path)

                # 获取类
                risk_class = getattr(module, class_name)

                # 创建实例
                return risk_class(**{k: v for k, v in risk_config.items() if k != 'type'})
            except Exception as e:
                print(f"动态导入风险管理器类失败: {e}")
                return None
        else:
            # 使用默认风险管理器
            return RiskManager(
                max_position=risk_config.get('max_position', 1000),
                stop_loss=risk_config.get('stop_loss', 0.05),
                max_drawdown=risk_config.get('max_drawdown', 0.2),
                max_volatility=risk_config.get('max_volatility', 0.15),
                risk_degree=risk_config.get('risk_degree', 0.95)
            )

    @staticmethod
    def create_bt_strategy_from_config(config_path: str = None, config: Dict[str, Any] = None) -> Tuple[Type[bt.Strategy], Dict[str, Any], Dict[str, Any]]:
        """
        从配置文件或配置对象创建 Backtrader 策略

        Args:
            config_path: 配置文件路径（与 config 参数二选一）
            config: 配置对象（与 config_path 参数二选一）

        Returns:
            策略类、策略参数和配置的元组
        """
        # 检查参数
        if config_path is None and config is None:
            raise ValueError("必须提供 config_path 或 config 参数")

        # 优先使用直接提供的配置对象
        if config is None:
            # 加载配置文件
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

        # 获取策略类型和参数
        # 支持两种配置格式：直接的 strategy_type 或嵌套的 strategy.type
        strategy_type = config.get('strategy_type')
        if strategy_type is None and 'strategy' in config:
            strategy_type = config.get('strategy', {}).get('type')

        # 支持两种参数格式：直接的 strategy_params 或嵌套的 strategy.params
        strategy_params = config.get('strategy_params', {})
        if not strategy_params and 'strategy' in config:
            strategy_params = config.get('strategy', {}).get('params', {})

        # 获取风险管理配置
        risk_config = config.get('risk_management', {})

        # 获取模型配置
        model_config = config.get('model', {})

        # 创建参数字典
        params = {}

        # 添加策略参数
        params.update(strategy_params)

        # 创建风险管理器
        risk_manager = StrategyFactory.create_risk_manager_from_config(risk_config)
        if risk_manager:
            params['risk_manager'] = risk_manager
        else:
            # 如果没有创建风险管理器，添加风险管理参数
            params['max_position'] = risk_config.get('max_position', 1000)
            params['stop_loss'] = risk_config.get('stop_loss', 0.05)
            params['max_drawdown'] = risk_config.get('max_drawdown', 0.2)
            params['max_volatility'] = risk_config.get('max_volatility', 0.15)
            params['risk_degree'] = risk_config.get('risk_degree', 0.95)

        # 添加模型参数（用于兼容旧版本）
        if model_config.get('use_model', False):
            params['model_path'] = model_config.get('model_path')
            params['model_type'] = model_config.get('model_type')

        # 选择策略类
        if strategy_type == 'PortfolioStrategy':
            strategy_class = BtPortfolioStrategy
        elif strategy_type == 'MomentumStrategy':
            strategy_class = BtMomentumStrategy
        else:
            raise ValueError(f"不支持的策略类型: {strategy_type}")

        return strategy_class, params, config
