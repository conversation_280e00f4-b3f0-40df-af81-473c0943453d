from strategies.base_strategy import Order
from .broker_interface import BrokerInterface

class ExecutionEngine:
    def __init__(self, broker: BrokerInterface):
        self.broker = broker

    def connect(self):
        self.broker.connect()

    def disconnect(self):
        self.broker.disconnect()

    def place_order(self, order: Order):
        return self.broker.place_order(order)

    def get_positions(self):
        return self.broker.get_positions()