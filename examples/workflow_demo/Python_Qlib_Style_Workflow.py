#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GBS 工作流示例 - 使用代码方式运行完整工作流

本示例展示如何使用代码方式运行 GBS 的完整工作流，包括：
1. 数据加载和处理
2. 模型训练
3. 信号生成
4. 交易记录生成
5. 回测分析
6. 绩效归因分析

这个示例相当于使用代码实现 workflow_aipm.yaml 的功能，
展示了如何不依赖 YAML 配置文件，直接使用 Python 代码
来定义和执行完整的量化研究工作流。
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
import torch
import json
import pickle

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, "../.."))
sys.path.insert(0, root_dir)

# 导入 GBS 相关模块
import gbs
from gbs.workflow import R
from gbs.workflow.utils import init_exp_manager
from gbs.core.utils.loki_logger import get_loki_logger
from gbs.core.utils.mod import init_instance_by_config
from gbs.data_system import D
from gbs.model_system.zoo.portfolio_transformer import PortfolioTransformer
from gbs.workflow.record_temp import (
    SignalRecord,
    SignalAnalysisRecord,
    BacktestRecord,
    PerfAttRecord
)

# 获取日志记录器
logger = get_loki_logger("workflow_by_code").logger

def main(runtime_config: dict = {}):
    """
    主函数 - 运行完整工作流
    """
    # 1. 初始化 GBS
    logger.info("初始化 GBS...")

    # 初始化 GBS 配置
    gbs.init(
        provider_uri={
            "__DEFAULT_FREQ": "/home/<USER>/gold-beast-system/data/processed_data/jkp",  # 默认数据路径
            "day": "/home/<USER>/gold-beast-system/data/processed_data/jkp/features"    # 日频数据路径
        },
        mount_path={
            "__DEFAULT_FREQ": "/home/<USER>/gold-beast-system/data/processed_data/jkp",  # 默认挂载路径
            "day": "/home/<USER>/gold-beast-system/data/processed_data/jkp/features"    # 日频挂载路径
        },
        region="us",
        logging_level="INFO"
    )

    # 初始化实验管理器
    init_exp_manager(uri="file:outputs/mlruns", default_exp_name="Qlib_Style_Workflow")

    # 2. 定义工作流配置
    # 这里我们使用代码定义配置，而不是从 YAML 文件加载
    logger.info("定义工作流配置...")

    # 2.1 定义数据集配置
    dataset_config = {
        "class": "DatasetH",
        "module_path": "gbs.data_system.base.dataset",
        "kwargs": {
            "handler": {
                "class": "PortfolioDataHandler",
                "module_path": "gbs.data_system.handlers.portfolio_handler",
                "kwargs": {
                    "config": {
                        "data": {
                            "start_time": "2010-01-01",
                            "end_time": "2020-12-31",
                            "instruments": "us",  # 使用us市场
                            "small_dataset": True,  # 使用小数据集进行测试
                            "small_dataset_size": 3,  # 使用3个月的数据
                            "limit_stocks": 50,  # 每个日期的股票数量限制
                            "data_source_type": "jkp",  # 使用JKP数据
                            # JKP专用DataLoader配置
                            "data_loader": {
                                "class": "StaticDataLoader",
                                "module_path": "gbs.data_system.base.dataset.loader",
                                "kwargs": {
                                    "data_source_type": "jkp",
                                    "jkp_file": "full_usa_ticker.pkl",
                                    "fields_groups": {
                                        "factors": ["ALL"]  # 使用ALL加载所有JKP因子
                                    },
                                    "instruments": "us",
                                    "freq": "day"
                                }
                            }
                        },
                        "segments": {
                            "train": ["2010-01-01", "2018-12-31"],
                            "valid": ["2019-01-01", "2019-06-30"],
                            "test": ["2019-07-01", "2020-12-31"]
                        }
                    }
                }
            },
            "segments": {
                "train": ("2010-01-01", "2018-12-31"),
                "valid": ("2019-01-01", "2019-06-30"),
                "test": ("2019-07-01", "2020-12-31")
            }
        }
    }

    # 2.2 定义模型配置
    model_config = {
        "class": "PortfolioTransformer",
        "module_path": "gbs.model_system.zoo.portfolio_transformer",
        "kwargs": {
            "input_dim": 127,  # 特征维度
            "d_model": 64,
            "num_blocks": 1,
            "d_ff": 128,
            "num_heads": 1,
            "n_epochs": 5,  # 减少训练轮数
            "lr": 0.001,
            "early_stop": 5,
            "batch_size": 64,
            "objective": "MSRR",
            "device": "cpu",  # 强制使用CPU
            "reg_strength": 0.01,
            "sum_penalty": 1.0,
            "neg_penalty": 1.0,
            "concentration_penalty": 0.1,
            "risk_aversion": 1.0
        }
    }

    # 2.3 定义回测配置
    backtest_config = {
        "strategy": {
            "class": "BtPortfolioStrategy",
            "module_path": "gbs.trading_system.strategies.bt_portfolio_strategy",
            "kwargs": {
                "signal": "<PRED>",  # 使用模型预测作为信号
                "top_pct": 0.1,  # 选择权重排名前10%的股票
                "compare_full_portfolio": True
            }
        },
        "backtest": {
            "start_time": runtime_config.get("start_time", "2019-07-01"),  # 与test_start_date保持一致
            "end_time": runtime_config.get("end_time", "2020-12-31"),    # 与test_end_date保持一致
            "initial_capital": runtime_config.get("initial_capital", 1000000),
            "commission": 0.0003,
            "instruments": ["aapl", "msft", "amzn", "nvda", "tsla"]  # 指定回测使用的股票列表
        },
        # OHLCV数据配置 - 专门用于回测
        "data_loader": {
            "class": "StaticDataLoader",
            "module_path": "gbs.data_system.base.dataset.loader",
            "kwargs": {
                "data_source_type": "file",
                "fields_groups": {
                    "price": ["open", "high", "low", "close"],
                    "volume": ["volume"]
                },
                "instruments": ["aapl", "msft", "amzn", "nvda", "tsla"],
                "freq": runtime_config.get("freq", "day")
            }
        }
    }

    # 3. 开始工作流
    logger.info("开始工作流...")
    with R.start(experiment_name="Qlib_Style_Workflow", recorder_name="run_" + datetime.now().strftime("%Y%m%d_%H%M%S")):
        # 3.1 记录配置
        logger.info("记录配置...")
        R.log_params(
            dataset_config=json.dumps(dataset_config),
            model_config=json.dumps(model_config),
            backtest_config=json.dumps(backtest_config)
        )

        # 3.2 初始化数据集
        logger.info("初始化数据集...")
        dataset = init_instance_by_config(dataset_config)

        # 3.3 初始化模型
        logger.info("初始化模型...")
        model = init_instance_by_config(model_config)

        # 3.4 训练模型
        logger.info("训练模型...")
        model.fit(dataset)

        # 3.5 保存模型
        logger.info("保存模型...")
        R.save_objects(params=model)

        # 3.6 获取记录器
        recorder = R.get_recorder()

        # 3.7 生成预测信号
        logger.info("生成预测信号...")
        sr = SignalRecord(model, dataset, recorder)
        sr.generate()

        # 3.8 信号分析（添加索引匹配逻辑解决标签问题）
        logger.info("尝试进行信号分析...")
        try:
            # 首先加载预测信号和JKP数据
            pred_df = sr.load("pred_df.pkl")
            logger.info(f"预测信号形状: {pred_df.shape}")

            # 从JKP数据中提取标签
            import os
            # pandas已经在文件顶部导入

            # 获取JKP数据文件路径
            jkp_file = "full_usa_ticker.pkl"
            provider_uri = "/home/<USER>/gold-beast-system/data/processed_data/jkp"
            jkp_path = os.path.join(provider_uri, jkp_file)
            logger.info(f"加载JKP数据: {jkp_path}")

            # 加载JKP数据
            jkp_data = pd.read_pickle(jkp_path)
            logger.info(f"JKP数据形状: {jkp_data.shape}")

            # 确保JKP数据中的ticker列是字符串类型
            if "ticker" in jkp_data.columns:
                jkp_data["ticker"] = jkp_data["ticker"].astype(str)

            # 确保JKP数据中的eom列是datetime类型
            if "eom" in jkp_data.columns:
                jkp_data["eom"] = pd.to_datetime(jkp_data["eom"])

            # 创建基于ticker的标签数据
            ticker_label = jkp_data.set_index(["ticker", "eom"])[["ret_exc_lead1m"]].copy()
            ticker_label.columns = ["label"]
            logger.info(f"基于ticker的标签数据形状: {ticker_label.shape}")

            # 创建标签DataFrame
            label = pd.DataFrame(index=pred_df.index, columns=["label"])

            # 匹配计数
            match_count = 0

            # 遍历预测信号索引
            for idx in pred_df.index:
                instrument, date = idx

                # 尝试不同的大小写组合
                for ticker in [instrument, instrument.upper(), instrument.lower()]:
                    if (ticker, date) in ticker_label.index:
                        label.loc[idx, "label"] = ticker_label.loc[(ticker, date), "label"]
                        match_count += 1
                        break

            logger.info(f"成功匹配 {match_count} 个标签值 ({match_count/len(pred_df.index):.2%})")

            # 如果匹配率太低，尝试更宽松的日期匹配
            if match_count / len(pred_df.index) < 0.5:
                logger.warning("标签匹配率低于50%，尝试更宽松的日期匹配")

                # 创建日期映射：将预测信号中的日期映射到最接近的JKP数据日期
                jkp_dates = sorted(jkp_data["eom"].unique())
                pred_dates = sorted(set([idx[1] for idx in pred_df.index]))

                date_mapping = {}
                for pred_date in pred_dates:
                    # 找到最接近的JKP日期
                    closest_date = min(jkp_dates, key=lambda x: abs((x - pred_date).days))
                    date_mapping[pred_date] = closest_date

                # 使用日期映射重新匹配
                match_count = 0
                for idx in pred_df.index:
                    instrument, date = idx
                    mapped_date = date_mapping.get(date, date)

                    # 尝试不同的大小写组合
                    for ticker in [instrument, instrument.upper(), instrument.lower()]:
                        if (ticker, mapped_date) in ticker_label.index:
                            label.loc[idx, "label"] = ticker_label.loc[(ticker, mapped_date), "label"]
                            match_count += 1
                            break

                logger.info(f"使用日期映射后成功匹配 {match_count} 个标签值 ({match_count/len(pred_df.index):.2%})")

            # 保存标签数据
            sr.save(**{"label.pkl": label})
            logger.info(f"成功保存标签数据，非NaN值比例: {label['label'].notna().sum() / len(label):.2%}")

            # 使用SignalAnalysisRecord进行信号分析
            sar = SignalAnalysisRecord(
                recorder,
                ana_long_short=True,
                ann_scaler=252
            )
            sar.generate()
            logger.info("信号分析完成")
        except Exception as e:
            logger.error(f"信号分析失败: {str(e)}")
            logger.info("继续执行后续步骤...")

        # 3.9 直接进行回测分析（包含交易记录生成）
        logger.info("尝试进行回测分析...")
        try:
            br = BacktestRecord(recorder, config=backtest_config)
            br.generate()
            logger.info("回测分析完成")
        except Exception as e:
            logger.error(f"回测分析失败: {str(e)}")
            logger.info("继续执行后续步骤...")

        # 3.11 绩效归因分析
        logger.info("尝试进行绩效归因分析...")
        try:
            par = PerfAttRecord(recorder)
            par.generate()
            logger.info("绩效归因分析完成")
        except Exception as e:
            logger.error(f"绩效归因分析失败: {str(e)}")
            logger.info("继续执行后续步骤...")

        # 3.12 记录完成状态
        logger.info("工作流完成")
        R.set_tags(status="completed")

        # 返回记录器ID，方便后续查询
        logger.info(f"工作流记录器ID: {recorder.id}")
        return recorder

if __name__ == "__main__":
    # 运行工作流
    recorder = main()
    logger.info(f"工作流已完成，记录器ID: {recorder.id}")

    # 记录结果位置
    logger.info(f"结果保存在: outputs/mlruns/{recorder.experiment_id}/{recorder.id}/artifacts/")