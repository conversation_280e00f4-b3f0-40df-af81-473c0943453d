# GBS 类qlib风格工作流配置文件 - 跳过训练版本
#
# 这个配置文件采用类似qlib的风格组织配置，并且跳过训练阶段：
# 1. 将model和dataset相关配置放在task下面
# 2. 将data_handler_config放在顶层
# 3. 使用bt_analysis_config和perf_att_config进行回测和绩效归因
# 4. 设置skip_train=true跳过训练阶段，直接使用预训练模型

# GBS初始化配置
gbs_init:
  # 使用字典格式的provider_uri，为不同频率指定不同的数据路径
  provider_uri:
    __DEFAULT_FREQ: "/data/gold-beast-system/gbs/data/processed_data/jkp"  # 默认数据路径
    day: "/data/gold-beast-system/gbs/data/processed_data/jkp/features"    # 日频数据路径
  # 添加mount_path配置，对应provider_uri中的频率
  mount_path:
    __DEFAULT_FREQ: "/data/gold-beast-system/gbs/data/processed_data/jkp"  # 默认挂载路径
    day: "/data/gold-beast-system/gbs/data/processed_data/jkp/features"    # 日频挂载路径
  region: "us"  # 市场区域: us或cn
  logging_level: "INFO"  # 日志级别
  exp_manager:
    class: "MLflowExpManager"
    module_path: "gbs.workflow.expm"
    kwargs:
      uri: "file:outputs/mlruns"  # 使用相对路径
      default_exp_name: "Qlib_Style_Skip_Train_Workflow"
  output_path: "outputs"  # 使用相对路径
  device: "cuda"  # 使用GPU，如果没有GPU可以设置为"cpu"
  seed: 42

# JKP因子数据处理配置
jkp_handler_config: &jkp_handler_config
  # 基本数据参数
  start_time: "2010-01-01"
  end_time: "2020-12-31"
  fit_start_time: "2010-01-01"
  fit_end_time: "2018-12-31"
  instruments: "us"  # 市场区域
  columns:
    date: "eom"
    stock_id: "ticker"
    return: "ret_exc_lead1m"
  limit_stocks: 50  # 每个日期的股票数量限制
  small_dataset: true  # 使用小数据集进行测试
  small_dataset_size: 3  # 使用3个月的数据
  # 处理器配置
  processor:
    factor_selection: false
    log_returns: true
    rank_normalize: true

# 回测分析配置
bt_analysis_config: &bt_analysis_config
  strategy:
    class: "BtPortfolioStrategy"
    module_path: "gbs.trading_system.strategies.bt_portfolio_strategy"
    kwargs:
      signal: "<PRED>"  # 使用模型预测作为信号
      top_pct: 0.1  # 选择权重排名前10%的股票
      compare_full_portfolio: true

  backtest:
    start_time: "2019-07-01"  # 与test_start_date保持一致
    end_time: "2020-12-31"    # 与test_end_date保持一致
    initial_capital: 1000000
    commission: 0.0003
    instruments: ["aapl", "msft", "amzn", "nvda", "tsla"]  # 指定回测使用的股票列表

    # OHLCV数据配置 - 专门用于回测
    data_loader:
      class: "StaticDataLoader"
      module_path: "gbs.data_system.base.dataset.loader"
      kwargs:
        data_source_type: "file"
        fields_groups:
          price: ["open", "high", "low", "close"]
          volume: ["volume"]
        instruments: ["aapl", "msft", "amzn", "nvda", "tsla"]
        freq: "day"

# 绩效归因配置
perf_att_config: &perf_att_config
  analysis_types: ["risk", "quantstats", "brinson"]
  benchmark: "SPY"  # 基准指数
  risk_metrics: ["annual_return", "annual_volatility", "sharpe_ratio", "max_drawdown"]

  # 各分析器的特定配置
  risk:
    periods_per_year: 252  # 年化因子
    risk_free_rate: 0.0  # 无风险利率

  quantstats:
    mode: "basic"  # 分析模式：basic, full

  brinson:
    portfolio_weight_col: "portfolio_weight"  # 投资组合权重列名
    portfolio_return_col: "portfolio_returns"  # 投资组合收益率列名
    benchmark_weight_col: "benchmark_weight"  # 基准权重列名
    benchmark_return_col: "benchmark_returns"  # 基准收益率列名
    sector_col: "sector"  # 行业分类列名
    date_col: "date"  # 日期列名

# 工作流配置
workflow:
  components:
    - predict  # 移除train组件，只保留predict、backtest和analyze
    - backtest
    - analyze
  skip_train: true  # 设置为true跳过训练阶段
  skip_predict: false
  save_artifacts: true
  log_level: INFO
  custom_evaluate: true

task:
  # 模型配置 - 使用预训练模型
  model:
    class: "PortfolioTransformer"
    module_path: "gbs.model_system.zoo.portfolio_transformer"
    kwargs:
      # 预训练模型路径 - 使用最近一次成功运行的实验中的params.pkl文件
      pretrained_path: "outputs/mlruns/903910728330385575/eae28e415c564c278ebdeff8ed0861e0/artifacts/params.pkl"
      # 模型结构参数 - 需要与预训练模型一致
      input_dim: 127  # 特征维度
      d_model: 64
      num_blocks: 1
      d_ff: 128
      num_heads: 1
      device: "cuda"  # 使用GPU，如果没有GPU可以设置为"cpu"

  # 数据集配置 - 使用DataLoaderDH管理多个DataHandler
  dataset:
    class: "DatasetH"
    module_path: "gbs.data_system.base.dataset"
    kwargs:
      handler:
        # 使用DataLoaderDH管理JKP数据处理器
        class: "DataLoaderDH"
        module_path: "gbs.data_system.base.dataset.loader"
        kwargs:
          is_group: false
          # 配置DataHandler
          handler_config:
            class: "PortfolioDataHandler"
            module_path: "gbs.data_system.handlers.portfolio_handler"
            kwargs:
              config:
                data:
                  # 引用JKP处理配置
                  <<: *jkp_handler_config
                  # JKP专用DataLoader配置
                  data_loader:
                    class: "StaticDataLoader"
                    module_path: "gbs.data_system.base.dataset.loader"
                    kwargs:
                      data_source_type: "jkp"
                      jkp_file: "full_usa_ticker.pkl"
                      fields_groups:
                        factors: ["ALL"]  # 使用ALL加载所有JKP因子
                      instruments: "us"
                      freq: "day"

          # 数据获取参数
          fetch_kwargs:
            col_set: "__raw"  # 使用原始数据，不进行列过滤

      segments:
        train: ["2010-01-01", "2018-12-31"]
        valid: ["2019-01-01", "2019-06-30"]
        test: ["2019-07-01", "2020-12-31"]

  # 记录配置
  record:
    - class: "SignalRecord"
      module_path: "gbs.workflow.record_temp"
      kwargs:
        model: "<MODEL>"
        dataset: "<DATASET>"

    - class: "SignalAnalysisRecord"
      module_path: "gbs.workflow.record_temp"
      kwargs:
        ana_long_short: true
        ann_scaler: 252
        jkp_file: "full_usa_ticker.pkl"  # 使用与数据加载相同的JKP文件

    - class: "TradeRecord"
      module_path: "gbs.workflow.trade_record"
      kwargs:
        top_pct: 0.1  # 选择排名前10%的股票
        initial_capital: 1000000  # 初始资金100万
        skip_existing: false

    - class: "BacktestRecord"
      module_path: "gbs.workflow.record_temp"
      kwargs:
        config: *bt_analysis_config

    - class: "PerfAttRecord"
      module_path: "gbs.workflow.record_temp"
      kwargs:
        perf_att_config: *perf_att_config
