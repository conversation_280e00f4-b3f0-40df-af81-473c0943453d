#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GBS 工作流示例 - 使用代码方式运行仅回测工作流

本示例展示如何使用代码方式运行 GBS 的仅回测工作流，包括：
1. 直接使用已有的交易记录
2. 回测分析
3. 绩效归因分析

这个示例相当于使用代码实现 backtest_only.yaml 的功能，
展示了如何不依赖 YAML 配置文件，直接使用 Python 代码
来定义和执行仅回测的工作流。
"""

from gbs.workflow.record_temp import (
    BacktestRecord,
    PerfAttRecord
)
from gbs.core.utils.loki_logger import get_loki_logger
from gbs.workflow.utils import init_exp_manager
from gbs.workflow import R
import gbs
import os
import sys
from pathlib import Path
from datetime import datetime
import json

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, "../.."))
sys.path.insert(0, root_dir)

# 导入 GBS 相关模块

# 获取日志记录器
logger = get_loki_logger("backtest_only_by_code").logger


def main(runtime_config: dict = {}):
    """
    主函数 - 运行仅回测工作流
    """
    # 1. 初始化 GBS
    logger.info("初始化 GBS...")

    # 初始化 GBS 配置
    gbs.init(
        provider_uri={
            "__DEFAULT_FREQ": "/data/gold-beast-system/gbs/data/processed_data/jkp",  # 默认数据路径
            "day": "/data/gold-beast-system/gbs/data/processed_data/jkp/features"    # 日频数据路径
        },
        mount_path={
            "__DEFAULT_FREQ": "/data/gold-beast-system/gbs/data/processed_data/jkp",  # 默认挂载路径
            "day": "/data/gold-beast-system/gbs/data/processed_data/jkp/features"    # 日频挂载路径
        },
        region="us",
        logging_level="INFO"
    )

    # 初始化实验管理器
    init_exp_manager(uri="file:outputs/mlruns",
                     default_exp_name="Backtest_Only_Workflow")

    # 2. 定义工作流配置
    # 这里我们使用代码定义配置，而不是从 YAML 文件加载
    logger.info("定义工作流配置...")

    # 2.1 定义回测配置
    backtest_config = {
        "strategy": {
            "class": "BtPortfolioStrategy",
            "module_path": "gbs.trading_system.strategies.bt_portfolio_strategy",
            "kwargs": {
                "signal_from_trade_record": True,  # 使用TradeRecord的结果作为信号
                "signal": None,  # 显式设置signal为None，让策略知道应该从TradeRecord加载数据
                "top_pct": 0.1,  # 选择权重排名前10%的股票
                "compare_full_portfolio": True
            }
        },
        "backtest": {
            "start_time": runtime_config.get("start_time", "2019-07-01"),
            "end_time": runtime_config.get("end_time", "2020-12-31"),
            "initial_capital": runtime_config.get("initial_capital", 1000000),
            "commission": 0.0003,
            # 指定回测使用的股票列表
            "instruments": ["aapl", "msft", "amzn", "nvda", "tsla"]
        },
        # OHLCV数据配置 - 专门用于回测
        "data_loader": {
            "class": "StaticDataLoader",
            "module_path": "gbs.data_system.base.dataset.loader",
            "kwargs": {
                "data_source_type": "file",
                "fields_groups": {
                    "price": ["open", "high", "low", "close"],
                    "volume": ["volume"]
                },
                "instruments": ["aapl", "msft", "amzn", "nvda", "tsla"],
                "freq": runtime_config.get("freq", "day")
            }
        }
    }

    # 2.2 定义绩效归因配置
    perf_att_config = {
        "analysis_types": ["risk", "quantstats", "brinson"],
        "benchmark": "SPY",  # 基准指数
        "risk_metrics": ["annual_return", "annual_volatility", "sharpe_ratio", "max_drawdown"],

        # 各分析器的特定配置
        "risk": {
            "periods_per_year": 252,  # 年化因子
            "risk_free_rate": 0.0  # 无风险利率
        },

        "quantstats": {
            "mode": "basic"  # 分析模式：basic, full
        },

        "brinson": {
            "portfolio_weight_col": "portfolio_weight",  # 投资组合权重列名
            "portfolio_return_col": "portfolio_returns",  # 投资组合收益率列名
            "benchmark_weight_col": "benchmark_weight",  # 基准权重列名
            "benchmark_return_col": "benchmark_returns",  # 基准收益率列名
            "sector_col": "sector",  # 行业分类列名
            "date_col": "date"  # 日期列名
        }
    }

    # 3. 开始工作流
    logger.info("开始工作流...")
    with R.start(experiment_name="Backtest_Only_Workflow", recorder_name="run_" + datetime.now().strftime("%Y%m%d_%H%M%S")):
        # 3.1 记录配置
        logger.info("记录配置...")
        R.log_params(
            backtest_config=json.dumps(backtest_config),
            perf_att_config=json.dumps(perf_att_config)
        )

        # 3.2 获取记录器
        recorder = R.get_recorder()
        # 3.3 直接进行回测分析（使用已有的交易记录）
        logger.info("尝试进行回测分析...")
        try:
            # 指定TradeRecord的实验ID和运行ID
            trade_record_exp_id = "380860726240567684"  # 实际的实验ID
            trade_record_run_id = "708e68cc431e49b8abddd5f3027d278e"  # 实际的运行ID

            br = BacktestRecord(
                recorder,
                config=backtest_config,
                trade_record_exp_id=trade_record_exp_id,
                trade_record_run_id=trade_record_run_id
            )
            br.generate()
            logger.info("回测分析完成")
        except Exception as e:
            logger.error(f"回测分析失败: {str(e)}")
            logger.info("继续执行后续步骤...")

        # 3.4 绩效归因分析
        logger.info("尝试进行绩效归因分析...")
        try:
            par = PerfAttRecord(recorder, perf_att_config=perf_att_config)
            par.generate()
            logger.info("绩效归因分析完成")
        except Exception as e:
            logger.error(f"绩效归因分析失败: {str(e)}")
            logger.info("继续执行后续步骤...")

        # 3.5 记录完成状态
        logger.info("工作流完成")
        R.set_tags(status="completed")

        # 返回记录器ID，方便后续查询
        logger.info(f"工作流记录器ID: {recorder.id}")
        return recorder


if __name__ == "__main__":
    # 运行工作流
    recorder = main()
    logger.info(f"工作流已完成，记录器ID: {recorder.id}")

    # 记录结果位置
    logger.info(
        f"结果保存在: outputs/mlruns/{recorder.experiment_id}/{recorder.id}/artifacts/")
