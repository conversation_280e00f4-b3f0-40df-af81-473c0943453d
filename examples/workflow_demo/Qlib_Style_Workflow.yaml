# GBS 类qlib风格工作流配置文件
#
# 这个配置文件采用类似qlib的风格组织配置：
# 1. 将model和dataset相关配置放在task下面
# 2. 将data_handler_config放在顶层
# 3. 使用bt_analysis_config和perf_att_config进行回测和绩效归因

# GBS初始化配置
gbs_init:
  # 使用字典格式的provider_uri，为不同频率指定不同的数据路径
  provider_uri:
    __DEFAULT_FREQ: "/home/<USER>/gold-beast-system/gbs/data/gbs_data/us_data"  # 使用us_data作为默认数据路径
    day: "/home/<USER>/gold-beast-system/gbs/data/gbs_data/us_data"             # 日频数据路径
  # 添加mount_path配置，对应provider_uri中的频率
  mount_path:
    __DEFAULT_FREQ: "/home/<USER>/gold-beast-system/gbs/data/gbs_data/us_data"  # 使用us_data作为默认挂载路径
    day: "/home/<USER>/gold-beast-system/gbs/data/gbs_data/us_data"             # 日频挂载路径
  region: "us"  # 市场区域: us或cn
  logging_level: "DEBUG"  # 日志级别
  exp_manager:
    class: "MLflowExpManager"
    module_path: "gbs.workflow.expm"
    kwargs:
      uri: "file:outputs/mlruns"  # 使用相对路径
      default_exp_name: "Qlib_Style_Workflow"
  output_path: "outputs"  # 使用相对路径
  device: "cpu"  # 使用CPU，避免CUDA相关错误
  seed: 42

# JKP因子数据处理配置
jkp_handler_config: &jkp_handler_config
  # 基本数据参数
  start_time: "2010-01-01"
  end_time: "2020-12-31"
  fit_start_time: "2010-01-01"
  fit_end_time: "2018-12-31"
  instruments: "us"  # 市场区域
  columns:
    date: "eom"
    stock_id: "ticker"
    return: "ret_exc_lead1m"
  limit_stocks: 50  # 每个日期的股票数量限制
  small_dataset: false  # 使用完整数据集
  small_dataset_size: 3  # 使用3个月的数据（当small_dataset为true时使用）
  # 处理器配置
  processor:
    factor_selection: false
    log_returns: true
    rank_normalize: true
  # 添加数据格式转换配置
  format_conversion:
    to_dict: true  # 将DataFrame转换为字典格式
    dict_fields:
      features: ["ALL"]  # 使用所有列作为特征
      exclude: ["eom", "ticker", "ret_exc_lead1m"]  # 排除这些列
  # 添加数据源类型
  data_source_type: "jkp"  # 使用JKP数据源
  # 添加JKP数据文件路径
  jkp_file: "full_usa.pkl"  # 使用实际存在的文件

#

# 回测分析配置
bt_analysis_config: &bt_analysis_config
  strategy:
    class: "BtPortfolioStrategy"
    module_path: "gbs.trading_system.strategies.bt_portfolio_strategy"
    kwargs:
      signal: "<PRED>"  # 使用模型预测作为信号
      top_pct: 0.8  # 选择权重排名前80%的股票，这样会选择更多的股票
      compare_full_portfolio: true
      min_position_size: 1  # 最小持仓数量为1股

  backtest:
    start_time: "2020-07-01"  # 与test分段的起始日期一致
    end_time: "2020-12-31"    # 与test分段的结束日期一致
    initial_capital: 1000000
    commission: 0.0003
    instruments: ["AAPL", "AMZN", "NVDA", "TSLA"]  # 回测使用的股票列表

    # OHLCV数据配置 - 专门用于回测，使用us_data目录中的数据
    data_loader:
      class: "StaticDataLoader"
      module_path: "gbs.data_system.base.dataset.loader"
      kwargs:
        data_source_type: "file"  # 使用文件数据源
        provider_uri: "/home/<USER>/gold-beast-system/gbs/data/gbs_data/us_data"  # 使用us_data目录
        fields_groups:
          price: ["open", "high", "low", "close"]
          volume: ["volume"]
        instruments: ["AAPL", "AMZN", "NVDA", "TSLA"]  # 使用这些股票进行回测
        freq: "day"

# 绩效归因配置
perf_att_config: &perf_att_config
  analysis_types: ["risk", "quantstats", "brinson"]
  benchmark: "SPY"  # 基准指数
  risk_metrics: ["annual_return", "annual_volatility", "sharpe_ratio", "max_drawdown"]

  # 各分析器的特定配置
  risk:
    periods_per_year: 252  # 年化因子
    risk_free_rate: 0.0  # 无风险利率

  quantstats:
    mode: "basic"  # 分析模式：basic, full

  brinson:
    portfolio_weight_col: "portfolio_weight"  # 投资组合权重列名
    portfolio_return_col: "portfolio_returns"  # 投资组合收益率列名
    benchmark_weight_col: "benchmark_weight"  # 基准权重列名
    benchmark_return_col: "benchmark_returns"  # 基准收益率列名
    sector_col: "sector"  # 行业分类列名
    date_col: "date"  # 日期列名

# 工作流配置
workflow:
  components:
    - train
    - predict
    - backtest
    - analyze
  skip_train: false  # 修改为false，确保执行训练步骤
  skip_predict: false
  save_artifacts: true
  log_level: DEBUG
  custom_evaluate: true

# 任务配置 - qlib风格
task:
  # 模型配置
  model:
    class: "PortfolioTransformer"
    module_path: "gbs.model_system.zoo.portfolio_transformer"
    kwargs:
      input_dim: 127  # 特征维度
      d_model: 64
      num_blocks: 1
      d_ff: 128
      num_heads: 1
      n_epochs: 5  # 减少训练轮数
      lr: 0.001
      early_stop: 2
      batch_size: 64
      objective: "MSRR"
      device: "cpu"  # 使用CPU，避免CUDA相关错误
      reg_strength: 0.01
      sum_penalty: 1.0
      neg_penalty: 1.0
      concentration_penalty: 0.1
      risk_aversion: 1.0
      # 添加数据格式处理配置
      expect_dict_format: true  # 期望字典格式的数据
      handle_format_conversion: true  # 自动处理格式转换

  # 数据集配置 - 使用DataLoaderDH管理JKP数据处理器
  dataset:
    class: "DatasetH"
    module_path: "gbs.data_system.base.dataset"
    kwargs:
      handler:
        # 使用DataLoaderDH管理JKP数据处理器
        class: "DataLoaderDH"
        module_path: "gbs.data_system.base.dataset.loader"
        kwargs:
          is_group: false
          # 直接配置JKP数据处理器
          handler_config:
            class: "PortfolioDataHandler"
            module_path: "gbs.data_system.handlers.portfolio_handler"
            kwargs:
              config:
                data:
                  # 基本数据参数
                  start_time: "2010-01-01"
                  end_time: "2020-12-31"
                  fit_start_time: "2010-01-01"
                  fit_end_time: "2018-12-31"
                  instruments: ["AAPL", "AMZN", "NVDA", "TSLA"]  # 使用具体的股票代码列表
                  columns:
                    date: "eom"
                    stock_id: "ticker"
                    return: "ret_exc_lead1m"
                  limit_stocks: 50  # 每个日期的股票数量限制
                  small_dataset: false  # 使用完整数据集
                  # 处理器配置
                  processor:
                    factor_selection: false
                    log_returns: true
                    rank_normalize: true
                  # 添加数据格式转换配置
                  format_conversion:
                    to_dict: true  # 将DataFrame转换为字典格式
                    dict_fields:
                      features: ["open", "high", "low", "close", "volume"]  # 使用具体的字段列表
                      exclude: ["eom", "ticker", "ret_exc_lead1m"]  # 排除这些列
                  # 添加数据源类型
                  data_source_type: "file"  # 使用文件数据源
                  # 添加数据路径
                  provider_uri: "/home/<USER>/gold-beast-system/gbs/data/gbs_data/us_data"  # 使用us_data目录
                  # 使用us_data目录中的数据
                  data_loader:
                    class: "StaticDataLoader"
                    module_path: "gbs.data_system.base.dataset.loader"
                    kwargs:
                      data_source_type: "file"  # 使用文件数据源
                      provider_uri: "/home/<USER>/gold-beast-system/gbs/data/gbs_data/us_data"  # 使用us_data目录
                      fields_groups:
                        factors: ["open", "high", "low", "close", "volume"]  # 使用具体的字段列表
                        labels: ["ret_exc_lead1m"]  # 添加标签字段
                        # 排除日期列和其他可能导致问题的列
                        exclude: ["eom", "date", "datetime"]
                      instruments: ["AAPL", "AMZN", "NVDA", "TSLA"]  # 使用具体的股票代码列表
                      freq: "day"
                      # 添加日期处理配置
                      date_column_name: "eom"
                      handle_date_column: true
                      # 添加数据格式转换配置
                      format_conversion:
                        to_dict: true  # 将DataFrame转换为字典格式
                        dict_fields:
                          features: ["ALL"]  # 使用所有列作为特征
                          exclude: ["eom", "ticker", "ret_exc_lead1m"]  # 排除这些列

          # 数据获取参数
          fetch_kwargs:
            col_set: "__raw"  # 使用原始数据，不进行列过滤

      segments:
        train: ["2018-01-01", "2018-12-31"]
        valid: ["2019-01-01", "2019-06-30"]
        test: ["2020-07-01", "2020-12-31"]

  # 记录配置
  record:
    - class: "SignalRecord"
      module_path: "gbs.workflow.signal_record"
      kwargs:
        model: "<MODEL>"
        dataset: "<DATASET>"

    - class: "LabelRecord"
      module_path: "gbs.workflow.label_record"
      kwargs:
        label_field: "ret_exc_lead1m"
        skip_existing: false

    - class: "SignalAnalysisRecord"
      module_path: "gbs.workflow.signal_analysis_record"
      kwargs:
        ana_long_short: true
        ann_scaler: 252

    - class: "TradeRecord"
      module_path: "gbs.workflow.trade_record"
      kwargs:
        top_pct: 0.1  # 选择排名前10%的股票
        initial_capital: 1000000  # 初始资金100万
        skip_existing: false

    - class: "BacktestRecord"
      module_path: "gbs.workflow.backtest_record"
      kwargs:
        config: *bt_analysis_config

    - class: "PerfAttRecord"
      module_path: "gbs.workflow.perf_att_record"
      kwargs:
        perf_att_config: *perf_att_config
