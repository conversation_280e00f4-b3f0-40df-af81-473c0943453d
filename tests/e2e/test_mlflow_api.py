#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MLflow Management API 端点的 E2E 测试

测试 /gbs-api/v1/mlflows 相关的所有端点：
- GET /gbs-api/v1/mlflows/history - 获取 MLflow 运行历史
- GET /gbs-api/v1/mlflows/run-detail - 获取特定运行的详细结果
- DELETE /gbs-api/v1/mlflows/runs/{experiment_id}/{run_id} - 删除特定运行
- GET /gbs-api/v1/mlflows/metadata - 获取 MLflow 元数据
- POST /gbs-api/v1/mlflows/update-run-name - 更新运行名称

测试策略：
1. 先创建投资组合并运行回测，确保有数据可用
2. 然后测试各种 MLflow 管理接口
"""

import pytest
import uuid
from typing import Dict, Any
from e2e.backtest_file_checker import check_strategy_backtest_files


@pytest.fixture(scope="module")
def test_portfolio_for_mlflow(api_client: Dict[str, Any]):
    """
    创建一个测试用的投资组合，用于生成 MLflow 数据

    使用 module 级别的 fixture，确保在整个测试模块中只创建一次
    """
    # 创建测试投资组合
    portfolio_data = {
        "name": f"MLflow Test Portfolio {uuid.uuid4().hex[:8]}",
        "description": "Created for MLflow API testing",
        "model_type": "MH",
        "stocks": [
            {"ticker": "AAPL", "weight": 0.7},
            {"ticker": "AMZN", "weight": 0.3}
        ]
    }

    # 创建投资组合
    create_response = api_client["post"]("portfolios", json=portfolio_data)
    assert create_response.status_code == 200, f"Failed to create test portfolio: {create_response.text}"

    portfolio = create_response.json()["data"]

    yield portfolio

    # 测试结束后清理：删除创建的投资组合
    try:
        api_client["delete"](f"portfolios/{portfolio['id']}")
        # 不强制要求删除成功，因为可能在测试过程中已经被删除
    except Exception as e:
        print(f"Warning: Failed to cleanup test portfolio: {e}")


@pytest.fixture(scope="module")
def mlflow_run_data(api_client: Dict[str, Any], test_portfolio_for_mlflow: Dict[str, Any]):
    """
    运行投资组合回测，生成 MLflow 数据

    返回回测运行的 experiment_id 和 run_id
    """
    portfolio = test_portfolio_for_mlflow

    # 准备回测请求参数
    backtest_request = {
        "portfolio_id": portfolio["id"],
        "start_date": "2020-01-01",
        "end_date": "2020-12-31",  # 使用较短的时间范围以加快测试速度
        "initial_capital": 1000000,
        "benchmark": "SPY",
        "freq": "day",
        "rebalance_freq": "monthly"
    }

    # 运行投资组合回测
    backtest_response = api_client["post"]("portfolio-backtest", json=backtest_request)
    assert backtest_response.status_code == 200, f"Failed to run portfolio backtest: {backtest_response.text}"

    # 获取回测结果数据
    backtest_data = backtest_response.json()
    assert backtest_data["status"] == "success", f"Backtest failed: {backtest_data}"

    run_data = {
        "experiment_id": backtest_data["experiment_id"],
        "run_id": backtest_data["run_id"],
        "portfolio_name": portfolio["name"]
    }

    yield run_data

    # 测试完成后清理 MLflow 运行
    try:
        delete_response = api_client["delete"](f"mlflows/runs/{run_data['experiment_id']}/{run_data['run_id']}")
        if delete_response.status_code == 200:
            print(f"MLflow run {run_data['run_id']} cleaned up successfully")
        else:
            print(f"Failed to clean up MLflow run {run_data['run_id']}: {delete_response.status_code}")
    except Exception as e:
        print(f"Error cleaning up MLflow run {run_data['run_id']}: {str(e)}")


# ============================================================================
# MLflow 数据预热测试 - 确保有数据可供其他测试使用
# ============================================================================

def test_00_mlflow_data_warmup(api_client: Dict[str, Any]):
    """
    预热测试：运行一个简单的投资组合回测来生成 MLflow 数据

    这个测试确保后续的 MLflow API 测试有数据可用
    使用 test_00_ 前缀确保它首先运行
    """
    # 创建一个临时投资组合用于预热
    temp_portfolio_data = {
        "name": f"MLflow Warmup Portfolio {uuid.uuid4().hex[:8]}",
        "description": "Temporary portfolio for MLflow data warmup",
        "model_type": "MH",
        "stocks": [
            {"ticker": "AAPL", "weight": 1.0}
        ]
    }

    # 创建临时投资组合
    create_response = api_client["post"]("portfolios", json=temp_portfolio_data)
    assert create_response.status_code == 200, f"Failed to create warmup portfolio: {create_response.text}"

    temp_portfolio = create_response.json()["data"]
    warmup_experiment_id = None
    warmup_run_id = None

    try:
        # 运行一个快速回测来生成 MLflow 数据
        backtest_request = {
            "portfolio_id": temp_portfolio["id"],
            "start_date": "2020-01-01",
            "end_date": "2020-01-31",  # 只用一个月的数据，快速完成
            "initial_capital": 100000,
            "benchmark": "SPY",
            "freq": "day",
            "rebalance_freq": "monthly"
        }

        backtest_response = api_client["post"]("portfolio-backtest", json=backtest_request)
        assert backtest_response.status_code == 200, f"Failed to run warmup backtest: {backtest_response.text}"

        # 获取生成的 MLflow 运行信息用于后续清理
        backtest_data = backtest_response.json()
        if "experiment_id" in backtest_data:
            warmup_experiment_id = backtest_data["experiment_id"]
        if "run_id" in backtest_data:
            warmup_run_id = backtest_data["run_id"]

        print("MLflow data warmup completed successfully")

    finally:
        # 清理临时投资组合
        try:
            api_client["delete"](f"portfolios/{temp_portfolio['id']}")
        except:
            pass  # 忽略清理错误

        # 清理生成的 MLflow 运行（如果有的话）
        if warmup_experiment_id and warmup_run_id:
            try:
                delete_response = api_client["delete"](f"mlflows/runs/{warmup_experiment_id}/{warmup_run_id}")
                if delete_response.status_code == 200:
                    print("Warmup MLflow run cleaned up successfully")
            except:
                pass  # 忽略清理错误


# ============================================================================
# MLflow 元数据测试
# ============================================================================

def test_get_mlflow_metadata(api_client: Dict[str, Any]):
    """
    测试获取 MLflow 元数据端点

    验证 GET /gbs-api/v1/mlflows/metadata 端点返回正确的元数据
    注意：依赖于 test_00_mlflow_data_warmup 预热测试提供数据
    """
    # 发送请求
    response = api_client["get"]("mlflows/metadata")

    # 验证状态码 - 预热后应该有数据
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # 验证返回数据结构
    data = response.json()
    assert isinstance(data, dict), f"Expected dict, got {type(data)}"

    # 验证包含必要的字段（根据实际API响应调整）
    # 这里先做基础验证，具体字段可能需要根据实际响应调整
    print(f"MLflow metadata response: {data}")


# ============================================================================
# MLflow 历史记录测试
# ============================================================================

def test_get_mlflow_history_basic(api_client: Dict[str, Any], mlflow_run_data: Dict[str, Any]):
    """
    测试获取 MLflow 运行历史的基本功能

    验证 GET /gbs-api/v1/mlflows/history 端点返回正确的历史记录
    """
    # 发送请求
    response = api_client["get"]("mlflows/history")

    # 验证状态码
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # 验证返回数据结构
    data = response.json()
    assert isinstance(data, dict), f"Expected dict, got {type(data)}"

    # MLflow history API 返回格式: {count: int, runs: [...], status: 'success'}
    assert "runs" in data, f"Missing 'runs' field in response: {data.keys()}"
    assert "status" in data, f"Missing 'status' field in response: {data.keys()}"
    assert data["status"] == "success", f"Expected status 'success', got {data['status']}"

    runs = data["runs"]
    assert isinstance(runs, list), f"Expected list of runs, got {type(runs)}"

    # 验证我们的测试运行在历史记录中
    test_run_found = False
    for run in runs:
        if (run.get("experiment_id") == mlflow_run_data["experiment_id"] and
            run.get("run_id") == mlflow_run_data["run_id"]):
            test_run_found = True
            break

    assert test_run_found, f"Test run not found in history. Expected experiment_id: {mlflow_run_data['experiment_id']}, run_id: {mlflow_run_data['run_id']}"


def test_get_mlflow_history_with_filters(api_client: Dict[str, Any], mlflow_run_data: Dict[str, Any]):
    """
    测试带过滤条件的 MLflow 运行历史

    验证各种过滤参数的功能
    """
    # 测试 limit 参数
    response = api_client["get"]("mlflows/history", params={"limit": 5})
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    data = response.json()
    assert isinstance(data, dict), f"Expected dict, got {type(data)}"
    assert "runs" in data, f"Missing 'runs' field in response"

    runs = data["runs"]
    assert isinstance(runs, list), f"Expected list of runs, got {type(runs)}"
    assert len(runs) <= 5, f"Expected at most 5 runs, got {len(runs)}"

    # 测试 strategy_name 过滤（使用投资组合名称）
    response = api_client["get"]("mlflows/history", params={
        "strategy_name": mlflow_run_data["portfolio_name"]
    })
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # 测试日期过滤（可能不被支持，所以允许失败）
    response = api_client["get"]("mlflows/history", params={
        "start_date": "2020-01-01",
        "end_date": "2020-12-31"
    })
    # 日期过滤可能不被支持，允许返回500错误
    assert response.status_code in [200, 500], f"Unexpected status code: {response.status_code}"


# ============================================================================
# MLflow 运行详情测试
# ============================================================================

def test_get_mlflow_run_detail(api_client: Dict[str, Any], mlflow_run_data: Dict[str, Any]):
    """
    测试获取特定 MLflow 运行的详细结果

    验证 GET /gbs-api/v1/mlflows/run-detail 端点返回正确的详细信息，
    并检查对应的回测文件是否存在
    """
    experiment_id = mlflow_run_data["experiment_id"]
    run_id = mlflow_run_data["run_id"]

    # 发送请求
    response = api_client["get"]("mlflows/run-detail", params={
        "run_id": run_id,
        "experiment_id": experiment_id
    })

    # 验证状态码
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # 验证返回数据结构
    data = response.json()
    assert isinstance(data, dict), f"Expected dict, got {type(data)}"

    # 验证包含详细信息（根据实际API响应调整）
    print(f"MLflow run detail response: {data}")

    # 检查对应的回测文件是否存在
    # 这验证了 run-detail API 返回的数据确实对应真实存在的回测结果
    check_strategy_backtest_files(experiment_id, run_id)


def test_get_mlflow_run_detail_invalid_params(api_client: Dict[str, Any]):
    """
    测试使用无效参数获取 MLflow 运行详情

    验证错误处理
    """
    # 测试无效的 run_id
    response = api_client["get"]("mlflows/run-detail", params={
        "run_id": "invalid_run_id",
        "experiment_id": "invalid_experiment_id"
    })

    # 应该返回错误状态码或空结果
    assert response.status_code in [200, 404, 400], f"Unexpected status code: {response.status_code}"


# ============================================================================
# MLflow 运行名称更新测试
# ============================================================================

def test_update_mlflow_run_name(api_client: Dict[str, Any], mlflow_run_data: Dict[str, Any]):
    """
    测试更新 MLflow 运行名称

    验证 POST /gbs-api/v1/mlflows/update-run-name 端点功能
    """
    new_name = f"Updated Test Run {uuid.uuid4().hex[:8]}"

    # 准备更新请求
    update_request = {
        "experiment_id": mlflow_run_data["experiment_id"],
        "run_id": mlflow_run_data["run_id"],
        "new_name": new_name
    }

    # 发送更新请求
    response = api_client["post"]("mlflows/update-run-name", json=update_request)

    # 验证状态码
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # 验证返回数据结构
    data = response.json()
    assert isinstance(data, dict), f"Expected dict, got {type(data)}"
    assert "status" in data, "Missing 'status' field in response"
    assert "message" in data, "Missing 'message' field in response"

    # 验证更新成功
    assert data["status"] == "success", f"Update failed: {data}"
    assert data["new_name"] == new_name, f"Name not updated correctly: expected {new_name}, got {data.get('new_name')}"


def test_update_mlflow_run_name_invalid_params(api_client: Dict[str, Any]):
    """
    测试使用无效参数更新 MLflow 运行名称

    验证错误处理
    """
    # 测试无效的参数
    invalid_request = {
        "experiment_id": "invalid_experiment_id",
        "run_id": "invalid_run_id",
        "new_name": "Invalid Update Test"
    }

    # 发送更新请求
    response = api_client["post"]("mlflows/update-run-name", json=invalid_request)

    # 应该返回错误状态码
    assert response.status_code in [400, 404, 500], f"Expected error status code, got {response.status_code}"


# ============================================================================
# MLflow 运行删除测试
# ============================================================================

def test_delete_mlflow_run_invalid_params(api_client: Dict[str, Any]):
    """
    测试删除不存在的 MLflow 运行

    验证 DELETE /gbs-api/v1/mlflows/runs/{experiment_id}/{run_id} 端点的错误处理
    """
    # 使用无效的 experiment_id 和 run_id
    invalid_experiment_id = "invalid_experiment_id"
    invalid_run_id = "invalid_run_id"

    # 发送删除请求
    response = api_client["delete"](f"mlflows/runs/{invalid_experiment_id}/{invalid_run_id}")

    # 应该返回错误状态码或成功状态码（取决于API实现）
    assert response.status_code in [200, 404, 400], f"Unexpected status code: {response.status_code}"


def test_delete_mlflow_run_success(api_client: Dict[str, Any]):
    """
    测试成功删除 MLflow 运行

    注意：这个测试会创建一个新的运行然后删除它，以避免影响其他测试
    """
    # 首先创建一个临时投资组合用于测试删除
    temp_portfolio_data = {
        "name": f"Temp Delete Test Portfolio {uuid.uuid4().hex[:8]}",
        "description": "Temporary portfolio for delete test",
        "model_type": "MH",
        "stocks": [
            {"ticker": "AAPL", "weight": 1.0}
        ]
    }

    # 创建临时投资组合
    create_response = api_client["post"]("portfolios", json=temp_portfolio_data)
    assert create_response.status_code == 200, f"Failed to create temp portfolio: {create_response.text}"

    temp_portfolio = create_response.json()["data"]

    try:
        # 运行回测生成 MLflow 数据
        backtest_request = {
            "portfolio_id": temp_portfolio["id"],
            "start_date": "2020-01-01",
            "end_date": "2020-03-31",  # 使用很短的时间范围
            "initial_capital": 100000,
            "benchmark": "SPY",
            "freq": "day",
            "rebalance_freq": "monthly"
        }

        backtest_response = api_client["post"]("portfolio-backtest", json=backtest_request)
        assert backtest_response.status_code == 200, f"Failed to run temp backtest: {backtest_response.text}"

        backtest_data = backtest_response.json()
        experiment_id = backtest_data["experiment_id"]
        run_id = backtest_data["run_id"]

        # 验证运行存在（通过获取详情）
        detail_response = api_client["get"]("mlflows/run-detail", params={
            "run_id": run_id,
            "experiment_id": experiment_id
        })
        assert detail_response.status_code == 200, "Run should exist before deletion"

        # 删除运行
        delete_response = api_client["delete"](f"mlflows/runs/{experiment_id}/{run_id}")
        assert delete_response.status_code == 200, f"Failed to delete MLflow run: {delete_response.text}"

        # 验证删除成功（尝试再次获取详情应该失败或返回空）
        detail_response_after = api_client["get"]("mlflows/run-detail", params={
            "run_id": run_id,
            "experiment_id": experiment_id
        })
        # 删除后应该返回404或空结果
        assert detail_response_after.status_code in [200, 404], f"Unexpected status after deletion: {detail_response_after.status_code}"

    finally:
        # 清理：删除临时投资组合
        try:
            api_client["delete"](f"portfolios/{temp_portfolio['id']}")
        except Exception as e:
            print(f"Warning: Failed to cleanup temp portfolio: {e}")


# ============================================================================
# 综合测试
# ============================================================================

def test_mlflow_workflow_integration(api_client: Dict[str, Any], mlflow_run_data: Dict[str, Any]):
    """
    测试 MLflow API 的综合工作流

    验证各个端点之间的协同工作，并检查回测文件的完整性
    """
    experiment_id = mlflow_run_data["experiment_id"]
    run_id = mlflow_run_data["run_id"]

    # 1. 获取历史记录，确认运行存在
    history_response = api_client["get"]("mlflows/history")
    assert history_response.status_code == 200

    # 2. 获取运行详情
    detail_response = api_client["get"]("mlflows/run-detail", params={
        "run_id": run_id,
        "experiment_id": experiment_id
    })
    assert detail_response.status_code == 200

    # 3. 检查回测文件是否存在
    # 这确保了 MLflow 数据与实际的回测文件保持一致
    check_strategy_backtest_files(experiment_id, run_id)

    # 4. 更新运行名称
    new_name = f"Integration Test Run {uuid.uuid4().hex[:8]}"
    update_response = api_client["post"]("mlflows/update-run-name", json={
        "experiment_id": experiment_id,
        "run_id": run_id,
        "new_name": new_name
    })
    assert update_response.status_code == 200

    # 5. 再次获取历史记录，验证名称更新
    history_response_after = api_client["get"]("mlflows/history")
    assert history_response_after.status_code == 200

    # 6. 获取元数据
    metadata_response = api_client["get"]("mlflows/metadata")
    assert metadata_response.status_code == 200

    print("MLflow workflow integration test completed successfully")


def test_mlflow_history_pagination_and_filtering(api_client: Dict[str, Any]):
    """
    测试 MLflow 历史记录的分页和过滤功能

    验证各种查询参数的组合使用
    注意：依赖于 test_00_mlflow_data_warmup 预热测试提供数据
    """
    # 测试不同的 limit 值
    for limit in [1, 5, 10]:
        response = api_client["get"]("mlflows/history", params={"limit": limit})
        assert response.status_code == 200, f"Failed with limit={limit}"

        data = response.json()
        assert isinstance(data, dict), f"Expected dict, got {type(data)}"

        if "runs" in data:
            runs = data["runs"]
            assert isinstance(runs, list), f"Expected list of runs, got {type(runs)}"
            assert len(runs) <= limit, f"Expected at most {limit} runs, got {len(runs)}"

    # 测试日期范围过滤（可能不被支持）
    date_filters = [
        {"start_date": "2020-01-01"},
        {"end_date": "2023-12-31"},
        {"start_date": "2020-01-01", "end_date": "2023-12-31"}
    ]

    for date_filter in date_filters:
        response = api_client["get"]("mlflows/history", params=date_filter)
        # 日期过滤可能不被支持，允许返回500错误
        assert response.status_code in [200, 500], f"Unexpected status code with date filter {date_filter}: {response.status_code}"

    # 测试组合过滤（仅使用支持的参数）
    combined_params = {
        "limit": 10
    }
    response = api_client["get"]("mlflows/history", params=combined_params)
    assert response.status_code == 200, f"Failed with combined params: {combined_params}"
