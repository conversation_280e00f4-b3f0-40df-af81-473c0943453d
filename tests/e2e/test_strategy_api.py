#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
策略 API 端点的 E2E 测试

测试 /gbs-api/v1/strategies 相关的所有端点：
- GET /gbs-api/v1/strategies - 获取策略列表
- GET /gbs-api/v1/strategies/{strategy_id} - 获取策略详情
- POST /gbs-api/v1/strategies/{strategy_id} - 运行策略
"""

import yaml
import json
import pytest
import random
import requests
from datetime import datetime, timedelta
from typing import Dict, Any
import time
from e2e.backtest_file_checker import check_strategy_backtest_files


# ============================================================================
# 策略列表测试 (原 test_strategies_api.py)
# ============================================================================

def test_list_strategies(api_client: Dict[str, Any]):
    """
    测试获取策略列表端点

    验证 /gbs-api/v1/strategies 端点返回正确的策略列表
    """
    # 发送请求
    response = api_client["get"]("strategies")

    # 验证状态码
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # 验证返回数据是一个列表
    data = response.json()
    assert isinstance(data, list), f"Expected list, got {type(data)}"

    # 验证列表不为空
    assert len(data) > 0, "Expected non-empty list of strategies"

    # 验证每个策略都有必要的字段
    for strategy in data:
        assert "strategy_id" in strategy, "Missing 'strategy_id' field"
        assert "strategy_name" in strategy, "Missing 'strategy_name' field"
        assert "description" in strategy, "Missing 'description' field"
        assert "file_type" in strategy, "Missing 'file_type' field"

        # 验证 strategy_id 是字符串
        assert isinstance(strategy["strategy_id"], str), f"Expected 'strategy_id' to be string, got {type(strategy['strategy_id'])}"

        # 验证 file_type 是 'python' 或 'yaml'
        assert strategy["file_type"] in ["python", "yaml"], f"Expected 'file_type' to be 'python' or 'yaml', got {strategy['file_type']}"


def test_python_strategy_exists(api_client: Dict[str, Any]):
    """
    测试 Python 策略存在

    验证 Python_Backtest_Only_Workflow.py 策略存在于列表中
    """
    # 发送请求
    response = api_client["get"]("strategies")

    # 验证状态码
    assert response.status_code == 200

    # 获取策略列表
    strategies = response.json()

    # 查找 Python_Backtest_Only_Workflow.py 策略
    python_strategy = next(
        (s for s in strategies if s["strategy_id"] == "Python_Backtest_Only_Workflow.py"),
        None
    )

    # 验证策略存在
    assert python_strategy is not None, "Python_Backtest_Only_Workflow.py strategy not found"

    # 验证策略字段
    assert python_strategy["strategy_name"] == "Python_Backtest_Only_Workflow", "Incorrect strategy_name"
    assert python_strategy["file_type"] == "python", "Incorrect file_type"


def test_yaml_strategy_exists(api_client: Dict[str, Any]):
    """
    测试 YAML 策略存在

    验证 Backtest_Only_Workflow.yaml 策略存在于列表中
    """
    # 发送请求
    response = api_client["get"]("strategies")

    # 验证状态码
    assert response.status_code == 200

    # 获取策略列表
    strategies = response.json()

    # 查找 Backtest_Only_Workflow.yaml 策略
    yaml_strategy = next(
        (s for s in strategies if s["strategy_id"] == "Backtest_Only_Workflow.yaml"),
        None
    )

    # 验证策略存在
    assert yaml_strategy is not None, "Backtest_Only_Workflow.yaml strategy not found"

    # 验证策略字段
    assert yaml_strategy["strategy_name"] == "Backtest_Only_Workflow", "Incorrect strategy_name"
    assert yaml_strategy["file_type"] == "yaml", "Incorrect file_type"


def test_strategy_pagination(api_client: Dict[str, Any]):
    """
    测试策略列表分页

    验证 limit 和 offset 参数正确工作
    """
    # 获取所有策略
    all_response = api_client["get"]("strategies")
    all_strategies = all_response.json()

    # 如果策略数量少于2，则跳过此测试
    if len(all_strategies) < 2:
        pytest.skip("Not enough strategies to test pagination")

    # 测试 limit 参数
    limit_response = api_client["get"]("strategies", params={"limit": 1})
    limit_strategies = limit_response.json()

    assert len(limit_strategies) == 1, f"Expected 1 strategy, got {len(limit_strategies)}"
    assert limit_strategies[0]["strategy_id"] == all_strategies[0]["strategy_id"], "First strategy doesn't match"

    # 测试 offset 参数
    offset_response = api_client["get"]("strategies", params={"offset": 1})
    offset_strategies = offset_response.json()

    assert len(offset_strategies) > 0, "Expected at least one strategy with offset=1"
    assert offset_strategies[0]["strategy_id"] == all_strategies[1]["strategy_id"], "Second strategy doesn't match"


# ============================================================================
# 策略详情测试 (原 test_strategy_details_api.py)
# ============================================================================

def test_get_strategy_details(api_client: Dict[str, Any]):
    """
    测试获取策略详情端点

    验证 /gbs-api/v1/strategies/{strategy_id} 端点返回正确的策略详情
    """
    # 首先获取策略列表
    list_response = api_client["get"]("strategies")
    assert list_response.status_code == 200

    strategies = list_response.json()
    assert len(strategies) > 0, "No strategies found"

    # 选择第一个策略
    strategy_id = strategies[0]["strategy_id"]

    # 获取策略详情
    detail_response = api_client["get"](f"strategies/{strategy_id}")

    # 验证状态码
    assert detail_response.status_code == 200, f"Expected status code 200, got {detail_response.status_code}"

    # 验证返回数据
    data = detail_response.json()

    # 验证返回的是文件内容
    assert "file_content" in data, "Missing 'file_content' field"
    assert isinstance(data["file_content"], str), f"Expected 'file_content' to be string, got {type(data['file_content'])}"
    assert len(data["file_content"]) > 0, "Empty file content"


def test_get_python_strategy_details(api_client: Dict[str, Any]):
    """
    测试获取 Python 策略详情

    验证 Python_Backtest_Only_Workflow.py 策略详情正确
    """
    strategy_id = "Python_Backtest_Only_Workflow.py"

    # 获取策略详情
    response = api_client["get"](f"strategies/{strategy_id}")

    # 验证状态码
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # 验证返回数据
    data = response.json()

    # 验证文件内容
    assert "file_content" in data, "Missing 'file_content' field"
    assert isinstance(data["file_content"], str), f"Expected 'file_content' to be string, got {type(data['file_content'])}"
    assert len(data["file_content"]) > 0, "Empty file content"

    # 验证文件内容包含 Python 代码特征
    assert "def main(" in data["file_content"], "Python function 'main' not found in file content"
    assert "import" in data["file_content"], "Python import statement not found in file content"


def test_get_yaml_strategy_details(api_client: Dict[str, Any]):
    """
    测试获取 YAML 策略详情

    验证 Backtest_Only_Workflow.yaml 策略详情正确
    """
    strategy_id = "Backtest_Only_Workflow.yaml"

    # 获取策略详情
    response = api_client["get"](f"strategies/{strategy_id}")

    # 验证状态码
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # 验证返回数据
    data = response.json()

    # 验证文件内容
    assert "file_content" in data, "Missing 'file_content' field"
    assert isinstance(data["file_content"], str), f"Expected 'file_content' to be string, got {type(data['file_content'])}"
    assert len(data["file_content"]) > 0, "Empty file content"

    # 验证文件内容包含 YAML 代码特征
    assert "gbs_init:" in data["file_content"], "YAML 'gbs_init' section not found in file content"


def test_nonexistent_strategy(api_client: Dict[str, Any]):
    """
    测试获取不存在的策略

    验证请求不存在的策略时返回 404 错误
    """
    strategy_id = "nonexistent_strategy.yaml"

    # 获取策略详情
    response = api_client["get"](f"strategies/{strategy_id}")

    # 验证状态码
    assert response.status_code == 404, f"Expected status code 404, got {response.status_code}"


# ============================================================================
# 运行策略测试 (原 test_run_strategy_api.py)
# ============================================================================

def modify_yaml_config(yaml_content: str) -> Dict[str, Any]:
    """
    修改 YAML 配置文件内容

    根据 runStrategy.ts 中的 field_map 修改关键参数
    """
    # 解析 YAML 内容
    config = yaml.safe_load(yaml_content)

    # 获取原始值
    original_start_time = config.get('bt_analysis_config', {}).get('backtest', {}).get('start_time', '2019-07-01')
    original_end_time = config.get('bt_analysis_config', {}).get('backtest', {}).get('end_time', '2020-12-31')
    original_capital = config.get('bt_analysis_config', {}).get('backtest', {}).get('initial_capital', 1000000)
    original_freq = config.get('bt_analysis_config', {}).get('backtest', {}).get('data_loader', {}).get('kwargs', {}).get('freq', 'day')

    # 转换日期字符串为日期对象
    start_date = datetime.strptime(original_start_time, '%Y-%m-%d')
    end_date = datetime.strptime(original_end_time, '%Y-%m-%d')

    # 随机调整日期（加减2天以内）
    start_date_adjusted = start_date + timedelta(days=random.randint(-2, 2))
    end_date_adjusted = end_date + timedelta(days=random.randint(-2, 2))

    # 确保开始日期早于结束日期
    if start_date_adjusted >= end_date_adjusted:
        start_date_adjusted = end_date_adjusted - timedelta(days=1)

    # 随机调整资金（加减10%以内）
    capital_adjusted = int(original_capital * (1 + random.uniform(-0.1, 0.1)))

    # 创建修改后的参数
    modified_params = {
        'start_date': start_date_adjusted.strftime('%Y-%m-%d'),
        'end_date': end_date_adjusted.strftime('%Y-%m-%d'),
        'initial_capital': capital_adjusted,
        'freq': original_freq  # 保持频率不变
    }

    return modified_params


# 注意：此测试非常耗时且可能导致服务器问题
# 如果只想运行其他测试，可以使用：python -m pytest e2e/test_strategy_api.py -k "not test_run_qlib_style_workflow"
def test_run_qlib_style_workflow(api_client: Dict[str, Any]):
    """
    测试运行 Qlib_Style_Workflow.yaml 策略

    验证 POST /gbs-api/v1/strategies/Qlib_Style_Workflow.yaml 端点功能

    注意：此测试非常耗时（约2分钟）且可能导致服务器问题
    如果只想运行其他测试，可以使用：
    python -m pytest e2e/test_strategy_api.py -k "not test_run_qlib_style_workflow"
    """
    strategy_id = "Qlib_Style_Workflow.yaml"

    # 获取策略文件内容
    response = api_client["get"](f"strategies/{strategy_id}")
    assert response.status_code == 200, f"Failed to get strategy content: {response.status_code}"

    yaml_content = response.json().get("file_content", "")
    assert yaml_content, "Empty YAML content"

    # 修改 YAML 配置
    modified_params = modify_yaml_config(yaml_content)

    print(f"Modified parameters: {json.dumps(modified_params, indent=2)}")

    # 构建请求负载
    payload = {
        "workflow_content": yaml_content,  # 使用原始内容，让服务器端处理修改
        **modified_params  # 添加修改后的参数
    }

    # 发送 POST 请求，带重试机制
    print(f"Sending POST request to /gbs-api/v1/strategies/{strategy_id}")

    max_retries = 3
    retry_delay = 10  # 重试间隔（秒）

    for attempt in range(1, max_retries + 1):
        try:
            start_time = time.time()
            response = api_client["post"](f"strategies/{strategy_id}", json=payload)
            end_time = time.time()

            # 打印响应时间
            print(f"Request completed in {end_time - start_time:.2f} seconds")

            if response.status_code == 200:
                print(f"Request successful on attempt {attempt}")
                break
            elif (response.status_code == 503 or response.status_code == 500) and attempt < max_retries:
                print(f"Received {response.status_code} error, retrying in {retry_delay} seconds (attempt {attempt}/{max_retries})")
                time.sleep(retry_delay)
            else:
                # 最后一次尝试或其他错误
                error_message = f"Expected status code 200, got {response.status_code}"
                if hasattr(response, 'text'):
                    error_message += f": {response.text}"
                print(f"Error details: {error_message}")
                print(f"Request payload: {json.dumps(payload, indent=2)}")
                assert response.status_code == 200, error_message
        except requests.exceptions.RequestException as e:
            if attempt < max_retries:
                print(f"Request failed with error: {str(e)}, retrying in {retry_delay} seconds (attempt {attempt}/{max_retries})")
                time.sleep(retry_delay)
            else:
                raise

    # 验证响应内容
    data = response.json()

    # 验证关键字段
    assert "experiment_id" in data, "Missing 'experiment_id' in response"
    assert "recorder_id" in data, "Missing 'recorder_id' in response"

    experiment_id = data["experiment_id"]
    run_id = data["recorder_id"]  # API 返回 recorder_id 而不是 run_id

    # 使用公共的文件检查函数
    check_strategy_backtest_files(experiment_id, run_id)

    # 清理生成的 MLflow 运行
    try:
        delete_response = api_client["delete"](f"mlflows/runs/{experiment_id}/{run_id}")
        if delete_response.status_code == 200:
            print(f"Strategy backtest MLflow run {run_id} cleaned up successfully")
    except Exception as e:
        print(f"Warning: Failed to clean up MLflow run {run_id}: {str(e)}")
