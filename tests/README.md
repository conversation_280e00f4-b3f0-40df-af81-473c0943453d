# Gold Beast System 测试

本目录包含 Gold Beast System 的测试代码。

## E2E 测试

E2E（端到端）测试用于验证系统的各个组件在集成环境中是否正常工作。

### 安装依赖

```bash
pip install -r requirements-test.txt
```

### 运行测试

确保 API 服务器正在运行：

```bash
cd api && python main.py
```

然后在另一个终端中运行测试：

```bash
cd tests && pytest
```

如果只想运行特定的测试文件（例如，跳过耗时的 `test_run_strategy_api.py`）：

```bash
cd tests && pytest e2e/test_strategies_api.py e2e/test_strategy_details_api.py
```

### 环境变量

可以通过环境变量自定义测试配置：

- `API_BASE_URL`: API 服务器的基础 URL，默认为 `http://localhost:8000/gbs-api/v1`

例如：

```bash
API_BASE_URL=http://localhost:8000/gbs-api/v1 pytest
```

### 测试覆盖率

要生成测试覆盖率报告，请运行：

```bash
cd tests && pytest --cov=../api
```

注意：E2E 测试通常不会提供细粒度的代码覆盖率，因为它们主要关注系统的整体功能而非具体实现细节。
