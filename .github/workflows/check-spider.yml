name: Check <PERSON>

on:
    schedule:
        -   cron: '45 */1 * * *'  # every 1 hour
    workflow_dispatch:
        inputs:
            send_to_slack:
                description: 'Send report to slack'
                default: True
                required: false
                type: boolean

permissions:
    contents: read

env:
    ENV: check
    ALERT_ENABLED_ENV: check
    OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
    FMP_API_KEY: ${{ secrets.FMP_API_KEY }}
    QUARTR_API_KEY: ${{ secrets.QUARTR_API_KEY }}
    GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
    GOOGLE_CSE_ID: ${{ secrets.GOOGLE_CSE_ID }}
    PINECONE_API_KEY: ${{ secrets.PINECONE_API_KEY }}
    PINECONE_NAMESPACE: prod-20240618
    PINECONE_INDEX_NAME.A: cocktail-prod-a-financial-serverless
    PINECONE_INDEX_NAME.B: cocktail-prod-b-company-serverless
    PINECONE_INDEX_NAME.C: cocktail-prod-c-media-serverless
    PINECONE_INDEX_NAME.D: cocktail-prod-d-social-serverless
    PINECONE_INDEX_NAME.E: cocktail-prod-e-customer-serverless
    AWS_DEFAULT_REGION: us-east-1
    S3_DOC_BUCKET: onwish-docs-bucket
    S3_CUSTOMER_DATA_BUCKET: onwish-customer-data
    SEC_API: ${{ secrets.SEC_API }}
    GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID }}
    GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET }}
    SERPAPI_API_KEY: b5b9fed370cf56bda882d2bfb28120d9584269f3484bcccec7b672e473428fd7
    SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
    EMAIL_BACKEND: ${{ secrets.EMAIL_BACKEND }}
    EMAIL_HOST: ${{ secrets.EMAIL_HOST }}
    EMAIL_PORT: ${{ secrets.EMAIL_PORT }}
    EMAIL_USE_SSL: ${{ secrets.EMAIL_USE_SSL }}
    EMAIL_HOST_USER: ${{ secrets.EMAIL_HOST_USER }}
    EMAIL_HOST_PASSWORD: ${{ secrets.EMAIL_HOST_PASSWORD }}
    TIKHUB_API_KEY: ${{ secrets.TIKHUB_API_KEY }}
    SPIDER_INSTAGRAM_PROFILE_BY:

jobs:
    test:
        runs-on: ubuntu-latest

        steps:
            -   uses: actions/checkout@v4
            -   name: Set up Python 3.11
                uses: actions/setup-python@v5
                with:
                    python-version: "3.11"
            -   uses: actions/cache@v4
                id: pip_cache_v9
                with:
                    path: venv
                    key: ${{ runner.os }}-pip-v9-${{ hashFiles('**/requirements.txt') }}
                    restore-keys: |
                        ${{ runner.os }}-pip-v9-
            -   name: Install dependencies
                if: steps.pip_cache_v9.outputs.cache-hit != 'true'
                run: |
                    python -m venv venv
                    source venv/bin/activate
                    python -m pip install --upgrade pip
                    pip install -r requirements.txt
            -   name: Install Playwright browsers
                run: |
                    source venv/bin/activate
                    playwright install chromium
            -   name: Run check index
                run: |
                    CMD="source venv/bin/activate; python app/manage.py check_atg_spider"
                    if [[ "${{ github.event.inputs.send_to_slack || 'true' }}" == "true" ]]; then
                        CMD="$CMD --send-to-slack"
                    fi
                    eval $CMD
