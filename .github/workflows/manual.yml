name: "[Manual] Create and publish a Docker image"

on:
  workflow_dispatch:
    inputs:
      registry:
        required: true
        default: "docker.io"
        type: choice
        options: ["docker.io", "ghcr.io"]
      image:
        required: true
        default: 'samuelchen/pycommon'
      tag:
        required: true
        default: 'latest'
      docker_hub_password:
        description: Docker Hub password.(Available only registry is "docker.io")
        required: false
        default: ''

env:
  REGISTRY: ${{ github.event.inputs.registry }}
  IMAGE_NAME: ${{ github.event.inputs.image }}
  TAG: ${{ github.event.inputs.tag }}
  DOCKER_HUB_PASSWORD: ${{ github.event.inputs.docker_hub_password }}


jobs:
  build-and-push-image:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Log in to ghcr.io Container registry
        if: ${{ env.REGISTRY == 'ghcr.io' }}
        uses: docker/login-action@f054a8b539a109f9f41c372932f1ae047eff08c9
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Log in to ghcr.io Container registry
        if: ${{ env.REGISTRY == 'docker.io' }}
        uses: docker/login-action@f054a8b539a109f9f41c372932f1ae047eff08c9
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ env.DOCKER_HUB_PASSWORD }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@98669ae865ea3cffbcbaa878cf57c20bbf1c6c38
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

      - name: Build and push Docker image
        uses: docker/build-push-action@ad44023a93711e3deb337508980b4b5e9bcdc5dc
        with:
          context: .
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ env.TAG }}
          labels: ${{ steps.meta.outputs.labels }}

      - name: Print image name & tags to STDOUT
        run: echo   Image tag is "${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ env.TAG }}"

