name: Trigger E2E testing for FE
on:
  push:
    branches:
      - main
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger a repository_dispatch event
        run: |
          curl -v -XPOST -u "USERNAME:${{secrets.FE_E2E_TESTING_TOKEN}}" \
          -H "Accept: application/vnd.github.everest-preview+json" \
          "https://api.github.com/repos/onwish/cocktail-fe/dispatches" \
          -d '{
                "event_type": "cocktail_push_event",
                "client_payload": {
                  "actor": "${{ github.actor }}",
                  "ref": "${{ github.event.ref }}",
                  "sha": "${{ github.sha }}"
                }
              }'
