name: Lint and Test

on:
    push:
        branches:
            - main
    pull_request:

permissions:
    contents: read

env:
    ENV: test
    OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
    FMP_API_KEY: ${{ secrets.FMP_API_KEY }}
    GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
    GOOGLE_CSE_ID: ${{ secrets.GOOGLE_CSE_ID }}
    PINECONE_API_KEY: ${{ secrets.PINECONE_API_KEY }}
    AWS_DEFAULT_REGION: us-east-1
    PINECONE_NAMESPACE: prod-20240618
    PINECONE_INDEX_NAME.A: cocktail-prod-a-financial-serverless
    PINECONE_INDEX_NAME.B: cocktail-prod-b-company-serverless
    PINECONE_INDEX_NAME.C: cocktail-prod-c-media-serverless
    PINECONE_INDEX_NAME.D: cocktail-prod-d-social-serverless
    PINECONE_INDEX_NAME.E: cocktail-prod-e-customer-serverless
    S3_DOC_BUCKET: onwish-docs-bucket
    S3_CUSTOMER_DATA_BUCKET: onwish-customer-data
    SEC_API: ${{ secrets.SEC_API }}
    GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID }}
    GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET }}
    EMAIL_BACKEND: ${{ secrets.EMAIL_BACKEND }}
    EMAIL_HOST: ${{ secrets.EMAIL_HOST }}
    EMAIL_PORT: ${{ secrets.EMAIL_PORT }}
    EMAIL_USE_SSL: ${{ secrets.EMAIL_USE_SSL }}
    EMAIL_HOST_USER: ${{ secrets.EMAIL_HOST_USER }}
    EMAIL_HOST_PASSWORD: ${{ secrets.EMAIL_HOST_PASSWORD }}
    TIKHUB_API_KEY: ${{ secrets.TIKHUB_API_KEY }}


jobs:
    test:
        runs-on: ubuntu-24.04

        steps:
            -   uses: actions/checkout@v4
            -   name: Set up Python 3.11
                uses: actions/setup-python@v5
                with:
                    python-version: "3.11"
            -   uses: actions/cache@v4
                id: pip_cache_v9
                with:
                    path: venv
                    key: ${{ runner.os }}-pip-v9-${{ hashFiles('**/requirements.txt') }}
                    restore-keys: |
                        ${{ runner.os }}-pip-v9-
            -   name: Install dependencies
                if: steps.pip_cache_v9.outputs.cache-hit != 'true'
                run: |
                    python -m venv venv
                    source venv/bin/activate
                    python -m pip install --upgrade pip
                    pip install -r requirements.txt
            -   name: Lint with flake8, black and mypy
                run: |
                    source venv/bin/activate
                    flake8 app --count --max-complexity=10 --statistics
                    black --check ./app
                    mypy . --install-types --non-interactive
            -   name: Install Playwright browsers
                run: |
                    source venv/bin/activate
                    playwright install chromium
            -   name: Run Unit Tests
                run: |
                    source venv/bin/activate
                    pytest -p no:warnings --cov=datasvc ./app/datasvc/tests
                    pytest -p no:warnings --cov=agent ./app/agent/tests/unit_tests
                    pytest -p no:warnings --cov=common.scraper ./app/common/scraper/tests
            # -   name: Run Integraion Tests
            #     run: |
            #         source venv/bin/activate
            #         pytest -p no:warnings ./app/agent/tests/integration_tests
