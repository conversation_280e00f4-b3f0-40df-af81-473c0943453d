name: Evaluation

on:
    # Temporarily disabled. Will enable once we are evaluating MVP 3.0 search
    # schedule:
    #     -   cron: '30 1 * * *'
    workflow_dispatch:
        inputs:
            eval_type:
                description: 'agent/retrieval/plan_retrieval'
                default: 'retrieval'
                required: false
                type: string
            dataset:
                description: 'Dataset to evaluate on'
                default: 'simple'
                required: false
                type: string
            pinecone_index_name:
                description: 'The index name to use'
                default: 'onwish-dev'
                required: false
                type: string
            pinecone_namespace:
                description: 'The index namespace to use'
                default: 'pre'
                required: false
                type: string
            verbose:
                description: 'Verbose output, contains more info'
                default: True
                required: false
                type: boolean
            send_to_slack:
                description: 'Send report to slack'
                default: True
                required: false
                type: boolean
jobs:
    eval:
        runs-on: ubuntu-latest
        env:
            ENV: test
            OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
            FMP_API_KEY: ${{ secrets.FMP_API_KEY }}
            GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
            GOOGLE_CSE_ID: ${{ secrets.GOOGLE_CSE_ID }}
            GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID }}
            GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET }}
            EMAIL_BACKEND: ${{ secrets.EMAIL_BACKEND }}
            EMAIL_HOST: ${{ secrets.EMAIL_HOST }}
            EMAIL_PORT: ${{ secrets.EMAIL_PORT }}
            EMAIL_USE_SSL: ${{ secrets.EMAIL_USE_SSL }}
            EMAIL_HOST_USER: ${{ secrets.EMAIL_HOST_USER }}
            EMAIL_HOST_PASSWORD: ${{ secrets.EMAIL_HOST_PASSWORD }}
            PINECONE_API_KEY: ${{ secrets.PINECONE_API_KEY }}
            PINECONE_ENV: ${{ secrets.PINECONE_ENV }}
            PINECONE_INDEX_NAME: ${{ github.event.inputs.pinecone_index_name || 'onwish-dev'  }}
            # configurable via workflow input params
            PINECONE_NAMESPACE: ${{ github.event.inputs.pinecone_namespace || 'pre' }}
            PINECONE_HYBRID_INDEX_NAME: ${{ github.event.inputs.pinecone_index_name || 'onwish-dev'  }}
            SEC_API: ${{ secrets.SEC_API }}
            SLACK_BOT_TOKEN: *********************************************************
        steps:
            -   uses: actions/checkout@v4
            -   name: Set up Python 3.11
                uses: actions/setup-python@v5
                with:
                    python-version: "3.11"
            - uses: actions/cache@v4
              id: pip_cache_v9
              with:
                  path: venv
                  key: ${{ runner.os }}-pip-v9-${{ hashFiles('**/requirements.txt') }}
                  restore-keys: |
                    ${{ runner.os }}-pip-v9-
            -   name: Install dependencies
                if: steps.pip_cache_v9.outputs.cache-hit != 'true'
                run: |
                   python -m venv venv
                   source venv/bin/activate
                   python -m pip install --upgrade pip
                   pip install -r requirements.txt
            -   name: migrate_createcachetable
                run: |
                    source venv/bin/activate
                    python app/manage.py migrate
                    python app/manage.py createcachetable
            -   name: Run Eval

                run: |
                    CMD="source venv/bin/activate; python app/manage.py eval ${{ github.event.inputs.eval_type || 'retrieval' }} ${{ github.event.inputs.dataset || 'simple' }} --num-tasks=4"
                    if [[ "${{ github.event.inputs.verbose || 'true' }}" == "true" ]]; then
                      CMD="$CMD --verbose"
                    fi
                    if [[ "${{ github.event.inputs.send_to_slack || 'true' }}" == "true" ]]; then
                      CMD="$CMD --send-to-slack"
                    fi
                    eval $CMD
