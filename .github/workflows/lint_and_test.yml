name: Lint and Test

on:
    push:
        branches:
            - main
    pull_request:

permissions:
    contents: read

env:
    ENV: test

jobs:
    test:
        runs-on: ubuntu-latest

        steps:
            -   uses: actions/checkout@v4
            -   name: Set up Python 3.11
                uses: actions/setup-python@v5
                with:
                    python-version: "3.11"
            -   uses: actions/cache@v4
                id: pip_cache_v9
                with:
                    path: venv
                    key: ${{ runner.os }}-pip-v9-${{ hashFiles('**/requirements.txt') }}
                    restore-keys: |
                        ${{ runner.os }}-pip-v9-
            -   name: Install dependencies
                if: steps.pip_cache_v9.outputs.cache-hit != 'true'
                run: |
                    python -m venv venv
                    source venv/bin/activate
                    python -m pip install --upgrade pip
                    pip install -r requirements.txt
            -   name: Lint with flake8, black and mypy
                run: |
                    source venv/bin/activate
                    flake8 app --count --max-complexity=10 --statistics
                    black --check ./app
                    mypy . --install-types --non-interactive
#            -   name: Run Unit Tests
#                run: |
#                    source venv/bin/activate
#                    pytest -p no:warnings --cov=djcommon ./djcommon/tests
#                    pytest -p no:warnings --cov=pycommon ./pycommon/tests
#            -   name: Run Integraion Tests
#                run: |
#                    source venv/bin/activate
#                    pytest -p no:warnings ./app/agent/tests/integration_tests
