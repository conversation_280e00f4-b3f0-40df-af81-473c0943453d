aiohttp==3.8.4
aiosignal==1.3.1
alembic==1.12.0
annotated-types==0.5.0
anyio==3.7.1
apify-client==1.4.1
apify-shared==1.0.2
apispec==6.3.0
appnope==0.1.3
argcomplete==3.1.2
asgiref==3.7.2
asttokens==2.2.1
async-timeout==4.0.2
attrs==23.1.0
Babel==2.13.0
backcall==0.2.0
backoff==2.2.1
beautifulsoup4==4.12.2
black==23.9.1
blinker==1.6.3
boto3==1.34.29
cachelib==0.9.0
cachetools==5.3.1
cattrs==23.1.2
certifi==2023.5.7
cffi==1.16.0
charset-normalizer==3.2.0
click==8.1.7
clickclick==20.10.2
colorama==0.4.6
colorlog==4.8.0
ConfigUpdater==3.1.1
connexion==2.14.2
coverage==7.3.2
cron-descriptor==1.4.0
croniter==2.0.1
cryptography==41.0.4
cssselect==1.2.0
dataclasses-json==0.5.14
decorator==5.1.1
Deprecated==1.2.14
dill==0.3.7
Django==4.2.2
djangorestframework==3.14.0
django-ninja==1.0.1
dnspython==2.3.0
docutils==0.20.1
email-validator==1.3.1
executing==1.2.0
fake-useragent==1.2.1
feedfinder2==0.0.4
feedparser==6.0.10
filelock==3.12.3
flake8==6.1.0
Flask==2.2.5
Flask-AppBuilder==4.3.6
Flask-Babel==2.0.0
Flask-Caching==2.1.0
Flask-Mail==0.9.1
Flask-JWT-Extended==4.5.3
Flask-Limiter==3.5.0
Flask-Login==0.6.2
Flask-Session==0.5.0
Flask-SQLAlchemy==2.5.1
Flask-WTF==1.2.1
frozenlist==1.3.3
fsspec==2023.9.1
google-api-core==2.11.1
google-api-python-client==2.99.0
google-auth==2.17.3
google-auth-httplib2==0.1.1
google-re2==1.1
googleapis-common-protos==1.60.0
graphviz==0.20.1
grpcio==1.59.0
gunicorn==21.2.0
h11==0.14.0
html2text==2020.1.16
httpcore==0.17.3
httplib2==0.22.0
httpx==0.24.1
huggingface-hub==0.17.2
idna==3.4
importlib-metadata==6.8.0
importlib-resources==6.1.0
inflection==0.5.1
iniconfig==2.0.0
ipdb==0.13.13
ipython==8.14.0
itsdangerous==2.1.2
jedi==0.18.2
jieba3k==0.35.1
Jinja2==3.1.2
joblib==1.3.2
jsonschema==4.19.1
jsonschema-specifications==2023.7.1
lazy-object-proxy==1.9.0
Levenshtein==0.25.1
limits==3.6.0
linkify-it-py==2.0.2
lockfile==0.12.2
loguru==0.7.0
lxml==4.9.3
Mako==1.2.4
Markdown==3.5
markdown-it-py==3.0.0
MarkupSafe==2.1.3
marshmallow==3.20.1
marshmallow-oneofschema==3.0.1
marshmallow-sqlalchemy==0.26.1
matplotlib-inline==0.1.6
mccabe==0.7.0
mdit-py-plugins==0.4.0
mdurl==0.1.2
mpmath==1.3.0
multidict==6.0.4
mypy==1.6.1
mypy-extensions==1.0.0
networkx==3.1
newspaper3k==0.2.8
nltk==3.8.1
numexpr==2.8.6
numpy==1.25.1
openai==0.27.8
opentelemetry-api==1.20.0
opentelemetry-exporter-otlp==1.20.0
opentelemetry-exporter-otlp-proto-common==1.20.0
opentelemetry-exporter-otlp-proto-grpc==1.20.0
opentelemetry-exporter-otlp-proto-http==1.20.0
opentelemetry-proto==1.20.0
opentelemetry-sdk==1.20.0
opentelemetry-semantic-conventions==0.41b0
ordered-set==4.1.0
parso==0.8.3
pathspec==0.11.2
pendulum==2.1.2
pexpect==4.8.0
pickleshare==0.7.5
Pillow==10.0.1
platformdirs==3.11.0
playwright==1.47.0
pluggy==1.3.0
prison==0.2.1
prompt-toolkit==3.0.39
protobuf==4.24.3
psutil==5.9.6
ptyprocess==0.7.0
pure-eval==0.2.2
pyasn1==0.5.0
pyasn1-modules==0.3.0
pycodestyle==2.11.0
pycparser==2.21
pydantic==2.1.1
pydantic_core==2.4.0
pyflakes==3.1.0
Pygments==2.15.1
PyJWT==2.8.0
pyparsing==3.1.1
pytest==7.4.2
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-django==4.5.2
python-daemon==3.0.1
python-dateutil==2.8.2
python-dotenv<=2.0.0
python-nvd3==0.15.0
python-slugify==8.0.1
python-telegram-bot==20.3
python-Levenshtein==0.25.1
pytz==2023.3.post1
pytzdata==2020.1
PyYAML==6.0
rapidfuzz==3.8.1
referencing==0.30.2
regex==2023.8.8
requests==2.31.0
requests-file==1.5.1
requests-toolbelt==1.0.0
rfc3339-validator==0.1.4
rich==13.6.0
rich-argparse==1.4.0
rpds-py==0.10.6
rsa==4.9
safetensors==0.3.3
scikit-learn==1.3.1
scipy==1.11.3
sec-api==1.0.17
sentence-transformers==2.2.2
sentencepiece==0.1.99
setproctitle==1.3.3
sgmllib3k==1.0.0
six==1.16.0
sniffio==1.3.0
soupsieve==2.5
SQLAlchemy==1.4.49
SQLAlchemy-JSONField==1.0.1.post0
SQLAlchemy-Utils==0.41.1
sqlparse==0.4.4
stack-data==0.6.2
supervisor==4.2.5
sympy==1.12
tabulate==0.9.0
tenacity==8.2.3
termcolor==2.3.0
text-unidecode==1.3
threadpoolctl==3.2.0
tiktoken==0.5.1
tinysegmenter==0.3
tldextract==3.4.4
tokenizers==0.13.3
tqdm==4.65.0
traitlets==5.9.0
transformers==4.33.2
typing-inspect==0.9.0
#typing_extensions==4.7.1
uc-micro-py==1.0.2
unicodecsv==0.14.1
uritemplate==4.1.1
#urllib3==2.0.3
wcwidth==0.2.6
Werkzeug==2.2.3
wrapt==1.15.0
WTForms==3.1.0
yarl==1.9.2
zipp==3.17.0
channels==4.1.0
daphne==4.0.0
PyJWT==2.8.0
psycopg2-binary==2.9.9
redis==5.0.1

django_extensions==3.2.3
jupyter==1.0.0
nb-clean==3.2.0

BeautifulSoup4==4.12.2
scikit-learn==1.3.1
nltk==3.8.1

django-json-widget==1.1.1
types-requests
slack-sdk==3.26.1
dateparser==1.2.0
types-dateparser
django-allauth==0.63.2

anthropic_bedrock==0.8.0
anthropic==0.18.1
xxhash==3.4.1
pandas==2.1.4

feedparser==6.0.10
markdown-it-py==3.0.0
aioredis==1.3.1

#pinecone-client==2.2.2
#pinecone-text==0.7.1
#pinecone-client==4.1.1
pinecone-client==3.2.2
pinecone-text==0.9.0

#langchain==0.2.1
#langchain-community==0.2.1
langchain==0.2.3
langchain-community==0.2.4
langchain-pinecone==0.1.1


#pinecone-client==2.2.2
#pinecone-client=4.1.1
pinecone-client==3.2.2

#pinecone-text==0.7.1
pinecone-text==0.9.0

selenium==4.23.0
pyrate-limiter==3.7.0

stripe==10.10.0

#  brew install pandoc (https://pandoc.org/installing.html)
pandoc==2.4
pymupdf4llm==0.0.17

celery==5.4
flower==2.0
django-celery-results==2.5


#scraper & client
fastapi==0.115.6
uvicorn==0.34.0
aiohttp-sse-client==0.2.1

bitarray==3.0
torch==2.4.1

more-itertools>=10.7.0
