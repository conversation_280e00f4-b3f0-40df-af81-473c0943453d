from logging import get<PERSON>ogger
from typing import Optional

import jwt
from django.conf import settings
from django.http import HttpRequest
from ninja.errors import AuthenticationError
from ninja.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIKeyHeader, Http<PERSON>earer
from pycommon.django_ext.model import User

from .. import conf
from ..authentication import JwtSecret, check_subscription

logger = getLogger(__name__)


class _Auth:
    param_name: str = "X-TOKEN"
    _member_required: bool = False

    async def authenticate(self, request, token):  # type: ignore  # noqa: C901
        if not token:
            raise AuthenticationError()

        user = None
        payload = {}
        try:
            payload = JwtSecret.verify_token(token)
        except jwt.exceptions.ExpiredSignatureError:
            raise AuthenticationError("token expired")
        except jwt.exceptions.DecodeError:
            user = await self._debug_auth(request, token)
        except Exception as e:
            logger.exception(e)
            user = await self._debug_auth(request, token)

        if payload:
            uid = payload.get("sub")
            name = payload.get("name")
            try:
                user = await User.objects.aget(id=uid, username=name, is_active=True)
            except User.DoesNotExist:
                raise AuthenticationError("invalid user")
            request.user = user
        elif user is not None:
            request.user = user
        else:
            raise AuthenticationError("invalid token")

        if self._member_required:
            if not request.path.startswith("/p/"):
                await check_subscription(request.user)
        return user

    async def _debug_auth(self, request: HttpRequest, token: str) -> Optional[User]:
        if settings.DEBUG and conf.RESTAPI_DEBUG_TOKEN and token == conf.RESTAPI_DEBUG_TOKEN:
            logger.warning("try authorizing with debug token")
            uid = int(request.headers.get("X-DEBUG-USER", -1))
            try:
                user = await User.objects.aget(id=uid, is_staff=False, is_superuser=False, is_active=True)
            except User.DoesNotExist:
                raise AuthenticationError("invalid token")
            request.user = user
            return user
        return None


class CookieAuth(_Auth, APIKeyCookie):
    pass


class HeaderAuth(_Auth, APIKeyHeader):
    pass


class BearAuth(_Auth, HttpBearer):
    pass


class MemberBearAuth(_Auth, HttpBearer):
    _member_required = True


class MemberHeaderAuth(_Auth, APIKeyHeader):
    _member_required = True
