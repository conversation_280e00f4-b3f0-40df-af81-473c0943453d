from logging import getLogger

from django.http import HttpRequest, HttpResponse
from ninja import NinjaAPI
from ninja.errors import ValidationError, AuthenticationError, Http404, HttpError

from pycommon.utils.gitutil import git_commit_hash, git_log
from .account import account_router
from .chat import chat_router, msg_router
from .cms import cms_router
from .company import company_router
from .company_dashboard import company_dashboard_router
from .contact_us import contact_us_router
from .dashboard import dashboard_router
from .docs import docs_router
from .example_question import example_question_router
from .history import history_router
from .misc import misc_router
from .payment import payment_router
from .search import search_router
from .sharing import sharing_router
from .status import status_router
from .token import auth_router
from ..auth import (
    MemberBearAuth,
    MemberHeaderAuth,
)
from ..response import ApiResults, ApiResponse
from ...authentication import (
    AccessDenyError,
    TrialExpiredError,
    SubscriptionExpiredError,
)
from ...constants import APP_TITLE, APP_DESC

logger = getLogger(__name__)

__version__ = (1, 0, 0)
__git_hash__ = git_commit_hash(short=True)
__git_log__ = git_log(n=1)

restapi = NinjaAPI(
    title=APP_TITLE,
    version=f"{__version__[0]}.{__version__[1]}.{__version__[2]}.{__git_hash__}",
    description=APP_DESC + f"\n\nlast commit\n```\n{__git_log__}\n```",
    csrf=False,
    auth=[MemberBearAuth(), MemberHeaderAuth()],
)

restapi.add_router("/auth", auth_router)
restapi.add_router("/chats", chat_router)
restapi.add_router("/messages", msg_router)
restapi.add_router("/contact-us", contact_us_router)
restapi.add_router("/example-list", example_question_router)
restapi.add_router("/dashboard", dashboard_router)
restapi.add_router("/status", status_router)
restapi.add_router("/", misc_router)
restapi.add_router("/docs", docs_router)
restapi.add_router("/histories", history_router)
restapi.add_router("/account", account_router)
restapi.add_router("/sharing", sharing_router)
restapi.add_router("/company-dashboard", company_dashboard_router)
restapi.add_router("/cms", cms_router)
restapi.add_router("/p", payment_router)
restapi.add_router("/companies", company_router)
restapi.add_router("/search", search_router)


@restapi.exception_handler(AuthenticationError)
def auth_fail(request: HttpRequest, exc: AuthenticationError) -> HttpResponse:
    return restapi.create_response(request, ApiResults.UNAUTHORIZED(detail=str(exc)), status=401)


@restapi.exception_handler(AccessDenyError)
def access_deny(request: HttpRequest, exc: AccessDenyError) -> HttpResponse:
    if isinstance(exc, SubscriptionExpiredError):
        return restapi.create_response(request, ApiResults.SUBSCRIPTION_EXPIRED, status=403)
    elif isinstance(exc, TrialExpiredError):
        return restapi.create_response(request, ApiResults.TRIAL_EXPIRED, status=403)

    return restapi.create_response(request, ApiResults.FORBIDDEN(detail=str(exc)), status=403)


@restapi.exception_handler(ValidationError)
def bad_quest(request: HttpRequest, exc: ValidationError) -> HttpResponse:
    return restapi.create_response(request, ApiResults.BAD_REQUEST(detail=str(exc)), status=400)


@restapi.exception_handler(Http404)
def not_found(request: HttpRequest, exc: Http404) -> HttpResponse:
    return restapi.create_response(request, ApiResults.NOT_FOUND(detail=str(exc)), status=404)


@restapi.exception_handler(HttpError)
def http_error(request: HttpRequest, exc: HttpError) -> HttpResponse:
    logger.exception(exc)
    result: ApiResponse = ApiResponse(code=exc.status_code, message=exc.message, detail=str(exc))
    return restapi.create_response(request, result, status=exc.status_code)


@restapi.exception_handler(Exception)
def unexpected(request: HttpRequest, exc: Exception) -> HttpResponse:
    logger.exception(exc)
    return restapi.create_response(request, ApiResults.INTERNAL_ERROR(detail=str(exc)), status=500)
