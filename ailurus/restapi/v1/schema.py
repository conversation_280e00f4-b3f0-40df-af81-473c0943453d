from datetime import datetime
from typing import Optional, Dict, Any, List

from django.db.models import Q
from ninja import FilterSchema, Schema
from ninja.orm import create_schema
from pycommon.django_ext.model import User
from pydantic import Field
from pydantic import <PERSON><PERSON>

from ...models import Chat, ChatMsg

# ----- schemas -----


UserSchema = create_schema(User, depth=0, exclude=["password"])
ChatSchema = create_schema(Chat, depth=0)
ChatUpdateSchema = create_schema(Chat, depth=0, fields=["title"])
ChatMsgSchema = create_schema(ChatMsg, depth=0)


class LoginSchema(Schema):
    username: str
    password: str


class UpdateUserProfile(Schema):
    avatar: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    profession: Optional[str] = None
    trial_start_date: Optional[datetime] = None


# ----- filter schemas -----


class BaseFilterSchema(FilterSchema):
    def _super_q(self) -> Q:
        """to combine all Q expressions from all base Mixins"""
        ignores = [FilterSchema, BaseFilterSchema, self.__class__, object]
        q = Q()
        for cls in self.__class__.__mro__:
            if cls not in ignores and hasattr(cls, "custom_expression"):
                q &= cls.custom_expression(self)
        return q

    def get_filter_expression(self) -> Q:
        """
        Returns a Q expression based on the current filters

        This is the OVERRIDE to official method.
        In doc, it described this function will handle fields filters after custom_expression.
        But they did not. Let me made it for us.

        TODO(sam): change it back or refactor it later. The behavior is different.
        """
        q = Q()
        try:
            q = self.custom_expression()
        except NotImplementedError:
            pass
        q &= self._connect_fields()
        return q


class CreateTimeFilterSchemaMixin:
    """filter items by create time range.

    The following 2 fields must be presented in child model to use this mixin.
    `created_before`: filter item create time before this.
    `created_after`: filter item create time after this.

    i.e.

    NOTE: Designing this mixin as this style is because `pydantic` does not support multiple inheritance so far.
    Otherwise, shall move these 2 fields in this Mixin as below. Now have to add them to real model.
    ```
    class CreateTimeFilterSchemaMixin:
        created_before: datetime = None
        created_after: datetime = None
    ```
    """

    def custom_expression(self) -> Q:
        q = Q()
        created_before: datetime = getattr(self, "created_before") or datetime.max
        created_after: datetime = getattr(self, "created_after") or datetime.min
        if created_before and created_after:
            if created_before > created_after:
                q = q & (Q(created_at__gt=created_after) & Q(created_at__lt=created_before))
            elif created_before < created_after:
                q = q & (Q(created_at__gt=created_after) | Q(created_at__lt=created_before))
            else:
                q &= Q(created_at=created_before)
        return q


class UpdateTimeFilterSchemaMixin:
    """filter items by update time range.

    The following 2 fields must be presented in child model to use this mixin.
    `updated_before`: filter item update time before this.
    `updated_after`: filter item update time after this.

    i.e.

    NOTE: Designing this mixin as this style is because `pydantic` does not support multiple inheritance so far.
    Otherwise, shall move these 2 fields in this Mixin as below. Now we have to add them to real model.
    ```
    class UpdateTimeFilterSchemaMixin:
        updated_before: datetime = None
        updated_after: datetime = None
    ```
    """

    def custom_expression(self) -> Q:
        q = Q()
        updated_before: datetime = getattr(self, "updated_before") or datetime.max
        updated_after: datetime = getattr(self, "updated_after") or datetime.min
        if updated_before and updated_after:
            if updated_before > updated_after:
                q = q & (Q(updated_at__gt=updated_after) & Q(updated_at__lt=updated_before))
            elif updated_before < updated_after:
                q = q & (Q(updated_at__gt=updated_after) | Q(updated_at__lt=updated_before))
            else:
                q &= Q(updated_at=updated_before)
        return q


class ChatFilterSchema(BaseFilterSchema, CreateTimeFilterSchemaMixin, UpdateTimeFilterSchemaMixin):
    """to filter chat conversation items"""

    title: str = Field(None, q="title__icontains")  # contains
    platform: Optional[str] = None

    created_before: Optional[datetime] = None
    created_after: Optional[datetime] = None

    updated_before: Optional[datetime] = None
    updated_after: Optional[datetime] = None

    def custom_expression(self) -> Q:
        return self._super_q()


class MessageFilterSchema(BaseFilterSchema, CreateTimeFilterSchemaMixin):
    """to filter chat messages"""

    role: Optional[str] = None
    text: str = Field(None, q="text__icontains")  # contains

    created_before: Optional[datetime] = None
    created_after: Optional[datetime] = None

    def custom_expression(self) -> Q:
        return self._super_q()


class SummarizeOneDocFilterSchema(Schema):
    """to filter sources"""

    key: str  # the doc key
    streaming: bool = True


class LimitFilterSchema(Schema):
    """to limit result."""

    limit: int | None = None
    min_ts: str = ""


class GetDocFilterSchema(Schema):
    """to get a doc content, meta or etc."""

    key: str  # the doc key


class AddHistorySchema(Schema):
    """to add a history record"""

    value: str


class SnippetSchema(Schema):
    snippet: str
    number: int


class SimpleSearchSchema(Schema):
    snippets: List[SnippetSchema]
    doc_key: str


class SearchInDocsSchema(Schema):
    query: str
    tickers: Optional[list[str]] = []
    doc_keys: Optional[list[str]] = []
    doc_types: Optional[list[str]] = []
    min_date: Optional[str] = None
    max_date: Optional[str] = None


class CreatePresignedUrlSchema(Schema):
    file_path: str
    content_type: str


class PlanningFormSchema(Schema):
    payload: str = Field(..., description="JSON string of payload")
    union_id: str


class ShareChatTurnSchema(Schema):
    title: str


class CmsFilterSchema(Schema):
    type: Optional[str] = None
    filters: Optional[Json[Dict[str, Any]]] = None
    sort: Optional[str] = None


class ContactUsSchema(Schema):
    email: str
    first_name: str
    last_name: str
    profession: str
    message: str
    source: str
    url: str
    type: str


class CompanyWatchListDocumentListParams(Schema):
    max_docs_per_ticker: int = 20
