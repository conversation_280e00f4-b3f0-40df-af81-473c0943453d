from logging import get<PERSON>ogger
from typing import As<PERSON><PERSON>enerator

from playwright.async_api import <PERSON><PERSON>er<PERSON><PERSON>xt as Async<PERSON>rowserContext

from scraper.spiders.base import (
    PlaywrightSpiderConfig,
    SpiderNavAnchor,
    PlaywrightSpider,
)

logger = getLogger(__name__)


class VisualSpiderConfig(PlaywrightSpiderConfig):
    """the config for contact spider"""

    async def async_get_browser_context(self) -> AsyncBrowserContext:
        is_created = self._browser_context is None
        await super().async_get_browser_context()
        # only add cookies if the context is newly created
        if not self.user_data_dir and is_created:
            all_cookies = []
            for platform in ["tiktok", "instagram"]:
                cookies = await self._async_load_cookies(platform)
                all_cookies.extend(cookies)
                logger.debug(f"{platform} cookies added")
            await self._browser_context.add_cookies(all_cookies)
        return self._browser_context


class VisualSpider(PlaywrightSpider):
    config: VisualSpiderConfig

    async def async_start(self) -> None:
        raise NotImplementedError

    async def async_check_if_has_next_page(self) -> bool:
        raise NotImplementedError

    def get_next_page_url(self, anchor: SpiderNavAnchor = None) -> str:
        raise NotImplementedError

    def async_iterate(self) -> AsyncGenerator[CrawledEntry, None]:
        raise NotImplementedError
