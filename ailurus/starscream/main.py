import asyncio
import sys
from pathlib import Path

from starscream.agent import VisualSpiderAgentConfig, VisualSpiderAgent

lib_paths = [
    Path(__file__).absolute().parent.parent.parent.parent / "lab/lab-llm",
    Path(__file__).absolute().parent.parent.parent.parent / "lab/lab-pycommon",
]
[sys.path.insert(0, str(p)) for p in lib_paths]

# Import pyllm instead of lab_llm
import logging
import colorlog

colorlog.basicConfig(
    level=logging.DEBUG,
    format="%(log_color)s[%(levelname).05s] [%(asctime)s] [%(name)s:%(lineno)d] %(message)s",
)

url = ""


async def start_visual() -> None:
    cfg = VisualSpiderAgentConfig(max_depths=4)
    agent = VisualSpiderAgent(config=cfg)
    rc = await agent.run(url)
    print("------  result  ------")
    print(rc or "not found")
    print("----------------------")


async def start_douyin() -> None:
    cfg = VisualSpiderAgentConfig(max_depths=4)
    agent = VisualSpiderAgent(config=cfg)
    rc = await agent.run(url)
    print("------  result  ------")
    print(rc or "not found")
    print("----------------------")


if __name__ == "__main__":
    asyncio.run(start_visual())
