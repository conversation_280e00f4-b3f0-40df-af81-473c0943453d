from datetime import datetime, timedelta, UTC
from logging import getLogger

import jwt
from django.conf import settings

from djcommon.model import User
from .models import Profile

logger = getLogger(__name__)


class AccessDenyError(Exception):
    pass


class TrialExpiredError(AccessDenyError):
    pass


class SubscriptionExpiredError(AccessDenyError):
    pass


class JwtSecret:
    _jwt_algo = "HS256"
    _jwt_key = settings.SECRET_KEY
    _jwt_headers = {"alg": _jwt_algo, "typ": "JWT"}

    @classmethod
    def refresh_token(cls, user: User, expires: int = conf.RESTAPI_TOKEN_EXPIRATION) -> str:
        payload = {
            "sub": user.id,
            "name": user.username,
            "iss": "urn:onwish",
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(seconds=expires),
        }
        token = jwt.encode(
            payload,
            cls._jwt_key,
            algorithm=cls._jwt_algo,
            headers=cls._jwt_headers,
        )
        return token

    @classmethod
    def verify_token(cls, token: str) -> dict:
        payload = jwt.decode(
            token,
            cls._jwt_key,
            leeway=15,
            algorithms=[cls._jwt_algo],
            headers=cls._jwt_headers,
        )
        return payload


async def check_subscription(user: User) -> None:
    sub = None

    if await user.groups.filter(name="internal_subscribers").aexists():
        # logger.debug(f"{user} is an internal subscriber.")
        return

    async for sub in user.subscription_set.all():
        if not sub.is_expired:
            # logger.debug(f"{user} has available subscription {sub}")
            break

    if sub and sub.is_expired:
        raise SubscriptionExpiredError("subscription expired.")

    try:
        profile = await Profile.objects.aget(user=user)
    except Profile.DoesNotExist:
        profile = None

    if (
        not sub
        and profile
        and profile.trial_start_date
        and (datetime.now(tz=UTC) - profile.trial_start_date > timedelta(minutes=conf.TRIAL_MINUTES))
    ):
        raise TrialExpiredError("trial expired.")

    (
        logger.debug(f"{user} has subscription {sub}. Trial duration is {conf.TRIAL_MINUTES} minutes.")
        if conf.VERBOSE > 1
        else None
    )
