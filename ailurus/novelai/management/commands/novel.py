import asyncio
import json
import random
from textwrap import fill
from typing import Any, Optional, List

from django.core.management.base import BaseCommand
from django.utils.translation import gettext as _
from prettytable import PrettyTable

from novelai.engine.agent import (
    StoryAgentConfig,
    StoryAgent,
    StoryCharacterAgentConfig,
    StoryCharacterAgent,
)
from novelai.engine.character import <PERSON>Character
from novelai.engine.consts import NOVEL_TYPES
from novelai.engine.story import Story
from pycommon.utils.lang import obj_to_dict_dumps
from pycommon.utils.strutil import ellipse
from pycommon.utils.terminal import tip, error
from pyllm import ModelNameEnum

LLM_MODEL = ModelNameEnum.GROK_V2


class Command(BaseCommand):
    """Command to operate novelai model elements.

    Note: all function names are combined by arguments <action> and <element>, so do not use plural form.
    """

    help = "write novel"

    # -----  story  -----
    async def async_create_story(self, **kwargs: Any) -> Optional[Story]:
        # Get novel type from user or generate randomly
        novel_type = input(_("Enter novel type (press Enter for random): ")).strip()
        if not novel_type:
            novel_type = "+".join(random.sample(list(NOVEL_TYPES.keys()), random.randint(2, 4)))
            tip(_("Generated novel type: {}").format(novel_type))

        # Get word count from user or use default
        while True:
            word_count = input(_("Enter word count (max 1,000,000, press Enter for default 50000): ")).strip()
            if not word_count:
                word_count = 50000
                tip(_("Using default word count: {}").format(word_count))
                break
            try:
                word_count = int(word_count)
                if 0 < word_count <= 1_000_000:
                    break
                error(_("Word count must be between 1 and 1000000"))
            except ValueError:
                error(_("Please enter a valid number"))

        config = StoryAgentConfig(
            type=novel_type,
            word_count=word_count,
            llm_model=LLM_MODEL,
        )
        agent = StoryAgent(config=config)
        story = await agent.async_run()
        if story:
            await self.async_show_story(story=story)
            return story
        return None

    async def async_delete_story(self, **kwargs: Any) -> None:
        story = await self._async_get_story(**kwargs)
        await story.adelete()
        self.stdout.write(_("Story {}({}) deleted successfully").format(story.name, story.key))

    async def async_list_story(self, **kwargs: Any) -> List[Story]:
        stories = []
        fields = ["id", "name", "key", "words plan", "types", "theme", "sub-themes", "plot", "plot characters"]
        table = PrettyTable(field_names=fields, border=False, preserve_internal_border=True, align="l")
        async for story in Story.objects.all():
            stories.append(story)
            table.add_row(
                [
                    story.id,
                    story.name,
                    story.key,
                    story.words_plan,
                    story.types,
                    ",".join(story.themes.get("major") or []),
                    ",".join(story.themes.get("minor") or []),
                    fill(ellipse(str(story.plot), 50), width=20),
                    fill(ellipse("\n".join([f"{k}: {v}" for k, v in story.plot_characters.items()]), 50), width=20),
                ]
            )
        print(table)
        return stories

    async def async_show_story(self, **kwargs: Any) -> Story:
        story = await self._async_get_story(**kwargs)
        table = PrettyTable(
            header=False,
            border=True,
            hrules=True,
            align="l",
        )
        keys = [f.name for f in Story._meta.get_fields() if not f.is_relation]
        for key in keys:
            table.add_row([key, fill(str(getattr(story, key)), width=50)])
        print(table)
        return story

    # -----  character  -----
    async def async_create_character(self, **kwargs: Any) -> Optional[list[StoryCharacter]]:
        story = await self._async_get_story(**kwargs)
        config = StoryCharacterAgentConfig(name=story.key, llm_model=LLM_MODEL)
        agent = StoryCharacterAgent(config=config)
        characters = await agent.async_run()
        print(json.dumps(obj_to_dict_dumps(characters), indent=2, default=str))
        if characters:
            return characters
        return None

    async def async_delete_character(self, **kwargs: Any) -> None:
        character = await self._async_get_character(**kwargs)
        await character.adelete()
        self.stdout.write(f"Character {character.key} deleted successfully")

    async def async_list_character(self, **kwargs: Any) -> List[StoryCharacter]:
        story = await self._async_get_story(**kwargs)
        characters = []
        headers = ["id", "story", "name", "key", "nicks", "bio"]
        table = PrettyTable(field_names=headers, border=False, preserve_internal_border=True, align="l")
        async for character in story.storycharacter_set.all():
            characters.append(character)
            table.add_row(
                [
                    character.id,
                    character.story.name,
                    character.name,
                    character.key,
                    ",".join(character.nicks),
                    ellipse(str(character.bio), 50),
                ]
            )
        print(table)
        return characters

    async def async_show_character(self, **kwargs: Any) -> StoryCharacter:
        character = await self._async_get_character(**kwargs)
        table = PrettyTable(
            field_names=["Field", "Value"],
            border=False,
            preserve_internal_border=True,
            align="l",
        )
        for key, value in character.to_dict().items():
            table.add_row([key, str(value)])
        print(table)
        return character

    # -----  outline  -----
    async def async_create_outline(self, **kwargs: Any) -> Optional[Any]:
        # TODO: Implement outline creation logic
        self.stdout.write(_("Outline creation not implemented yet"))
        return None

    async def async_delete_outline(self, **kwargs: Any) -> None:
        outline = await self._async_get_outline(**kwargs)
        await outline.adelete()
        self.stdout.write(_("Outline {} deleted successfully").format(outline.key))

    async def async_list_outline(self, **kwargs: Any) -> List[Any]:
        story = await self._async_get_story(**kwargs)
        outlines = []
        headers = ["name", "key", "content"]
        table = PrettyTable(field_names=headers, border=False, preserve_internal_border=True, align="l")
        async for outline in story.outlines.all():
            outlines.append(outline)
            content_preview = outline.content[:50] + "..." if len(outline.content) > 50 else outline.content
            table.add_row([outline.name, outline.key, content_preview])
        print(table)
        return outlines

    async def async_show_outline(self, **kwargs: Any) -> Any:
        outline = await self._async_get_outline(**kwargs)
        table = PrettyTable(
            field_names=["Field", "Value"],
            border=False,
            preserve_internal_border=True,
            align="l",
        )
        for key, value in outline.to_dict().items():
            table.add_row([key, str(value)])
        print(table)
        return outline

    # -----  chapter  -----
    async def async_create_chapter(self, **kwargs: Any) -> Optional[Any]:
        # TODO: Implement chapter creation logic
        self.stdout.write(_("Chapter creation not implemented yet"))
        return None

    async def async_delete_chapter(self, **kwargs: Any) -> None:
        chapter = await self._async_get_chapter(**kwargs)
        await chapter.adelete()
        self.stdout.write(_("Chapter {} deleted successfully").format(chapter.key))

    async def async_list_chapter(self, **kwargs: Any) -> List[Any]:
        story = await self._async_get_story(**kwargs)
        chapters = []
        headers = ["name", "key", "content"]
        table = PrettyTable(field_names=headers, border=False, preserve_internal_border=True, align="l")
        async for chapter in story.chapters.all():
            chapters.append(chapter)
            content_preview = chapter.content[:50] + "..." if len(chapter.content) > 50 else chapter.content
            table.add_row([chapter.name, chapter.key, content_preview])
        print(table)
        return chapters

    async def async_show_chapter(self, **kwargs: Any) -> Any:
        chapter = await self._async_get_chapter(**kwargs)
        table = PrettyTable(
            field_names=["Field", "Value"],
            border=False,
            preserve_internal_border=True,
            align="l",
        )
        for key, value in chapter.to_dict().items():
            table.add_row([key, str(value)])
        print(table)
        return chapter

    # -----  helper functions  -----
    async def _async_get_story(self, **kwargs: Any) -> Story:
        story_id_or_key = kwargs["story"]
        if not story_id_or_key:
            self.stderr.write(_("Story id or key is required"))
            exit(1)

        if isinstance(story_id_or_key, Story):
            story = story_id_or_key
        else:
            if story_id_or_key.isdigit():
                story = await Story.objects.filter(id=story_id_or_key).afirst()
            else:
                story = await Story.objects.filter(key=story_id_or_key).afirst()
            if not story:
                self.stderr.write(_("Story with id or key {} not found").format(story_id_or_key))
                exit(1)
        return story

    async def _async_get_character(self, **kwargs: Any) -> StoryCharacter:
        character_id_or_key: str = kwargs["character"]
        if character_id_or_key.isdigit():
            character = await StoryCharacter.objects.afirst(id=character_id_or_key)
        else:
            character = await StoryCharacter.objects.afirst(key=character_id_or_key)
        if not character:
            self.stderr.write(_("Character with id or key {} not found").format(character_id_or_key))
            exit(1)
        return character

    async def _async_get_outline(self, **kwargs: Any) -> Any:
        # TODO: Implement outline retrieval logic
        self.stderr.write(_("Outline retrieval not implemented yet"))
        exit(1)

    async def _async_get_chapter(self, **kwargs: Any) -> Any:
        # TODO: Implement chapter retrieval logic
        self.stderr.write(_("Chapter retrieval not implemented yet"))
        exit(1)

    def add_arguments(self, parser):
        parser.add_argument(
            "action",
            type=str,
            choices=["create", "delete", "list", "show"],
            help="Action to perform on the element",
        )
        parser.add_argument(
            "element",
            type=str,
            choices=["story", "character", "outline", "chapter"],
            help="Element to perform action on",
        )
        parser.add_argument(
            "--story",
            type=str,
            help="Story ID or key",
        )
        parser.add_argument(
            "--character",
            type=str,
            help="Character ID or key",
        )
        parser.add_argument(
            "--outline",
            type=str,
            help="Outline ID or key",
        )
        parser.add_argument(
            "--chapter",
            type=str,
            help="Chapter ID or key",
        )

    def handle(self, *args: Any, **kwargs: Any) -> None:
        action = kwargs["action"]
        element = kwargs["element"]
        func_name = f"async_{action}_{element}"
        func = getattr(self, func_name)
        if not func:
            self.stderr.write(f'"{action} {element}" is not implemented.')
            exit(1)

        asyncio.run(func(**kwargs))
        self.stdout.write(self.style.SUCCESS("-- FINISHED --"))
