NOVEL_TYPE_DICT = {
    "玄幻": {
        "desc": "以东方神话、仙侠、武道为背景，注重修炼、战斗和奇幻设定",
        "examples": ["斗破苍穹", "武动乾坤"],
        "popularity": 10,
    },
    "仙侠": {
        "desc": "聚焦修仙、道教文化，强调长生、飞升和仙魔争斗",
        "examples": ["凡人修仙传", "诛仙"],
        "popularity": 9,
    },
    "奇幻": {
        "desc": "融合西方奇幻元素，如魔法、龙族、异世界冒险",
        "examples": ["诡秘之主", "奥术神座"],
        "popularity": 7,
    },
    "科幻": {"desc": "涉及未来科技、星际探索、人工智能等主题", "examples": ["三体", "流浪地球"], "popularity": 6},
    "都市": {
        "desc": "以现代城市为背景，常包含职场、异能、爱情或重生元素",
        "examples": ["重生之都市修仙", "大王饶命"],
        "popularity": 9,
    },
    "历史": {
        "desc": "以中国古代为背景，包含穿越、架空历史、权谋等",
        "examples": ["赘婿", "回到明朝当王爷"],
        "popularity": 8,
    },
    "穿越": {
        "desc": "主角穿越到古代或其他时空，结合历史、仙侠等元素",
        "examples": ["庆余年", "步步惊心"],
        "popularity": 8,
    },
    "重生": {
        "desc": "主角重生到过去或平行世界，改变命运，常見於都市、商战",
        "examples": ["重生完美时代", "重生之财源滚滚"],
        "popularity": 8,
    },
    "悬疑": {"desc": "以推理、探案、灵异或心理博弈为主", "examples": ["盗墓笔记", "鬼吹灯"], "popularity": 7},
    "爱情": {
        "desc": "聚焦浪漫情感，包含现代、古代、校园等背景",
        "examples": ["何以笙箫默", "微微一笑很倾城"],
        "popularity": 9,
    },
    "武侠": {
        "desc": "以江湖、武功、侠义为主题，传统风格或新武侠网文",
        "examples": ["天龙八部", "笑傲江湖"],
        "popularity": 5,
    },
    "游戏": {"desc": "以虚拟网游或电竞为背景", "examples": ["全职高手", "网游之近战法师"], "popularity": 7},
    "无限流": {
        "desc": "主角在多个副本世界冒险，融合科幻、恐怖等元素",
        "examples": ["无限恐怖", "最终进化"],
        "popularity": 6,
    },
    "灵异": {"desc": "以鬼怪、恐怖、神秘事件为主", "examples": ["我有一座恐怖屋", "深夜书屋"], "popularity": 6},
    "种田": {
        "desc": "注重生活化、缓慢发展，常见于穿越或历史背景",
        "examples": ["农家小福女", "田园小当家"],
        "popularity": 7,
    },
}
NOVEL_TYPES = list(NOVEL_TYPE_DICT.keys())
MyLiteralType = Literal[*my_list]


NOVEL_THEME_DICT = {
    "复仇": {
        "desc": "主角因家族覆灭、背叛或冤屈而踏上复仇之路",
        "examples": ["《斗破苍穹》中萧炎的复仇成长", "《武动乾坤》中林动的复仇", "《雪中悍刀行》中徐凤年的家仇"],
        "popularity": 9,
    },
    "救赎": {
        "desc": "主角通过努力救赎自己或他人，弥补过去遗憾，常見於重生文",
        "examples": ["《重生之都市修仙》", "《重生完美时代》", "《重生之财源滚滚》"],
        "popularity": 7,
    },
    "寻找自我": {
        "desc": "主角在冒险或修炼中探索自我价值与人生意义",
        "examples": ["《诡秘之主》中克莱恩的身份追寻", "《奥术神座》中路西恩的探索", "《大道朝天》中井九的自我认知"],
        "popularity": 6,
    },
    "对抗邪恶": {
        "desc": "主角与邪恶势力、魔族或阴谋集团斗争，保卫正义",
        "examples": ["《诛仙》中对抗魔教", "《凡人修仙传》中对抗魔宗", "《遮天》中对抗禁区"],
        "popularity": 8,
    },
    "成长与蜕变": {
        "desc": "从平凡到强大的成长历程，克服困难实现蜕变",
        "examples": ["《凡人修仙传》中韩立的奋斗", "《斗罗大陆》中唐三的成长", "《大主宰》中牧尘的蜕变"],
        "popularity": 10,
    },
    "爱情与守护": {
        "desc": "以爱情为核心，守护爱人或追求真爱",
        "examples": ["《何以笙箫默》的深情守候", "《微微一笑很倾城》", "《三生三世十里桃花》"],
        "popularity": 9,
    },
    "权谋与争霸": {
        "desc": "围绕权力斗争、帝王霸业或家族兴衰展开",
        "examples": ["《赘婿》中的商战与权谋", "《庆余年》中范闲的权谋", "《琅琊榜》的朝堂博弈"],
        "popularity": 8,
    },
    "逆袭": {
        "desc": "底层主角通过智慧或机缘逆袭，改变命运",
        "examples": ["《斗罗大陆》中的唐三", "《武动乾坤》中的林动", "《全职高手》中叶修的逆袭"],
        "popularity": 9,
    },
    "探索与冒险": {
        "desc": "在未知世界或副本中探险，揭开秘密",
        "examples": ["《盗墓笔记》的探墓之旅", "《鬼吹灯》的冒险", "《诡秘之主》的神秘探索"],
        "popularity": 7,
    },
    "家族复兴": {
        "desc": "主角肩负复兴家族或宗门使命，重建荣耀",
        "examples": ["《武动乾坤》中林动的家族崛起", "《斗破苍穹》中萧炎的家族复兴", "《雪中悍刀行》中徐家的复兴"],
        "popularity": 7,
    },
    "自由与反抗": {
        "desc": "对抗命运束缚或强权压迫，追求自由",
        "examples": ["《雪中悍刀行》的江湖抗争", "《仙逆》中王林的反抗", "《完美世界》中石昊的自由之路"],
        "popularity": 6,
    },
    "传承与责任": {
        "desc": "继承前辈遗志或承担拯救世界的责任",
        "examples": ["《遮天》中的传承与宇宙使命", "《圣墟》中楚风的使命", "《诛仙》中张小凡的责任"],
        "popularity": 6,
    },
    "友情与羁绊": {
        "desc": "强调伙伴间的信任与共同成长",
        "examples": [
            "《全职高手》中战队的团队精神",
            "《斗破苍穹》中萧炎与朋友的羁绊",
            "《斗罗大陆》中史莱克七怪的友情",
        ],
        "popularity": 6,
    },
    "自我超越": {
        "desc": "通过修炼或磨砺突破自身极限，追求更高境界",
        "examples": ["《大主宰》中的牧尘", "《凡人修仙传》中韩立的突破", "《遮天》中叶凡的超越"],
        "popularity": 8,
    },
    "轮回与命运": {
        "desc": "探索命运安排或轮回因果，打破宿命",
        "examples": ["《仙逆》中王林的逆天之路", "《遮天》中轮回的探索", "《西游记》中孙悟空的命运抗争"],
        "popularity": 5,
    },
}
NOVEL_THEMES = list(NOVEL_THEME_DICT.keys())
