import json
from typing import Iterable, ClassVar

from django.db import models

from djcommon.model import DictMixin, CreateTimeMixin, UpdateTimeMixin
from novelai.engine.base import PromptAble


class NovelModelBase(DictMixin, CreateTimeMixin, UpdateTimeMixin, PromptAble, models.Model):
    """abstract base model for all Django models which will be used as LLM Agent model"""

    __schema_ignored_fields: ClassVar[set[str]] = {"id", "key", "created_at", "updated_at", "lang"}

    @classmethod
    def as_schema(cls) -> dict:
        """return model's schema for LLM Agent use as JSON schema"""
        fields = cls._meta.fields
        schema = {"type": "object", "properties": {}, "required": []}

        for field in fields:
            if field.name in cls.__schema_ignored_fields:
                continue

            field_schema = {"type": cls._get_field_type(field), "description": field.help_text or ""}

            if field.max_length:
                field_schema["maxLength"] = field.max_length

            if field.choices:
                field_schema["oneOf"] = [{k: v} for k, v in field.choices]

            schema["properties"][field.name] = field_schema
            schema["required"].append(field.name)

            # if not field.null and not field.blank:
            #     schema["required"].append(field.name)

        return schema

    @classmethod
    def as_schema_str(cls) -> str:
        schema = cls.as_schema()
        return json.dumps(schema, indent=2, ensure_ascii=False)

    @classmethod
    def as_array_schema(cls) -> dict:
        schema = {
            "type": "object",
            "properties": {"array": {"type": "array", "items": cls.as_schema()}},
            "required": ["array"],
        }
        return schema

    @classmethod
    def as_array_schema_str(cls) -> str:
        schema = cls.as_array_schema()
        return json.dumps(schema, indent=2, ensure_ascii=False)

    def as_prompt(self, includes: Iterable[str] = (), excludes: Iterable[str] = ()) -> str:
        """return model as a LLM prompt

        args:
            includes: list of field-names to include
            excludes: list of field-names to exclude
        """

        excludes = self.__schema_ignored_fields | set(excludes)
        includes = set(includes)

        prompt_parts = []
        fields = self._meta.fields
        for field in fields:
            if field.name in excludes and field.name not in includes:
                continue

            value = getattr(self, field.name)
            if value:
                prompt_parts.append(f"- {field.name}: {value}")

        return "\n".join(prompt_parts)

    @classmethod
    def _get_field_type(cls, field: models.Field) -> str:
        """Helper method to convert Django field types to JSON schema types"""
        if isinstance(field, (models.CharField, models.TextField)):
            return "string"
        elif isinstance(field, models.IntegerField):
            return "integer"
        elif isinstance(field, models.FloatField):
            return "number"
        elif isinstance(field, models.BooleanField):
            return "boolean"
        elif isinstance(field, models.DateTimeField):
            return "string"
        elif isinstance(field, models.JSONField):
            if field.default == list:
                return "array"
            else:
                return "object"
        else:
            return "string"

    class Meta:
        abstract = True
