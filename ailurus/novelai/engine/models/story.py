from django.db import models

from novelai import conf
from novelai.engine.author import AUTHOR_DICT
from novelai.engine.models.base import NovelModelBase
from pycommon.utils.strutil import to_pinyin, slugify

d_column_helps = {
    "name": {
        "zh": "小说名字，多样性的、流行的、具有多种元素且吸引人的名字（除了'-'外，不要使用其他标点符号）",
        "en": "The name of the story. A diverse, popular, and attractive name with multiple elements. (no punctuation other than '-')",
    },
    "author": {
        "zh": "小说作者名字",
        "en": "Name of the author of the story",
    },
    "key": {
        "zh": "小说唯一标识",
        "en": "The unique key of the story",
    },
    "words_plan": {
        "zh": "小说计划要写的字数",
        "en": "The planed word count of the story",
    },
    "lang": {
        "zh": "小说语言(default zh-Hans 简体中文 ISO 639)",
        "en": "The language of the story(default zh-Hans)",
    },
    "types": {
        "zh": "小说类型，多个类型用加号连接，如科幻+奇幻",
        "en": "The type of the story, multiple types joined by plus sign, like 'science-fiction+fantasy'. do not use blank space.",
    },
    "themes": {
        "zh": '小说主题。{"major":[<主要主题1>, <主要主题2>, ...], "minor":[<次要主题1>, <次要主题2>, ...]} 格式的 dict',
        "en": 'The themes of the story, dict like {"major":[<major theme 1>, <major theme 2>, ...], "minor":[<minor theme 1>, <minor theme 2>, ...]}',
    },
    "background": {
        "zh": "小说背景，在1000字以内",
        "en": "The background of the story, no more than 1000 words",
    },
    "plot": {
        "zh": "主要情节概要，大约在1000到5000字以内",
        "en": "The plot of the story, about 1000 to 5000 words",
    },
    "plot_characters": {
        "zh": "概要中产生的主要角色列表，使用 {<角色代码>:<角色描述>} 格式。角色代码请使用正常人物名组合(称号、昵称等可在描述里说明)。",
        "en": "The list of main characters generated from the plot summary. use {<character code>:<character description>} format. The character code should be built by normal name of the character. (titles, nicknames, etc. can be explained in the description).",
    },
}


class Story(NovelModelBase):
    """represents a story of the novel"""

    key = models.CharField(max_length=250, unique=True, help_text=d_column_helps["key"][conf.LANG])
    name = models.CharField(max_length=250, help_text=d_column_helps["name"][conf.LANG])
    author = models.CharField(
        max_length=250, help_text=d_column_helps["author"][conf.LANG], choices=((k, k) for k in AUTHOR_DICT.keys())
    )
    lang = models.CharField(max_length=20, default="zh-Hans", help_text=d_column_helps["lang"][conf.LANG])
    words_plan = models.IntegerField(default=20_000, help_text=d_column_helps["words_plan"][conf.LANG])
    types = models.CharField(max_length=50, help_text=d_column_helps["types"][conf.LANG])
    themes = models.JSONField(default=dict, blank=True, help_text=d_column_helps["themes"][conf.LANG])
    background = models.CharField(max_length=1000, help_text=d_column_helps["background"][conf.LANG])
    plot = models.TextField(default="", help_text=d_column_helps["plot"][conf.LANG])
    plot_characters = models.JSONField(default=dict, blank=True, help_text=d_column_helps["plot_characters"][conf.LANG])

    class Meta:
        verbose_name_plural = "stories"
        ordering = ["-created_at"]

    def __str__(self):
        return self.name

    @staticmethod
    def generate_key(name: str) -> str:
        """generate a unique key for story by a given name

        1. If name is English, combine all parts of names in lower case.
        2. If name is Chinese, combine all Pinyin of each word.

        Args:
            name: The story's name

        Returns:
            A unique key for the story
        """
        return slugify(to_pinyin(name, initial_only=True))
