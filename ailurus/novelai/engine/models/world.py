from django.db import models

from novelai import conf
from novelai.engine.story import NovelModelBase

d_column_helps = {
    "story": {
        "zh": "大纲纲所属的小说",
        "en": "The story this outline belongs to",
    },
    "name": {
        "zh": "世界观的名称",
        "en": "The name of the world schema",
    },
    "content": {
        "zh": "世界观的描述",
        "en": "The content of the world schema",
    },
}


class WorldSchema(NovelModelBase):
    """The world schema such as Level System, Magic System, etc. of the story"""

    story = models.ForeignKey(
        "Story",
        on_delete=models.CASCADE,
        help_text=d_column_helps["story"][conf.LANG],
    )
    name = models.Char<PERSON><PERSON>(max_length=250, help_text=d_column_helps["name"][conf.LANG])
    content = models.J<PERSON><PERSON>ield(default=dict, help_text=d_column_helps["content"][conf.LANG])

    class Meta:
        verbose_name_plural = "world schemas"
        ordering = ["story", "name"]
