from django.db import models

from djcommon.model import CreateTimeMixin, UpdateTimeMixin
from novelai import conf
from novelai.engine.story import NovelModelBase

d_column_helps = {
    "story": {
        "zh": "大纲纲所属的小说",
        "en": "The story this outline belongs to",
    },
    "background": {
        "zh": "背景故事",
        "en": "The background story",
    },
    "toc": {
        "zh": "章节目录",
        "en": "Table of contents as a list of dictionaries",
    },
}


class StoryOutline(NovelModelBase, CreateTimeMixin, UpdateTimeMixin):
    """represents the story outline."""

    story = models.ForeignKey(
        "Story",
        on_delete=models.CASCADE,
        help_text=d_column_helps["story"][conf.LANG],
    )
    background = models.TextField(help_text=d_column_helps["background"][conf.LANG])
    toc = models.JSONField(default=list, help_text=d_column_helps["toc"][conf.LANG])

    class Meta:
        ordering = ["story", "id"]
        # unique_together = ["story", "id"]

    def __str__(self):
        return f"Story Outline ({self.story}, {self.created_at})"
