from django.db import models

from djcommon.mixins import CreateTimeModelMixin
from djcommon.model import UpdateTimeMixin
from novelai import conf
from novelai.engine.story import NovelModelBase
from pycommon.utils.lang import BaseStrEnum

d_column_helps = {
    "story": {
        "zh": "章节所属的故事",
        "en": "The story this chapter belongs to",
    },
    "type": {
        "zh": "章节类型",
        "en": "The type of this chapter",
    },
    "order": {
        "zh": "章节在故事中的顺序",
        "en": "The order of this chapter in the story",
    },
    "title": {
        "zh": "章节标题",
        "en": "chapter title",
    },
    "content": {
        "zh": "章节内容",
        "en": "The content of the chapter",
    },
    "character_ids": {
        "zh": "本章出现的角色ID列表，按出场顺序排列",
        "en": "Character IDs present in this chapter, list in order of appearance",
    },
}


class StoryChapterType(BaseStrEnum):
    PREFACE = "preface"  # 序言 - Preface/Introduction
    NOTE = "note"  # 作者的话 - Author's notes
    CHAPTER = "chapter"  # 正文 - Main chapter content
    AFTERWORD = "afterword"  # 后记 - Afterword/Epilogue
    EXTRA = "extra"  # 番外 - Extra/side story
    INTERLUDE = "interlude"  # 插曲 - Interlude
    SPECIAL = "special"  # 特别篇 - Special chapter
    BONUS = "bonus"  # 福利篇 - Bonus content
    QA = "qa"  # 问答 - Q&A section
    PROFILE = "profile"  # 人物志 - Character profiles

    def __str__(self) -> str:
        return self.text_by_lang()

    def text_by_lang(self, lang: str = "") -> str:
        """Get localized string for chapter type

        Args:
            lang: Language code ('zh' or 'en'). If empty, uses conf.LANG

        Returns:
            Localized string for the chapter type
        """
        lang = lang or conf.LANG
        d_trans = {
            "preface": {"zh": "序言", "en": "Preface"},
            "note": {"zh": "作者的话", "en": "Author's Notes"},
            "chapter": {"zh": "正文", "en": "Chapter"},
            "afterword": {"zh": "后记", "en": "Afterword"},
            "extra": {"zh": "番外", "en": "Extra Story"},
            "interlude": {"zh": "插曲", "en": "Interlude"},
            "special": {"zh": "特别篇", "en": "Special Chapter"},
            "bonus": {"zh": "福利篇", "en": "Bonus Content"},
            "qa": {"zh": "问答", "en": "Q&A"},
            "profile": {"zh": "人物志", "en": "Character Profile"},
        }
        return d_trans[lang][self.value]


class StoryChapter(NovelModelBase, CreateTimeModelMixin, UpdateTimeMixin):
    """represents a chapter of a novel"""

    story = models.ForeignKey(
        "Story",
        on_delete=models.CASCADE,
        help_text=d_column_helps["story"][conf.LANG],
    )

    type = models.CharField(
        max_length=20,
        choices=StoryChapterType.choices(),
        default=StoryChapterType.CHAPTER,
        help_text=d_column_helps["type"][conf.LANG],
    )

    order = models.PositiveIntegerField(help_text=d_column_helps["order"][conf.LANG])
    title = models.CharField(max_length=255, help_text=d_column_helps["title"][conf.LANG])
    content = models.TextField(default="", help_text=d_column_helps["content"][conf.LANG])
    character_ids = models.JSONField(help_text=d_column_helps["character_ids"][conf.LANG])

    class Meta:
        ordering = ["story", "order"]
        unique_together = ["story", "order"]

    def __str__(self):
        return f"{self.story.title} - Chapter {self.order}: {self.title}"
