from django.core.validators import validate_unicode_slug
from django.db import models

from djcommon.model import CreateTimeMixin, UpdateTimeMixin
from novelai import conf
from novelai.models.base import NovelModelBase
from pycommon.utils.lang import BaseStrEnum
from pycommon.utils.strutil import to_pinyin


class StoryCharacterLevel(BaseStrEnum):
    MAIN = "main"  # main/key characters
    SECONDARY = "secondary"  # secondary/minor characters
    EXTRA = "extra"  # extras, background actors

    @classmethod
    def value_to_level(cls) -> dict[str, int]:
        return {
            StoryCharacterLevel.MAIN.value: 30,
            StoryCharacterLevel.SECONDARY.value: 20,
            StoryCharacterLevel.EXTRA.value: 10,
        }

    def __gt__(self, other: "StoryCharacterLevel") -> bool:
        l1: int = self.value_to_level()[self.value]
        l2: int = self.value_to_level()[other.value]
        return l1 > l2

    def __lt__(self, other: "StoryCharacterLevel") -> bool:
        l1: int = self.value_to_level()[self.value]
        l2: int = self.value_to_level()[other.value]
        return l1 < l2


d_column_helps = {
    "story": {
        "zh": "角色所属的小说",
        "en": "The story this character belongs to",
    },
    "name": {
        "zh": "角色的正式名字(可以修改，确认后使用)，考虑多样性，长短、古韵、现代、优雅、普通、搞笑等.",
        "en": "formal human name of the character(use it after confirmed). can be empty in the beginning.",
    },
    "code": {
        "zh": "角色的名字代码。小说中唯一, 不可修改，用于在撰写时在文中代替角色名字，便于后期整体替换为正式名字。使用'$['和']$'包围，例如: '$[张三]$', '$[李四]$' 等 ",
        "en": "code of the character, used to replace the character name in the writing process, convenience to replace all at once later. enclosed by '$[' and ']$', like '$[Michael]$', '$[SteveJobs]$', etc.",
    },
    "is_name_confirmed": {
        "zh": "角色的名字是否已经确认，确认后将不再修改。并且将不再使用代码(code)",
        "en": "whether the name of the character is confirmed, once confirmed, it can't be changed. and will no longer use code.",
    },
    "key": {
        "zh": "角色的唯一标识",
        "en": "unique identity key of the character",
    },
    "nicks": {
        "zh": "角色的昵称列表。最多不超过3个(视重要性)。反映角色某特质。",
        "en": "nickname list of the character. no more than 3 (by importance). reflect one aspect of the character.",
    },
    "level": {
        "zh": f"角色在故事中的重要程度. 主要角色({StoryCharacterLevel.MAIN.value}) > 次要角色({StoryCharacterLevel.SECONDARY.value}) > 配角({StoryCharacterLevel.EXTRA.value})",
        "en": f"role level of the character. {StoryCharacterLevel.MAIN.value} > {StoryCharacterLevel.SECONDARY.value} > {StoryCharacterLevel.EXTRA.value}",
    },
    "bio": {
        "zh": "角色的背景介绍，包括出生，经历，学习，成长，足迹等。",
        "en": "background introduction of the character, including birth, experience, learning, growth, footprint, etc.",
    },
    "personality": {
        "zh": "角色的性格特征",
        "en": "personality traits of the character",
    },
    "appearance": {
        "zh": "角色的外貌描述",
        "en": "physical appearance description of the character",
    },
    "background": {
        "zh": "角色的背景故事",
        "en": "background story of the character",
    },
    "motivation": {
        "zh": "角色的动机和目标",
        "en": "motivations and goals of the character",
    },
    "relationships": {
        "zh": "与其他角色的关系",
        "en": "relationships with other characters",
    },
}


class StoryCharacter(NovelModelBase, CreateTimeMixin, UpdateTimeMixin):
    """represents a character of a novel story"""

    story = models.ForeignKey(
        "Story",
        on_delete=models.CASCADE,
        help_text=d_column_helps["story"][conf.LANG],
    )
    name = models.CharField(max_length=255, blank=True, help_text=d_column_helps["name"][conf.LANG])
    code = models.CharField(max_length=20, help_text=d_column_helps["code"][conf.LANG])
    is_name_confirmed = models.BooleanField(default=False, help_text=d_column_helps["is_name_confirmed"][conf.LANG])
    key = models.CharField(
        max_length=50,
        unique=True,
        help_text=d_column_helps["key"][conf.LANG],
        validators=[validate_unicode_slug],
    )
    level = models.CharField(
        max_length=20,
        db_index=True,
        choices=StoryCharacterLevel.choices(),
        help_text=d_column_helps["level"][conf.LANG],
    )
    nicks = models.JSONField(default=list, blank=True, help_text=d_column_helps["nicks"][conf.LANG])
    bio = models.TextField(default="", help_text=d_column_helps["bio"][conf.LANG])
    personality = models.TextField(default="", help_text=d_column_helps["personality"][conf.LANG])
    appearance = models.TextField(default="", help_text=d_column_helps["appearance"][conf.LANG])
    background = models.TextField(default="", help_text=d_column_helps["background"][conf.LANG])
    motivation = models.TextField(default="", help_text=d_column_helps["motivation"][conf.LANG])
    relationships = models.JSONField(default=dict, blank=True, help_text=d_column_helps["relationships"][conf.LANG])

    class Meta:
        ordering = ["story", "level"]
        unique_together = [["story", "code"]]

    def __str__(self) -> str:
        return f"{self.name}({self.code}, {self.level}, {self.story, self.key}"

    def is_main(self) -> bool:
        """whether this character is main character"""
        return self.level == StoryCharacterLevel.MAIN

    @staticmethod
    def generate_key(code: str, story_key: str) -> str:
        """generate a unique key for character by a given name-code

        1. If code is English, combine all parts of code in lower case.
        2. If code is Chinese, combine all Pinyin of each word.
        3. generated key must be unique in a story.

        Args:
            code: The character's code
            story_key: The key of the story this character belongs to

        Returns:
            A unique key for the character
        """
        if not code:
            raise ValueError("code is required")
        code = code.strip().removeprefix("$[").removesuffix("]$")
        key = to_pinyin(code, initial_only=False)
        return f"{story_key}_{key}"
