import asyncio
import json
import logging
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Self

from pydantic import BaseModel

import pyllm
from pycommon.observation import <PERSON>LogObserver
from pycommon.utils.confutils import get_str_env
from pycommon.utils.lang import DictMixin
from pyllm import LlmClient
from .author import Author, AUTHOR_DICT
from .consts import WEB_NOVEL_THEME_DICT
from .models.chapter import StoryChapter
from .models.character import Story<PERSON>haracter
from .models.outline import StoryOutline
from .models.story import Story

logger = logging.getLogger(__name__)


# ==========   Base Agent Configs   ==========


class AgentConfig(BaseModel, DictMixin):
    """base config for all agents"""

    name: str = "agent"  # agent name
    streaming: bool = False
    llm_model: pyllm.ModelNameEnum = pyllm.ModelNameEnum.OLLAMA_DEEPSEEK_R1_8B

    class Config:
        arbitrary_types_allowed = True

    @staticmethod
    def get_agent_config_path() -> Path:
        return Path(get_str_env("AGENT_CONFIG_PATH") or "agent_configs")

    def __str__(self) -> str:
        return str(self.to_dict())

    @classmethod
    def load(cls, name: str, group: str) -> Self:
        """load from pre-defined json file
        NOTE: LLM_MODEL overriding env will not be effective if configs are loaded from json files.
        """
        config = cls()
        agent_config_path = cls.get_agent_config_path()
        path: Path = agent_config_path / group / f"{name}.json"
        if path.exists():
            try:
                with path.open() as fp:
                    config_dict = json.load(fp)
                config.update_from_dict(config_dict)
                logger.info(f'Agent config "{name}" ({cls.__name__}) loaded from group "{group}".')
            except Exception as e:
                logger.error(e)
                logger.error(f'Fail to load agent config "{name}" ({cls.__name__}) from {path}.')
        else:
            logger.debug(f'Agent config "{name}" ({cls.__name__}) not exists.')
        return config

    @classmethod
    def load_all(cls, group: str) -> dict[str, Self]:
        configs = {}
        agent_config_path = cls.get_agent_config_path()
        group_dir: Path = agent_config_path / group
        for p in group_dir.iterdir():
            if p.is_file() and p.suffix.lower() == ".json":
                name = p.stem
                try:
                    cfg = cls.load(name, group)
                    configs[name] = cfg
                except Exception as e:
                    logger.debug(f"{group}/{p} is not a config of {Self.__class__.__name__}. {e}")
        return configs


class NovelWritingConfig(AgentConfig, ABC):
    """base agent for all novel writing related agents"""

    author: Author | str
    novel_type: str  # novel type
    words_plan: int  # 计划字数. (中文,字数；英文:词数) planned word count, actually token count;
    novel_lang: str = "zh-Hans"  # 小说语言. default zh-Hans(简体中文 ISO 639)

    def __init__(self, **kwargs: Any):
        author = kwargs.get("author")
        if author and isinstance(author, str):
            author = AUTHOR_DICT.get(author)
            if author:
                kwargs["author"] = author
        super().__init__(**kwargs)

    @property
    def sys_prompt(self) -> str:
        return f"""{self.author.as_prompt}
在写作中，对于未确定的角色你喜欢使用<角色代码(code)>代替<角色名字(name)>，便于后期整体替换为确认的正式名字。角色代码格式是使用'$['和']$'包围，例如: '$[张三]$', '$[ZhangSan]$', '$[extra1]$' 等
出版社约你写一本{self.novel_type}类型的网络小说，因为极丰厚的报酬以及会带来非常大的影响力，你非常看重这本小说，因此进行了认真的构思。
"""


# ==========   Base Agents   ==========


class Agent(ABC):
    """base for agents"""

    config: AgentConfig

    _llm_cli: LlmClient
    _stop_event: asyncio.Event

    class Config:
        arbitrary_types_allowed = True

    def __init__(self, config: AgentConfig) -> None:
        self.config = config
        self._stop_event = asyncio.Event()
        llm_setting = pyllm.get_pre_defined_settings().get(self.config.llm_model).duplicate()
        llm_setting.max_input_token = 1_000_000
        llm_setting.max_output_token = 8192
        self._llm_cli = pyllm.create_client(llm_setting, fallback_to_default=True)
        self._llm_cli.ob.register_observer(PythonLogObserver(level="warning", format_json=True))

    @abstractmethod
    async def async_run(self, **kwargs: Any) -> Any:
        pass

    def stop(self) -> None:
        """stop the running agent"""
        self._stop_event.set()

    def shall_stop(self) -> bool:
        """check whether the running agent should stop"""
        return self._stop_event.is_set()


# ==========   Novel Writing   ==========


class NovelAgentConfig(NovelWritingConfig):
    """config for NovelAgent"""

    story: Story | None = None
    outline: StoryOutline | None = None
    characters: list[StoryCharacter] = []
    chapters: list[StoryChapter] = []


class NovelAgent(Agent):
    """撰写一本小说的智能体。An agent to write a complete novel automatically"""

    async def async_run(self, **kwargs: Any) -> Any:
        raise NotImplementedError


# ==========   Story Conceiving (for a novel)   ==========


class StoryAgentConfig(NovelWritingConfig):
    """config for StoryAgent"""

    name: str = "story-agent"
    less_themes: bool = True  # 是否使用较少的主题数, default True.


class StoryAgent(Agent):
    """为写小说构思一个故事的智能体。An agent to conceive a story for writing a novel"""

    config: StoryAgentConfig

    _theme_prompt: str = ""

    async def async_run(self, **kwargs: Any) -> Story:
        user_prompt = self.get_user_prompt()
        prompts = (
            pyllm.ChatPrompts(output_format="json", output_schema=Story.as_schema(), output_lang=self.config.novel_lang)
            .set_system(self.config.sys_prompt)
            .set_inquery(user_prompt)
        )
        llm_param = pyllm.create_param(self._llm_cli.setting, fallback_to_default=True, streaming=True, timeout=30)
        answer = await self._llm_cli.async_completion(prompts, llm_param)
        d = json.loads(answer)
        story = Story(
            **d,
            key=Story.generate_key(d["name"]),
            lang=self.config.novel_lang,
        )
        await story.asave()
        return story

    def get_user_prompt(self) -> str:
        major_theme_count, minor_theme_count = self.get_theme_counts()

        return f"""这本小说你计划写约{self.config.words_plan}字，并有如下构思：

# 主题
{self.get_theme_prompt()}
你从中选择了{major_theme_count}个主要主题{'' if major_theme_count>1 else '交织'}贯通主线{'，'+str(minor_theme_count)+'个次要主题穿插其间' if minor_theme_count>0 else ''}。
经过认真的思考、设计，你已经确定了主题、背景、情节以及几个主要人物（人物名称使用代码,格式 $[张三]$）。输出如下：
"""

    def get_theme_counts(self) -> tuple[int, int]:
        """Get major and minor counts of themes depending on the word count.

        Returns:
            tuple[int, int]: A tuple containing the counts of major themes and minor themes
        """
        less = self.config.less_themes
        if self.config.words_plan < 100_000:
            return (1, 0) if less else (1, 1)
        elif self.config.words_plan < 300_000:
            return (1, 2) if less else (2, 4)
        elif self.config.words_plan < 1_000_000:
            return (2, 3) if less else (3, 5)
        else:
            return (3, 5) if less else (4, 7)

    def get_theme_prompt(self) -> str:
        if not self._theme_prompt:
            s = "现在流行的故事主题主要有如下这些（当然你也可以选择其他主题）：\n"
            for k, v in WEB_NOVEL_THEME_DICT.items():
                s += f"- {k}：{v['desc']}。例如:{', '.join(v['examples'])}。流行度:{v['popularity']}/10\n"
            self._theme_prompt = s
        return self._theme_prompt


# ==========   Outline Design (for a novel)   ==========


class StoryOutlineAgentConfig(NovelWritingConfig):
    """config for StoryOutlineAgent"""

    name: str = "outline-agent"

    @property
    def outline(self) -> StoryOutline | None:
        """Get the story outline from the database.

        Returns:
            StoryOutline: The outline of the story

        Raises:
            ValueError: If story is not set in config
        """
        if not self.story:
            return None

        return StoryOutline.objects.filter(story_id=self.story.id).first()


class StoryOutlineAgent(Agent):
    """设计小说故事大纲的智能体。An agent to design the outline of a story for writing a novel"""

    config: StoryOutlineAgentConfig

    async def async_run(self, **kwargs: Any) -> StoryOutline:
        story = self.config.story
        if not story:
            raise ValueError("Story must be conceived before designing outline")

        # Create prompt for outline
        outline_prompt = f"""请为一部约{self.config.story}字的仙侠（中国奇幻/修仙）小说生成一份详细提纲，面向喜爱长篇、复杂剧情和深刻人物塑造的成人读者。提纲需结构清晰、内容全面，包含以下要素，体现仙侠特色的修真体系、门派争斗、仙魔对立和情感纠葛：

1. **故事概述**：
   - 提供小说暂定标题。
   - 明确类型（仙侠，包含奇幻、修真、冒险、情感等元素）。
   - 描述基调与风格（如恢弘大气，融合热血与细腻情感）。
   - 用2-3句话概括核心情节（如凡人少年获上古仙器，逆天改命，对抗仙魔势力）。
   - 确定核心主题（如自我超越、爱与牺牲、自由与天道的抗争）。
   - 明确目标读者（仙侠爱好者，偏爱长篇剧情）。
   - 预计字数（约100万字，300-400章，每章2500-3500字）。
   - 指定叙述视角（如第三人称有限视角，以主角为主，辅以关键配角）。
   - 概述主要冲突：
     - 外部：主角对抗敌对势力（如魔族、敌对宗门）。
     - 内部：主角面对心魔、情感纠葛或修道信念的挣扎。
   - 提供5-8句话的故事概述，涵盖开端、中段和结局。

2. **世界观设定**：
   - 描述设定（如凡人界、修仙界九大洲、仙界、魔界、秘境）。
   - 详述修真体系（如境界划分：炼气、筑基、金丹等，每阶段分初期、中期、后期、巅峰；包括灵根、功法、丹药、灵宝、天劫、灵兽契约、阵法等独特机制）。
   - 列出主要势力（正派宗门、魔道势力、中立散修或妖族、其他如上古遗族）。
   - 概述世界历史（如万年前仙魔大战）与文化（如尊师重道、修士视凡人为蝼蚁）。
   - 定义核心宝物或元素（如上古仙器“天霄印”）及天地规则（如天道、因果报应）。

3. **人物设定**：
   - 塑造主角（姓名、背景、性格、灵根、动机、从凡人到传奇的成长弧线）。
   - 详细描述关键配角（如红颜知己的悲剧弧线、忠诚挚友、导师、魔尊级主反派、次要对头）。
   - 加入其他推动剧情的角色（如妖族领袖、仙界使者）。
   - 提供人物关系图（如主角与红颜因门派对立产生误会、与挚友的兄弟情、与反派的理念冲突）。

4. **情节框架（三幕式）**：
   - **第一幕：开端**（约20万字，60章）：
     - 目标：建立世界观，介绍主角和核心冲突，埋下伏笔。
     - 列出关键事件（如主角家族覆灭，加入宗门，发现核心宝物）。
     - 定义转折点（如主角突破境界，暴露宝物引来危机）。
     - 说明情感基调（如希望与挣扎交织）。
   - **第二幕：中段**（约60万字，180章）：
     - 目标：深化冲突，展现成长，揭示世界真相。
     - 分为前期、中期、后期，列出关键事件（如宗门大战、秘境探险、盟友背叛）。
     - 定义转折点（如揭开反派阴谋，失去重要盟友）。
     - 说明情感基调（如热血与悲壮）。
   - **第三幕：高潮与结局**（约20万字，60章）：
     - 目标：解决主要冲突，完成人物弧线，收束伏笔。
     - 列出关键事件（如最终战、牺牲、重塑天地秩序）。
     - 定义转折点（如主角从个人复仇转向为天下而战）。
     - 说明情感基调（如悲壮与希望并存）。

5. **章节规划**：
   - 提供前10章的详细规划（每章约3000字），包括：
     - 场景（如凡人界、宗门、秘境）。
     - 事件（如主角的引子事件、结识关键角色、早期挑战）。
     - 涉及人物。
     - 后续剧情的伏笔（如宝物来历、反派计划的暗示）。
   - 概述后续章节（11-300+章）的发展，每10-15章为一个小型故事弧，每50章设置重大转折，确保节奏均衡。

6. **主题与象征**：
   - 明确核心主题（如自我超越、爱与牺牲、挑战天道）。
   - 突出象征元素（如仙器象征命运、天劫隐喻内心挣扎）。
   - 概述情感线索（如悲剧爱情、兄弟情深、理念对决）。

7. **其他要素**：
   - 列出关键伏笔与线索（如仙器真相、反派真实身份）。
   - 建议节奏控制（如每5-10章一个小高潮，每30-50章一个大高潮，动作与情感戏交替）。
   - 强调仙侠特色：
     - 战斗描写：突出法宝、阵法、功法的独特设计，注重视觉冲击。
     - 秘境探险：融入机关、灵兽、遗迹，增强冒险感。
     - 情感纠葛：师徒情、道侣情、宿命对决，强化人物冲突。
   - 推荐写作风格：典雅中带热血，融入古风诗词（如功法命名、场景描写）；注重画面感（如仙山云海、魔气战场）；关键人物命运以暗示方式处理，增强余韵。

8. **创作建议**：
   - 提供前期准备建议（如细化修真体系、绘制地图、建立人物档案）。
   - 提出节奏策略（如前50章吸引读者，中段保持悬念，后期收束伏笔）。
   - 建议灵活性（如根据灵感调整次要剧情）。
   - 考虑读者期待（如逆袭、荡气回肠的爱情、哲思深度；避免复杂体系或冗长说明）。

**输出格式**：
- 以详细的 markdown 文档形式输出。
- 将整个提纲包裹在单个 `<xaiArtifact>` 标签中，设置唯一 UUID 作为 `artifact_id`，标题为“仙侠小说提纲”，`contentType="text/markdown"`。
- 确保提纲全面且灵活，清晰划分各部分。
- 使用符合仙侠氛围的命名（如天霄宗、星陨剑）为角色、宗门、宝物、功法命名。
- 平衡动作、情感和世界观，体现仙侠精神。

**附加说明**：
- 参考经典仙侠元素（如草根逆袭、悲剧爱情、上古阴谋），但融入独特创意。
- 确保修真体系逻辑一致，包含独特亮点。
- 避免过于通俗的剧情或角色，加入新颖转折（如复杂反派、不寻常的宝物）。
- 如需具体细节（如特定设定或主题），我可提供以进一步定制。
        """
        user_prompt = (
            f'为小说"{story.name}"创建详细的人物设定。\n\n'
            f"# 故事设定(人物未创建)\n{story.as_prompt()}\n\n"
            "经过认真的思考以及参考流行的网络小说，你根据小说构思创建数个还未创建的角色，且已为每个角色创建详细的背景设定，"
            "包括：性格特征、外貌描写、背景故事、动机目标、与其他角色的关系等等(描述中不要包含角色名字)。输出如下：\n"
        )

        # Generate character profiles using LLM
        prompts = (
            pyllm.ChatPrompts(
                output_format="json", output_schema=StoryCharacter.as_array_schema(), output_lang=self.config.novel_lang
            )
            .set_system(self.config.sys_prompt)
            .set_inquery(user_prompt)
        )
        llm_param = pyllm.create_param(self._llm_cli.setting, fallback_to_default=True)
        llm_param.streaming = False
        llm_param.timeout = 30

        answer = await self._llm_cli.async_completion(prompts, llm_param)
        characters_data = json.loads(answer)

        # Create and save StoryCharacter objects
        characters = []
        for char_d in characters_data["array"]:
            char_key = StoryCharacter.generate_key(char_d["code"], story.key)
            char_d.pop("story", None)
            character = StoryCharacter(story=self.config.story, key=char_key, **char_d)
            await character.asave()
            characters.append(character)

        return characters


# ==========   Characters Design (for a novel)   ==========


class StoryCharacterAgentConfig(NovelWritingConfig):
    """config for StoryCharacterAgent"""

    name: str = "character-agent"
    story: Story

    def get_characters(self) -> list[StoryCharacter]:
        """Get all characters for this story from the database.

        Returns:
            list[StoryCharacter]: List of all characters in the story

        Raises:
            ValueError: If story is not set in config
        """
        if not self.config.story:
            raise ValueError("Story must be set in config before getting characters")
        return StoryCharacter.get_all(self.config.story.id)


class StoryCharacterAgent(Agent):
    """设计小说角色的智能体。An agent to design the characters of a story for writing a novel"""

    config: StoryCharacterAgentConfig

    async def async_run(self, **kwargs: Any) -> list[StoryCharacter]:
        """Create detailed character profiles for the story's main characters.

        This method generates detailed descriptions and backgrounds for each character
        mentioned in the story's initial conception, including their:
        - Personality traits
        - Physical appearance
        - Background/origin story
        - Motivations and goals
        - Relationships with other characters

        Returns:
            list[StoryCharacter]: A list of StoryCharacter objects containing the
            detailed character profiles
        """
        story = self.config.story
        if not story:
            raise ValueError("Story must be conceived before creating characters")

        # Create prompt for existed characters
        char_prompt = "\n".join(
            [f"## {c.name}\n{c.as_prompt(includes=['code', 'bio'])}" async for c in story.storycharacter_set.all()]
        )
        user_prompt = (
            f'为小说"{story.name}"创建详细的人物设定。\n\n'
            f"# 故事设定(人物未创建)\n{story.as_prompt()}\n\n"
            f"# 已创建的人物(不要再创建)\n{char_prompt or '无'}\n\n"
            "经过认真的思考以及参考流行的网络小说，你根据小说构思创建数个还未创建的角色，且已为每个角色创建详细的背景设定，"
            "包括：性格特征、外貌描写、背景故事、动机目标、与其他角色的关系等等(描述中不要包含角色名字)。输出如下：\n"
        )

        # Generate character profiles using LLM
        prompts = (
            pyllm.ChatPrompts(
                output_format="json", output_schema=StoryCharacter.as_array_schema(), output_lang=self.config.novel_lang
            )
            .set_system(self.config.sys_prompt)
            .set_inquery(user_prompt)
        )
        llm_param = pyllm.create_param(self._llm_cli.setting, fallback_to_default=True)
        llm_param.streaming = False
        llm_param.timeout = 30

        answer = await self._llm_cli.async_completion(prompts, llm_param)
        characters_data = json.loads(answer)

        # Create and save StoryCharacter objects
        characters = []
        for char_d in characters_data["array"]:
            char_key = StoryCharacter.generate_key(char_d["code"], story.key)
            char_d.pop("story", None)
            character = StoryCharacter(story=self.config.story, key=char_key, **char_d)
            await character.asave()
            characters.append(character)

        return characters


# ==========   Chapter Writing (for a novel)   ==========


class StoryChapterAgentConfig(NovelWritingConfig):
    """config for StoryChapterAgent"""

    name: str = "chapter-agent"

    def get_chapter(self) -> StoryChapter:
        """Get the latest chapter of the story from the database.

        Returns:
            StoryChapter: The latest chapter of the story

        Raises:
            ValueError: If story is not set in config
        """
        if not self.config.story:
            raise ValueError("Story must be set in config before getting chapter")
        return StoryChapter.get_latest(self.config.story.id)


class StoryChapterAgent(Agent):
    """撰写小说章节的智能体。An agent to write chapters for writing a novel"""

    config: StoryChapterAgentConfig

    async def async_run(self, **kwargs: Any) -> list[StoryCharacter]:
        pass
