from pycommon.utils.confutils import get_bool_env, get_str_env

DEBUG = get_bool_env("DEBUG", False)
LANG = "zh"

LOG_COLOR_DEBUG = get_str_env("LOG_COLOR_DEBUG", "thin_white")
LOG_COLOR_INFO = get_str_env("LOG_COLOR_INFO", "white")
LOG_FORMATTER = get_str_env("colorful", "colorful")
LOG_LEVEL = get_str_env("LOG_LEVEL", "INFO" if DEBUG else "WARNING")
LOG_CONFIG_DICT = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "[%(levelname)s]%(asctime)s|%(name)s: %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        "colorful": {
            "format": "%(log_color)s[%(asctime)s] %(levelname).1s [%(threadName).10s][%(name)s:%(lineno)d] %(message)s",
            "()": "colorlog.ColoredFormatter",
            "reset": True,
            "log_colors": {
                "DEBUG": LOG_COLOR_DEBUG,
                "INFO": LOG_COLOR_INFO,
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "red,bg_white",
            },
            "secondary_log_colors": {},
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": LOG_FORMATTER,
        },
    },
    "root": {
        "handlers": ["console"],
        "level": LOG_LEVEL,
    },
    "loggers": {
        **{
            name: {"level": "INFO"}
            for name in [
                "django.db.backends",
            ]
        },
        **{
            name: {"level": "WARNING"}
            for name in ["httpx", "urllib3", "telegram", "httpcore", "asyncio", "daphne", "LiteLLM", "litellm"]
        },
    },
}
