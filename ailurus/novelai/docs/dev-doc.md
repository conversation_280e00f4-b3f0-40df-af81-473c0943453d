# NovelAI

`novelai` is come from "novel" + "AI". Its the code name of the project.
This project is used to generate novel via AI.

## Features Roadmap

- [ ] 生成 故事大纲
	- [ ] 设计伏笔
	- [ ] 设计跌宕起伏的情节
- [ ] 生成 故事类型、背景 和 主题
	- [ ] 支持的类型 [类型，例如：科幻、奇幻、悬疑、爱情、...]

		* 	  玄幻：以东方神话、仙侠、武道为背景，注重修炼、战斗和奇幻设定，如《斗破苍穹》。
		* 	  仙侠：聚焦修仙、道教文化，强调长生、飞升和仙魔争斗，如《凡人修仙传》。
		* 	  奇幻：融合西方奇幻元素，如魔法、龙族、异世界冒险，如《诡秘之主》。
		* 	  科幻：涉及未来科技、星际探索、人工智能等主题，如《三体》系列的网文风格作品。
		* 	  都市：以现代城市为背景，常包含职场、异能、爱情或重生元素，如《重生之都市修仙》。
		* 	  历史：以中国古代为背景，包含穿越、架空历史、权谋等，如《赘婿》。
		* 	  穿越：主角穿越到古代或其他时空，结合历史、仙侠等元素，如《庆余年》。
		* 	  重生：主角重生到过去或平行世界，改变命运，常見於都市、商战，如《重生完美时代》。
		* 	  悬疑：以推理、探案、灵异或心理博弈为主，如《盗墓笔记》。
		* 	  爱情：聚焦浪漫情感，包含现代、古代、校园等背景，如《何以笙箫默》。
		* 	  武侠：以江湖、武功、侠义为主题，传统风格如《天龙八部》或新武侠网文。
		* 	  游戏：以虚拟网游或电竞为背景，如《全职高手》。
		* 	  无限流：主角在多个副本世界冒险，融合科幻、恐怖等元素，如《无限恐怖》。
		* 	  灵异：以鬼怪、恐怖、神秘事件为主，如《我有一座恐怖屋》。
		* 	  种田：注重生活化、缓慢发展，常见于穿越或历史背景，如《农家小福女》。

	- [ ] 有哪些主流的主题 [主题，例如：复仇、救赎、寻找自我、对抗邪恶、...]
		* 		复仇：主角因家族覆灭、背叛或冤屈而踏上复仇之路，如《斗破苍穹》中萧炎的复仇成长。
		* 		救赎：主角通过努力救赎自己或他人，弥补过去遗憾，常見於重生文，如《重生之都市修仙》。
		* 		寻找自我：主角在冒险或修炼中探索自我价值与人生意义，如《诡秘之主》中克莱恩的身份追寻。
		* 		对抗邪恶：主角与邪恶势力、魔族或阴谋集团斗争，保卫正义，如《诛仙》中对抗魔教。
		* 		成长与蜕变：从平凡到强大的成长历程，克服困难实现蜕变，如《凡人修仙传》中韩立的奋斗。
		* 		爱情与守护：以爱情为核心，守护爱人或追求真爱，如《何以笙箫默》的深情守候。
		* 		权谋与争霸：围绕权力斗争、帝王霸业或家族兴衰展开，如《赘婿》中的商战与权谋。
		* 		逆袭：底层主角通过智慧或机缘逆袭，改变命运，如《斗罗大陆》中的唐三。
		* 		探索与冒险：在未知世界或副本中探险，揭开秘密，如《盗墓笔记》的探墓之旅。
		* 		家族复兴：主角肩负复兴家族或宗门使命，重建荣耀，如《武动乾坤》中林动的家族崛起。
		* 		自由与反抗：对抗命运束缚或强权压迫，追求自由，如《雪中悍刀行》的江湖抗争。
		* 		传承与责任：继承前辈遗志或承担拯救世界的责任，如《遮天》中的传承与宇宙使命。
		* 		友情与羁绊：强调伙伴间的信任与共同成长，如《全职高手》中战队的团队精神。
		* 		自我超越：通过修炼或磨砺突破自身极限，追求更高境界，如《大主宰》中的牧尘。
		* 		轮回与命运：探索命运安排或轮回因果，打破宿命，如《仙逆》中王林的逆天之路。
- [ ] 生成 主要角色 [角色描述，例如：年轻的魔法师、落魄的侦探、身世神秘的女孩]
	- [ ] 主角 背景 [背景描述，例如：充满魔法的古代王国、赛博朋克风格的未来都市、被战争摧毁的荒凉世界]
	- [ ] 主角 性格
- [ ] 角色性格和记忆的一致性 （主、配角色）
	- [ ] 角色档案
	- [ ] 角色记忆
- [ ] 情节连贯性
	- [ ] 剧情摘要
	- [ ] 章节总结
- [ ] 伏笔的设置和兑现
	- [ ] 大纲规划
	- [ ] 具体指示
	- [ ] 后期调整

## 设计

- [ ] 角色性格和记忆的一致性
	- [ ] **角色档案**：
	  为每个角色创建详细的档案，包括性格、背景、动机和重要特质。在生成每章时，将相关角色的档案纳入提示，确保他们的行为和对话与档案一致。例如，如果一个角色害怕高空，在涉及高空场景的章节中，提示中应明确提到这一特质。
	- [ ] **角色记忆**： 维护一个角色时间线或事件列表，记录每个角色经历的重要事件。在生成新章节时，将相关记忆纳入提示，确保角色根据过往经历做出决策。例如，如果角色之前遇见过某个嫌疑人，提示中应提到他们已知的信息。
- [ ] 情节连贯性
	- [ ] **剧情摘要**： 在每章生成后更新剧情摘要，记录到目前为止的关键事件和进展。在生成下一章时，将此摘要纳入提示，确保新章节与之前的情节逻辑连贯。例如，如果上一章中角色发现了线索，提示中应提到这一进展。
	- [ ] **章节总结**： 根据大纲为每个章节生成总结，描述该章的内容和在整体故事中的作用，帮助保持情节的连贯性。
- [ ] 伏笔的设置和兑现
	- [ ] **大纲规划**： 在生成大纲时，明确规划主要情节点和伏笔的位置，例如在早期章节暗示某个角色的秘密身份，晚期章节揭露。大纲应包括这些伏笔的设置和兑现时间。
	- [ ] **具体指示**： 在生成每章时，提示中加入具体指示，例如“在本章中，暗示角色A与角色B有私人关系，但不要明确说明”，确保伏笔自然融入故事。
	- [ ] **后期调整**： 生成完整草稿后，审查所有章节，确保伏笔设置合理且兑现有效。如有需要，可让AI编辑相关章节，添加或改进伏笔。

## 设计跌宕起伏的情节

要让AI LLM设计“跌宕起伏的情节”，你需要编写一个清晰、详细且富有引导性的prompt。以下是一些建议，帮助你构建有效的prompt：

1. **明确故事类型和主题：**

	- **指定类型：**
		- “请创作一个[类型，例如：科幻、奇幻、悬疑、爱情]小说的情节。”
	- **设定主题：**
		- “故事主题是[主题，例如：复仇、救赎、寻找自我、对抗邪恶]。”

2. **描述主要角色和背景：**

	- **主要角色：**
		- “主要角色是一个[角色描述，例如：年轻的魔法师、落魄的侦探、身世神秘的女孩]。”
	- **故事背景：**
		- “故事发生在一个[背景描述，例如：充满魔法的古代王国、赛博朋克风格的未来都市、被战争摧毁的荒凉世界]。”

3. **设定情节发展的关键要素：**

	- **核心冲突：**
		- “故事的核心冲突是[冲突描述，例如：主角与强大的敌人之间的对抗、主角内心的挣扎、主角必须解决的复杂谜题]。”
	- **情节转折点：**
		- “请设计至少三个情节转折点，让故事充满意外和悬念。”
		- “情节转折点要求：1，要和前面的事件有关系。2，要让主角陷入困境。3，要让读者感到意外。”
	- **高潮和结局：**
		- “设计一个高潮，让主角面临最大的挑战。”
		- “设计一个令人满意的结局，既能解决冲突，又能留下回味。”
	- **伏笔和线索：**
		- “在故事中设置一些伏笔和线索，为未来的情节发展做铺垫。”

4. **强调“跌宕起伏”的要素：**

	- **情感变化：**
		- “让主角的情感经历起伏，从希望到绝望，再到希望。”
	- **意外事件：**
		- “在故事中加入一些意外事件，打破平静，制造紧张感。”
	- **悬念和紧张感：**
		- “在关键时刻制造悬念，让读者迫不及待地想知道下一步会发生什么。”
	- **节奏控制：**
		- “控制故事的节奏，快慢结合，让读者时而紧张，时而放松。”

5. **示例prompt：**

	-
   “请创作一个发生在赛博朋克未来都市的悬疑小说情节。主要角色是一个落魄的侦探，他必须解决一起涉及人工智能的复杂谜题。故事的核心冲突是侦探与一个强大的科技公司之间的对抗。请设计至少三个情节转折点，让故事充满意外和悬念。让主角的情感经历起伏，从希望到绝望，再到希望。在故事中设置一些伏笔和线索，为未来的情节发展做铺垫。设计一个高潮，让主角面临最大的挑战，并设计一个令人满意的结局。”

**重要提示：**

- LLM的输出可能会有差异，可以多次尝试，并根据需要进行修改。
- 你可以逐步添加细节，让LLM逐步完善情节。
- 将LLM的输出作为灵感，进行自己的创作。

## Implementations

### Models

#### Object Models

##### `Novel` - 保存一部小说需要的所有部分

- `story`: 1:1, 小说的故事是什么（背景、概要 等）
	- `load_story`: method, 从数据库载入小说故事信息
	- `save_story`: method, 保存小说故事信息到数据库
	- `update_story`: method, 更新小说故事信息
- `outline`: 1:1, 小说的大纲是怎样的
	- `load_outline`: method, 从数据库载入小说大纲
	- `save_outline`: method, 保存小说大纲到数据库
	- `update_outline`: method, 更新小说大纲
- `characters`: 1:N, 小说的角色有
	- `load_characters(id_key_list:Optional[list[int|str]])`: method, 载入一个、多个、所有角色
	- `save_characters(id_key_list:Optional[list[int|str]])`: method, 保存一个、多个、所有角色
- `chapters`: 1:N, 小说的章节
	- `load_chapters(ids:Optional[list[int]])`: method, 载入指定或所有章节
	- `save_chapters(ids:Optional[list[int]])`: method, 保存章节
- `memory`: 1:1, 小说的记忆系统
	- `load_memory`: method, 载入记忆
	- `save_memory`: method, 保存记忆
	- `update_memory`: method, 更新记忆
	- `get_memory_prompt`: method, 获取记忆相关的 prompt

#### Database Models

##### `NovelModelBase`

- Methods:
	- `get_schema() -> dict`: 返回模型的schema供LLM Agent使用
	- `as_prompt() -> str`: 将模型转换为LLM prompt格式

##### `Story` 故事

- 代表一个完整的小说故事
- Fields:
	- `name`: CharField(255), 小说名字
	- `key`: CharField(255), unique=True, 小说唯一标识
	- `background`: TextField, 小说背景
	- `theme`: TextField, 小说主题
	- `plot`: TextField, 主要情节
	- `plot_characters`: JSONField, 概要中产生的主要角色列表，以`list[str]`形式存储
- Methods:
	- `generate_key(name: str) -> str`: 根据小说名生成唯一标识
		- 英文名: 将所有部分小写组合
		- 中文名: 将所有汉字转为拼音首字母组合
- Mixins: CreateTimeMixin, UpdateTimeMixin
- 继承: NovelModelBase

##### `StoryCharacter` 故事角色

- 代表小说中的一个角色
- Fields:
	- `story`: ForeignKey(Story), 角色所属的故事
	- `key`: CharField(50), unique=True, 角色唯一标识
	- `name`: CharField(255), 角色名字
	- `level`: CharField(20), 角色等级/重要性
	- `bio`: TextField, 角色背景介绍
	- `nicks`: JSONField, 角色的昵称列表
- Methods:
	- `load_memories()`: method, 载入角色的记忆
	- `save_memories()`: method, 保存角色的记忆
	- `update_memories()`: method, 更新角色的记忆
	- `generate_key(name: str, story_key: str) -> str`: 根据角色名和故事ID生成唯一标识
- Mixins: CreateTimeMixin, UpdateTimeMixin
- 继承: NovelModelBase

##### `StoryOutline` 故事大纲

- 代表小说的大纲结构
- Fields:
	- `story`: ForeignKey(Story), 大纲所属的小说
	- `toc`: JSONField, 章节目录，以字典列表形式存储
- Methods:
- Mixins: CreateTimeMixin, UpdateTimeMixin
- 继承: NovelModelBase
- Meta:
	- ordering: ["story", "id"]

##### `StoryChapter` 故事章节

- 代表小说的一个章节
- Fields:
	- `story`: ForeignKey(Story), 章节所属的故事
	- `type`: CharField, 章节类型
	- `order`: IntegerField, 章节在故事中的顺序
	- `title`: CharField, 章节标题
	- `content`: TextField, 章节内容
	- `character_ids`: JSONField, 本章出现的角色ID列表，按出场顺序排列
- Methods:
- Mixins: CreateTimeMixin, UpdateTimeMixin
- 继承: NovelModelBase

##### `StoryPlotSegment` 故事情节

- 代表小说中一段相对完整的故事情节，通常包含一系列事件、角色发展或叙事推进，强调叙事结构的一部分。例如，一章或几章内描述的某个故事片段（如主人公的冒险旅程或一场冲突的展开）。
- Fields:
	- `story`: ForeignKey(Story), 段落所属的小说
	- `order`: IntegerField, 段落在情节中的顺序
	- `content`: TextField, 段落内容
- Methods:
- Mixins: CreateTimeMixin, UpdateTimeMixin
- 继承: NovelModelBase

##### `StoryEvent` 故事事件

- 代表小说中的一个具体的事件或发生的事情，通常是单一的、离散的行动或情况（如一场战斗、一次对话）。它更聚焦于某个特定的“事件点”，而不一定涉及更广泛的叙事上下文。
- Fields:
	- `plot_segment`: ForeignKey(StoryPlotSegment), 事件所属的情节
	- `type`: CharField, 事件类型
	- `chapter_ids`: JSONField, 事件相关的章节
	- `character_ids`: JSONField, 事件中出现的角色ID列表
	- `content`: TextField, 事件内容
- Methods:
- Mixins: CreateTimeMixin, UpdateTimeMixin

##### `NovelMemory` 小说的记忆系统

- 代表小说的记忆系统，用于存储和管理生成过程中的上下文信息
- Fields:
	- `story`: ForeignKey(Story), 记忆所属的故事
	- `data`: JSONField, 记忆数据，以字典形式存储
- Methods: (待实现)

##### `CharacterMemory` 角色记忆

- 维护一个角色时间线或事件列表，记录每个角色经历的重要事件。在生成新章节时，将相关记忆纳入提示，确保角色根据过往经历做出决策。例如，如果角色之前遇见过某个嫌疑人，提示中应提到他们已知的信息。
- Fields:
	- `character`: ForeignKey(StoryCharacter), 记忆所属的角色
	- `context`: JSONField, 角色经历在此章节、此时间的事件记忆上下文，以字典形式存储 <人｜物｜事｜地点|...>:<内容概要>
- Methods:
- Mixins: CreateTimeMixin, UpdateTimeMixin
- 继承: NovelModelBase
-

### Agent

#### `NovelAgent`

#### `StoryAgent(NovelAgent)`

#### `CharacterAgent(NovelAgent)`

#### `OutlineAgent(NovelAgent)`

#### `ChapterAgent(NovelAgent)`

#### `NovelMemory`

### 生成角色

- 在构思设计的时候，会有几个主要角色，这时候应该存在 `Story` 里
- 生成概要时

## 3rd references

### AI chat

- 如何用LLM写长小说  https://grok.com/chat/ff5e79af-4d0b-4879-a752-77d6383ab0ea
- LLM写小说中的一些细节和翻译  https://gemini.google.com/app/01c880f1c6eacb18

### libs

- AI Writer  https://github.com/BlinkDL/AI-Writer

### papers
