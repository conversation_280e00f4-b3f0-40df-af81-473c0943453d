from django.contrib import admin
from django.utils.safestring import mark_safe

from djcommon.model import CreateTimeMixin
from novelai.engine.consts import WEB_NOVEL_GENRE_DICT
from novelai.models.chapter import StoryChapter
from novelai.models.character import <PERSON><PERSON>haracter
from novelai.models.outline import StoryOutline
from novelai.models.story import Story
from pycommon.utils.datetimeutils import format_datetime


@admin.display(description="created at")
def created_at_disp(obj: CreateTimeMixin) -> str:
    return mark_safe(format_datetime(obj.created_at))


class StoryTypeFilter(admin.SimpleListFilter):
    title = "Story Type"  # 过滤标题"
    parameter_name = "types"  # 过滤器使用的过滤字段

    def lookups(self, request, model_admin):
        """针对字段值设置过滤器的显示效果"""
        return ((k, k) for k in WEB_NOVEL_GENRE_DICT.keys())

    def queryset(self, request, queryset):
        """定义过滤器的过滤动作"""
        v = self.value()
        if not v:
            return queryset.all()
        return queryset.filter(types__contains=v).all()


class StoryAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "name_disp",
        "author",
        "types",
        "words_plan",
        "themes_disp",
        "characters_disp",
        created_at_disp,
    ]
    list_display_links = ["id", "name_disp"]
    search_fields = ["id", "name", "key", "author", "background", "plot", "plot_characters"]
    list_filter = ["lang", "author", StoryTypeFilter]
    # date_hierarchy = "created_at"
    # raw_id_fields = ["user", "chat", "chat_turn"]
    # readonly_fields = ["role", "created_at"]

    @admin.display(description="name")
    def name_disp(self, obj: Story) -> str:
        s = f"{obj.name}<br/>{obj.key}"
        return mark_safe(s)

    @admin.display(description="name")
    def themes_disp(self, obj: Story) -> str:
        s = ", ".join(obj.themes.get("major") or []) + " | " + ", ".join(obj.themes.get("minor") or [])
        return mark_safe(s)

    @admin.display(description="characters(code)")
    def characters_disp(self, obj: Story) -> str:
        s = ", ".join([k for k in obj.plot_characters.keys()])
        return mark_safe(s)

    # @admin.display(description="text")
    # def text_disp(self, obj: ChatMsg) -> str:
    #     message_source = obj.chat_turn.message_source if obj.role == "user" else ""
    #     message_source = (
    #         f"<br /><span style='color: goldenrod;'>{message_source}</span>" if message_source == "example" else ""
    #     )
    #     return mark_safe(f"{ellipse(obj.text, 100)} {message_source}")
    #
    #
    # @admin.display(description="related")
    # def related_disp(self, obj: ChatMsg) -> str:
    #     return mark_safe(f"chat: {obj.chat.id} | turn: {obj.chat_turn.id}<br/>{obj.user}")


# Register your models here.
admin.site.register(Story, StoryAdmin)
admin.site.register(StoryOutline)
admin.site.register(StoryCharacter)
admin.site.register(StoryChapter)
