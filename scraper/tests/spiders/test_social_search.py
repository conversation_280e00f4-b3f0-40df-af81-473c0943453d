import logging

import pytest

from scraper.spiders.instagram import (
    InstagramSearchSpider,
    InstagramSearchSpiderConfig,
)
from scraper.spiders.tiktok import T<PERSON><PERSON><PERSON>earch<PERSON>pider, TiktokSearchSpiderConfig
from scraper.spiders.youtube import (
    YoutubeSearchSpider,
    YoutubeSearchSpiderConfig,
)


@pytest.fixture(autouse=True)
def disable_logging() -> None:
    # Remove all handlers from all loggers
    loggers = [logging.getLogger()] + list(logging.Logger.manager.loggerDict.values())
    for logger in loggers:
        if hasattr(logger, "handlers"):
            logger.handlers.clear()

    # Set null handler to root logger to prevent "No handlers found" warning
    logging.getLogger().addHandler(logging.NullHandler())

    # Set level to CRITICAL
    logging.getLogger().setLevel(logging.CRITICAL)


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
@pytest.mark.skip(reason="we are not using instagram search spider right now")
async def test_instagram_search_spider() -> None:
    config = InstagramSearchSpiderConfig(
        term="makeup artist usa",
        name="instagram_search",
        browser_type="chromium",
        start_url="https://www.instagram.com",
        is_headless=True,
        pages=0,
        items=5,
        with_profile=True,
        use_lambda_avatar_processor=False,
    )

    spider = InstagramSearchSpider(config=config)
    results = []

    async for profile in spider.async_iterate():
        results.append(profile)
        if len(results) >= 5:
            break

    del spider

    assert len(results) > 0
    for profile in results:
        assert profile.handle
        assert profile.followers > 0
        assert profile.url.startswith("https://www.instagram.com/")


@pytest.mark.asyncio
async def test_youtube_search_spider() -> None:
    config = YoutubeSearchSpiderConfig(
        term="ai",
        name="youtube_search",
        browser_type="chromium",
        start_url="https://www.youtube.com",
        is_headless=True,
        pages=0,
        items=5,
        with_profile=True,
        use_lambda_avatar_processor=False,
    )

    spider = YoutubeSearchSpider(config=config)
    results = []

    async for profile in spider.async_iterate():
        results.append(profile)
        if len(results) >= 20:
            break

    del spider

    has_keyword_count = 0
    for profile in results:
        assert profile.handle and len(profile.handle) > 0
        assert not profile.handle.isspace()
        assert profile.followers > 10
        assert profile.url.startswith("https://www.youtube.com/@")
        assert any(len(p.full_name) > 0 for p in results)
        assert any(len(p.biography) > 0 for p in results)
        assert profile.relevant_post
        assert profile.relevant_post.url.startswith("https://www.youtube.com/watch?")
        if "ai" in profile.relevant_post.caption.lower():
            has_keyword_count += 1

    assert has_keyword_count >= 2


@pytest.mark.asyncio
async def test_tiktok_search_spider() -> None:
    config = TiktokSearchSpiderConfig(
        term="#dance",
        name="tiktok_search",
        browser_type="chromium",
        start_url="https://www.tiktok.com",
        is_headless=True,
        pages=1,
        items=20,
        use_lambda_avatar_processor=False,
    )

    spider = TiktokSearchSpider(config=config)
    results = []

    async for profile in spider.async_iterate():
        results.append(profile)
        if len(results) >= 20:
            break

    del spider

    assert len(results) > 5

    has_keyword_count = 0
    for profile in results:
        assert profile.handle and len(profile.handle) > 0
        assert not profile.handle.isspace()
        assert profile.followers > 10
        assert profile.url.startswith("https://www.tiktok.com/@")
        assert any(len(p.full_name) > 0 for p in results)
        assert any(len(p.biography) > 0 for p in results)
        assert profile.relevant_post
        assert profile.relevant_post.url.startswith("https://www.tiktok.com/@")
        if "dance" in profile.relevant_post.caption.lower():
            has_keyword_count += 1

    assert has_keyword_count >= 2
