import pytest

from scraper.spiders.fetch_instagram_profile import async_fetch_profile

EXPECTED_PROFILES = [
    {
        "username": "patrickta",
        "expected": {
            "handle": "patrickta",
            "full_name": lambda x: isinstance(x, str) and "patrick" in x.lower(),
            "platform": "instagram",
            "followers": lambda x: x > 3750000 and x < 5000000,
            "following": lambda x: x > 100 and x < 5000,
            "posts_count": lambda x: x > 20,
            "biography": lambda x: len(x) > 50 and "patrick" in x,
            "home_page": "https://www.instagram.com/patrickta",
            "url": "https://www.instagram.com/patrickta",
            "posts": lambda x: isinstance(x, list) and len(x) > 0,
        },
    },
    {
        "username": "gina.makeup",
        "expected": {
            "handle": "gina.makeup",
            "full_name": lambda x: isinstance(x, str) and "gina" in x.lower(),
            "platform": "instagram",
            "followers": lambda x: x > 10_000 and x < 1_000_000,
            "following": lambda x: x > 100 and x < 10_000,
            "posts_count": lambda x: x > 50 and x < 1000,
            "biography": lambda x: isinstance(x, str) and "gina" in x.lower(),
            "home_page": "https://www.instagram.com/gina.makeup",
            "url": "https://www.instagram.com/gina.makeup",
            "posts": lambda x: isinstance(x, list) and len(x) > 0,
        },
    },
]


@pytest.mark.asyncio
async def test_fetch_instagram_profiles() -> None:
    for test_case in EXPECTED_PROFILES:
        username = test_case["username"]
        expected = test_case["expected"]

        profile = await async_fetch_profile(username, use_cache=False)

        assert profile is not None, f"Profile for {username} should not be None"

        for key, expected_value in expected.items():  # type: ignore
            assert key in profile, f"Key '{key}' missing in profile for {username}"

            actual_value = profile[key]

            if callable(expected_value):
                # If expected_value is a function, use it as a validator
                assert expected_value(
                    actual_value
                ), f"Validation failed for {key} in {username}. Got: {actual_value}"
            else:
                # Direct comparison for static values
                assert (
                    actual_value == expected_value
                ), f"Mismatch for {key} in {username}. Expected: {expected_value}, Got: {actual_value}"

        # Check that posts have required fields
        if "posts" in profile:
            for post in profile["posts"]:
                assert "id" in post
                assert "pk" in post
                assert "url" in post
                assert "caption" in post
                assert "taken_at" in post
                assert "like_count" in post
                assert "comment_count" in post
                assert isinstance(post["like_count"], int)
                assert isinstance(post["comment_count"], int)
