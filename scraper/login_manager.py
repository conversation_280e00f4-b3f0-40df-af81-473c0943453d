import json
import os
import threading
import time
from abc import ABC, abstractmethod
from datetime import datetime
from logging import getLogger
from pathlib import Path
from typing import ClassVar, Optional, Any, Self

from common.utils.datetimeutils import LOCAL_TZ
from services import s3

logger = getLogger(__name__)


class SyncManager(ABC):
    __s3_bucket: ClassVar[str] = "onwish-prepared-data"
    __daemon_interval: ClassVar[int] = 3600  # seconds
    __instance: ClassVar[Optional[Self]] = None

    @classmethod
    def get_instance(cls) -> "SyncManager":
        if cls.__instance is None:
            cls.__instance = cls()
        return cls.__instance

    @classmethod
    @abstractmethod
    def get_manager_name(cls) -> str:
        """naming the data. will be used as file & s3 path prefix as well."""

    @classmethod
    def get_local_path(cls) -> str:
        """local path to store the syned data"""
        return str(Path(__file__).parent / "sessions" / cls.get_manager_name())

    @classmethod
    def get_s3_prefix(cls) -> str:
        """s3 path to store the syned data"""
        return f"autogather-sessions/{cls.get_manager_name()}"

    def sync(self) -> None:
        """sync data with S3. (download and remove only, no upload)"""

        logger.info(f"syncing {self.get_manager_name()}")

        # List objects in the S3 bucket with the given prefix
        s3_objects = s3.list_objects(self.__s3_bucket, self.get_s3_prefix())

        # Create a set of filenames in S3
        s3_filenames = set()

        for s3_object in s3_objects:
            s3_key: str = s3_object["Key"]
            name: str = s3_key.split("/")[-1]  # Get the filename from the S3 key
            if not name.strip():
                continue
            s3_filenames.add(name)
            local_file = Path(self.get_local_path()) / name

            # Ensure the directory exists
            local_file.parent.mkdir(parents=True, exist_ok=True)

            # Check if LastModified is already in s3_object
            s3_updated_at = s3_object.get("LastModified") or datetime.min.replace(tzinfo=LOCAL_TZ)

            # Check if local file exists and get its modification time
            if local_file.exists():
                local_updated_at = datetime.fromtimestamp(local_file.stat().st_mtime, tz=LOCAL_TZ)
            else:
                local_updated_at = datetime.min.replace(tzinfo=LOCAL_TZ)

            # If S3 file is newer, download it
            if s3_updated_at > local_updated_at:
                json_str = s3.get_str_object(self.__s3_bucket, s3_key)

                # Write the content to the local file
                with local_file.open("w") as f:
                    f.write(json_str)

                # Set the modification time of the local file to match S3
                local_file.touch(exist_ok=True)
                os.utime(local_file, (s3_updated_at.timestamp(), s3_updated_at.timestamp()))

                logger.info(f"synced {name} from S3")
            else:
                logger.debug(f"ignore syncing {name} due to it's up to date")

        # Remove local files that don't exist in S3
        local_files = set(os.listdir(self.get_local_path()))
        files_to_remove = local_files - s3_filenames
        for file_to_remove in files_to_remove:
            file_path = Path(self.get_local_path()) / file_to_remove
            os.remove(file_path)
            logger.info(f"removed local file {file_to_remove} as it no longer exists in S3")

        logger.info(f"finished syncing {self.get_manager_name()}")

    @classmethod
    def load(
        cls,
        name: str,
        suffix: str = "",
        ext: str = "json",
        *args: Any,
        **kwargs: Any,
    ) -> Any:
        """load local data by given name & suffix.

        e.g.
        Says we have a set of cookies for 2 persons:
        - 1.instagram_cookie.json
        - 1.my_cookie.json
        - 2.instagram_cookie.json
        - 2.my_cookie.json

        "1" and "2" are the set prefixes. They are the set of all these 2 person's data file.
        "instagram" and "my" are the names of the 2 data files.
        "_cookie" is the suffix for all files.
        """
        local_path = Path(cls.get_local_path())
        suffix = suffix

        # load specified data file
        fname = f"{name}-{suffix}.{ext}"
        fpath = local_path / fname
        if fpath.exists():
            with fpath.open("r") as f:
                data = json.load(f)
            if isinstance(data, list):
                data = [cls.convert_item(d) for d in data]
                data = [d for d in data if d]
            else:
                data = cls.convert_item(data)
            return data
        else:
            logger.debug(f"{cls.get_manager_name()} file {fpath} not found")
            return None

    @classmethod
    def convert_item(cls, item: Any) -> Any:
        """convert given item (commonly item is loaded data of a file)"""
        return item

    @classmethod
    def run_as_bg_daemon(cls, stop_event: threading.Event) -> None:
        """run current sync manager as a daemon"""
        logger.warning(f"{cls.get_manager_name()} manager started.")

        instance = cls.get_instance()
        while not stop_event.is_set():
            instance.sync()
            time.sleep(instance.__daemon_interval)
        logger.warning(f"{cls.get_manager_name()} manager quited.")


class CookieManager(SyncManager):
    @classmethod
    def get_manager_name(cls) -> str:
        """naming the data. will be used as file & s3 path prefix as well."""
        return "cookies"

    @classmethod
    def load(
        cls,
        name: str,
        suffix: str = "cookie",
        ext: str = "json",
        *args: Any,
        **kwargs: Any,
    ) -> Any:
        # changed default suffix
        return super().load(name, suffix, ext, *args, **kwargs)

    @classmethod
    def convert_item(cls, item: dict) -> dict:
        c = item
        if len(c) == 1 and "comments" in c:
            return {}
        if c["sameSite"] == "no_restriction":
            c["sameSite"] = "Strict"
        elif c["sameSite"] == "lax":
            c["sameSite"] = "Lax"
        else:
            c["sameSite"] = "None"
        return c


class HeaderManager(SyncManager):
    @classmethod
    def get_manager_name(cls) -> str:
        """naming the data. will be used as file & s3 path prefix as well."""
        return "headers"

    @classmethod
    def load(
        cls,
        name: str = "",
        suffix: str = "header",
        ext: str = "json",
        *args: Any,
        **kwargs: Any,
    ) -> Any:
        # changed default suffix
        return super().load(name, suffix, ext, *args, **kwargs)


class SessionManager:
    """manage multi sets of session datas (cookies, headers, etc). Rotate when failed.

    - manager name (mgr_name): it's name of a SyncManager which represents a kind of session type such as "cookies, headers, etc."
    - session name: it's name of a set of different session datas. e.g.  "instagram" cookies, headers; "mine" cookies, headers; "Jacks"...
    - set index: we will have multiple set of session datas. e.g. "Jack's" cookies & headers; "Mike's" cookies & headers.
    - rotate, next and circled
            For convenience syncing, we do NOT name the filename with "Jack's" or 'Mike's", we name them as "1" or "2" (or "a", "b'). so "1" and "2" are
            the set index. We rotate between the indexes from one to next.
            e.g.
            current set is "1" (1 means Jack's), we load: 1) Jack's cookies 2) Jack's headers 3) other session data of Jack
            then we "rotate" to "next",
            current set is "2" (2 means Mike's), we load: 1) Mike's cookies 2) Mike's headers 3) other session data of Mike
            then we "next" again, reach the end (all sets are "circled" for this session name).
            we need to "reset" the session

    - set boundary: sometimes the session data are not aligned.
            e.g. Jack has "1.jack-A-cookie.json', "2.jack-B-cookie.json", "1.jack-A-header.josn", which means Jack has lack of set 2 for "B-header".
            So after reload from disk, we have only "1" set for it. we do NOT have "2" set.

    """

    __indexes: dict[str, int] = dict()
    __set_lists: dict[str, list] = dict()
    __circle_counts: dict[str, int] = dict()
    __data: dict = dict()
    __sync_mgrs: dict[str, type[SyncManager]] = {
        CookieManager.get_manager_name(): CookieManager,
        HeaderManager.get_manager_name(): HeaderManager,
    }

    __instance: ClassVar[Optional[Self]] = None

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.reset_all()

    @classmethod
    def get_instance(cls) -> "SessionManager":
        if cls.__instance is None:
            cls.__instance = cls()
        return cls.__instance

    def current_set(self, session_name: str) -> str:
        """get current set index for given session name

        :param session_name: session name such as "instagram", "mine", "for_test" and etc.
        :return:
        """
        assert session_name
        assert session_name in self.__set_lists
        return self.__set_lists[session_name][self.__indexes[session_name]]

    def current_set_data(self, session_name: str) -> Any:
        """get data of current set by given session name. load it from dist if not loaded

        :param session_name: session name such as "instagram", "mine", "for_test" and etc.
        :return:
        """
        assert session_name
        if session_name not in self.__indexes:
            return None

        if self.__data is None:
            self.__data = dict()
        f_prefix = f"{self.__set_lists[session_name][self.__indexes[session_name]]}.{session_name}"
        for mgr in [CookieManager, HeaderManager]:
            session_sets = self.__data.get(session_name)
            if session_sets is None:
                session_sets = dict()
            if mgr.get_manager_name() not in session_sets:
                session_sets[mgr.get_manager_name()] = mgr.load(f_prefix)
            self.__data[session_name] = session_sets
        return self.__data.get(session_name)

    def next(self, session_name: str) -> str:
        """rotate to next set by given session name

        :param session_name: session name such as "instagram", "mine", "for_test" and etc.
        :return:
        """
        assert session_name

        if session_name not in self.__indexes:
            logger.warning(f"session {session_name} was not loaded.")
            return ""

        if self.circled(session_name):
            logger.warning(f"session {session_name} circled to the end. Use reset() to restart.")
            return ""

        self.__data[session_name] = None
        self.__indexes[session_name] = (
            1
            if self.__indexes[session_name] == len(self.__set_lists[session_name]) - 1
            else self.__indexes[session_name] + 1
        )
        self.__circle_counts[session_name] -= 1
        return self.__set_lists[session_name][self.__indexes[session_name]]

    def circled(self, session_name: str) -> bool:
        """whether a given session were finished a full rotation

        :param session_name: session name such as "instagram", "mine", "for_test" and etc.
        :return:
        """
        assert session_name
        assert session_name in self.__circle_counts
        return self.__circle_counts[session_name] <= 0

    def reset(self, session_name: str) -> None:
        """reset a session's set to first index

        :param session_name: session name such as "instagram", "mine", "for_test" and etc.
        :return:
        """
        assert session_name
        assert session_name in self.__indexes

        self.__indexes[session_name] = 0
        self.__circle_counts[session_name] = len(self.__set_lists[session_name]) - 1
        self.__data[session_name] = None

    def reset_all(self) -> None:
        """reload all sessions sets and reset all sessions' sets to first indexes"""
        # try refreshing first. if failed, will not clear current data.
        set_lists = self.refresh_local_sets()

        self.__set_lists.clear()
        self.__set_lists = set_lists
        self.__indexes.clear()
        self.__circle_counts.clear()
        for session_name, sets in set_lists.items():
            self.__indexes[session_name] = 0
            self.__circle_counts[session_name] = len(sets) - 1
            self.__data[session_name] = None

    def refresh_local_sets(self) -> dict[str, list]:
        """read from local data. list all sets across session name and type."""
        sets: dict[str, dict[str, set]] = dict()
        for mgr_name, mgr in self.__sync_mgrs.items():
            local_path = Path(mgr.get_local_path())
            if not (local_path.exists() and local_path.is_dir()):
                logger.warning(f"session folder {local_path} not exists or is dir.")
                continue
            for file in local_path.iterdir():
                if file.is_file():
                    parts = file.name.split(".")
                    if len(parts) == 3:
                        set_index, session_name_with_suffix = parts[0:2]
                        session_name = session_name_with_suffix.split("-")[0]

                        session_sets: dict[str, set] = sets.get(session_name) or dict()
                        mgr_sets: set = session_sets.get(mgr_name) or set()
                        mgr_sets.add(set_index)
                        session_sets[mgr_name] = mgr_sets
                        sets[session_name] = session_sets

        set_lists: dict[str, list] = dict()
        for session_name, session_sets in sets.items():
            intersection: set = set()
            for mgr_name, mgr_sets in session_sets.items():
                # get intersection of index boundary
                intersection = intersection & mgr_sets if intersection else mgr_sets
            set_lists[session_name] = list(intersection)
        return set_lists


if __name__ == "__main__":
    session_name = "instagram"
    mgr = SessionManager.get_instance()
    data = mgr.current_set_data(session_name)
    cookies = data[CookieManager.get_manager_name()]
    print(f"{mgr.current_set(session_name)} - {cookies}")
    print("rotate ...")
    time.sleep(2)

    mgr.next(session_name)
    data = mgr.current_set_data(session_name)
    cookies = data[CookieManager.get_manager_name()]
    print(cookies)
