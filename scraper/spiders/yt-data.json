{
	"responseContext": {
		"serviceTrackingParams": [
			{
				"service": "GUIDED_HELP",
				"params": [
					{
						"key": "context",
						"value": "yt_web_search"
					},
					{
						"key": "logged_in",
						"value": "0"
					}
				]
			},
			{
				"service": "GFEEDBACK",
				"params": [
					{
						"key": "has_unlimited_entitlement",
						"value": "False"
					},
					{
						"key": "has_premium_lite_entitlement",
						"value": "False"
					},
					{
						"key": "logged_in",
						"value": "0"
					},
					{
						"key": "e",
						"value": "9405995,23804281,23986030,24004644,24077241,24166867,24181174,24241378,24299873,24439361,24453989,24466622,24499534,24548629,24566687,24619146,24699899,39325854,39326986,51009781,51010235,51017346,51020570,51025415,51030101,51037342,51037349,51043774,51050361,51053689,51057842,51057857,51063643,51072748,51091058,51095478,51098299,51105628,51111738,51115184,51124104,51141472,51144925,51145218,51151423,51152050,51153490,51157411,51160545,51169118,51176421,51176511,51178314,51178329,51178344,51178351,51178982,51183910,51184990,51194137,51199253,51204329,51208678,51217504,51222382,51222973,51226860,51227037,51227776,51228850,51230478,51236019,51237842,51239093,51241028,51242448,51248255,51248734,51255676,51255743,51256074,51256084,51263449,51272458,51274583,51275785,51276343,51276557,51276565,51281227,51282792,51285052,51285717,51287196,51289920,51289931,51289938,51289956,51289963,51289972,51292055,51294322,51296439,51298020,51299710,51299724,51299981,51300001,51300014,51300176,51300241,51302680,51303432,51303667,51303669,51303789,51304155,51305839,51306259,51307301,51308710,51310742,51311029,51311040,51311505,51313109,51313765,51313767,51313802,51314158,51315044,51315912,51315921,51315928,51315931,51315938,51315949,51315954,51315963,51315968,51315979,51318845,51320245,51322669,51326208,51326932,51327146,51327169,51327186,51327613,51327615,51327636,51330194,51331485,51331502,51331520,51331535,51331538,51331545,51331552,51331561,51332896,51333541,51333878,51334535,51335365,51335392,51335594,51335928,51336633,51337186,51337349,51338524,51340662,51341214,51341226,51341730,51341974,51342615,51342857,51343368,51343796,51345295,51345629,51346352,51346801,51346806,51346827,51346842,51346857,51346866,51346891,51346902,51347325,51347575,51348208,51348482,51348672,51348754,51349439,51349880,51351446,51352297,51352762,51353231,51353361,51353393,51354114,51354507,51354569,51355266,51355271,51355291,51355305,51355312,51355339,51355344,51355417,51355679,51355802,51355912,51357477,51358764,51359177,51360104,51360121,51360130,51360208,51360215,51361727,51361830,51362071,51362455,51362643,51362674,51362857,51362879,51363729,51363732,51363741,51363750,51363761,51363768,51365460,51365716,51365751,51365987,51366214,51366246,51366423,51366620,51366864,51367318,51367487,51367600,51367664,51367993,51368933,51369559,51370738,51370986,51370997,51371010,51371044,51371294,51371522,51372663,51372698,51374439,51375074,51375168,51375719,51375867,51376651"
					},
					{
						"key": "visitor_data",
						"value": "CgstVFd2SFMyLTNoQSi5js-7BjIKCgJVUxIEGgAgTg%3D%3D"
					}
				]
			},
			{
				"service": "CSI",
				"params": [
					{
						"key": "yt_ad",
						"value": "0"
					},
					{
						"key": "c",
						"value": "WEB"
					},
					{
						"key": "cver",
						"value": "2.20241219.01.01"
					},
					{
						"key": "yt_li",
						"value": "0"
					},
					{
						"key": "GetSearch_rid",
						"value": "0xbfe93ed14e80e53e"
					}
				]
			},
			{
				"service": "ECATCHER",
				"params": [
					{
						"key": "client.version",
						"value": "2.20241219"
					},
					{
						"key": "client.name",
						"value": "WEB"
					}
				]
			}
		],
		"mainAppWebResponseContext": {
			"loggedOut": true,
			"trackingParam": "kVCfmPxhojoROLLGSRSfB7g7QWCPXPXfCUM4m_e79OSMoAwMb4lsT7Co8vtfX6AgzQhvIDGfU5EMmfDHnADszanYlrYkTcMAcV9Mb24grzRQLuMIVLBwOcC0g9TnwslLKPQShS"
		},
		"webResponseContextExtensionData": {
			"hasDecorated": true
		}
	},
	"estimatedResults": "73914946",
	"trackingParams": "CAAQvGkiEwiVionD5tGKAxUIA60GHT2pFEqCAR0INBC3qQsYASITCOiD0ZXm0YoDFRM-rQYd94wHHw==",
	"header": {
		"searchHeaderRenderer": {
			"searchFilterButton": {
				"buttonRenderer": {
					"style": "STYLE_TEXT",
					"size": "SIZE_DEFAULT",
					"isDisabled": false,
					"text": {
						"runs": [
							{
								"text": "Filters"
							}
						]
					},
					"icon": {
						"iconType": "TUNE"
					},
					"tooltip": "Search filters",
					"trackingParams": "CKQBEPBbIhMIlYqJw-bRigMVCAOtBh09qRRK",
					"accessibilityData": {
						"accessibilityData": {
							"label": "Search filters"
						}
					},
					"command": {
						"clickTrackingParams": "CKQBEPBbIhMIlYqJw-bRigMVCAOtBh09qRRK",
						"openPopupAction": {
							"popup": {
								"searchFilterOptionsDialogRenderer": {
									"title": {
										"runs": [
											{
												"text": "Search filters"
											}
										]
									},
									"groups": [
										{
											"searchFilterGroupRenderer": {
												"title": {
													"simpleText": "Upload date"
												},
												"filters": [
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Last hour"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CMQBEJN1GAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQIARAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQIARAB"
																}
															},
															"tooltip": "Search for Last hour",
															"trackingParams": "CMQBEJN1GAAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Today"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CMMBEJN1GAEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQIAhAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQIAhAB"
																}
															},
															"tooltip": "Search for Today",
															"trackingParams": "CMMBEJN1GAEiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "This week"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CMIBEJN1GAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQIAxAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQIAxAB"
																}
															},
															"tooltip": "Search for This week",
															"trackingParams": "CMIBEJN1GAIiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "This month"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CMEBEJN1GAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQIBBAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQIBBAB"
																}
															},
															"tooltip": "Search for This month",
															"trackingParams": "CMEBEJN1GAMiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "This year"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CMABEJN1GAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQIBRAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQIBRAB"
																}
															},
															"tooltip": "Search for This year",
															"trackingParams": "CMABEJN1GAQiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													}
												],
												"trackingParams": "CL8BEJJ1GAAiEwiVionD5tGKAxUIA60GHT2pFEo="
											}
										},
										{
											"searchFilterGroupRenderer": {
												"title": {
													"simpleText": "Type"
												},
												"filters": [
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Video"
															},
															"status": "FILTER_STATUS_SELECTED",
															"navigationEndpoint": {
																"clickTrackingParams": "CL4BEJN1GAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk"
																}
															},
															"tooltip": "Remove Video filter",
															"trackingParams": "CL4BEJN1GAAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Channel"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CL0BEJN1GAEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgIQAg%253D%253D",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgIQAg%3D%3D"
																}
															},
															"tooltip": "Search for Channel",
															"trackingParams": "CL0BEJN1GAEiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Playlist"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CLwBEJN1GAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgIQAw%253D%253D",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgIQAw%3D%3D"
																}
															},
															"tooltip": "Search for Playlist",
															"trackingParams": "CLwBEJN1GAIiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Movie"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CLsBEJN1GAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgIQBA%253D%253D",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgIQBA%3D%3D"
																}
															},
															"tooltip": "Search for Movie",
															"trackingParams": "CLsBEJN1GAMiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													}
												],
												"trackingParams": "CLoBEJJ1GAEiEwiVionD5tGKAxUIA60GHT2pFEo="
											}
										},
										{
											"searchFilterGroupRenderer": {
												"title": {
													"simpleText": "Duration"
												},
												"filters": [
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Under 4 minutes"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CLkBEJN1GAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQARgB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQARgB"
																}
															},
															"tooltip": "Search for Under 4 minutes",
															"trackingParams": "CLkBEJN1GAAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "4 - 20 minutes"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CLgBEJN1GAEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQARgD",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQARgD"
																}
															},
															"tooltip": "Search for 4 - 20 minutes",
															"trackingParams": "CLgBEJN1GAEiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Over 20 minutes"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CLcBEJN1GAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQARgC",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQARgC"
																}
															},
															"tooltip": "Search for Over 20 minutes",
															"trackingParams": "CLcBEJN1GAIiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													}
												],
												"trackingParams": "CLYBEJJ1GAIiEwiVionD5tGKAxUIA60GHT2pFEo="
											}
										},
										{
											"searchFilterGroupRenderer": {
												"title": {
													"simpleText": "Features"
												},
												"filters": [
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Live"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CLUBEJN1GAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQAUAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQAUAB"
																}
															},
															"tooltip": "Search for Live",
															"trackingParams": "CLUBEJN1GAAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "4K"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CLQBEJN1GAEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQAXAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQAXAB"
																}
															},
															"tooltip": "Search for 4K",
															"trackingParams": "CLQBEJN1GAEiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "HD"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CLMBEJN1GAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQASAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQASAB"
																}
															},
															"tooltip": "Search for HD",
															"trackingParams": "CLMBEJN1GAIiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Subtitles/CC"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CLIBEJN1GAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQASgB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQASgB"
																}
															},
															"tooltip": "Search for Subtitles/CC",
															"trackingParams": "CLIBEJN1GAMiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Creative Commons"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CLEBEJN1GAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQATAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQATAB"
																}
															},
															"tooltip": "Search for Creative Commons",
															"trackingParams": "CLEBEJN1GAQiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "360\\u00b0"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CLABEJN1GAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQAXgB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQAXgB"
																}
															},
															"tooltip": "Search for 360\\u00b0",
															"trackingParams": "CLABEJN1GAUiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "VR180"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CK8BEJN1GAYiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgUQAdABAQ%253D%253D",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgUQAdABAQ%3D%3D"
																}
															},
															"tooltip": "Search for VR180",
															"trackingParams": "CK8BEJN1GAYiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "3D"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CK4BEJN1GAciEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQATgB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQATgB"
																}
															},
															"tooltip": "Search for 3D",
															"trackingParams": "CK4BEJN1GAciEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "HDR"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CK0BEJN1GAgiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgUQAcgBAQ%253D%253D",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgUQAcgBAQ%3D%3D"
																}
															},
															"tooltip": "Search for HDR",
															"trackingParams": "CK0BEJN1GAgiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Location"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CKwBEJN1GAkiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgUQAbgBAQ%253D%253D",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgUQAbgBAQ%3D%3D"
																}
															},
															"tooltip": "Search for Location",
															"trackingParams": "CKwBEJN1GAkiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Purchased"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CKsBEJN1GAoiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=EgQQAUgB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "EgQQAUgB"
																}
															},
															"tooltip": "Search for Purchased",
															"trackingParams": "CKsBEJN1GAoiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													}
												],
												"trackingParams": "CKoBEJJ1GAMiEwiVionD5tGKAxUIA60GHT2pFEo="
											}
										},
										{
											"searchFilterGroupRenderer": {
												"title": {
													"simpleText": "Sort by"
												},
												"filters": [
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Relevance"
															},
															"status": "FILTER_STATUS_SELECTED",
															"tooltip": "Sort by relevance",
															"trackingParams": "CKkBEJN1GAAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Upload date"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CKgBEJN1GAEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=CAISAhAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "CAISAhAB"
																}
															},
															"tooltip": "Sort by upload date",
															"trackingParams": "CKgBEJN1GAEiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "View count"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CKcBEJN1GAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=CAMSAhAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "CAMSAhAB"
																}
															},
															"tooltip": "Sort by view count",
															"trackingParams": "CKcBEJN1GAIiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"searchFilterRenderer": {
															"label": {
																"simpleText": "Rating"
															},
															"navigationEndpoint": {
																"clickTrackingParams": "CKYBEJN1GAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/results?search_query=Elon+Musk&sp=CAESAhAB",
																		"webPageType": "WEB_PAGE_TYPE_SEARCH",
																		"rootVe": 4724
																	}
																},
																"searchEndpoint": {
																	"query": "Elon Musk",
																	"params": "CAESAhAB"
																}
															},
															"tooltip": "Sort by rating",
															"trackingParams": "CKYBEJN1GAMiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													}
												],
												"trackingParams": "CKUBEJJ1GAQiEwiVionD5tGKAxUIA60GHT2pFEo="
											}
										}
									]
								}
							},
							"popupType": "DIALOG"
						}
					},
					"iconPosition": "BUTTON_ICON_POSITION_TYPE_RIGHT_OF_TEXT"
				}
			},
			"trackingParams": "CKMBEIjqCiITCJWKicPm0YoDFQgDrQYdPakUSg=="
		}
	},
	"topbar": {
		"desktopTopbarRenderer": {
			"logo": {
				"topbarLogoRenderer": {
					"iconImage": {
						"iconType": "YOUTUBE_LOGO"
					},
					"tooltipText": {
						"runs": [
							{
								"text": "YouTube Home"
							}
						]
					},
					"endpoint": {
						"clickTrackingParams": "CKIBELFeIhMIlYqJw-bRigMVCAOtBh09qRRK",
						"commandMetadata": {
							"webCommandMetadata": {
								"url": "/",
								"webPageType": "WEB_PAGE_TYPE_BROWSE",
								"rootVe": 3854,
								"apiUrl": "/youtubei/v1/browse"
							}
						},
						"browseEndpoint": {
							"browseId": "FEwhat_to_watch"
						}
					},
					"trackingParams": "CKIBELFeIhMIlYqJw-bRigMVCAOtBh09qRRK",
					"overrideEntityKey": "EgZ0b3BiYXIg9QEoAQ%3D%3D"
				}
			},
			"searchbox": {
				"fusionSearchboxRenderer": {
					"icon": {
						"iconType": "SEARCH"
					},
					"placeholderText": {
						"runs": [
							{
								"text": "Search"
							}
						]
					},
					"config": {
						"webSearchboxConfig": {
							"requestLanguage": "en",
							"requestDomain": "us",
							"hasOnscreenKeyboard": false,
							"focusSearchbox": true
						}
					},
					"trackingParams": "CKABEO1QIhMIlYqJw-bRigMVCAOtBh09qRRK",
					"searchEndpoint": {
						"clickTrackingParams": "CKABEO1QIhMIlYqJw-bRigMVCAOtBh09qRRK",
						"commandMetadata": {
							"webCommandMetadata": {
								"url": "/results?search_query=",
								"webPageType": "WEB_PAGE_TYPE_SEARCH",
								"rootVe": 4724
							}
						},
						"searchEndpoint": {
							"query": ""
						}
					},
					"clearButton": {
						"buttonRenderer": {
							"style": "STYLE_DEFAULT",
							"size": "SIZE_DEFAULT",
							"isDisabled": false,
							"icon": {
								"iconType": "CLOSE"
							},
							"trackingParams": "CKEBEPBbIhMIlYqJw-bRigMVCAOtBh09qRRK",
							"accessibilityData": {
								"accessibilityData": {
									"label": "Clear search query"
								}
							}
						}
					}
				}
			},
			"trackingParams": "CJQBEKusASITCJWKicPm0YoDFQgDrQYdPakUSg==",
			"topbarButtons": [
				{
					"topbarMenuButtonRenderer": {
						"icon": {
							"iconType": "MORE_VERT"
						},
						"menuRequest": {
							"clickTrackingParams": "CJ4BEP6rARgAIhMIlYqJw-bRigMVCAOtBh09qRRK",
							"commandMetadata": {
								"webCommandMetadata": {
									"sendPost": true,
									"apiUrl": "/youtubei/v1/account/account_menu"
								}
							},
							"signalServiceEndpoint": {
								"signal": "GET_ACCOUNT_MENU",
								"actions": [
									{
										"clickTrackingParams": "CJ4BEP6rARgAIhMIlYqJw-bRigMVCAOtBh09qRRK",
										"openPopupAction": {
											"popup": {
												"multiPageMenuRenderer": {
													"trackingParams": "CJ8BEP-rASITCJWKicPm0YoDFQgDrQYdPakUSg==",
													"style": "MULTI_PAGE_MENU_STYLE_TYPE_SYSTEM",
													"showLoadingSpinner": true
												}
											},
											"popupType": "DROPDOWN",
											"beReused": true
										}
									}
								]
							}
						},
						"trackingParams": "CJ4BEP6rARgAIhMIlYqJw-bRigMVCAOtBh09qRRK",
						"accessibility": {
							"accessibilityData": {
								"label": "Settings"
							}
						},
						"tooltip": "Settings",
						"style": "STYLE_DEFAULT"
					}
				},
				{
					"buttonRenderer": {
						"style": "STYLE_SUGGESTIVE",
						"size": "SIZE_SMALL",
						"text": {
							"runs": [
								{
									"text": "Sign in"
								}
							]
						},
						"icon": {
							"iconType": "AVATAR_LOGGED_OUT"
						},
						"navigationEndpoint": {
							"clickTrackingParams": "CJ0BENSABBgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
							"commandMetadata": {
								"webCommandMetadata": {
									"url": "https://accounts.google.com/ServiceLogin?service=youtube&uilel=3&passive=true&continue=https%3A%2F%2Fwww.youtube.com%2Fsignin%3Faction_handle_signin%3Dtrue%26app%3Ddesktop%26hl%3Den%26next%3Dhttps%253A%252F%252Fwww.youtube.com%252Fresults%253Fsearch_query%253DElon%252BMusk%2526sp%253DCAASAhAB&hl=en&ec=65620",
									"webPageType": "WEB_PAGE_TYPE_UNKNOWN",
									"rootVe": 83769
								}
							},
							"signInEndpoint": {
								"idamTag": "65620"
							}
						},
						"trackingParams": "CJ0BENSABBgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
						"targetId": "topbar-signin"
					}
				}
			],
			"hotkeyDialog": {
				"hotkeyDialogRenderer": {
					"title": {
						"runs": [
							{
								"text": "Keyboard shortcuts"
							}
						]
					},
					"sections": [
						{
							"hotkeyDialogSectionRenderer": {
								"title": {
									"runs": [
										{
											"text": "Playback"
										}
									]
								},
								"options": [
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Toggle play/pause"
													}
												]
											},
											"hotkey": "k"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Rewind 10 seconds"
													}
												]
											},
											"hotkey": "j"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Fast forward 10 seconds"
													}
												]
											},
											"hotkey": "l"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Previous video"
													}
												]
											},
											"hotkey": "P (SHIFT+p)"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Next video"
													}
												]
											},
											"hotkey": "N (SHIFT+n)"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Previous frame (while paused)"
													}
												]
											},
											"hotkey": ",",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Comma"
												}
											}
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Next frame (while paused)"
													}
												]
											},
											"hotkey": ".",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Period"
												}
											}
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Decrease playback rate"
													}
												]
											},
											"hotkey": "< (SHIFT+,)",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Less than or SHIFT + comma"
												}
											}
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Increase playback rate"
													}
												]
											},
											"hotkey": "> (SHIFT+.)",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Greater than or SHIFT + period"
												}
											}
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Seek to specific point in the video (7 advances to 70% of duration)"
													}
												]
											},
											"hotkey": "0..9"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Seek to previous chapter"
													}
												]
											},
											"hotkey": "OPTION + \\u2190"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Seek to next chapter"
													}
												]
											},
											"hotkey": "OPTION + \\u2192"
										}
									}
								]
							}
						},
						{
							"hotkeyDialogSectionRenderer": {
								"title": {
									"runs": [
										{
											"text": "General"
										}
									]
								},
								"options": [
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Toggle full screen"
													}
												]
											},
											"hotkey": "f"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Toggle theater mode"
													}
												]
											},
											"hotkey": "t"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Toggle miniplayer"
													}
												]
											},
											"hotkey": "i"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Close miniplayer or current dialog"
													}
												]
											},
											"hotkey": "ESCAPE"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Toggle mute"
													}
												]
											},
											"hotkey": "m"
										}
									}
								]
							}
						},
						{
							"hotkeyDialogSectionRenderer": {
								"title": {
									"runs": [
										{
											"text": "Subtitles and closed captions"
										}
									]
								},
								"options": [
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "If the video supports captions, toggle captions ON/OFF"
													}
												]
											},
											"hotkey": "c"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Rotate through different text opacity levels"
													}
												]
											},
											"hotkey": "o"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Rotate through different window opacity levels"
													}
												]
											},
											"hotkey": "w"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Rotate through font sizes (increasing)"
													}
												]
											},
											"hotkey": "+"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Rotate through font sizes (decreasing)"
													}
												]
											},
											"hotkey": "-",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Minus"
												}
											}
										}
									}
								]
							}
						},
						{
							"hotkeyDialogSectionRenderer": {
								"title": {
									"runs": [
										{
											"text": "Spherical Videos"
										}
									]
								},
								"options": [
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Pan up"
													}
												]
											},
											"hotkey": "w"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Pan left"
													}
												]
											},
											"hotkey": "a"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Pan down"
													}
												]
											},
											"hotkey": "s"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Pan right"
													}
												]
											},
											"hotkey": "d"
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Zoom in"
													}
												]
											},
											"hotkey": "+ on numpad or ]",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Plus on number pad or right bracket"
												}
											}
										}
									},
									{
										"hotkeyDialogSectionOptionRenderer": {
											"label": {
												"runs": [
													{
														"text": "Zoom out"
													}
												]
											},
											"hotkey": "- on numpad or [",
											"hotkeyAccessibilityLabel": {
												"accessibilityData": {
													"label": "Minus on number pad or left bracket"
												}
											}
										}
									}
								]
							}
						}
					],
					"dismissButton": {
						"buttonRenderer": {
							"style": "STYLE_BLUE_TEXT",
							"size": "SIZE_DEFAULT",
							"isDisabled": false,
							"text": {
								"runs": [
									{
										"text": "Dismiss"
									}
								]
							},
							"trackingParams": "CJwBEPBbIhMIlYqJw-bRigMVCAOtBh09qRRK"
						}
					},
					"trackingParams": "CJsBELXmAyITCJWKicPm0YoDFQgDrQYdPakUSg=="
				}
			},
			"backButton": {
				"buttonRenderer": {
					"trackingParams": "CJoBELyGAyITCJWKicPm0YoDFQgDrQYdPakUSg==",
					"command": {
						"clickTrackingParams": "CJoBELyGAyITCJWKicPm0YoDFQgDrQYdPakUSg==",
						"commandMetadata": {
							"webCommandMetadata": {
								"sendPost": true
							}
						},
						"signalServiceEndpoint": {
							"signal": "CLIENT_SIGNAL",
							"actions": [
								{
									"clickTrackingParams": "CJoBELyGAyITCJWKicPm0YoDFQgDrQYdPakUSg==",
									"signalAction": {
										"signal": "HISTORY_BACK"
									}
								}
							]
						}
					}
				}
			},
			"forwardButton": {
				"buttonRenderer": {
					"trackingParams": "CJkBEL2GAyITCJWKicPm0YoDFQgDrQYdPakUSg==",
					"command": {
						"clickTrackingParams": "CJkBEL2GAyITCJWKicPm0YoDFQgDrQYdPakUSg==",
						"commandMetadata": {
							"webCommandMetadata": {
								"sendPost": true
							}
						},
						"signalServiceEndpoint": {
							"signal": "CLIENT_SIGNAL",
							"actions": [
								{
									"clickTrackingParams": "CJkBEL2GAyITCJWKicPm0YoDFQgDrQYdPakUSg==",
									"signalAction": {
										"signal": "HISTORY_FORWARD"
									}
								}
							]
						}
					}
				}
			},
			"a11ySkipNavigationButton": {
				"buttonRenderer": {
					"style": "STYLE_DEFAULT",
					"size": "SIZE_DEFAULT",
					"isDisabled": false,
					"text": {
						"runs": [
							{
								"text": "Skip navigation"
							}
						]
					},
					"trackingParams": "CJgBEPBbIhMIlYqJw-bRigMVCAOtBh09qRRK",
					"command": {
						"clickTrackingParams": "CJgBEPBbIhMIlYqJw-bRigMVCAOtBh09qRRK",
						"commandMetadata": {
							"webCommandMetadata": {
								"sendPost": true
							}
						},
						"signalServiceEndpoint": {
							"signal": "CLIENT_SIGNAL",
							"actions": [
								{
									"clickTrackingParams": "CJgBEPBbIhMIlYqJw-bRigMVCAOtBh09qRRK",
									"signalAction": {
										"signal": "SKIP_NAVIGATION"
									}
								}
							]
						}
					}
				}
			},
			"voiceSearchButton": {
				"buttonRenderer": {
					"style": "STYLE_DEFAULT",
					"size": "SIZE_DEFAULT",
					"isDisabled": false,
					"serviceEndpoint": {
						"clickTrackingParams": "CJUBEO2vBSITCJWKicPm0YoDFQgDrQYdPakUSg==",
						"commandMetadata": {
							"webCommandMetadata": {
								"sendPost": true
							}
						},
						"signalServiceEndpoint": {
							"signal": "CLIENT_SIGNAL",
							"actions": [
								{
									"clickTrackingParams": "CJUBEO2vBSITCJWKicPm0YoDFQgDrQYdPakUSg==",
									"openPopupAction": {
										"popup": {
											"voiceSearchDialogRenderer": {
												"placeholderHeader": {
													"runs": [
														{
															"text": "Listening..."
														}
													]
												},
												"promptHeader": {
													"runs": [
														{
															"text": "Didn\'t hear that. Try again."
														}
													]
												},
												"exampleQuery1": {
													"runs": [
														{
															"text": "\\"
															Play
															Dua
															Lipa
															\
															\
															""
														}
													]
												},
												"exampleQuery2": {
													"runs": [
														{
															"text": "\\"
															Show
															me
															my
															subscriptions
															\
															\
															""
														}
													]
												},
												"promptMicrophoneLabel": {
													"runs": [
														{
															"text": "Tap microphone to try again"
														}
													]
												},
												"loadingHeader": {
													"runs": [
														{
															"text": "Working..."
														}
													]
												},
												"connectionErrorHeader": {
													"runs": [
														{
															"text": "No connection"
														}
													]
												},
												"connectionErrorMicrophoneLabel": {
													"runs": [
														{
															"text": "Check your connection and try again"
														}
													]
												},
												"permissionsHeader": {
													"runs": [
														{
															"text": "Waiting for permission"
														}
													]
												},
												"permissionsSubtext": {
													"runs": [
														{
															"text": "Allow microphone access to search with voice"
														}
													]
												},
												"disabledHeader": {
													"runs": [
														{
															"text": "Search with your voice"
														}
													]
												},
												"disabledSubtext": {
													"runs": [
														{
															"text": "To search by voice, go to your browser settings and allow access to microphone"
														}
													]
												},
												"microphoneButtonAriaLabel": {
													"runs": [
														{
															"text": "Cancel"
														}
													]
												},
												"exitButton": {
													"buttonRenderer": {
														"style": "STYLE_DEFAULT",
														"size": "SIZE_DEFAULT",
														"isDisabled": false,
														"icon": {
															"iconType": "CLOSE"
														},
														"trackingParams": "CJcBENCxBSITCJWKicPm0YoDFQgDrQYdPakUSg==",
														"accessibilityData": {
															"accessibilityData": {
																"label": "Cancel"
															}
														}
													}
												},
												"trackingParams": "CJYBEO6vBSITCJWKicPm0YoDFQgDrQYdPakUSg==",
												"microphoneOffPromptHeader": {
													"runs": [
														{
															"text": "Microphone off. Try again."
														}
													]
												}
											}
										},
										"popupType": "TOP_ALIGNED_DIALOG"
									}
								}
							]
						}
					},
					"icon": {
						"iconType": "MICROPHONE_ON"
					},
					"tooltip": "Search with your voice",
					"trackingParams": "CJUBEO2vBSITCJWKicPm0YoDFQgDrQYdPakUSg==",
					"accessibilityData": {
						"accessibilityData": {
							"label": "Search with your voice"
						}
					}
				}
			}
		}
	},
	"onResponseReceivedCommands": [
		{
			"clickTrackingParams": "CAAQvGkiEwiVionD5tGKAxUIA60GHT2pFEo=",
			"appendContinuationItemsAction": {
				"continuationItems": [
					{
						"itemSectionRenderer": {
							"contents": [
								{
									"videoRenderer": {
										"videoId": "360RE1xQ0Dg",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/360RE1xQ0Dg/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLCwiwt2uwHEp_nsu_4voZyJVklcnA",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/360RE1xQ0Dg/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLC8HEfmRGiPeL8kWxnVnBQzzoFwTQ",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Elon Musk voices support for H-1B immigration visas"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Elon Musk voices support for H-1B immigration visas by Yahoo Finance 21,485 views 10 hours ago 4 minutes, 46 seconds"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "Yahoo Finance",
													"navigationEndpoint": {
														"clickTrackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@YahooFinance",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCEAZeUIeJs0IjQiqTCdVSIg",
															"canonicalBaseUrl": "/@YahooFinance"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "10 hours ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "4 minutes, 46 seconds"
												}
											},
											"simpleText": "4:46"
										},
										"viewCountText": {
											"simpleText": "21,485 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=360RE1xQ0Dg&pp=ygUJRWxvbiBNdXNr",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "360RE1xQ0Dg",
												"params": "qgMJRWxvbiBNdXNrugMKCMqeoKjLpO-qf7oDCwiPzYXLl7LppIEBugMLCP_Hu7PuwfKxpAG6AwoI3PD47Lj92O9AugMKCJeM-evLxKLxELoDCwjM0qWxwY3kyq4BugMLCKCqssTpruXa-gG6AwsIhNz-qobIntS7AboDCgir0tnSi7v7qWy6AwsIu73bgemqieX2AboDCwirisWNytL8yYgBugMLCJ7d2cLa367_3QG6AwsIrJaYioeJxqqlAboDCgi54d64k5TmjUK6AwsIy8rcuJedzPegAboDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "ygUJRWxvbiBNdXNr",
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr5---sn-5ualdnsz.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=dfad11135c50d038&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"badges": [
											{
												"metadataBadgeRenderer": {
													"style": "BADGE_STYLE_TYPE_SIMPLE",
													"label": "New",
													"trackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											}
										],
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "Yahoo Finance",
													"navigationEndpoint": {
														"clickTrackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@YahooFinance",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCEAZeUIeJs0IjQiqTCdVSIg",
															"canonicalBaseUrl": "/@YahooFinance"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "Yahoo Finance",
													"navigationEndpoint": {
														"clickTrackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@YahooFinance",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCEAZeUIeJs0IjQiqTCdVSIg",
															"canonicalBaseUrl": "/@YahooFinance"
														}
													}
												}
											]
										},
										"trackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEpAuKDD4rWixNbfAQ==",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "21K views"
												}
											},
											"simpleText": "21K views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CJMBEP6YBBgPIhMIlYqJw-bRigMVCAOtBh09qRRK",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CJMBEP6YBBgPIhMIlYqJw-bRigMVCAOtBh09qRRK",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "360RE1xQ0Dg",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CJMBEP6YBBgPIhMIlYqJw-bRigMVCAOtBh09qRRK",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"360RE1xQ0Dg"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"360RE1xQ0Dg"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CJMBEP6YBBgPIhMIlYqJw-bRigMVCAOtBh09qRRK"
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CJIBENGqBRgQIhMIlYqJw-bRigMVCAOtBh09qRRK",
																"offlineVideoEndpoint": {
																	"videoId": "360RE1xQ0Dg",
																	"onAddCommand": {
																		"clickTrackingParams": "CJIBENGqBRgQIhMIlYqJw-bRigMVCAOtBh09qRRK",
																		"getDownloadActionCommand": {
																			"videoId": "360RE1xQ0Dg",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CJIBENGqBRgQIhMIlYqJw-bRigMVCAOtBh09qRRK"
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgszNjBSRTF4UTBEZw%3D%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CJEBEI5iIhMIlYqJw-bRigMVCAOtBh09qRRK",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/b1DiJRW66nbwC7IS01tHKxX8AUSxQIaUT9UFeqAx0-sNOOoyHI4k9LufXrowvULc24xVKf9xUA=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@YahooFinance",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCEAZeUIeJs0IjQiqTCdVSIg",
														"canonicalBaseUrl": "/@YahooFinance"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "4 minutes, 46 seconds"
															}
														},
														"simpleText": "4:46"
													},
													"style": "DEFAULT"
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CJABEPnnAxgDIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "360RE1xQ0Dg",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CJABEPnnAxgDIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "360RE1xQ0Dg"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CJABEPnnAxgDIhMIlYqJw-bRigMVCAOtBh09qRRK"
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CI8BEMfsBBgEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CI8BEMfsBBgEIhMIlYqJw-bRigMVCAOtBh09qRRK",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "360RE1xQ0Dg",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CI8BEMfsBBgEIhMIlYqJw-bRigMVCAOtBh09qRRK",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"360RE1xQ0Dg"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"360RE1xQ0Dg"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CI8BEMfsBBgEIhMIlYqJw-bRigMVCAOtBh09qRRK"
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/360RE1xQ0Dg/mqdefault_6s.webp?du=3000&sqp=CNDQzrsG&rs=AOn4CLANfIXTZ8bvQ0OabX1IO-ueZryBYQ",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "President-elect Donald Trump campaigned on strict immigration policies, including promises of mass deportations. However, tech\\u00a0..."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEDEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=360RE1xQ0Dg&pp=YAHIAQE%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "360RE1xQ0Dg",
												"params": "qgMJRWxvbiBNdXNrugMKCMqeoKjLpO-qf7oDCwiPzYXLl7LppIEBugMLCP_Hu7PuwfKxpAG6AwoI3PD47Lj92O9AugMKCJeM-evLxKLxELoDCwjM0qWxwY3kyq4BugMLCKCqssTpruXa-gG6AwsIhNz-qobIntS7AboDCgir0tnSi7v7qWy6AwsIu73bgemqieX2AboDCwirisWNytL8yYgBugMLCJ7d2cLa367_3QG6AwsIrJaYioeJxqqlAboDCgi54d64k5TmjUK6AwsIy8rcuJedzPegAboDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "YAHIAQE%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr5---sn-5ualdnsz.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=dfad11135c50d038&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgszNjBSRTF4UTBEZyDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/b1DiJRW66nbwC7IS01tHKxX8AUSxQIaUT9UFeqAx0-sNOOoyHI4k9LufXrowvULc24xVKf9xUA=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CI4BENwwGAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@YahooFinance",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCEAZeUIeJs0IjQiqTCdVSIg",
																	"canonicalBaseUrl": "/@YahooFinance"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "f1W9JLUID0o",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/f1W9JLUID0o/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgVCg3MA8=&rs=AOn4CLDZq9mFqZ-4pylqJTAhAEaBzt-GPw",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/f1W9JLUID0o/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgVCg3MA8=&rs=AOn4CLB0UzKUKB8vfpkyvxq4BHM8RiQR3Q",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "A List of Elon Musk Ex Wives & Girlfriends"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "A List of Elon Musk Ex Wives & Girlfriends by Facts About Celebrities 2,057,316 views 5 months ago 59 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "Facts About Celebrities",
													"navigationEndpoint": {
														"clickTrackingParams": "CIcBEJ2kBxgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@FactsAboutCelebrities001",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCxJIJ2Mo1U-pV_7NfeYFxYg",
															"canonicalBaseUrl": "/@FactsAboutCelebrities001"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "5 months ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "59 seconds"
												}
											},
											"simpleText": "0:59"
										},
										"viewCountText": {
											"simpleText": "2,057,316 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CIcBEJ2kBxgBIhMIlYqJw-bRigMVCAOtBh09qRRKUglFbG9uIE11c2uaAQUIMhD0JA==",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/f1W9JLUID0o",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "f1W9JLUID0o",
												"playerParams": "8AEByAMyuAQUogYVAePzwRODn0okykwidk96WyKYFhASkAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/f1W9JLUID0o/frame0.jpg",
															"width": 1080,
															"height": 1920
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CI0BELC1BCITCJWKicPm0YoDFQgDrQYdPakUSg==",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgtmMVc5SkxVSUQwbyocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerText": {
											"runs": [
												{
													"text": "Facts About Celebrities",
													"navigationEndpoint": {
														"clickTrackingParams": "CIcBEJ2kBxgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@FactsAboutCelebrities001",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCxJIJ2Mo1U-pV_7NfeYFxYg",
															"canonicalBaseUrl": "/@FactsAboutCelebrities001"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "Facts About Celebrities",
													"navigationEndpoint": {
														"clickTrackingParams": "CIcBEJ2kBxgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@FactsAboutCelebrities001",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCxJIJ2Mo1U-pV_7NfeYFxYg",
															"canonicalBaseUrl": "/@FactsAboutCelebrities001"
														}
													}
												}
											]
										},
										"trackingParams": "CIcBEJ2kBxgBIhMIlYqJw-bRigMVCAOtBh09qRRKQMqeoKjLpO-qfw==",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "2 million views"
												}
											},
											"simpleText": "2M views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CIwBEP6YBBgRIhMIlYqJw-bRigMVCAOtBh09qRRK",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CIwBEP6YBBgRIhMIlYqJw-bRigMVCAOtBh09qRRK",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "f1W9JLUID0o",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CIwBEP6YBBgRIhMIlYqJw-bRigMVCAOtBh09qRRK",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"f1W9JLUID0o"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"f1W9JLUID0o"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CIwBEP6YBBgRIhMIlYqJw-bRigMVCAOtBh09qRRK"
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CIsBENGqBRgSIhMIlYqJw-bRigMVCAOtBh09qRRK",
																"offlineVideoEndpoint": {
																	"videoId": "f1W9JLUID0o",
																	"onAddCommand": {
																		"clickTrackingParams": "CIsBENGqBRgSIhMIlYqJw-bRigMVCAOtBh09qRRK",
																		"getDownloadActionCommand": {
																			"videoId": "f1W9JLUID0o",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CIsBENGqBRgSIhMIlYqJw-bRigMVCAOtBh09qRRK"
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CIcBEJ2kBxgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtmMVc5SkxVSUQwb1ICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CIcBEJ2kBxgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CIoBEI5iIhMIlYqJw-bRigMVCAOtBh09qRRK",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CIcBEJ2kBxgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CIcBEJ2kBxgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/47tE7N7xHjO_iSWzgP_XAleROalxarsp5O0WBkLcz1-2xJraWi3M9njp6hI1jbdH88zvrp8ch6c=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CIcBEJ2kBxgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@FactsAboutCelebrities001",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCxJIJ2Mo1U-pV_7NfeYFxYg",
														"canonicalBaseUrl": "/@FactsAboutCelebrities001"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CIkBEPnnAxgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "f1W9JLUID0o",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CIkBEPnnAxgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "f1W9JLUID0o"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CIkBEPnnAxgBIhMIlYqJw-bRigMVCAOtBh09qRRK"
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CIgBEMfsBBgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CIgBEMfsBBgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "f1W9JLUID0o",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CIgBEMfsBBgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"f1W9JLUID0o"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"f1W9JLUID0o"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CIgBEMfsBBgCIhMIlYqJw-bRigMVCAOtBh09qRRK"
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/f1W9JLUID0o/mqdefault_6s.webp?du=3000&sqp=CML_zrsG&rs=AOn4CLDC9Ectos2b0vBQU-dYM6ZJ5vvt0A",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "A List of "
														},
														{
															"text": "Elon Musk",
															"bold": true
														},
														{
															"text": " Ex Wives & Girlfriends #Short, #"
														},
														{
															"text": "ElonMusk",
															"bold": true
														},
														{
															"text": ", #AmberHeard, #CameronDiaz, #CaraDelevingne,\\u00a0..."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CIcBEJ2kBxgBIhMIlYqJw-bRigMVCAOtBh09qRRKMgZzZWFyY2hSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=f1W9JLUID0o&pp=YAHIAQHwAQHoBQGiBhUB4_PBE4OfSiTKTCJ2T3pbIpgWEBKQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "f1W9JLUID0o",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwsIj82Fy5ey6aSBAboDCwj_x7uz7sHysaQBugMKCNzw-Oy4_djvQLoDCgiXjPnry8Si8RC6AwsIzNKlscGN5MquAboDCwigqrLE6a7l2voBugMLCITc_qqGyJ7UuwG6AwoIq9LZ0ou7-6lsugMLCLu924Hpqonl9gG6AwsIq4rFjcrS_MmIAboDCwie3dnC2t-u_90BugMLCKyWmIqHicaqpQG6AwoIueHeuJOU5o1CugMLCMvK3LiXncz3oAG6AwoIp6qBjPDwv-JYugMKCPmyzYeInbL0GroDCgiug-GbkICfhWe6AwoIo7PIsbvrm8pG8gMFDSXK9D4%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE4OfSiTKTCJ2T3pbIpgWEBKQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr5---sn-5uaeznlz.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=7f55bd24b5080f4a&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtmMVc5SkxVSUQwbyDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/47tE7N7xHjO_iSWzgP_XAleROalxarsp5O0WBkLcz1-2xJraWi3M9njp6hI1jbdH88zvrp8ch6c=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CIcBEJ2kBxgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@FactsAboutCelebrities001",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCxJIJ2Mo1U-pV_7NfeYFxYg",
																	"canonicalBaseUrl": "/@FactsAboutCelebrities001"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "gUmlkXlhZo8",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/gUmlkXlhZo8/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGGUgYChWMA8=&rs=AOn4CLBc-NQWQaVLw2AHrE2_bcL3eFpblg",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/gUmlkXlhZo8/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGGUgYChWMA8=&rs=AOn4CLB1Z-QRr0jwsrrI1GM45fqWMay-_A",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Elon Musk: Age is just a number #starship #spacex"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Elon Musk: Age is just a number #starship #spacex by Ellie in Space 8,742,655 views 6 months ago 33 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "Ellie in Space",
													"navigationEndpoint": {
														"clickTrackingParams": "CIABEJ2kBxgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@ellieinspace",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCelXvXZDvx8_TdOOffevzGg",
															"canonicalBaseUrl": "/@ellieinspace"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "6 months ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "33 seconds"
												}
											},
											"simpleText": "0:33"
										},
										"viewCountText": {
											"simpleText": "8,742,655 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CIABEJ2kBxgCIhMIlYqJw-bRigMVCAOtBh09qRRKUglFbG9uIE11c2uaAQUIMhD0JA==",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/gUmlkXlhZo8",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "gUmlkXlhZo8",
												"playerParams": "8AEByAMyuAQUogYVAePzwRNFD5Ixf_ix_5Khy-w2MGZBkAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/gUmlkXlhZo8/frame0.jpg",
															"width": 1080,
															"height": 1920
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CIYBELC1BCITCJWKicPm0YoDFQgDrQYdPakUSg==",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgtnVW1sa1hsaFpvOCocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerText": {
											"runs": [
												{
													"text": "Ellie in Space",
													"navigationEndpoint": {
														"clickTrackingParams": "CIABEJ2kBxgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@ellieinspace",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCelXvXZDvx8_TdOOffevzGg",
															"canonicalBaseUrl": "/@ellieinspace"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "Ellie in Space",
													"navigationEndpoint": {
														"clickTrackingParams": "CIABEJ2kBxgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@ellieinspace",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCelXvXZDvx8_TdOOffevzGg",
															"canonicalBaseUrl": "/@ellieinspace"
														}
													}
												}
											]
										},
										"trackingParams": "CIABEJ2kBxgCIhMIlYqJw-bRigMVCAOtBh09qRRKQI_NhcuXsumkgQE=",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "8.7 million views"
												}
											},
											"simpleText": "8.7M views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CIUBEP6YBBgKIhMIlYqJw-bRigMVCAOtBh09qRRK",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CIUBEP6YBBgKIhMIlYqJw-bRigMVCAOtBh09qRRK",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "gUmlkXlhZo8",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CIUBEP6YBBgKIhMIlYqJw-bRigMVCAOtBh09qRRK",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"gUmlkXlhZo8"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"gUmlkXlhZo8"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CIUBEP6YBBgKIhMIlYqJw-bRigMVCAOtBh09qRRK"
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CIQBENGqBRgLIhMIlYqJw-bRigMVCAOtBh09qRRK",
																"offlineVideoEndpoint": {
																	"videoId": "gUmlkXlhZo8",
																	"onAddCommand": {
																		"clickTrackingParams": "CIQBENGqBRgLIhMIlYqJw-bRigMVCAOtBh09qRRK",
																		"getDownloadActionCommand": {
																			"videoId": "gUmlkXlhZo8",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CIQBENGqBRgLIhMIlYqJw-bRigMVCAOtBh09qRRK"
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CIABEJ2kBxgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtnVW1sa1hsaFpvOFICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CIABEJ2kBxgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CIMBEI5iIhMIlYqJw-bRigMVCAOtBh09qRRK",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CIABEJ2kBxgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CIABEJ2kBxgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/EgvUlcGSz_pXmszz4po6tS7Eg6nrQtbWJIGJlfyUmhB2pOseI7ZCvRHON9CfAki3dK7PhuoA8w=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CIABEJ2kBxgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@ellieinspace",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCelXvXZDvx8_TdOOffevzGg",
														"canonicalBaseUrl": "/@ellieinspace"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CIIBEPnnAxgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "gUmlkXlhZo8",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CIIBEPnnAxgBIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "gUmlkXlhZo8"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CIIBEPnnAxgBIhMIlYqJw-bRigMVCAOtBh09qRRK"
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CIEBEMfsBBgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CIEBEMfsBBgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "gUmlkXlhZo8",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CIEBEMfsBBgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"gUmlkXlhZo8"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"gUmlkXlhZo8"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CIEBEMfsBBgCIhMIlYqJw-bRigMVCAOtBh09qRRK"
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/gUmlkXlhZo8/mqdefault_6s.webp?du=3000&sqp=CMDGzrsG&rs=AOn4CLDKNlppMt_nWYvMt3AKlo5Y1lbqNg",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CIABEJ2kBxgCIhMIlYqJw-bRigMVCAOtBh09qRRKMgZzZWFyY2hSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=gUmlkXlhZo8&pp=YAHIAQHwAQHoBQGiBhUB4_PBE0UPkjF_-LH_kqHL7DYwZkGQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "gUmlkXlhZo8",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCP_Hu7PuwfKxpAG6AwoI3PD47Lj92O9AugMKCJeM-evLxKLxELoDCwjM0qWxwY3kyq4BugMLCKCqssTpruXa-gG6AwsIhNz-qobIntS7AboDCgir0tnSi7v7qWy6AwsIu73bgemqieX2AboDCwirisWNytL8yYgBugMLCJ7d2cLa367_3QG6AwsIrJaYioeJxqqlAboDCgi54d64k5TmjUK6AwsIy8rcuJedzPegAboDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE0UPkjF_-LH_kqHL7DYwZkGQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr5---sn-5ualdnse.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=8149a5917961668f&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtnVW1sa1hsaFpvOCDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/EgvUlcGSz_pXmszz4po6tS7Eg6nrQtbWJIGJlfyUmhB2pOseI7ZCvRHON9CfAki3dK7PhuoA8w=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CIABEJ2kBxgCIhMIlYqJw-bRigMVCAOtBh09qRRK",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@ellieinspace",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCelXvXZDvx8_TdOOffevzGg",
																	"canonicalBaseUrl": "/@ellieinspace"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "pGPKDuZu4_8",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/pGPKDuZu4_8/hq720.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgTShFMA8=&rs=AOn4CLBRyc9AILoNtnAoR-7U1ebLhBdi1Q",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/pGPKDuZu4_8/hq720.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgTShFMA8=&rs=AOn4CLDf1ZokOFngxVtAnWA0k6eCuGqgXw",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "\'An added value\': Italy\'s PM Giorgia Meloni praises Elon Musk after Trump\'s election"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "\'An added value\': Italy\'s PM Giorgia Meloni praises Elon Musk after Trump\'s election by The Telegraph 71,809 views 1 month ago 35 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "The Telegraph",
													"navigationEndpoint": {
														"clickTrackingParams": "CHkQnaQHGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@telegraph",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCPgLNge0xqQHWM5B5EFH9Cg",
															"canonicalBaseUrl": "/@telegraph"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "1 month ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "35 seconds"
												}
											},
											"simpleText": "0:35"
										},
										"viewCountText": {
											"simpleText": "71,809 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CHkQnaQHGAMiEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/pGPKDuZu4_8",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "pGPKDuZu4_8",
												"playerParams": "8AEByAMyuAQUogYVAePzwRMAzG8bsnd84sheVCp2isl9kAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/pGPKDuZu4_8/frame0.jpg",
															"width": 1080,
															"height": 1920
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CH8QsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgtwR1BLRHVadTRfOCocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CHkQnaQHGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "The Telegraph",
													"navigationEndpoint": {
														"clickTrackingParams": "CHkQnaQHGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@telegraph",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCPgLNge0xqQHWM5B5EFH9Cg",
															"canonicalBaseUrl": "/@telegraph"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "The Telegraph",
													"navigationEndpoint": {
														"clickTrackingParams": "CHkQnaQHGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@telegraph",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCPgLNge0xqQHWM5B5EFH9Cg",
															"canonicalBaseUrl": "/@telegraph"
														}
													}
												}
											]
										},
										"trackingParams": "CHkQnaQHGAMiEwiVionD5tGKAxUIA60GHT2pFEpA_8e7s-7B8rGkAQ==",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "71K views"
												}
											},
											"simpleText": "71K views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CH4Q_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CH4Q_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "pGPKDuZu4_8",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CH4Q_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"pGPKDuZu4_8"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"pGPKDuZu4_8"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CH4Q_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CH0Q0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "pGPKDuZu4_8",
																	"onAddCommand": {
																		"clickTrackingParams": "CH0Q0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "pGPKDuZu4_8",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CH0Q0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CHkQnaQHGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtwR1BLRHVadTRfOFICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CHkQnaQHGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CHwQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CHkQnaQHGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CHkQnaQHGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/rSm619jUmehqFMzZ9OHgc9saNFDnEEKAOfJ__NAjMm4mhVBJEktghy4gXNBqiTbXywA2KKQG=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CHkQnaQHGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@telegraph",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCPgLNge0xqQHWM5B5EFH9Cg",
														"canonicalBaseUrl": "/@telegraph"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CHsQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "pGPKDuZu4_8",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CHsQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "pGPKDuZu4_8"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CHsQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CHoQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CHoQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "pGPKDuZu4_8",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CHoQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"pGPKDuZu4_8"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"pGPKDuZu4_8"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CHoQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/pGPKDuZu4_8/mqdefault_6s.webp?du=3000&sqp=CPvjzrsG&rs=AOn4CLDo19BTDWuWkATXSDMdwTHSHkvXQw",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "Italian Prime Minister Giorgia Meloni said she considered Tesla CEO "
														},
														{
															"text": "Elon Musk",
															"bold": true
														},
														{
															"text": ", who backed Donald Trump in his victorious\\u00a0..."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CHkQnaQHGAMiEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=pGPKDuZu4_8&pp=YAHIAQHwAQHoBQGiBhUB4_PBEwDMbxuyd3ziyF5UKnaKyX2QBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "pGPKDuZu4_8",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwoI3PD47Lj92O9AugMKCJeM-evLxKLxELoDCwjM0qWxwY3kyq4BugMLCKCqssTpruXa-gG6AwsIhNz-qobIntS7AboDCgir0tnSi7v7qWy6AwsIu73bgemqieX2AboDCwirisWNytL8yYgBugMLCJ7d2cLa367_3QG6AwsIrJaYioeJxqqlAboDCgi54d64k5TmjUK6AwsIy8rcuJedzPegAboDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBEwDMbxuyd3ziyF5UKnaKyX2QBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr3---sn-5uaeznlz.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=a463ca0ee66ee3ff&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtwR1BLRHVadTRfOCDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/rSm619jUmehqFMzZ9OHgc9saNFDnEEKAOfJ__NAjMm4mhVBJEktghy4gXNBqiTbXywA2KKQG=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CHkQnaQHGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@telegraph",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCPgLNge0xqQHWM5B5EFH9Cg",
																	"canonicalBaseUrl": "/@telegraph"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "QN9j642eOFw",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/QN9j642eOFw/hq720.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgUCg0MA8=&rs=AOn4CLBCpdYe6QiizIchOGBf0KChhcYX-g",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/QN9j642eOFw/hq720.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgUCg0MA8=&rs=AOn4CLAHVkp4XeJH_hUik4ng5ClX2oqtTw",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "What is Elon Musk planning by staying \'front and center\' in Trump\'s universe?"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "What is Elon Musk planning by staying \'front and center\' in Trump\'s universe? by MSNBC 549,019 views 1 month ago 1 minute - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "MSNBC",
													"navigationEndpoint": {
														"clickTrackingParams": "CGkQnaQHGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@msnbc",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCaXkIU1QidjPwiAYu6GcHjg",
															"canonicalBaseUrl": "/@msnbc"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "1 month ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "1 minute"
												}
											},
											"simpleText": "1:00"
										},
										"viewCountText": {
											"simpleText": "549,019 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CGkQnaQHGAQiEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/QN9j642eOFw",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "QN9j642eOFw",
												"playerParams": "8AEByAMyuAQUogYVAePzwRPsKuWch0v3C2k9mF0osL8ikAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/QN9j642eOFw/frame0.jpg",
															"width": 1080,
															"height": 1920
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CHgQsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgtRTjlqNjQyZU9GdyocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CGkQnaQHGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "MSNBC",
													"navigationEndpoint": {
														"clickTrackingParams": "CGkQnaQHGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@msnbc",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCaXkIU1QidjPwiAYu6GcHjg",
															"canonicalBaseUrl": "/@msnbc"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "MSNBC",
													"navigationEndpoint": {
														"clickTrackingParams": "CGkQnaQHGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@msnbc",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCaXkIU1QidjPwiAYu6GcHjg",
															"canonicalBaseUrl": "/@msnbc"
														}
													}
												}
											]
										},
										"trackingParams": "CGkQnaQHGAQiEwiVionD5tGKAxUIA60GHT2pFEpA3PD47Lj92O9A",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "549K views"
												}
											},
											"simpleText": "549K views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CHcQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CHcQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "QN9j642eOFw",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CHcQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"QN9j642eOFw"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"QN9j642eOFw"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CHcQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CHYQ0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "QN9j642eOFw",
																	"onAddCommand": {
																		"clickTrackingParams": "CHYQ0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "QN9j642eOFw",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CHYQ0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CGkQnaQHGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtRTjlqNjQyZU9Gd1ICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CGkQnaQHGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CHUQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CGkQnaQHGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CGkQnaQHGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/Kn00Sgjs6hlFbGtbYmE2bqMrGr_Gbz062eFK_fnVfziUN4HZ2C6I988wsfARNEcFwBB_5rARdw=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CGkQnaQHGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@msnbc",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCaXkIU1QidjPwiAYu6GcHjg",
														"canonicalBaseUrl": "/@msnbc"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CHQQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "QN9j642eOFw",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CHQQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "QN9j642eOFw"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CHQQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CHMQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CHMQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "QN9j642eOFw",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CHMQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"QN9j642eOFw"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"QN9j642eOFw"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CHMQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/QN9j642eOFw/mqdefault_6s.webp?du=3000&sqp=CPyKz7sG&rs=AOn4CLAw_cq8PvnkrURRepFWTUa14rmO6A",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "Tech mogul "
														},
														{
															"text": "Elon Musk",
															"bold": true
														},
														{
															"text": ", who campaigned for Trump in the final weeks of the 2024 race, has been a regular presence at\\u00a0..."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": true
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CGkQnaQHGAQiEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=QN9j642eOFw&pp=YAHIAQHwAQHoBQGiBhUB4_PBE-wq5ZyHS_cLaT2YXSiwvyKQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "QN9j642eOFw",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgiXjPnry8Si8RC6AwsIzNKlscGN5MquAboDCwigqrLE6a7l2voBugMLCITc_qqGyJ7UuwG6AwoIq9LZ0ou7-6lsugMLCLu924Hpqonl9gG6AwsIq4rFjcrS_MmIAboDCwie3dnC2t-u_90BugMLCKyWmIqHicaqpQG6AwoIueHeuJOU5o1CugMLCMvK3LiXncz3oAG6AwoIp6qBjPDwv-JYugMKCPmyzYeInbL0GroDCgiug-GbkICfhWe6AwoIo7PIsbvrm8pG8gMFDSXK9D4%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE-wq5ZyHS_cLaT2YXSiwvyKQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr4---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=40df63eb8d9e385c&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"expandableMetadata": {
											"expandableMetadataRenderer": {
												"header": {
													"collapsedTitle": {
														"runs": [
															{
																"text": "Intro | Where is Elon Musk | What does Elon Musk want"
															}
														]
													},
													"collapsedThumbnail": {
														"thumbnails": [
															{
																"url": "https://i.ytimg.com/vi/QN9j642eOFw/hqdefault_1500.jpg?sqp=-oaymwFBCNACELwBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgUCg0MA8=&rs=AOn4CLB-X6IUU5-nBQRzT8TZ1sc4j_pHOw",
																"width": 336,
																"height": 188
															}
														]
													},
													"collapsedLabel": {
														"runs": [
															{
																"text": "3"
															},
															{
																"text": " chapters"
															}
														]
													},
													"expandedTitle": {
														"runs": [
															{
																"text": "3"
															},
															{
																"text": " auto-generated chapters in this video"
															}
														]
													},
													"showLeadingCollapsedLabel": true
												},
												"expandedContent": {
													"horizontalCardListRenderer": {
														"cards": [
															{
																"macroMarkersListItemRenderer": {
																	"title": {
																		"runs": [
																			{
																				"text": "Intro"
																			}
																		]
																	},
																	"timeDescription": {
																		"runs": [
																			{
																				"text": "0:00"
																			}
																		]
																	},
																	"thumbnail": {
																		"thumbnails": [
																			{
																				"url": "https://i.ytimg.com/vi/QN9j642eOFw/hqdefault_1500.jpg?sqp=-oaymwFBCNACELwBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgUCg0MA8=&rs=AOn4CLB-X6IUU5-nBQRzT8TZ1sc4j_pHOw",
																				"width": 336,
																				"height": 188
																			}
																		]
																	},
																	"onTap": {
																		"clickTrackingParams": "CHIQ0NAGGAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"commandMetadata": {
																			"webCommandMetadata": {
																				"url": "/watch?v=QN9j642eOFw",
																				"webPageType": "WEB_PAGE_TYPE_WATCH",
																				"rootVe": 3832
																			}
																		},
																		"watchEndpoint": {
																			"videoId": "QN9j642eOFw",
																			"watchEndpointSupportedOnesieConfig": {
																				"html5PlaybackOnesieConfig": {
																					"commonConfig": {
																						"url": "https://rr4---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=40df63eb8d9e385c&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
																					}
																				}
																			}
																		}
																	},
																	"trackingParams": "CHIQ0NAGGAAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"layout": "MACRO_MARKERS_LIST_ITEM_RENDERER_LAYOUT_VERTICAL",
																	"isHighlighted": false
																}
															},
															{
																"macroMarkersListItemRenderer": {
																	"title": {
																		"runs": [
																			{
																				"text": "Where is Elon Musk"
																			}
																		]
																	},
																	"timeDescription": {
																		"runs": [
																			{
																				"text": "0:20"
																			}
																		]
																	},
																	"thumbnail": {
																		"thumbnails": [
																			{
																				"url": "https://i.ytimg.com/vi/QN9j642eOFw/hqdefault_33500.jpg?sqp=-oaymwFBCNACELwBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgUCg0MA8=&rs=AOn4CLA13-2RYNUF9eyyC092Ekkw9Ncbqg",
																				"width": 336,
																				"height": 188
																			}
																		]
																	},
																	"onTap": {
																		"clickTrackingParams": "CHEQ0NAGGAEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"commandMetadata": {
																			"webCommandMetadata": {
																				"url": "/watch?v=QN9j642eOFw&t=20s",
																				"webPageType": "WEB_PAGE_TYPE_WATCH",
																				"rootVe": 3832
																			}
																		},
																		"watchEndpoint": {
																			"videoId": "QN9j642eOFw",
																			"startTimeSeconds": 20,
																			"watchEndpointSupportedOnesieConfig": {
																				"html5PlaybackOnesieConfig": {
																					"commonConfig": {
																						"url": "https://rr4---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=40df63eb8d9e385c&ip=*************&osts=20&initcwndbps=1512500&mt=1735640720&oweuc="
																					}
																				}
																			}
																		}
																	},
																	"trackingParams": "CHEQ0NAGGAEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"layout": "MACRO_MARKERS_LIST_ITEM_RENDERER_LAYOUT_VERTICAL",
																	"isHighlighted": false
																}
															},
															{
																"macroMarkersListItemRenderer": {
																	"title": {
																		"runs": [
																			{
																				"text": "What does Elon Musk want"
																			}
																		]
																	},
																	"timeDescription": {
																		"runs": [
																			{
																				"text": "0:40"
																			}
																		]
																	},
																	"thumbnail": {
																		"thumbnails": [
																			{
																				"url": "https://i.ytimg.com/vi/QN9j642eOFw/hqdefault_56000.jpg?sqp=-oaymwFBCNACELwBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgUCg0MA8=&rs=AOn4CLDljHAN7XZ3H2-l6YYeLX0iMVdixw",
																				"width": 336,
																				"height": 188
																			}
																		]
																	},
																	"onTap": {
																		"clickTrackingParams": "CHAQ0NAGGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"commandMetadata": {
																			"webCommandMetadata": {
																				"url": "/watch?v=QN9j642eOFw&t=40s",
																				"webPageType": "WEB_PAGE_TYPE_WATCH",
																				"rootVe": 3832
																			}
																		},
																		"watchEndpoint": {
																			"videoId": "QN9j642eOFw",
																			"startTimeSeconds": 40,
																			"watchEndpointSupportedOnesieConfig": {
																				"html5PlaybackOnesieConfig": {
																					"commonConfig": {
																						"url": "https://rr4---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=40df63eb8d9e385c&ip=*************&osts=40&initcwndbps=1512500&mt=1735640720&oweuc="
																					}
																				}
																			}
																		}
																	},
																	"trackingParams": "CHAQ0NAGGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"layout": "MACRO_MARKERS_LIST_ITEM_RENDERER_LAYOUT_VERTICAL",
																	"isHighlighted": false
																}
															}
														],
														"trackingParams": "CG0QkVoiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"style": {
															"type": "HORIZONTAL_CARD_LIST_STYLE_TYPE_ENGAGEMENT_PANEL_SECTION"
														},
														"previousButton": {
															"buttonRenderer": {
																"style": "STYLE_DEFAULT",
																"size": "SIZE_DEFAULT",
																"isDisabled": false,
																"icon": {
																	"iconType": "CHEVRON_LEFT"
																},
																"trackingParams": "CG8Q8FsiEwiVionD5tGKAxUIA60GHT2pFEo="
															}
														},
														"nextButton": {
															"buttonRenderer": {
																"style": "STYLE_DEFAULT",
																"size": "SIZE_DEFAULT",
																"isDisabled": false,
																"icon": {
																	"iconType": "CHEVRON_RIGHT"
																},
																"trackingParams": "CG4Q8FsiEwiVionD5tGKAxUIA60GHT2pFEo="
															}
														}
													}
												},
												"expandButton": {
													"buttonRenderer": {
														"style": "STYLE_DEFAULT",
														"size": "SIZE_DEFAULT",
														"isDisabled": false,
														"icon": {
															"iconType": "EXPAND_MORE"
														},
														"trackingParams": "CGwQ8FsiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"accessibilityData": {
															"accessibilityData": {
																"label": "More"
															}
														}
													}
												},
												"collapseButton": {
													"buttonRenderer": {
														"style": "STYLE_DEFAULT",
														"size": "SIZE_DEFAULT",
														"isDisabled": false,
														"icon": {
															"iconType": "EXPAND_LESS"
														},
														"trackingParams": "CGsQ8FsiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"accessibilityData": {
															"accessibilityData": {
																"label": "Less"
															}
														}
													}
												},
												"trackingParams": "CGoQ78MHIhMIlYqJw-bRigMVCAOtBh09qRRK",
												"colorData": {
													"lightColorPalette": {
														"section1Color": 4294966523,
														"section2Color": 4294505713,
														"section3Color": 4294110439,
														"primaryTitleColor": 4279833616,
														"secondaryTitleColor": 4285881932,
														"iconActivatedColor": 4284038696,
														"iconInactiveColor": 4289041284,
														"section4Color": 4293649629,
														"iconDisabledColor": 4074294467
													},
													"darkColorPalette": {
														"section1Color": 4282330140,
														"section2Color": 4281541399,
														"section3Color": 4280687121,
														"primaryTitleColor": 4294963685,
														"secondaryTitleColor": 4291605923,
														"iconActivatedColor": 4294963685,
														"iconInactiveColor": 4287857526,
														"section4Color": 4279832843,
														"iconDisabledColor": 4066399305
													},
													"vibrantColorPalette": {
														"section1Color": 4285681716,
														"section2Color": 4284827438,
														"section3Color": 4284038696,
														"primaryTitleColor": 4294963685,
														"secondaryTitleColor": 4293579195,
														"iconActivatedColor": 4294963685,
														"iconInactiveColor": 4288249707,
														"section4Color": 4283184418,
														"iconDisabledColor": 4065083189
													}
												},
												"useCustomColors": true,
												"loggingDirectives": {
													"trackingParams": "CGoQ78MHIhMIlYqJw-bRigMVCAOtBh09qRRK",
													"visibility": {
														"types": "12"
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtRTjlqNjQyZU9GdyDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/Kn00Sgjs6hlFbGtbYmE2bqMrGr_Gbz062eFK_fnVfziUN4HZ2C6I988wsfARNEcFwBB_5rARdw=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CGkQnaQHGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@msnbc",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCaXkIU1QidjPwiAYu6GcHjg",
																	"canonicalBaseUrl": "/@msnbc"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "EOKKJL1-Rhc",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/EOKKJL1-Rhc/hq720.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AHOBYACgAqKAgwIABABGGUgTChLMA8=&rs=AOn4CLAkjYuFvIM4a6EEfBuwmbUYWA1MrQ",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/EOKKJL1-Rhc/hq720.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AHOBYACgAqKAgwIABABGGUgTChLMA8=&rs=AOn4CLBGGe1GP8JFQBoy8FOZYVSyFAf6Rg",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Trump Criticizes Gavin Newsom for Losing Elon Musk and Tesla Amid California\'s Economic Decline"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Trump Criticizes Gavin Newsom for Losing Elon Musk and Tesla Amid California\'s Economic Decline by Valuetainment 1,691,144 views 2 months ago 56 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "Valuetainment",
													"navigationEndpoint": {
														"clickTrackingParams": "CGIQnaQHGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@VALUETAINMENT",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCIHdDJ0tjn_3j-FS7s_X1kQ",
															"canonicalBaseUrl": "/@VALUETAINMENT"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "2 months ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "56 seconds"
												}
											},
											"simpleText": "0:56"
										},
										"viewCountText": {
											"simpleText": "1,691,144 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CGIQnaQHGAUiEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/EOKKJL1-Rhc",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "EOKKJL1-Rhc",
												"playerParams": "8AEByAMyuAQUogYVAePzwRNTY-AOvEWZL1h427u_wFLQkAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/EOKKJL1-Rhc/frame0.jpg",
															"width": 720,
															"height": 1280
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CGgQsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgtFT0tLSkwxLVJoYyocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CGIQnaQHGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "Valuetainment",
													"navigationEndpoint": {
														"clickTrackingParams": "CGIQnaQHGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@VALUETAINMENT",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCIHdDJ0tjn_3j-FS7s_X1kQ",
															"canonicalBaseUrl": "/@VALUETAINMENT"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "Valuetainment",
													"navigationEndpoint": {
														"clickTrackingParams": "CGIQnaQHGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@VALUETAINMENT",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCIHdDJ0tjn_3j-FS7s_X1kQ",
															"canonicalBaseUrl": "/@VALUETAINMENT"
														}
													}
												}
											]
										},
										"trackingParams": "CGIQnaQHGAUiEwiVionD5tGKAxUIA60GHT2pFEpAl4z568vEovEQ",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "1.6 million views"
												}
											},
											"simpleText": "1.6M views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CGcQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CGcQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "EOKKJL1-Rhc",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CGcQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"EOKKJL1-Rhc"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"EOKKJL1-Rhc"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CGcQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CGYQ0aoFGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "EOKKJL1-Rhc",
																	"onAddCommand": {
																		"clickTrackingParams": "CGYQ0aoFGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "EOKKJL1-Rhc",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CGYQ0aoFGA8iEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CGIQnaQHGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtFT0tLSkwxLVJoY1ICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CGIQnaQHGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CGUQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CGIQnaQHGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CGIQnaQHGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/ytc/AIdro_m8k9-w93UgQgDTNDD4CjCCroRV_GQqEjEC2997mYKSiNw=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CGIQnaQHGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@VALUETAINMENT",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCIHdDJ0tjn_3j-FS7s_X1kQ",
														"canonicalBaseUrl": "/@VALUETAINMENT"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CGQQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "EOKKJL1-Rhc",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CGQQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "EOKKJL1-Rhc"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CGQQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CGMQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CGMQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "EOKKJL1-Rhc",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CGMQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"EOKKJL1-Rhc"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"EOKKJL1-Rhc"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CGMQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/EOKKJL1-Rhc/mqdefault_6s.webp?du=3000&sqp=CICIz7sG&rs=AOn4CLBI8ScHYvIiTQnFnm_ntTCAf5hfnQ",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "Patrick Bet-David interviews President Donald J. Trump about the border crisis, voter ID laws, the economy under Biden, and\\u00a0..."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CGIQnaQHGAUiEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=EOKKJL1-Rhc&pp=YAHIAQHwAQHoBQGiBhUB4_PBE1Nj4A68RZkvWHjbu7_AUtCQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "EOKKJL1-Rhc",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwsIzNKlscGN5MquAboDCwigqrLE6a7l2voBugMLCITc_qqGyJ7UuwG6AwoIq9LZ0ou7-6lsugMLCLu924Hpqonl9gG6AwsIq4rFjcrS_MmIAboDCwie3dnC2t-u_90BugMLCKyWmIqHicaqpQG6AwoIueHeuJOU5o1CugMLCMvK3LiXncz3oAG6AwoIp6qBjPDwv-JYugMKCPmyzYeInbL0GroDCgiug-GbkICfhWe6AwoIo7PIsbvrm8pG8gMFDSXK9D4%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE1Nj4A68RZkvWHjbu7_AUtCQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr4---sn-5uaeznlz.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=10e28a24bd7e4617&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtFT0tLSkwxLVJoYyDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/ytc/AIdro_m8k9-w93UgQgDTNDD4CjCCroRV_GQqEjEC2997mYKSiNw=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CGIQnaQHGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@VALUETAINMENT",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCIHdDJ0tjn_3j-FS7s_X1kQ",
																	"canonicalBaseUrl": "/@VALUETAINMENT"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "rpWQbBYpaUw",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/rpWQbBYpaUw/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGGUgYChVMA8=&rs=AOn4CLD0Kuw7wHnlMvtYJX4DcLBFQvqzPg",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/rpWQbBYpaUw/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGGUgYChVMA8=&rs=AOn4CLAv-BmUxxfrPBWOnzJFzyqGEm7Y5A",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Elon Musk Shows Off Tesla\'s Humanoid Robot Optimus at We, Robot Event | TechCrunch"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Elon Musk Shows Off Tesla\'s Humanoid Robot Optimus at We, Robot Event | TechCrunch by TechCrunch 578,976 views 2 months ago 24 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "TechCrunch",
													"navigationEndpoint": {
														"clickTrackingParams": "CFsQnaQHGAYiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@TechCrunch",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCCjyq_K1Xwfg8Lndy7lKMpA",
															"canonicalBaseUrl": "/@TechCrunch"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "2 months ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "24 seconds"
												}
											},
											"simpleText": "0:24"
										},
										"viewCountText": {
											"simpleText": "578,976 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CFsQnaQHGAYiEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/rpWQbBYpaUw",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "rpWQbBYpaUw",
												"playerParams": "8AEByAMyuAQUogYVAePzwRM4wL6LyHeYXWk_nD7LYXyckAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/rpWQbBYpaUw/frame0.jpg",
															"width": 1080,
															"height": 1920
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CGEQsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgtycFdRYkJZcGFVdyocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CFsQnaQHGAYiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "TechCrunch",
													"navigationEndpoint": {
														"clickTrackingParams": "CFsQnaQHGAYiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@TechCrunch",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCCjyq_K1Xwfg8Lndy7lKMpA",
															"canonicalBaseUrl": "/@TechCrunch"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "TechCrunch",
													"navigationEndpoint": {
														"clickTrackingParams": "CFsQnaQHGAYiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@TechCrunch",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCCjyq_K1Xwfg8Lndy7lKMpA",
															"canonicalBaseUrl": "/@TechCrunch"
														}
													}
												}
											]
										},
										"trackingParams": "CFsQnaQHGAYiEwiVionD5tGKAxUIA60GHT2pFEpAzNKlscGN5MquAQ==",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "578K views"
												}
											},
											"simpleText": "578K views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CGAQ_pgEGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CGAQ_pgEGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "rpWQbBYpaUw",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CGAQ_pgEGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"rpWQbBYpaUw"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"rpWQbBYpaUw"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CGAQ_pgEGA8iEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CF8Q0aoFGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "rpWQbBYpaUw",
																	"onAddCommand": {
																		"clickTrackingParams": "CF8Q0aoFGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "rpWQbBYpaUw",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CF8Q0aoFGBAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CFsQnaQHGAYiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtycFdRYkJZcGFVd1ICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CFsQnaQHGAYiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CF4QjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CFsQnaQHGAYiEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CFsQnaQHGAYiEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/ytc/AIdro_kCWnlG0S5KmFxBckuWUwXOaIsmZL7hBkuXa4CFY27vtk_y=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CFsQnaQHGAYiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@TechCrunch",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCCjyq_K1Xwfg8Lndy7lKMpA",
														"canonicalBaseUrl": "/@TechCrunch"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CF0Q-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "rpWQbBYpaUw",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CF0Q-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "rpWQbBYpaUw"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CF0Q-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CFwQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CFwQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "rpWQbBYpaUw",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CFwQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"rpWQbBYpaUw"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"rpWQbBYpaUw"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CFwQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/rpWQbBYpaUw/mqdefault_6s.webp?du=3000&sqp=CKz-zrsG&rs=AOn4CLB0BUlCcDuKu76hP64D-0ID9xM9cg",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "Elon Musk",
															"bold": true
														},
														{
															"text": " showed off Tesla\'s humanoid robot Optimus once again at the company\'s We, Robot Event, claiming: \\u201cWhatever you\\u00a0..."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CFsQnaQHGAYiEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=rpWQbBYpaUw&pp=YAHIAQHwAQHoBQGiBhUB4_PBEzjAvovId5hdaT-cPsthfJyQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "rpWQbBYpaUw",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCKCqssTpruXa-gG6AwsIhNz-qobIntS7AboDCgir0tnSi7v7qWy6AwsIu73bgemqieX2AboDCwirisWNytL8yYgBugMLCJ7d2cLa367_3QG6AwsIrJaYioeJxqqlAboDCgi54d64k5TmjUK6AwsIy8rcuJedzPegAboDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBEzjAvovId5hdaT-cPsthfJyQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr3---sn-5uaezny6.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=ae95906c1629694c&ip=*************&initcwndbps=1507500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtycFdRYkJZcGFVdyDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/ytc/AIdro_kCWnlG0S5KmFxBckuWUwXOaIsmZL7hBkuXa4CFY27vtk_y=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CFsQnaQHGAYiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@TechCrunch",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCCjyq_K1Xwfg8Lndy7lKMpA",
																	"canonicalBaseUrl": "/@TechCrunch"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "-rWVdpiMlSA",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/-rWVdpiMlSA/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgTShGMA8=&rs=AOn4CLCq57ul8k3SAYIky4QycGqfq9IdTg",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/-rWVdpiMlSA/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgTShGMA8=&rs=AOn4CLCCxPDaJvJLhk5qoaXPxTEVpPOy8A",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Elon Musk pledges \\u00a345M to Trumps re-election"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Elon Musk pledges \\u00a345M to Trumps re-election by Daily Mail 4,399,046 views 5 months ago 19 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "Daily Mail",
													"navigationEndpoint": {
														"clickTrackingParams": "CFQQnaQHGAciEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@dailymail",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCw3fku0sH3qA3c3pZeJwdAw",
															"canonicalBaseUrl": "/@dailymail"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "5 months ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "19 seconds"
												}
											},
											"simpleText": "0:19"
										},
										"viewCountText": {
											"simpleText": "4,399,046 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CFQQnaQHGAciEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/-rWVdpiMlSA",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "-rWVdpiMlSA",
												"playerParams": "8AEByAMyuAQUogYVAePzwRPN6GIml8xlyzTaJxpVRU38kAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/-rWVdpiMlSA/frame0.jpg",
															"width": 1080,
															"height": 1920
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CFoQsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgstcldWZHBpTWxTQSocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CFQQnaQHGAciEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "Daily Mail",
													"navigationEndpoint": {
														"clickTrackingParams": "CFQQnaQHGAciEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@dailymail",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCw3fku0sH3qA3c3pZeJwdAw",
															"canonicalBaseUrl": "/@dailymail"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "Daily Mail",
													"navigationEndpoint": {
														"clickTrackingParams": "CFQQnaQHGAciEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@dailymail",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCw3fku0sH3qA3c3pZeJwdAw",
															"canonicalBaseUrl": "/@dailymail"
														}
													}
												}
											]
										},
										"trackingParams": "CFQQnaQHGAciEwiVionD5tGKAxUIA60GHT2pFEpAoKqyxOmu5dr6AQ==",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "4.3 million views"
												}
											},
											"simpleText": "4.3M views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CFkQ_pgEGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CFkQ_pgEGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "-rWVdpiMlSA",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CFkQ_pgEGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"-rWVdpiMlSA"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"-rWVdpiMlSA"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CFkQ_pgEGBEiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CFgQ0aoFGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "-rWVdpiMlSA",
																	"onAddCommand": {
																		"clickTrackingParams": "CFgQ0aoFGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "-rWVdpiMlSA",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CFgQ0aoFGBIiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CFQQnaQHGAciEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgstcldWZHBpTWxTQVICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CFQQnaQHGAciEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CFcQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CFQQnaQHGAciEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CFQQnaQHGAciEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/8c6erlsLYyXVe5dKHuENfwEyHyplvJYQDeWDdp2t6jcS6KkUnd5w3mjrkhj3blhFTfMZDX9Blg=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CFQQnaQHGAciEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@dailymail",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCw3fku0sH3qA3c3pZeJwdAw",
														"canonicalBaseUrl": "/@dailymail"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CFYQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "-rWVdpiMlSA",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CFYQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "-rWVdpiMlSA"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CFYQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CFUQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CFUQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "-rWVdpiMlSA",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CFUQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"-rWVdpiMlSA"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"-rWVdpiMlSA"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CFUQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/-rWVdpiMlSA/mqdefault_6s.webp?du=3000&sqp=CMCCz7sG&rs=AOn4CLAu3luGhBMq_kJgdjllo-E3-FVAHQ",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "Elon Musk",
															"bold": true
														},
														{
															"text": " pledges \\u00a345M to Trumps re-election #shorts #trump #trumpnews #"
														},
														{
															"text": "elonmusk",
															"bold": true
														},
														{
															"text": " Daily Mail Website:\\u00a0..."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CFQQnaQHGAciEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=-rWVdpiMlSA&pp=YAHIAQHwAQHoBQGiBhUB4_PBE83oYiaXzGXLNNonGlVFTfyQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "-rWVdpiMlSA",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIhNz-qobIntS7AboDCgir0tnSi7v7qWy6AwsIu73bgemqieX2AboDCwirisWNytL8yYgBugMLCJ7d2cLa367_3QG6AwsIrJaYioeJxqqlAboDCgi54d64k5TmjUK6AwsIy8rcuJedzPegAboDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE83oYiaXzGXLNNonGlVFTfyQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr3---sn-5uaeznyz.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=fab59576988c9520&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgstcldWZHBpTWxTQSDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/8c6erlsLYyXVe5dKHuENfwEyHyplvJYQDeWDdp2t6jcS6KkUnd5w3mjrkhj3blhFTfMZDX9Blg=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CFQQnaQHGAciEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@dailymail",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCw3fku0sH3qA3c3pZeJwdAw",
																	"canonicalBaseUrl": "/@dailymail"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "u6h6QGVfrgQ",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/u6h6QGVfrgQ/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AHOBYACgAqKAgwIABABGGUgRSg_MA8=&rs=AOn4CLAkHegRI2Kd81B1Sf_oM37Q5SlWPw",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/u6h6QGVfrgQ/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AHOBYACgAqKAgwIABABGGUgRSg_MA8=&rs=AOn4CLDJjiQyseOZ-3G7bhSdisJk5hs45g",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Mark Cuban TAKES ON Elon Musk"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Mark Cuban TAKES ON Elon Musk by Brian Tyler Cohen 1,705,964 views 3 months ago 52 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "Brian Tyler Cohen",
													"navigationEndpoint": {
														"clickTrackingParams": "CE0QnaQHGAgiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@briantylercohen",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCQANb2YPwAtK-IQJrLaaUFw",
															"canonicalBaseUrl": "/@briantylercohen"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "3 months ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "52 seconds"
												}
											},
											"simpleText": "0:52"
										},
										"viewCountText": {
											"simpleText": "1,705,964 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CE0QnaQHGAgiEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/u6h6QGVfrgQ",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "u6h6QGVfrgQ",
												"playerParams": "8AEByAMyuAQUogYVAePzwRPpUAftJs_btcBj1e8tcGcEkAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/u6h6QGVfrgQ/frame0.jpg",
															"width": 720,
															"height": 1280
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CFMQsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "Cgt1Nmg2UUdWZnJnUSocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CE0QnaQHGAgiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "Brian Tyler Cohen",
													"navigationEndpoint": {
														"clickTrackingParams": "CE0QnaQHGAgiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@briantylercohen",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCQANb2YPwAtK-IQJrLaaUFw",
															"canonicalBaseUrl": "/@briantylercohen"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "Brian Tyler Cohen",
													"navigationEndpoint": {
														"clickTrackingParams": "CE0QnaQHGAgiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@briantylercohen",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCQANb2YPwAtK-IQJrLaaUFw",
															"canonicalBaseUrl": "/@briantylercohen"
														}
													}
												}
											]
										},
										"trackingParams": "CE0QnaQHGAgiEwiVionD5tGKAxUIA60GHT2pFEpAhNz-qobIntS7AQ==",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "1.7 million views"
												}
											},
											"simpleText": "1.7M views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CFIQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CFIQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "u6h6QGVfrgQ",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CFIQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"u6h6QGVfrgQ"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"u6h6QGVfrgQ"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CFIQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CFEQ0aoFGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "u6h6QGVfrgQ",
																	"onAddCommand": {
																		"clickTrackingParams": "CFEQ0aoFGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "u6h6QGVfrgQ",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CFEQ0aoFGA8iEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CE0QnaQHGAgiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "Cgt1Nmg2UUdWZnJnUVICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CE0QnaQHGAgiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CFAQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CE0QnaQHGAgiEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CE0QnaQHGAgiEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/Gn4FidKLelYIcGyfLJXyM25SqKkx_42OdKqhp2nAgqVsnbona6sqOMrF3PD9wQrS-WzXct7FCw=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CE0QnaQHGAgiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@briantylercohen",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCQANb2YPwAtK-IQJrLaaUFw",
														"canonicalBaseUrl": "/@briantylercohen"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CE8Q-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "u6h6QGVfrgQ",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CE8Q-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "u6h6QGVfrgQ"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CE8Q-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CE4Qx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CE4Qx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "u6h6QGVfrgQ",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CE4Qx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"u6h6QGVfrgQ"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"u6h6QGVfrgQ"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CE4Qx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/u6h6QGVfrgQ/mqdefault_6s.webp?du=3000&sqp=CMztzrsG&rs=AOn4CLBcpB6IL5P9KPKrgnrqFf2VbQ7z9g",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "shorts."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CE0QnaQHGAgiEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=u6h6QGVfrgQ&pp=YAHIAQHwAQHoBQGiBhUB4_PBE-lQB-0mz9u1wGPV7y1wZwSQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "u6h6QGVfrgQ",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCgir0tnSi7v7qWy6AwsIu73bgemqieX2AboDCwirisWNytL8yYgBugMLCJ7d2cLa367_3QG6AwsIrJaYioeJxqqlAboDCgi54d64k5TmjUK6AwsIy8rcuJedzPegAboDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE-lQB-0mz9u1wGPV7y1wZwSQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr2---sn-5uaeznze.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=bba87a40655fae04&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "Egt1Nmg2UUdWZnJnUSDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/Gn4FidKLelYIcGyfLJXyM25SqKkx_42OdKqhp2nAgqVsnbona6sqOMrF3PD9wQrS-WzXct7FCw=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CE0QnaQHGAgiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@briantylercohen",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCQANb2YPwAtK-IQJrLaaUFw",
																	"canonicalBaseUrl": "/@briantylercohen"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "bFPt2LpWaSs",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/bFPt2LpWaSs/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AHOBYACgAqKAgwIABABGEIgUShlMA8=&rs=AOn4CLDBgB0bSDwMjeLTnnKpZQ_RsfSY4g",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/bFPt2LpWaSs/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AHOBYACgAqKAgwIABABGEIgUShlMA8=&rs=AOn4CLBjTA_xjtTLZ9TPd9OJFb4pprAPWg",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Elon Musk joins Donald Trump on stage in Butler"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Elon Musk joins Donald Trump on stage in Butler by Haber L\\u00fctfen 12,090,033 views 2 months ago 29 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "Haber L\\u00fctfen",
													"navigationEndpoint": {
														"clickTrackingParams": "CEYQnaQHGAkiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@HaberL%C3%BCtfen",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UC3ELm5o92cfi-h43u4Nse7w",
															"canonicalBaseUrl": "/@HaberL%C3%BCtfen"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "2 months ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "29 seconds"
												}
											},
											"simpleText": "0:29"
										},
										"viewCountText": {
											"simpleText": "12,090,033 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CEYQnaQHGAkiEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/bFPt2LpWaSs",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "bFPt2LpWaSs",
												"playerParams": "8AEByAMyuAQUogYVAePzwRNnwe53ZPFXxlX4j5LaJC3VkAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/bFPt2LpWaSs/frame0.jpg",
															"width": 720,
															"height": 1280
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CEwQsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgtiRlB0MkxwV2FTcyocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerText": {
											"runs": [
												{
													"text": "Haber L\\u00fctfen",
													"navigationEndpoint": {
														"clickTrackingParams": "CEYQnaQHGAkiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@HaberL%C3%BCtfen",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UC3ELm5o92cfi-h43u4Nse7w",
															"canonicalBaseUrl": "/@HaberL%C3%BCtfen"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "Haber L\\u00fctfen",
													"navigationEndpoint": {
														"clickTrackingParams": "CEYQnaQHGAkiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@HaberL%C3%BCtfen",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UC3ELm5o92cfi-h43u4Nse7w",
															"canonicalBaseUrl": "/@HaberL%C3%BCtfen"
														}
													}
												}
											]
										},
										"trackingParams": "CEYQnaQHGAkiEwiVionD5tGKAxUIA60GHT2pFEpAq9LZ0ou7-6ls",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "12 million views"
												}
											},
											"simpleText": "12M views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CEsQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CEsQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "bFPt2LpWaSs",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CEsQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"bFPt2LpWaSs"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"bFPt2LpWaSs"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CEsQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CEoQ0aoFGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "bFPt2LpWaSs",
																	"onAddCommand": {
																		"clickTrackingParams": "CEoQ0aoFGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "bFPt2LpWaSs",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CEoQ0aoFGA8iEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CEYQnaQHGAkiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtiRlB0MkxwV2FTc1ICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CEYQnaQHGAkiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CEkQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CEYQnaQHGAkiEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CEYQnaQHGAkiEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/ytc/AIdro_nK-8PFNaDwVLXq74vGWcDAXY0y1oaFdZOT2fawtRSUV7k=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CEYQnaQHGAkiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@HaberL%C3%BCtfen",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UC3ELm5o92cfi-h43u4Nse7w",
														"canonicalBaseUrl": "/@HaberL%C3%BCtfen"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CEgQ-ecDGAEiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "bFPt2LpWaSs",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CEgQ-ecDGAEiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "bFPt2LpWaSs"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CEgQ-ecDGAEiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CEcQx-wEGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CEcQx-wEGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "bFPt2LpWaSs",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CEcQx-wEGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"bFPt2LpWaSs"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"bFPt2LpWaSs"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CEcQx-wEGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/bFPt2LpWaSs/mqdefault_6s.webp?du=3000&sqp=CIz7zrsG&rs=AOn4CLDPjwyTZBhRYEdz3tdQJ7C_DKoeQg",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "Elon Musk",
															"bold": true
														},
														{
															"text": " joined Donald Trump at his rally Saturday in Butler. Video Credit: Margo Martin."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CEYQnaQHGAkiEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=bFPt2LpWaSs&pp=YAHIAQHwAQHoBQGiBhUB4_PBE2fB7ndk8VfGVfiPktokLdWQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "bFPt2LpWaSs",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMLCLu924Hpqonl9gG6AwsIq4rFjcrS_MmIAboDCwie3dnC2t-u_90BugMLCKyWmIqHicaqpQG6AwoIueHeuJOU5o1CugMLCMvK3LiXncz3oAG6AwoIp6qBjPDwv-JYugMKCPmyzYeInbL0GroDCgiug-GbkICfhWe6AwoIo7PIsbvrm8pG8gMFDSXK9D4%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE2fB7ndk8VfGVfiPktokLdWQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr4---sn-5uaeznls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=6c53edd8ba56692b&ip=*************&initcwndbps=1507500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtiRlB0MkxwV2FTcyDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/ytc/AIdro_nK-8PFNaDwVLXq74vGWcDAXY0y1oaFdZOT2fawtRSUV7k=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CEYQnaQHGAkiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@HaberL%C3%BCtfen",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UC3ELm5o92cfi-h43u4Nse7w",
																	"canonicalBaseUrl": "/@HaberL%C3%BCtfen"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "9solVpA23rs",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/9solVpA23rs/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLD1OzMOyDIfsLBx2HoEoIC-ZgTsDw",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/9solVpA23rs/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLC3LG5yI6HLy05jXrSPyh6eExoMYw",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Trump Escalates MAGA Civil War By Siding With Elon Musk Over Steve Bannon"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Trump Escalates MAGA Civil War By Siding With Elon Musk Over Steve Bannon by The Ring of Fire 100,188 views 16 hours ago 8 minutes, 6 seconds"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "The Ring of Fire",
													"navigationEndpoint": {
														"clickTrackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSg==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@TheRingofFire",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCYWIEbibRcZav6xMLo9qWWw",
															"canonicalBaseUrl": "/@TheRingofFire"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "16 hours ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "8 minutes, 6 seconds"
												}
											},
											"simpleText": "8:06"
										},
										"viewCountText": {
											"simpleText": "100,188 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSjIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=9solVpA23rs&pp=ygUJRWxvbiBNdXNr",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "9solVpA23rs",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMKCKvS2dKLu_upbLoDCwirisWNytL8yYgBugMLCJ7d2cLa367_3QG6AwsIrJaYioeJxqqlAboDCgi54d64k5TmjUK6AwsIy8rcuJedzPegAboDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "ygUJRWxvbiBNdXNr",
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr5---sn-5uaeznl6.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=f6ca25569036debb&ip=*************&initcwndbps=1507500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"badges": [
											{
												"metadataBadgeRenderer": {
													"style": "BADGE_STYLE_TYPE_SIMPLE",
													"label": "New",
													"trackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSg=="
												}
											},
											{
												"metadataBadgeRenderer": {
													"style": "BADGE_STYLE_TYPE_SIMPLE",
													"label": "CC",
													"trackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSg==",
													"accessibilityData": {
														"label": "Closed captions"
													}
												}
											}
										],
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSg==",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "The Ring of Fire",
													"navigationEndpoint": {
														"clickTrackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSg==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@TheRingofFire",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCYWIEbibRcZav6xMLo9qWWw",
															"canonicalBaseUrl": "/@TheRingofFire"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "The Ring of Fire",
													"navigationEndpoint": {
														"clickTrackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSg==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@TheRingofFire",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCYWIEbibRcZav6xMLo9qWWw",
															"canonicalBaseUrl": "/@TheRingofFire"
														}
													}
												}
											]
										},
										"trackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSkC7vduB6aqJ5fYB",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "100K views"
												}
											},
											"simpleText": "100K views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CEUQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CEUQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "9solVpA23rs",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CEUQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"9solVpA23rs"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"9solVpA23rs"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CEUQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CEQQ0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "9solVpA23rs",
																	"onAddCommand": {
																		"clickTrackingParams": "CEQQ0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "9solVpA23rs",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CEQQ0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSg==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "Cgs5c29sVnBBMjNycw%3D%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSg==",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CEMQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSg==",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSg==",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/jJwLtO9uiqKO2yQZ1q1M1556ACegIF9Wy-ocssYVuK3_q4JPMG-86y_-YTlat7_3Xmp-k9oyDw=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSg==",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@TheRingofFire",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCYWIEbibRcZav6xMLo9qWWw",
														"canonicalBaseUrl": "/@TheRingofFire"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "8 minutes, 6 seconds"
															}
														},
														"simpleText": "8:06"
													},
													"style": "DEFAULT"
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CEIQ-ecDGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "9solVpA23rs",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CEIQ-ecDGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "9solVpA23rs"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CEIQ-ecDGAQiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CEEQx-wEGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CEEQx-wEGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "9solVpA23rs",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CEEQx-wEGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"9solVpA23rs"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"9solVpA23rs"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CEEQx-wEGAUiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/9solVpA23rs/mqdefault_6s.webp?du=3000&sqp=CIyLz7sG&rs=AOn4CLDja6jtNKUyaJbyJ2oxa-p6HRMNAw",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "Go to https://ground.news/rof to see through media bias and know where your news is coming from. Sign up through my link to get\\u00a0..."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSjIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=9solVpA23rs&pp=YAHIAQE%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "9solVpA23rs",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMKCKvS2dKLu_upbLoDCwirisWNytL8yYgBugMLCJ7d2cLa367_3QG6AwsIrJaYioeJxqqlAboDCgi54d64k5TmjUK6AwsIy8rcuJedzPegAboDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "YAHIAQE%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr5---sn-5uaeznl6.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=f6ca25569036debb&ip=*************&initcwndbps=1507500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "Egs5c29sVnBBMjNycyDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/jJwLtO9uiqKO2yQZ1q1M1556ACegIF9Wy-ocssYVuK3_q4JPMG-86y_-YTlat7_3Xmp-k9oyDw=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CEAQ3DAYCiITCJWKicPm0YoDFQgDrQYdPakUSg==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@TheRingofFire",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCYWIEbibRcZav6xMLo9qWWw",
																	"canonicalBaseUrl": "/@TheRingofFire"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "iJPylKGxRSs",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/iJPylKGxRSs/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGH8gPSgeMA8=&rs=AOn4CLD6WlVwSsiyuBG-gT7KV9Sxv_oW9w",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/iJPylKGxRSs/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGH8gPSgeMA8=&rs=AOn4CLDJlTRZYUc5OH3_WPoqdRWZZomW7w",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "What it\\u2019s like having a conversation with Elon Musk #JoeRoganLive"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "What it\\u2019s like having a conversation with Elon Musk #JoeRoganLive by Netflix Is A Joke 257,096 views 4 months ago 17 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "Netflix Is A Joke",
													"navigationEndpoint": {
														"clickTrackingParams": "CDkQnaQHGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@netflixisajoke",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCObk_g1hQBy0RKKriVX_zOQ",
															"canonicalBaseUrl": "/@netflixisajoke"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "4 months ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "17 seconds"
												}
											},
											"simpleText": "0:17"
										},
										"viewCountText": {
											"simpleText": "257,096 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CDkQnaQHGAsiEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/iJPylKGxRSs",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "iJPylKGxRSs",
												"playerParams": "8AEByAMyuAQUogYVAePzwRMQGTO5M-USuX-4yTQoaLOpkAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/iJPylKGxRSs/frame0.jpg",
															"width": 1080,
															"height": 1920
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CD8QsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgtpSlB5bEtHeFJTcyocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CDkQnaQHGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "Netflix Is A Joke",
													"navigationEndpoint": {
														"clickTrackingParams": "CDkQnaQHGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@netflixisajoke",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCObk_g1hQBy0RKKriVX_zOQ",
															"canonicalBaseUrl": "/@netflixisajoke"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "Netflix Is A Joke",
													"navigationEndpoint": {
														"clickTrackingParams": "CDkQnaQHGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@netflixisajoke",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCObk_g1hQBy0RKKriVX_zOQ",
															"canonicalBaseUrl": "/@netflixisajoke"
														}
													}
												}
											]
										},
										"trackingParams": "CDkQnaQHGAsiEwiVionD5tGKAxUIA60GHT2pFEpAq4rFjcrS_MmIAQ==",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "257K views"
												}
											},
											"simpleText": "257K views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CD4Q_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CD4Q_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "iJPylKGxRSs",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CD4Q_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"iJPylKGxRSs"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"iJPylKGxRSs"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CD4Q_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CD0Q0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "iJPylKGxRSs",
																	"onAddCommand": {
																		"clickTrackingParams": "CD0Q0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "iJPylKGxRSs",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CD0Q0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CDkQnaQHGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtpSlB5bEtHeFJTc1ICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CDkQnaQHGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CDwQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CDkQnaQHGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CDkQnaQHGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/3CYVzLztKZ_nGzrs6UTXwKr5W-RUe5uIUuJoHno6HgUVj-KpbJ8_u-6MveD3k-AZXEOdWpUPSw=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CDkQnaQHGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@netflixisajoke",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCObk_g1hQBy0RKKriVX_zOQ",
														"canonicalBaseUrl": "/@netflixisajoke"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CDsQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "iJPylKGxRSs",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CDsQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "iJPylKGxRSs"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CDsQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CDoQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CDoQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "iJPylKGxRSs",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CDoQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"iJPylKGxRSs"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"iJPylKGxRSs"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CDoQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/iJPylKGxRSs/mqdefault_6s.webp?du=3000&sqp=CKSCz7sG&rs=AOn4CLB3zTNBT_slWtj1zWZX9R5g9KNHgA",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "Joe Rogan explains what it\'s like talking to "
														},
														{
															"text": "Elon Musk",
															"bold": true
														},
														{
															"text": " in his new special, Joe Rogan: Burn the Boats now playing only on Netflix."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CDkQnaQHGAsiEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=iJPylKGxRSs&pp=YAHIAQHwAQHoBQGiBhUB4_PBExAZM7kz5RK5f7jJNChos6mQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "iJPylKGxRSs",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMKCKvS2dKLu_upbLoDCwi7vduB6aqJ5fYBugMLCJ7d2cLa367_3QG6AwsIrJaYioeJxqqlAboDCgi54d64k5TmjUK6AwsIy8rcuJedzPegAboDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBExAZM7kz5RK5f7jJNChos6mQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr2---sn-5uaezned.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=8893f294a1b1452b&ip=*************&initcwndbps=1507500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtpSlB5bEtHeFJTcyDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/3CYVzLztKZ_nGzrs6UTXwKr5W-RUe5uIUuJoHno6HgUVj-KpbJ8_u-6MveD3k-AZXEOdWpUPSw=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CDkQnaQHGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@netflixisajoke",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCObk_g1hQBy0RKKriVX_zOQ",
																	"canonicalBaseUrl": "/@netflixisajoke"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "3f66_ahWbp4",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/3f66_ahWbp4/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AHOBYACgAqKAgwIABABGHIgVyg6MA8=&rs=AOn4CLBDlV0D2Cmk_WuxxyKYPBWL9gvBNw",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/3f66_ahWbp4/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AHOBYACgAqKAgwIABABGHIgVyg6MA8=&rs=AOn4CLB41iLJ2BO0hOHHy-nzVLUSmP18ig",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "See Elon Musk\'s 2-year-old son X \\ud83d\\ude07 #shorts"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "See Elon Musk\'s 2-year-old son X \\ud83d\\ude07 #shorts by Access Hollywood 491,699 views 1 year ago 19 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "Access Hollywood",
													"navigationEndpoint": {
														"clickTrackingParams": "CDIQnaQHGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@AccessHollywood",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCiKGMZZmZXK-RpbKJGXgH3Q",
															"canonicalBaseUrl": "/@AccessHollywood"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "1 year ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "19 seconds"
												}
											},
											"simpleText": "0:19"
										},
										"viewCountText": {
											"simpleText": "491,699 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CDIQnaQHGAwiEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/3f66_ahWbp4",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "3f66_ahWbp4",
												"playerParams": "8AEByAMyuAQUogYVAePzwRN74x2gflM67_nQGIcB5tt5kAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/3f66_ahWbp4/frame0.jpg",
															"width": 720,
															"height": 1280
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CDgQsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgszZjY2X2FoV2JwNCocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CDIQnaQHGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "Access Hollywood",
													"navigationEndpoint": {
														"clickTrackingParams": "CDIQnaQHGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@AccessHollywood",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCiKGMZZmZXK-RpbKJGXgH3Q",
															"canonicalBaseUrl": "/@AccessHollywood"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "Access Hollywood",
													"navigationEndpoint": {
														"clickTrackingParams": "CDIQnaQHGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@AccessHollywood",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCiKGMZZmZXK-RpbKJGXgH3Q",
															"canonicalBaseUrl": "/@AccessHollywood"
														}
													}
												}
											]
										},
										"trackingParams": "CDIQnaQHGAwiEwiVionD5tGKAxUIA60GHT2pFEpAnt3Zwtrfrv_dAQ==",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "491K views"
												}
											},
											"simpleText": "491K views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CDcQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CDcQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "3f66_ahWbp4",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CDcQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"3f66_ahWbp4"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"3f66_ahWbp4"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CDcQ_pgEGA4iEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CDYQ0aoFGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "3f66_ahWbp4",
																	"onAddCommand": {
																		"clickTrackingParams": "CDYQ0aoFGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "3f66_ahWbp4",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CDYQ0aoFGA8iEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CDIQnaQHGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgszZjY2X2FoV2JwNFICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CDIQnaQHGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CDUQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CDIQnaQHGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CDIQnaQHGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/3Ffp0n-8Kw4gT52GBSuItqgwX7Vul876e3JeMRr9Cf1JcUIRSOr8HdR6UZDhDzK-h_9X3Y6Ufeg=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CDIQnaQHGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@AccessHollywood",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCiKGMZZmZXK-RpbKJGXgH3Q",
														"canonicalBaseUrl": "/@AccessHollywood"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CDQQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "3f66_ahWbp4",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CDQQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "3f66_ahWbp4"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CDQQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CDMQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CDMQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "3f66_ahWbp4",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CDMQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"3f66_ahWbp4"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"3f66_ahWbp4"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CDMQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "Elon Musk",
															"bold": true
														},
														{
															"text": " is showing his youngest son X \\u00c6 A-XII all about the family business! The 51-year-old SpaceX founder was spotted at\\u00a0..."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CDIQnaQHGAwiEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=3f66_ahWbp4&pp=YAHIAQHwAQHoBQGiBhUB4_PBE3vjHaB-Uzrv-dAYhwHm23mQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "3f66_ahWbp4",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMKCKvS2dKLu_upbLoDCwi7vduB6aqJ5fYBugMLCKuKxY3K0vzJiAG6AwsIrJaYioeJxqqlAboDCgi54d64k5TmjUK6AwsIy8rcuJedzPegAboDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE3vjHaB-Uzrv-dAYhwHm23mQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr3---sn-5uaezne6.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=ddfebafda8566e9e&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgszZjY2X2FoV2JwNCDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/3Ffp0n-8Kw4gT52GBSuItqgwX7Vul876e3JeMRr9Cf1JcUIRSOr8HdR6UZDhDzK-h_9X3Y6Ufeg=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CDIQnaQHGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@AccessHollywood",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCiKGMZZmZXK-RpbKJGXgH3Q",
																	"canonicalBaseUrl": "/@AccessHollywood"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "pVUYSHFGCyw",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/pVUYSHFGCyw/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AHOBYACgAqKAgwIABABGHIgSyg5MA8=&rs=AOn4CLCs3gTGFCr-xjosboC0G0sv8xCoeA",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/pVUYSHFGCyw/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AHOBYACgAqKAgwIABABGHIgSyg5MA8=&rs=AOn4CLBCWf3AbuzBI0Zyg_9hW7O3Yetu9w",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Elon Musk denies report that he would be donating $45 million a month to Trump campaign #shorts"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Elon Musk denies report that he would be donating $45 million a month to Trump campaign #shorts by CBS News 2,149,731 views 5 months ago 18 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "CBS News",
													"navigationEndpoint": {
														"clickTrackingParams": "CCsQnaQHGA0iEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@CBSNews",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UC8p1vwvWtl6T73JiExfWs1g",
															"canonicalBaseUrl": "/@CBSNews"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "5 months ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "18 seconds"
												}
											},
											"simpleText": "0:18"
										},
										"viewCountText": {
											"simpleText": "2,149,731 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CCsQnaQHGA0iEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/pVUYSHFGCyw",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "pVUYSHFGCyw",
												"playerParams": "8AEByAMyuAQUogYVAePzwRPHCYc346pNG-q5X9CTw_IpkAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/pVUYSHFGCyw/frame0.jpg",
															"width": 720,
															"height": 1280
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CDEQsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgtwVlVZU0hGR0N5dyocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CCsQnaQHGA0iEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "CBS News",
													"navigationEndpoint": {
														"clickTrackingParams": "CCsQnaQHGA0iEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@CBSNews",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UC8p1vwvWtl6T73JiExfWs1g",
															"canonicalBaseUrl": "/@CBSNews"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "CBS News",
													"navigationEndpoint": {
														"clickTrackingParams": "CCsQnaQHGA0iEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@CBSNews",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UC8p1vwvWtl6T73JiExfWs1g",
															"canonicalBaseUrl": "/@CBSNews"
														}
													}
												}
											]
										},
										"trackingParams": "CCsQnaQHGA0iEwiVionD5tGKAxUIA60GHT2pFEpArJaYioeJxqqlAQ==",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "2.1 million views"
												}
											},
											"simpleText": "2.1M views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CDAQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CDAQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "pVUYSHFGCyw",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CDAQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"pVUYSHFGCyw"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"pVUYSHFGCyw"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CDAQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CC8Q0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "pVUYSHFGCyw",
																	"onAddCommand": {
																		"clickTrackingParams": "CC8Q0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "pVUYSHFGCyw",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CC8Q0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CCsQnaQHGA0iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtwVlVZU0hGR0N5d1ICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CCsQnaQHGA0iEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CC4QjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CCsQnaQHGA0iEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CCsQnaQHGA0iEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/ytc/AIdro_nlnjM_fZG5f-TKrO9NVjXn0FWSw_MjlyAV13KEtO-959Y=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CCsQnaQHGA0iEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@CBSNews",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UC8p1vwvWtl6T73JiExfWs1g",
														"canonicalBaseUrl": "/@CBSNews"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CC0Q-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "pVUYSHFGCyw",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CC0Q-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "pVUYSHFGCyw"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CC0Q-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CCwQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CCwQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "pVUYSHFGCyw",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CCwQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"pVUYSHFGCyw"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"pVUYSHFGCyw"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CCwQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/pVUYSHFGCyw/mqdefault_6s.webp?du=3000&sqp=COzfzrsG&rs=AOn4CLDY7-g4rpTKLHb8BxOQPtBzyWKGuA",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "news #"
														},
														{
															"text": "elonmusk",
															"bold": true
														},
														{
															"text": " #trump."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CCsQnaQHGA0iEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=pVUYSHFGCyw&pp=YAHIAQHwAQHoBQGiBhUB4_PBE8cJhzfjqk0b6rlf0JPD8imQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "pVUYSHFGCyw",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMKCKvS2dKLu_upbLoDCwi7vduB6aqJ5fYBugMLCKuKxY3K0vzJiAG6AwsInt3Zwtrfrv_dAboDCgi54d64k5TmjUK6AwsIy8rcuJedzPegAboDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE8cJhzfjqk0b6rlf0JPD8imQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr4---sn-5uaeznez.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=a555184871460b2c&ip=*************&initcwndbps=1507500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtwVlVZU0hGR0N5dyDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/ytc/AIdro_nlnjM_fZG5f-TKrO9NVjXn0FWSw_MjlyAV13KEtO-959Y=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CCsQnaQHGA0iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@CBSNews",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UC8p1vwvWtl6T73JiExfWs1g",
																	"canonicalBaseUrl": "/@CBSNews"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "QhuYoTcXsLk",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/QhuYoTcXsLk/hq720.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGFogZShUMA8=&rs=AOn4CLBSLQrzdhZ-e7e_mRCa-KKjFrWwhQ",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/QhuYoTcXsLk/hq720.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGFogZShUMA8=&rs=AOn4CLDI8PtppRV5jgBQvBs-J7livpN6GA",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Was that a glitch, or did Trump just tell Elon Musk he wants to close the Department of Education?"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Was that a glitch, or did Trump just tell Elon Musk he wants to close the Department of Education? by The Daily Show 4,170,605 views 4 months ago 57 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "The Daily Show",
													"navigationEndpoint": {
														"clickTrackingParams": "CCQQnaQHGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@TheDailyShow",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCwWhs_6x42TyRM4Wstoq8HA",
															"canonicalBaseUrl": "/@TheDailyShow"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "4 months ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "57 seconds"
												}
											},
											"simpleText": "0:57"
										},
										"viewCountText": {
											"simpleText": "4,170,605 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CCQQnaQHGA4iEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/QhuYoTcXsLk",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "QhuYoTcXsLk",
												"playerParams": "8AEByAMyuAQUogYVAePzwRMQHzYwa72Y9RwiCJbhSupXkAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/QhuYoTcXsLk/frame0.jpg",
															"width": 1080,
															"height": 1920
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CCoQsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgtRaHVZb1RjWHNMayocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CCQQnaQHGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "The Daily Show",
													"navigationEndpoint": {
														"clickTrackingParams": "CCQQnaQHGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@TheDailyShow",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCwWhs_6x42TyRM4Wstoq8HA",
															"canonicalBaseUrl": "/@TheDailyShow"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "The Daily Show",
													"navigationEndpoint": {
														"clickTrackingParams": "CCQQnaQHGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@TheDailyShow",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCwWhs_6x42TyRM4Wstoq8HA",
															"canonicalBaseUrl": "/@TheDailyShow"
														}
													}
												}
											]
										},
										"trackingParams": "CCQQnaQHGA4iEwiVionD5tGKAxUIA60GHT2pFEpAueHeuJOU5o1C",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "4.1 million views"
												}
											},
											"simpleText": "4.1M views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CCkQ_pgEGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CCkQ_pgEGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "QhuYoTcXsLk",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CCkQ_pgEGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"QhuYoTcXsLk"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"QhuYoTcXsLk"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CCkQ_pgEGAsiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CCgQ0aoFGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "QhuYoTcXsLk",
																	"onAddCommand": {
																		"clickTrackingParams": "CCgQ0aoFGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "QhuYoTcXsLk",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CCgQ0aoFGAwiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CCQQnaQHGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtRaHVZb1RjWHNMa1ICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CCQQnaQHGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CCcQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CCQQnaQHGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CCQQnaQHGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/vfgTwN2vtrIFe7jfON70eXNw9BH0LLujtyXs6Oc8OvvISFb9i-Bn4Bxxzkos_BwCxHjdytYa5g=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CCQQnaQHGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@TheDailyShow",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCwWhs_6x42TyRM4Wstoq8HA",
														"canonicalBaseUrl": "/@TheDailyShow"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CCYQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "QhuYoTcXsLk",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CCYQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "QhuYoTcXsLk"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CCYQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CCUQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CCUQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "QhuYoTcXsLk",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CCUQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"QhuYoTcXsLk"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"QhuYoTcXsLk"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CCUQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/QhuYoTcXsLk/mqdefault_6s.webp?du=3000&sqp=CJvzzrsG&rs=AOn4CLBgoN0mNJcyGLLUOW5cFlrz_KRxRg",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CCQQnaQHGA4iEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=QhuYoTcXsLk&pp=YAHIAQHwAQHoBQGiBhUB4_PBExAfNjBrvZj1HCIIluFK6leQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "QhuYoTcXsLk",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMKCKvS2dKLu_upbLoDCwi7vduB6aqJ5fYBugMLCKuKxY3K0vzJiAG6AwsInt3Zwtrfrv_dAboDCwislpiKh4nGqqUBugMLCMvK3LiXncz3oAG6AwoIp6qBjPDwv-JYugMKCPmyzYeInbL0GroDCgiug-GbkICfhWe6AwoIo7PIsbvrm8pG8gMFDSXK9D4%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBExAfNjBrvZj1HCIIluFK6leQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr1---sn-5ualdnls.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=421b98a13717b0b9&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtRaHVZb1RjWHNMayDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/vfgTwN2vtrIFe7jfON70eXNw9BH0LLujtyXs6Oc8OvvISFb9i-Bn4Bxxzkos_BwCxHjdytYa5g=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CCQQnaQHGA4iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@TheDailyShow",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCwWhs_6x42TyRM4Wstoq8HA",
																	"canonicalBaseUrl": "/@TheDailyShow"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "oO8w6XcXJUs",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/oO8w6XcXJUs/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLBZqupI4mC5_N1A9C_pNKT9CzGHsQ",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/oO8w6XcXJUs/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLATcBFaMlrlRtgqi2_n95Esl-SqRA",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Elon Musk (Full Interview) | Real Time with Bill Maher (HBO)"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Elon Musk (Full Interview) | Real Time with Bill Maher (HBO) by Real Time with Bill Maher 8,621,656 views 1 year ago 21 minutes"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "Real Time with Bill Maher",
													"navigationEndpoint": {
														"clickTrackingParams": "CB4Q3DAYDyITCJWKicPm0YoDFQgDrQYdPakUSg==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@RealTime",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCy6kyFxaMqGtpE3pQTflK8A",
															"canonicalBaseUrl": "/@RealTime"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "1 year ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "21 minutes, 21 seconds"
												}
											},
											"simpleText": "21:21"
										},
										"viewCountText": {
											"simpleText": "8,621,656 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CB4Q3DAYDyITCJWKicPm0YoDFQgDrQYdPakUSjIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=oO8w6XcXJUs&pp=ygUJRWxvbiBNdXNr",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "oO8w6XcXJUs",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMKCKvS2dKLu_upbLoDCwi7vduB6aqJ5fYBugMLCKuKxY3K0vzJiAG6AwsInt3Zwtrfrv_dAboDCwislpiKh4nGqqUBugMKCLnh3riTlOaNQroDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "ygUJRWxvbiBNdXNr",
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr5---sn-5uaeznsl.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=a0ef30e97717254b&ip=*************&initcwndbps=1507500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CB4Q3DAYDyITCJWKicPm0YoDFQgDrQYdPakUSg==",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "Real Time with Bill Maher",
													"navigationEndpoint": {
														"clickTrackingParams": "CB4Q3DAYDyITCJWKicPm0YoDFQgDrQYdPakUSg==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@RealTime",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCy6kyFxaMqGtpE3pQTflK8A",
															"canonicalBaseUrl": "/@RealTime"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "Real Time with Bill Maher",
													"navigationEndpoint": {
														"clickTrackingParams": "CB4Q3DAYDyITCJWKicPm0YoDFQgDrQYdPakUSg==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@RealTime",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCy6kyFxaMqGtpE3pQTflK8A",
															"canonicalBaseUrl": "/@RealTime"
														}
													}
												}
											]
										},
										"trackingParams": "CB4Q3DAYDyITCJWKicPm0YoDFQgDrQYdPakUSkDLyty4l53M96AB",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "8.6 million views"
												}
											},
											"simpleText": "8.6M views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CCMQ_pgEGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CCMQ_pgEGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "oO8w6XcXJUs",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CCMQ_pgEGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"oO8w6XcXJUs"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"oO8w6XcXJUs"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CCMQ_pgEGA8iEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CCIQ0aoFGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "oO8w6XcXJUs",
																	"onAddCommand": {
																		"clickTrackingParams": "CCIQ0aoFGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "oO8w6XcXJUs",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CCIQ0aoFGBAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CB4Q3DAYDyITCJWKicPm0YoDFQgDrQYdPakUSg==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtvTzh3NlhjWEpVcw%3D%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CB4Q3DAYDyITCJWKicPm0YoDFQgDrQYdPakUSg==",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CCEQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CB4Q3DAYDyITCJWKicPm0YoDFQgDrQYdPakUSg==",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CB4Q3DAYDyITCJWKicPm0YoDFQgDrQYdPakUSg==",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/SRI-9q3ozvwDbzOkZ1HcGlFyxeUOYO83d5A57reaahM-RLJIF3wf7c0KTcsBQJ5V-dES6Df2=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CB4Q3DAYDyITCJWKicPm0YoDFQgDrQYdPakUSg==",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@RealTime",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCy6kyFxaMqGtpE3pQTflK8A",
														"canonicalBaseUrl": "/@RealTime"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "21 minutes, 21 seconds"
															}
														},
														"simpleText": "21:21"
													},
													"style": "DEFAULT"
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CCAQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "oO8w6XcXJUs",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CCAQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "oO8w6XcXJUs"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CCAQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CB8Qx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CB8Qx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "oO8w6XcXJUs",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CB8Qx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"oO8w6XcXJUs"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"oO8w6XcXJUs"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CB8Qx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/oO8w6XcXJUs/mqdefault_6s.webp?du=3000&sqp=COD9zrsG&rs=AOn4CLAXJkYjDfpI7VxLCs9hZSxi1yS6jQ",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "Elon Musk",
															"bold": true
														},
														{
															"text": " joins Bill for an exclusive Real Time interview."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CB4Q3DAYDyITCJWKicPm0YoDFQgDrQYdPakUSjIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=oO8w6XcXJUs&pp=YAHIAQE%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "oO8w6XcXJUs",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMKCKvS2dKLu_upbLoDCwi7vduB6aqJ5fYBugMLCKuKxY3K0vzJiAG6AwsInt3Zwtrfrv_dAboDCwislpiKh4nGqqUBugMKCLnh3riTlOaNQroDCginqoGM8PC_4li6AwoI-bLNh4idsvQaugMKCK6D4ZuQgJ-FZ7oDCgijs8ixu-ubykbyAwUNJcr0Pg%3D%3D",
												"playerParams": "YAHIAQE%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr5---sn-5uaeznsl.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=a0ef30e97717254b&ip=*************&initcwndbps=1507500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtvTzh3NlhjWEpVcyDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/SRI-9q3ozvwDbzOkZ1HcGlFyxeUOYO83d5A57reaahM-RLJIF3wf7c0KTcsBQJ5V-dES6Df2=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CB4Q3DAYDyITCJWKicPm0YoDFQgDrQYdPakUSg==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@RealTime",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCy6kyFxaMqGtpE3pQTflK8A",
																	"canonicalBaseUrl": "/@RealTime"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "WMT_hwGAVSc",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/WMT_hwGAVSc/hq720.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGGUgWyhTMA8=&rs=AOn4CLAH8QEoSuzQgpebHkcaTt9YnbjGqA",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/WMT_hwGAVSc/hq720.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGGUgWyhTMA8=&rs=AOn4CLCi_lrroca5YvUTZ-mZVKW-j_Hl7A",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Sheikh Mohammed Bin Rashid Al Maktoum Sheikh Hamdan Sheikh Maktoum Met By Elon Musk #shorts #dubai"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Sheikh Mohammed Bin Rashid Al Maktoum Sheikh Hamdan Sheikh Maktoum Met By Elon Musk #shorts #dubai by Rasgulla Reddy 24,014,632 views 6 months ago 13 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "Rasgulla Reddy",
													"navigationEndpoint": {
														"clickTrackingParams": "CBcQnaQHGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@RasgullaReddy",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCrElD57N2ee3FfwSiiiocSg",
															"canonicalBaseUrl": "/@RasgullaReddy"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "6 months ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "13 seconds"
												}
											},
											"simpleText": "0:13"
										},
										"viewCountText": {
											"simpleText": "24,014,632 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CBcQnaQHGBAiEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/WMT_hwGAVSc",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "WMT_hwGAVSc",
												"playerParams": "8AEByAMyuAQUogYVAePzwROltl3oLgj4gZZsNqJ7p2a3kAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/WMT_hwGAVSc/frame0.jpg",
															"width": 1080,
															"height": 1920
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CB0QsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgtXTVRfaHdHQVZTYyocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CBcQnaQHGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "Rasgulla Reddy",
													"navigationEndpoint": {
														"clickTrackingParams": "CBcQnaQHGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@RasgullaReddy",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCrElD57N2ee3FfwSiiiocSg",
															"canonicalBaseUrl": "/@RasgullaReddy"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "Rasgulla Reddy",
													"navigationEndpoint": {
														"clickTrackingParams": "CBcQnaQHGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@RasgullaReddy",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCrElD57N2ee3FfwSiiiocSg",
															"canonicalBaseUrl": "/@RasgullaReddy"
														}
													}
												}
											]
										},
										"trackingParams": "CBcQnaQHGBAiEwiVionD5tGKAxUIA60GHT2pFEpAp6qBjPDwv-JY",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "24 million views"
												}
											},
											"simpleText": "24M views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CBwQ_pgEGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CBwQ_pgEGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "WMT_hwGAVSc",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CBwQ_pgEGAsiEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"WMT_hwGAVSc"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"WMT_hwGAVSc"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CBwQ_pgEGAsiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CBsQ0aoFGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "WMT_hwGAVSc",
																	"onAddCommand": {
																		"clickTrackingParams": "CBsQ0aoFGAwiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "WMT_hwGAVSc",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CBsQ0aoFGAwiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CBcQnaQHGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtXTVRfaHdHQVZTY1ICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CBcQnaQHGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CBoQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CBcQnaQHGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CBcQnaQHGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/G4HF4N4wOmmWUiGnGsxSWeKMCDPYvsURR8rHKY-wPJUGqkQBRqWd_0p0j0mIzptPsPDr7CbOaYg=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CBcQnaQHGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@RasgullaReddy",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCrElD57N2ee3FfwSiiiocSg",
														"canonicalBaseUrl": "/@RasgullaReddy"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CBkQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "WMT_hwGAVSc",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CBkQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "WMT_hwGAVSc"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CBkQ-ecDGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CBgQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CBgQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "WMT_hwGAVSc",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CBgQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"WMT_hwGAVSc"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"WMT_hwGAVSc"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CBgQx-wEGAMiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/WMT_hwGAVSc/mqdefault_6s.webp?du=3000&sqp=CLaDz7sG&rs=AOn4CLDEjFmEmDWhlFIqE_PDnlGgNK0xtg",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CBcQnaQHGBAiEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=WMT_hwGAVSc&pp=YAHIAQHwAQHoBQGiBhUB4_PBE6W2XeguCPiBlmw2onunZreQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "WMT_hwGAVSc",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMKCKvS2dKLu_upbLoDCwi7vduB6aqJ5fYBugMLCKuKxY3K0vzJiAG6AwsInt3Zwtrfrv_dAboDCwislpiKh4nGqqUBugMKCLnh3riTlOaNQroDCwjLyty4l53M96ABugMKCPmyzYeInbL0GroDCgiug-GbkICfhWe6AwoIo7PIsbvrm8pG8gMFDSXK9D4%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE6W2XeguCPiBlmw2onunZreQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr5---sn-5uaezny6.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=58c4ff8701805527&ip=*************&initcwndbps=1507500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtXTVRfaHdHQVZTYyDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/G4HF4N4wOmmWUiGnGsxSWeKMCDPYvsURR8rHKY-wPJUGqkQBRqWd_0p0j0mIzptPsPDr7CbOaYg=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CBcQnaQHGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@RasgullaReddy",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCrElD57N2ee3FfwSiiiocSg",
																	"canonicalBaseUrl": "/@RasgullaReddy"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "GujI6IDzWXk",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/GujI6IDzWXk/hq720.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGH8gEygVMA8=&rs=AOn4CLBF2Rrr9WgLvbsBA6it3nh7uaWXpQ",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/GujI6IDzWXk/hq720.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGH8gEygVMA8=&rs=AOn4CLCl3SzpY8r77xTWLaFLVzYGxW8WXQ",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Sigma Rule\\ud83d\\ude0e\\ud83d\\udd25~Elon Musk Most Famous Quote\\u26a1Motivation Quotes \\ud83d\\udd25#shorts #motivation #elonmusk #sigmamale"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Sigma Rule\\ud83d\\ude0e\\ud83d\\udd25~Elon Musk Most Famous Quote\\u26a1Motivation Quotes \\ud83d\\udd25#shorts #motivation #elonmusk #sigmamale by MrHuzfi 2.0 383,361 views 4 months ago 14 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "MrHuzfi 2.0",
													"navigationEndpoint": {
														"clickTrackingParams": "CBAQnaQHGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@mrhuzfi2.0",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UC0LIU5N2kOkxJFpSePHv0hQ",
															"canonicalBaseUrl": "/@mrhuzfi2.0"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "4 months ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "14 seconds"
												}
											},
											"simpleText": "0:14"
										},
										"viewCountText": {
											"simpleText": "383,361 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CBAQnaQHGBEiEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/GujI6IDzWXk",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "GujI6IDzWXk",
												"playerParams": "8AEByAMyuAQUogYVAePzwROH7fxUg8Qfa5UxSj3_A-IWkAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/GujI6IDzWXk/frame0.jpg",
															"width": 1080,
															"height": 1920
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CBYQsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "CgtHdWpJNklEeldYayocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"ownerText": {
											"runs": [
												{
													"text": "MrHuzfi 2.0",
													"navigationEndpoint": {
														"clickTrackingParams": "CBAQnaQHGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@mrhuzfi2.0",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UC0LIU5N2kOkxJFpSePHv0hQ",
															"canonicalBaseUrl": "/@mrhuzfi2.0"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "MrHuzfi 2.0",
													"navigationEndpoint": {
														"clickTrackingParams": "CBAQnaQHGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@mrhuzfi2.0",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UC0LIU5N2kOkxJFpSePHv0hQ",
															"canonicalBaseUrl": "/@mrhuzfi2.0"
														}
													}
												}
											]
										},
										"trackingParams": "CBAQnaQHGBEiEwiVionD5tGKAxUIA60GHT2pFEpA-bLNh4idsvQa",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "383K views"
												}
											},
											"simpleText": "383K views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CBUQ_pgEGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CBUQ_pgEGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "GujI6IDzWXk",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CBUQ_pgEGA8iEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"GujI6IDzWXk"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"GujI6IDzWXk"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CBUQ_pgEGA8iEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CBQQ0aoFGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "GujI6IDzWXk",
																	"onAddCommand": {
																		"clickTrackingParams": "CBQQ0aoFGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "GujI6IDzWXk",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CBQQ0aoFGBAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CBAQnaQHGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtHdWpJNklEeldYa1ICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CBAQnaQHGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CBMQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CBAQnaQHGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CBAQnaQHGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/RX0mT-HEkyZjnxwT7rn4LS18240_zbiit_fHPQDIYJJs19kQEUPru-JGWVZhy_C8M_rYoDm3IQ=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CBAQnaQHGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@mrhuzfi2.0",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UC0LIU5N2kOkxJFpSePHv0hQ",
														"canonicalBaseUrl": "/@mrhuzfi2.0"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CBIQ-ecDGAEiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "GujI6IDzWXk",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CBIQ-ecDGAEiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "GujI6IDzWXk"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CBIQ-ecDGAEiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CBEQx-wEGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CBEQx-wEGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "GujI6IDzWXk",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CBEQx-wEGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"GujI6IDzWXk"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"GujI6IDzWXk"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CBEQx-wEGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/GujI6IDzWXk/mqdefault_6s.webp?du=3000&sqp=CMD4zrsG&rs=AOn4CLDxdmCJIDYyj10SMHS6oDQsyqP8-A",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "Sigma Rule    ~"
														},
														{
															"text": "Elon Musk",
															"bold": true
														},
														{
															"text": " Most Famous Quote \\u26a1 Motivation Quotes -------------------------------------------------------- #shorts\\u00a0..."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CBAQnaQHGBEiEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=GujI6IDzWXk&pp=YAHIAQHwAQHoBQGiBhUB4_PBE4ft_FSDxB9rlTFKPf8D4haQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "GujI6IDzWXk",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMKCKvS2dKLu_upbLoDCwi7vduB6aqJ5fYBugMLCKuKxY3K0vzJiAG6AwsInt3Zwtrfrv_dAboDCwislpiKh4nGqqUBugMKCLnh3riTlOaNQroDCwjLyty4l53M96ABugMKCKeqgYzw8L_iWLoDCgiug-GbkICfhWe6AwoIo7PIsbvrm8pG8gMFDSXK9D4%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE4ft_FSDxB9rlTFKPf8D4haQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr3---sn-5ualdnsr.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=1ae8c8e880f35979&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtHdWpJNklEeldYayDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/RX0mT-HEkyZjnxwT7rn4LS18240_zbiit_fHPQDIYJJs19kQEUPru-JGWVZhy_C8M_rYoDm3IQ=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CBAQnaQHGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@mrhuzfi2.0",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UC0LIU5N2kOkxJFpSePHv0hQ",
																	"canonicalBaseUrl": "/@mrhuzfi2.0"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "Zwp8AQN4Qa4",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/Zwp8AQN4Qa4/hq720_2.jpg?sqp=-oaymwFBCOgCEMoBSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgUig9MA8=&rs=AOn4CLBSM0TToqgmVCN9yDpgou0yqWdTug",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/Zwp8AQN4Qa4/hq720_2.jpg?sqp=-oaymwFBCNAFEJQDSFryq4qpAzMIARUAAIhCGADYAQHiAQoIGBACGAY4AUAB8AEB-AG2CIACgA-KAgwIABABGHIgUig9MA8=&rs=AOn4CLDqbAyUnOGhpzrcXgL2UaATFQAp5A",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Mark Zuckerberg reacts to Elon Musk possibly buying Twitter #shorts"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Mark Zuckerberg reacts to Elon Musk possibly buying Twitter #shorts by The Verge 2,903,858 views 2 years ago 36 seconds - play Short"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "The Verge",
													"navigationEndpoint": {
														"clickTrackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@TheVerge",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCddiUEpeqJcYeBxX1IVBKvQ",
															"canonicalBaseUrl": "/@TheVerge"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "2 years ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "36 seconds"
												}
											},
											"simpleText": "0:36"
										},
										"viewCountText": {
											"simpleText": "2,903,858 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEpSCUVsb24gTXVza5oBBQgyEPQk",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/shorts/Zwp8AQN4Qa4",
													"webPageType": "WEB_PAGE_TYPE_SHORTS",
													"rootVe": 37414
												}
											},
											"reelWatchEndpoint": {
												"videoId": "Zwp8AQN4Qa4",
												"playerParams": "8AEByAMyuAQUogYVAePzwRNM968dmO5a0vrPWgRGFHK4kAcC",
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/vi/Zwp8AQN4Qa4/frame0.jpg",
															"width": 1080,
															"height": 1920
														}
													],
													"isOriginalAspectRatio": true
												},
												"overlay": {
													"reelPlayerOverlayRenderer": {
														"style": "REEL_PLAYER_OVERLAY_STYLE_SHORTS",
														"trackingParams": "CA8QsLUEIhMIlYqJw-bRigMVCAOtBh09qRRK",
														"reelPlayerNavigationModel": "REEL_PLAYER_NAVIGATION_MODEL_UNSPECIFIED"
													}
												},
												"params": "CBQwAkoYCgsiCUVsb24gTXVzaxIJRWxvbiBNdXNr",
												"sequenceProvider": "REEL_WATCH_SEQUENCE_PROVIDER_RPC",
												"sequenceParams": "Cgtad3A4QVFONFFhNCocGBQiGAoLIglFbG9uIE11c2sSCUVsb24gTXVzaw%3D%3D",
												"loggingContext": {
													"vssLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													},
													"qoeLoggingContext": {
														"serializedContextData": "CgIIDA%3D%3D"
													}
												},
												"ustreamerConfig": "CAw="
											}
										},
										"badges": [
											{
												"metadataBadgeRenderer": {
													"style": "BADGE_STYLE_TYPE_SIMPLE",
													"label": "CC",
													"trackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Closed captions"
													}
												}
											}
										],
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "The Verge",
													"navigationEndpoint": {
														"clickTrackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@TheVerge",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCddiUEpeqJcYeBxX1IVBKvQ",
															"canonicalBaseUrl": "/@TheVerge"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "The Verge",
													"navigationEndpoint": {
														"clickTrackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@TheVerge",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCddiUEpeqJcYeBxX1IVBKvQ",
															"canonicalBaseUrl": "/@TheVerge"
														}
													}
												}
											]
										},
										"trackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEpAroPhm5CAn4Vn",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "2.9 million views"
												}
											},
											"simpleText": "2.9M views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CA4Q_pgEGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CA4Q_pgEGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "Zwp8AQN4Qa4",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CA4Q_pgEGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"Zwp8AQN4Qa4"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"Zwp8AQN4Qa4"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CA4Q_pgEGBEiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CA0Q0aoFGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "Zwp8AQN4Qa4",
																	"onAddCommand": {
																		"clickTrackingParams": "CA0Q0aoFGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "Zwp8AQN4Qa4",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CA0Q0aoFGBIiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "Cgtad3A4QVFONFFhNFICCAI%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CAwQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/lVEb1IUc9D8dZSQF4l8IlBWGjr636qaDHfMbzud0uv_LxdhiMeHtsuvUcSvJa86pWzJd9MCUeQ=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@TheVerge",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCddiUEpeqJcYeBxX1IVBKvQ",
														"canonicalBaseUrl": "/@TheVerge"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "Shorts"
															}
														},
														"simpleText": "SHORTS"
													},
													"style": "SHORTS",
													"icon": {
														"iconType": "YOUTUBE_SHORTS_FILL_NO_TRIANGLE_RED_16"
													}
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CAsQ-ecDGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "Zwp8AQN4Qa4",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CAsQ-ecDGAMiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "Zwp8AQN4Qa4"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CAsQ-ecDGAMiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CAoQx-wEGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CAoQx-wEGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "Zwp8AQN4Qa4",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CAoQx-wEGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"Zwp8AQN4Qa4"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"Zwp8AQN4Qa4"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CAoQx-wEGAQiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/Zwp8AQN4Qa4/mqdefault_6s.webp?du=3000&sqp=CPj3zrsG&rs=AOn4CLD5fd2jGtTV64NJNfQctdKUI8cILA",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "Mark Zuckerberg on "
														},
														{
															"text": "Elon Musk",
															"bold": true
														},
														{
															"text": " possibly buying Twitter: \\u201cI think it\'s interesting as a saga\\u2026 but I think even at this point, it\'s not\\u00a0..."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEoyBnNlYXJjaFIJRWxvbiBNdXNrmgEFCDIQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=Zwp8AQN4Qa4&pp=YAHIAQHwAQHoBQGiBhUB4_PBE0z3rx2Y7lrS-s9aBEYUcriQBwI%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "Zwp8AQN4Qa4",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMKCKvS2dKLu_upbLoDCwi7vduB6aqJ5fYBugMLCKuKxY3K0vzJiAG6AwsInt3Zwtrfrv_dAboDCwislpiKh4nGqqUBugMKCLnh3riTlOaNQroDCwjLyty4l53M96ABugMKCKeqgYzw8L_iWLoDCgj5ss2HiJ2y9Bq6AwoIo7PIsbvrm8pG8gMFDSXK9D4%3D",
												"playerParams": "YAHIAQHwAQHoBQGiBhUB4_PBE0z3rx2Y7lrS-s9aBEYUcriQBwI%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr1---sn-5ualdnsy.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=670a7c01037841ae&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "Egtad3A4QVFONFFhNCDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/lVEb1IUc9D8dZSQF4l8IlBWGjr636qaDHfMbzud0uv_LxdhiMeHtsuvUcSvJa86pWzJd9MCUeQ=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CAkQnaQHGBIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@TheVerge",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCddiUEpeqJcYeBxX1IVBKvQ",
																	"canonicalBaseUrl": "/@TheVerge"
																}
															}
														}
													}
												}
											}
										}
									}
								},
								{
									"videoRenderer": {
										"videoId": "RpRvW7YyGaM",
										"thumbnail": {
											"thumbnails": [
												{
													"url": "https://i.ytimg.com/vi/RpRvW7YyGaM/hq720.jpg?sqp=-oaymwEnCOgCEMoBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLDUEh7B8tnaO0JsRZAC70C-0h-e5g",
													"width": 360,
													"height": 202
												},
												{
													"url": "https://i.ytimg.com/vi/RpRvW7YyGaM/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLBzQiQ5SMsHxJHlKgcUOp63rCWN-Q",
													"width": 720,
													"height": 404
												}
											]
										},
										"title": {
											"runs": [
												{
													"text": "Trump sides with Elon Musk in the H-1B visa debate: Here\'s what you need to know"
												}
											],
											"accessibility": {
												"accessibilityData": {
													"label": "Trump sides with Elon Musk in the H-1B visa debate: Here\'s what you need to know by CNBC Television 61,401 views 21 hours ago 4 minutes, 41 seconds"
												}
											}
										},
										"longBylineText": {
											"runs": [
												{
													"text": "CNBC Television",
													"navigationEndpoint": {
														"clickTrackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSg==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@CNBCtelevision",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCrp_UI8XtuYfpiqluWLD7Lw",
															"canonicalBaseUrl": "/@CNBCtelevision"
														}
													}
												}
											]
										},
										"publishedTimeText": {
											"simpleText": "21 hours ago"
										},
										"lengthText": {
											"accessibility": {
												"accessibilityData": {
													"label": "4 minutes, 41 seconds"
												}
											},
											"simpleText": "4:41"
										},
										"viewCountText": {
											"simpleText": "61,401 views"
										},
										"navigationEndpoint": {
											"clickTrackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSjIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=RpRvW7YyGaM&pp=ygUJRWxvbiBNdXNr",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "RpRvW7YyGaM",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMKCKvS2dKLu_upbLoDCwi7vduB6aqJ5fYBugMLCKuKxY3K0vzJiAG6AwsInt3Zwtrfrv_dAboDCwislpiKh4nGqqUBugMKCLnh3riTlOaNQroDCwjLyty4l53M96ABugMKCKeqgYzw8L_iWLoDCgj5ss2HiJ2y9Bq6AwoIroPhm5CAn4Vn8gMFDSXK9D4%3D",
												"playerParams": "ygUJRWxvbiBNdXNr",
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr2---sn-5uaeznyz.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=46946f5bb63219a3&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"badges": [
											{
												"metadataBadgeRenderer": {
													"style": "BADGE_STYLE_TYPE_SIMPLE",
													"label": "New",
													"trackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSg=="
												}
											},
											{
												"metadataBadgeRenderer": {
													"style": "BADGE_STYLE_TYPE_SIMPLE",
													"label": "CC",
													"trackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSg==",
													"accessibilityData": {
														"label": "Closed captions"
													}
												}
											}
										],
										"ownerBadges": [
											{
												"metadataBadgeRenderer": {
													"icon": {
														"iconType": "CHECK_CIRCLE_THICK"
													},
													"style": "BADGE_STYLE_TYPE_VERIFIED",
													"tooltip": "Verified",
													"trackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSg==",
													"accessibilityData": {
														"label": "Verified"
													}
												}
											}
										],
										"ownerText": {
											"runs": [
												{
													"text": "CNBC Television",
													"navigationEndpoint": {
														"clickTrackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSg==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@CNBCtelevision",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCrp_UI8XtuYfpiqluWLD7Lw",
															"canonicalBaseUrl": "/@CNBCtelevision"
														}
													}
												}
											]
										},
										"shortBylineText": {
											"runs": [
												{
													"text": "CNBC Television",
													"navigationEndpoint": {
														"clickTrackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSg==",
														"commandMetadata": {
															"webCommandMetadata": {
																"url": "/@CNBCtelevision",
																"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																"rootVe": 3611,
																"apiUrl": "/youtubei/v1/browse"
															}
														},
														"browseEndpoint": {
															"browseId": "UCrp_UI8XtuYfpiqluWLD7Lw",
															"canonicalBaseUrl": "/@CNBCtelevision"
														}
													}
												}
											]
										},
										"trackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSkCjs8ixu-ubykY=",
										"showActionMenu": false,
										"shortViewCountText": {
											"accessibility": {
												"accessibilityData": {
													"label": "61K views"
												}
											},
											"simpleText": "61K views"
										},
										"menu": {
											"menuRenderer": {
												"items": [
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Add to queue"
																	}
																]
															},
															"icon": {
																"iconType": "ADD_TO_QUEUE_TAIL"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CAgQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true
																	}
																},
																"signalServiceEndpoint": {
																	"signal": "CLIENT_SIGNAL",
																	"actions": [
																		{
																			"clickTrackingParams": "CAgQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"addToPlaylistCommand": {
																				"openMiniplayer": true,
																				"videoId": "RpRvW7YyGaM",
																				"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																				"onCreateListCommand": {
																					"clickTrackingParams": "CAgQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo=",
																					"commandMetadata": {
																						"webCommandMetadata": {
																							"sendPost": true,
																							"apiUrl": "/youtubei/v1/playlist/create"
																						}
																					},
																					"createPlaylistServiceEndpoint": {
																						"videoIds": [
																							"RpRvW7YyGaM"
																						],
																						"params": "CAQ%3D"
																					}
																				},
																				"videoIds": [
																					"RpRvW7YyGaM"
																				]
																			}
																		}
																	]
																}
															},
															"trackingParams": "CAgQ_pgEGBAiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemDownloadRenderer": {
															"serviceEndpoint": {
																"clickTrackingParams": "CAcQ0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																"offlineVideoEndpoint": {
																	"videoId": "RpRvW7YyGaM",
																	"onAddCommand": {
																		"clickTrackingParams": "CAcQ0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo=",
																		"getDownloadActionCommand": {
																			"videoId": "RpRvW7YyGaM",
																			"params": "CAIQAA%3D%3D"
																		}
																	}
																}
															},
															"trackingParams": "CAcQ0aoFGBEiEwiVionD5tGKAxUIA60GHT2pFEo="
														}
													},
													{
														"menuServiceItemRenderer": {
															"text": {
																"runs": [
																	{
																		"text": "Share"
																	}
																]
															},
															"icon": {
																"iconType": "SHARE"
															},
															"serviceEndpoint": {
																"clickTrackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSg==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"sendPost": true,
																		"apiUrl": "/youtubei/v1/share/get_share_panel"
																	}
																},
																"shareEntityServiceEndpoint": {
																	"serializedShareEntity": "CgtScFJ2VzdZeUdhTQ%3D%3D",
																	"commands": [
																		{
																			"clickTrackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSg==",
																			"openPopupAction": {
																				"popup": {
																					"unifiedSharePanelRenderer": {
																						"trackingParams": "CAYQjmIiEwiVionD5tGKAxUIA60GHT2pFEo=",
																						"showLoadingSpinner": true
																					}
																				},
																				"popupType": "DIALOG",
																				"beReused": true
																			}
																		}
																	]
																}
															},
															"trackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSg==",
															"hasSeparator": true
														}
													}
												],
												"trackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSg==",
												"accessibility": {
													"accessibilityData": {
														"label": "Action menu"
													}
												}
											}
										},
										"channelThumbnailSupportedRenderers": {
											"channelThumbnailWithLinkRenderer": {
												"thumbnail": {
													"thumbnails": [
														{
															"url": "https://yt3.ggpht.com/WhC1oycyrx_oXiMM1EeRZm6sWBA1ciHeyeuIkQuDuK2aeQBbLIkfshKIHJuUjD7d1w-gq4AqlyU=s68-c-k-c0x00ffffff-no-rj",
															"width": 68,
															"height": 68
														}
													]
												},
												"navigationEndpoint": {
													"clickTrackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSg==",
													"commandMetadata": {
														"webCommandMetadata": {
															"url": "/@CNBCtelevision",
															"webPageType": "WEB_PAGE_TYPE_CHANNEL",
															"rootVe": 3611,
															"apiUrl": "/youtubei/v1/browse"
														}
													},
													"browseEndpoint": {
														"browseId": "UCrp_UI8XtuYfpiqluWLD7Lw",
														"canonicalBaseUrl": "/@CNBCtelevision"
													}
												},
												"accessibility": {
													"accessibilityData": {
														"label": "Go to channel"
													}
												}
											}
										},
										"thumbnailOverlays": [
											{
												"thumbnailOverlayTimeStatusRenderer": {
													"text": {
														"accessibility": {
															"accessibilityData": {
																"label": "4 minutes, 41 seconds"
															}
														},
														"simpleText": "4:41"
													},
													"style": "DEFAULT"
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"isToggled": false,
													"untoggledIcon": {
														"iconType": "WATCH_LATER"
													},
													"toggledIcon": {
														"iconType": "CHECK"
													},
													"untoggledTooltip": "Watch later",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CAUQ-ecDGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"addedVideoId": "RpRvW7YyGaM",
																	"action": "ACTION_ADD_VIDEO"
																}
															]
														}
													},
													"toggledServiceEndpoint": {
														"clickTrackingParams": "CAUQ-ecDGAQiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true,
																"apiUrl": "/youtubei/v1/browse/edit_playlist"
															}
														},
														"playlistEditEndpoint": {
															"playlistId": "WL",
															"actions": [
																{
																	"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID",
																	"removedVideoId": "RpRvW7YyGaM"
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Watch later"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CAUQ-ecDGAQiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayToggleButtonRenderer": {
													"untoggledIcon": {
														"iconType": "ADD_TO_QUEUE_TAIL"
													},
													"toggledIcon": {
														"iconType": "PLAYLIST_ADD_CHECK"
													},
													"untoggledTooltip": "Add to queue",
													"toggledTooltip": "Added",
													"untoggledServiceEndpoint": {
														"clickTrackingParams": "CAQQx-wEGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
														"commandMetadata": {
															"webCommandMetadata": {
																"sendPost": true
															}
														},
														"signalServiceEndpoint": {
															"signal": "CLIENT_SIGNAL",
															"actions": [
																{
																	"clickTrackingParams": "CAQQx-wEGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
																	"addToPlaylistCommand": {
																		"openMiniplayer": true,
																		"videoId": "RpRvW7YyGaM",
																		"listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE",
																		"onCreateListCommand": {
																			"clickTrackingParams": "CAQQx-wEGAUiEwiVionD5tGKAxUIA60GHT2pFEo=",
																			"commandMetadata": {
																				"webCommandMetadata": {
																					"sendPost": true,
																					"apiUrl": "/youtubei/v1/playlist/create"
																				}
																			},
																			"createPlaylistServiceEndpoint": {
																				"videoIds": [
																					"RpRvW7YyGaM"
																				],
																				"params": "CAQ%3D"
																			}
																		},
																		"videoIds": [
																			"RpRvW7YyGaM"
																		]
																	}
																}
															]
														}
													},
													"untoggledAccessibility": {
														"accessibilityData": {
															"label": "Add to queue"
														}
													},
													"toggledAccessibility": {
														"accessibilityData": {
															"label": "Added"
														}
													},
													"trackingParams": "CAQQx-wEGAUiEwiVionD5tGKAxUIA60GHT2pFEo="
												}
											},
											{
												"thumbnailOverlayNowPlayingRenderer": {
													"text": {
														"runs": [
															{
																"text": "Now playing"
															}
														]
													}
												}
											},
											{
												"thumbnailOverlayLoadingPreviewRenderer": {
													"text": {
														"runs": [
															{
																"text": "Keep hovering to play"
															}
														]
													}
												}
											}
										],
										"richThumbnail": {
											"movingThumbnailRenderer": {
												"movingThumbnailDetails": {
													"thumbnails": [
														{
															"url": "https://i.ytimg.com/an_webp/RpRvW7YyGaM/mqdefault_6s.webp?du=3000&sqp=CP_3zrsG&rs=AOn4CLDhmAEZGXKAS69H-Z_bcrpKx9pjfA",
															"width": 320,
															"height": 180
														}
													],
													"logAsMovingThumbnail": true
												},
												"enableHoveredLogging": true,
												"enableOverlay": true
											}
										},
										"detailedMetadataSnippets": [
											{
												"snippetText": {
													"runs": [
														{
															"text": "Tim Higgins, Wall Street Journal business columnist, joins \'Squawk Box\' to discuss the debate surrounding the H-1B visa and\\u00a0..."
														}
													]
												},
												"snippetHoverText": {
													"runs": [
														{
															"text": "From the video description"
														}
													]
												},
												"maxOneLine": false
											}
										],
										"inlinePlaybackEndpoint": {
											"clickTrackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSjIGc2VhcmNoUglFbG9uIE11c2uaAQMQ9CQ=",
											"commandMetadata": {
												"webCommandMetadata": {
													"url": "/watch?v=RpRvW7YyGaM&pp=YAHIAQE%3D",
													"webPageType": "WEB_PAGE_TYPE_WATCH",
													"rootVe": 3832
												}
											},
											"watchEndpoint": {
												"videoId": "RpRvW7YyGaM",
												"params": "qgMJRWxvbiBNdXNrugMLCLigw-K1osTW3wG6AwoIyp6gqMuk76p_ugMLCI_NhcuXsumkgQG6AwsI_8e7s-7B8rGkAboDCgjc8PjsuP3Y70C6AwoIl4z568vEovEQugMLCMzSpbHBjeTKrgG6AwsIoKqyxOmu5dr6AboDCwiE3P6qhsie1LsBugMKCKvS2dKLu_upbLoDCwi7vduB6aqJ5fYBugMLCKuKxY3K0vzJiAG6AwsInt3Zwtrfrv_dAboDCwislpiKh4nGqqUBugMKCLnh3riTlOaNQroDCwjLyty4l53M96ABugMKCKeqgYzw8L_iWLoDCgj5ss2HiJ2y9Bq6AwoIroPhm5CAn4Vn8gMFDSXK9D4%3D",
												"playerParams": "YAHIAQE%3D",
												"playerExtraUrlParams": [
													{
														"key": "inline",
														"value": "1"
													}
												],
												"watchEndpointSupportedOnesieConfig": {
													"html5PlaybackOnesieConfig": {
														"commonConfig": {
															"url": "https://rr2---sn-5uaeznyz.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=46946f5bb63219a3&ip=*************&initcwndbps=1512500&mt=1735640720&oweuc="
														}
													}
												}
											}
										},
										"searchVideoResultEntityKey": "EgtScFJ2VzdZeUdhTSDnAigB",
										"avatar": {
											"decoratedAvatarViewModel": {
												"avatar": {
													"avatarViewModel": {
														"image": {
															"sources": [
																{
																	"url": "https://yt3.ggpht.com/WhC1oycyrx_oXiMM1EeRZm6sWBA1ciHeyeuIkQuDuK2aeQBbLIkfshKIHJuUjD7d1w-gq4AqlyU=s68-c-k-c0x00ffffff-no-rj",
																	"width": 68,
																	"height": 68
																}
															]
														},
														"avatarImageSize": "AVATAR_SIZE_M"
													}
												},
												"a11yLabel": "Go to channel",
												"rendererContext": {
													"commandContext": {
														"onTap": {
															"innertubeCommand": {
																"clickTrackingParams": "CAMQ3DAYEyITCJWKicPm0YoDFQgDrQYdPakUSg==",
																"commandMetadata": {
																	"webCommandMetadata": {
																		"url": "/@CNBCtelevision",
																		"webPageType": "WEB_PAGE_TYPE_CHANNEL",
																		"rootVe": 3611,
																		"apiUrl": "/youtubei/v1/browse"
																	}
																},
																"browseEndpoint": {
																	"browseId": "UCrp_UI8XtuYfpiqluWLD7Lw",
																	"canonicalBaseUrl": "/@CNBCtelevision"
																}
															}
														}
													}
												}
											}
										}
									}
								}
							],
							"trackingParams": "CAIQuy8YASITCJWKicPm0YoDFQgDrQYdPakUSg=="
						}
					},
					{
						"continuationItemRenderer": {
							"trigger": "CONTINUATION_TRIGGER_ON_ITEM_SHOWN",
							"continuationEndpoint": {
								"clickTrackingParams": "CAEQt6kLGAIiEwiVionD5tGKAxUIA60GHT2pFEo=",
								"commandMetadata": {
									"webCommandMetadata": {
										"sendPost": true,
										"apiUrl": "/youtubei/v1/search"
									}
								},
								"continuationCommand": {
									"token": "EpoDEglFbG9uIE11c2sajANDQUFTQWhBQlNDaUNBUXN6TmpCU1JURjRVVEJFWjRJQkMyWXhWemxLVEZWSlJEQnZnZ0VMWjFWdGJHdFliR2hhYnppQ0FRdHdSMUJMUkhWYWRUUmZPSUlCQzFGT09XbzJOREpsVDBaM2dnRUxSVTlMUzBwTU1TMVNhR09DQVF0eWNGZFJZa0paY0dGVmQ0SUJDeTF5VjFaa2NHbE5iRk5CZ2dFTGRUWm9ObEZIVm1aeVoxR0NBUXRpUmxCME1reHdWMkZUYzRJQkN6bHpiMnhXY0VFeU0zSnpnZ0VMYVVwUWVXeExSM2hTVTNPQ0FRc3paalkyWDJGb1YySndOSUlCQzNCV1ZWbFRTRVpIUTNsM2dnRUxVV2gxV1c5VVkxaHpUR3VDQVF0dlR6aDNObGhqV0VwVmM0SUJDMWROVkY5b2QwZEJWbE5qZ2dFTFIzVnFTVFpKUkhwWFdHdUNBUXRhZDNBNFFWRk9ORkZoTklJQkMxSndVblpYTjFsNVIyRk5zZ0VHQ2dRSUtCQUQYgeDoGCILc2VhcmNoLWZlZWQ%3D",
									"request": "CONTINUATION_REQUEST_TYPE_SEARCH"
								}
							},
							"loggingDirectives": {
								"trackingParams": "CAEQt6kLGAIiEwiVionD5tGKAxUIA60GHT2pFEo="
							}
						}
					}
				],
				"targetId": "search-feed"
			}
		}
	]
}
