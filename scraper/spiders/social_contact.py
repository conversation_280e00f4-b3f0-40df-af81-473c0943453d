from logging import getLogger
from typing import AsyncGenerator

from scraper.spiders.base import (
    Playwright<PERSON>piderConfig,
    SocialProfile,
    SpiderNavAnchor,
    PlaywrightSpider,
)

logger = getLogger(__name__)


class SocialContactSpiderConfig(PlaywrightSpiderConfig):
    """the config for contact spider"""

    pass

    # async def async_get_browser_context(self) -> AsyncBrowserContext:
    #     is_created = self._browser_context is None
    #     await super().async_get_browser_context()
    #     # only add cookies if the context is newly created
    #     if not self.user_data_dir and is_created:
    #         all_cookies = []
    #         for platform in ["tiktok", "instagram"]:
    #             cookies = await self._async_load_cookies(platform)
    #             all_cookies.extend(cookies)
    #             logger.debug(f"{platform} cookies added")
    #         await self._browser_context.add_cookies(all_cookies)
    #     return self._browser_context


class SocialContactSpider(PlaywrightSpider):
    config: SocialContactSpiderConfig

    async def async_start(self) -> None:
        raise NotImplementedError

    async def async_check_if_has_next_page(self) -> bool:
        raise NotImplementedError

    def get_next_page_url(self, anchor: SpiderNavAnchor = None) -> str:
        raise NotImplementedError

    def async_iterate(self) -> AsyncGenerator[SocialProfile, None]:
        raise NotImplementedError
