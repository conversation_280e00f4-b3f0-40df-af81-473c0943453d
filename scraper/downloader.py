import asyncio
import random
from abc import abstractmethod, <PERSON>
from logging import getLogger
from pathlib import Path
from typing import Optional, Dict, Any, Literal

import aiohttp
import requests
from asgiref.sync import async_to_sync
from fake_useragent import UserAgent
from playwright.async_api import (
    <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
    async_playwright,
    <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
    BrowserContext as AsyncBrowserContext,
    Page as AsyncPage,
)

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.webdriver import WebDriver as ChromeWebDriver
    from selenium.webdriver.remote.webdriver import WebDriver
except ImportError:
    pass

from common.utils.asyncutils import gather_with_error, with_concurrency_limit
from common.utils.datetimeutils import timeit
from scraper.base import (
    default_header_template,
    DownloadResult,
    Downloader,
    DownloaderConfig,
)
from scraper.exceptions import ConfigError
from scraper.login_manager import (
    <PERSON><PERSON><PERSON><PERSON>,
    SessionManager,
)

logger = getLogger(__name__)

VERBOSE = 0


# -----  configs  ------


class StaticLocalDownloaderConfig(DownloaderConfig):
    header_template: dict = default_header_template
    verify_ssl: bool = True
    # requests proxy format
    proxy: Optional[dict] = None


class PlaywrightDownloaderConfig(DownloaderConfig):
    browser_type: Literal["chromium", "firefox", "webkit"] = "firefox"
    is_headless: bool = False

    # possible other resources: [
    #   "texttrack", "xhr", "fetch", "eventsource", "websocket", "manifest", "other"
    # ]
    ignored_resources: list[Literal["image", "media", "stylesheet", "font", "script", "websocket"]] = [
        "image",
        "media",
        "font",
    ]

    user_data_dir: str = ""

    # proxy format:
    """{
      "server": "http://myproxy.com:3128",
      "username": "usr",
      "password": "pwd"
    }"""
    proxy: Optional[dict] = None

    # NOTE: Try Firefox instead of Chromium due to ERR_HTTP2_PROTOCOL_ERROR

    # NOTE: async_to_sync will cause browser.new_page() hang.

    _lock: asyncio.Lock = asyncio.Lock()
    _playwright: Optional[AsyncPlaywright] = None
    _browser_context: AsyncBrowserContext | None = None

    _browser: AsyncBrowser | None = None
    _is_browser_auto_created: bool = False

    __page: AsyncPage | None = None
    __page_used_cnt: int = 0
    __page_usage_limit: int = 15  # for gc

    def __del__(self) -> None:
        if self._playwright is not None:
            logger.warning("close()/async_close() must be manually called in the same asyncio loop when it created.")

    async def async_close(self) -> None:
        if self.__page:
            await self.__page.close()
            # logger.debug("old browser page closed.")
            self.__page = None
            self.__page_used_cnt = 0
        if self._browser_context:
            await self._browser_context.close()
            # logger.debug("old browser-context closed")
            self._browser_context = None
        if self._is_browser_auto_created and self._browser is not None:
            try:
                await self._browser.close()
                # logger.debug("old auto-created browser closed")
            except Exception as e:
                logger.warning(f"error closing browser: {e}")
            self._browser = None
        if self._playwright is not None:
            try:
                await self._playwright.stop()
                # logger.debug("old auto-created playwright stopped")
            except Exception as e:
                logger.warning(f"error stopping playwright: {e}")
            self._playwright = None
        logger.warning(f".-= browser related CLOSED ({self.name}) =-.")

    # @timeit()
    async def async_get_browser_page(self, create_new: bool = False, auto_gc: bool = True) -> AsyncPage:
        """to get an active tab page. managed.

        :param create_new: whether create a new page (default False)
        :param auto_gc: whether destroy old page and create a new page if reach page usage limit (for gc, default True)
        :return:
        """
        context = await self.async_get_browser_context()

        if self.__page is not None:
            if create_new or (self.__page_used_cnt >= self.__page_usage_limit and auto_gc):
                # destroy old page if used many times to release memory
                logger.debug("destroying old tab page")
                await self.__page.close()
                self.__page = None

        if self.__page is None:
            if context.pages:
                # Use the last page if it exists and hasn't exceeded usage limit
                self.__page = context.pages[-1]
                logger.info("using existing tab page")
            else:
                # create a new page
                self.__page = await context.new_page()
                logger.debug("new browser page created")
                self.__page_used_cnt = 0

        # Block unnecessary resources to reduce latency and network load
        if self.ignored_resources:
            await self.__page.route(
                "**/*",
                lambda route, request: (
                    route.abort() if request.resource_type in self.ignored_resources else route.continue_()
                ),
            )

        self.__page_used_cnt += 1
        return self.__page

    async def async_get_browser_context(self) -> AsyncBrowserContext:
        """Get or create a browser context."""
        options: dict[str, Any] = {
            # "locale": "en-US",
            # "timezone_id": "America/New_York",
        }
        if self.user_data_dir:
            if self._browser_context is None:
                playwright = await self._async_get_playwright()
                brw = getattr(playwright, self.browser_type)
                if not brw:
                    raise NotImplementedError(f'"{self.browser_type}" is not supported browser.')
                options.update({"headless": self.is_headless, "proxy": self.proxy})
                self._browser_context = await brw.launch_persistent_context(self.user_data_dir, **options)
                logger.info(f"{self.browser_type} persisted browser context created.")
        else:
            if self._browser_context is None:
                browser = await self.async_get_browser()
                if len(browser.contexts) > 0:
                    self._browser_context = browser.contexts[-1]
                else:
                    # Create a new context if it doesn't exist
                    self._browser_context = await browser.new_context(**options)

                # load ALL stored login session, make some platforms login-ed
                await self.async_auto_login(rotate_session=False)

                logger.debug("New browser context created")
        return self._browser_context

    async def async_auto_login(
        self,
        platform: str = "",
        rotate_session: bool = True,
    ) -> None:
        """Auto login platforms which has stored login-ed session/cookies."""

        platforms = ["instagram", "tiktok"]
        platforms = [platform] if platform and platform in platforms else platforms
        session = SessionManager.get_instance()
        for platform in platforms:
            if rotate_session:
                if not session.circled(platform) and session.next(platform):
                    logger.warning(f"rotated {platform} sessions (set={session.current_set(platform)})")
                else:
                    logger.warning(f"fail to rotate session {platform} (set={session.current_set(platform)})")

            # cookie
            mgr_name = CookieManager.get_manager_name()
            data = session.current_set_data(platform)
            if data is None:
                logger.warning(f"fail to load session {platform} cookie (now set={session.current_set(platform)})")
            else:
                cookies = data[mgr_name]
                assert self._browser_context
                await self._browser_context.add_cookies(cookies)
                logger.info(f"loaded {platform} {mgr_name} (set={session.current_set(platform)})")

    async def async_get_browser(self) -> AsyncBrowser:
        async with self._lock:
            if self._browser is None:
                self._browser = await self.async_create_browser(self)
                self._is_browser_auto_created = True
        return self._browser

    async def async_set_browser(self, value: AsyncBrowser) -> None:
        async with self._lock:
            if self._browser:
                await self.async_close()

            self._browser = value
            self._is_browser_auto_created = False

    async def _async_get_playwright(self) -> AsyncPlaywright:
        if self._playwright is None:
            self._playwright = await async_playwright().start()
            logger.debug("playwright created")

        if not self.__is_same_loop(self._playwright):
            raise RuntimeError("can not run in different loop")
        return self._playwright

    # async def __aenter__(self) -> None:
    #     if not (self._playwright is None or self.__is_same_loop(self._playwright)):
    #         await self._playwright.stop()
    #         self._playwright = None
    #     await self._async_get_playwright()
    #
    # async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
    #     if not (self._playwright is None or self.__is_same_loop(self._playwright)):
    #         await self._playwright.stop()
    #         self._playwright = None
    #     await self._async_get_playwright()

    async def __is_same_loop(self, obj: AsyncPlaywright | AsyncBrowser | AsyncBrowserContext | AsyncPage) -> bool:
        obj_loop = getattr(obj, "_loop", None)
        if obj_loop:
            loop = asyncio.get_running_loop()
            if obj_loop is loop:
                return True
        return False

    @staticmethod
    @timeit()
    async def async_create_browser(
        config: "PlaywrightDownloaderConfig",
    ) -> AsyncBrowser:
        if config.user_data_dir:
            raise ConfigError('when "user_data_dir" specified, you can not create a browser. Use get_browser_context')
        playwright = await config._async_get_playwright()
        brw = getattr(playwright, config.browser_type)
        if not brw:
            raise NotImplementedError(f'"{config.browser_type}" is not supported browser.')
        logger.info(f"{config.browser_type} browser created.")
        if config.browser_type == "chromium":
            # ref in Chinese: https://www.cnblogs.com/gurenyumao/p/14721035.html
            args = [
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                # "--disable-gpu",
                "--no-zygote",
                # "--single-process", # this will cause browser crash.
                "--safebrowsing-disable-auto-update",
                "--disable-translate",
                "--disable-sync",
                "--disable-default-apps",
                "--no-first-run",
                "--disable-web-security",
                "--disable-extensions",
                "--hide-scrollbars",
                "--disable-popup-blocking",
                "--window-size=1024,1280",
                "--hide-crash-restore-bubble",
            ]
        else:
            args = []
        return await brw.launch(headless=config.is_headless, proxy=config.proxy, args=args)


class SeleniumDownloaderConfig(DownloaderConfig):
    """config for downloader built by Selenium"""

    driver_name: Literal["chrome", "firefox"] = "chrome"
    is_headless: bool = False

    # if presents, will connect remote driver to download
    remote_addr: str = ""
    # when "remote_addr" is presented, used to specify whether remote is Windows OS
    remote_is_win: bool = False

    proxy: str = ""
    load_image: bool = False
    user_data_path: str | Path = ""

    _driver: WebDriver | None = None
    _is_drive_auto_created: bool = False

    def __init__(self, **kwargs: Any) -> None:
        driver = kwargs.pop("driver", None)
        if driver is not None and not isinstance(driver, WebDriver):
            raise ValueError(f'arg "driver" must be instance of {WebDriver.__name__}')
        if driver:
            self._driver = driver
        super().__init__(**kwargs)

    def __del__(self) -> None:
        if self._is_drive_auto_created and self._driver is not None:
            try:
                self._driver.quit()
            except Exception:
                pass

    @property
    def driver(self) -> WebDriver:
        if self._driver is None:
            self._driver = self.create_driver(self)
            self._is_drive_auto_created = True
        return self._driver

    @driver.setter
    def driver(self, value: WebDriver) -> None:
        if self._driver:
            self._driver.quit()

        self._driver = value
        self._is_drive_auto_created = False

    @staticmethod
    def create_driver(config: "SeleniumDownloaderConfig") -> WebDriver:
        if config.driver_name == "chrome":
            driver = SeleniumDownloaderConfig.create_chrome_driver(config)
        else:
            raise NotImplementedError(f'"{config.driver_name}" is not supported driver.')
        return driver

    @staticmethod
    def create_chrome_driver(
        config: "SeleniumDownloaderConfig",
    ) -> ChromeWebDriver | WebDriver:
        chrome_options = webdriver.ChromeOptions()
        if config.is_headless:
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--window-size=1920,1080")
        if config.proxy:
            chrome_options.add_argument("--proxy-server=%s" % config.proxy)

        if not config.load_image:
            chrome_options.add_argument("--blink-settings=imagesEnabled=false")

        if config.remote_addr:
            if config.remote_is_win:
                config.user_data_path = rf"C:\.chrome_user_data\{config.name}"
            else:
                config.user_data_path = f"~/.chrome_user_data/{config.name}"
            logger.warning(f"user-data-dir is changed to {config.user_data_path} for Remote Driver")
        if config.user_data_path:
            logger.debug(f"chrome user data: {config.user_data_path}")
            chrome_options.add_argument(f"--user-data-dir={config.user_data_path}")

        if config.remote_addr:
            # chrome_options.add_argument("--no-sandbox")
            # chrome_options.add_argument("--disable-dev-shm-usage")
            chrome = webdriver.Remote(config.remote_addr, options=chrome_options)
        else:
            chrome = webdriver.Chrome(options=chrome_options)
        logger.info("chrome webdriver initialized.")
        # chrome.maximize_window()

        return chrome


# -----------------------


class BaseDownloader(Downloader, ABC):
    config: DownloaderConfig

    def __init__(self, config: DownloaderConfig, *args: Any, **kwargs: Any) -> None:
        self.config = config

    def download(self, url: str) -> DownloadResult:
        return async_to_sync(self.async_download)(url)

    def download_all(self, urls: list[str]) -> list[DownloadResult]:
        # TODO(spider): make it returns Generator
        return async_to_sync(self.async_download_all)(urls)

    @abstractmethod
    async def _async_download(self, url: str) -> DownloadResult:
        """for subclass to implement the real download logic"""

    @timeit()
    async def async_download(self, url: str) -> DownloadResult:
        logger.debug(f"downloading {url}")
        result = DownloadResult(url=url)
        if self.config.cache:
            result.data = await self.config.cache.async_get(url)
            if result.data:
                return result

        try:
            result = await self._async_download(url)
            if result.data and self.config.cache:
                await self.config.cache.async_set(url, result.data)
        except Exception as e:
            result.ex = e
        return result

    async def async_download_all(self, urls: list[str]) -> list[DownloadResult]:
        # TODO(spider): make it returns AsyncGenerator
        logger.debug(f"start downloading {len(urls)} url(s)")
        results, _ = await gather_with_error(
            *with_concurrency_limit(
                [self.async_download(url) for url in urls],
                limit=self.config.concurrency,
            ),
            log_errors=True,
            split_errors=False,
        )
        return results


class StaticLocalDownloader(BaseDownloader):
    """local downloader for static page"""

    config: StaticLocalDownloaderConfig

    session: Optional[requests.Session] = None

    def __init__(self, config: StaticLocalDownloaderConfig, *args: Any, **kwargs: Any) -> None:
        super().__init__(config, *args, **kwargs)
        self._init_session()

    def _init_session(self) -> None:
        headers: Dict[str, str] = self.config.header_template
        if not headers.get("User-Agent"):
            headers["User-Agent"] = UserAgent().random

        session = requests.Session()
        session.headers = dict(headers)
        session.verify = self.config.verify_ssl

        if self.config.proxy:
            session.proxies.update(self.config.proxy)
            logger.warning(f"using proxies: {self.config.proxy}")

        self.session = session

    async def _async_download(self, url: str) -> DownloadResult:
        timeout = aiohttp.ClientTimeout(total=self.config.timeout, connect=10, sock_connect=10, sock_read=15)
        async with aiohttp.ClientSession(timeout=timeout, trust_env=True) as session:
            result = await self._download_inner(
                session,
                url,
                self.session.headers,  # type: ignore
                self.session.verify,  # type: ignore
            )
        return result

    async def _download_inner(
        self,
        session: aiohttp.ClientSession,
        url: str,
        headers: dict,
        verify: Optional[bool],
    ) -> DownloadResult:
        async with session.get(
            url,
            headers=headers,
            ssl=None if verify else False,
        ) as response:
            _ = await response.read()
            (
                logger.debug(f"{url} - {response.content_type} {response.content_length} " f"{response.charset} ")
                if VERBOSE > 1
                else None
            )
            result = DownloadResult(url=url, content_type=response.content_type)
            encoding = response.get_encoding()
            result.data = await response.text(encoding=encoding)
            return result


class PlaywrightDownloader(BaseDownloader):
    """downloader built by Playwright for either static or dynamic page from local or remote (note1).

    * note1: remote is experimental feature and only support Selenium Grid (https://playwright.dev/python/docs/selenium-grid)
    """

    config: PlaywrightDownloaderConfig

    def __init__(self, config: PlaywrightDownloaderConfig, *args: Any, **kwargs: Any) -> None:
        super().__init__(config, *args, **kwargs)

    async def _async_download(self, url: str) -> DownloadResult:
        browser = await self.config.async_get_browser()
        page = await browser.new_page()
        result = DownloadResult(url=url)

        try:
            # Block unnecessary resources to reduce latency and network load
            await page.route(
                "**/*",
                lambda route, request: (
                    route.abort() if request.resource_type in ["image", "stylesheet", "font"] else route.continue_()
                ),
            )

            # Navigate to the URL and wait for the page to fully load
            await page.goto(url, wait_until="networkidle", timeout=self.config.timeout * 1000)

            # wait for page loaded
            await page.wait_for_load_state("load")

            # Get the fully rendered HTML content
            html_content = await page.content()
            result.data = html_content
        except Exception as e:
            logger.warning(f"error: {e} - {url}")
            result.ex = e
        finally:
            await page.close()

        return result


class SeleniumDownloader(BaseDownloader):
    """Downloader built by Selenium for either static or dynamic page from either local or remote."""

    config: SeleniumDownloaderConfig

    def __init__(self, config: SeleniumDownloaderConfig, *args: Any, **kwargs: Any) -> None:
        assert config.driver is not None
        super().__init__(config, *args, **kwargs)

    async def _async_download(self, url: str) -> DownloadResult:
        driver = self.config.driver
        driver.get(url)
        await self.__async_wait_page_loaded()
        logger.info(f"page loaded - {url}")
        # content_type = requests.head(url).headers["Content-Type"]
        result = DownloadResult(url=url, data=driver.page_source, content_type="")
        return result

    async def __async_wait_page_loaded(self) -> None:
        page_state = ""
        n = 1
        seconds = 0
        logger.debug("waiting for page loaded ...")
        while page_state != "complete":
            logger.debug(f"sleep {n} seconds ...")
            await self.__async_sleep(n)
            seconds += n
            if seconds >= self.config.timeout:
                raise TimeoutError(f"timeout ({seconds} >= {self.config.timeout} seconds) reached.")
            page_state = self.config.driver.execute_script("return document.readyState;")

    async def __async_sleep(self, n: int = -1) -> None:
        """sleep n seconds. if n < 0, generate random n between 3 and 8"""
        if n < 0:
            n = random.randint(3, 8)
        await asyncio.sleep(n)
