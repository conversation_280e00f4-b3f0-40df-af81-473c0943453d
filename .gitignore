# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.pytype/
cython_debug/
pytest-report.xml
coverage.xml

# Virtual Environment
venv/
ENV/
env/
.env
.venv
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
.project
.pydevproject
.settings/
*.sublime-workspace
*.sublime-project
.spyderproject
.spyproject
.ropeproject

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini
$RECYCLE.BIN/

# Jupyter Notebook
.ipynb_checkpoints
profile_default/
ipython_config.py
*.nbconvert.ipynb
.jupyter/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# GBS specific outputs
gbs/model_system/outputs/
gbs/model_system/outputs/experiments/
gbs/model_system/outputs/artifacts/
gbs/model_system/outputs/mlruns/
gbs/model_system/outputs/logs/
gbs/model_system/outputs/checkpoints/
gbs/model_system/outputs/tensorboard/

# Backtest system outputs
gbs/backtest_system/outputs/
gbs/backtest_system/results/
gbs/backtest_system/logs/
gbs/backtest_system/plots/
gbs/backtest_system/reports/

# Evaluation system outputs
gbs/eval_system/outputs/
gbs/eval_system/reports/
gbs/eval_system/plots/
gbs/eval_system/metrics/

# Analysis outputs
outputs/
outputs/portfolio_analysis/
outputs/axioma_attribution/
outputs/analysis_reports/
outputs/plots/
outputs/metrics/
outputs/reports/

# Data files and directories
gbs/data/
gbs/data/processed_data/
gbs/data/processed_data/jkp/
gbs/data/raw_data/
gbs/data/external/
gbs/data/interim/
gbs/data/features/
gbs/data_system/data/
gbs/data_system/data/raw_data/
gbs/data_system/data/processed_data/
gbs/data_system/data/external/
gbs/data_system/data/interim/
gbs/data_system/data/features/

# Workflow outputs
gbs/workflow/mlruns/
gbs/gbs/workflow/mlruns/
examples/workflow_demo/mlruns/
examples/workflow_demo/outputs/
examples/workflow_demo/results/
examples/workflow_demo/logs/
examples/workflow_demo/artifacts/

# Data file formats
*.pkl
*.csv
*.tsv
*.h5
*.hdf5
*.parquet
*.feather
*.dta
*.sas7bdat
*.json
*.jsonl
*.xlsx
*.xls
*.db
*.sqlite
*.sqlite3
*.arrow
*.npy
*.npz
*.zarr
*.avro
*.orc

# Temporary files
*.tmp
*.bak
*.swp
*.swo
*~
.~*
\#*\#
.#*
*.orig
*.rej
tmp/
temp/
/tmp/
/temp/
.tmp/
.temp/
__tmp__/
__temp__/
.tmpfile*
.tempfile*

# Model checkpoints and artifacts
*.pt
*.pth
*.ckpt
*.model
*.bin
*.onnx
*.tflite
*.pb
*.savedmodel
*.h5model
*.weights
*.params
*.state_dict

# MLflow
mlruns/
mlflow-artifacts/
mlruns.db
mlflow.db
.mlflow/
mlartifacts/
mlflow-tracking/
mlflow-models/
mlruns/*/
mlruns/*/*/
mlruns/*/*/*/
mlruns/*/*/*/*/
mlruns/*/*/*/*/*/

# Qlib
qlib/

# Documentation build
docs/_build/
docs/api/
docs/auto_examples/
docs/_generated/
site/

!api/prepared_data/*

# Packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Misc
.env
.envrc
.direnv
.cache/
.pytest_cache/
.mypy_cache/
__pycache__/
.ruff_cache/
.coverage
htmlcov/
.hypothesis/
.tox/
.nox/
.pytype/
cython_debug/

# Checkpoint files
checkpoints/
*.checkpoint
checkpoint_*.pt
checkpoint_*.pth
checkpoint_*.ckpt
checkpoint_*.bin
best_model_*.pt
best_model_*.pth

# Experiment results
experiments/
experiment_results/
runs/
wandb/

# Generated files
generated/
output/
results/

# Local configuration
config_local.yaml
config_local.yml
config_local.json
.env.local
.env.development.local
.env.test.local
.env.production.local
