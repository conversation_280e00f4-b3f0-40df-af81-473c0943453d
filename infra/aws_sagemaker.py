"""tools to train/serve models on sagemaker"""
import asyncio
import time
from datetime import datetime
from logging import getLogger
from pathlib import Path

import boto3
import sagemaker
from sagemaker.estimator import Estimator
from sagemaker.inputs import FileSystemInput
from sagemaker.pytorch import PyTorch
#from sagemaker_ssh_helper.wrapper import SSHEstimatorWrapper

logger = getLogger(__name__)

ec2_type = "ml.g5.4xlarge"  # GPU instance for ML training
region = "us-east-1"
vpc = 'vpc-0514c93fc09cc7c4d'  # mixed-east-1
subnets = [
    # 'subnet-0a7cc3fc09e0a4676',  # us-east-1d
    # 'subnet-067864b5f907940e1',  # us-east-1f
    'subnet-01e904fb47c2e75c2',  # private-us-east-1d
]
security_groups = [
    'sg-00f5c37870854ddfd',  # test-onwish group
    'sg-077aa0bc29c69976e',  # sg-efs group for EFS access
]
efs_id = 'fs-06feb7702299e8e64'  # Persistent storage filesystem
role_arn = 'arn:aws:iam::471112791150:role/service-role/AmazonSageMaker-ExecutionRole-20250422T153085'
# s3 work bucket
s3_work_bucket = 'sagemaker-us-east-1-471112791150'

src_root = Path(__file__).parent.resolve() / 'sagemaker_src'  # source to copy


async def run_training_task(cmd: str) -> tuple[bool, str]:
    """Run a training task on EC2 and return success status and output/error message.

    Args:
        cmd (str): The command to run on the EC2 instance.

    Returns:
        Tuple[bool, str]: (success status, output/error message)
    """
    requirements_src = Path(__file__).parent.parent / 'requirements.txt'
    requirements_dest = Path(src_root) / 'requirements.txt'

    try:
        # Copy requirements.txt to sagemaker_src
        if requirements_src.exists():
            with open(requirements_dest, 'wt') as f:
                f.write(requirements_src.read_text())
        else:
            logger.warning(f"requirements.txt not found at {requirements_src}")
            return False, "requirements.txt not found"

        # Initialize SageMaker session
        sagemaker_session = sagemaker.Session(
            boto3.session.Session(region_name=region)
        )

        # Configure PyTorch estimator
        estimator = PyTorch(
            # https://github.com/aws/sagemaker-python-sdk/issues/3737
            entry_point='train.py',
            source_dir=str(src_root),
            role=role_arn,
            instance_count=1,
            instance_type=ec2_type,
            framework_version='2.0.1',
            # only support 310
            py_version='py310',
            hyperparameters={},
            subnets=subnets,
            security_group_ids=security_groups,
            # dependencies=[SSHEstimatorWrapper.dependency_dir()],  # <--NEW--

        )

        file_system_input = FileSystemInput(
            file_system_id=efs_id,
            file_system_type='EFS',
            directory_path='/',
            file_system_access_mode='rw',
        )

        # ssh_wrapper = SSHEstimatorWrapper.create(estimator, connection_wait_time_seconds=600)  # <--NEW--

        # Start training job with unique name
        job_name = f"run-experiment-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
        estimator.fit(file_system_input, wait=True, job_name=job_name)
        # ssh_wrapper.print_ssh_info()  # <--NEW--
        # training_job_name = estimator.latest_training_job.name
        # new_estimator = Estimator.attach(training_job_name, sagemaker_session)
        # new_estimator.logs()
        # ssh_wrapper.print_ssh_info()  # <--NEW--

        # Get training job status and logs
        training_job = sagemaker_session.sagemaker_client.describe_training_job(
            TrainingJobName=job_name
        )

        status = training_job['TrainingJobStatus']
        if status == 'Completed':
            return True, f"Training job {job_name} completed successfully"
        else:
            failure_reason = training_job.get('FailureReason', 'Unknown error')
            return False, f"Training job {job_name} failed: {failure_reason}"

    except Exception as e:
        logger.exception(e)
        return False, f"Error running SageMaker training task: {str(e)}"

    finally:
        # Clean up the temporary files
        if requirements_dest.exists():
            requirements_dest.unlink()
        # pass

if __name__ == '__main__':
    r = asyncio.run(
        run_training_task(
             'cd gold-beast-system && python model_system/scripts/run_experiment.py --config model_system/conf/project_aipm/config.yaml')
    )
    print(r)
