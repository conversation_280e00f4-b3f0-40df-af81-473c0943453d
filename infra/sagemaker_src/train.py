import subprocess
from pathlib import Path

# folders in sagemaker
input_root = '/opt/ml/input/data/training'
output_root = '/opt/ml/output/data'
data_root = f'{input_root}/gold-beast-system/gbs/data'
code_root = f'{input_root}/gold-beast-system'

# def print_tree(path, prefix=""):
#     path_obj = Path(path)
#     if not path_obj.exists():
#         print(f"Path not found: {path}")
#         return
#
#     print(path_obj.name + "/")
#     prefix_child = prefix + "│   "
#     prefix_last = prefix + "└── "
#     prefix_middle = prefix + "├── "
#
#     items = sorted(list(path_obj.iterdir()))
#     for i, item in enumerate(items):
#         is_last = i == len(items) - 1
#         if item.is_dir():
#             print(f"{prefix_last if is_last else prefix_middle}{item.name}/")
#             if not is_last:
#                 print_tree(item, prefix_child)
#             else:
#                 print_tree(item, prefix + "    ")
#
# print('------')
# print_tree(f'{input_root}/gold-beast-system/model_system')
# print('------')

# The directory should already be mounted by SageMaker
# data_path = Path(f'{data_root}/processed_data/jkp/full_usa.pkl')
# if not data_path.exists():
#     raise FileNotFoundError(f"Required data file not found: {data_path}")

cmd = [f"python", f"{code_root}/examples/workflow_demo/run_experiment_with_workflow.py",
       "--config", f"{code_root}/examples/workflow_demo/config.yaml",
       "--small_dataset", "--limit_stocks", "20"]
try:
    result = subprocess.run(cmd, check=True, capture_output=True, text=True)
except subprocess.CalledProcessError as e:
    raise Exception(f"Training process failed. Output:\n{e.output}\nError:\n{e.stderr}")
