"""EC2 related scripts"""
import json
import logging
import math
import time
from datetime import datetime, timedelta, UTC
from typing import Optional, List, Dict, Any
import os
from pathlib import Path
import statistics
import subprocess

import boto3
import paramiko
import test_consts as tc

logger = logging.getLogger(__name__)


class EC2Manager:
    region: str = ""

    def __init__(self, region: str = "us-east-1"):
        self.region = region
        self.ec2_client = boto3.client("ec2", region_name=region)
        self.ec2_resource = boto3.resource("ec2", region_name=region)
        self.ssh_client = paramiko.SSHClient()
        self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        # Price history log in same directory as ec2.py
        self.price_log_path = Path(__file__).parent / "spot_price_history.jsonl"
        # Cache for log entries
        self._log_entries: List[Dict] = []
        self._load_log_entries()

    def _load_log_entries(self) -> None:
        """Load log entries from file into memory"""
        self._log_entries = []
        if self.price_log_path.exists():
            with open(self.price_log_path, "r") as f:
                for line in f:
                    self._log_entries.append(json.loads(line))

    def log_spot_price(self, instance_type: str, zone: str, price: float, keep_days: int = 90) -> None:
        """
        Log spot price when we request a spot instance and clean up old entries

        Args:
            instance_type: EC2 instance type
            zone: Availability zone
            price: Spot price
            keep_days: Number of days of history to keep (default: 90)
        """
        try:
            # Add new entry
            new_entry = {
                "timestamp": datetime.now(tz=UTC).isoformat(),
                "instance_type": instance_type,
                "zone": zone,
                "price": price,
                "region": self.region
            }

            # Clean up old entries
            cutoff_date = datetime.now(tz=UTC) - timedelta(days=keep_days)
            self._log_entries = [
                entry for entry in self._log_entries
                if datetime.fromisoformat(entry["timestamp"]) >= cutoff_date
            ]

            # Add new entry
            self._log_entries.append(new_entry)

            # Write all entries back to file
            with open(self.price_log_path, "w") as f:
                for entry in self._log_entries:
                    f.write(json.dumps(entry) + "\n")

        except Exception as e:
            logger.error(f"Failed to log spot price: {e}")
            # Reload entries in case of error
            self._load_log_entries()

    def get_our_price_history(self, instance_type: str, zone: str) -> List[Dict]:
        """Get our spot price history"""
        try:
            return [
                entry for entry in self._log_entries
                if entry["instance_type"] == instance_type and entry["zone"] == zone
            ]
        except Exception as e:
            logger.error(f"Failed to get our price history: {e}")
            return []

    def get_spot_price_history(
        self, instance_type: str, zone: str, hours:int=48
    ) -> List[Dict]:
        """Get spot price history for the last X hours"""
        response = self.ec2_client.describe_spot_price_history(
            InstanceTypes=[instance_type],
            AvailabilityZone=zone,
            StartTime=datetime.now(tz=UTC) - timedelta(hours=hours),
            ProductDescriptions=["Linux/UNIX"],
        )
        return response["SpotPriceHistory"]

    def get_on_demand_price(self, instance_type: str) -> float | None:
        """
        Get on-demand price for an EC2 instance type

        Args:
            instance_type: EC2 instance type (e.g. 't2.micro')

        Returns:
            On-demand price per hour or None if price not found
        """
        try:
            # Pricing API is only available in us-east-1
            pricing_client = boto3.client('pricing', region_name='us-east-1')

            # Map region to location name used in pricing API
            region_map = {
                'us-east-1': 'US East (N. Virginia)',
                'us-east-2': 'US East (Ohio)',
                'us-west-1': 'US West (N. California)',
                'us-west-2': 'US West (Oregon)',
                # Add more regions as needed
            }

            location = region_map.get(self.region)
            if not location:
                raise ValueError(f"Unsupported region: {self.region}")

            response = pricing_client.get_products(
                ServiceCode='AmazonEC2',
                Filters=[
                    {'Type': 'TERM_MATCH', 'Field': 'instanceType', 'Value': instance_type},
                    {'Type': 'TERM_MATCH', 'Field': 'operatingSystem', 'Value': 'Linux'},
                    {'Type': 'TERM_MATCH', 'Field': 'location', 'Value': location},
                    {'Type': 'TERM_MATCH', 'Field': 'tenancy', 'Value': 'Shared'},
                    {'Type': 'TERM_MATCH', 'Field': 'capacitystatus', 'Value': 'Used'},
                ]
            )

            if response['PriceList']:
                price_data = json.loads(response['PriceList'][0])
                on_demand_data = list(price_data['terms']['OnDemand'].values())[0]
                price_dimensions = list(on_demand_data['priceDimensions'].values())[0]
                return float(price_dimensions['pricePerUnit']['USD'])

        except Exception as e:
            logger.error(f"Failed to get on-demand price for {instance_type}: {e}")
        return None

    def decide_spot_price(
        self, ec2_type: str, zone: str, duration: int
    ) -> dict[str, float]:
        """
        Decide spot price based on history and duration
        """
        # Get current market prices
        price_history = self.get_spot_price_history(
            instance_type=ec2_type,
            zone=zone,
            hours=24
        )
        if not price_history:
            raise ValueError(f"No price history found for {ec2_type} in {zone}")

        # Get our historical prices
        our_price_history = self.get_our_price_history(ec2_type, zone)

        # Calculate market statistics
        current_prices = [float(p["SpotPrice"]) for p in price_history]
        market_max = max(current_prices)
        market_median = statistics.median(current_prices)

        # Calculate our historical statistics
        our_prices = [p["price"] for p in our_price_history]
        our_stats = {
            "count": len(our_prices),
            "min": min(our_prices) if our_prices else None,
            "max": max(our_prices) if our_prices else None,
            "median": statistics.median(our_prices) if our_prices else None,
            "last_price": our_prices[-1] if our_prices else None
        }

        # Get on-demand price
        on_demand_price = self.get_on_demand_price(ec2_type)

        # Add buffer based on duration
        if duration <= 3:
            buffer = 1.01
        elif duration <= 12:
            buffer = 1.05
        elif duration <= 24:
            buffer = 1.07
        else:
            buffer = 1.1

        recommended_price = round(market_median * buffer, 3)
        # Cap at on-demand price
        recommended_price = min(recommended_price, on_demand_price)

        result = {
            "recommended_price": recommended_price,
            "market_stats": {
                "current_max": market_max,
                "current_median": market_median,
            },
            "our_price_history": our_stats,
            "on_demand_price": on_demand_price,
            "ratio": recommended_price / on_demand_price,
        }

        # Log the new price if we decide to use it
        self.log_spot_price(ec2_type, zone, recommended_price)

        return result

    def create_ec2_instances(
        self,
        ec2_type: str,
        image: str,
        subnet: str,
        zone: str,
        security_groups: list[str],
        key_pair_id: str,
        instance_market_options: Optional[Dict] = None,
        tags: dict[str, str] | None = None,
        count: int = 1,
        assign_public_ip: bool = True,
        name: Optional[str] = None,
        root_volume_size: Optional[int] = None,
        terminate_when_shutdown: bool = False,
    ) -> Dict[str, Any]:
        """Create EC2 instances with specified configuration

        Args:
            ec2_type: EC2 instance type
            image: AMI ID
            subnet: Subnet ID
            zone: Availability zone
            security_groups: List of security group IDs
            key_pair_id: SSH key pair ID
            instance_market_options: Options for spot instances
            tags: Instance tags
            count: Number of instances to launch
            assign_public_ip: Whether to auto-assign public IP (default: True)
            name: Name tag for the instance(s) (default: None)
            root_volume_size: Size in GB for root volume (default: None, uses AMI default)
            terminate_when_shutdown: Whether to terminate instance on shutdown (default: False)

        Returns:
            Dict containing instance information
        """
        # Get AMI details to find root device name and default size
        ami_info = self.ec2_client.describe_images(ImageIds=[image])['Images'][0]
        root_device_name = ami_info['RootDeviceName']

        # Get default root volume size from AMI if not specified
        if not root_volume_size:
            for device in ami_info.get('BlockDeviceMappings', []):
                if device.get('DeviceName') == root_device_name:
                    root_volume_size = device.get('Ebs', {}).get('VolumeSize')
                    logger.info(f"Using default root volume size from AMI: {root_volume_size} GB")
                    break

        # Prepare root volume configuration
        root_volume_config = {
            "DeleteOnTermination": True,
        }

        # Add size if specified or found in AMI
        if root_volume_size:
            root_volume_config["VolumeSize"] = root_volume_size

        # Prepare tags
        tags = tags or {}
        tags = tags | {'batch_id': datetime.now().strftime('%Y%m%d%H%M%S')}
        if name:
            if count == 1:
                tags['Name'] = name
            else:
                # If multiple instances, append number to name
                tags.update({
                    'Name': f"{name}-{i + 1}" for i in range(count)
                })
        tag_specifications = [{
            'ResourceType': 'instance',
            'Tags': [{'Key': k, 'Value': v} for k, v in tags.items()]
        }]

        launch_spec = {
            "MaxCount": count,
            "MinCount": count,
            "ImageId": image,
            "InstanceType": ec2_type,
            "Placement": {"AvailabilityZone": zone},
            "KeyName": key_pair_id,
            "TagSpecifications": tag_specifications,
            "BlockDeviceMappings": [{
                "DeviceName": root_device_name,
                "Ebs": root_volume_config
            }],
            "NetworkInterfaces": [{
                "DeviceIndex": 0,
                "SubnetId": subnet,
                "Groups": security_groups,
                "AssociatePublicIpAddress": assign_public_ip
            }],
            "InstanceInitiatedShutdownBehavior": "terminate" if terminate_when_shutdown else "stop"
        }

        if instance_market_options:
            launch_spec["InstanceMarketOptions"] = instance_market_options

        try:
            response = self.ec2_client.run_instances(**launch_spec)
            instance_ids = [
                instance["InstanceId"] for instance in response["Instances"]
            ]

            waiter = self.ec2_client.get_waiter("instance_running")
            waiter.wait(InstanceIds=instance_ids)

            return {inst["InstanceId"]: inst for inst in response["Instances"]}

        except Exception as e:
            logger.error(f"Failed to create EC2 instances: {e}")
            raise

    def create_spot_instance(
        self,
        ec2_type: str,
        image: str,
        subnet: str,
        zone: str,
        security_groups: list[str],
        key_pair_id: str,
        spot_price: Optional[float] = None,
        tags: dict[str, str] | None = None,
        count: int = 1,
        assign_public_ip: bool = True,
        name: Optional[str] = None,
        root_volume_size: Optional[int] = None,
        terminate_when_shutdown: bool = False,
    ) -> Dict[str, Any]:
        """Create spot instance with specified configuration"""
        price_info = self.decide_spot_price(ec2_type, zone, duration=1)
        if not spot_price:
            spot_price = price_info["recommended_price"]
        ratio = spot_price / price_info['on_demand_price']

        instance_market_options = {
            "MarketType": "spot",
            "SpotOptions": {
                "MaxPrice": str(spot_price),
                "SpotInstanceType": "one-time",
                "InstanceInterruptionBehavior": "terminate",
            },
        }

        tags = (tags or {}) | {'spot_price': f'{spot_price:.04f}', 'spot_price_ratio': f"{ratio * 100:.02f}%"}

        result = self.create_ec2_instances(
            ec2_type=ec2_type,
            image=image,
            subnet=subnet,
            zone=zone,
            security_groups=security_groups,
            key_pair_id=key_pair_id,
            instance_market_options=instance_market_options,
            tags=tags,
            count=count,
            assign_public_ip=assign_public_ip,
            name=name,
            root_volume_size=root_volume_size,
            terminate_when_shutdown=terminate_when_shutdown
        )

        return result

    def destroy_ec2_instance(self, instance_id: str) -> bool:
        """Destroy EC2 instance"""
        try:
            self.ec2_client.terminate_instances(InstanceIds=[instance_id])
            return True
        except Exception as e:
            logger.error(f"Failed to destroy EC2 instance: {e}")
            return False


    def attach_ebs(
        self, volume_id: str, instance_id: str, ec2_username: str = 'ubuntu'
    ) -> bool:
        """Attach EBS volume to instance and initialize if needed

        Args:
            volume_id: EBS volume ID
            instance_id: EC2 instance ID

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            devices = self.decide_ebs_device_path(instance_id=instance_id, volume_id=volume_id)

            # Attach volume
            self.ec2_client.attach_volume(
                Device=devices['requested'], InstanceId=instance_id, VolumeId=volume_id
            )

            # Wait for attachment
            waiter = self.ec2_client.get_waiter("volume_in_use")
            waiter.wait(VolumeIds=[volume_id])

            # Get instance IP
            instance_ip = self.get_instance_public_ip(instance_id)
            if not instance_ip:
                logger.error(f"Failed to get IP for instance {instance_id}")
                return False

            # Connect to instance
            self.ssh_client.connect(
                instance_ip,
                username=ec2_username,
                key_filename=None,  # Will use default key or look for keys
                look_for_keys=True,
            )

            try:
                device = devices['expected']

                # Check if volume is multi-attach enabled
                volume_info = self.ec2_client.describe_volumes(VolumeIds=[volume_id])['Volumes'][0]
                is_multi_attach = volume_info.get('MultiAttachEnabled', False)
                fs_type = 'gfs2' if is_multi_attach else 'ext4'

                # Check if device exists and is ready
                check_cmd = f"ls -l {device}"
                stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
                if stderr.channel.recv_exit_status() != 0:
                    logger.error(f"Device {device} not found or not ready")
                    return False

                # Check if volume has a filesystem
                check_fs_cmd = f"sudo file -s {device}"
                stdin, stdout, stderr = self.ssh_client.exec_command(check_fs_cmd)
                output = stdout.read().decode().strip()

                # Initialize only if volume is empty (no filesystem)
                if "data" in output.lower() or "empty" in output.lower():
                    logger.info(f"Initializing new volume {volume_id}")

                    # Check OS type
                    stdin, stdout, stderr = self.ssh_client.exec_command("cat /etc/os-release")
                    os_info = stdout.read().decode().lower()

                    # Create filesystem based on multi-attach status and OS type
                    commands = []
                    if is_multi_attach:
                        # For GFS2, we need additional setup
                        if "ubuntu" in os_info or "debian" in os_info:
                            commands.append("sudo apt-get install -y gfs2-utils")
                        else:  # RHEL/Amazon Linux
                            commands.append("sudo yum install -y gfs2-utils")
                        commands.append(f"sudo mkfs.gfs2 -j 3 -p lock_dlm -t onwish:data_fs -O -q {device}")
                    else:
                        commands.append(f"sudo mkfs -t {fs_type} {device}")

                    for cmd in commands:
                        stdin, stdout, stderr = self.ssh_client.exec_command(cmd)
                        if stderr.channel.recv_exit_status() != 0:
                            logger.error(f"Failed to initialize volume: {cmd}")
                            return False
                else:
                    logger.info(f"Volume {volume_id} already has a filesystem, skipping initialization")

                return True

            finally:
                self.ssh_client.close()

        except Exception as e:
            logger.error(f"Failed to attach or initialize volume: {e}")
            return False

    def setup_mount_point(
        self, instance_id: str, ebs_id: str, mount_point :str= '/data', ec2_username: str = 'ubuntu', ssh_key_path: str | None = None,

    ) -> bool:
        """Setup mount point for attached volume with existing data

        Args:
            instance_id: EC2 instance ID
            ebs_id: EBS volume ID
            ssh_key_path: Path to SSH key file
        """
        try:
            # Get instance IP
            instance_ip = self.get_instance_public_ip(instance_id)
            if not instance_ip:
                logger.error(f"Failed to get IP for instance {instance_id}")
                return False

            # get actually device
            device = self.decide_ebs_device_path(instance_id, ebs_id)['expected']

            # Connect to instance
            self.ssh_client.connect(
                instance_ip,
                username=ec2_username,
                key_filename=ssh_key_path,
                look_for_keys=True,
            )

            # Check if volume is multi-attach enabled
            volume_info = self.ec2_client.describe_volumes(VolumeIds=[ebs_id])['Volumes'][0]
            is_multi_attach = volume_info.get('MultiAttachEnabled', False)

            # Determine filesystem type based on multi-attach status
            fs_type = 'gfs2' if is_multi_attach else 'ext4'
            mount_opts = 'defaults,noatime,_netdev' if is_multi_attach else 'defaults,nofail'

            # Check OS type
            stdin, stdout, stderr = self.ssh_client.exec_command("cat /etc/os-release")
            os_info = stdout.read().decode().lower()

            # Verify the device exists and is ready
            check_cmd = "ls -l " + device
            stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
            if stderr.channel.recv_exit_status() != 0:
                logger.error(f"Device {device} not found or not ready")
                return False

            # Create mount point if it doesn't exist
            commands = [
                f"sudo mkdir -p {mount_point} && sudo chmod 777 {mount_point}",
                # Check if already mounted
                f"mountpoint -q {mount_point} || sudo mount -t {fs_type} {device} {mount_point}",
                # Add to fstab if not already present
                f"grep -q '{device} {mount_point}' /etc/fstab || echo '{device} {mount_point} {fs_type} {mount_opts} 0 {2 if fs_type == 'ext4' else 0}' | sudo tee -a /etc/fstab"
            ]

            for cmd in commands:
                stdin, stdout, stderr = self.ssh_client.exec_command(cmd)
                if stderr.channel.recv_exit_status() != 0:
                    logger.error(f"Command failed: {cmd}")
                    return False

            # Verify mount was successful
            stdin, stdout, stderr = self.ssh_client.exec_command(f"mountpoint -q {mount_point}")
            if stderr.channel.recv_exit_status() != 0:
                logger.error(f"Failed to mount {device} at {mount_point}")
                return False

            logger.info(f"Successfully mounted {device} as {fs_type} at {mount_point}")
            return True

        except Exception as e:
            logger.error(f"Failed to setup mount point: {e}")
            return False
        finally:
            self.ssh_client.close()

    def run_task(
        self,
        instance_ip: str,
        username: str,
        command: str,
        ssh_key_path: str | None = None,
    ) -> tuple[bool, str]:
        """Setup environment and run task

        Args:
            instance_ip: Instance IP address
            username: SSH username
            command: Command to execute
            ssh_key_path: Optional path to SSH key file

        Returns:
            Tuple of (success: bool, output: str)
        """
        try:
            self.ssh_client.connect(
                instance_ip,
                username=username,
                key_filename=ssh_key_path,
                look_for_keys=True,
            )

            # Get the directory of the current file
            current_dir = os.path.dirname(os.path.abspath(__file__))
            install_script = os.path.join(current_dir, "install-from-main.sh")

            # Run install-from-main.sh locally
            install_cmd = f"bash {install_script} {instance_ip} {username}"
            result = subprocess.run(install_cmd, shell=True, capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"Install script failed {result.returncode}: {result.stderr}")

            # Run the provided command
            stdin, stdout, stderr = self.ssh_client.exec_command(command)
            exit_status = stderr.channel.recv_exit_status()
            cmd_output = stdout.read().decode().strip()
            cmd_error = stderr.read().decode().strip()

            if exit_status != 0:
                raise Exception(f"Command failed: {command}\nError: {cmd_error}")

            return True, cmd_output

        except Exception as e:
            logger.error(f"Failed to run task: {e}")
            return False, str(e)
        finally:
            self.ssh_client.close()

    def cleanup(self, instance_id: str, volume_id: str) -> bool:
        """Cleanup resources"""
        try:
            # Detach volume
            self.ec2_client.detach_volume(VolumeId=volume_id)
            waiter = self.ec2_client.get_waiter("volume_available")
            waiter.wait(VolumeIds=[volume_id])

            # Terminate instance
            self.ec2_client.terminate_instances(InstanceIds=[instance_id])
            waiter = self.ec2_client.get_waiter("instance_terminated")
            waiter.wait(InstanceIds=[instance_id])

            return True
        except Exception as e:
            logger.error(f"Failed to cleanup: {e}")
            return False

    def support_multi_attach(
        self,
        volume_ids: Optional[List[str]] = None,
        instance_ids: Optional[List[str]] = None,
        ec2_username: str = "ubuntu",
    ) -> Dict[str, Any]:
        """Check if EBS volumes and EC2 instances support multi-attach

        Args:
            volume_ids: Optional list of EBS volume IDs to check
            instance_ids: Optional list of EC2 instance IDs to check

        Returns:
            Dict containing support status for each EBS and EC2 resource
        """
        result = {
            "volumes": {},
            "instances": {},
            "overall_status": {"ebs_support": True, "ec2_support": True},
        }

        # Check EBS volumes if provided
        if volume_ids:
            try:
                volumes = self.ec2_client.describe_volumes(VolumeIds=volume_ids)[
                    "Volumes"
                ]
                for volume in volumes:
                    volume_id = volume["VolumeId"]
                    volume_type = volume["VolumeType"]

                    volume_result = {"support": False, "details": []}

                    # Check volume type
                    if volume_type not in ["io1", "io2"]:
                        volume_result["details"].append(
                            f"Volume type {volume_type} does not support multi-attach. Need io1 or io2."
                        )
                    else:
                        volume_result["support"] = True

                    result["volumes"][volume_id] = volume_result
                    result["overall_status"]["ebs_support"] &= volume_result["support"]

            except Exception as e:
                logger.error(f"Failed to check EBS volumes: {e}")
                result["volumes"]["error"] = str(e)
                result["overall_status"]["ebs_support"] = False

        # Check EC2 instances if provided
        if instance_ids:
            try:
                instances = self.ec2_client.describe_instances(
                    InstanceIds=instance_ids
                )["Reservations"][0]["Instances"]

                for instance in instances:
                    instance_id = instance["InstanceId"]
                    instance_result = {"support": False, "details": []}

                    # Check instance connectivity and requirements
                    instance_ip = instance.get("PublicIpAddress")
                    if not instance_ip:
                        instance_result["details"].append(
                            "Instance has no public IP address"
                        )
                        continue

                    try:
                        self.ssh_client.connect(
                            instance_ip,
                            username=ec2_username,
                            key_filename=self.ssh_key_path,
                        )

                        # Check for GFS2 kernel module
                        stdin, stdout, stderr = self.ssh_client.exec_command(
                            "lsmod | grep gfs2 || echo 'NOT_FOUND'"
                        )
                        gfs2_check = stdout.read().decode().strip()

                        # Check required packages
                        stdin, stdout, stderr = self.ssh_client.exec_command(
                            "rpm -q gfs2-utils dlm || echo 'NOT_FOUND'"
                        )
                        packages_check = stdout.read().decode().strip()

                        if "NOT_FOUND" in gfs2_check:
                            instance_result["details"].append(
                                "GFS2 kernel module not loaded"
                            )
                        elif "NOT_FOUND" in packages_check:
                            instance_result["details"].append(
                                "Required packages (gfs2-utils, dlm) not installed"
                            )
                        else:
                            instance_result["support"] = True

                        self.ssh_client.close()

                    except Exception as e:
                        instance_result["details"].append(
                            f"SSH connection failed: {str(e)}"
                        )

                    result["instances"][instance_id] = instance_result
                    result["overall_status"]["ec2_support"] &= instance_result[
                        "support"
                    ]

            except Exception as e:
                logger.error(f"Failed to check EC2 instances: {e}")
                result["instances"]["error"] = str(e)
                result["overall_status"]["ec2_support"] = False

        return result

    def create_ebs_volume(
        self,
        volume_type: str,
        size: int,
        zone: str,
        iops: Optional[int] = None,
        throughput: Optional[int] = None,
        is_multi_attach: bool = False,
        tags: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """Create an EBS volume with specified configuration

        Args:
            volume_type: EBS volume type ('gp2', 'gp3', 'io1', 'io2')
            size: Volume size in GB
            zone: Availability zone
            iops: IOPS (required for io1/io2, optional for gp3)
            throughput: Throughput in MiB/s (gp3 only)
            is_multi_attach: Whether volume should support multi-attach
            tags: List of tags to apply to volume

        Returns:
            Dict containing volume information

        Raises:
            ValueError: If configuration is invalid
        """
        # Validate volume type
        valid_types = ['gp2', 'gp3', 'io1', 'io2']
        if volume_type not in valid_types:
            raise ValueError(f"Volume type must be one of {valid_types}")

        # Multi-attach validation
        if is_multi_attach:
            if volume_type not in ['io1', 'io2']:
                raise ValueError("Multi-attach is only supported for io1 and io2 volumes")
            if not iops or iops < 1000:
                raise ValueError("Multi-attach requires minimum 1000 IOPS")

        # IOPS validation
        if volume_type in ['io1', 'io2']:
            if not iops:
                raise ValueError(f"IOPS is required for {volume_type}")
            if iops < 100 or iops > 100000:
                raise ValueError("IOPS must be between 100 and 100000 for io1/io2")
            if iops > size * 50:  # 50 IOPS per GB ratio limit
                raise ValueError("IOPS cannot exceed 50 IOPS per GB")
        elif volume_type == 'gp3':
            if iops and (iops < 3000 or iops > 16000):
                raise ValueError("IOPS must be between 3000 and 16000 for gp3")

        # Throughput validation
        if throughput is not None:
            if volume_type != 'gp3':
                raise ValueError("Throughput can only be specified for gp3 volumes")
            if throughput < 125 or throughput > 1000:
                raise ValueError("Throughput must be between 125 and 1000 MiB/s for gp3")

        # Prepare create volume request
        create_args = {
            "VolumeType": volume_type,
            "Size": size,
            "AvailabilityZone": zone,
            "MultiAttachEnabled": is_multi_attach,
        }

        if iops and volume_type in ['io1', 'io2', 'gp3']:
            create_args["Iops"] = iops

        if throughput and volume_type == 'gp3':
            create_args["Throughput"] = throughput

        if tags:
            create_args["TagSpecifications"] = [{
                'ResourceType': 'volume',
                'Tags': [{"Key": k, "Value": v} for k, v in tags.items()]
            }]

        try:
            response = self.ec2_client.create_volume(**create_args)

            # Wait for volume to become available
            waiter = self.ec2_client.get_waiter('volume_available')
            waiter.wait(VolumeIds=[response['VolumeId']])

            return response

        except Exception as e:
            logger.error(f"Failed to create EBS volume: {e}")
            raise

    @staticmethod
    def decide_ebs_volume(
        size: int,
        zone: str,
        is_multi_attach: bool = False,
        min_iops: Optional[int] = None,
        min_throughput: Optional[int] = None,
    ) -> Dict[str, Dict[str, Any]]:
        """Decide optimal EBS volume configuration based on requirements

        Args:
            size: Required size in GB
            zone: Availability zone
            is_multi_attach: Whether multi-attach is required
            min_iops: Minimum required IOPS
            min_throughput: Minimum required throughput in MiB/s

        Returns:
            Dict containing three recommended configurations:
            - balanced: Balanced performance/cost
            - performance_optimized: Maximum performance
            - cost_optimized: Minimum cost meeting requirements

        Example return value:
            {
                "balanced": {
                    "volume_type": "gp3",
                    "size": 100,
                    "iops": 3000,
                    "throughput": 125,
                    "is_multi_attach": False,
                    "estimated_cost": 10.0,
                    "description": "General purpose SSD with baseline performance"
                },
                "performance_optimized": {...},
                "cost_optimized": {...}
            }
        """
        # Price per GB-month for different volume types (example prices)
        price_per_gb = {
            "gp2": 0.10,
            "gp3": 0.08,
            "io1": 0.125,
            "io2": 0.125
        }

        # Price per IOPS-month (for io1/io2/gp3)
        price_per_iops = {
            "io1": 0.065 / 1000,  # $0.065 per IOPS
            "io2": 0.065 / 1000,
            "gp3": 0.005 / 1000  # $0.005 per IOPS above 3000
        }

        # Price per MB/s-month (for gp3)
        price_per_throughput = {
            "gp3": 0.04  # $0.04 per MB/s above 125
        }

        def calculate_monthly_cost(config: Dict) -> float:
            """Calculate estimated monthly cost for a configuration"""
            volume_type = config["volume_type"]
            monthly_cost = size * price_per_gb[volume_type]

            if volume_type in ["io1", "io2"]:
                monthly_cost += config["iops"] * price_per_iops[volume_type]
            elif volume_type == "gp3":
                if config["iops"] > 3000:
                    monthly_cost += (config["iops"] - 3000) * price_per_iops["gp3"]
                if config["throughput"] > 125:
                    monthly_cost += (config["throughput"] - 125) * price_per_throughput["gp3"]

            return round(monthly_cost, 2)

        # Initialize configurations
        configs = {}

        # Handle multi-attach requirement
        if is_multi_attach:
            base_iops = max(1000, min_iops or 1000)  # Multi-attach requires min 1000 IOPS

            # Only io1/io2 support multi-attach
            configs["balanced"] = {
                "volume_type": "io2",
                "size": size,
                "iops": base_iops,
                "is_multi_attach": True,
                "zone": zone
            }

            configs["performance_optimized"] = {
                "volume_type": "io2",
                "size": size,
                "iops": min(100000, size * 50),  # Max IOPS or 50 IOPS/GB
                "is_multi_attach": True,
                "zone": zone
            }

            configs["cost_optimized"] = {
                "volume_type": "io1",  # io1 might be cheaper in some regions
                "size": size,
                "iops": base_iops,
                "is_multi_attach": True,
                "zone": zone
            }

        else:
            # Balanced configuration (gp3 with moderate performance)
            configs["balanced"] = {
                "volume_type": "gp3",
                "size": size,
                "iops": max(3000, min_iops or 3000),
                "throughput": max(125, min_throughput or 125),
                "is_multi_attach": False,
                "zone": zone
            }

            # Performance optimized (maximum IOPS and throughput)
            configs["performance_optimized"] = {
                "volume_type": "io2",
                "size": size,
                "iops": min(100000, size * 50),  # Max IOPS or 50 IOPS/GB
                "is_multi_attach": False,
                "zone": zone
            }

            # Cost optimized
            if size <= 1000 and (not min_iops or min_iops <= 3000) and (not min_throughput or min_throughput <= 125):
                # gp3 with baseline performance is cheapest
                configs["cost_optimized"] = {
                    "volume_type": "gp3",
                    "size": size,
                    "iops": 3000,
                    "throughput": 125,
                    "is_multi_attach": False,
                    "zone": zone
                }
            else:
                # Calculate if gp3 with custom IOPS/throughput is cheaper than io2
                configs["cost_optimized"] = {
                    "volume_type": "gp3",
                    "size": size,
                    "iops": max(3000, min_iops or 3000),
                    "throughput": max(125, min_throughput or 125),
                    "is_multi_attach": False,
                    "zone": zone
                }

        # Add estimated cost and description to each configuration
        descriptions = {
            "balanced": "Balanced performance and monthly cost",
            "performance_optimized": "Maximum performance configuration",
            "cost_optimized": "Most cost-effective meeting requirements"
        }

        for config_type, config in configs.items():
            configs[config_type]['tags'] = {
                "estimated_cost": f"{calculate_monthly_cost(config)}/month",
                "description": descriptions[config_type]
            }
        return configs

    def detach_ebs(self, volume_id: str, instance_id: Optional[str] = None) -> bool:
        """Detach EBS volume from specified instance or all attached instances

        Args:
            volume_id: EBS volume ID
            instance_id: Optional specific instance ID to detach from. If None, detach from all instances.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get volume information
            volume = self.ec2_client.describe_volumes(VolumeIds=[volume_id])['Volumes'][0]

            # Check if volume is attached
            if volume['State'] == 'in-use':
                # Detach from specific instance or all instances
                for attachment in volume['Attachments']:
                    if instance_id is None or attachment['InstanceId'] == instance_id:
                        self.ec2_client.detach_volume(
                            VolumeId=volume_id,
                            InstanceId=attachment['InstanceId']
                        )

                # Wait for volume to become available if detaching from all instances
                # or if detaching from the last attached instance
                if instance_id is None or len(volume['Attachments']) == 1:
                    waiter = self.ec2_client.get_waiter('volume_available')
                    waiter.wait(VolumeIds=[volume_id])

            return True
        except Exception as e:
            logger.error(f"Failed to detach volume: {e}")
            return False

    def delete_ebs(self, volume_id: str) -> bool:
        """Delete EBS volume

        Args:
            volume_id: EBS volume ID

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # First detach if needed
            self.detach_ebs(volume_id)

            # Delete the volume
            self.ec2_client.delete_volume(VolumeId=volume_id)
            return True
        except Exception as e:
            logger.error(f"Failed to delete volume: {e}")
            return False

    def _is_nitro_instance(self, instance_type: str) -> bool:
        """Check if instance is running on Nitro system"""
        nitro_prefixes = {
            'a1', 'c5', 'c6', 'd3', 'd3en', 'g4', 'g5', 'i3en', 'i4i', 'inf1',
            'm5', 'm6', 'p3', 'p4', 'r5', 'r6', 't3', 't4', 'u-', 'x2', 'z1d'
        }
        return any(instance_type.startswith(prefix) for prefix in nitro_prefixes)

    def _is_nvme_volume(self, volume_type: str) -> bool:
        """Check if volume type uses NVMe interface"""
        # gp3 and io2 Block Express volumes use NVMe protocol
        nvme_types = {'gp3', 'io2'}
        return volume_type in nvme_types

    def decide_ebs_device_path(
        self, instance_id: str, volume_id: str, preferred_device: str = "/dev/sdf"
    ) -> Dict[str, str]:
        """Decide appropriate device paths for EBS volume attachment

        Args:
            instance_id: EC2 instance ID
            volume_id: EBS volume ID
            preferred_device: Preferred device path (default: /dev/sdf)

        Returns:
            Dict containing:
                'requested': Device path to use in attachment API
                'expected': Expected device path on the instance
                'type': Device type ('nvme', 'xvd', or 'sd')

        Raises:
            ValueError: If no available device paths or invalid configuration
        """
        try:
            # Get instance information
            instance = self.ec2_client.describe_instances(InstanceIds=[instance_id])
            instance_info = instance['Reservations'][0]['Instances'][0]
            instance_type = instance_info['InstanceType']
            block_devices = instance_info.get('BlockDeviceMappings', [])
            used_devices = {bd.get('DeviceName') for bd in block_devices}

            # Get volume information
            volume = self.ec2_client.describe_volumes(VolumeIds=[volume_id])['Volumes'][0]
            volume_type = volume['VolumeType']

            # Determine device naming convention based on both instance and volume
            is_nitro = self._is_nitro_instance(instance_type)
            is_nvme_vol = self._is_nvme_volume(volume_type)

            # Device will be exposed as NVMe if:
            # 1. Instance is running on Nitro system AND
            # 2. Either the volume type uses NVMe or it's a high-performance instance
            is_nvme = is_nitro and (
                is_nvme_vol or
                instance_type.startswith(('p3', 'p4', 'g4', 'g5', 'inf1', 'dl1'))
            )

            # Base prefixes for different device types
            device_types = {
                'nvme': '/dev/nvme',  # Will be numbered as nvme1n1, nvme2n1, etc.
                'xvd': '/dev/xvd',  # For Xen-based instances
                'sd': '/dev/sd'  # For IO volumes and some legacy instances
            }

            # Determine the appropriate device type
            if is_nvme:
                device_type = 'nvme'
            elif volume_type in ['io1', 'io2']:
                device_type = 'sd'
            else:
                device_type = 'xvd'

            # For NVMe devices, we need to calculate the expected device number
            if device_type == 'nvme':
                # Count existing NVMe devices (root volume is typically nvme0)
                nvme_count = sum(1 for dev in used_devices if 'nvme' in dev)
                # API still needs a /dev/sd* name for attachment
                requested_device = f"/dev/sd{chr(ord('f') + nvme_count)}"
                # But the actual device will be nvme[n]n1
                expected_device = f"/dev/nvme{nvme_count + 1}n1"

                logger.info(
                    f"NVMe device selected due to: Nitro system={is_nitro}, "
                    f"NVMe volume={is_nvme_vol}, Instance type={instance_type}"
                )
            else:
                # For non-NVMe devices, find first available suffix
                available_suffixes = [chr(x) for x in range(ord('f'), ord('p') + 1)]
                base_prefix = device_types[device_type]

                # Try preferred device first
                preferred_suffix = preferred_device[-1]
                if preferred_suffix.isalpha():
                    requested_device = f"/dev/sd{preferred_suffix}"
                    expected_device = f"{base_prefix}{preferred_suffix}"
                    if expected_device not in used_devices:
                        return {
                            'requested': requested_device,
                            'expected': expected_device,
                            'type': device_type
                        }

                # Find first available device name
                for suffix in available_suffixes:
                    requested_device = f"/dev/sd{suffix}"
                    expected_device = f"{base_prefix}{suffix}"
                    if expected_device not in used_devices:
                        break
                else:
                    raise ValueError(f"No available device paths for instance {instance_id}")

            return {
                'requested': requested_device,
                'expected': expected_device,
                'type': device_type
            }

        except Exception as e:
            logger.error(f"Failed to decide device path: {e}")
            raise

    def setup_ec2_for_ebs_multi_attach(
        self, instance_ip: str,
        username: str = 'ubuntu',
        ssh_key_path: str | None = None
    ) -> bool:
        """Setup EC2 instance for EBS multi-attach support

        Args:
            instance_ip: Instance IP address
            username: ssh username
            ssh_key_path: Path to SSH key file. If None, uses default key path

        Returns:
            bool: True if setup successful, False otherwise
        """
        try:
            # Connect to instance
            self.ssh_client.connect(
                instance_ip,
                username=username,
                key_filename=ssh_key_path,
            )

            # Check OS type and install required packages
            stdin, stdout, stderr = self.ssh_client.exec_command("cat /etc/os-release")
            os_info = stdout.read().decode().lower()
            logger.info(f"Detected OS: {os_info}")

            if "ubuntu" in os_info or "debian" in os_info:
                commands = [
                    "sudo apt-get update -y",
                    # Install linux-modules for the current kernel
                    "sudo apt-get install -y linux-modules-$(uname -r)",
                    "sudo apt-get install -y gfs2-utils dlm-controld",
                    # Ensure kernel module is available
                    "sudo apt-get install -y linux-modules-extra-$(uname -r)",
                    # Try loading the module
                    "sudo modprobe gfs2 || (sudo depmod -a && sudo modprobe gfs2)",
                    "echo 'gfs2' | sudo tee -a /etc/modules-load.d/gfs2.conf",
                ]
            else:  # RHEL/Amazon Linux
                commands = [
                    "sudo yum update -y",
                    # Install kernel-devel for the current kernel
                    "sudo yum install -y kernel-devel-$(uname -r)",
                    "sudo yum install -y gfs2-utils dlm",
                    # Rebuild kernel modules
                    "sudo depmod -a",
                    "sudo modprobe gfs2",
                    "echo 'gfs2' | sudo tee -a /etc/modules-load.d/gfs2.conf",
                ]

            self.ssh_client.exec_command('sudo echo "">~/setup-multi-attach.log')
            for cmd in commands:
                cmd_with_log = f"bash -c 'set -o pipefail; {cmd} 2>&1 | tee -a ~/setup-multi-attach.log; exit $?'"
                stdin, stdout, stderr = self.ssh_client.exec_command(cmd_with_log, timeout=300)
                exit_status = stderr.channel.recv_exit_status()
                output = stdout.read().decode().strip()

                if exit_status != 0:
                    logger.warning(f"Command failed: {cmd} - {output}")
                    # Don't fail immediately, try to continue with other commands
                    continue
                else:
                    logger.info(f"SUCCESS: {cmd}")

            # Verify GFS2 module is loaded
            stdin, stdout, stderr = self.ssh_client.exec_command("lsmod | grep gfs2")
            if stderr.channel.recv_exit_status() != 0:
                raise Exception("Failed to load GFS2 module after installation")

            return True

        except Exception as e:
            logger.error(f"Failed to setup multi-attach requirements: {e}")
            return False
        finally:
            self.ssh_client.close()

    def get_instance_public_ip(self, instance_id: str) -> str | None:
        """Get public IP address of an EC2 instance

        Args:
            instance_id: EC2 instance ID

        Returns:
            Public IP address or None if not found/available
        """
        try:
            response = self.ec2_client.describe_instances(InstanceIds=[instance_id])
            instance = response['Reservations'][0]['Instances'][0]
            return instance.get('PublicIpAddress')
        except Exception as e:
            logger.error(f"Failed to get public IP for instance {instance_id}: {e}")
            return None

    def attach_efs(
        self,
            instance_id: str,
            efs_id: str,
            mount_point: str = '/data',
            use_tls: bool = False,
            ec2_username: str = 'ubuntu'
    ) -> bool:
        """Attach EFS to EC2 instance

        Args:
            instance_id: EC2 instance ID
            efs_id: EFS filesystem ID
            mount_point: Mount point path (default: /data)
            use_tls: Whether to use TLS for mounting (default: False)
            ec2_username: SSH username (default: ubuntu)

        Returns:
            bool: True if successful, False otherwise
        """
        MOUNT_SCRIPT_TEMPLATE = '''#!/bin/bash
set -e

# Configuration
EFS_ID="{efs_id}"
REGION="{region}"
MOUNT_POINT="{mount_point}"
USE_TLS={use_tls}

# Get mount DNS
EFS_DNS="${{EFS_ID}}.efs.${{REGION}}.amazonaws.com"

# Check if already mounted
if mountpoint -q "${{MOUNT_POINT}}"; then
    echo "EFS already mounted at ${{MOUNT_POINT}}"
    exit 0
fi

# Install NFS client
if [ -f /etc/system-release ]; then
    # Amazon Linux / RHEL / CentOS
    sudo yum install -y nfs-utils
elif [ -f /etc/debian_version ]; then
    # Ubuntu / Debian
    sudo apt-get update && sudo apt-get install -y nfs-common
fi

# Create mount point
sudo mkdir -p "${{MOUNT_POINT}}"
sudo chmod 777 "${{MOUNT_POINT}}/"

# Mount command
if [ "${{USE_TLS}}" = true ]; then
    sudo mount -t nfs4 -o nfsvers=4.1,tls "${{EFS_DNS}}:/" "${{MOUNT_POINT}}"
else
    sudo mount -t nfs4 -o nfsvers=4.1 "${{EFS_DNS}}:/" "${{MOUNT_POINT}}"
fi

# Add to /etc/fstab for auto-mount
FSTAB_LINE="${{EFS_DNS}}:/ ${{MOUNT_POINT}} nfs4 defaults,noatime,_netdev 0 0"
if ! grep -qs "${{EFS_DNS}}" /etc/fstab; then
    echo "${{FSTAB_LINE}}" | sudo tee -a /etc/fstab
fi

# Verify mount
if mountpoint -q "${{MOUNT_POINT}}"; then
    echo "✅ EFS mounted successfully at: ${{MOUNT_POINT}}"
    df -h "${{MOUNT_POINT}}"
else
    echo "❌ Failed to mount EFS"
    exit 1
fi
'''

        try:
            # Get instance IP
            instance_ip = self.get_instance_public_ip(instance_id)
            if not instance_ip:
                logger.error(f"Failed to get IP for instance {instance_id}")
                return False

            # Connect to instance
            self.ssh_client.connect(
                instance_ip,
                username=ec2_username,
                key_filename=None,
                look_for_keys=True,
            )

            try:
                # Generate script with parameters
                script_content = MOUNT_SCRIPT_TEMPLATE.format(
                    efs_id=efs_id,
                    region=self.region,
                    mount_point=mount_point,
                    use_tls=str(use_tls).lower()
                )

                # Create script using echo and heredoc (no SFTP needed)
                script_path = f'/home/<USER>/mount-efs.sh'
                create_script_cmd = f'''cat << 'EOF' > {script_path}
{script_content}
EOF'''

                # Create and execute script
                commands = [
                    create_script_cmd,
                    f"chmod +x {script_path}",
                    f"bash {script_path}"
                ]

                for cmd in commands:
                    stdin, stdout, stderr = self.ssh_client.exec_command(cmd)
                    exit_status = stderr.channel.recv_exit_status()
                    output = stdout.read().decode().strip()
                    error = stderr.read().decode().strip()

                    if exit_status != 0:
                        logger.error(f"Command failed: {error}")
                        return False
                    elif output:
                        logger.info(output)

                return True

            finally:
                self.ssh_client.close()

        except Exception as e:
            logger.error(f"Failed to attach EFS: {e}")
            return False

    @staticmethod
    def estimate_duration(cmd: str) -> int:
            """Estimate task duration in hours based on command parameters

            Currently supports these patterns:
            - Training epochs: --epochs N or -e N
            - Batch size: --batch-size N or -b N
            - Dataset size: --dataset-size N or -d N

            Returns:
                Estimated duration in hours, default is 24 if can't estimate
            """
            import re

            # Default duration if estimation fails
            DEFAULT_DURATION = 24

            try:
                # Extract common parameters using regex
                epochs = re.search(r'--epochs[= ](\d+)|-e[= ](\d+)', cmd)
                epochs = int(next(x for x in epochs.groups() if x)) if epochs else 100

                batch_size = re.search(r'--batch-size[= ](\d+)|-b[= ](\d+)', cmd)
                batch_size = int(next(x for x in batch_size.groups() if x)) if batch_size else 32

                dataset_size = re.search(r'--dataset-size[= ](\d+)|-d[= ](\d+)', cmd)
                dataset_size = int(next(x for x in dataset_size.groups() if x)) if dataset_size else 10000

                # Mock calculation: (dataset_size * epochs) / (batch_size * GPU_factor)
                GPU_factor = 1000  # g4dn.12xlarge performance factor
                estimated_minutes = (dataset_size * epochs) / (batch_size * GPU_factor)

                # Add 20% buffer and convert to hours
                estimated_hours = math.ceil((estimated_minutes * 1.2) / 60)

                # Ensure minimum 1 hour and maximum 72 hours
                return max(1, min(72, estimated_hours))

            except Exception as e:
                logger.warning(f"Failed to estimate duration: {e}. Using default {DEFAULT_DURATION}h")
                return DEFAULT_DURATION

    def analyze_price_trends(self, ec2_type: str, zone: str) -> Dict:
        """Analyze price trends over the last 3 months"""
        our_history = self.get_our_price_history(ec2_type, zone)
        if not our_history:
            return {"error": "No price history found"}

        # Group by month
        monthly_prices = {}
        for entry in our_history:
            month = datetime.fromisoformat(entry["timestamp"]).strftime("%Y-%m")
            if month not in monthly_prices:
                monthly_prices[month] = []
            monthly_prices[month].append(entry["price"])

        # Calculate monthly statistics
        analysis = {
            "monthly_stats": {
                month: {
                    "count": len(prices),
                    "min": min(prices),
                    "max": max(prices),
                    "median": statistics.median(prices),
                    "avg": sum(prices) / len(prices)
                }
                for month, prices in monthly_prices.items()
            },
            "total_instances": len(our_history),
            "price_range": {
                "min": min(entry["price"] for entry in our_history),
                "max": max(entry["price"] for entry in our_history)
            }
        }

        return analysis

def run_training_task(cmd: str) -> tuple[bool, str]:
    """Run a model training task on a spot EC2 instance with EFS storage.

    This function handles the complete lifecycle of a training task:
    1. Creates a spot EC2 instance with GPU
    2. Mounts EFS storage for data and model persistence
    3. Executes the training command
    4. Terminates the instance after completion

    Args:
        cmd: Training command to execute on the instance

    Returns:
        Tuple[bool, str]: (success status, output/error message)
    """
    # EC2 configuration constants
    ec2_type = "g4dn.12xlarge"  # GPU instance for ML training
    region = "us-east-1"
    zone = "us-east-1d"
    subnet = 'subnet-0a7cc3fc09e0a4676'  # us-east-1d subnet
    security_groups = [
        'sg-00f5c37870854ddfd',  # test-onwish group
        'sg-077aa0bc29c69976e',  # sg-efs group for EFS access
    ]
    efs_id = 'fs-06feb7702299e8e64'  # Persistent storage filesystem
    image = "ami-0c1e04d04497876bc"  # Ubuntu 24.04 AMD64 for GBS
    key_pair = "gbs-main-server"
    default_user = 'ubuntu'

    ec2_manager = None
    instance_id = None

    try:
        # Calculate optimal duration and spot price
        duration = EC2Manager.estimate_duration(cmd)
        logger.info(f"Estimated task duration: {duration} hours")

        ec2_manager = EC2Manager(region=region)
        spot_price = ec2_manager.decide_spot_price(ec2_type, zone=zone, duration=duration)
        price = spot_price['recommended_price']
        ratio = spot_price['ratio']
        logger.info(f"Spot price: {price} (ratio: {ratio})")

        # Launch spot instance
        d = ec2_manager.create_spot_instance(
            ec2_type=ec2_type,
            image=image,
            key_pair_id=key_pair,
            subnet=subnet,
            security_groups=security_groups,
            spot_price=price,
            zone=zone
        )

        instance_id = list(d.keys())[0] if len(d) > 0 else None
        if not instance_id:
            return False, "Failed to create EC2 instance"

        # Wait for instance to be ready and obtain IP
        max_wait_time = 60  # seconds
        wait_interval = 5   # seconds
        start_time = time.time()

        instance_ip = None
        while (time.time() - start_time) < max_wait_time:
            instance_ip = ec2_manager.get_instance_public_ip(instance_id)
            if instance_ip:
                break
            time.sleep(wait_interval)

        if not instance_ip:
            return False, f"Timeout waiting for instance IP after {max_wait_time} seconds"

        # Mount EFS for persistent storage
        if not ec2_manager.attach_efs(
            instance_id=instance_id,
            efs_id=efs_id,
            mount_point='/data',
            ec2_username=default_user
        ):
            return False, "Failed to mount EFS storage"

        # Execute training command
        success, output = ec2_manager.run_task(
            instance_ip=instance_ip,
            username=default_user,
            command=cmd
        )

        return success, output

    except Exception as e:
        # Ensure we log the full error details
        logger.error(f"Training task failed: {str(e)}", exc_info=True)
        return False, f"Task failed with error: {str(e)}"
    finally:
        if ec2_manager and instance_id:
            ec2_manager.destroy_ec2_instance(instance_id=instance_id)




if __name__ == "__main__":
    import test_consts as tc

    mgr = EC2Manager()

    p = EC2Manager().decide_spot_price(tc.ec2_type, zone=tc.zone, duration=24)
    print(json.dumps(p, indent=2))
