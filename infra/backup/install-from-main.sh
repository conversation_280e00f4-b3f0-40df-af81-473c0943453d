#!/usr/bin/env bash

# Check if target server argument is provided
if [ -z "$1" ]; then
    echo "Error: Target server argument is required"
    echo "Usage: $0 <target_server> [TARGET_USER]"
    echo "Note: If TARGET_USER is not provided, 'ubuntu' will be used"
    exit 1
fi

TARGET_SERVER="$1"
TARGET_USER="${2:-ubuntu}"
CODE_ROOT=$(realpath "$(dirname "$0")/..")

# make remote directory & copy codes
scp -r $CODE_ROOT/requirements.txt $TARGET_USER@$TARGET_SERVER:/home/<USER>/
scp -r $CODE_ROOT/trading_system $TARGET_USER@$TARGET_SERVER:/home/<USER>/

# install requirements
ssh $TARGET_USER@$TARGET_SERVER "source ~/venv/bin/activate && while IFS= read -r pkg; do pip install --no-cache-dir "$pkg"; done < ~/requirements.txt"