#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试数据库查询性能的脚本
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
import time
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'user': 'postgres',
    'password': 'onWISH#2024!DB',
    'host': 'prod-onwish-app-cluster.cluster-cjuysus6851i.us-east-1.rds.amazonaws.com',
    'database': 'algotrading',
    'port': '5432'
}


def connect_to_db():
    """连接到数据库"""
    try:
        conn = psycopg2.connect(
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            host=DB_CONFIG['host'],
            database=DB_CONFIG['database'],
            port=DB_CONFIG['port']
        )
        print(f"成功连接到数据库 {DB_CONFIG['database']}")
        return conn
    except Exception as e:
        print(f"连接数据库时出错: {str(e)}")
        raise


def test_query_performance(table_name='daily_ohlcv', start_date='2020-01-01', end_date='2023-12-31', limit=None):
    """测试查询性能"""
    conn = connect_to_db()

    try:
        # 获取表的列名
        cursor = conn.cursor()
        cursor.execute(f"SELECT column_name FROM information_schema.columns WHERE table_name = '{table_name}'")
        columns = [row[0] for row in cursor.fetchall()]
        print(f"表 {table_name} 的列: {columns}")

        # 确定使用哪个列作为股票代码
        symbol_column = 'symbol'
        if 'symbol' not in columns:
            if 'ticker' in columns:
                symbol_column = 'ticker'
            elif 'stock_id' in columns:
                symbol_column = 'stock_id'
            else:
                print(f"警告: 无法确定股票代码列")
                return

        # 测试1: 查询不同日期范围的记录数
        print("\n测试1: 查询不同日期范围的记录数")
        date_ranges = [
            ('2023-01-01', '2023-12-31'),  # 1年
            ('2022-01-01', '2023-12-31'),  # 2年
            ('2021-01-01', '2023-12-31'),  # 3年
            ('2020-01-01', '2023-12-31'),  # 4年
        ]

        for start, end in date_ranges:
            query = f"""
            SELECT COUNT(*)
            FROM {table_name}
            WHERE date >= '{start}' AND date <= '{end}'
            """

            start_time = time.time()
            cursor.execute(query)
            count = cursor.fetchone()[0]
            elapsed = time.time() - start_time

            print(f"日期范围 {start} 到 {end}: {count} 条记录, 查询耗时 {elapsed:.2f} 秒")

        # 测试2: 查询不同数量的股票
        print("\n测试2: 查询不同数量的股票")

        # 获取所有股票代码
        start_time = time.time()
        cursor.execute(f"SELECT DISTINCT {symbol_column} FROM {table_name} order by {symbol_column}")
        all_symbols = [row[0] for row in cursor.fetchall()]
        elapsed = time.time() - start_time
        print(f"总共有 {len(all_symbols)} 只股票. 耗时：{elapsed:.2f} 秒")

        # 测试不同数量的股票
        symbol_counts = [1, 10, 100, min(1000, len(all_symbols)), len(all_symbols)]

        for count in symbol_counts:
            if count > len(all_symbols):
                continue

            symbols = all_symbols[:count]
            placeholders = ', '.join([f"'{s}'" for s in symbols])

            query = f"""
            SELECT COUNT(*)
            FROM {table_name}
            WHERE {symbol_column} IN ({placeholders})
            AND date >= '{start_date}' AND date <= '{end_date}'
            """

            start_time = time.time()
            cursor.execute(query)
            record_count = cursor.fetchone()[0]
            elapsed = time.time() - start_time

            print(f"{count} 只股票, {start_date} 到 {end_date}: {record_count} 条记录, 查询耗时 {elapsed:.2f} 秒")

        # 测试3: 使用LIMIT限制结果数量
        print("\n测试3: 使用LIMIT限制结果数量")
        limits = [100, 1000, 10000, 100000]

        for limit in limits:
            query = f"""
            SELECT {symbol_column}, date, open, high, low, close, volume
            FROM {table_name}
            WHERE date >= '{start_date}' AND date <= '{end_date}'
            LIMIT {limit}
            """

            start_time = time.time()
            cursor.execute(query)
            results = cursor.fetchall()
            elapsed = time.time() - start_time

            print(f"LIMIT {limit}: 查询耗时 {elapsed:.2f} 秒, 获取 {len(results)} 条记录")

        # 测试4: 查询完整数据并转换为DataFrame
        print("\n测试4: 查询完整数据并转换为DataFrame")

        # 使用一个较小的日期范围s
        test_start_date = '2020-01-01'
        test_end_date = '2023-01-31'

        query = f"""
        SELECT {symbol_column} as symbol, date, open, high, low, close, volume
        FROM {table_name}
        WHERE date between '{test_start_date}' AND '{test_end_date}'
        ORDER BY date
        """

        start_time = time.time()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute(query)
        results = cursor.fetchall()
        elapsed = time.time() - start_time
        print(f"查询 {test_start_date} 到 {test_end_date} 的数据: {len(results)} 条记录, 总耗时 {elapsed:.2f} 秒")

        start_time = time.time()
        df = pd.DataFrame(results)
        elapsed = time.time() - start_time
        print(f"pd 读入 {len(df)} 条记录, 总耗时 {elapsed:.2f} 秒")

        if not df.empty:
            print(f"数据示例:")
            print(df.head())

            # 计算每只股票的记录数
            if 'symbol' in df.columns:
                symbol_counts = df['symbol'].value_counts()
                print(f"\n每只股票的记录数 (前10只):")
                print(symbol_counts.head(10))

    except Exception as e:
        print(f"测试查询性能时出错: {str(e)}")
    finally:
        conn.close()


if __name__ == "__main__":
    # 测试查询性能
    test_query_performance()
