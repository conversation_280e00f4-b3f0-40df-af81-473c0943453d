import os
import json
import logging
import boto3
import signal
import sys
from botocore.exceptions import BotoCoreError, ClientError
from multiprocessing import Pool, cpu_count
from tqdm import tqdm
from typing import Dict, Any, Tuple
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from tenacity import retry, stop_after_attempt, wait_exponential
import asyncio
import aiofiles

# === Logging Setup ===
logging.basicConfig(
    level=logging.INFO,
    format="[%(levelname).01s] [%(asctime)s] [%(name)s:%(lineno)d] %(message)s",
)
logger = logging.getLogger(__name__)

# === CONFIG ===
AWS_ACCESS_KEY = os.environ.get('AWS_ACCESS_KEY_ID', "********************")
AWS_SECRET_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY', "hGiuXOEyaonhMGNPaFPLy8Js59hWj92Pn86HmTfU")
AWS_REGION = os.environ.get('AWS_DEFAULT_REGION', "us-east-1")

TOTAL_FILES = 82448410  # Estimated total files for progress display
LOCAL_FOLDER = Path('/data-old/stock_cnn_images')
BUCKET_NAME = 'onwish-gbs'
S3_PREFIX = 'stock_cnn_images'
PROGRESS_FILE = Path('s3-progress.json')
MAX_WORKERS = min(32, cpu_count() * 4)
S3_SCAN_CACHE = Path('s3-files-cache.json')

# Global variables for graceful shutdown
_should_exit = False

# Global lock for progress file access
progress_lock = asyncio.Lock()


def signal_handler(signum, frame):
    """Handle termination signals by setting exit flag and saving progress."""
    global _should_exit
    sig_name = signal.Signals(signum).name
    logger.warning(f"\nReceived signal {sig_name}")
    logger.info("Initiating graceful shutdown...")
    _should_exit = True


# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
signal.signal(signal.SIGTERM, signal_handler)  # Kill
signal.signal(signal.SIGHUP, signal_handler)  # Terminal closed


def get_s3_client():
    try:
        return boto3.client(
            's3',
            aws_access_key_id=AWS_ACCESS_KEY,
            aws_secret_access_key=AWS_SECRET_KEY,
            region_name=AWS_REGION
        )
    except (BotoCoreError, ClientError) as e:
        logger.error(f"Failed to create S3 client: {e}")
        raise


async def load_progress() -> Dict[str, Any]:
    """Load progress information from file."""
    if PROGRESS_FILE.exists():
        with open(PROGRESS_FILE, 'r') as f:
            s = f.read().strip()
        if s:
            data = json.loads(s)
            in_progress = sorted(list(data.get('in_progress', [])))
            if len(in_progress) > 0:
                # Parse the first in-progress key to get starting point
                # Format: stock_cnn_images/window/year/company/file.png
                parts = in_progress[0].split('/')
                if len(parts) >= 5:  # Make sure we have enough parts including filename
                    return {
                        'window': parts[1],
                        'year': parts[2],
                        'company': parts[3],
                        'file': parts[4],
                        'total_uploaded': data.get('total_uploaded', 0),
                        'in_progress': set(),  # Always start with empty set
                        'last_update': datetime.now().isoformat()
                    }
    return {
        'window': None,
        'year': None,
        'company': None,
        'file': None,
        'total_uploaded': 0,
        'in_progress': set(),  # Empty set for new progress
        'last_update': datetime.now().isoformat()
    }


async def save_progress(progress: Dict[str, Any]):
    """Save current progress to file with file locking."""
    try:
        async with aiofiles.open(PROGRESS_FILE, 'w') as f:
            async with progress_lock:
                save_data = {
                    'window': progress.get('window'),
                    'year': progress.get('year'),
                    'company': progress.get('company'),
                    'file': progress.get('file'),
                    'in_progress': sorted(list(progress.get('in_progress', set()))),
                    # Convert set to list before sorting
                    'total_uploaded': progress.get('total_uploaded', 0),
                    'last_update': datetime.now().isoformat(),
                }
                await f.write(json.dumps(save_data, indent=2))
    except Exception as e:
        logger.error(f"Failed to save progress: {e}")
        raise  # Re-raise to handle in caller


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
async def process_file(args: Tuple[str, str, str, str]) -> None:
    """Process a file in a company folder. raise if any error.
    Args:
        args: Tuple containing (window, year, company, file_path)
    """
    window, year, company, file_path = args
    s3_key = f"{S3_PREFIX}/{window}/{year}/{company}/{file_path.name}"
    s3_client = get_s3_client()
    s3_client.upload_file(str(file_path), BUCKET_NAME, s3_key)


def init_worker():
    """Initialize worker process to handle signals."""
    signal.signal(signal.SIGINT, signal.SIG_IGN)
    signal.signal(signal.SIGTERM, signal.SIG_IGN)


async def get_existing_s3_files() -> set:
    """Get a set of all existing files in S3 bucket with the given prefix."""
    # Try to load from stored file first
    if S3_SCAN_CACHE.exists():
        try:
            logger.info("Loading S3 files from stored list...")
            with open(S3_SCAN_CACHE, 'rt', encoding='utf-8') as f:
                stored_files = set(json.load(f))
                logger.info(f"Loaded {len(stored_files):,} files from stored S3 list")
                return stored_files
        except Exception as e:
            logger.warning(f"Failed to load stored S3 files list: {e}")

    # If no stored list, scan S3
    logger.info(f"Fetching existing files from S3 bucket '{BUCKET_NAME}/{S3_PREFIX}'...")
    existing_files = set()
    s3_client = get_s3_client()

    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        page_iterator = paginator.paginate(
            Bucket=BUCKET_NAME,
            Prefix=S3_PREFIX
        )

        for page in tqdm(page_iterator, desc="Fetching S3 files"):
            if 'Contents' in page:
                for obj in page['Contents']:
                    existing_files.add(obj['Key'])

        # Save the list
        try:
            with open(S3_SCAN_CACHE, 'w') as f:
                json.dump(list(existing_files), f, indent=2)
            logger.info("S3 files list saved successfully")
        except Exception as e:
            logger.warning(f"Failed to save S3 files list: {e}")

        logger.info(f"Found {len(existing_files):,} existing files in S3")
        return existing_files
    except Exception as e:
        logger.error(f"Failed to fetch existing S3 files: {e}")
        raise


async def main():
    """Main execution function with ordered processing and resume capability."""
    progress = await load_progress()
    total_processed = progress.get('total_uploaded', 0)
    rc = 0

    # Create semaphore for controlling concurrent uploads
    upload_semaphore = asyncio.Semaphore(MAX_WORKERS)  # Use MAX_WORKERS for upload concurrency

    try:
        existing_s3_files = await get_existing_s3_files()
        windows = sorted(w.name for w in LOCAL_FOLDER.iterdir() if w.is_dir() and w.name in ['5day', '20day', '60day'])
        start_window_idx = windows.index(progress['window']) if progress['window'] in windows else 0

        for window in windows[start_window_idx:]:
            if _should_exit:
                break

            progress['window'] = window
            window_path = LOCAL_FOLDER / window
            years = sorted(y.name for y in window_path.iterdir() if y.is_dir())
            start_year_idx = years.index(progress['year']) if progress['year'] in years else 0

            for year in years[start_year_idx:]:
                if _should_exit:
                    break

                progress['year'] = year
                year_path = window_path / year
                companies = sorted(c.name for c in year_path.iterdir() if c.is_dir())
                start_company_idx = companies.index(progress['company']) if progress['company'] in companies else 0

                with tqdm(
                    total=TOTAL_FILES,
                    initial=total_processed,
                    desc=f"Processing {window}/{year}"
                ) as pbar:
                    for company in companies[start_company_idx:]:
                        if _should_exit:
                            break

                        try:
                            company_path = LOCAL_FOLDER / window / year / company
                            files = sorted(f for f in company_path.glob('*.png'))
                            start_file_idx = 0
                            if progress['file'] and company == progress['company']:
                                try:
                                    start_file_idx = next(i for i, f in enumerate(files) if f.name == progress['file'])
                                except StopIteration:
                                    start_file_idx = 0

                            # Reset file progress when moving to new company
                            progress['company'] = company
                            pbar.set_postfix({
                                'company': company,
                                'file': 'starting...'
                            })

                            for file_path in files[start_file_idx:]:
                                if _should_exit:
                                    break

                                s3_key = f"{S3_PREFIX}/{window}/{year}/{company}/{file_path.name}"
                                progress['file'] = None
                                if s3_key not in existing_s3_files:
                                    async with upload_semaphore:
                                        progress['in_progress'].add(s3_key)
                                        await process_file((window, year, company, file_path))
                                        progress['in_progress'].remove(s3_key)

                                total_processed += 1
                                progress['total_uploaded'] = total_processed
                                pbar.update(1)
                                pbar.set_postfix({
                                    'company': company,
                                    'file': file_path.name
                                })
                        finally:
                            await save_progress(progress)

        if _should_exit:
            logger.info(f"Upload interrupted and progress saved! Total files processed: {total_processed:,}")
        else:
            logger.info(f"Upload completed successfully! Total files processed: {total_processed:,}")

    except Exception as e:
        logger.exception(f"Fatal error: {e}")
        rc = 1
    finally:
        await save_progress(progress)

    return rc


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
