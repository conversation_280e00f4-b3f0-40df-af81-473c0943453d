# ec2_type = "g4dn.12xlarge"
ec2_type = 'c6a.large'
# ec2_type = 'g4dn.xlarge'
region = "us-east-1"
vpc = 'vpc-0514c93fc09cc7c4d'  # mixed-east-1
zone = "us-east-1d"
subnet = 'subnet-0a7cc3fc09e0a4676'  # us-east-1d
# zone = "us-east-1f"
# subnet='subnet-067864b5f907940e1' # us-east-1f
security_groups = ['sg-00f5c37870854ddfd',  # test-onwish
                   'sg-077aa0bc29c69976e',  # sg-efs
                   ]

efs_id = 'fs-06feb7702299e8e64'

image = "ami-0c1e04d04497876bc" # ubuntu 24.04 amd64 for GBS
# image = "ami-052655912f077d325"  # AI-Algo-trading x86
# image = "ami-084568db4383264d4"  # ubuntu 24.04 LTS x86
# image = "ami-0c4e709339fa8521a"  # ubuntu 24.04 LTS arm
# key_pair = "sam-mba"
key_pair = "gbs-main-server"
default_user = 'ubuntu'  # for ubuntu

