#!/bin/bash

# 配置参数
EFS_ID="fs-06feb7702299e8e64"
REGION="us-east-1"
MOUNT_POINT="/data"
USE_TLS=false
USE_AWS_TOOL=false

# 获取挂载 DNS
if [ "$USE_TLS" = true ]; then
  EFS_DNS="${EFS_ID}.efs.${REGION}.amazonaws.com"
else
  EFS_DNS="${EFS_ID}.efs.${REGION}.amazonaws.com"
fi

# 安装 NFS 客户端
if [ -f /etc/system-release ]; then
  # Amazon Linux / RHEL / CentOS
  sudo yum install -y nfs-utils amazon-efs-utils
  # 安装 fpart 工具用于高效复制
  sudo yum install -y fpart

elif [ -f /etc/debian_version ]; then
  # Ubuntu / Debian
  sudo apt-get update && sudo apt-get install -y nfs-common binutils
  sudo git clone https://github.com/aws/efs-utils
  cd efs-utils
  ./build-deb.sh
  sudo apt-get install -y ./build/amazon-efs-utils*deb
  # 安装 fpart 工具用于高效复制
  sudo apt-get install -y fpart

fi

# 创建挂载点
sudo mkdir -p "$MOUNT_POINT"
sudo chmod 777 "$MOUNT_POINT/"

# 挂载命令
if [ "$USE_AWS_TOOL" = true ]; then
  if [ "$USE_TLS" = true ]; then
    sudo mount -t nfs4 -o nfsvers=4.1,tls "${EFS_DNS}:/" "$MOUNT_POINT"
  else
    sudo mount -t nfs4 -o nfsvers=4.1 "${EFS_DNS}:/" "$MOUNT_POINT"
  fi
else
  if [ "$USE_TLS" = true ]; then
      MOUNT_OPTIONS="tls"
  else
      MOUNT_OPTIONS="defaults"
  fi
  # 使用 efs-utils 挂载
  sudo mount -t efs -o $MOUNT_OPTIONS ${EFS_ID}:/ ${MOUNT_POINT}
fi

# 写入 /etc/fstab 实现自动挂载
FSTAB_LINE="${EFS_DNS}:/ ${MOUNT_POINT} nfs4  defaults,noatime,_netdev,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,nofail  0 0"

if ! grep -qs "$EFS_DNS" /etc/fstab; then
  echo "$FSTAB_LINE" | sudo tee -a /etc/fstab
  # reload fstab
  sudo systemctl daemon-reload
fi


# 验证挂载成功
echo -e "\n✅ EFS 挂载完成！已挂载至: $MOUNT_POINT"
df -h "$MOUNT_POINT"
