"""
Configuration (configure-able) definitions.
"""

from os import getenv, linesep
from optenum import Options, Option
from gettext import gettext as _, pgettext as _p
from .enums import (
    DatabaseType,
)

# Prefix & prefix with underscore "_".
# used for env var key or other keys
APP_PREFIX = getenv(f"APP_PREFIX", "").upper()
APP_PREFIX_ = f"{APP_PREFIX}_" if APP_PREFIX else ""
# APP_PREFIX_LOWER = APP_PREFIX.lower()


class EnvKey(Options):
    _p("doc string", """Predefined environment variable names""")

    APP_PREFIX = "APP_PREFIX", _p("option text", "Application environment var prefix. default is empty")

    # app related
    APP_NAME = f"{APP_PREFIX_}APP_NAME", _p("option text", "Application name as identity. lower case, no blank.")
    APP_TITLE = f"{APP_PREFIX_}APP_TITLE", _p("option text", "Application title to display.")
    APP_DOMAIN = f"{APP_PREFIX_}APP_DOMAIN", _p("option text", "Application domain name")
    APP_PORT = f"{APP_PREFIX_}APP_PORT", _p("option text", "Application domain port")
    DOMAIN = f"{APP_PREFIX_}DOMAIN", _p("option text", "General domain name for almost all other apps.")

    # general
    DEBUG = f"{APP_PREFIX_}DEBUG", _p("option text", "Wether Debug enabled. default is False")
    DATA_DIR = f"{APP_PREFIX_}DATA_DIR", _p("option text", f"Folder to store data. default is /data/${APP_NAME}")
    LOG_DIR = f"{APP_PREFIX_}LOG_DIR", _p("option text", f"Folder to store log files. default is /var/log/${APP_NAME}")
    LOG_LEVEL = f"{APP_PREFIX_}LOG_LEVEL", _p(
        "option text",
        'Level string such as "DEBUG", "INFO" and etc to set which level log will be output. default is WARNING',
    )

    # django
    ALLOWED_HOSTS = f"{APP_PREFIX_}ALLOWED_HOSTS", _p(
        "option text",
        'Allowed hosts saperated by ",". Or "*" means all. General it is used in Django. default is empty',
    )
    LANGUAGE_CODE = f"{APP_PREFIX_}LANGUAGE_CODE", _p(
        "option text",
        "Language local code such as en-US, zh-Hans and etc. Default is en-US",
    )
    TIME_ZONE = f"{APP_PREFIX_}TIME_ZONE", _p(
        "option text",
        "Timezone database name such as Asia/Shanghai, Etc/GMT+8 and so on. Default is Etc/UTC",
    )

    # database
    DB_TYPE = f"{APP_PREFIX_}DB_TYPE", _p(
        "option text",
        f"Type of the database that app connects. support {DatabaseType.names}. default is {DatabaseType.SQLITE}",
    )
    DB_HOST = f"{APP_PREFIX_}DB_HOST", _p(
        "option text",
        f"Database host or IP address that app connects. default is 127.0.0.1",
    )
    DB_PORT = f"{APP_PREFIX_}DB_PORT", _p(
        "option text",
        f"Database serving port that app connects. default is depends on database type, 5432 for {DatabaseType.POSTGRESQL}, 3306 for {DatabaseType.MYSQL} ",
    )
    DB_NAME = f"{APP_PREFIX_}DB_NAME", _p(
        "option text",
        f"Database name that app connects. if {DB_TYPE} is {DatabaseType.SQLITE}, this should be the full path of db file.",
    )
    DB_USER = f"{APP_PREFIX_}DB_USER", _p("option text", f"User name used to connect the database.")
    DB_PASSWORD = f"{APP_PREFIX_}DB_PASSWORD", _p("option text", f"User password used to connect the database.")

    # CAS
    CAS_URL = f"{APP_PREFIX_}CAS_URL", _p("option text", "CAS service url.")

    # JWT
    JWT_SIGNING_KEY = f"{APP_PREFIX_}JWT_SIGNING_KEY", _p("option text", "JWT signing key.")

    @classmethod
    def get_help_str(cls) -> str:
        """help string of key - description list."""
        sb: list[str] = []
        d = cls.key_desc_dict()
        for k, v in d.items():
            sb.append(k + " - " + v)
        return linesep.join(sb)

    @classmethod
    def _get_recursive_key_opt_dict(cls) -> dict[str, Option]:
        """dict of {key, option} pairs by recursively invoking"""
        d: dict[str, Option] = cls.items
        super_cls = cls.__base__
        if cls is not EnvKey and super_cls and hasattr(super_cls, "_get_recursive_key_opt_dict"):
            d1: dict[str, Option] = super_cls._get_recursive_key_opt_dict()
            d1.update(d)
        else:
            d1 = d
        return d1

    @classmethod
    def get_key_desc_dict(cls) -> dict[str, str]:
        """map of {key, description} pairs"""
        d: dict[str, Option] = cls._get_recursive_key_opt_dict()
        return {v.code: v.text for k, v in d.items()}

    @classmethod
    def get_key_set(cls) -> set[str]:
        """set of available environment variable keys"""
        d: dict[str, Option] = cls._get_recursive_key_opt_dict()
        return {v.code for k, v in d.items()}

    @classmethod
    def get_key_value_dict(cls) -> dict[str]:
        """map of {key, value} pairs. values are read from environment variables."""
        d: dict[str] = {}
        for k in cls.get_key_set():
            v = getenv(k, "__DEFAULT__")
            d[k] = v
        return d


APP_NAME: str = getenv(EnvKey.APP_NAME, "").lower()
APP_TITLE: str = getenv(EnvKey.APP_TITLE, APP_NAME)
DOMAIN: str = getenv(EnvKey.DOMAIN, "").lower()  # whole site (many apps)
APP_DOMAIN: str = getenv(EnvKey.APP_DOMAIN, DOMAIN).lower()  # current app
APP_PORT: str = getenv(EnvKey.APP_PORT, "")

# if len(APP_NAME) == 0:
#     raise RuntimeError(f'{EnvKey.APP_NAME} is not set.')
try:
    if len(APP_PORT) > 0:
        port = int(APP_PORT)
        if port < 1 or port > 65535:
            raise ValueError()
except ValueError:
    raise RuntimeError(f"{EnvKey.APP_PORT}={APP_PORT} is not a valid port.")

DEBUG: bool = getenv(EnvKey.DEBUG, "false").lower() in ("true", "1", "y", "yes")
DATA_DIR: str = getenv(EnvKey.DATA_DIR, f"/data/{APP_NAME}")
LOG_DIR = getenv(EnvKey.LOG_DIR, f"/var/log/{APP_NAME}")
LOG_LEVEL = getenv(EnvKey.LOG_LEVEL, "DEBUG" if DEBUG else "WARNING")

# django settings
ALLOWED_HOSTS = getenv(EnvKey.ALLOWED_HOSTS)
LANGUAGE_CODE = getenv(EnvKey.LANGUAGE_CODE, "en")
TIME_ZONE = getenv(EnvKey.TIME_ZONE, "Etc/UTC")

# ----- database -----
# DB_TYPE: sqlite3, postgresql, mysql
DB_TYPE = getenv(EnvKey.DB_TYPE, DatabaseType.SQLITE)
DB_HOST = getenv(EnvKey.DB_HOST, "127.0.0.1")
DB_PORT = int(getenv(EnvKey.DB_PORT, 5432 if DB_TYPE == "postgresql" else 3306))
DB_NAME = getenv(EnvKey.DB_NAME, APP_NAME or "db")
DB_USER = getenv(EnvKey.DB_USER, APP_NAME or "root")
DB_PASSWORD = getenv(EnvKey.DB_PASSWORD, None)

# ----- redis -----

# REDIS_HOST = getenv(f'{APP_PREFIX}_REDIS_HOST', '127.0.0.1')
# REDIS_PORT = int(getenv(f'{APP_PREFIX}_REDIS_PORT', 6379))
# REDIS_PASSWORD = getenv(f'{APP_PREFIX}_REDIS_PASSWORD', '')
# REDIS_DB = getenv(f'{APP_PREFIX}_REDIS_DB', 3)

# ----- cas -----

CAS_URL = getenv(EnvKey.CAS_URL, f"https://auth.{DOMAIN}")

# ----- JWT -----

JWT_SIGNING_KEY = getenv(EnvKey.JWT_SIGNING_KEY, "JWT-SIGNING-KEY-oisf98&^@#(@")


# if DEBUG:
#     for k, v in EnvKey.key_value_dict().items():
#         print(k, "=", v)
