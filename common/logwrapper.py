"""log wrapper"""

import logging
import logging.config
import os
from pathlib import Path

from pycommon.config import APP_NAME, DEBUG, LOG_DIR, LOG_LEVEL, APP_PREFIX
from .utils.terminal import debug

LOG_PREFIX: str = APP_PREFIX.lower()


def getLogger(name: str) -> logging.Logger:
    # keep the camel stlye name so that it can replace logging.getLogger.
    name = name.lower()
    if LOG_PREFIX:
        if not name.startswith(f"{LOG_PREFIX}."):
            name = f"{LOG_PREFIX}.{name}"

    logger = logging.getLogger(name)
    return logger


def config_logging(fname: str = APP_NAME, prefix: str = f"{LOG_PREFIX}", level: str = LOG_LEVEL):
    if prefix:
        p = Path(f"{LOG_DIR}") / ".".join([prefix, fname, "log"])
    else:
        p = Path(f"{LOG_DIR}") / (fname + ".log")

    p.parent.mkdir(exist_ok=True)
    p.touch()

    logging.config.dictConfig(
        {
            "version": 1,
            "disable_existing_loggers": True,
            "level": os.getenv("LOG_LEVEL", "INFO" if DEBUG else "WARNING"),
            "formatters": {
                "standard": {
                    "format": "%(levelname)-8s [%(asctime)s] [%(process)-6d] [%(threadName)-8s] [%(name)s:%(lineno)d] %(message)s"
                },
                "colorful": {
                    "format": "%(log_color)s[%(levelname).01s] [%(asctime)s] [%(processName).04s][%(threadName).016s][%(name)s:%(lineno)d] %(message)s",
                    "()": "colorlog.ColoredFormatter",
                    "reset": True,
                    "log_colors": {
                        "DEBUG": os.getenv("LOG_COLOR_DEBUG", "thin_black"),
                        "INFO": os.getenv("LOG_COLOR_INFO", "white"),
                        "WARNING": "yellow",
                        "ERROR": "red",
                        "CRITICAL": "red,bg_white",
                    },
                    "secondary_log_colors": {},
                },
                "simple": {
                    "format": "[%(levelname).1s|%(asctime)s|%(name)s:%(lineno)d] %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S",
                },
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "formatter": os.getenv("LOG_FORMATTER", "colorful"),
                    "level": "DEBUG",
                },
                "file_rotate": {
                    "class": "logging.handlers.RotatingFileHandler",
                    "formatter": "verbose",
                    "filename": p,
                    "maxBytes": 1048576 * 20,
                    "backupCount": 10,
                    "level": "DEBUG",
                },
            },
            "loggers": {
                LOG_PREFIX: {
                    "handlers": ["console", "file_rotate"],
                    "level": level,
                },
                **{name: {"level": "INFO"} for name in [""]},
                **{name: {"level": "WARNING"} for name in ["asyncio", "unicorn", "uvicorn"]},
            },
        }
    )

    debug(f"logging configured ({level}). {p}")


config_logging()
