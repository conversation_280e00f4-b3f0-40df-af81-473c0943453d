"""language enhancements"""

import asyncio
import importlib
import json
import operator
import time
import traceback
from abc import ABC
from datetime import datetime, date
from enum import Enum, StrEnum, IntEnum
from functools import cache, wraps
from typing import (
    Any,
    Callable,
    List,
    Self,
    Sequence,
    Type,
    Union,
    Iterator,
    Optional,
    Iterable,
    TypeVar,
)


class LazyObject:
    """Lazy evaluated object
    django has a SimpleLazyObject.
    but this has no dependency.
    """

    _wrapped: Any = None
    _is_init: bool = False

    def __init__(self, factory: Callable) -> None:
        # Assign using __dict__ to avoid the setattr method.
        self.__dict__["_factory"] = factory

    def _setup(self) -> None:
        self._wrapped = self.__dict__["_factory"]()
        self._is_init = True

    @staticmethod
    def new_method_proxy(func: Callable) -> Callable:
        """
        Util function to help us route functions
        to the nested object.
        """

        def inner(self, *args: Any) -> Any:  # type: ignore
            if not self._is_init:
                self._setup()
            return func(self._wrapped, *args)

        return inner

    def __setattr__(self, name: str, value: Any) -> None:
        # These are special names that are on the LazyObject.
        # every other attribute should be on the wrapped object.
        if name in {"_is_init", "_wrapped"}:
            self.__dict__[name] = value
        else:
            if not self._is_init:
                self._setup()
            setattr(self._wrapped, name, value)

    def __delattr__(self, name: str) -> None:
        if name == "_wrapped":
            raise TypeError("can't delete _wrapped.")
        if not self._is_init:
            self._setup()
        delattr(self._wrapped, name)

    __getattr__ = new_method_proxy(getattr)
    __bytes__ = new_method_proxy(bytes)
    __str__ = new_method_proxy(str)
    __bool__ = new_method_proxy(bool)
    __dir__ = new_method_proxy(dir)
    __hash__ = new_method_proxy(hash)
    __class__ = property(new_method_proxy(operator.attrgetter("__class__")))  # type: ignore
    __eq__ = new_method_proxy(operator.eq)
    __lt__ = new_method_proxy(operator.lt)
    __gt__ = new_method_proxy(operator.gt)
    __ne__ = new_method_proxy(operator.ne)
    __getitem__ = new_method_proxy(operator.getitem)
    __setitem__ = new_method_proxy(operator.setitem)
    __delitem__ = new_method_proxy(operator.delitem)
    __iter__ = new_method_proxy(iter)
    __len__ = new_method_proxy(len)
    __contains__ = new_method_proxy(operator.contains)


def retry_sync(
    retries: int = 3,
    cooldown: int = 2,
    backoff: float = 1.5,
    exceptions: List[Type[BaseException]] = [],
) -> Callable:
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            for i in range(retries):
                try:
                    return func(*args, **kwargs)
                except tuple(exceptions) as e:
                    if i == retries - 1:
                        raise e
                    else:
                        time.sleep(cooldown * backoff**i)

        return wrapper

    return decorator


def retry(
    retries: int = 3,
    cooldown: int = 2,
    backoff: float = 1.5,
    exceptions: List[Type[BaseException]] = [],
) -> Callable:
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            for i in range(retries):
                try:
                    return await func(*args, **kwargs)
                except tuple(exceptions) as e:
                    if i == retries - 1:
                        raise e
                    else:
                        await asyncio.sleep(cooldown * backoff**i)

        return wrapper

    return decorator


class BaseEnum(Enum):
    @classmethod
    def choices(cls, extra: Union[dict[str, Any], Sequence[tuple[Any, str]]] = ()) -> tuple[tuple[Any, str], ...]:
        r = [(e.value, e.name if isinstance(e.value, str) else f"{e.name} | {e.value}") for e in cls]
        if extra:
            if isinstance(extra, dict):
                r.extend(
                    [
                        (
                            value,
                            name if isinstance(value, str) else f"{name} | {value}",
                        )
                        for name, value in extra.items()
                    ]
                )

            elif isinstance(extra, Sequence):
                r.extend(
                    [
                        (
                            value,
                            name if isinstance(value, str) else f"{name} | {value}",
                        )
                        for value, name in extra
                    ]
                )
        return tuple(r)

    @classmethod
    def choices_by_name(cls, prefix: str, suffix: str) -> Sequence[tuple[Any, str]]:
        r = [
            (e.value, e.name if isinstance(e.value, str) else f"{e.name} | {e.value}")
            for e in cls
            if (prefix and e.name.startswith(prefix)) or (suffix and e.name.endswith(suffix))
        ]
        r = sorted(r, key=lambda x: x[0])
        return r

    @classmethod
    def choices_by_value(cls, min_val: Self, max_val: Self) -> Sequence[tuple[Any, str]]:
        r = [
            (e.value, e.name if isinstance(e.value, str) else f"{e.name} | {e.value}")
            for e in cls
            if min_val.value <= e.value <= max_val.value
        ]
        r = sorted(r, key=lambda x: x[0])
        return r

    @classmethod
    @cache
    def values(cls) -> List:
        """ordered values as defined"""
        return [e.value for e in cls if not e.name.startswith("_")]

    @property
    def emoji(self) -> str:
        from common.utils.strutil import get_emoji

        s = str(self.value).lower()
        return get_emoji(s)


class BaseStrEnum(StrEnum, BaseEnum):
    pass


class BaseIntEnum(IntEnum, BaseEnum):
    pass


class JsonEncoder(json.JSONEncoder):
    def default(self, obj: Any) -> Any:
        if isinstance(obj, datetime):
            # strictly use the format
            return obj.isoformat(timespec="microseconds")
            # strftime("%Y-%m-%dT%H:%M:%S.%fZ"))

        # Let the base class default method raise the TypeError
        return json.JSONEncoder.default(self, obj)


class JsonDecoder(json.JSONDecoder):
    def decode(self, obj, *args, **kwargs) -> Any:  # type:ignore
        if not kwargs.get("recurse", False):
            obj = super().decode(obj, *args, **kwargs)
        if isinstance(obj, str):
            try:
                # strictly use the format
                # obj = datetime.strptime(obj, "%Y-%m-%dT%H:%M:%S.%fZ")
                obj = datetime.fromisoformat(obj)
                # obj = obj.replace(tzinfo=UTC)
            except ValueError:
                pass
        return obj


def import_from_module(dot_separated_module_class: str) -> Type:
    """import 'path.to.my_package.MyClass' or `path.to.my_package.my_method` style class, method or object"""
    tmp = dot_separated_module_class.split(".")
    module_name = ".".join(tmp[:-1])
    cls_name = tmp[-1]
    module = importlib.import_module(module_name)
    cls = getattr(module, cls_name)
    return cls


def diff_dicts(d1: dict, d2: dict, keys: set = set(), excludes: set = set()) -> tuple[dict, dict]:
    keys = (keys or (set(d1.keys()) | set(d2.keys()))) - excludes
    d1_only = {k: d1[k] for k in d1 if k in keys and (k not in d2 or d1[k] != d2[k])}
    d2_only = {k: d2[k] for k in d2 if k in keys and (k not in d1 or d2[k] != d1[k])}
    return d1_only, d2_only


def deduplicate(data: Sequence[Any], key: str) -> list[Any]:
    existed_set = set()
    new_data = []
    for d in data:
        if isinstance(d, dict):
            value = d.get(key, None)
        else:
            value = getattr(d, key, None)
        if value is not None and value not in existed_set:
            existed_set.add(value)
            new_data.append(d)

    return new_data


def json_dumps(obj: Any, *args, **kwargs) -> str:

    class _Encoder(json.JSONEncoder):
        def default(self, o: Any) -> Any:
            if isinstance(o, DictMixin):
                return o.to_dict()
            if isinstance(o, datetime):
                return o.isoformat()
            else:
                return str(o)
            # Let the base class default method raise the TypeError
            # return json.JSONEncoder.default(self, o)

    if "cls" not in kwargs:
        kwargs["cls"] = _Encoder
    if "indent" not in kwargs:
        kwargs["indent"] = 2
    return json.dumps(obj, *args, **kwargs)


class DictMixin(ABC):
    _dict_keys: Iterable[str] = []  # fields will be output to dict
    _dict_excludes: Iterable[str] = []  # fields will not be output to dict
    _store_keys: Iterable[str] = []  # fields are stored in Storage (Object, CDN, ...).

    @property
    def dict_keys(self) -> Iterable[str]:
        return self._dict_keys

    @classmethod
    def from_dict(cls, d: dict) -> Self:
        """create instance form a dict"""
        obj = cls()
        obj.update_from_dict(d)
        return obj

    def to_dict(
        self,
        keys: Optional[Iterable[str]] = None,
        excludes: Optional[Iterable[str]] = None,
    ) -> dict:
        """
        :param keys: fields to be included in dict. None - ignore. empty([],(),{}) - override cls._dict_keys
        :param excludes: fields to be excluded from dict. None - ignore, empty([],(),{})  - override cls._dict_exclude
        :return:
        """

        def handle_value(value: Any) -> Any:
            """Converts datetime to string, calls to_dict if possible, or returns the value."""
            if hasattr(value, "to_dict"):
                return value.to_dict()
            else:
                return value

        d = self.__dict__
        keys = set(keys if keys is not None else self.dict_keys)
        excludes = set(excludes if excludes is not None else self._dict_excludes)
        assert not keys & excludes, "conflict in keys and excludes"
        keys = keys or {k for k in d.keys() if not k.startswith("_")}
        keys = {f for f in keys if f not in excludes}
        return {k: handle_value(d[k]) for k in keys}

    def update_from_dict(self, d: dict, keys: Optional[Iterable[str]] = None) -> None:
        keys = set(keys if keys is not None else self.dict_keys)
        keys = keys or {k for k in self.__dict__.keys() if not k.startswith("_")}
        for k, v in d.items():
            setattr(self, k, v) if keys and k in keys and hasattr(self, k) else None

    def update_from_object(self, obj: Any, keys: Iterable[str] = ()) -> None:
        keys = keys or self.dict_keys
        for k in keys:
            v = getattr(obj, k)
            setattr(self, k, v) if v and hasattr(self, k) else None

    def to_json(
        self,
        keys: Optional[Iterable[str]] = None,
        excludes: Optional[Iterable[str]] = None,
        indent: int = 2,
        sort: bool = False,
    ) -> str:
        return json.dumps(self.to_dict(keys, excludes), indent=indent, sort_keys=sort, cls=JsonEncoder)


def is_generic_inst(obj: Any, type_var: TypeVar) -> bool:
    for T in [type_var.__bound__, type_var.__constraints__]:
        if isinstance(obj, T):  # type: ignore
            return True
    return False


# class AutoRegisterMetaModelBase:
#     __auto_reg_mapping: dict[str, type] = dict()
#
#     def __new__(mcs, name, bases, clsdict):  # type: ignore
#         cls = super().__new__(mcs, name, bases, clsdict)
#         for base in bases:
#             if isinstance(base, AutoRegisterMetaModelBase) and base.__subclasses__():
#                 key_field = getattr(cls, cls.auto_reg_key_field_name)
#                 if key_field:
#                     key = getattr(cls, key_field)
#                     cls_registered = base.__auto_reg_mapping.get(key)
#                     if cls_registered is not cls:
#                         raise KeyError(f"{key} was auto registered to {cls_registered}")
#                     base.__auto_reg_mapping[key] = cls
#         return cls
#
#     def registered_classes(cls) -> dict:
#         return cls.__auto_reg_mapping
#
#     @property
#     @abstractmethod
#     def auto_reg_key_field_name(self) -> str:
#         """name of the field which should be used as the key for registered class mapping"""


def iter_subclasses(_cls: type) -> Iterator:
    """recursively iterate all sub-classes of give class"""
    for _sub in _cls.__subclasses__():
        yield _sub
        yield from iter_subclasses(_sub)


def get_caller_loc(caller_depth: int = 1) -> str:
    """get line of code of the caller by given depth of stack backward

    e.g.  A -> B -> C
    def C():
        loc = get_caller_loc(1)  # this is loc of B (backward 1 depth)
        loc = get_caller_loc(2)  # this is loc of A (backward 2 depths)
    """
    stack = traceback.extract_stack(limit=caller_depth + 2)[0]
    loc = f"{stack.filename}:{stack.lineno}"
    return loc


def get_exception_loc(e: Exception) -> str:
    """get line of code of the exception by given depth of stack backward"""

    summary = traceback.extract_tb(e.__traceback__)
    stack = summary[-1]
    loc = f"{stack.filename}:{stack.lineno}"
    return loc


def get_callstack_loc_list(depth: int = 3) -> list[str]:
    """get a list of line of code from call stack."""
    stacks = traceback.extract_stack()
    if depth > len(stacks):
        depth = len(stacks) - 1
    loc_list = [f"{stack.filename}:{stack.lineno}" for stack in stacks[:depth]]
    return loc_list


def obj_to_dict_dumps(obj: Any, datetime_to_str: bool = False) -> list | dict:
    """convert any kind of object into dict or list recursively for all fields"""
    from pycommon.utils.datetimeutils import format_datetime
    from pycommon.utils.datetimeutils import LOCAL_TZ

    _d = {}
    if hasattr(obj, "to_dict"):
        try:
            _d = obj.to_dict(datetime_to_str=datetime_to_str)
        except TypeError as e:
            if "unexpected keyword argument 'datetime_to_str'" in str(e):
                _d = obj.to_dict()
            else:
                raise
    elif hasattr(obj, "model_dump"):
        _d = obj.model_dump()
    elif isinstance(obj, list):
        _d = [obj_to_dict_dumps(o, datetime_to_str=datetime_to_str) for o in obj]  # type: ignore
    elif isinstance(obj, dict):
        _d = {_k: obj_to_dict_dumps(_v, datetime_to_str=datetime_to_str) for _k, _v in obj.items()}
    elif isinstance(obj, (datetime, time, date)) and datetime_to_str:
        _d = format_datetime(obj, tz=LOCAL_TZ, for_persist=True)
    else:
        _d = obj
    return _d
