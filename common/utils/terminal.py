#!/usr/bin/env python
# coding=utf-8

"""
Some common useful functions for all parts.
"""

import platform
from datetime import datetime
from os import linesep, system
from sys import stdout, stderr, platform as sys_platform
from .confutils import get_bool_env

DEBUG = get_bool_env("DEBUG", False)

# ----- terminal output related -----

_win_vterm_mode = None
_is_win = sys_platform.startswith("win")
if _is_win:

    def enable_windows_terminal_mode():
        """Enable virtual terminal processing in windows terminal. Does
        nothing if not on Windows. This is based on the rejected
        enhancement <https://bugs.python.org/issue29059>."""
        global _win_vterm_mode
        if _win_vterm_mode != None:
            return _win_vterm_mode

        # Note: Cygwin should return something like "CYGWIN_NT..."
        _win_vterm_mode = platform.system().lower() == "windows"
        if _win_vterm_mode == False:
            return

        from ctypes import windll, c_int, byref, c_void_p

        ENABLE_VIRTUAL_TERMINAL_PROCESSING = 0x0004
        INVALID_HANDLE_VALUE = c_void_p(-1).value
        STD_OUTPUT_HANDLE = c_int(-11)

        hStdout = windll.kernel32.GetStdHandle(STD_OUTPUT_HANDLE)
        if hStdout == INVALID_HANDLE_VALUE:
            _win_vterm_mode = False
            return

        mode = c_int(0)
        ok = windll.kernel32.GetConsoleMode(c_int(hStdout), byref(mode))
        if not ok:
            _win_vterm_mode = False
            return

        mode = c_int(mode.value | ENABLE_VIRTUAL_TERMINAL_PROCESSING)
        ok = windll.kernel32.SetConsoleMode(c_int(hStdout), mode)
        if not ok:
            # Something went wrong, proably an too old version
            # that doesn't support the VT100 mode.
            # To be more certain we could check kernel32.GetLastError
            # for STATUS_INVALID_PARAMETER, but since we only enable
            # one flag we can be certain enough.
            _win_vterm_mode = False
            return

    enable_windows_terminal_mode()


# refer to
# posix - https://chrisyeh96.github.io/2020/03/28/terminal-colors.html
# windows - https://learn.microsoft.com/en-us/windows/console/console-virtual-terminal-sequences

if _win_vterm_mode or not _is_win:
    # define posix colors
    pass
else:
    # define window colors
    pass


class _FgColor(object):
    default = 0

    black = 30
    red = 31
    green = 32
    yellow = 33
    blue = 34
    magenta = 35
    cyan = 36
    white = 37

    # bright foreground colors. none standard
    gray = 90  # bright black
    br_black = 90
    br_red = 91
    br_green = 92
    br_yellow = 93
    br_blue = 94
    br_magenta = 95
    br_cyan = 96
    br_white = 97


class _BgColor(object):
    default = 0

    black = 40
    red = 41
    green = 42
    yellow = 43
    blue = 44
    magenta = 45
    cyan = 46
    white = 47

    # bright background colors. none standard
    gray = 100  # bright black
    br_black = 100
    br_red = 101
    br_green = 102
    br_yellow = 103
    br_blue = 104
    br_magenta = 105
    br_cyan = 106
    br_white = 107


class Effect(object):
    default = 0
    bold = 1
    faint = 2
    italic = 3
    underline = 4
    blink = 5
    xor = 7
    invisible = 8


def pcrlf(count=1, to_stderr=False):
    """print a CR|LF new line"""
    out = stdout if not to_stderr else stderr
    for i in range(count):
        out.write(linesep)
    out.flush()


def clear_screen():
    if _win_vterm_mode or not _is_win:
        print(chr(27) + "[2J")
    else:
        system("cls")


def output(
    msg,
    fg,
    bg=_BgColor.default,
    effect=Effect.default,
    newline=True,
    flush=False,
    log_lvl=None,
    log_dt=False,
    to_stderr=False,
):
    if log_dt:
        msg = f"{datetime.now(): %Y-%m-%d %H:%M:%S.%f%z} {msg}"
    if log_lvl is not None:
        msg = f"{log_lvl} {msg}"

    s = f"\033[{effect};{bg};{fg}m{msg}\033[0m" if _win_vterm_mode or not _is_win else msg

    out = stdout if not to_stderr else stderr
    out.write(s)
    if flush:
        out.flush()
    if newline is True:
        pcrlf(to_stderr=to_stderr)


def debug(s, bg=_BgColor.default, effect=Effect.default, newline=True, flush=False):
    if DEBUG is True:
        output(s, _FgColor.gray, bg=bg, effect=effect, newline=newline, flush=flush)


def info(s, bg=_BgColor.default, effect=Effect.default, newline=True, flush=False):
    output(s, _FgColor.default, bg=bg, effect=effect, newline=newline, flush=flush)


def tip(s, bg=_BgColor.default, effect=Effect.default, newline=True, flush=False):
    output(s, _FgColor.cyan, bg=bg, effect=effect, newline=newline, flush=flush)


def warn(s, bg=_BgColor.default, effect=Effect.default, newline=True, flush=False):
    output(s, _FgColor.yellow, bg=bg, effect=effect, newline=newline, flush=flush)


def error(s, bg=_BgColor.default, effect=Effect.default, newline=True, flush=False):
    output(s, _FgColor.red, bg=bg, effect=effect, newline=newline, flush=flush)


def success(s, bg=_BgColor.default, effect=Effect.default, newline=True, flush=False):
    output(s, _FgColor.green, bg=bg, effect=effect, newline=newline, flush=flush)
