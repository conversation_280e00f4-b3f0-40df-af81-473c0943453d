"""
Asset Optimization Services

Core services for asset data retrieval and portfolio optimization
"""

import os
import requests
import pandas as pd
import numpy as np
from scipy.optimize import minimize
from datetime import datetime, timedelta
import logging

# Configure logging
logger = logging.getLogger(__name__)

# FMP API configuration
FMP_API_KEY = "b84f473ad7e06c8905867ba6d0886f4e"
FMP_BASE_URL = 'https://financialmodelingprep.com/api/v3'


def generate_mock_data(symbol, days=365, base_price=100):
    """Generate mock financial data for demonstration"""
    dates = pd.date_range(end=datetime.now(), periods=days, freq='D')

    # Generate realistic price movements
    np.random.seed(hash(symbol) % 2**32)  # Consistent data for same symbol
    returns = np.random.normal(0.001, 0.02, days)  # Daily returns

    # Add some correlation patterns
    if symbol in ['EURUSD', 'forex']:
        returns += np.sin(np.arange(days) * 0.1) * 0.005  # Cyclical pattern
    elif symbol in ['GCUSX', 'commodities']:
        returns += np.random.normal(0.002, 0.015, days)  # Slightly higher volatility
    elif symbol in ['^GSPC', 'equities']:
        returns += np.random.normal(0.0005, 0.018, days)  # Market trend
    elif symbol in ['TREASURY_10Y', 'bonds']:
        returns = np.random.normal(0.0001, 0.005, days)  # Lower volatility

    # Calculate cumulative prices
    prices = base_price * np.exp(np.cumsum(returns))

    df = pd.DataFrame({
        'date': dates,
        symbol: prices
    })

    return df


class AssetDataService:
    def __init__(self, api_key=None):
        self.api_key = api_key or FMP_API_KEY
        self.base_url = FMP_BASE_URL
        self.use_mock_data = api_key == 'demo'  # Use mock data for demo key

    def _make_request(self, endpoint, params=None):
        """Make API request to FMP"""
        if params is None:
            params = {}
        params['apikey'] = self.api_key

        url = f"{self.base_url}/{endpoint}"
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return None

    def get_historical_data(self, symbol, days=365):
        """Get historical price data for a symbol"""
        endpoint = f"historical-price-full/{symbol}"
        params = {
            'from': (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d'),
            'to': datetime.now().strftime('%Y-%m-%d')
        }

        data = self._make_request(endpoint, params)
        if data and 'historical' in data:
            df = pd.DataFrame(data['historical'])
            df['date'] = pd.to_datetime(df['date'], format='mixed')
            df = df.sort_values('date')
            return df[['date', 'close']].rename(columns={'close': symbol})
        return None

    def get_forex_data(self, pair='EURUSD', days=365):
        """Get forex historical data"""
        return self.get_historical_data(pair, days)

    def get_commodity_data(self, symbol='GC=F', days=365):
        """Get commodity historical data (Gold)"""
        return self.get_historical_data(symbol, days)

    def get_equity_data(self, symbol='^GSPC', days=365):
        """Get equity index data (S&P 500)"""
        return self.get_historical_data(symbol, days)

    def get_treasury_data(self, days=365, bond_type='10Y'):
        """Get treasury rates data"""
        # Treasury API uses v4
        url = f"https://financialmodelingprep.com/api/v4/treasury"
        params = {
            'from': (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d'),
            'to': datetime.now().strftime('%Y-%m-%d'),
            'apikey': self.api_key
        }

        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()

            if data:
                df = pd.DataFrame(data)
                if not df.empty and 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'], format='mixed')
                    df = df.sort_values('date')

                    # Map bond type to column name
                    bond_column_map = {
                        '2Y': 'year2',
                        '10Y': 'year10',
                        '30Y': 'year30'
                    }

                    column_name = bond_column_map.get(bond_type, 'year10')
                    if column_name in df.columns:
                        return df[['date', column_name]].rename(columns={column_name: f'TREASURY_{bond_type}'})
        except requests.exceptions.RequestException as e:
            logger.error(f"Treasury API request failed: {e}")

        return None

    def get_us_companies(self):
        """Get US companies list with 1-day file cache"""
        import os
        import json
        import tempfile
        from datetime import datetime, timedelta

        try:
            # 缓存文件路径
            cache_dir = tempfile.gettempdir()
            cache_file = os.path.join(cache_dir, "us_companies_cache.json")
            cache_duration = timedelta(days=1)

            # 检查缓存文件是否存在且未过期
            if os.path.exists(cache_file):
                file_mtime = datetime.fromtimestamp(os.path.getmtime(cache_file))
                if datetime.now() - file_mtime < cache_duration:
                    logger.info(f"Loading US companies from cache: {cache_file}")
                    try:
                        with open(cache_file, 'r', encoding='utf-8') as f:
                            cached_data = json.load(f)
                            return cached_data
                    except Exception as cache_error:
                        logger.warning(f"Failed to load cache, will fetch from API: {cache_error}")

            # 缓存不存在或已过期，调用 API 获取数据
            logger.info("Fetching US companies from FMP API...")
            url = f"https://financialmodelingprep.com/api/v3/stock/list?apikey={self.api_key}"

            response = requests.get(url, timeout=30)
            response.raise_for_status()

            all_stocks = response.json()

            # Filter US stocks
            us_exchanges = ['NASDAQ', 'NYSE', 'AMEX', 'NYSE ARCA', 'BATS']
            us_stocks = []

            # Famous companies whitelist
            famous_companies = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX', 'JPM', 'V', 'JNJ', 'WMT', 'PG', 'UNH', 'HD', 'BAC', 'KO', 'PEP', 'ABBV', 'PFE']

            for stock in all_stocks:
                symbol = stock.get('symbol', '')
                name = stock.get('name', '')
                stock_type = stock.get('type', '')

                # Include famous companies directly
                if symbol in famous_companies:
                    us_stocks.append({
                        'symbol': symbol,
                        'name': name,
                        'exchange': stock.get('exchange', ''),
                        'type': 'stock'
                    })
                    continue

                # Filter other companies
                if (stock.get('exchange') in us_exchanges and
                    symbol and name and
                    stock_type == 'stock' and
                    len(symbol) <= 5 and
                    '.' not in symbol and
                    not symbol.endswith('W') and
                    not symbol.endswith('U') and
                    not symbol.endswith('R') and
                    not symbol.endswith('X') and
                    'Fund' not in name and
                    'ETF' not in name and
                    'Trust' not in name):

                    us_stocks.append({
                        'symbol': symbol,
                        'name': name,
                        'exchange': stock.get('exchange', ''),
                        'type': stock_type
                    })

            # Sort by symbol
            us_stocks.sort(key=lambda x: x['symbol'])

            # 保存到缓存文件
            try:
                with open(cache_file, 'w', encoding='utf-8') as f:
                    json.dump(us_stocks, f, ensure_ascii=False, indent=2)
                logger.info(f"US companies data cached to {cache_file} ({len(us_stocks)} companies)")
            except Exception as cache_error:
                logger.warning(f"Failed to save cache: {cache_error}")

            return us_stocks

        except Exception as e:
            logger.error(f"Error fetching US companies: {e}")
            return []


class PortfolioOptimizer:
    def __init__(self, asset_service):
        self.asset_service = asset_service

    def get_portfolio_data(self, portfolio, days=365):
        """Get historical data for all assets in portfolio"""
        data = {}

        for asset in portfolio:
            symbol = asset['symbol']
            category = asset['category']

            # Get data based on asset category
            if category == 'forex':
                asset_data = self.asset_service.get_forex_data(symbol, days)
            elif category == 'commodities':
                asset_data = self.asset_service.get_commodity_data(symbol, days)
            elif category == 'equities':
                asset_data = self.asset_service.get_equity_data(symbol, days)
            elif category == 'bonds':
                asset_data = self.asset_service.get_treasury_data(days, symbol)
            else:
                # Default to equity data
                asset_data = self.asset_service.get_historical_data(symbol, days)

            if asset_data is not None and not asset_data.empty:
                data[symbol] = asset_data
            else:
                logger.warning(f"No data found for {symbol}")

        return data

    def calculate_returns(self, price_data):
        """Calculate daily returns from price data"""
        returns = {}

        for symbol, data in price_data.items():
            if len(data) > 1:
                # Get price column (should be the symbol name or close price)
                price_col = [col for col in data.columns if col != 'date'][0]
                prices = data[price_col].values
                daily_returns = np.diff(prices) / prices[:-1]
                returns[symbol] = daily_returns
            else:
                returns[symbol] = np.array([])

        return returns

    def align_returns(self, returns_dict):
        """Align returns data to common dates"""
        # Find minimum length
        min_length = min(len(returns) for returns in returns_dict.values() if len(returns) > 0)

        if min_length == 0:
            return np.array([]), []

        # Align all returns to same length
        aligned_returns = []
        symbols = []

        for symbol, returns in returns_dict.items():
            if len(returns) >= min_length:
                aligned_returns.append(returns[-min_length:])
                symbols.append(symbol)

        return np.array(aligned_returns).T, symbols

    def calculate_portfolio_metrics(self, returns, weights):
        """Calculate portfolio return, volatility, and Sharpe ratio"""
        if len(returns) == 0 or len(weights) == 0:
            return 0, 0, 0

        # Portfolio return (annualized)
        portfolio_return = np.sum(returns.mean(axis=0) * weights) * 252

        # Portfolio volatility (annualized)
        cov_matrix = np.cov(returns.T)
        portfolio_variance = np.dot(weights.T, np.dot(cov_matrix, weights))
        portfolio_volatility = np.sqrt(portfolio_variance) * np.sqrt(252)

        # Sharpe ratio (assuming risk-free rate of 2%)
        risk_free_rate = 0.02
        sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_volatility if portfolio_volatility > 0 else 0

        return portfolio_return, portfolio_volatility, sharpe_ratio

    def calculate_max_drawdown(self, returns, weights):
        """Calculate maximum drawdown"""
        if len(returns) == 0 or len(weights) == 0:
            return 0

        # Calculate portfolio daily returns
        portfolio_returns = np.dot(returns, weights)

        # Calculate cumulative returns
        cumulative_returns = np.cumprod(1 + portfolio_returns)

        # Calculate running maximum
        running_max = np.maximum.accumulate(cumulative_returns)

        # Calculate drawdown
        drawdown = (cumulative_returns - running_max) / running_max

        return abs(np.min(drawdown))

    def optimize_portfolio(self, returns, symbols, target_return=None, target_volatility=None):
        """Optimize portfolio using Modern Portfolio Theory"""
        if len(returns) == 0 or len(symbols) == 0:
            return None

        n_assets = len(symbols)

        # Calculate expected returns and covariance matrix
        expected_returns = returns.mean(axis=0) * 252  # Annualized
        cov_matrix = np.cov(returns.T) * 252  # Annualized

        # Objective functions
        def portfolio_volatility(weights):
            return np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))

        def portfolio_return(weights):
            return np.sum(expected_returns * weights)

        def negative_sharpe(weights):
            ret = portfolio_return(weights)
            vol = portfolio_volatility(weights)
            return -(ret - 0.02) / vol if vol > 0 else -999

        # Constraints
        constraints = [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1}]  # Weights sum to 1

        if target_return is not None:
            constraints.append({'type': 'eq', 'fun': lambda x: portfolio_return(x) - target_return})

        if target_volatility is not None:
            constraints.append({'type': 'eq', 'fun': lambda x: portfolio_volatility(x) - target_volatility})

        # Bounds (0 <= weight <= 1)
        bounds = tuple((0, 1) for _ in range(n_assets))

        # Initial guess (equal weights)
        x0 = np.array([1/n_assets] * n_assets)

        # Optimize
        if target_return is None and target_volatility is None:
            # Maximize Sharpe ratio
            result = minimize(negative_sharpe, x0, method='SLSQP', bounds=bounds, constraints=constraints)
        elif target_return is not None:
            # Minimize volatility for target return
            result = minimize(portfolio_volatility, x0, method='SLSQP', bounds=bounds, constraints=constraints)
        else:
            # Maximize return for target volatility
            result = minimize(lambda x: -portfolio_return(x), x0, method='SLSQP', bounds=bounds, constraints=constraints)

        if result.success:
            weights = result.x
            ret = portfolio_return(weights)
            vol = portfolio_volatility(weights)
            sharpe = (ret - 0.02) / vol if vol > 0 else 0

            return {
                'weights': weights.tolist(),
                'expected_return': ret,
                'volatility': vol,
                'sharpe_ratio': sharpe,
                'symbols': symbols
            }

        return None

    def generate_efficient_frontier(self, returns, symbols, num_points=50):
        """Generate efficient frontier"""
        if len(returns) == 0 or len(symbols) == 0:
            return []

        expected_returns = returns.mean(axis=0) * 252
        min_ret = np.min(expected_returns)
        max_ret = np.max(expected_returns)

        target_returns = np.linspace(min_ret, max_ret, num_points)
        efficient_portfolios = []

        for target_ret in target_returns:
            portfolio = self.optimize_portfolio(returns, symbols, target_return=target_ret)
            if portfolio:
                efficient_portfolios.append(portfolio)

        return efficient_portfolios
