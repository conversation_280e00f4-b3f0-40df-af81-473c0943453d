#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FastAPI Application Entry Point

Provides API interfaces for generating and returning analysis reports
"""

import os
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
import uvicorn

# Import router modules
from api.routers import strategy_router

# Create FastAPI application
app = FastAPI(
    title="Gold Beast System API",
    description="API for portfolio analysis and backtesting",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins, should be restricted in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(strategy_router.router, prefix="/api", tags=["Strategy Management"])

# Create static files directory (for storing temporary images)
current_dir = os.path.dirname(os.path.abspath(__file__))
static_dir = os.path.join(current_dir, "static")
os.makedirs(static_dir, exist_ok=True)
app.mount("/static", StaticFiles(directory=static_dir), name="static")

@app.get("/", response_class=HTMLResponse)
async def root():
    """API root path, returns a simple HTML page"""
    return """
    <html>
        <head>
            <title>Gold Beast System API</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
                h1 { color: #333; }
                a { color: #0066cc; text-decoration: none; }
                a:hover { text-decoration: underline; }
                .container { max-width: 800px; margin: 0 auto; }
                .endpoint { margin-bottom: 10px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Gold Beast System API</h1>
                <p>Welcome to the Gold Beast System API. Here are the available endpoints:</p>

                <h2>Strategy Management</h2>
                <div class="endpoint">
                    <a href="/api/strategies">/api/strategies</a> - List all strategies
                </div>
                <div class="endpoint">
                    <a href="/api/strategies/{strategy_id}">/api/strategies/{strategy_id}</a> - Get strategy details
                </div>
                <div class="endpoint">
                    <a href="/api/run-latest-strategy">/api/run-latest-strategy</a> - Run the latest strategy and return results
                </div>

                <h2>API Documentation</h2>
                <div class="endpoint">
                    <a href="/docs">/docs</a> - Swagger UI API documentation
                </div>
                <div class="endpoint">
                    <a href="/redoc">/redoc</a> - ReDoc API documentation
                </div>
            </div>
        </body>
    </html>
    """

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
