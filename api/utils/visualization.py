"""
Visualization Utility Functions

Provides utility functions for chart generation and conversion
"""

import io
import matplotlib.pyplot as plt
import matplotlib


def fig_to_svg(fig):
    """
    Convert Matplotlib figure to optimized SVG text

    Args:
        fig: Matplotlib figure object

    Returns:
        String in SVG format
    """
    # Temporarily set SVG parameters
    with plt.rc_context({
        'svg.fonttype': 'path',  # Convert text to paths for consistent display
        'svg.image_inline': True,  # Inline images
        'figure.figsize': (8, 6),  # Control figure size
        'figure.facecolor': 'white',  # White background
        'savefig.transparent': False,  # Non-transparent background
        'savefig.bbox': 'tight',  # Tight layout
        'savefig.pad_inches': 0.1,  # Small padding
        'font.size': 12,
        # Font fallback order
        'font.sans-serif': ['PingFang-SC', 'Microsoft YaHei', 'SimHei']
    }):
        buf = io.BytesIO()
        fig.savefig(buf, format='svg', bbox_inches='tight')
        buf.seek(0)
        svg_str = buf.read().decode('utf-8')
        buf.close()

        return svg_str
