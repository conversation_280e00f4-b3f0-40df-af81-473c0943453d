#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Python Code Execution Utilities

Provides utilities for executing Python code from strings
"""

import os
import sys
import importlib.util
import logging
from typing import Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def execute_python_workflow(file_path: str, runtime_config: dict) -> Any:
    """
    Execute Python workflow code from a file path and return the result.

    This function takes a file path to a Python file and executes its main() function.
    The module name is derived from the file name (without extension).

    Args:
        file_path: Path to the Python file to execute

    Returns:
        The result of the main() function in the code

    Raises:
        FileNotFoundError: If the file does not exist
        ImportError: If the module cannot be imported
        AttributeError: If the module does not have a main() function
        Exception: Any other exception that occurs during execution
    """
    # Check if the file exists
    if not os.path.isfile(file_path):
        raise FileNotFoundError(f"Python file not found: {file_path}")

    # Derive module name from the file name (without extension)
    module_name = os.path.splitext(os.path.basename(file_path))[0]

    logger.info(f"Using Python file at {file_path}")

    # Ensure outputs/mlruns directory exists
    project_root = os.path.abspath(
        os.path.join(os.path.dirname(__file__), "../.."))
    mlruns_dir = os.path.join(project_root, "outputs/mlruns")
    os.makedirs(mlruns_dir, exist_ok=True)
    logger.info(f"Ensured mlruns directory exists at {mlruns_dir}")

    # Save current working directory
    original_cwd = os.getcwd()

    try:
        # Change working directory to project root
        os.chdir(project_root)
        logger.info(f"Changed working directory to {project_root}")

        # Add the directory containing the Python file to sys.path
        file_dir = os.path.dirname(file_path)
        if file_dir not in sys.path:
            sys.path.insert(0, file_dir)

        # Import the module
        spec = importlib.util.spec_from_file_location(
            module_name, file_path)
        if spec is None:
            raise ImportError(f"Could not create spec for {file_path}")

        module = importlib.util.module_from_spec(spec)
        sys.modules[module_name] = module
        spec.loader.exec_module(module)

        # Check if the module has a main() function
        if not hasattr(module, 'main'):
            raise AttributeError(
                f"Module {module_name} does not have a main() function")

        # Execute the main() function
        logger.info(f"Executing main() function from {module_name}")
        result = module.main(runtime_config)

        return result

    except Exception as e:
        logger.error(f"Error executing Python workflow: {str(e)}")
        raise
    finally:
        # Restore original working directory
        os.chdir(original_cwd)
        logger.info(f"Restored working directory to {original_cwd}")

        # Clean up
        if file_dir in sys.path:
            sys.path.remove(file_dir)

        # Note: We're not removing the temporary file to allow for debugging
        logger.info(
            f"Python workflow execution completed. File path: {file_path}")
