#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from sqlalchemy import create_engine, text, types
import time
import os
import sys
import glob
import re
from datetime import datetime
from tqdm import tqdm

# Database connection parameters
DB_CONFIG = {
    'user': 'postgres',
    'password': 'onWISH#2024!DB',
    'host': 'prod-onwish-app-cluster.cluster-cjuysus6851i.us-east-1.rds.amazonaws.com',
    'database': 'algotrading',
    'port': '5432'
}

# Path to the factors folder
FACTORS_DIR = 'factors'

def get_db_engine():
    """
    创建并返回数据库连接引擎
    """
    try:
        print(f"正在连接到PostgreSQL数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}...")
        connection_string = f"postgresql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"
        engine = create_engine(connection_string)

        # 测试连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("数据库连接成功")

        return engine
    except Exception as e:
        print(f"连接数据库时出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def drop_axioma_exp_table(engine):
    """
    删除axioma_exp表（如果存在）
    """
    print("删除axioma_exp表（如果存在）...")

    try:
        with engine.connect() as conn:
            conn.execute(text('DROP TABLE IF EXISTS axioma_exp'))
            conn.commit()
            print("axioma_exp表已删除或不存在")
            return True

    except Exception as e:
        print(f"删除axioma_exp表时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_axioma_exp_table(engine):
    """
    创建axioma_exp表并创建索引
    """
    print("创建axioma_exp表...")

    try:
        # 查找第一个文件来获取列名
        axioma_files = find_axioma_files()
        if not axioma_files:
            print("未找到符合条件的文件，无法创建表")
            return False

        first_file = axioma_files[0]
        columns, _ = read_axioma_file(first_file)
        if not columns:
            print(f"无法从文件 {first_file} 中提取列名，无法创建表")
            return False

        with engine.connect() as conn:
            # 构建CREATE TABLE语句
            create_table_sql = "CREATE TABLE axioma_exp (\n"
            create_table_sql += "    \"Date\" DATE NOT NULL,\n"

            # 添加列定义
            for i, col in enumerate(columns):
                if col == 'AxiomaID':
                    create_table_sql += f"    \"{col}\" TEXT"
                else:
                    create_table_sql += f"    \"{col}\" FLOAT"

                if i < len(columns) - 1:
                    create_table_sql += ",\n"
                else:
                    create_table_sql += "\n"

            create_table_sql += ")"

            # 执行创建表的SQL
            conn.execute(text(create_table_sql))
            conn.commit()
            print("axioma_exp表已创建")

            # 为Date列创建索引
            print("为axioma_exp表创建索引...")
            conn.execute(text('CREATE INDEX idx_axioma_exp_date ON axioma_exp ("Date")'))
            conn.commit()
            print("索引已创建")

            return True

    except Exception as e:
        print(f"创建axioma_exp表时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def find_axioma_files():
    """
    查找factors目录下的所有AXUS4-MH.*.exp文件，不包括AXUS4-MH-S.*.exp文件
    """
    print(f"查找{FACTORS_DIR}目录下的AXUS4-MH.*.exp文件...")

    # 使用glob递归查找所有AXUS4-MH.*.exp文件
    all_files = glob.glob(f"{FACTORS_DIR}/**/AXUS4-MH.*.exp", recursive=True)

    # 排除AXUS4-MH-S.*.exp文件
    axioma_files = [f for f in all_files if "AXUS4-MH-S" not in f]

    print(f"找到 {len(axioma_files)} 个AXUS4-MH.*.exp文件")
    return axioma_files

def extract_date_from_filename(filepath):
    """
    从文件名中提取日期，并转换为YYYY-MM-DD格式
    """
    filename = os.path.basename(filepath)
    match = re.search(r'AXUS4-MH\.(\d{8})\.exp', filename)
    if match:
        date_str = match.group(1)
        # 转换YYYYMMDD为YYYY-MM-DD
        formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
        return formatted_date
    return None

def read_axioma_file(filepath):
    """
    读取单个AXUS4-MH.*.exp文件，提取列名和数据
    """
    # 从文件名中提取日期
    date_str = extract_date_from_filename(filepath)
    if not date_str:
        print(f"无法从文件名 {filepath} 中提取日期，跳过")
        return None, None

    try:
        # 读取文件
        with open(filepath, 'r') as file:
            lines = file.readlines()

            # 提取列名（第6行，索引5）
            if len(lines) >= 6:
                header_line = lines[5].strip()
                # 移除"#Columns: "前缀
                header_line = header_line.replace("#Columns: ", "")
                # 按"|" 分割得到列名
                columns = header_line.split('|')

                # 数据从第8行（索引7）开始
                data_rows = []
                for line in lines[7:]:
                    if line.strip() and not line.startswith('#'):
                        values = line.strip().split('|')
                        data_rows.append(values)

                # 创建DataFrame
                if data_rows:
                    df = pd.DataFrame(data_rows, columns=columns)
                    # 添加Date列作为第一列
                    df.insert(0, 'Date', date_str)

                    # 将空字符串转换为None（NULL）
                    df = df.replace('', None)

                    return columns, df
    except Exception as e:
        print(f"读取文件 {filepath} 时出错: {e}")

    return None, None

def get_column_types(columns):
    """
    根据列名确定数据库列类型
    """
    sql_dtypes = {}
    for col in columns:
        if col == 'Date':
            sql_dtypes[col] = types.Date()
        elif col == 'AxiomaID':
            sql_dtypes[col] = types.Text()
        else:
            sql_dtypes[col] = types.Float()
    return sql_dtypes

def import_axioma_exp_data(engine):
    """
    从文件夹中读取AXUS4-MH.*.exp文件，并导入到axioma_exp表
    """
    print("开始从文件夹中读取并导入数据...")

    try:
        # 查找所有符合条件的文件
        axioma_files = find_axioma_files()
        if not axioma_files:
            print("未找到符合条件的文件，导入终止")
            return False

        # 读取第一个文件的列名，用于创建表结构
        first_file = axioma_files[0]
        columns, _ = read_axioma_file(first_file)
        if not columns:
            print(f"无法从文件 {first_file} 中提取列名，导入终止")
            return False

        # 定义SQL列类型
        sql_dtypes = get_column_types(['Date'] + columns)

        # 处理每个文件
        total_rows = 0
        for filepath in tqdm(axioma_files, desc="处理文件"):
            _, df = read_axioma_file(filepath)
            if df is not None and not df.empty:
                # 确保日期列是日期类型
                df['Date'] = pd.to_datetime(df['Date'])

                # 将数据写入数据库
                df.to_sql(
                    name='axioma_exp',
                    con=engine,
                    if_exists='append',
                    index=False,
                    dtype=sql_dtypes
                )

                total_rows += len(df)
                print(f"已处理文件 {filepath}")
                print(f"已导入 {total_rows} 行数据")

                # 每导入500,000行后检查一下表中的行数
                if total_rows % 500000 == 0:
                    with engine.connect() as conn:
                        result = conn.execute(text('SELECT COUNT(*) FROM axioma_exp'))
                        db_count = result.scalar()
                        print(f"数据库中实际行数: {db_count}")

        # 导入完成后再次检查表中的行数
        with engine.connect() as conn:
            result = conn.execute(text('SELECT COUNT(*) FROM axioma_exp'))
            final_count = result.scalar()
            print(f"导入完成！数据库中实际行数: {final_count}")

        return True

    except Exception as e:
        print(f"导入axioma_exp数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主函数
    """
    # 获取数据库引擎
    engine = get_db_engine()

    # 删除现有的axioma_exp表（如果存在）
    if not drop_axioma_exp_table(engine):
        print("删除axioma_exp表失败")
        return

    # 创建axioma_exp表和索引
    if not create_axioma_exp_table(engine):
        print("创建axioma_exp表失败")
        return

    # 从文件夹中读取并导入数据
    if not import_axioma_exp_data(engine):
        print("导入axioma_exp数据失败")
        return

    print("数据导入完成！")

if __name__ == "__main__":
    main()