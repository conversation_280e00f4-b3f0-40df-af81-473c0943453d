#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from sqlalchemy import create_engine, text, types
import sys

# Database connection parameters
DB_CONFIG = {
    'user': 'postgres',
    'password': 'onWISH#2024!DB',
    'host': 'prod-onwish-app-cluster.cluster-cjuysus6851i.us-east-1.rds.amazonaws.com',
    'database': 'algotrading',
    'port': '5432'
}

def get_db_engine():
    """
    创建并返回数据库连接引擎
    """
    try:
        print(f"正在连接到PostgreSQL数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}...")
        connection_string = f"postgresql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"
        engine = create_engine(connection_string)
        
        # 测试连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("数据库连接成功")
        
        return engine
    except Exception as e:
        print(f"连接数据库时出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def check_table_info(engine, table_name):
    """
    检查表的信息，包括行数、列信息和示例数据
    """
    try:
        with engine.connect() as conn:
            # 检查表是否存在
            result = conn.execute(text(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = '{table_name}'
                )
            """))
            table_exists = result.scalar()
            
            if not table_exists:
                print(f"表 '{table_name}' 不存在")
                return
            
            # 获取表中的行数
            result = conn.execute(text(f'SELECT COUNT(*) FROM {table_name}'))
            row_count = result.scalar()
            print(f"表 '{table_name}' 中有 {row_count} 行数据")
            
            # 获取表的列信息
            result = conn.execute(text(f"""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = '{table_name}'
                ORDER BY ordinal_position
            """))
            columns = result.fetchall()
            print(f"表 '{table_name}' 有 {len(columns)} 列:")
            for col in columns:
                print(f"  {col[0]} ({col[1]})")
            
            # 获取前5行数据
            result = conn.execute(text(f'SELECT * FROM {table_name} LIMIT 5'))
            rows = result.fetchall()
            print("\n前5行数据:")
            for row in rows:
                print(row)
            
    except Exception as e:
        print(f"检查表信息时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    # 获取数据库引擎
    engine = get_db_engine()
    
    # 检查axioma_exp表的信息
    check_table_info(engine, 'axioma_exp')

if __name__ == "__main__":
    main()
