#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Strategy Management API Router

Provides API endpoints for strategy listing, details, and related backtests
"""

import os
import json
import shutil
import tempfile
import hashlib
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Query, Path, Body
from pydantic import BaseModel
import mlflow
from mlflow.tracking import M<PERSON>lowClient
from api.utils.strategy_utils import load_strategies
from api.utils.python_executor import execute_python_workflow
from gbs.workflow.cli import workflow
from uuid import uuid4
from gbs.core.conf.mlflow_config import MLFLOW_TRACKING_URI

# datetime is not used in this file after refactoring

routes = APIRouter()

def get_mlflow_client():
    mlflow.set_tracking_uri(MLFLOW_TRACKING_URI)
    return MlflowClient()

# Strategy data model


class Strategy(BaseModel):
    strategy_id: str
    strategy_name: str = ""
    description: str = ""
    file_type: str = "yaml"


class WorkflowRequest(BaseModel):
    workflow_id: str = ''
    workflow_yaml_content: Optional[str] = None


@routes.get("/strategies", response_model=List[Strategy])
async def list_strategies(
    limit: int = Query(100, description="Maximum number of results"),
    offset: int = Query(0, description="Result offset")
):
    """
    Get strategy list

    Returns a list of all available strategies in the system
    """
    # Load strategies
    strategies = load_strategies()

    # Convert to list
    strategy_list = list(strategies.values())

    # Apply pagination
    paginated_list = strategy_list[offset:offset+limit]

    # Remove backtests field (only shown in details)
    for strategy in paginated_list:
        if "backtests" in strategy:
            del strategy["backtests"]

    return paginated_list


@routes.get("/strategies/{strategy_id}")
async def get_strategy(strategy_id: str):
    """
    Get strategy details

    Returns the original file content (YAML or Python) for the specified strategy ID
    """
    # Load strategies
    strategies = load_strategies()

    # Check if strategy exists
    if strategy_id not in strategies:
        raise HTTPException(
            status_code=404, detail=f"Strategy not found: {strategy_id}")

    # Get the config file path and type
    config_file = strategies[strategy_id].get("config_file")
    file_type = strategies[strategy_id].get("file_type", "yaml")

    # Read and return the original file content
    with open(config_file, 'r') as f:
        file_content = f.read()

    # Return standardized response with file_content
    return {
        "strategy_id": strategy_id,
        "file_type": file_type,
        "file_content": file_content
    }


class WorkflowContent(BaseModel):
    workflow_content: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    initial_capital: Optional[float] = None
    freq: Optional[str] = None
    strategy_class: Optional[str] = None


@routes.post("/strategies/{workflow_id}")
async def post_workflow_strategy(
    workflow_id: str = Path(..., description="Workflow ID"),
    request: WorkflowContent = Body(
        default=None, description="Optional workflow content")
):
    """
    Execute a workflow strategy

    This endpoint executes a workflow strategy and returns the results.
    It supports both YAML and Python workflow files.

    For YAML workflows:
    - The workflow is executed using the gbs.workflow.cli.workflow function

    For Python workflows:
    - The Python code is executed directly using a custom executor
    - The Python code must have a main() function that returns a recorder object

    The workflow_content from the request body is saved to a temporary file and executed.
    The file type is determined from the workflow_id extension (.py, .yaml, or .yml).

    Args:
        workflow_id: Required workflow ID (including file extension)
        request: Request body containing workflow content and optional parameters
    """

    temp_dir = os.path.join(tempfile.gettempdir(), uuid4().hex)
    os.makedirs(temp_dir, exist_ok=True)

    # Determine file extension based on file type
    source_code_path = os.path.join(temp_dir, f"{workflow_id}")

    with open(source_code_path, 'w') as f:
        f.write(request.workflow_content)

    file_type = workflow_id.split(".")[-1].lower()

    runtime_config = {
        "start_date": request.start_date,
        "end_date": request.end_date,
        "initial_capital": request.initial_capital,
        "freq": request.freq
    }

    # 提取文件名（不含扩展名）作为 backtest_group_id 的基础
    strategy_base_name = workflow_id.split(
        ".")[0] if "." in workflow_id else workflow_id

    # Execute the workflow based on file type
    if file_type == "py":
        # Use the file_path parameter for Python workflows, just like YAML workflows
        recorder = execute_python_workflow(source_code_path, runtime_config)
    elif file_type in ["yaml", "yml"]:
        # For YAML workflows, use the existing workflow function
        recorder = workflow(source_code_path, recorder_name=strategy_base_name)
    else:
        raise HTTPException(
            status_code=400, detail="Unsupported file type. Only .py and .yaml/.yml are supported."
        )

    # 获取MLflow客户端
    client = get_mlflow_client()

    # 设置重要的标签，用于前端显示
    tags = {
        "strategy_file": workflow_id,
        "strategy_class": request.strategy_class if hasattr(request, "strategy_class") and request.strategy_class else "Custom",
    }

    tags["backtest_group_id"] = f"strategy_{strategy_base_name}"

    # 计算策略内容的哈希值，用于版本跟踪
    # 只考虑策略代码内容，忽略其他可能变化的元数据
    content = {
        "content": request.workflow_content,
        "runtime_config": runtime_config
    }
    strategy_content_hash = hashlib.sha256(json.dumps(
        content, sort_keys=True).encode()).hexdigest()[:8]
    tags["content_hash"] = strategy_content_hash

    # 设置回测周期标签
    if hasattr(request, "start_date") and request.start_date:
        tags["backtest_start_date"] = request.start_date
    if hasattr(request, "end_date") and request.end_date:
        tags["backtest_end_date"] = request.end_date

    # 设置其他可能有用的标签
    if hasattr(request, "initial_capital") and request.initial_capital:
        tags["initial_capital"] = str(request.initial_capital)
    if hasattr(request, "freq") and request.freq:
        tags["freq"] = request.freq

    # 将标签设置到MLflow运行中
    for key, value in tags.items():
        client.set_tag(recorder.info.get('id'), key, value)

    # 获取recorder ID和experiment ID
    recorder_id = recorder.info.get('id')
    experiment_id = recorder.info.get('experiment_id')

    # 获取artifact路径
    artifact_uri = recorder.artifact_uri
    if artifact_uri.startswith('file:'):
        artifact_path = artifact_uri[5:]  # 移除'file:'前缀
    else:
        artifact_path = artifact_uri

    # 获取分析结果
    analysis_results = get_analysis_results(artifact_path)

    # 构建响应
    response = {
        "status": "success",
        "message": "Workflow execution completed",
        "strategy_id": workflow_id,
        "recorder_id": recorder_id,
        "experiment_id": experiment_id,
        "artifact_path": artifact_path,
        "analysis_results": analysis_results
    }

    # 如果使用了临时文件，添加临时文件路径
    if source_code_path:
        response["source_code_path"] = source_code_path

    # 添加文件内容到响应
    if request and request.workflow_content:
        response["file_content"] = request.workflow_content

    # 将源码拷贝一份到artifacts目录，方便后面调试
    if source_code_path:
        shutil.copy2(source_code_path, os.path.join(
            artifact_path, "source_code." + file_type))

    # 将 request 保存到 artifacts 目录
    with open(os.path.join(artifact_path, "request.json"), 'w') as f:
        json.dump(request.model_dump(), f, indent=2, ensure_ascii=False)

    # 删除临时文件夹
    shutil.rmtree(temp_dir)

    return response


def get_analysis_results(artifact_path: str) -> List[Dict[str, Any]]:
    """
    获取策略运行的分析结果

    Args:
        artifact_path: 工件路径

    Returns:
        分析结果列表
    """
    results = []

    # 检查回测分析结果
    backtest_path = os.path.join(artifact_path, "backtest_analysis")
    if os.path.exists(backtest_path):
        # 加载metrics.json
        metrics_path = os.path.join(backtest_path, "metrics.json")
        metrics = None
        if os.path.exists(metrics_path):
            try:
                with open(metrics_path, 'r') as f:
                    metrics = json.load(f)
            except Exception as e:
                print(f"Failed to load backtest metrics: {str(e)}")

        # 加载equity_curve.csv
        equity_curve_path = os.path.join(backtest_path, "equity_curve.csv")
        equity_curve_data = None
        if os.path.exists(equity_curve_path):
            try:
                import pandas as pd
                equity_curve_df = pd.read_csv(equity_curve_path)
                # 转换为前端可用的格式
                equity_curve_data = {
                    "dates": equity_curve_df.iloc[:, 0].tolist(),  # 第一列通常是日期
                    "equity": equity_curve_df["equity"].tolist() if "equity" in equity_curve_df.columns else []
                }
            except Exception as e:
                print(f"Failed to load equity curve data: {str(e)}")

        # 加载weights_history.csv
        weights_path = os.path.join(backtest_path, "weights_history.csv")
        weights_data = None
        if os.path.exists(weights_path):
            try:
                import pandas as pd
                weights_df = pd.read_csv(weights_path)
                # 转换为前端可用的格式
                # 按日期分组
                weights_by_date = {}
                for _, row in weights_df.iterrows():
                    date = row.get('date', '')
                    ticker = row.get('ticker', '')
                    weight = row.get('weight_pct', 0)

                    if date not in weights_by_date:
                        weights_by_date[date] = []

                    weights_by_date[date].append({
                        "ticker": ticker,
                        "weight": weight
                    })

                weights_data = {
                    "dates": sorted(weights_by_date.keys()),
                    "weights_by_date": weights_by_date
                }
            except Exception as e:
                print(f"Failed to load weights history data: {str(e)}")

        # 加载backtest_config.json
        config_path = os.path.join(backtest_path, "backtest_config.json")
        backtest_config = None
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    backtest_config = json.load(f)
            except Exception as e:
                print(f"Failed to load backtest config: {str(e)}")

        # 加载strategy_config.json
        strategy_config_path = os.path.join(
            backtest_path, "strategy_config.json")
        strategy_config = None
        if os.path.exists(strategy_config_path):
            try:
                with open(strategy_config_path, 'r') as f:
                    strategy_config = json.load(f)
            except Exception as e:
                print(f"Failed to load strategy config: {str(e)}")

        # 创建回测结果对象，使用sections结构
        backtest_sections = []

        # 添加指标部分
        if metrics:
            backtest_sections.append({
                "type": "metrics",
                "description": "回测指标",
                "metrics": metrics
            })

        # 添加权益曲线部分
        if equity_curve_data:
            backtest_sections.append({
                "type": "equity_curve",
                "description": "权益曲线",
                "equity_curve": equity_curve_data
            })

        # 添加配置信息部分
        if backtest_config or strategy_config:
            backtest_sections.append({
                "type": "config",
                "description": "配置信息",
                "backtest_config": backtest_config,
                "strategy_config": strategy_config
            })

        backtest_result = {
            "tab_name": "Backtest Results",
            "type": "backtest",
            "sections": backtest_sections
        }

        # 添加到结果列表
        results.append(backtest_result)

        # 创建持仓分析对象（如果有权重数据）
        if weights_data:
            position_sections = [{
                "type": "weights_history",
                "description": "持仓权重历史",
                "weights_history": weights_data
            }]

            position_analysis = {
                "tab_name": "Position Analysis",
                "type": "position",
                "sections": position_sections
            }
            results.append(position_analysis)

    # 检查绩效归因结果
    perf_att_path = os.path.join(artifact_path, "perf_attribution")
    if os.path.exists(perf_att_path):
        # 查找所有分析结果
        for analyzer in ["quantstats", "brinson", "axioma"]:
            metrics_path = os.path.join(
                perf_att_path, f"{analyzer}_metrics.json")
            report_path = os.path.join(
                perf_att_path, f"{analyzer}_report.json")

            if os.path.exists(metrics_path) or os.path.exists(report_path):
                try:
                    metrics = None
                    sections = None

                    if os.path.exists(metrics_path):
                        with open(metrics_path, 'r') as f:
                            metrics = json.load(f)

                    if os.path.exists(report_path):
                        with open(report_path, 'r') as f:
                            sections = json.load(f)

                    # 创建sections数组
                    analyzer_sections = []

                    # 如果有metrics，添加到sections中
                    if metrics:
                        analyzer_sections.append({
                            "type": "metrics",
                            "description": f"{analyzer.capitalize()} 指标",
                            "metrics": metrics
                        })

                    # 如果有sections，将其内容添加到analyzer_sections
                    if sections:
                        analyzer_sections.extend(sections)

                    results.append({
                        "tab_name": analyzer,
                        "type": analyzer,
                        "sections": analyzer_sections
                    })
                except Exception as e:
                    print(
                        f"Failed to load {analyzer} analysis results: {str(e)}")

    return results
