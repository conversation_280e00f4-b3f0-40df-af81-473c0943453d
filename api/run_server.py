#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
<PERSON>rip<PERSON> to start the FastAPI server
"""

import os
import sys
import uvicorn

# Add project root directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

if __name__ == "__main__":
    # Run using module import
    uvicorn.run("api.main:app", host="0.0.0.0", port=8000, reload=True)
