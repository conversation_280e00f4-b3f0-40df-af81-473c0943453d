# supervisord service for systemd (CentOS 7.0+)
# by ET-CS (https://github.com/ET-CS)
# for PROD. copy me to /lib/systemd/system
[Unit]
Description=Supervisor daemon

[Service]
User=ec2-user
Group=ec2-user
Type=forking
ExecStart=/home/<USER>/venv/bin/supervisord -c /home/<USER>/cocktail/deploy/autogather/lb/supervisor/autogather.conf
ExecStop=/home/<USER>/venv/bin/supervisorctl $OPTIONS shutdown
ExecReload=/home/<USER>/venv/bin/supervisorctl $OPTIONS reload
KillMode=process
Restart=on-failure
RestartSec=42s

[Install]
WantedBy=multi-user.target
