# Simple LB & HA deployment

Deploy `autogather.ai` with `load balancer` (a nginx proxy)

## Simple Architect

```
                      |- backend 0
User --> LB (nginx) --|  ...
                      |- backend x
```

## Load Balance Nginx Server & FE

### requirements & config

1. An EC2 with high network performance.
2. Low CPU performance, ARM is OK.
3. Low Memory. (higher if use nginx to cache pages)
4. put FE to /opt/onwish/dist
5. modify `/etc/hosts` to set `atg0`, `atg1` IPs (which will be used in nginx conf)
6. need a `EIP` for public access
7. use `prod-onwish` security group
8. edit `upstream` section in `autogather.conf` & `autogather-admin.conf` in `nginx/conf.d/` for more backends.

   ```
   upstream autogather-backends {
	   server atg0:8000;
	   server atg0:8001;
	   server atg1:8000;
	   server atg1:8001;
   }
   ```

We are using `c7g.medium` (1 core 2G ram, 8G SSD, up to 12.5Gbits network)

## Backend

### requirements & config

So far, we still run spiders in backend. So we need:

1. Ubuntu OS. (for `playwright`)
2. create `ec2-user` as common user. (copy ssh key, make it be able to `sudo`, create home at /home/<USER>
3. Common level CPU performance or higher
4. Above common level Memory (2G for 1 playwright instance)
5. Only run backend python app (no FE on it)
6. **EIP is NO need**. (access via private network)
7. use `private-onwish` security group

we are using `t3a.large` (2core 8G) or `t3a.xlarge` (4core 16G).

### Possible enhancement

extract spider related out as a service. then we can use small EC2 boxes.

## More

Tried but passed at current time

1. tried AWS `Application LB`, it's complex to configure and cost heavy.
2. tried AWS `Classic LB`, it's simple but does not support `websocket`

Considered other solutions but passed

1. HAProxy
2. LVS
