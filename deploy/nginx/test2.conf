upstream backends-test2 {
    server 127.0.0.1:8080;
    #server *********:8000 backup;
}

map $http_upgrade $connection_upgrade {
	default upgrade;
	''		close;
}

server {
    listen       80;
    listen       [::]:80;
    server_name  test2.onwish.ai;

    return 301 https://$host$request_uri;
}

server {
	keepalive_requests 120;
	listen       443 ssl;
        listen       [::]:443 ssl;
	server_name  test2.onwish.ai;


	ssl_certificate /etc/nginx/ssl/onwish.ai/onwish-chain.crt;
	ssl_certificate_key /etc/nginx/ssl/onwish.ai/onwish.key;
	ssl_prefer_server_ciphers on;


        location = /robots.txt {
            alias /opt/onwish/dist/robots-test2.txt;
        }


	location  ^~ /role/ {
    	return 200 'test2';
    	add_header Content-Type text/plain;
	}

        location ^~ /airflow/ {
                proxy_pass http://localhost:8090;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
        }

	location  ^~ /api/ {
		proxy_pass	http://backends-test2;
		proxy_read_timeout 600;


	}

	location  ^~ /accounts/ {
		proxy_pass	http://backends-test2;
	}

        location  ^~ /account/ {
		proxy_pass	http://backends-test2;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
	}


	location  ^~ /_allauth/ {
                proxy_pass      http://backends-test2;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
        }

	location /admin {
           return 301 https://test2-admin.onwish.ai$request_uri;
        }

	location  ^~ /tools/ {
	   proxy_pass	http://backends-test2;
	}

	location  ^~ /p/ {
	   proxy_pass	http://backends-test2;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
	}

	location ^~ /ws/ {
		proxy_pass http://backends-test2;
		proxy_http_version 1.1;
		proxy_set_header Host $host;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection $connection_upgrade;
	}

	# for dev only. if settings.DEBUG=True, this will not work.
		location ^~ /static/ {
			proxy_pass http://backends-test2;
	}

        location / {
            root /opt/onwish/dist-test2;
            index index.html;
            try_files $uri $uri/ /index.html;
        }	
	
	#error_page 404 /404.html;
	#location = /404.html {
	#}

	#error_page 500 502 503 504 /50x.html;
	#location = /50x.html {
	#}
}

