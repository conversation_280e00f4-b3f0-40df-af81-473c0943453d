upstream backends {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
#     server 127.0.0.1:8002;
#     server 127.0.0.1:8003;
}

map $http_upgrade $connection_upgrade {
	default upgrade;
	''		close;
}

server {
    listen       80;
    listen       [::]:80;
	server_name  aiflow.onwish.ai;

    return 301 https://$host$request_uri;
}

server {
	keepalive_requests 120;
	listen       443 ssl;
	listen       [::]:443 ssl;
	server_name  aiflow.onwish.ai;

	ssl_certificate /etc/nginx/ssl/onwish.ai/onwish-chain.crt;
	ssl_certificate_key /etc/nginx/ssl/onwish.ai/onwish.key;
	ssl_prefer_server_ciphers on;

	location  ^~ /role/ {
    	return 200 'aiflow';
    	add_header Content-Type text/plain;
	}

	location  ^~ /api/ {
		proxy_pass	http://backends;
	}

	location  ^~ /accounts/ {
		proxy_pass	http://backends;
	}

	location  ^~ /account/ {
			proxy_pass      http://backends;
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Forwarded-Proto $scheme;
	}

	location  ^~ /_allauth/ {
			proxy_pass      http://backends;
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Forwarded-Proto $scheme;
	}

	location /admin {
            return 301 https://admin.onwish.ai$request_uri;
        }

	location  ^~ /tools/ {
	   proxy_pass	http://backends;
	}

	location  ^~ /p/ {
			proxy_pass      http://backends;
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Forwarded-Proto $scheme;
	}

	location ^~ /ws/ {
		proxy_pass http://backends;
		proxy_http_version 1.1;
		proxy_set_header Host $host;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection $connection_upgrade;
	}

	# for prod / pre only.
	#location ^~ /static {
	#	alias /opt/onwish/static;
	#}

	location  ^~ /pages {
		alias /opt/onwish/pages;
	}

#  	location  ~*^.+$ {
#  	   root		/opt/onwish/dist-aiflow;
#  	   index	index.html;
#  	   #proxy_pass	http://backends;
#  	   #deny 127.0.0.1;
#  	   #allow **********;
#  	}

	error_page 404 /404.html;
	location = /404.html {
	}

	error_page 500 502 503 504 /50x.html;
	location = /50x.html {
	}

	location / {
		root /opt/onwish/dist-aiflow;
		index index.html;
		try_files $uri $uri/ /index.html;
		add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
	}
}
