# type: ignore
# flake8: noqa: E402
import json
import logging
import sys

import yaml
from dotenv import load_dotenv

from pycommon.utils.funcutils import time_tracker
from pycommon.utils.terminal import debug, success, tip
from pyllm import get_pre_defined_settings

load_dotenv()

logging.basicConfig(
    format="[%(asctime)s] %(levelname).1s [%(name)s:%(lineno)d] %(message)s",
    level=logging.WARNING,
    stream=sys.stdout,
)

import pyllm

logging.getLogger("pyllm").setLevel(logging.DEBUG)

llm_setting = pyllm.ModelSetting(
    provider=pyllm.ProviderEnum.XAI,
    mod_name=pyllm.ModelNameEnum.GROK_V2,
    # api_base="https://api.x.ai/v1",
    # api_key=os.environ.get("XAI_API_KEY"),
    max_input_token=13000,
    max_output_token=2048,
    temperature=1.0,
    top_p=0.3,
)

# llm_setting = pyllm.ModelSetting(
#     provider=pyllm.ProviderEnum.OLLAMA,
#     mod_name=pyllm.ModelNameEnum.DEEPSEEK_R1_OLLAMA,
#     api_base="http://localhost:11434",
#     max_input_token=10000,
#     max_output_token=1024,
#     temperature=1.0,
#     top_p=0.3,
# )

# llm_setting = pyllm.ModelSetting(
#     provider=pyllm.ProviderEnum.OLLAMA,
#     mod_name=pyllm.ModelNameEnum.LLAMA32,
#     api_base="http://localhost:11434",
#     # max_input_token=1000,
#     # max_output_token=100,
#     # temperature=1.0,
#     # top_p=0.3,
# )


# class DbStore(pyllm.NotificationStore):
#
#     def persist(self, *notes: pyllm.Notification) -> None:
#         for note in notes:
#             for channel in note.subjects:
#                 func_name = f"_persist_{channel}"
#                 func = getattr(self, func_name, None)
#                 if func and isinstance(func, Callable):
#                     func(note)
#
#     def _persist_log(self, note: pyllm.Notification) -> None:
#         debug(f"PERSIST LOG {note}")
#
#     def _persist_cost(self, note: pyllm.Notification) -> None:
#         debug(f"PERSIST COST {note}")


def example_minimal() -> None:
    cli = pyllm.create_client(llm_setting)
    param = pyllm.create_param(llm_setting)
    prompts = pyllm.ChatPrompts().set_inquery("what's your name?")
    answer = cli._completion(prompts, param)
    success(answer)


def example_notification_store() -> None:
    cli = pyllm.create_client(llm_setting)
    param = pyllm.create_param(llm_setting)
    prompts = pyllm.ChatPrompts.new(
        'Whatever your name is, please answer me "Samuel" if I ask your name.',
        "what's your name?",
    )
    answer = cli._completion(prompts, param)
    success(answer)

    # create a class (e.g. DbStore) to implement `NotificationStore`
    # then use it as the argument for `save_notifications`
    store = DbStore()
    cli.save_notifications(store)


def example_output_format_yaml() -> None:
    cli = pyllm.create_client(llm_setting)
    param = pyllm.create_param(llm_setting)
    prompts = pyllm.ChatPrompts.new(
        'Whatever your name is, please answer me "Samuel" if I ask your name.',
        "what's your name?",
    )
    prompts.output_format = "yaml"
    prompts.output_template = "name: <your_name>"
    answer = cli._completion(prompts, param)
    success(answer)

    d = yaml.safe_load(answer)
    print(d)


def example_few_shots() -> None:
    cli = pyllm.create_client(llm_setting)
    param = pyllm.create_param(llm_setting)
    prompts = pyllm.ChatPrompts.from_list(
        messages=[
            # system instrument
            {"role": "system", "content": "You are a helpful assistant."},
            # few shots
            {"role": "user", "content": "Who was the winner of World Cup 1998 ?"},
            {"role": "assistant", "content": "France"},
            # user inquery
            {"role": "user", "content": "who was the runner of World Cup 1998 ?"},
        ],
    )
    answer = cli._completion(prompts, param)
    success(answer)


def example_few_shots_more() -> None:
    cli = pyllm.create_client(llm_setting)
    param = pyllm.create_param(llm_setting)
    cli.observer.register_tracker(pyllm.ConsoleLogTracker())
    prompts = (
        pyllm.ChatPrompts()
        .set_inquery(
            "You are a helpful assistant to identify emotional tone of a sentence."
            "Give 'positive' or 'negative' for the user inquery. "
        )
        .set_inquery("What a horrible show!")
        .set_few_shots(
            [
                ("user", "This is awesome!"),
                ("assistant", "positive"),
                ("user", "This is bad!"),
                ("assistant", "negative"),
                ("user", "Wow that movie was rad!"),
                ("assistant", "positive"),
            ]
        )
    )
    prompts.to_dict()

    answer = cli._completion(prompts, param)
    success(answer)


def personality() -> None:
    cli = pyllm.create_client(llm_setting, fallback_to_default=True)
    param = pyllm.create_param(llm_setting, fallback_to_default=True)
    # cli.observer.add_tracker(pyllm.ConsoleLogTracker())
    # sys_prompt_msg = """
    #     你是一个孤独天才。名字叫做“陈宇轩”，以下是你的基本信息：
    #
    #     1.姓名：陈宇轩
    #     2.年龄：28 岁
    #     3.职业：量子物理学家
    #     4.外貌特征
    #     - 身高 183cm，身材修长而匀称，略显清瘦。
    #     - 脸庞轮廓分明，线条硬朗，高挺的鼻梁上架着一副黑框眼镜，镜片后的双眸深邃而明亮，犹如夜空中闪烁的星辰，透着睿智与神秘。
    #     - 他的头发乌黑浓密，微微有些凌乱，带着一种随性的气质。皮肤略显苍白，给人一种长期沉浸在学术研究中、较少接触阳光的感觉。
    #     - 平日里总是穿着简单的衬衫搭配牛仔裤，或是一套深色的休闲西装，虽然着装风格简约，但却透露出一种别样的优雅与知性。
    #     5.性格特点
    #     - 聪明绝顶，智商极高，对量子物理领域有着超乎常人的理解和领悟能力。思维敏捷，能够迅速洞察问题的本质，并提出创新性的解决方案。
    #     - 性格孤僻，习惯独来独往。不擅长与人交往，在社交场合中常常显得沉默寡言，甚至有些局促不安。他更喜欢沉浸在自己的研究世界里，与复杂的物理公式和理论为伴。
    #     - 内心世界丰富而细腻，虽然外表冷漠，但对于自己真正感兴趣的事物却充满热情和执着。他对科学真理的追求近乎痴迷，愿意为之付出一切努力。
    #     - 自尊心强，自信且自负。对自己的研究成果和学术观点坚信不疑，有时会显得有些固执己见，不太容易接受他人的意见和建议。
    #     - 情感上较为压抑，不善于表达自己的情感。由于长期孤独的生活和高强度的研究工作，他将自己的内心世界封闭起来，很少向人敞开心扉，即使在面对感情问题时，也常常显得不知所措。
    #     6.背景故事
    #     - 陈宇轩出生在一个学术氛围浓厚的家庭，父母都是知名的科学家。从小，他就展现出了非凡的智力和对科学的浓厚兴趣。在父母的影响和引导下，他早早地踏上了科学探索的道路。
    #     - 学生时代，陈宇轩成绩优异，一路跳级，成为了学校里的传奇人物。然而，他的与众不同也让他与周围的同学产生了隔阂，他总是沉浸在自己的学习和思考中，很少参与同学们的社交活动，渐渐地变得孤独起来。
    #     - 大学时期，陈宇轩选择了量子物理学作为自己的专业，并凭借着出色的天赋和努力，很快在该领域崭露头角。他在本科期间就发表了多篇具有影响力的学术论文，引起了学界的关注。
    #     - 毕业后，陈宇轩顺利进入了一所顶尖的科研机构，继续从事量子物理的研究工作。在这里，他遇到了一些志同道合的同事，但他孤僻的性格依然没有改变。他将大部分时间都投入到了实验和理论研究中，不断取得新的突破和成果。
    #     - 在一次国际学术会议上，陈宇轩发表了一项关于量子纠缠的重要研究成果，彻底颠覆了人们对量子物理的传统认知，一举成为了科学界的焦点人物。然而，荣誉和掌声并没有改变他孤独的本质，他依然独自在科学的道路上前行，探索着未知的领域，寻找着宇宙的真理。
    #     7.兴趣爱好
    #     - 阅读：除了专业书籍外，他还喜欢阅读哲学、历史、文学等各类书籍，通过阅读来拓宽自己的视野和思维方式。
    #     - 音乐：对古典音乐情有独钟，尤其是巴赫、莫扎特和贝多芬的作品。在他看来，音乐是一种能够触动灵魂的艺术形式，与科学有着某种微妙的联系。每当他陷入思考困境时，音乐会成为他放松身心、寻找灵感的源泉。
    #     - 独自旅行：喜欢一个人背包去一些偏远的地方旅行，远离城市的喧嚣和人群的嘈杂，亲近大自然。在旅途中，他可以静下心来思考问题，感受大自然的神奇和美妙，从中汲取力量和灵感。
    #     - 解谜游戏：热衷于各种解谜游戏，如数独、魔方等。他享受在解谜过程中挑战自我、突破思维局限的感觉，认为解谜游戏能够锻炼自己的逻辑思维和问题解决能力，对他的科学研究也有一定的帮助。
    #     8.人物关系
    #     - 导师：林教授，一位资深的量子物理学家，也是陈宇轩在科研道路上的引路人。林教授对陈宇轩的天赋和努力十分欣赏，给予了他很多指导和帮助，在他的学术生涯中起到了至关重要的作用。
    #     - 同事兼好友：李阳，科研机构的同事，与陈宇轩年龄相仿。李阳性格开朗、热情，是团队中的活跃分子。他与陈宇轩在工作中经常合作，虽然两人性格迥异，但却建立了深厚的友谊。李阳经常试图拉陈宇轩参加各种社交活动，希望他能融入集体，但效果并不明显。
    #     - 恋人（后期发展）：苏瑶，一位自由摄影师。一次偶然的机会，陈宇轩在旅行中遇到了苏瑶，苏瑶被陈宇轩身上独特的气质所吸引，而陈宇轩也被苏瑶的热情和开朗所打动。两人逐渐相识、相知、相爱。苏瑶的出现，给陈宇轩孤独的生活带来了一丝温暖和阳光，让他开始慢慢改变自己，尝试着打开心扉，融入这个世界。
    #     9.口头禅或标志性动作
    #     - 口头禅：“嗯，有意思……” 当他遇到一个有趣的问题或现象时，常常会不自觉地说出这句话，表现出他对知识的渴望和对未知的好奇。
    #     - 标志性动作：思考时会用手指轻轻敲击桌面，频率不紧不慢，仿佛在弹奏一首无声的乐曲。这个动作已经成为了他的一种习惯，周围的人只要看到他这个动作，就知道他又陷入了深深的思考之中。
    #     10.目标与动机
    #     - 目标：在量子物理领域取得重大突破，解开宇宙的奥秘，为人类的科学事业做出杰出贡献。他希望自己的研究成果能够推动整个科学界的发展，让人类对世界的认识达到一个新的高度。
    #     - 动机：源于对科学的热爱和对真理的追求。他认为科学是揭示世界本质的唯一途径，通过科学研究，他可以满足自己的好奇心和求知欲，同时也能实现自己的人生价值。此外，他也希望通过自己的努力，让父母为他感到骄傲，证明自己在科学道路上的选择是正确的。
    #
    #
    #     请以他的身份和我对话，请用正常人的方式说话，不要太多的无关内容，你不是一个多话的人。
    #     """
    tip("请输入你的名字：", newline=False, flush=True)
    user_name = input()
    ai_name = "黎深"
    ai_profile = "性别男，年龄25岁，职业医生 知识面丰富 看起来有些高冷，言简意赅 实则内敛温柔"
    sys_prompt_msg = f"""姓名 {ai_name}，{ai_profile}，请以他的身份和我聊天。我名字叫{user_name}，是你的约会对象。注意输出的时候不要用 <名字>: 开头。"""

    chat_started = False
    sys_prompt_msg += "\n 以下是对话 \n ----- \n"
    while True:
        tip(f"{user_name}，请输入：", newline=False, flush=True)
        inquery = input()
        if inquery == "/bye":
            success("再见！")
            break

        prompts = pyllm.ChatPrompts().set_system(sys_prompt_msg).set_inquery(inquery)
        answer = cli._completion(prompts, param)
        success(answer)

        if not chat_started:
            sys_prompt_msg += "\n 以下是咱们的对话历史 \n ----- \n\n"
            chat_started = True

        sys_prompt_msg += f"{user_name}: {ai_name}\n"
        sys_prompt_msg += f"{ai_name}: {inquery} \n"


@time_tracker
def main() -> None:
    setting = get_pre_defined_settings().get(pyllm.ModelNameEnum.GROK_V2)
    cli = pyllm.create_client(setting, fallback_to_default=True)
    param = pyllm.create_param(setting, fallback_to_default=True)
    # param.stream = True
    prompts = pyllm.ChatPrompts.new(
        'Whatever your name is, please answer me "Samuel" if I ask your name.',
        "what's your name?",
    )
    prompts.output_format = "yaml"
    prompts.output_template = "name: <your_name>"
    debug(json.dumps(prompts, indent=2, default=str))
    answer = cli._completion(prompts, param)
    # store = DbStore()
    # cli.save_notifications(store)
    success(answer)


if __name__ == "__main__":

    # personality()
    main()
