from logging import getLogger
from typing import overload, Type, Any

import litellm

from pycommon.utils.confutils import get_str_env
from pycommon.utils.lang import BaseStrEnum
from .client import (
    OllamaClient,
    LlmClient,
    OpenAIClient,
    GeminiClient,
    DeepSeekClient,
    LlmGeneralClient,
)
from .consts import NAME_SEP
from .enums import ProviderEnum, ModelNameEnum, model_to_provider
from .params import (
    OllamaParam,
    ModelSetting,
    ChatParam,
    OpenAIParam,
    GeminiParam,
    DeepSeekParam,
    get_pre_defined_settings,
)
from .prompts import ChatPrompts, Prompt  # noqa: F401

# Globally init litellm logging with logfile
if get_str_env("LOGFIRE_TOKEN"):
    litellm.callbacks = ["logfire"]
    # litellm.success_callback = ["logfire"]
    # litellm.failure_callback = ['logfire']


__DEFAULT_PROVIDER = "default"
__DEFAULT_MODEL = "default"

logger = getLogger(__name__)


class __KIND(BaseStrEnum):
    CLIENT = "client"
    PARAMETER = "parameter"


__model_class_mapping = {
    "default": {
        "default": {__KIND.CLIENT: LlmGeneralClient, __KIND.PARAMETER: ChatParam}
    },
    ProviderEnum.OPENAI: {
        ModelNameEnum.GPT_4o_MINI: {
            __KIND.CLIENT: OpenAIClient,
            __KIND.PARAMETER: OpenAIParam,
        },
    },
    ProviderEnum.OLLAMA: {
        ModelNameEnum.OLLAMA_LLAMA_2_UNCENSORED: {
            __KIND.CLIENT: OllamaClient,
            __KIND.PARAMETER: OllamaParam,
        },
        ModelNameEnum.OLLAMA_LLAMA_32_3B: {
            __KIND.CLIENT: OllamaClient,
            __KIND.PARAMETER: OllamaParam,
        },
        ModelNameEnum.OLLAMA_LLAMA_32_VISION: {
            __KIND.CLIENT: OllamaClient,
            __KIND.PARAMETER: OllamaParam,
        },
    },
    ProviderEnum.GEMINI: {
        ModelNameEnum.GEMINI_20_FLASH_LITE: {
            __KIND.CLIENT: GeminiClient,
            __KIND.PARAMETER: GeminiParam,
        },
    },
    ProviderEnum.DEEPSEEK: {
        ModelNameEnum.DEEPSEEK_V3: {
            __KIND.CLIENT: DeepSeekClient,
            __KIND.PARAMETER: DeepSeekParam,
        },
        ModelNameEnum.DEEPSEEK_R1: {
            __KIND.CLIENT: DeepSeekClient,
            __KIND.PARAMETER: DeepSeekParam,
        },
    },
    ProviderEnum.XAI: {
        ModelNameEnum.GROK_V2: {},
    },
}


@overload
def create_client(
    provider: ProviderEnum | str, model_name: ModelNameEnum | str, **kwargs: Any
) -> LlmClient:
    pass


@overload
def create_client(model_name: ModelNameEnum | str, **kwargs: Any) -> LlmClient:
    pass


@overload
def create_client(setting: ModelSetting, **kwargs: Any) -> LlmClient:
    pass


def create_client(*args: Any, **kwargs: Any) -> LlmClient:
    """create a LLM client

    args: (see overloads)
        setting (ModelSetting): setting to create LLM client.

    args (alternative 1, see overloads):
        model_name : name of the LLM model

    args: (alternative 2, see overloads)
        provider (LlmProviderEnum): provider of the LLM model.
        model_name (LlmNameEnum): name of the LLM model.

    keyword args:
        fallback_to_default (bool): whether fallback to default

    Returns (LlmClient):
        Client object of a kind of LLM client.

    """
    setting = _get_setting_from_args(*args, **kwargs)
    cls = get_client_type(*args, **kwargs)
    return cls(setting=setting)


@overload
def create_param(
    provider: ProviderEnum | str, model_name: ModelNameEnum | str, **kwargs: Any
) -> ChatParam:
    pass


@overload
def create_param(model_name: ModelNameEnum | str, **kwargs: Any) -> ChatParam:
    pass


@overload
def create_param(setting: ModelSetting, **kwargs: Any) -> ChatParam:
    pass


def create_param(*args, **kwargs) -> ChatParam:
    """create a LLM chat param

    args: (see overloads)
        setting (ModelSetting): setting to create LLM param.

    args (alternative 1, see overloads):
        model_name : name of the LLM model

    args: (alternative 2, see overloads)
        provider (LlmProviderEnum): provider of the LLM model.
        model_name (LlmNameEnum): name of the LLM model.

    keyword args:
        fallback_to_default (bool): whether fallback to default

    Returns (ChatParam):
        ChatParam object of a kind of LLM.

    """
    cls = get_param_type(*args, **kwargs)
    return cls(**kwargs)


@overload
def get_param_type(setting: ModelSetting, **kwargs: Any) -> Type[ChatParam]:
    pass


@overload
def get_param_type(
    provider: ProviderEnum | str, model_name: ModelNameEnum | str, **kwargs: Any
) -> Type[ChatParam]:
    pass


@overload
def get_param_type(model_name: ModelNameEnum | str, **kwargs: Any) -> Type[ChatParam]:
    pass


def get_param_type(*args, **kwargs) -> Type[ChatParam]:
    """get the type (class) of parameter for run model.

    args: (see overloads)
        setting (ModelSetting): setting to get param type from.

    args (alternative 1, see overloads):
        model_name : name of the LLM model

    args: (alternative 2, see overloads)
        provider (LlmProviderEnum): provider of the LLM model.
        model_name (LlmNameEnum): name of the LLM model.

    keyword args:
        fallback_to_default (bool): whether fallback to default

    Returns:
        Type `LlmParam`(class) , parameter of a kind of LLM client.
    """
    return _get_type(__KIND.PARAMETER, *args, **kwargs)  # type: ignore


@overload
def get_client_type(setting: ModelSetting, **kwargs: Any) -> Type[LlmClient]:
    pass


@overload
def get_client_type(
    provider: ProviderEnum | str, model_name: ModelNameEnum | str, **kwargs: Any
) -> Type[LlmClient]:
    pass


@overload
def get_client_type(model_name: ModelNameEnum | str, **kwargs: Any) -> Type[LlmClient]:
    pass


def get_client_type(*args, **kwargs) -> Type[LlmClient]:
    """get the type (class) of a kind LLM client.

    args: (see overloads)
        setting (ModelSetting): setting to get client type from.

    args (alternative 1, see overloads):
        model_name : name of the LLM model

    args: (alternative 2, see overloads)
        provider (LlmProviderEnum): provider of the LLM model.
        model_name (LlmNameEnum): name of the LLM model.

    keyword args:
        fallback_to_default (bool): whether fallback to default


    Returns:
        Type `LlmClient` (class) of a kind of LLM client.
    """
    return _get_type(__KIND.CLIENT, *args, **kwargs)  # type:ignore


def _get_type(
    t: __KIND, *args, fallback_to_default: bool = False, **kwargs
) -> Type[LlmClient | ChatParam]:
    """get type by given KIND ("client", "parameter") and setting
    (or setting from arguments provider and model_name)

    :param t:
    :param args:
    :param fallback_to_default:
    :param kwargs:
    :return:
    """
    setting = _get_setting_from_args(
        *args, fallback_to_default=fallback_to_default, **kwargs
    )
    cls = (
        __model_class_mapping.get(setting.provider, {}).get(setting.mod_name, {}).get(t)
    )
    if cls is None:
        msg = f'{t} class for "{setting.provider}{NAME_SEP}{setting.mod_name}" not implemented.'
        if fallback_to_default:
            cls = (
                __model_class_mapping.get(__DEFAULT_PROVIDER, {})
                .get(__DEFAULT_MODEL, {})
                .get(t)
            )
            logger.debug(f"Fallback to {cls} due to {msg}")
        else:
            raise NotImplementedError(
                f"{msg} You can use `fallback_to_default=True` arg to fallback to default class."
            )
    return cls  # type: ignore


def _get_setting_from_args(
    *args, fallback_to_default: bool = False, **kwargs
) -> ModelSetting:
    try:
        if len(args) == 1 and isinstance(args[0], ModelSetting):
            setting = args[0]
        elif len(args) == 1 and isinstance(args[0], (ModelNameEnum, str)):
            model_name = args[0]
            provider = model_to_provider.get(model_name)
            setting = ModelSetting(
                provider=ProviderEnum(provider), mod_name=ModelNameEnum(model_name)
            )
        elif (
            len(args) == 2
            and isinstance(args[0], (ProviderEnum, str))
            and isinstance(args[1], (ModelNameEnum, str))
        ):
            setting = ModelSetting(
                provider=ProviderEnum(args[0]), mod_name=ModelNameEnum(args[1])
            )
        else:
            setting = kwargs.get("setting")  # type: ignore
            if not setting:
                model_name = kwargs.get("model_name", "")
                provider = kwargs.get("provider") or model_to_provider.get(model_name)
                setting = ModelSetting(
                    provider=ProviderEnum(provider), mod_name=ModelNameEnum(model_name)
                )
    except ValueError as e:
        if fallback_to_default:
            setting = ModelSetting(
                provider=__DEFAULT_PROVIDER, mod_name=__DEFAULT_MODEL
            )
            logger.warning(
                f"Fallback to {setting} due to invalid arguments. {e}. "
                f'Set "fallback_to_default=False" if you do not want fallback.'
            )

        else:
            raise
    return setting
