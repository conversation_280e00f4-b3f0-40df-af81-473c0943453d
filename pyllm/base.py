import asyncio
from datetime import datetime
from decimal import Decimal
from typing import Literal, Optional, Callable, Any, get_args, Union

from pydantic import BaseModel

from pycommon.utils.datetimeutils import UTC
from pycommon.utils.lang import BaseStrEnum, DictMixin
from .enums import ProviderEnum, ModelNameEnum

Modality = Literal["text", "image", "audio", "video"]


class ModelCap(BaseStrEnum):
    """Model's capability"""

    STREAMING = "streaming"
    BATCH = "batch"
    FUNCTION_CALL = "function_call"
    STRUCTURED_OUTPUT = "structured_output"
    COT = "chain_of_thought"


class ModelRateLimit(BaseModel, DictMixin):
    rpm: Optional[int] = None  # request / min
    rpd: Optional[int] = None  # request / day
    tpm: Optional[int] = None  # tokens / min
    bql: Optional[int] = None  # batch queue limit


class ModalityPrice(BaseModel, DictMixin):
    input_per_token: Decimal
    cached_input_per_token: Optional[Decimal] = None
    output_per_token: Decimal

    @property
    def input_price_per_million_token(self) -> float:
        return float(self.input_per_token * 1_000_000)

    @property
    def cached_input_price_per_million_token(self) -> Optional[float]:
        return float(self.cached_input_per_token * 1_000_000) if self.cached_input_per_token else None

    @property
    def output_price_per_million_token(self) -> float:
        return float(self.output_per_token * 1_000_000)


class ModelPrice(BaseModel, DictMixin):
    currency: Literal["USD", "CNY"] = "USD"

    modal_text: Optional[ModalityPrice] = None
    modal_image: Optional[ModalityPrice] = None
    modal_audio: Optional[ModalityPrice] = None
    modal_video: Optional[ModalityPrice] = None

    def __init__(self, **kwargs: Any) -> None:
        fields = {k.split("_")[1] for k in self.model_fields if k.startswith("modal_")}
        modals = set(get_args(Modality))
        if fields != modals:
            raise AttributeError(f"{self.__class__.__name__} must has and only has fields of {modals}")

        super().__init__(**kwargs)

    @property
    def default(self) -> ModalityPrice:
        """return default price"""
        return self.modal_text

    @property
    def currency(self) -> Literal["USD", "CNY"]:
        return self.default.currency

    @property
    def input_per_token(self) -> Decimal:
        return self.default.input_per_token

    @property
    def cached_input_per_token(self) -> Decimal:
        return self.default.cached_input_per_token

    @property
    def output_per_token(self) -> Decimal:
        return self.output_per_token


class ModelSpec(BaseModel, DictMixin):
    context_window_tokens_limit: int
    input_tokens_limit: Optional[int]  # input limit maybe dynamic
    output_tokens_limit: int

    # chain of thought (CoT) token limit, only available if features has "cot"
    cot_token_limit: Optional[int] = None

    # to get default price key
    # - str: the key of the default price.
    # - callable: function/coro-func to get price key.
    default_price: Union[str, Callable[["ModelSpec"], str]] = ""
    prices: dict[str, ModelPrice] = {}

    input_modalities: set[Modality] = {"text"}
    output_modalities: set[Modality] = {"text"}
    capabilities: set[ModelCap] = set()  # model's supported capabilities
    rate_limits: dict[str, ModelRateLimit] = {}

    # when the knowledge cutoff for training the model
    knowledge_cutoff: Optional[datetime] = None

    @property
    def current_price(self) -> ModelPrice:
        if isinstance(self.default_price, Callable):
            if asyncio.iscoroutinefunction(self.default_price):
                key = asyncio.get_event_loop().run_until_complete(self.default_price(self))
            else:
                key = self.default_price(self)
        else:
            key = str(self.default_price)
        assert key in self.prices.keys(), f"default_price must be or return one of {self.prices.keys()}"

        price = self.prices.get(key) or self.prices.get("default") or self.prices.get("")
        return price


model_specs = {
    ProviderEnum.OPENAI.value: {
        # gpt40-mini
        ModelNameEnum.GPT_4o_MINI.value: ModelSpec(
            context_window_tokens_limit=128_1000,
            input_tokens_limit=None,
            output_tokens_limit=16_384,
            input_modalities={"text", "image"},
            output_modalities={"text"},
            default_price="standard",
            prices={
                "standard": ModelPrice(
                    currency="USD",
                    modal_text=ModalityPrice(
                        input_per_token=Decimal(0.15) / 1_000_000,
                        cached_input_per_token=Decimal(0.075) / 1_000_000,
                        output_per_token=Decimal(0.6) / 1_000_000,
                    ),
                )
            },
            capabilities={
                ModelCap.BATCH,
                ModelCap.STREAMING,
                ModelCap.FUNCTION_CALL,
                ModelCap.STRUCTURED_OUTPUT,
            },
            rate_limits={
                "free": ModelRateLimit(rpm=3, rpd=200, tpm=40_000, bql=None),
                "tier1": ModelRateLimit(rpm=500, rpd=10_000, tpm=200_000, bql=2_000_000),
                "tier2": ModelRateLimit(rpm=5_000, rpd=None, tpm=2_000_000, bql=20_000_000),
                "tier3": ModelRateLimit(rpm=5_000, rpd=None, tpm=4_000_000, bql=40_000_000),
                "tier4": ModelRateLimit(rpm=10_000, rpd=None, tpm=10_000_000, bql=1_000_000_000),
                "tier5": ModelRateLimit(rpm=30_000, rpd=None, tpm=150_000_000, bql=15_000_000_000),
            },
            knowledge_cutoff=datetime.strptime("2023-10-01", "%Y-%m-%d").replace(tzinfo=UTC),
        ),
    },
    ProviderEnum.GEMINI.value: {
        # Google gemini-2.0-flash-light
        ModelNameEnum.GEMINI_20_FLASH_LITE.value: ModelSpec(
            context_window_tokens_limit=1_048_576,
            input_tokens_limit=1_048_576,
            output_tokens_limit=8_192,
            input_modalities={"text", "image", "audio", "video"},
            output_modalities={"text"},
            default_price="free",
            prices={
                "free": ModelPrice(
                    currency="USD",
                    modal_text=ModalityPrice(
                        input_per_token=Decimal(0) / 1_000_000,
                        cached_input_per_token=Decimal(0) / 1_000_000,
                        output_per_token=Decimal(0) / 1_000_000,
                    ),
                ),
                "standard": ModelPrice(
                    currency="USD",
                    modal_text=ModalityPrice(
                        input_per_token=Decimal(0.075) / 1_000_000,
                        # cached_input_per_token=Decimal(0.25) / 1_000_000,
                        output_per_token=Decimal(0.3) / 1_000_000,
                    ),
                ),
            },
            capabilities={
                ModelCap.STREAMING,
                ModelCap.STRUCTURED_OUTPUT,
            },
            rate_limits={
                "free": ModelRateLimit(rpm=30, rpd=1_500, tpm=1_000_000, bql=None),
                "tier1": ModelRateLimit(rpm=4_000, rpd=None, tpm=4_000_000, bql=None),
                "tier2": ModelRateLimit(rpm=4_000, rpd=None, tpm=4_000_000, bql=None),
            },
            knowledge_cutoff=datetime.strptime("2024-08-01", "%Y-%m-%d").replace(tzinfo=UTC),
        ),
    },
    ProviderEnum.XAI.value: {
        # xAI(twitter), grok-2-latest
        ModelNameEnum.GEMINI_20_FLASH_LITE.value: ModelSpec(
            context_window_tokens_limit=131_072,
            input_tokens_limit=None,
            output_tokens_limit=8_192,
            input_modalities={"text"},
            output_modalities={"text"},
            default_price="standard",
            prices={
                "standard": ModelPrice(
                    currency="USD",
                    modal_text=ModalityPrice(
                        input_per_token=Decimal(2) / 1_000_000,
                        # cached_input_per_token=Decimal(0.25) / 1_000_000,
                        output_per_token=Decimal(10) / 1_000_000,
                    ),
                ),
            },
            capabilities={
                ModelCap.STREAMING,
                ModelCap.STRUCTURED_OUTPUT,
            },
            rate_limits={},
            knowledge_cutoff=datetime.strptime("2024-07-17", "%Y-%m-%d").replace(tzinfo=UTC),
        ),
    },
    ProviderEnum.DEEPSEEK.value: {
        # deepseek v3 (deepseek-chat)
        ModelNameEnum.DEEPSEEK_V3.value: ModelSpec(
            context_window_tokens_limit=64_000,
            input_tokens_limit=None,
            output_tokens_limit=8_192,
            input_modalities={"text"},
            output_modalities={"text"},
            default_price=(
                lambda spec: (
                    spec.prices["standard"]
                    if (
                        datetime.now(tz=UTC).replace(hour=0, minute=30, second=0)
                        < datetime.now(tz=UTC)
                        < datetime.now(tz=UTC).replace(hour=8, minute=30, second=0)
                    )
                    else spec.prices["discount"]
                )
            ),
            prices={
                "standard": ModelPrice(
                    # （北京时间 08:30-00:30）(UTC 00:30~16:30)
                    currency="CNY",
                    modal_text=ModalityPrice(
                        input_per_token=Decimal(2) / 1_000_000,
                        cached_input_per_token=Decimal(0.5) / 1_000_000,
                        output_per_token=Decimal(8) / 1_000_000,
                    ),
                ),
                "discount": ModelPrice(
                    # （北京时间 00:30-08:30）(UTC 16:30~00:30)
                    modal_text=ModalityPrice(
                        input_per_token=Decimal(1) / 1_000_000,
                        cached_input_per_token=Decimal(0.25) / 1_000_000,
                        output_per_token=Decimal(4) / 1_000_000,
                    )
                ),
            },
            capabilities={
                ModelCap.STREAMING,
                ModelCap.FUNCTION_CALL,
                ModelCap.STRUCTURED_OUTPUT,
            },
            rate_limits={},
            knowledge_cutoff=datetime.strptime("2024-06-01", "%Y-%m-%d").replace(tzinfo=UTC),  # unofficial
        ),
        # deepseek R1 (deepseek-reasoner)
        ModelNameEnum.DEEPSEEK_R1.value: ModelSpec(
            context_window_tokens_limit=64_000,
            cot_token_limit=32_000,
            input_tokens_limit=None,
            output_tokens_limit=8_192,
            input_modalities={"text"},
            output_modalities={"text"},
            default_price=(
                lambda spec: (
                    spec.prices["standard"]
                    if (
                        datetime.now(tz=UTC).replace(hour=0, minute=30, second=0)
                        < datetime.now(tz=UTC)
                        < datetime.now(tz=UTC).replace(hour=8, minute=30, second=0)
                    )
                    else spec.prices["discount"]
                )
            ),
            prices={
                "standard": ModelPrice(
                    # （北京时间 08:30-00:30）(UTC 00:30~16:30)
                    currency="CNY",
                    modal_text=ModalityPrice(
                        input_per_token=Decimal(4) / 1_000_000,
                        cached_input_per_token=Decimal(1) / 1_000_000,
                        output_per_token=Decimal(16) / 1_000_000,
                    ),
                ),
                "discount": ModelPrice(
                    # （北京时间 00:30-08:30）(UTC 16:30~00:30)
                    modal_text=ModalityPrice(
                        input_per_token=Decimal(1) / 1_000_000,
                        cached_input_per_token=Decimal(0.25) / 1_000_000,
                        output_per_token=Decimal(4) / 1_000_000,
                    )
                ),
            },
            capabilities={
                ModelCap.COT,
                ModelCap.STREAMING,
                ModelCap.FUNCTION_CALL,
                ModelCap.STRUCTURED_OUTPUT,
            },
            rate_limits={},
            knowledge_cutoff=datetime.strptime("2024-06-01", "%Y-%m-%d").replace(tzinfo=UTC),  # unofficial
        ),
    },
}
