from typing import Literal, Self

from pycommon.utils.confutils import get_str_env
from pycommon.utils.lang import DictMixin
from pydantic import BaseModel

from .base import ModelSpec, model_specs
from .consts import NAME_SEP
from .enums import ProviderEnum, ModelNameEnum


# ----- model settings -----


class ModelSetting(BaseModel, DictMixin):
    """Setting to specify a LLM."""

    provider: ProviderEnum | Literal["default"]
    mod_name: ModelNameEnum | Literal["default"]

    # optional attributes depends on provider & model
    api_base: str | None = None
    api_key: str | None = None

    # tokens
    max_input_token: int = 4096  # used to truncate prompt messages.
    max_output_token: int = 1024  # limit LLM output generation

    # sampling
    temperature: float | None = None  # 0~2. Higher(0.9), random. Lower(0.1), focus.
    top_p: float | None = None  # instruct model to consider result top p tokens. (e.g. 0.1 means top 10% possibility)

    def __str__(self):
        return f"<{self.__class__.__name__} {self.provider}{NAME_SEP}{self.mod_name}>"

    @property
    def spec(self) -> ModelSpec:
        return model_specs[self.provider][self.mod_name]

    def copy(self)-> Self:


class OllamaSetting(ModelSetting):
    pass


# ----- completion parameters -----


class ChatParam(BaseModel, DictMixin):
    """Parameter to run a LLM completion."""

    streaming: bool = False
    timeout: int = 30  # in seconds

    class Config:
        arbitrary_types_allowed = True


class OpenAIParam(ChatParam):
    pass


class OllamaParam(ChatParam):
    pass


class GeminiParam(ChatParam):
    pass


class DeepSeekParam(ChatParam):
    pass


def get_pre_defined_settings() -> dict[str, ModelSetting]:
    """get the pre-defined LLM settings.

    :returns dict of ModelNameEnum (str): ModelSetting (obj)
    """
    settings: dict[str, ModelSetting] = {}
    kwargs = {"max_input_token": 8092, "max_output_token": 8092, "temperature": 1.0, "top_p": 0.3}

    if get_str_env("XAI_API_KEY"):
        # xAI (twitter) grok-2-latest
        setting = ModelSetting(provider=ProviderEnum.XAI, mod_name=ModelNameEnum.GROK_V2, **kwargs)
        settings[ModelNameEnum.GROK_V2] = setting
    if get_str_env("GEMINI_API_KEY"):
        # Google gemini-2.0-flash-lite
        setting = ModelSetting(provider=ProviderEnum.GEMINI, mod_name=ModelNameEnum.GEMINI_20_FLASH_LITE, **kwargs)
        settings[ModelNameEnum.GEMINI_20_FLASH_LITE] = setting
    if get_str_env("DEEPSEEK_API_KEY"):
        # deepseek v3 (deepseek-chat)
        setting = ModelSetting(provider=ProviderEnum.DEEPSEEK, mod_name=ModelNameEnum.DEEPSEEK_V3, **kwargs)
        settings[ModelNameEnum.DEEPSEEK_V3] = setting
        # deepseek R1 (deepseek-reasoner)
        setting = ModelSetting(provider=ProviderEnum.DEEPSEEK, mod_name=ModelNameEnum.DEEPSEEK_R1, **kwargs)
        settings[ModelNameEnum.DEEPSEEK_R1] = setting
    if get_str_env("OPENAI_API_KEY"):
        # Openai gpt-4o-mini
        setting = ModelSetting(provider=ProviderEnum.OPENAI, mod_name=ModelNameEnum.GPT_4o_MINI, **kwargs)
        settings[ModelNameEnum.GPT_4o_MINI] = setting

    # kwargs['api_base'] = "http://localhost:11434"

    # Ollama "llama3.2:3b"
    setting = ModelSetting(provider=ProviderEnum.OLLAMA, mod_name=ModelNameEnum.OLLAMA_LLAMA_32_3B, **kwargs)
    settings[ModelNameEnum.OLLAMA_LLAMA_32_3B] = setting

    # Ollama "llama3.2-vision"
    setting = ModelSetting(provider=ProviderEnum.OLLAMA, mod_name=ModelNameEnum.OLLAMA_LLAMA_32_VISION, **kwargs)
    settings[ModelNameEnum.OLLAMA_LLAMA_32_VISION] = setting

    return settings
