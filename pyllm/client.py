import json
import re
from abc import abstractmethod, ABC
from json import JSONDecoder
from logging import getLogger
from typing import Any, Type, Optional

import litellm  # type: ignore
from asgiref.sync import sync_to_async
from pydantic import BaseModel

from pycommon.observation import (
    Notification,
    PythonLogObserver,
    ObserverManager,
    Observable,
)
from pycommon.utils.datetimeutils import timeit
from pycommon.utils.terminal import tip
from .observation import LlmCostTracker
from .params import ModelSetting, ChatParam
from .prompts import ChatPrompts

logger = getLogger(__name__)


class LlmClient(BaseModel, ABC):
    """Abstracted base class of wrapper for all LLM clients.

    Attributes:
        setting (params.ModelSetting): Setting to set up a LLM client.
    """

    setting: ModelSetting
    ob: Observable = ObserverManager(LlmCostTracker(), PythonLogObserver())

    class Config:
        arbitrary_types_allowed = True

    @timeit(tip)
    @abstractmethod
    def completion(self, prompts: Chat<PERSON>rom<PERSON>, params: Chat<PERSON>aram, **kwargs: Any) -> str:
        """completely finish a turn of conversation with LLM.

        Returns (str):
             A string of the answer text from LLM
        """

    @timeit(tip)
    async def async_completion(self, prompts: ChatPrompts, params: ChatParam, **kwargs: Any) -> str:
        return await sync_to_async(self.completion)(prompts, params, **kwargs)

    @staticmethod
    def try_parse_json(
        s: str | bytes | bytearray, cls: Optional[Type[JSONDecoder]] = None, *args: Any, **kwargs: Any
    ) -> Any:
        try:
            data = json.loads(s, cls=cls, *args, **kwargs)
        except json.decoder.JSONDecodeError as e:
            m = re.match(r"[^\{\[]*(?P<jsonstr>[\{\[].*[\}\]]).*", s, flags=re.I | re.S)
            if m:
                s = m["jsonstr"]
                data = json.loads(s, cls=cls, *args, **kwargs)
            else:
                raise e
        return data


class LlmGeneralClient(LlmClient):
    """An client implemented with `litellm`"""

    @property
    def name_observable(self) -> str:
        return "litellm_cli"

    def completion(self, prompts: ChatPrompts, params: ChatParam, **kwargs: Any) -> str | litellm.CustomStreamWrapper:
        # assert prompts.inquery, "No user prompt."

        message_list = prompts.to_list()
        self.ob.observe(prompts.to_str(), data="debug", subjects=["log"])
        self.ob.observe(Notification(message="prompts", data=prompts).to("state"))
        self.ob.observe(Notification(message="setting", data=self.setting).to("state"))
        self.ob.observe(Notification(message="params", data=params).to("state"))
        resp = litellm.completion(
            model=f"{self.setting.provider.value}/{self.setting.mod_name.value}",
            max_tokens=self.setting.max_output_token,
            messages=message_list,
            response_format=({"type": "json_object"} if prompts.output_format == "json" else None),
            **params.to_dict(),
            **kwargs,
        )
        # self.ob.observe(Notification(message="completion", data=resp).to("log", "cost"))
        return self._handle_output(resp)

    def _handle_output(
        self, resp: litellm.ModelResponse | litellm.CustomStreamWrapper
    ) -> str | litellm.CustomStreamWrapper:
        if isinstance(resp, litellm.CustomStreamWrapper):
            return resp

        self.ob.observe(message="answer", data=resp.json(), subjects=("log", "cost"))
        s = resp.choices[0].message.content
        s = re.sub(r"^```\w*\n", "", s)
        s = re.sub(r"```$", "", s)
        return s

    def _litellm_log_callback(
        self,
        kwargs,  # kwargs to completion
        completion_response,  # response from completion
        start_time,
        end_time,  # start/end time
    ):
        pass


class OpenAIClient(LlmGeneralClient):
    pass


class OllamaClient(LlmGeneralClient):
    pass


class GeminiClient(LlmGeneralClient):
    pass


class DeepSeekClient(LlmGeneralClient):
    pass
