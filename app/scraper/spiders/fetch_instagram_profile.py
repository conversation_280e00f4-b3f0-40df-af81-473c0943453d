#!/usr/bin/env python3
import asyncio
import json
import random
import sys
import time
import traceback
import uuid
from datetime import datetime
from datetime import timed<PERSON><PERSON>
from logging import getLogger
from pathlib import Path
from typing import Any, Callable
from urllib.parse import urlencode

import aiohttp
import requests

from common.services import slack
from common.utils.confutil import get_str_env
from common.utils.datetimeutils import UTC, timeit
from scraper.base import CacheStore
from scraper.login_manager import HeaderManager, SessionManager
from scraper.models import InstagramPost
from scraper.redis_cache import RedisCache

sys.path.insert(0, str(Path(__file__).absolute().parent.parent.parent.parent))
from django_init import init_django

init_django()

logger = getLogger(__name__)
cache: CacheStore = RedisCache()
IG_PROFILE_CACHE_EXPIRES = 3600 * 24


async def async_cache_get_or_set(
    key: str, func: Callable, *args: Any, **kwargs: Any
) -> str:
    value = await cache.async_get(key)
    if not value:
        if asyncio.iscoroutinefunction(func):
            value = await func(*args, **kwargs)
        else:
            value = func(*args, **kwargs)
        if value:
            await cache.async_set(key, value, expires=IG_PROFILE_CACHE_EXPIRES)
    else:
        logger.debug(f"ig api cache hit - {key}")
    return value


pigeonSessionIdLifetime = 1200000


def _get_user_device_info() -> dict:
    # file_path = (
    #     Path(__file__).parent.parent / "ig-api-headers" / "instagram-headers.3.json"
    # )
    # with open(file_path, "r") as f:
    #     return json.load(f)
    # cookie
    session = SessionManager.get_instance()
    mgr_name = HeaderManager.get_manager_name()
    platform = "instagram"
    data = session.current_set_data(platform)
    headers = data[mgr_name]
    logger.debug(
        f"loaded {platform} {mgr_name} (set={session.current_set(platform)}) - {headers}"
    )
    return headers


def _generate_bandwidth_headers() -> dict:
    speed_kbps = random.uniform(15000, 30000)
    # 10MB to 30MB
    total_bytes = random.randint(10000000, 30000000)
    # Calculate a realistic time
    total_time_ms = int(total_bytes * 8 / speed_kbps)

    return {
        "X-Ig-Bandwidth-Speed-Kbps": f"{speed_kbps:.3f}",
        "X-Ig-Bandwidth-Totalbytes-B": str(total_bytes),
        "X-Ig-Bandwidth-Totaltime-Ms": str(total_time_ms),
    }


def _generateTemporaryGuid(seed: str, device_id: str, lifetime: int) -> str:
    current_time = int(time.time() * 1000)
    unique_string = f"{seed}{device_id}{current_time // lifetime}"
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, unique_string)) + "-0"


def _get_ig_r_rur(device_info: dict) -> str:
    try:
        with open(
            f'/tmp/autogather-ig-header-ig-r-rur-{device_info["user_id"]}', "r"
        ) as f:
            str = f.read()
            logger.debug(f"get ig-r-rur: {str}")
            return str
    except FileNotFoundError:
        return "NHA"
    except Exception as e:
        logger.error(f"Error reading ig-r-rur: {e}")
        return "NHA"


def _set_ig_r_rur(device_info: dict, value: str) -> int:
    logger.debug(f"set ig-r-rur to {value}")
    with open(f'/tmp/autogather-ig-header-ig-r-rur-{device_info["user_id"]}', "w") as f:
        return f.write(value)


def _pigeonSessionId(device_info: dict) -> str:
    device_id = device_info["global_headers"]["X-Ig-Device-Id"]
    return "UFS-" + _generateTemporaryGuid(
        "pigeonSessionId", device_id, pigeonSessionIdLifetime
    )


async def async_fetch_user_info(user_id: str, use_cache: bool = True) -> dict:
    url = f"https://i.instagram.com/api/v1/users/{user_id}/info_stream/"

    async def __invoke() -> str:
        device_info = _get_user_device_info()
        form_data = {
            "entry_point": "profile",
            "from_module": "self_profile",
            "_uuid": uuid.uuid4(),
        }

        form_data_str = urlencode(form_data)
        global_headers: dict = device_info["global_headers"]
        static_headers: dict = device_info["fetch_user_info"]["headers"]

        headers = {
            **global_headers,
            **static_headers,
            **_generate_bandwidth_headers(),
            "X-Pigeon-Session-Id": _pigeonSessionId(device_info),
            "X-Pigeon-Rawclienttime": f"{time.time():.3f}",
            "X-Ig-Nav-Chain": f"MainFeedFragment:feed_timeline:1:cold_start:1737019396.339:::1737019431.478,UserDetailFragment:profile:6:suggested_users:1737019448.694:::{time.time():.3f}",
            "Ig-U-Rur": _get_ig_r_rur(device_info),
            "Content-Length": f"{len(form_data_str)}",
        }
        response = requests.post(url, headers=headers, data=urlencode(form_data))
        response.raise_for_status()
        _set_ig_r_rur(device_info, response.headers["ig-set-ig-u-rur"])
        return json.dumps(
            {
                "text": response.text.strip(),
                "headers": {"ig-set-ig-u-rur": response.headers["ig-set-ig-u-rur"]},
            }
        )

    try:
        cache_key = f"ig:fetch_user_info:{user_id}"
        if use_cache:
            s = await async_cache_get_or_set(cache_key, __invoke)
        else:
            s = await __invoke()
        s_json = json.loads(s)
        json_strs = s_json.get("text", "").split("\n")
        # assert len(json_strs) == 2

        try:
            # short_profile = json.loads(json_strs[0])
            # long_profile = json.loads(json_strs[1])

            # should be profile, in case of only 1, we use last
            obj = json.loads(json_strs[-1])
            if obj and "user" in obj:
                return obj["user"] or {}
        except json.JSONDecodeError as e:
            logger.error(
                {"error": "JSON Parse Error", "rawResponse": s, "detail": str(e)}
            )
    except requests.exceptions.RequestException as e:
        logger.error({"error": f"Request Error: {str(e)}"})
        handle_error(e)
    return {}


def instagram_id_to_shortcode(media_id: int) -> str:
    alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"
    shortcode = ""

    while media_id > 0:
        remainder = media_id % 64
        media_id //= 64
        shortcode = alphabet[remainder] + shortcode

    return shortcode


async def async_get_profile_posts(
    user_id: str | int, use_cache: bool = True, shall_fetch_hidden_count: bool = False
) -> list[dict[str, Any]]:
    if not user_id:
        return []

    api_url = f"https://i.instagram.com/api/v1/feed/user/{user_id}/"
    params = {
        "exclude_comment": "false",
        "should_delay_media_metadata_fetch": "false",
        # "max_id": "",
    }

    async def __invoke() -> str:
        device_info = _get_user_device_info()
        global_headers: dict = device_info["global_headers"]
        static_headers: dict = device_info["fetch_user_info"]["headers"]
        headers = {
            **global_headers,
            **static_headers,
            **_generate_bandwidth_headers(),
            "X-Pigeon-Session-Id": _pigeonSessionId(device_info),
            "X-Pigeon-Rawclienttime": f"{time.time():.3f}",
            "X-Ig-Nav-Chain": f"MainFeedFragment:feed_timeline:1:cold_start:1736926439.904:::{time.time():.3f}::",
            "Ig-U-Rur": _get_ig_r_rur(device_info),
        }

        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout, trust_env=True) as session:
            try:
                async with session.get(api_url, headers=headers, params=params) as resp:
                    resp_text = await resp.text()
                    _set_ig_r_rur(device_info, resp.headers["ig-set-ig-u-rur"])
                    return json.dumps(
                        {
                            "text": resp_text.strip(),
                            "headers": {
                                "ig-set-ig-u-rur": resp.headers["ig-set-ig-u-rur"]
                            },
                        }
                    )
            except asyncio.TimeoutError:
                logger.error(f"Timeout instagram(id={user_id}) API posts.")
                return "{}"

    try:
        cache_key = f"ig:get_profile_posts:{user_id}"
        if use_cache:
            s = await async_cache_get_or_set(cache_key, __invoke)
        else:
            s = await __invoke()
        s_json = json.loads(s)

        data = json.loads(s_json.get("text", ""))
        handle_error(data)

        # next_max_id = data.get("next_max_id", "")

        posts = []
        for post in data.get("items", []):
            post_id = post.get("id")
            # We don't have a direct "detail_url" in p,
            # so we build a best-guess URL from the pk
            post_url = f"https://www.instagram.com/p/{instagram_id_to_shortcode(post.get('pk'))}/"
            like_and_view_counts_disabled = bool(
                post.get("like_and_view_counts_disabled", False)
            )
            like_count = post.get("like_count") or -1
            play_count = post.get("play_count") or -1

            if like_and_view_counts_disabled:
                if shall_fetch_hidden_count:
                    logger.info(
                        f"try to get post likers due to it is hidden (got {like_count}). {post_url}"
                    )
                    d = await _async_get_posts_likes_plays_3rd_party(
                        post_id, post_url, use_cache=use_cache
                    )
                    like_count = max(like_count, d["like_count"])
                    play_count = max(play_count, d["play_count"])
                else:
                    like_count = -1
                    play_count = -1

            thumbnail = {}
            if "image_versions2" in post:
                candidates = post["image_versions2"].get("candidates", [])
                if candidates:
                    best_thumb = candidates[
                        0
                    ]  # Usually the first one is the best quality
                    thumbnail = {
                        "height": best_thumb.get("height"),
                        "width": best_thumb.get("width"),
                        "url": best_thumb.get("url"),
                    }

            posts.append(
                {
                    "id": post.get("id"),
                    "pk": post.get("pk"),
                    "url": post_url,
                    "caption": (post.get("caption") or {}).get("text", ""),
                    "taken_at": post.get("taken_at"),
                    "like_count": like_count if like_count >= 0 else None,
                    "reshare_count": post.get("reshare_count") or 0,
                    "play_count": play_count if play_count >= 0 else None,
                    "comment_count": post.get("comment_count") or 0,
                    "thumbnail": thumbnail,
                }
            )
        return posts
    except requests.RequestException as e:
        logger.error(f"Error fetching profile posts: {e}")
        handle_error(e)
        return []


async def _async_get_posts_likes_plays_3rd_party(
    post_id: str, post_url: str, use_cache: bool = True
) -> dict:
    # get liked users by instagram API
    likers = await async_get_post_likers(post_id, use_cache=use_cache)
    # likers = {}  # TODO: temporary disabled due to always 404 on dev.
    like_user_count = max(len(likers.get("users", [])), likers.get("user_count", -1))
    tikhub_count = -1
    if like_user_count == 99 or not likers:
        logger.info(
            f"try tikhub for post likers due to instagram API got empty or 99 (max). {post_url}"
        )
        # instagram API limit returns max 99 users.
        # try tikhub api
        likers_tikhub = await async_get_post_likers_by_tikhub(
            post_url, use_cache=use_cache
        )
        tikhub_count = likers_tikhub.get("count") or -1
    like_count = max(like_user_count, tikhub_count)
    # comment following to enable real play count (but only for tihub api data)
    play_count = likers.get("play_count") or -1
    return {"like_count": like_count, "play_count": play_count, "users": likers}


def _get_nav_chain(nav_chain: list[str]) -> list[str]:
    n = len(nav_chain) - 1
    if n <= 0:
        return []
    base_dt = datetime.now(tz=UTC) - timedelta(seconds=2)
    dt_list = [base_dt]
    for i in range(n - 1):
        dt_list.insert(
            0, dt_list[-i - 1] - timedelta(seconds=random.randrange(10, 300))
        )
    rc = []
    for i in range(n):
        rc.append(nav_chain[i] % dt_list[i].timestamp())
    return rc


@timeit(logger.warning)
async def async_get_post_likers_by_tikhub(
    post_url: str, use_cache: bool = True
) -> dict[str, Any]:
    api_key = get_str_env("TIKHUB_API_KEY")

    async def __invoke() -> str:
        headers = {
            "Authorization": f"Bearer {api_key}",
        }
        timeout = aiohttp.ClientTimeout(
            total=10, connect=10, sock_connect=10, sock_read=15
        )
        url = f"https://api.tikhub.io/api/v1/instagram/web_app/fetch_post_likes_by_url?url={post_url}"
        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.get(url, headers=headers) as response:
                    return await response.text()
            except asyncio.TimeoutError:
                logger.error(
                    f"Timeout tikhub API for fetch instagram post likers. {post_url}"
                )
                return ""

    cache_key = f"ig:get_profile_posts:{post_url}"
    if use_cache:
        s = await async_cache_get_or_set(cache_key, __invoke)
    else:
        s = await __invoke()
    resp_json = json.loads(s)

    status = resp_json.get("code")
    if status == 200:
        tikhub_data = resp_json.get("data")
        status = tikhub_data.get("status")
        data = tikhub_data.get("data")
        if status and data:
            if "items" in data:
                return data
            else:
                logger.warning(f"invalid resp Tikhub likers API. {data}")
        else:
            logger.error(f"No data from TikHub likers API. {tikhub_data}")
    else:
        logger.error(f"fail TikHub likers API: {resp_json}")

    return {}


async def async_get_post_likers(
    post_id: str | int, use_cache: bool = True
) -> dict[str, Any]:
    # if got 99 'users' in result, may need fetch next page. but this api has no pagination

    api_url = f"https://i.instagram.com/api/v1/media/{post_id}/likers/"
    params: dict[str, str] = {}

    async def __invoke() -> str:
        device_info = _get_user_device_info()
        global_headers: dict = device_info["global_headers"]
        static_headers: dict = device_info["get_post_likers"]["headers"]

        nav_chain = _get_nav_chain(
            [
                "ExploreFragment:explore_popular:2:main_search:%s::",
                "SingleSearchTypeaheadTabFragment:search_typeahead:3:button:%s::",
                "UserDetailFragment:profile:4:search_result:%s::",
                "ProfileMediaTabFragment:profile:5:button:%s::",
                "ContextualFeedFragment:feed_contextual:6:button:%s::",
            ]
        )
        headers = {
            **global_headers,
            **static_headers,
            **_generate_bandwidth_headers(),
            "X-Pigeon-Session-Id": _pigeonSessionId(device_info),
            "X-Pigeon-Rawclienttime": f"{time.time():.3f}",
            "X-Ig-Nav-Chain": ",".join(nav_chain),
            "Ig-U-Rur": _get_ig_r_rur(device_info),
        }

        response = requests.get(api_url, headers=headers, params=params)
        response.raise_for_status()
        _set_ig_r_rur(device_info, response.headers["ig-set-ig-u-rur"])
        return json.dumps(
            {
                "text": response.text.strip(),
                "headers": {"ig-set-ig-u-rur": response.headers["ig-set-ig-u-rur"]},
            }
        )

    try:
        cache_key = f"ig:async_get_post_likers:{post_id}"
        if use_cache:
            s = await async_cache_get_or_set(cache_key, __invoke)
        else:
            s = await __invoke()
        s_json = json.loads(s)

        data = json.loads(s_json.get("text", ""))
        handle_error(data)

        return {
            "user_count": data.get("user_count"),
            "play_count": data.get("play_count"),
            "users": data.get("users", []),
        }
    except requests.RequestException as e:
        logger.error(f"Error fetching profile posts: {e}")
        return {}


async def async_fetch_userid_by_username(  # noqa: C901
    username: str, use_cache: bool = True
) -> str:
    url = "https://i.instagram.com/api/v1/fbsearch/account_serp/"
    params = {
        "search_surface": "user_serp",
        "timezone_offset": "28800",
        "count": "30",
        "query": username,
    }

    async def __invoke() -> str:
        device_info = _get_user_device_info()
        global_headers: dict = device_info["global_headers"]
        static_headers: dict = device_info["fetch_user_info"]["headers"]
        headers = {
            **global_headers,
            **static_headers,
            **_generate_bandwidth_headers(),
            "X-Pigeon-Session-Id": _pigeonSessionId(device_info),
            "X-Pigeon-Rawclienttime": f"{time.time():.3f}",
            "X-Ig-Nav-Chain": f"ExploreFragment:explore_popular:6:main_search:**********.523:::**********.558,SingleSearchTypeaheadTabFragment:search_typeahead:15:button:**********.663:::{time.time():.3f}",
            "Ig-U-Rur": _get_ig_r_rur(device_info),
        }

        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        _set_ig_r_rur(device_info, response.headers["ig-set-ig-u-rur"])
        return json.dumps(
            {
                "text": response.text.strip(),
                "headers": {"ig-set-ig-u-rur": response.headers["ig-set-ig-u-rur"]},
            }
        )

    try:
        cache_key = f"ig:fetch_userid_by_username:{username}"
        if use_cache:
            s = await async_cache_get_or_set(cache_key, __invoke)
        else:
            s = await __invoke()
        s_json = json.loads(s)
        json_objects = s_json.get("text", "").split("\n")

        found_user = None
        for i, json_str in enumerate(json_objects):
            try:
                json_data = json.loads(json_str)
                handle_error(json_data)
                # Iterate through all users to find exact username match
                users = json_data.get("users", [])
                for user in users:
                    if user.get("username") == username:
                        found_user = user
                        break
            except json.JSONDecodeError as e:
                logger.debug(f"Error decoding JSON object {i}: {e}")

            if found_user:
                return found_user.get("id")
        return ""

    except requests.exceptions.RequestException as e:
        logger.error(f"error: {e}")
        handle_error(e)
        return ""


async def async_fetch_profile(
    username: str, use_cache: bool = True, with_posts: bool = False
) -> dict:
    """crawl instagram profile by fetch internal API"""
    if get_str_env("SPIDER_INSTAGRAM_PROFILE_BY", "tikhub") == "tikhub":
        return await async_fetch_profile_by_tikhub(
            username, use_cache=use_cache, with_posts=with_posts
        )
    else:
        return await async_fetch_profile_by_self(
            username, use_cache=use_cache, with_posts=with_posts
        )


async def async_fetch_profile_by_self(
    username: str, use_cache: bool = True, with_posts: bool = False
) -> dict:
    user_id = await async_fetch_userid_by_username(username, use_cache=use_cache)
    profile = {}
    if user_id:
        p = await async_fetch_user_info(user_id, use_cache=use_cache)
        if p:
            profile = {
                "id": p.get("id") or p.get("pk_id") or str(p.get("pk") or ""),
                "platform": "instagram",
                "handle": username,
                "full_name": p.get("full_name") or "",
                "followers": p.get("follower_count") or 0,
                "following": p.get("following_count") or 0,
                "posts_count": p.get("media_count") or 0,
                "biography": p.get("biography"),
                "home_page": f"https://www.instagram.com/{username}",
                # TODO(ruiwang): consolidate url and home_page and make return value structured
                "url": f"https://www.instagram.com/{username}",
                "avatar": p.get("profile_pic_url") or "",
                "html": "",
                "email": p.get("public_email") or "",
            }
            if with_posts:
                # TODO: shall we move the following in engagement/instagram ?
                posts = await async_get_profile_posts(user_id, use_cache=use_cache)
                profile["posts"] = posts

    return profile


async def async_fetch_profile_by_tikhub(  # noqa: C901
    username: str, use_cache: bool = True, with_posts: bool = False
) -> dict:
    """crawl instagram profile by tikhub"""

    api_key = get_str_env("TIKHUB_API_KEY")

    if True:
        url = f"https://api.tikhub.io/api/v1/instagram/web_app/fetch_user_info_by_username_v2?username={username}"
    else:
        # the API has no `followers`
        url = f"https://api.tikhub.io/api/v1/instagram/web_app/fetch_user_info_by_username?username={username}"

    headers = {
        "Authorization": f"Bearer {api_key}",
    }
    timeout = aiohttp.ClientTimeout(total=15, connect=10, sock_connect=10, sock_read=15)

    async def __invoke() -> str:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.get(url, headers=headers) as response:
                    resp_json = await response.json()
            except asyncio.TimeoutError:
                logger.error(f"Timeout tikhub API for {username}.")
                return ""

        status = resp_json.get("code")
        if status == 200:
            data = resp_json.get("data")
            if data:
                return json.dumps(data)
            else:
                logger.error(f"No data from TikHub instagram profile API. {resp_json}")
        else:
            logger.error(f"fail TikHub instagram profile API: {resp_json}")

        return ""

    try:
        cache_key = f"ig:fetch_profile_tikhub:{username}"
        if use_cache:
            s = await async_cache_get_or_set(cache_key, __invoke)
        else:
            s = await __invoke()
        if not s:
            return {}

        p = json.loads(s)
        if p:
            profile = {
                "id": p.get("id") or p.get("pk_id") or str(p.get("pk") or ""),
                "platform": "instagram",
                "handle": username,
                "full_name": p.get("full_name") or "",
                "followers": p.get("follower_count") or 0,
                "following": p.get("following_count") or 0,
                "posts_count": p.get("media_count") or 0,
                "biography": p.get("biography"),
                "home_page": f"https://www.instagram.com/{username}",
                "url": f"https://www.instagram.com/{username}",
                "avatar": p.get("profile_pic_url") or "",
                "html": "",
                "email": p.get("public_email") or p.get("business_email") or "",
            }
            if with_posts:
                posts = await async_get_profile_posts(p.get("id"), use_cache=use_cache)
                profile["posts"] = posts
            return profile
    except requests.RequestException as e:
        logger.error(f"Error fetching profile: {e}")

    return {}


def handle_error(json_obj_or_err: dict | Exception) -> None:
    # suspected by automated behavior
    # [
    #     {
    #         "message": "challenge_required",
    #         "challenge": {
    #             "url": "https://i.instagram.com/challenge/?next=/api/v1/users/646713657/info_stream/",
    #             "api_path": "/challenge/",
    #             "hide_webview_header": True,
    #             "lock": True,
    #             "logout": False,
    #             "native_flow": True,
    #             "flow_render_type": 0,
    #         },
    #         "status": "fail",
    #     }
    # ]

    stack = traceback.extract_stack(limit=2)[0]
    loc = f"{stack.filename}:{stack.lineno}"

    if isinstance(json_obj_or_err, Exception):
        msg = str(json_obj_or_err)
    elif json_obj_or_err.get("status") == "fail":
        msg = json.dumps(json_obj_or_err, default=str)
    else:
        return
    msg = f"fail {loc} - {msg}"
    slack.send_alert(msg, channel=slack.ENGINEERING_CHANNEL)

    session = SessionManager.get_instance()
    platform = "instagram"
    if session.next(platform):
        logger.warning(
            f"{platform} headers rotated. (set={session.current_set(platform)})"
        )


if __name__ == "__main__":

    async def run() -> None:
        # user = await async_fetch_user_info("58642837633")
        # print(user)

        # nav_chain = _get_nav_chain(
        #     [
        #         "ExploreFragment:explore_popular:2:main_search:%s::",
        #         "SingleSearchTypeaheadTabFragment:search_typeahead:3:button:%s::",
        #         "UserDetailFragment:profile:4:search_result:%s::",
        #         "ProfileMediaTabFragment:profile:5:button:%s::",
        #         "ContextualFeedFragment:feed_contextual:6:button:%s::",
        #     ]
        # )
        # print(nav_chain)

        user_id = await async_fetch_userid_by_username("theglobewanderer")
        print("user_id", user_id)
        posts: list[InstagramPost] = await async_get_profile_posts(user_id)
        for p in posts:
            print(f"likes={p['like_count']}, views={p['play_count']}, {p}")

        # likers = await async_get_post_likers("3576588200963669129_1439628189")
        # print(json.dumps(likers, indent=2, default=str))
        # ids = [int(u["id"]) for u in likers["users"]]
        #
        # print(f"---------- {len(ids)}")
        #
        # max_id = ids[-1]
        # likers2 = await async_get_post_likers(
        #     "3576588200963669129_1439628189", max_id=str(max_id)
        # )
        # print(json.dumps(likers2, indent=2, default=str))
        # ids += [int(u["id"]) for u in likers2["users"]]
        #
        # print(f"---------- {len(ids)}")
        #
        # last_id = 0
        # for _id in ids:
        #     if _id < last_id:
        #         print(f"{_id} < prev id {last_id}")
        #         last_id = _id

        # profile = await async_fetch_profile("beauty.maven")
        # print(json.dumps(profile))

        pass

    asyncio.run(run())

    pass
