# if we import any spider/downloader, make cookies syncing at background

from common.utils.bg_runner import get_bg_runner
from scraper.login_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, Header<PERSON>anager, SessionManager

session_mgr = SessionManager.get_instance()

bg_runner = get_bg_runner()
for mgr in [<PERSON><PERSON>, HeaderManager]:
    mgr.get_instance().on_updated = lambda fpath_list: session_mgr.reset_all()
    bg_runner.add(f"atg-{mgr.get_manager_name()}-mgr", mgr.run_as_bg_daemon)
