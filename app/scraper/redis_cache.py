from abc import abstractmethod
from datetime import datetime
from logging import get<PERSON>ogger
from typing import Self, Optional, Iterable, Any

import aioredis
from asgiref.sync import async_to_sync

from common.utils.asyncioutil import gather_with_error
from common.utils.confutil import get_str_env, get_bool_env
from common.utils.datetimeutils import LOCAL_TZ
from scraper.base import CacheStore

logger = getLogger(__name__)

# TODO (scraper): disable cache
CACHE_DISABLED: bool = get_bool_env("CACHE_DISABLED")


class RedisCache(CacheStore):
    _cli: aioredis.Redis

    def __init__(self, conn_url: str = get_str_env("SPIDER_CACHE_REDIS_URL")) -> None:
        self._cli = async_to_sync(aioredis.create_redis_pool)(conn_url)

    def __del__(self) -> None:
        if self._cli:
            try:
                self._cli.close()
                async_to_sync(self._cli.wait_closed)()
            except Exception:
                pass

    @property
    def default_expires(self) -> int:
        return 3600

    async def async_get(self, key: str) -> str | bytes | None:
        s = await self._cli.get(key)
        if s:
            logger.warning(f"cache hit for {key}")
        return s

    async def async_set(
        self, key: str, value: str | bytes, expires: int | datetime | None = None
    ) -> None:
        if isinstance(expires, datetime):
            delta = expires - datetime.now(tz=LOCAL_TZ)
            if delta.total_seconds() <= 0:
                raise ValueError(f"{expires} is a past time.")
            expires = int(delta.total_seconds())

        return await self._cli.set(key, value, expire=expires)

    async def async_set_if_not_exist(
        self, key: str, value: str | bytes, expires: int | datetime | None = None
    ) -> None:
        if isinstance(expires, datetime):
            delta = expires - datetime.now(tz=LOCAL_TZ)
            if delta.total_seconds() <= 0:
                raise ValueError(f"{expires} is a past time.")
            expires = int(delta.total_seconds())

        if await self._cli.setnx(key, value):
            await self._cli.expire(key, expires)

    async def async_delete(self, *keys: str) -> None:
        return await self._cli.delete(*keys)

    async def async_list(self, prefix: str) -> list:
        return await self._cli.keys(f"{prefix}*")

    async def async_exists(self, keys: Iterable[str]) -> dict[str, bool]:
        results, _ = await gather_with_error(
            *[self._cli.exists(key) for key in keys], split_result_errors=False
        )
        return {key: bool(r) for key, r in zip(keys, results)}


cache: CacheStore = RedisCache()


class CacheMixin:
    __is_loaded_from_cache: bool = False

    @property
    def cache_expires(self) -> int:
        """how many seconds will the cache expires in. default 3600"""
        return cache.default_expires

    @property
    @abstractmethod
    def cache_key(self) -> str:
        """return object's cache key"""

    @classmethod
    @abstractmethod
    def gen_cache_key(cls, *args: str) -> str:
        """generate a cache key for given arguments"""

    @classmethod
    @abstractmethod
    def from_cache_str(cls, s: str | bytes) -> Self:
        """generate object from deserializing cache string"""

    @abstractmethod
    def to_cache_str(self) -> str:
        """serialize object to string for caching"""

    @classmethod
    async def async_cache_get(
        cls, cache_key: str, raise_if_none: bool = False
    ) -> Optional[Self]:
        """load object from cache by key

        :param cache_key:
        :param raise_if_none:
        :return:
        """

        if not cache_key:
            if raise_if_none:
                raise KeyError("cache_key is required")
            return None

        try:
            s = await cache.async_get(cache_key)
            if s:
                obj = cls.from_cache_str(s)
                obj.__is_loaded_from_cache = True
            else:
                obj = None
                if raise_if_none:
                    raise ValueError(f"cache not found '{cache_key}' )")
            return obj
        except (KeyError, ValueError) as e:
            raise
        except Exception as e:
            logger.exception(e)
            return None

    async def async_cache_set(self, overwrite: bool = False) -> Any:
        """save entry to cache by key"""
        try:
            cache_key = self.cache_key
            s = self.to_cache_str()
            if not self.__is_loaded_from_cache:
                if overwrite:
                    await cache.async_set(cache_key, s, self.cache_expires)
                else:
                    await cache.async_set_if_not_exist(cache_key, s, self.cache_expires)
                self.__is_loaded_from_cache = True
            return cache_key
        except Exception as e:
            logger.exception(e)
            return None

    async def async_cache_del(self) -> None:
        """del entry from cache by key"""
        try:
            await cache.async_delete(self.cache_key)
        except Exception as e:
            logger.exception(e)
