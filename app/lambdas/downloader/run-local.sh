#!/usr/bin/env bash
set -e

# TO install lambda simulator, run following
if [ ! -f ~/.aws-lambda-rie/aws-lambda-rie ]; then
    mkdir -p ~/.aws-lambda-rie

    # Detect architecture
    ARCH=$(uname -m)
    if [ "$ARCH" = "arm64" ] || [ "$ARCH" = "aarch64" ]; then
        RIE_ARCH="-arm64"
    elif [ "$ARCH" = "x86_64" ]; then
        RIE_ARCH="" # amd64 no suffix
    else
        echo "Unsupported architecture: $ARCH"
        exit 1
    fi

    echo "Downloading aws-lambda-rie for $ARCH architecture..."
    curl -Lo ~/.aws-lambda-rie/aws-lambda-rie https://github.com/aws/aws-lambda-runtime-interface-emulator/releases/latest/download/aws-lambda-rie$RIE_ARCH
    chmod +x ~/.aws-lambda-rie/aws-lambda-rie
fi

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

PROJECT=download
VENDOR=aws
VER=${1:-"latest-slim"}

IMAGE_PREFIX=471112791150.dkr.ecr.us-east-1.amazonaws.com
HANDLER=aws_lambda_handler


docker run --platform linux/arm64 -d \
    --name ${PROJECT} \
    --rm \
    -v ~/.aws-lambda-rie:/aws-lambda \
    -p 9000:8080 \
    --entrypoint /aws-lambda/aws-lambda-rie \
    ${IMAGE_PREFIX}/onwish-${PROJECT}-${VENDOR}:${VER} \
        /usr/local/bin/python -m awslambdaric main.${HANDLER}

echo "invoking ..."
result=$(curl -s "http://localhost:9000/2015-03-31/functions/function/invocations" \
    -d '{"urls":["https://www.onwish.ai/insights/anticipating-advertising-spending-impacts-from-the-upcoming-us-presidential-election"]}')
echo "-------------"
echo "Result:"
echo "$result" | jq .
read -n 1 -s -r -p "Press any key to exit..."
docker stop ${PROJECT} 2>&1 1>/dev/null
