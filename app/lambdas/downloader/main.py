import traceback
from typing import Any

from asgiref.sync import async_to_sync

from common.utils.strutil import ellipse
from scraper.downloader import (
    StaticLocalDownloader,
    StaticLocalDownloaderConfig,
    PlaywrightDownloader,
    PlaywrightDownloaderConfig,
)


def aws_lambda_handler(event: dict, context: Any) -> Any:
    """
    :param event: payload
    :param context: sample - LambdaContext([aws_request_id=2999d84d-fa60-4335-bdf7-33aac8b2249e,log_group_name=/aws/lambda/Functions,log_stream_name=$LATEST,function_name=test_function,memory_limit_in_mb=3008,function_version=$LATEST,invoked_function_arn=arn:aws:lambda:us-east-1:012345678912:function:test_function,client_context=None,identity=CognitoIdentity([cognito_identity_id=None,cognito_identity_pool_id=None])]))
    :return:
    """
    urls = event.get("urls", [])
    htmls = download(urls)
    return htmls


def gcp_cloud_run_handler() -> Any:
    raise NotImplementedError


def _static_download(urls: list[str]) -> list[str]:
    config = StaticLocalDownloaderConfig(verify_ssl=False)
    downloader = StaticLocalDownloader(config=config)
    results = downloader.download_all(urls)
    return [r.data or f"error: {r.ex}" for r in results]


async def _async_playwright_download(urls: list[str]) -> list[str]:
    config = PlaywrightDownloaderConfig(proxy={"server": "http://127.0.0.1:1087"})
    browser = await PlaywrightDownloaderConfig.async_create_browser(config=config)
    await config.async_set_browser(browser)

    downloader = PlaywrightDownloader(config=config)
    results = await downloader.async_download_all(urls)
    return [
        r.data or f"error: {(lambda:[r.ex, traceback.print_exception(r.ex)])()[0]}"
        for r in results
    ]


# def _playwright_download(urls: list[str]) -> list[str]:
#     config = PlaywrightDownloaderConfig(proxy={"server": "http://127.0.0.1:1087"})
#     # NOTE: this function will not work because of the following line
#     #       (browser hang when await browser.new_page())
#     browser = PlaywrightDownloaderConfig.create_browser(config=config)
#     config.browser = browser
#
#     downloader = PlaywrightDownloader(config=config)
#     results = async_to_sync(downloader.async_download_all)(urls)
#     return [
#         r.data or f"error: {(lambda:[r.ex, traceback.print_exception(r.ex)])()[0]}"
#         for r in results
#     ]


def download(urls: list[str]) -> list[str]:
    assert len(urls) <= 10

    config = StaticLocalDownloaderConfig(verify_ssl=False)
    downloader = StaticLocalDownloader(config=config)
    results = downloader.download_all(urls)

    async def __inner() -> Any:
        err_urls = [r.url for r in results if r.ex or not r.data]
        if err_urls:
            dy_config = PlaywrightDownloaderConfig()
            dy_downloader = PlaywrightDownloader(config=dy_config)
            dy_results = await dy_downloader.async_download_all(err_urls)
            d_fixes = {r.url: r for r in dy_results}

            return [
                d_fixes[r.url].data or str(d_fixes[r.url].ex)
                if r.ex or not r.data
                else r.data
                for r in results
            ]
        else:
            return [r.data or f"error: {r.ex}" for r in results]

    return async_to_sync(__inner)()


if __name__ == "__main__":
    import json
    import logging
    import colorlog

    colorlog.basicConfig(
        level=logging.DEBUG,
        format="%(log_color)s[%(levelname).05s] [%(asctime)s] [%(name)s:%(lineno)d] %(message)s",
    )
    url_list = [
        # "https://www.onwish.ai/insights/anticipating-advertising-spending-impacts-from-the-upcoming-us-presidential-election",
        "https://www.baidu.com"
    ]
    if False:
        result_list = download(url_list)
    else:
        result_list = async_to_sync(_async_playwright_download)(url_list)
        # result_list = _playwright_download(url_list)  # error example

    result_list = [s if s.startswith("error:") else ellipse(s) for s in result_list]
    print(json.dumps(result_list, indent=2))
