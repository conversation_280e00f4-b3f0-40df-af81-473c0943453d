ARG APP_DIR=/app
ARG PROJECT=downloader
ARG IMAGE_PREFIX=471112791150.dkr.ecr.us-east-1.amazonaws.com

FROM ${IMAGE_PREFIX}/onwish-dlbase-aws:latest

ARG APP_DIR
ARG PROJECT
ARG VER

LABEL version=${VER}
LABEL description="web page downloader image for AWS Lambda"
LABEL owner="Onwish Inc."

EXPOSE 80/tcp

RUN mkdir /app
WORKDIR ${APP_DIR}

COPY app/common ${APP_DIR}/common
COPY app/lambdas/${PROJECT}/requirements.txt app/lambdas/${PROJECT}/main.py ${APP_DIR}/
RUN pip install -r requirements.txt && pip install --default-timeout=100 awslambdaric

ENTRYPOINT [ "/usr/local/bin/python", "-m", "awslambdaric" ]

CMD [ "main.aws_lambda_handler" ]


