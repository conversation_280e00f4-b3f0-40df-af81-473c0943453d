#!/usr/bin/env bash
set -e

# Add usage function
usage() {
    echo "Usage: $0 PROJECT [VENDOR] [VERSION] [--retry N] [-p|--push]"
    echo
    echo "Build and push Docker images for onwish lambda projects. Name is onwish-{PROJECT}-{VENDOR}:v{VERSION}"
    echo
    echo "Arguments:"
    echo "  PROJECT       Project name (required, must be a subfolder name without '_' prefix)"
    echo "  VENDOR        Vendor names (default: aws | choices: aws, gcp)"
    echo "                Note: If PROJECT is 'base', VENDOR must be absent"
    echo "    aws         Build the image for AWS environment"
    echo "    gcp         Build the image for Google Cloud Platform environment"
    echo "  VERSION       Version tag (default: current date in YYYY.MM.DD format)"
    echo
    echo "Options:"
    echo "  -h, --help    Show this help message and exit"
    echo "  --retry N     Maximum number of build attempts (default: 20, range: 1-100)"
    echo "  -p, --push    Push the built images to the repository"
}

check_arguments() {
    # Check if PROJECT is provided
    if [ -z "$PROJECT" ]; then
        echo "Error: PROJECT is required"
        usage
        exit 1
    fi

    # Check if PROJECT is a valid subfolder name without '_' prefix
    if [[ ! -d "$SCRIPT_DIR/$PROJECT" || "$PROJECT" == _* ]]; then
        echo "Error: PROJECT must be a valid subfolder name without '_' prefix"
        echo "Available projects:"
        ls -d $SCRIPT_DIR/*/ | grep -v '^_' | xargs -n 1 basename
        exit 1
    fi

    # Check VENDOR based on PROJECT
    if [ "$PROJECT" == "base" ]; then
        if [ -z "$VENDOR" ]; then
            echo "Error: When PROJECT is 'base', VENDOR must be absent"
            usage
            exit 1
        fi
        VENDOR="base"
    else
        if [[ ! "$VENDOR" =~ ^(aws|gcp)$ ]]; then
            echo "Error: VENDOR must be one of 'aws' or 'gcp' for non-base projects"
            usage
            exit 1
        fi
    fi

    # Check if VER is a valid version string
    if [[ ! "$VER" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        echo "Error: VERSION must be in the format vX.X.X (e.g., v1.2.3)"
        usage
        exit 1
    fi
}

# Parse command line options
PUSH_IMAGES=false
MAX_ATTEMPTS=3
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            usage
            exit 0
            ;;
        -p|--push)
            PUSH_IMAGES=true
            shift
            ;;
        --retry)
            if [[ $2 =~ ^[1-9][0-9]?$|^100$ ]]; then
                MAX_ATTEMPTS=$2
                shift 2
            else
                echo "Error: --retry value must be between 1 and 100"
                usage
                exit 1
            fi
            ;;
        *)
            break
            ;;
    esac
done

echo "-- ARM64 only --"

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

PROJECT=$1
VENDOR=${2:-"aws"}
VER=${3:-"v$(date +"%Y.%m.%d")"}

check_arguments

attempt=1

# Update the image name to include the PROJECT
IMAGE_PREFIX=471112791150.dkr.ecr.us-east-1.amazonaws.com
# IMAGE_PREFIX=public.ecr.aws/onwish
if [ "$PROJECT" == "base" ]; then
    IMAGE_NAME="onwish-${PROJECT}"
    CONTEXT_DIR="${SCRIPT_DIR}/../.."
else
    IMAGE_NAME="onwish-${PROJECT}-${VENDOR}"
#    CONTEXT_DIR="${SCRIPT_DIR}"
		CONTEXT_DIR="${SCRIPT_DIR}/../.."

fi

# --platform linux/amd64 \

# Flag to indicate if a termination signal has been received
terminate=0

# Signal handler function
handle_signal() {
    echo "Received termination signal. Stopping the build process..."
    terminate=1
}

# Set up signal handling
trap handle_signal SIGINT SIGTERM

while [ $attempt -le $MAX_ATTEMPTS ] && [ $terminate -eq 0 ]; do
    echo "Attempt $attempt of $MAX_ATTEMPTS"
    if docker buildx build \
        -f ${SCRIPT_DIR}/${PROJECT}/${VENDOR}.Dockerfile \
        --platform linux/arm64 \
        --build-arg VER=${VER} \
        --build-arg PROJECT=${PROJECT} \
        --build-arg IMAGE_PREFIX=${IMAGE_PREFIX} \
        -t ${IMAGE_NAME}:${VER} \
        ${CONTEXT_DIR}; then
        echo "Build successful!"
        break
    else
        if [ $terminate -eq 1 ]; then
            echo "Build process was interrupted."
            exit 1
        fi
        echo "Build failed. Retrying in 30 seconds..."
        sleep 30
        ((attempt++))
    fi
done

if [ $terminate -eq 1 ]; then
    echo "Build process was interrupted."
    exit 1
elif [ $attempt -gt $MAX_ATTEMPTS ]; then
    echo "Build failed after $MAX_ATTEMPTS attempts. Exiting."
    exit 1
fi

# Only proceed with tagging and pushing if build was successful and not interrupted
if [ $terminate -eq 0 ] && [ $attempt -le $MAX_ATTEMPTS ]; then
    docker tag ${IMAGE_NAME}:${VER} ${IMAGE_NAME}:latest
    docker tag ${IMAGE_NAME}:${VER} ${IMAGE_PREFIX}/${IMAGE_NAME}:${VER}
    docker tag ${IMAGE_NAME}:${VER} ${IMAGE_PREFIX}/${IMAGE_NAME}:latest


		# slim image
    echo ""| slim build --http-probe=false --tag ${IMAGE_NAME}:${VER}-slim ${IMAGE_NAME}:${VER}
    docker tag ${IMAGE_NAME}:${VER}-slim ${IMAGE_NAME}:latest-slim
    docker tag ${IMAGE_NAME}:${VER}-slim ${IMAGE_PREFIX}/${IMAGE_NAME}:${VER}-slim
    docker tag ${IMAGE_NAME}:${VER}-slim ${IMAGE_PREFIX}/${IMAGE_NAME}:latest-slim

    if [ "$PUSH_IMAGES" = true ]; then
        aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ${IMAGE_PREFIX}
        docker push ${IMAGE_PREFIX}/${IMAGE_NAME}:${VER}-slim
        docker push ${IMAGE_PREFIX}/${IMAGE_NAME}:latest-slim
        echo "Images pushed to repository."
    else
        echo "Images built successfully. Use -p or --push to push to repository."
    fi
fi
