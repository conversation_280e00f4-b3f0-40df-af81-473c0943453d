ARG IMAGE_PREFIX=

FROM ${IMAGE_PREFIX}/onwish-base:latest

ARG VER

LABEL version=${VER}
LABEL description="downloader base image for Google Cloud Run"
LABEL owner="Onwish Inc."

RUN --mount=target=/var/lib/apt/lists,type=cache,sharing=locked \
    --mount=target=/var/cache/apt,type=cache,sharing=locked \
    rm -f /etc/apt/apt.conf.d/docker-clean \
    && playwright install-deps && playwright install

