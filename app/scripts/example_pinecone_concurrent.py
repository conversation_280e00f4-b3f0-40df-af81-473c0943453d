# type: ignore
import asyncio
import itertools
import json
import random
import sys
import threading
from pathlib import Path
from time import perf_counter

from dotenv import load_dotenv

from common.utils.asyncioutil import gather_with_error, with_concurrency_limit
from common.utils.terminal import debug, warn

# fmt: off
sys.path.insert(0, str(Path(__file__).absolute().parent.parent))

load_dotenv()

from django_init import init_django
init_django()


# import internal packages after path inserted.
from biz.enums import DocTypeEnum
from biz.constants import SP500_COMPANY_TICKER_LIST, RUSSELL_2000_TICKER_LIST, EXTRA_TICKER_LIST
from biz.search import <PERSON>coneHybridSearcher
from biz.search.base import IndexUpsertIDs
from datasvc.models import PressRelease
from datasvc.indexing import get_index
from datasvc.store import get_doc_store

# fmt: on

# all_tickers = get_ticker_list_by_scope(TickerScopeEnum.FULL)
all_tickers = SP500_COMPANY_TICKER_LIST + RUSSELL_2000_TICKER_LIST + EXTRA_TICKER_LIST

doc_store = get_doc_store()

DATA_PATH = Path(__file__).parent / "data"


async def _download():
    docs = [p for p in PressRelease.objects.all()]
    coroutines = [doc_store.aload(p.content) for p in docs]
    _, _ = gather_with_error(
        *with_concurrency_limit(coroutines, limit=10), log_errors=True
    )
    for p in docs:
        with open(DATA_PATH / f"{p.id}", "w") as f:
            f.write(p.content.get_data())


async def _index():
    pass


async def _query():
    concurrency = 100

    async def __inner_run_search(_q: str, _k: int, _f: dict) -> list:
        _doc_type = _f["$and"][0]["doc_type"]
        _ticker = _f["$and"][1]["ticker"]
        _t1 = perf_counter()
        rc = await searcher.query_with_scores(
            query=_q,
            top_k=_k,
            metadata_filter=_f,
        )
        _dur = perf_counter() - _t1
        debug(f"{_doc_type} - {_ticker} - {_dur} s")
        return rc

    tickers = []
    for i in range(concurrency):
        j = random.randint(0, len(all_tickers))
        debug(f"{all_tickers[j]} selected")
        tickers.append(all_tickers[j])

    coroutines = []
    for i in range(concurrency):
        ticker = tickers[i]
        doc_type = random.choice(
            [
                DocTypeEnum.SEC_FILING.value,
                # DocTypeEnum.EARNING_CALL.value,
                # DocTypeEnum.EVENT_TRANSCRIPT.value,
                # DocTypeEnum.CONFERENCE.value,
                # DocTypeEnum.PRESS_RELEASE.value,
            ]
        )
        index = get_index(doc_type)
        searcher = PineconeHybridSearcher(index=index, alpha=0.7)
        filters = {
            "$and": [
                {"doc_type": doc_type},
                {"ticker": ticker},
                {"index_upsert_id": IndexUpsertIDs.MVP31.value},
            ]
        }
        # q = datasdk.company_name_by_ticker(ticker)
        q = "revenue"
        coroutines.append(__inner_run_search(q, 10, filters))

    t1 = perf_counter()
    docs_with_scores, errs = await gather_with_error(*coroutines, log_errors=True)
    dur = perf_counter() - t1
    warn(f"Totally {dur} s")
    return docs_with_scores


async def _query_threading():
    concurrency = 100
    lock = threading.Lock()
    docs_with_scores = []

    def __inner_thread_search(_q: str, _k: int, _f: dict) -> list:
        _doc_type = _f["$and"][0]["doc_type"]
        _ticker = _f["$and"][1]["ticker"]
        _t1 = perf_counter()
        rc = asyncio.run(
            searcher.query_with_scores(
                query=_q,
                top_k=_k,
                metadata_filter=_f,
            )
        )
        _dur = perf_counter() - _t1
        debug(f"{_doc_type} - {_ticker} - {_dur} s")

        with lock:
            docs_with_scores.append(rc)

        return rc

    tickers = []
    for i in range(concurrency):
        j = random.randint(0, len(all_tickers))
        debug(f"{all_tickers[j]} selected")
        tickers.append(all_tickers[j])

    tasks = []
    for i in range(concurrency):
        ticker = tickers[i]
        doc_type = random.choice(
            [
                DocTypeEnum.SEC_FILING.value,
                # DocTypeEnum.EARNING_CALL.value,
                # DocTypeEnum.EVENT_TRANSCRIPT.value,
                # DocTypeEnum.CONFERENCE.value,
                # DocTypeEnum.PRESS_RELEASE.value,
            ]
        )
        index = get_index(doc_type)
        searcher = PineconeHybridSearcher(index=index, alpha=0.7)
        filters = {
            "$and": [
                {"doc_type": doc_type},
                {"ticker": ticker},
                {"index_upsert_id": IndexUpsertIDs.MVP31.value},
            ]
        }
        # q = datasdk.company_name_by_ticker(ticker)
        q = "revenue"
        thread = threading.Thread(target=__inner_thread_search, args=(q, 10, filters))
        tasks.append(thread)

    t1 = perf_counter()

    # docs_with_scores, errs = await gather_with_error(*coroutines, log_errors=True)

    for thread in tasks:
        thread.start()
    for thread in tasks:
        thread.join()

    dur = perf_counter() - t1
    warn(f"Totally {dur} s")
    return docs_with_scores


def main() -> None:
    # docs_with_scores = asyncio.run(_query())
    docs_with_scores = asyncio.run(_query_threading())
    docs_with_scores = list(itertools.chain(*docs_with_scores))
    print(json.dumps(docs_with_scores, indent=2, default=str))


if __name__ == "__main__":
    main()
