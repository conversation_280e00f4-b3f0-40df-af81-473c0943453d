import multiprocessing
import os
import signal
import subprocess
import sys
import threading
import traceback
from datetime import datetime, UTC, timedelta
from multiprocessing.pool import ThreadPool
from pathlib import Path

from dotenv import load_dotenv
from tqdm.auto import tqdm

load_dotenv()

# fmt: off
sys.path.insert(0, str(Path(__file__).absolute().parent.parent))

# import internal packages after path inserted.
from biz.enums import DocTypeEnum, TickerScopeEnum
from common.utils.terminal import warn, tip, error
from common.utils.datetimeutils import LOCAL_TZ

# fmt: on

os.environ["DEBUG"] = "true"
os.environ["VERBOSE"] = "0"
os.environ["LOG_LEVEL"] = "INFO"

overwrite_store = False
overwrite_index = False
start_from_min_ts = True

separate_logs = False

scope = TickerScopeEnum.FULL.value
run_types = [
    # "update",
    "fetch",
    "parse",
]

min_ts_5y = (datetime.now(tz=UTC) - timedelta(days=365 * 5 + 1)).strftime("%Y-%m-%d")
min_ts_2y = (datetime.now(tz=UTC) - timedelta(days=365 * 2 + 1)).strftime("%Y-%m-%d")
min_ts_1y = (datetime.now(tz=UTC) - timedelta(days=365 * 1 + 1)).strftime("%Y-%m-%d")
min_ts_6m = (datetime.now(tz=UTC) - timedelta(days=30 * 6 + 3)).strftime("%Y-%m-%d")
min_ts_3m = (datetime.now(tz=UTC) - timedelta(days=30 * 3 + 2)).strftime("%Y-%m-%d")
min_ts_1w = (datetime.now(tz=UTC) - timedelta(days=7 * 1)).strftime("%Y-%m-%d")

doc_type_to_min_ts = {
    DocTypeEnum.SEC_FILING.value: min_ts_3m,
    DocTypeEnum.EARNING_CALL.value: min_ts_3m,
    DocTypeEnum.EVENT_TRANSCRIPT.value: min_ts_6m,
    DocTypeEnum.CONFERENCE.value: min_ts_6m,
    DocTypeEnum.PRESS_RELEASE.value: min_ts_1w,
    # DocTypeEnum.NEWS.value: min_ts_3m,
    # DocTypeEnum.YOUTUBE.value: min_ts_2y,
    # DocTypeEnum.TWITTER.value: min_ts_3m,
}

log_prefix = Path(__file__).parent.parent.parent / "logs" / "build_index"
log_prefix.mkdir(parents=True, exist_ok=True)
log_prefix = log_prefix / datetime.now(tz=LOCAL_TZ).strftime("%Y-%m-%d_%H%M%S")
full_log_path = str(log_prefix) + ".log"

# concurrency control variables
stop_flag = multiprocessing.Value("i", 0)
log_lock = multiprocessing.RLock()


print(
    f"building indexing {run_types} for {list(doc_type_to_min_ts.keys())} on '{scope}' tickers."
)


def signal_handler(signal, frame):  # type: ignore
    stop_flag.value = 1
    warn("\nCTRL-C! Terminating ...")


signal.signal(signal.SIGINT, signal_handler)


def run_cmd(cmd_and_contexts: tuple) -> tuple[int, str, tuple]:
    pid = ""
    output = ""
    cmd, doc_type, run_type, min_ts = cmd_and_contexts

    log_path = log_prefix / f"{scope}.log"
    try:
        # collect log & show pid
        cmd += " 2>&1; echo $$ 1>&2"
        # if required, use popen for better control.
        p = subprocess.run(cmd, shell=True, check=True, capture_output=True)
        pid = "PROC-" + p.stderr.decode().strip()
        output = p.stdout.decode().strip()
        rc = p.returncode
    except Exception as e:
        rc = -1
        output += (
            f"\n[{datetime.now()}] E [{threading.current_thread().name}] "
            + "".join(traceback.format_exception(e))
        )

    try:
        with log_lock:
            fpath: str = str(log_path) if separate_logs else full_log_path
            with open(fpath, "a") as f:
                f.write(f"\n-----  {scope} {run_type} {doc_type} to {min_ts} -----\n")
                f.write(output)

    except Exception as e:
        error(str(e))
    # debug(f"-----  DONE {ticker} {run_type} {data_type} to {min_ts} -----")
    return rc, pid, cmd_and_contexts


def gen_cmds() -> list[tuple]:
    cmd_and_contexts: list[tuple] = []
    for run_type in run_types:
        for doc_type, min_ts in doc_type_to_min_ts.items():
            cmd = [
                "python",
                "app/manage.py",
                "index_v2",
                doc_type,
                f"--run-type={run_type}",
                f"--scope={scope}",
                "--skip-checks",
            ]
            if start_from_min_ts:
                cmd.append(f"--min-ts={min_ts}")
            if overwrite_store and run_type == "fetch":
                cmd.append("--overwrite")
                warn(
                    "!! All existing docs in storage will be overwritten (download & upload again) !!"
                )
            if overwrite_index and run_type == "parse":
                cmd.append("--overwrite")
                warn(
                    "!! All existing chunks in index will be overwritten (split & index again) !!"
                )
            cmd_and_contexts.append((" ".join(cmd), doc_type, run_type, min_ts))
    return cmd_and_contexts


def main() -> None:
    cmd_and_contexts = gen_cmds()
    if separate_logs:
        log_prefix.mkdir(parents=True, exist_ok=True)
        print(f"log path: {log_prefix}")
    else:
        print(f"log path: {full_log_path}")
    # debug(json.dumps(cmds, indent=2))
    tip("----- start -----")
    # results: dict[str, dict[str, int]] = {}
    with ThreadPool() as pool:  # default size is cpu count
        progress_bar = tqdm(
            pool.imap_unordered(run_cmd, cmd_and_contexts),
            desc="Indexing ...",
            total=len(cmd_and_contexts),
        )
        for rc, pid, args in progress_bar:
            cmd, doc_type, run_type, min_ts = args
            progress_bar.set_description(
                f"{datetime.now()}|{pid:<13}|✅{scope:>5}|{run_type:6}|{doc_type:13}"
            )
            # results[ticker] = results.get(ticker, {})
            # results[ticker][f"{run_type}_{doc_type}_{min_ts}"] = rc
            # mypy issue: https://github.com/python/typeshed/issues/8799
            if stop_flag.value == 1:  # type: ignore
                break
    tip("----- finished -----")
    # info(json.dumps(results, indent=2))


if __name__ == "__main__":
    main()
