# Generated by Django 4.2.2 on 2024-09-17 15:19

from django.db import migrations, models


def create_group(apps, schema_editor) -> None:
    # We get the model from the versioned app registry;
    # if we directly import it, it'll be the wrong version
    group_model = apps.get_model("auth", "Group")
    db_alias = schema_editor.connection.alias
    group_model.objects.using(db_alias).create(name="internal_subscribers")


def rollback_group(apps, schema_editor) -> None:
    group_model = apps.get_model("auth", "Group")
    db_alias = schema_editor.connection.alias
    group_model.objects.using(db_alias).filter(name="internal_subscribers").delete()


class Migration(migrations.Migration):
    dependencies = [
        ("payment", "0002_alter_subscription_customer_provider_id_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="subscription",
            name="cancel_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="plan_provider_url",
            field=models.URLField(
                blank=True,
                default="",
                help_text="url of plan page on provider platform",
            ),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="plan_title",
            field=models.CharField(
                blank=True, help_text="display title of the plan", max_length=255
            ),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="price_title",
            field=models.CharField(
                blank=True, help_text="display title of the price model", max_length=255
            ),
        ),
        migrations.RunPython(create_group, rollback_group),
    ]
