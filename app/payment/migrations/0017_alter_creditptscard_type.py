# Generated by Django 4.2.2 on 2025-03-19 18:54

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("payment", "0016_subscription_provider_default_payment_method_id"),
    ]

    operations = [
        migrations.AlterField(
            model_name="creditptscard",
            name="type",
            field=models.CharField(
                choices=[
                    ("free", "FREE"),
                    ("subscription", "SUBSCRIPTION"),
                    ("reward", "REWARD"),
                    ("gift", "GIFT"),
                    ("redeem", "REDEEM"),
                ],
                help_text="what type of this card. <ul><li><b>FREE</b> - The free credit points card created when user registered. <b style='color:red'>Do NOT create new, keep only ONE per user.</b></li><li><b>SUBSCRIPTION</b> - Automatically created when user subscribed to a plan. <b style='color:red'>Do NOT create mannually.</b></li><li><b>REWARD</b> - Reward points when user contributed. (e.g. report mistake)</li><li><b>GIFT</b> - The gift card for operation.<b style='color:blue'>Use this if you want to give user some free points.</b></li></ul> we probably add 'ONE_OFF', 'REFERRAL', 'COMPENSATION' cards in the future.",
                max_length=20,
            ),
        ),
    ]
