# Generated by Django 4.2.2 on 2024-09-12 14:36

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("payment", "0001_initial"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="subscription",
            name="customer_provider_id",
            field=models.Char<PERSON>ield(help_text="id of provider customer", max_length=255),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="last_invoice_provider_id",
            field=models.Char<PERSON>ield(
                help_text="id of last provider invoice ", max_length=255
            ),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="plan_provider_id",
            field=models.Char<PERSON>ield(
                help_text="id of plan data entry on platform", max_length=255
            ),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="price_provider_id",
            field=models.Char<PERSON>ield(
                help_text="id of price model data entry on platform", max_length=255
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="subscription",
            name="provider_id",
            field=models.Char<PERSON><PERSON>(
                help_text="id of provider subscription", max_length=255
            ),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="session_provider_id",
            field=models.Char<PERSON>ield(
                help_text="id of provider checkout session ", max_length=255
            ),
        ),
    ]
