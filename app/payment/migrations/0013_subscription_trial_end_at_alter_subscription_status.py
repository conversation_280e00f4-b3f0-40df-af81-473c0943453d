# Generated by Django 4.2.2 on 2025-02-19 11:33

from django.db import migrations, models
import payment.models


class Migration(migrations.Migration):
    dependencies = [
        ("payment", "0012_alter_creditptscard_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="subscription",
            name="trial_end_at",
            field=models.DateTimeField(
                blank=True,
                default=None,
                help_text="when trial of the subscription ends.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="status",
            field=models.CharField(
                choices=[
                    ("trial", "TRIAL"),
                    ("open", "OPEN"),
                    ("closed", "CLOSED"),
                    ("cancelled", "CANCELLED"),
                    ("refunded", "REFUNDED"),
                ],
                default=payment.models.SubscriptionStatusEnum["OPEN"],
                help_text="Status of current subscription. values: <bound method BaseEnum.values of <enum 'SubscriptionStatusEnum'>>",
                max_length=20,
            ),
        ),
    ]
