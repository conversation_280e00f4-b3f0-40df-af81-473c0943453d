# Generated by Django 4.2.2 on 2025-01-22 06:34
from datetime import datetime, timedelta

from django.db import migrations, models

from common.utils.datetimeutils import LOCAL_TZ
from payment.models import (
    CreditPtsTypeEnum,
    DEFAULT_FREE_CREDITS,
    DEFAULT_CREDITS_EXPIRES,
)


def create_free_credit_pts_and_set_type(apps, schema_editor) -> None:
    user_cls = apps.get_model("auth", "User")
    credit_pts_card_cls = apps.get_model("payment", "CreditPtsCard")
    setting_cls = apps.get_model("agent", "Settings")
    db_alias = schema_editor.connection.alias

    try:
        initial_free_credit = setting_cls.objects.get(key="initial_free_credit")
        credit_points = int(initial_free_credit.value.get("credit"))
    except setting_cls.DoesNotExist:
        credit_points = DEFAULT_FREE_CREDITS

    credit_pts_card_cls.objects.using(db_alias).filter(
        type="", provider_price_id="initial_free_credit"
    ).update(type=CreditPtsTypeEnum.FREE, provider_price_id="")
    credit_pts_card_cls.objects.using(db_alias).filter(
        type="", provider_price_id="earned_balance"
    ).update(type=CreditPtsTypeEnum.REWARD, provider_price_id="")
    credit_pts_card_cls.objects.using(db_alias).filter(
        type="", provider_price_id__istartswith="price_"
    ).update(type=CreditPtsTypeEnum.SUBSCRIPTION)

    for user in user_cls.objects.using(db_alias).all():
        if not credit_pts_card_cls.objects.filter(
            user=user, type=CreditPtsTypeEnum.FREE
        ).exists():
            card = credit_pts_card_cls(
                user=user,
                type=CreditPtsTypeEnum.FREE,
                credits=credit_points,
                start_at=datetime.now(tz=LOCAL_TZ),
                end_at=datetime.now(tz=LOCAL_TZ)
                + timedelta(days=DEFAULT_CREDITS_EXPIRES),
            )
            card.save()


class Migration(migrations.Migration):
    dependencies = [
        ("payment", "0008_alter_subscription_cancel_at_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="creditptscard",
            name="type",
            field=models.CharField(
                choices=[
                    ("free", "FREE"),
                    ("subscription", "SUBSCRIPTION"),
                    ("reward", "REWARD"),
                ],
                default="",
                help_text="what type of this card",
                max_length=20,
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="creditptscard",
            name="end_at",
            field=models.DateTimeField(
                db_index=True, help_text="when the card will be expired"
            ),
        ),
        migrations.AlterField(
            model_name="creditptscard",
            name="provider_price_id",
            field=models.CharField(
                blank=True,
                default="",
                help_text="price ID from its provider",
                max_length=255,
            ),
        ),
        migrations.RunPython(create_free_credit_pts_and_set_type),
    ]
