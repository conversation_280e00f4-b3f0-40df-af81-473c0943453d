#!/usr/bin/env bash

# script to trigger an Stripe event to webhook

USER_ID=2
USER_KEY=c516228d83d59ad97c0e90eb86d37fa209f99ee46115e805ccb6d36b76a1263b
ENV=dev
PRICE_ID=xxx
MODE=subscription
SUBSCRIPTION=sub_1PyZcFPROm4rLEEEpPBjyaHO

# trigger checkout session complete
stripe trigger checkout.session.completed \
	--add checkout_session:metadata.uid=${USER_ID} \
	--add checkout_session:metadata.key=${USER_KEY} \
	--add checkout_session:metadata.env=${ENV} \
	--add checkout_session:metadata.price_id=${PRICE_ID} \
	--add checkout_session:line_items.price=${PRICE_ID} \
	--override checkout_session:mode=${MODE}



#	--add checkout_session:subscription=${SUBSCRIPTION} \

