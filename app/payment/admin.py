from django import forms
from django.contrib import admin
from django.http import HttpRequest
from django.utils.safestring import mark_safe

from common.django_ext.model import CreateTimeMixin, UpdateTimeMixin
from common.utils.datetimeutils import format_datetime, LOCAL_TZ
from .models import (
    CreditPtsCard,
    PaymentMethodFingerprint,
    Subscription,
    SubscriptionStatusEnum,
    CreditPtsTypeEnum,
)


def text_widget(cols: int = 150, rows: int = 25) -> forms.Textarea:
    return forms.Textarea(
        attrs={"readonly": "readonly", "cols": str(cols), "rows": str(rows)}
    )


@admin.display(description="created at")
def created_at_disp(obj: CreateTimeMixin) -> str:
    # TODO(data): Timezone
    return mark_safe(obj.created_at.strftime("%y-%m-%d<br/>%H:%M:%S"))


@admin.display(description="updated at")
def updated_at_disp(obj: UpdateTimeMixin) -> str:
    # TODO(data): Timezone
    return mark_safe(obj.updated_at.strftime("%y-%m-%d<br/>%H:%M:%S"))


@admin.display(description="period")
def period_disp(obj: Subscription | CreditPtsCard) -> str:
    return mark_safe(
        f'<span style="white-space:nowrap">{format_datetime(obj.start_at, tz=LOCAL_TZ)}</span>'
        f'<br/><span style="white-space:nowrap">{format_datetime(obj.end_at,tz=LOCAL_TZ)}</span>'
    )


# class PlanAdmin(admin.ModelAdmin):
#     list_display = [
#         "name",
#         "price",
#         "interval",
#         created_at_disp,
#     ]
#     list_display_links = ["name", "price"]
#     search_fields = ["name", "price"]
#     list_filter = ["interval"]
#     date_hierarchy = "created_at"
#     # raw_id_fields = ["name"]
#     readonly_fields = ["name", "price", "interval", "created_at"]


class SubscriptionAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "user",
        "status_disp",
        # "amount_disp",
        "price_disp",
        period_disp,
        "provider_link",
        created_at_disp,
    ]
    list_display_links = ["id", "user", "price_disp"]
    search_fields = [
        "user__username",
        "user__email",
        # "user__firstname",
        # "user__lastname",
        "status",
        "price_title",
        "provider_id",
    ]
    list_filter = ["status", "price_title"]
    date_hierarchy = "created_at"
    # raw_id_fields = ["name"]
    # readonly_fields = []

    def get_readonly_fields(
        self, request: HttpRequest, obj: Subscription | None = None
    ) -> list:
        if obj is None:
            return []
        else:
            return [
                k
                for k in obj.__dict__.keys()
                if not (k.startswith("_") or k in {"status", "end_at", "trial_end_at"})
            ]

    @admin.display(description="ST")
    def status_disp(self, obj: Subscription) -> str:
        emoji = SubscriptionStatusEnum(obj.status).emoji
        if emoji == obj.status:
            s = emoji
        else:
            s = f"<span title={obj.status}>{emoji}</span>"
        return mark_safe(s)

    @admin.display(description="amount")
    def amount_disp(self, obj: Subscription) -> str:
        return str(int(obj.amount / 100))

    @admin.display(description="price")
    def price_disp(self, obj: Subscription) -> str:
        # return mark_safe(
        #     f"{CURRENCIES.get(obj.price_currency, {}).get('symbol', '$')}{int(obj.price_unit_price/100)}/{obj.price_interval}ly"
        # )
        return obj.price_title

    @admin.display(description="provider link")
    def provider_link(self, obj: Subscription) -> str:
        url = f"https://dashboard.stripe.com/subscriptions/{obj.provider_id}"
        return mark_safe(
            f'<a href="{url}">{obj.provider_id[:8]}...{obj.provider_id[-4:]}</a>'
        )


class CreditPtsCardAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "user",
        "type_disp",
        "is_disabled",
        "points_disp",
        "profession_disp",
        period_disp,
        created_at_disp,
    ]
    list_display_links = ["id", "user", "points_disp"]
    search_fields = [
        "user__username",
        "user__email",
        # "user__firstname",
        # "user__lastname",
        "type",
    ]
    list_filter = ["type", "is_disabled", "credits"]
    date_hierarchy = "created_at"

    @admin.display(description="type")
    def type_disp(self, obj: CreditPtsCard) -> str:
        return mark_safe(
            f"<span title='{obj.type}'>{CreditPtsTypeEnum(obj.type).emoji}</span>"
        )

    @admin.display(description="credits")
    def points_disp(self, obj: CreditPtsCard) -> str:
        return mark_safe(f"{obj.used_credits}/{obj.credits}")

    @admin.display(description="profession")
    def profession_disp(self, obj: CreditPtsCard) -> str:
        profile = obj.user.profile_set.first()
        profession = profile.profession or "" if profile else ""

        return mark_safe(f"{profession}")


# admin.site.register(PriceModel)
# admin.site.register(Plan)
admin.site.register(Subscription, SubscriptionAdmin)
admin.site.register(CreditPtsCard, CreditPtsCardAdmin)
admin.site.register(PaymentMethodFingerprint)
