from common.utils.lang import BaseEnum


class IndexProviderEnum(str, BaseEnum):
    """represents the target where the doc is indexed into"""

    PINECONE_EMBEDDING = "pinecone_embedding"
    PINECONE_HYBRID = "pinecone_hybrid"
    # ELASTIC = "elastic"


class DocTypeEnum(str, BaseEnum):
    NOT_SPECIFIED = ""
    EARNING_CALL = "earning_call"
    EVENT_TRANSCRIPT = "event_transcript"
    SEC_FILING = "sec_filing"
    CONFERENCE = "conference"
    PRESS_RELEASE = "press_release"
    NEWS = "news"
    TWITTER = "twitter"
    YOUTUBE = "youtube"

    CUSTOMER = "customer"

    def __str__(self) -> str:
        return self.value

    def __bool__(self) -> bool:
        return bool(self.value)

    @property
    def display_value(self) -> str:
        s = "earnings call" if self == DocTypeEnum.EARNING_CALL else self.value
        return s.title().replace("_", " ")

    @classmethod
    def values(cls) -> list:
        return [
            e.value
            for e in cls  # type: ignore
            if not e.name.startswith("_") and e != cls.NOT_SPECIFIED
        ]


class DataProviderEnum(str, BaseEnum):
    NOT_SPECIFIED = ""

    FMP = "fmp"
    QUARTR = "quartr"
    BENZINGA = "benzinga"
    SEEKING_ALPHA = "seeking_alpha"

    BOT = "bot"
    APIFY = "apify"
    CUSTOMER = "customer"

    TWITTER = "twitter"
    YOUTUBE = "youtube"


class TickerScopeEnum(str, BaseEnum):
    TOP = "top"
    KEY = "key"
    SNP = "snp"
    RUSSELL = "russell"
    EXTRA = "extra"
    FULL = "full"
