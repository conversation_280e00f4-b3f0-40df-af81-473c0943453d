import logging
from abc import ABC
from typing import Any, Dict, List, Optional, Tuple

from langchain.docstore.document import Document
from pinecone_text.hybrid import hybrid_convex_scale

from biz.search.base import IndexSearcher

logger = logging.getLogger(__name__)


class PineconeSearcher(IndexSearcher, ABC):
    pass


class PineconeEmbeddingSearcher(PineconeSearcher):
    async def query_with_scores(
        self,
        query: str,
        top_k: int = 8,
        metadata_filter: Optional[Dict] = None,
        **kwargs: Any,
    ) -> List[Tuple[Document, float]]:
        logger.info(f"Querying pinecone embedding searcher with query: {query}")
        # TODO: use the index in this class rather than langchain Pinecone object
        return await self.index.target.asimilarity_search_with_relevance_scores(  # type: ignore
            query=query,
            k=top_k,
            filter=metadata_filter,
        )


class PineconeHybridSearcher(PineconeSearcher):
    alpha: float = 0.5
    """The alpha value for the hybrid search, which is a float between 0 and 1
    where 0 == sparse only and 1 == dense only"""

    async def query_with_scores(
        self,
        query: str,
        top_k: int = 8,
        metadata_filter: Optional[Dict] = None,
        **kwargs: Any,
    ) -> List[Tuple[Document, float]]:
        logger.info(
            f"Querying {self.index} with query: '{query}' and alpha: {self.alpha}"
            f". filter: {metadata_filter}"
        )
        sparse_vec = self.index.sparse_encoder.encode_query(query)
        # convert the question into a dense vector
        dense_vec = self.index.dense_embeddings.encode_query(query)
        # scale alpha with hybrid_scale
        dense_vec, sparse_vec = hybrid_convex_scale(dense_vec, sparse_vec, self.alpha)
        sparse_vec["values"] = [float(s1) for s1 in sparse_vec["values"]]
        # query pinecone with the query parameters
        result = self.index.target.query(
            vector=dense_vec,
            sparse_vector=sparse_vec,
            top_k=top_k,
            include_metadata=True,
            filter=metadata_filter,
            namespace=self.index.config.namespace,
        )
        final_result = []
        for res in result["matches"]:
            context = res["metadata"].pop("context")
            meta = res["metadata"]
            meta["index_id"] = res["id"]
            final_result.append(
                (Document(page_content=context, metadata=meta), res["score"])
            )
        return final_result
