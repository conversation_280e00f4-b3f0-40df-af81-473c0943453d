from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent

# TODO: 1. assert for duplicate tickers.
#       2. assert for duplicate entry in common/finance/*.csv
#       3. remove duplicated from common/finance/companies_patch.csv
#       3. assert if company missing in common/finance/*.csv
PRELOADED_MODELS_DIR = BASE_DIR / "agent/preloaded_models/"


TICKER_PLACE_HOLDER = "__TICKER__"
assert len(TICKER_PLACE_HOLDER) <= 10
