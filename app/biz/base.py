import sys
from abc import ABC
from datetime import datetime
from typing import Literal, ClassVar

from pydantic import BaseModel
from pydantic import Field

from biz.enums import DocTypeEnum, DataProviderEnum
from common.django_ext.model import convert_pydantic_to_django

VALUE_OF_MAX_LENGTH_MEANS_TEXT_FIELD = sys.maxsize
FIELD_MAX_LENGTH = 510


class FiscalMetaMixin(BaseModel):
    fiscal_year: int = Field(
        index_filter=True,
        default=...,
        description=(
            "The fiscal year of the doc. it's common to financial docs such as earning call, SEC filing, etc. "
        ),
    )
    fiscal_quarter: int = Field(
        index_filter=True,
        default=...,
        choices=[(1, "Q1"), (2, "Q2"), (3, "Q3"), (4, "Q4")],
        description="The fiscal quarter of the doc. it's common to financial docs such as earning call, SEC filing, etc. ",
    )


class SocialPostMetaMixin(BaseModel):
    channel_name: str = Field(
        index_meta=True,
        default=...,
        max_length=100,
        description="channel or account name of the social media post. usually it is unique.",
    )
    post_id: str = Field(
        index_meta=True,
        default=...,
        max_length=32,
        description="id of the social media post. usually it is the unique identity of the post on the social platform.",
    )


class DocMetaBase(BaseModel, ABC):
    """Base of the doc meta. The fields will be:
        1. added as meta to vector db
        2. stored in db.
    This model is designed to be easily converted to a Django model."""

    # doc_type should be ClassVar to identify a DocMeta class
    doc_type: ClassVar[DocTypeEnum] = DocTypeEnum.NOT_SPECIFIED

    # General fields, same as DocModel
    key: str = Field(
        index_filter=True,
        db_unique=True,
        default=...,
        max_length=FIELD_MAX_LENGTH,
        description="the unique identifier of the doc",
    )
    ticker: str = Field(
        index_filter=True,
        db_index=True,
        default=...,
        max_length=10,
        description="the doc related company ticker",
    )
    pub_date: datetime = Field(
        index_filter=True,
        db_index=True,
        default=...,
        description="when the doc published, filed, or released.",
    )
    title: str = Field(
        index_meta=True,
        default="",
        max_length=FIELD_MAX_LENGTH,
        description="The title or the doc. If the original doc does not have a title, let's generate for it.",
    )
    provider: DataProviderEnum = Field(
        index_meta=True,
        default=DataProviderEnum.NOT_SPECIFIED,
        max_length=50,
        description="which data provider the data comes from",
    )
    original_url: str = Field(
        index_meta=True,
        default="",
        max_length=FIELD_MAX_LENGTH,
        description="the original link where the doc downloaded",
    )
    quality_score: float = Field(
        index_filter=True,
        default=0.0,
        le=1.0,
        ge=0.0,
        description="the score of the doc quality which evaluated by ourselves",
    )

    # Extensions
    tickers: list[str] = Field(
        index_filter=True, default_factory=list, db_excluded=True
    )
    pub_date_str: str = Field(index_meta=True, default="", db_excluded=True)

    # extendable fields for future extension
    ext1: str = Field(index_filter=True, default="", db_excluded=True)
    ext2: str = Field(index_filter=True, default="", db_excluded=True)
    ext3: str = Field(index_filter=True, default="", db_excluded=True)
    ext4: str = Field(index_filter=True, default="", db_excluded=True)
    ext5: str = Field(index_filter=True, default="", db_excluded=True)

    class Config:
        arbitrary_types_allowed = True
        use_enum_values = True
        from_attributes = True

    class DjangoModelMeta:
        abstract = True


class EarningCallMetaBase(DocMetaBase, FiscalMetaMixin, ABC):
    doc_type: ClassVar[DocTypeEnum] = DocTypeEnum.EARNING_CALL

    class DjangoModelMeta:
        abstract = True
        unique_together = (
            "ticker",
            "fiscal_quarter",
            "fiscal_year",
        )


class EventTranscriptMetaBase(DocMetaBase, ABC):
    """transcript of company hosted event except Earnings Call"""

    doc_type: ClassVar[DocTypeEnum] = DocTypeEnum.EVENT_TRANSCRIPT
    event_type: str = Field(
        index_meta=True,
        db_index=True,
        default=...,
        max_length=100,
        description="The event type such as AGM, Investor Day and etc.",
    )

    class DjangoModelMeta:
        abstract = True


class SECFilingMetaBase(DocMetaBase, FiscalMetaMixin, ABC):
    doc_type: ClassVar[DocTypeEnum] = DocTypeEnum.SEC_FILING

    filing_type: Literal["10-Q", "10-K"] = Field(
        index_filter=True, default=..., index=True
    )
    sec_section: str = Field(index_filter=True, default="", db_excluded=True)

    class DjangoModelMeta:
        abstract = True
        unique_together = (
            "ticker",
            "fiscal_quarter",
            "fiscal_year",
        )


class ConferenceMetaBase(DocMetaBase, ABC):
    doc_type: ClassVar[DocTypeEnum] = DocTypeEnum.CONFERENCE

    class DjangoModelMeta:
        abstract = True


class PressReleaseMetaBase(DocMetaBase, ABC):
    doc_type: ClassVar[DocTypeEnum] = DocTypeEnum.PRESS_RELEASE

    class DjangoModelMeta:
        abstract = True


class NewsMetaBase(DocMetaBase, ABC):
    doc_type: ClassVar[DocTypeEnum] = DocTypeEnum.NEWS

    classification: list[str] = Field(index_filter=True, default_factory=list)

    class DjangoModelMeta:
        abstract = True


class TweetMetaBase(DocMetaBase, SocialPostMetaMixin, ABC):
    doc_type: ClassVar[DocTypeEnum] = DocTypeEnum.TWITTER

    class DjangoModelMeta:
        abstract = True


class YoutubeVideoMetaBase(DocMetaBase, SocialPostMetaMixin, ABC):
    doc_type: ClassVar[DocTypeEnum] = DocTypeEnum.YOUTUBE

    channel_id: str = Field(index_meta=True, default=..., max_length=32)
    desc: str = Field(default="", max_length=VALUE_OF_MAX_LENGTH_MEANS_TEXT_FIELD)

    class DjangoModelMeta:
        abstract = True


class CustomerDocMetaBase(DocMetaBase, ABC):
    doc_type: ClassVar[DocTypeEnum] = DocTypeEnum.CUSTOMER

    user_id: int = Field(index_filter=True, db_index=True, default=...)
    org_id: str = Field(index_filter=True, db_index=True, default="", max_length=50)
    original_filename: str = Field(
        default=..., max_length=255, description="raw file name without folder"
    )
    content_type: str = Field(
        index_filter=True, default="", max_length=255, description="raw content type"
    )
    # Extensions
    tickers: list[str] = Field(index_filter=True, default_factory=list)

    class DjangoModelMeta:
        abstract = True


DocModelBase = convert_pydantic_to_django(
    DocMetaBase, "DocModelBase", __name__, extra_fields=["doc_type"]
)
EarningCallModelBase = convert_pydantic_to_django(
    EarningCallMetaBase, "EarningCallModelBase", __name__, extra_fields=["doc_type"]
)
EventTranscriptModelBase = convert_pydantic_to_django(
    EventTranscriptMetaBase,
    "EventTranscriptModelBase",
    __name__,
    extra_fields=["doc_type"],
)
SECFilingModelBase = convert_pydantic_to_django(
    SECFilingMetaBase, "SECFilingModelBase", __name__, extra_fields=["doc_type"]
)
ConferenceModelBase = convert_pydantic_to_django(
    ConferenceMetaBase, "ConferenceModelBase", __name__, extra_fields=["doc_type"]
)
PressReleaseModelBase = convert_pydantic_to_django(
    PressReleaseMetaBase, "PressReleaseModelBase", __name__, extra_fields=["doc_type"]
)
NewsModelBase = convert_pydantic_to_django(
    NewsMetaBase, "NewsModelBase", __name__, extra_fields=["doc_type"]
)
TweetModelBase = convert_pydantic_to_django(
    TweetMetaBase, "TweetModelBase", __name__, extra_fields=["doc_type"]
)
YoutubeVideoModelBase = convert_pydantic_to_django(
    YoutubeVideoMetaBase, "YoutubeVideoModelBase", __name__, extra_fields=["doc_type"]
)
CustomerDocModelBase = convert_pydantic_to_django(
    CustomerDocMetaBase, "CustomerDocModelBase", __name__, extra_fields=["doc_type"]
)
