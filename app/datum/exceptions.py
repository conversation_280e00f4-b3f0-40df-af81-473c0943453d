import django.core.exceptions


class DatumException(Exception):
    pass


class ConflictError(DatumException):
    pass


class ContentExistedError(DatumException):
    pass


class MetaValidationError(DatumException, django.core.exceptions.ValidationError):
    pass


class FieldValidationError(DatumException, django.core.exceptions.ValidationError):
    pass


class FilterValidationError(DatumException, django.core.exceptions.ValidationError):
    pass


class VolitionError(DatumException):
    pass


class ClassifyError(Exception):
    pass
