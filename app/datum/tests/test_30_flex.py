from django.test import TestCase
from django.utils.datetime_safe import datetime

from datum.tests.base import BaseDatumTest, DatumOwnerTestMixin
from ..enums import MetaKindEnum, MetaTypeEnum
from ..models import (
    MetaField,
)
from ..wrapper import <PERSON><PERSON><PERSON>rapper, DatumManager


class TestFlexDatumCase1(BaseDatumTest, DatumOwnerTestMixin, TestCase):
    def setUp(self) -> None:
        self.org = self.DatumOwnerModel.get_test_instance()
        self.mgr = DatumManager(owner=self.org, is_strict=False)
        self.wrappers = self._create_wrappers()

    def tearDown(self) -> None:
        try:
            self.mgr.batch_delete(self.wrappers, include_store=True)
        except Exception:
            pass
        self.mgr = None  # type:ignore

    def _metas(self) -> list:
        return []

    def _datum_list(self) -> list:
        return [{"v": "aaa"}]

    def _get_key(self, idx: int) -> str:
        return f"flex-{idx}"

    def _create_wrappers(self) -> list[DatumWrapper]:
        metas = self._datum_list()
        wrappers = []
        for i, d in enumerate(metas):
            key = self._get_key(i)
            v = d.pop("v")
            wrapper = self.mgr.create(
                owner=self.org.key,
                key=key,
                value=v,
                meta_dict=d,
            )
            wrapper.save(include_store=False)
            wrappers.append(wrapper)
        return wrappers

    def test_00_manager(self) -> None:
        key = self._get_key(0)

        with self.assertRaises(PermissionError):
            self.mgr.get(key, datum_owner="another_owner", preload_content=True)

    def test_10_add_meta(self) -> None:
        wrapper = self.wrappers[0]
        self.assertEqual(len(wrapper.meta_kv_dict), 0)

        meta = {
            "name": "test1",
            "age": 28,
        }
        wrapper.update_meta(**meta)
        self.assertEqual(len(wrapper.meta_kv_dict), 2)
        self.assertDictEqual(wrapper.meta_kv_dict, meta)

        wrapper["favorites"] = ["apple", "football", "reading"]
        self.assertEqual(len(wrapper.meta_kv_dict), 3)
        wrapper.save(include_store=False)
        self.assertEqual(
            "test1",
            self.get_meta_value(
                "name",
                datum_owner=self.org.key,
                datum_key=wrapper.key,
            ),
        )
        self.assertEqual(
            28,
            self.get_meta_value(
                "age",
                datum_owner=self.org.key,
                datum_key=wrapper.key,
            ),
        )
        self.assertEqual(
            ["apple", "football", "reading"],
            self.get_meta_value(
                "favorites",
                datum_owner=self.org.key,
                datum_key=wrapper.key,
            ),
        )

    def test_11_add_invalid_meta(self) -> None:
        wrapper = self.wrappers[0]
        self.assertNotIn("foo", wrapper.meta_kv_dict.keys())
        with self.assertRaises(TypeError):
            wrapper["foo"] = {"hello": "world"}

    def test_20_update_meta(self) -> None:
        wrapper = self.wrappers[0]
        self.assertEqual(len(wrapper.meta_kv_dict), 0)

        meta = {
            "name": "test1",
            "age": 28,
        }
        wrapper.update_meta(**meta)
        wrapper.save(include_store=False)
        key = wrapper.key
        owner = wrapper.owner

        wrapper = self.mgr.get(datum_key=key, datum_owner=owner)  # type:ignore
        assert wrapper
        self.assertEqual(wrapper["name"], "test1")
        self.assertEqual(wrapper["age"], 28)

        t = datetime.now()
        wrapper["name"] = t
        wrapper.update_meta(age="14")
        wrapper.save(include_store=False)
        self.assertEqual(
            t, self.get_meta_value("name", datum_owner=owner, datum_key=key)
        )
        self.assertEqual(
            "14", self.get_meta_value("age", datum_owner=owner, datum_key=key)
        )

    def test_21_update_invalid_meta(self) -> None:
        wrapper = self.wrappers[0]
        wrapper["name"] = "test1"
        self.assertIn("name", wrapper.meta_kv_dict.keys())
        with self.assertRaises(TypeError):
            wrapper["name"] = b"1test"

    def test_40_add_fields(self) -> None:
        metas = self._datum_list()
        i = 0
        key = self._get_key(i)

        wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        self.assertIsNotNone(wrapper)
        assert wrapper

        for meta in wrapper.meta_obj_list:
            self.assertIn(meta.meta_name, metas[i].keys())
            self.assertEqual(meta.meta_value, metas[i].get(meta.meta_name))

        no_field_metas = {f"no_field_meta{j}": f"meta{j}" for j in range(3)}
        for k, v in no_field_metas.items():
            wrapper[k] = v

        f_name = "foo"
        field = MetaField(
            name=f_name,
            type=MetaTypeEnum.STR,
            kind=MetaKindEnum.USER,
            description="foo",
            default="Default Value",
        )
        self.mgr.batch_add_field([wrapper], field)
        wrapper.save(include_store=False)

        updated_wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        self.assertIsNotNone(updated_wrapper)
        assert updated_wrapper

        for k, v in no_field_metas.items():
            self.assertEqual(v, updated_wrapper.meta_kv_dict[k])
        self.assertEqual("Default Value", updated_wrapper.meta_kv_dict[f_name])

        # field added into manager as well
        updated_mgr = DatumManager(
            owner=self.org, is_strict=False, fields=self.mgr.fields + [field]
        )
        self.assertEqual(1, len(updated_mgr.fields))
        f_name_set = {f.name for f in updated_mgr.fields}
        for k, v in no_field_metas.items():
            self.assertNotIn(k, f_name_set)
        self.assertIn(f_name, f_name_set)

        updated_wrapper = updated_mgr.get(datum_key=key, datum_owner=self.org.key)
        assert updated_wrapper
        self.assertIn(f_name, updated_wrapper.meta_kv_dict)
        self.assertEqual(updated_wrapper[f_name], "Default Value")

        updated_wrapper[f_name] = "New Value"
        updated_wrapper.save()
        reloaded_wrapper = updated_mgr.get(datum_key=key, datum_owner=self.org.key)
        assert reloaded_wrapper
        self.assertEqual(reloaded_wrapper[f_name], "New Value")

        f_name1 = "bar"
        another_field = MetaField(
            name=f_name1,
            type=MetaTypeEnum.INT,
            kind=MetaKindEnum.CLASSIFIED,
            description="another field",
            default=888,
        )
        updated_mgr.batch_add_field([wrapper], another_field, add_to_manager=True)
        wrapper.save(include_store=False)
        updated_wrapper = updated_mgr.get(datum_key=key, datum_owner=self.org.key)
        assert updated_wrapper
        self.assertIn(f_name1, updated_wrapper.meta_kv_dict)
        self.assertEqual(updated_wrapper[f_name1], 888)

        self.assertEqual(5, len(updated_wrapper.meta_kv_dict))

    def test_41_update_fields(self) -> None:
        # same as strict datum
        pass

    def test_45_delete_fields(self) -> None:
        key = self._get_key(0)
        wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        assert wrapper

        no_field_metas = {f"no_field_meta{j}": f"meta{j}" for j in range(3)}
        for k, v in no_field_metas.items():
            wrapper[k] = v

        fields = [
            MetaField(
                name="foo",
                type=MetaTypeEnum.STR,
                kind=MetaKindEnum.USER,
                description="foo",
                default="Default Value",
            ),
            MetaField(
                name="bar",
                type=MetaTypeEnum.INT,
                kind=MetaKindEnum.CLASSIFIED,
                description="another field",
                default=888,
            ),
        ]
        try:
            self.mgr.batch_add_field([wrapper], fields[0], add_to_manager=True)
        except KeyError:
            pass
        try:
            self.mgr.batch_add_field([wrapper], fields[1], add_to_manager=True)
        except KeyError:
            pass
        wrapper.save(include_store=False)

        # Try to delete a non-existent field
        with self.assertRaises(KeyError):
            self.mgr.batch_del_field([wrapper], "non_existent_field")

        # Delete a field
        deleted_fields = self.mgr.batch_del_field(
            [wrapper], fields[0].name, del_from_manager=True
        )
        wrapper.save(include_store=False)

        # Verify the field is deleted
        self.assertEqual(len(deleted_fields), 1)
        assert deleted_fields[0][0]
        self.assertEqual(deleted_fields[0][0].name, fields[0].name)
        assert deleted_fields[0][1]
        self.assertEqual(deleted_fields[0][1].meta_value, fields[0].default)

        # verify the changes
        updated_mgr = DatumManager(
            owner=self.org, is_strict=False, fields=self.mgr.fields
        )
        assert updated_mgr
        updated_wrapper = updated_mgr.get(datum_key=key, datum_owner=self.org.key)
        assert updated_wrapper
        self.assertEqual(4, len(updated_wrapper.meta_kv_dict))
        self.assertNotIn(fields[0].name, updated_wrapper.meta_kv_dict)

        # delete no_field_meta
        updated_wrapper.del_field("no_field_meta1")
        updated_wrapper.save(include_store=False)
        updated_wrapper = updated_mgr.get(datum_key=key, datum_owner=self.org.key)
        assert updated_wrapper
        self.assertNotIn("no_field_meta1", updated_wrapper.meta_kv_dict)

    def test_50_query(self) -> None:
        fields, no_field_metas = self._prepare_data_4_query()

        wrappers = self.mgr.query(datum_owner=self.org.key)
        self.assertEqual(3, len(wrappers))

        # query meta with field
        wrappers = self.mgr.query(foo="Default Value")
        self.assertEqual(1, len(wrappers))
        wrapper = wrappers[0]
        assert wrapper
        self.assertEqual(self._get_key(1), wrapper.key)
        self.assertEqual(3, len(wrapper.meta_kv_dict))
        for k in ["no_field_meta0", "no_field_meta1"]:
            self.assertIn(k, wrapper.meta_kv_dict)
        for k in ["bar", "no_field_meta2"]:
            self.assertNotIn(k, wrapper.meta_kv_dict)

        # query no field meta

        # startswith seems not work with django+sqlite+mem
        # wrappers = self.mgr.query(no_field_meta1__istartswith="meta")
        # self.assertEqual(2, len(wrappers))

        wrappers = self.mgr.query(no_field_meta1="meta1")
        self.assertEqual(2, len(wrappers))

        wrappers = self.mgr.query(no_field_meta2__icontains="meta")
        self.assertEqual(1, len(wrappers))
        wrapper = wrappers[0]
        assert wrapper
        self.assertEqual(self._get_key(2), wrapper.key)
        self.assertEqual(4, len(wrapper.meta_kv_dict))
        for k in ["bar", "no_field_meta0", "no_field_meta1", "no_field_meta2"]:
            self.assertIn(k, wrapper.meta_kv_dict)
        for k in ["foo"]:
            self.assertNotIn(k, wrapper.meta_kv_dict)

    def test_90_delete_by_filter(self) -> None:
        self._prepare_data_4_query()

        self.mgr.delete_by_filters(no_field_meta1="meta1", include_store=False)
        wrappers = self.mgr.query(without_metas=True, page_size=100)
        self.assertEqual(1, len(wrappers))
        for wrapper in wrappers:
            self.assertNotEquals(wrapper.key, self._get_key(1))

    def test_91_delete(self) -> None:
        self._prepare_data_4_query()

        wrapper = self.mgr.get(datum_owner=self.org.key, datum_key=self._get_key(0))
        self.assertIsNotNone(wrapper)
        assert wrapper
        wrapper.delete(include_store=False)
        wrappers = self.mgr.query(without_metas=False, page_size=100)
        self.assertEqual(2, len(wrappers))

        for wrapper in wrappers:
            self.assertNotEquals(wrapper.key, self._get_key(0))

    def _prepare_data_4_query(self) -> tuple:
        # prepare data
        key0 = self._get_key(0)
        wrapper0, _ = self.mgr.get_or_create(datum_key=key0, datum_owner=self.org.key)
        key1 = self._get_key(1)
        wrapper1, _ = self.mgr.get_or_create(datum_key=key1, datum_owner=self.org.key)
        key2 = self._get_key(2)
        wrapper2, _ = self.mgr.get_or_create(datum_key=key2, datum_owner=self.org.key)

        no_field_metas = {f"no_field_meta{j}": f"meta{j}" for j in range(3)}
        for j in [0]:
            k = f"no_field_meta{j}"
            v = no_field_metas[k]
            wrapper0[k] = v
        for j in [0, 1]:
            k = f"no_field_meta{j}"
            v = no_field_metas[k]
            wrapper1[k] = v
        for j in [0, 1, 2]:
            k = f"no_field_meta{j}"
            v = no_field_metas[k]
            wrapper2[k] = v

        fields = [
            MetaField(
                name="foo",
                type=MetaTypeEnum.STR,
                kind=MetaKindEnum.USER,
                description="foo",
                default="Default Value",
            ),
            MetaField(
                name="bar",
                type=MetaTypeEnum.INT,
                kind=MetaKindEnum.CLASSIFIED,
                description="another field",
                default=888,
            ),
        ]
        try:
            self.mgr.batch_add_field([wrapper1], fields[0], add_to_manager=False)
        except KeyError:
            pass
        try:
            self.mgr.batch_add_field([wrapper2], fields[1], add_to_manager=False)
        except KeyError:
            pass
        wrapper0.save(include_store=False)
        wrapper1.save(include_store=False)
        wrapper2.save(include_store=False)

        return fields, no_field_metas
