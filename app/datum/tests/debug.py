from datetime import datetime, UTC

from django.test import TestCase

from ..enums import Meta<PERSON>indEnum, MetaTypeEnum
from ..models import DatumModel, MetaField, DatumMetaModel
from ..wrapper import DatumWrapper


class TestDebug(TestCase):
    __test__ = False

    def test_debug(self) -> None:
        owner = "sam"
        key = "aaa"
        datum = DatumModel(owner=owner, key=key, value="")
        meta_dict_list = [
            {"foo": datetime.now(tz=UTC), "bar": "Mike", "baaz": [1, 2, 3]},
            # {
            #     "foo": datetime.now(tz=UTC) - timedelta(days=30),
            #     "bar": "Jack",
            #     "baaz": ["a", "b", "c"],
            # },
        ]
        metas = [
            DatumMetaModel(datum_owner=owner, datum_key=key, meta_name=k, meta_value=v)
            for k, v in meta_dict_list[0].items()
        ]
        fields = [
            MetaField(
                name="foo",
                type=MetaTypeEnum.DATETIME,
                kind=MetaKindEnum.DESIGNED,
                description="foo",
            ),
            MetaField(
                name="bar",
                type=MetaTypeEnum.STR,
                kind=MetaKindEnum.CLASSIFIED,
                description="bar",
            ),
            MetaField(
                name="baaz",
                type=MetaTypeEnum.LIST,
                kind=MetaKindEnum.USER,
                description="baaz",
            ),
        ]
        wrapper = DatumWrapper(
            datum=datum,
            metas=metas,
            fields=fields,
            is_strict=True,
        )
        print(wrapper["foo"])
