from datetime import datetime

from django.test import TestCase

from common.utils.datetimeutils import parse_datetime
from datum.tests.base import BaseDatumTest, DatumOwnerTestMixin
from ..enums import MetaKindEnum, MetaTypeEnum
from ..exceptions import MetaValidationError, VolitionError
from ..models import (
    MetaField,
)
from ..wrapper import <PERSON>tumWrapper, DatumManager


class TestStrictDatumCase1(BaseDatumTest, DatumOwnerTestMixin, TestCase):
    """
    A user has multiple similar documentation

    The prototype is “<PERSON>”. He has 30~40 transcripts of Uber & Dash events.

    example:
        1. user - "Jack"
        2. meta fields - "pub_date", "fiscal_year", "fiscal_quarter", "ticker"
    """

    def setUp(self) -> None:
        # self.user, _ = User.objects.get_or_create(
        #     username="jack", email="<EMAIL>"
        # )
        self.org = self.DatumOwnerModel.get_test_instance()
        fields = self._designed_meta() + self._classified_meta() + self._user_meta()
        self.mgr = DatumManager(owner=self.org, is_strict=True, fields=fields)
        self.wrappers = self._create_wrappers()

    def tearDown(self) -> None:
        try:
            self.mgr.batch_delete(self.wrappers, include_store=True)
        except Exception:
            pass

    def _designed_meta(self) -> list:
        return [
            MetaField(
                name="pub_date",
                type=MetaTypeEnum.DATETIME,
                kind=MetaKindEnum.DESIGNED,
                default=datetime.min,
                description="when the transcript published",
            ),
            MetaField(
                name="ticker",
                type=MetaTypeEnum.STR,
                kind=MetaKindEnum.DESIGNED,
                default="",
                description="ticker of the transcript",
            ),
            MetaField(
                name="fiscal_year",
                type=MetaTypeEnum.INT,
                kind=MetaKindEnum.DESIGNED,
                default=0,
                description="fiscal year of the transcript",
            ),
            MetaField(
                name="fiscal_quarter",
                type=MetaTypeEnum.INT,
                kind=MetaKindEnum.DESIGNED,
                default=0,
                description="fiscal quarter of the transcript",
            ),
        ]

    def _classified_meta(self) -> list:
        return []

    def _user_meta(self) -> list:
        return [
            # MetaField(
            #     name="test_bool",
            #     type=MetaTypeEnum.BOOL,
            #     description="bool type test field",
            # ),
            # MetaField(
            #     name="test_float",
            #     type=MetaTypeEnum.FLOAT,
            #     description="float type test field",
            # ),
            # MetaField(
            #     name="test_list",
            #     type=MetaTypeEnum.LIST,
            #     description="list type test field",
            # ),
        ]

    def _datum_list(self) -> list:
        return [
            {
                "pub_date": parse_datetime("2024-10-01"),
                "fiscal_year": 2024,
                "fiscal_quarter": 3,
                "ticker": "1111",
                "v": "UBER example transcript 2024 Q3",
            },
            {
                "pub_date": parse_datetime("2024-07-02"),
                "fiscal_year": 2024,
                "fiscal_quarter": 2,
                "ticker": "1111",
                "v": "UBER example transcript 2024 Q2",
            },
            {
                "pub_date": parse_datetime("2024-10-03"),
                "fiscal_year": 2025,
                "fiscal_quarter": 1,
                "ticker": "2222",
                "v": "DASH example transcript 2025 Q1",
            },
        ]

    def _get_key(self, idx: int) -> str:
        return f"jack-transcript-{idx}"

    def _create_wrappers(self) -> list[DatumWrapper]:
        """insert a piece of datum with metas
        :return:
        """
        metas = self._datum_list()
        wrappers = []
        for i, d in enumerate(metas):
            key = self._get_key(i)
            v = d.pop("v")
            wrapper = self.mgr.create(
                owner=self.org.key,
                key=key,
                value=v,
                meta_dict=d,
            )
            wrapper.save(include_store=False)
            wrappers.append(wrapper)
        return wrappers

    def test_00_create_and_save(self) -> None:
        """insert a piece of datum with metas
        :return:
        """
        fields = self._designed_meta() + self._classified_meta() + self._user_meta()
        metas = self._datum_list()
        for i, wrapper in enumerate(self.wrappers):
            self.assertDatumInDb(wrapper.key, self.org.key)

            meta_name = fields[0].name
            meta_value = metas[i].get(meta_name)
            self.assertEqual(wrapper.meta_kv_dict[meta_name], meta_value)
            self.assertEqual(
                meta_value,
                self.get_meta_value(
                    meta_name,
                    datum_owner=self.org.key,
                    datum_key=wrapper.key,
                ),
            )

    def test_01_get(self) -> None:
        metas = self._datum_list()
        i = 0
        key = self._get_key(i)

        self.assertRaises(
            PermissionError,
            self.mgr.get,
            datum_key=key,
            datum_owner="another_owner",
            preload_content=True,
        )

        wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        self.assertIsNotNone(wrapper)
        assert wrapper is not None
        self.assertEqual(wrapper.get_data(), "")

        for meta in wrapper.meta_obj_list:
            self.assertIn(meta.meta_name, metas[i].keys())
            self.assertEqual(meta.meta_value, metas[i].get(meta.meta_name))

    def test_02_query(self) -> None:
        fields = self._designed_meta() + self._classified_meta() + self._user_meta()

        wrappers = self.mgr.query(fiscal_year=2024)
        self.assertEqual(len(wrappers), 2)
        for wrapper in wrappers:
            self.assertEqual(wrapper.meta_kv_dict["fiscal_year"], 2024)
            meta_keys = set(wrapper.meta_kv_dict.keys())
            field_keys = {f.name for f in fields}
            assert meta_keys == field_keys

        wrappers = self.mgr.query(datum_owner=self.org.key)
        self.assertEqual(len(wrappers), 3)

        wrappers = self.mgr.query(datum_key__endswith="-2")
        self.assertEqual(len(wrappers), 1)
        self.assertEqual(wrappers[0].key, self._get_key(2))

    def test_10_query_without_meta(self) -> None:
        wrappers = self.mgr.query(without_metas=True, order_by=["-datum_created_at"])
        self.assertEqual(len(wrappers), 3)
        for wrapper in wrappers:
            self.assertEqual(wrapper.meta_obj_list, [])
        self.assertEqual(wrappers[0].key, self._get_key(2))  # last created

    def test_11_query_orderby(self) -> None:
        wrappers = self.mgr.query(without_metas=False, order_by=["datum_key"])
        self.assertEqual(len(wrappers), 3)
        for wrapper in wrappers:
            self.assertGreater(len(wrapper.meta_obj_list), 0)
        self.assertEqual(wrappers[0].key, self._get_key(0))  # xxx-0 is first

    def test_20_query_pagination(self) -> None:
        wrappers = self.mgr.query(
            without_metas=True, order_by=["-datum_key"], page=0, page_size=2
        )
        self.assertEqual(len(wrappers), 2)
        self.assertEqual(wrappers[0].key, self._get_key(2))
        self.assertEqual(wrappers[1].key, self._get_key(1))

        wrappers = self.mgr.query(
            without_metas=True, order_by=["-datum_created_at"], page=1, page_size=2
        )
        self.assertEqual(len(wrappers), 1)
        self.assertEqual(wrappers[0].key, self._get_key(0))

    def test_30_update(self) -> None:
        key = self._get_key(0)
        wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        assert wrapper is not None
        wrapper["ticker"] = "TSLA"
        wrapper["fiscal_year"] = 2021
        key = wrapper.key
        wrapper.save()

        wrapper1 = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        assert wrapper1 is not None
        self.assertEqual(wrapper1["ticker"], "TSLA")
        self.assertEqual(wrapper1["fiscal_year"], 2021)

    def test_31_update_invalid_value(self) -> None:
        key = self._get_key(0)
        wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        assert wrapper is not None
        with self.assertRaises(MetaValidationError):
            wrapper["fiscal_year"] = "2021"

        with self.assertRaises(KeyError):
            wrapper["foo"] = "bar"

    def test_40_add_fields(self) -> None:
        key = self._get_key(0)
        f_name = "foo"
        wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        assert wrapper is not None

        field = MetaField(
            name=f_name,
            type=MetaTypeEnum.STR,
            kind=MetaKindEnum.USER,
            description="A new test field with explicit default",
            default="Default Value",
        )
        self.mgr.batch_add_field([wrapper], field, add_to_manager=False)
        wrapper.save(include_store=False)
        updated_wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        self.assertIsNone(updated_wrapper)

        updated_mgr = DatumManager(
            owner=self.org, is_strict=True, fields=self.mgr.fields + [field]
        )
        updated_wrapper = updated_mgr.get(datum_key=key, datum_owner=self.org.key)
        assert updated_wrapper
        self.assertIn(f_name, updated_wrapper.meta_kv_dict)
        self.assertEqual(updated_wrapper[f_name], "Default Value")

        updated_wrapper[f_name] = "New Value"
        updated_wrapper.save()
        reloaded_wrapper = updated_mgr.get(datum_key=key, datum_owner=self.org.key)
        assert reloaded_wrapper
        self.assertEqual(reloaded_wrapper[f_name], "New Value")

        duplicate_field = MetaField(
            name=f_name,
            type=MetaTypeEnum.INT,
            kind=MetaKindEnum.USER,
            description="Duplicate field",
            default=0,
        )
        with self.assertRaises(KeyError):
            updated_mgr.batch_add_field([wrapper], duplicate_field)

        # test when default is None
        field1 = MetaField(
            name="bar",
            type=MetaTypeEnum.DATETIME,
            kind=MetaKindEnum.USER,
            description="A new test field with implicit default",
        )
        self.mgr.batch_add_field([wrapper], field1, add_to_manager=False)
        wrapper.save(include_store=False)

        updated_mgr = DatumManager(
            owner=self.org, is_strict=True, fields=self.mgr.fields + [field, field1]
        )
        updated_wrapper = updated_mgr.get(datum_key=key, datum_owner=self.org.key)
        assert updated_wrapper
        self.assertIsNotNone(updated_wrapper)
        self.assertEqual(datetime.min, updated_wrapper["bar"])

    def test_41_update_fields(self) -> None:
        key = self._get_key(0)
        wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        assert wrapper

        # Test updating field type (fail to convert)
        field_to_update = self.mgr.fields[0]
        new_type_field = MetaField(
            name=field_to_update.name,
            type=MetaTypeEnum.INT,
            kind=field_to_update.kind,
            description=field_to_update.description,
            default=0,
        )
        old_fields_count = len(self.mgr.fields)
        with self.assertRaises(TypeError):
            self.mgr.batch_update_field(
                [wrapper], new_type_field, update_in_manager=False
            )
        self.assertEqual(old_fields_count, len(self.mgr.fields))

        # Test updating field type (success to convert)
        field_to_update = self.mgr.fields[0]
        new_type_field = MetaField(
            name=field_to_update.name,
            type=MetaTypeEnum.INT,
            kind=field_to_update.kind,
            description=field_to_update.description,
            default=0,
        )

        def _convert(old_value: datetime) -> int:
            return old_value.day

        self.mgr.batch_update_field(
            [wrapper], new_type_field, func_convert=_convert, update_in_manager=True
        )
        wrapper.save(include_store=False)
        updated_wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        assert updated_wrapper
        self.assertEqual(1, updated_wrapper[new_type_field.name])

        # Test updating field type (str to int)
        field_to_update = self.mgr.fields[1]
        new_type_field = MetaField(
            name=field_to_update.name,
            type=MetaTypeEnum.INT,
            kind=field_to_update.kind,
            description=field_to_update.description,
            default=0,
        )
        self.mgr.batch_update_field([wrapper], new_type_field, update_in_manager=True)
        wrapper.save(include_store=False)
        updated_wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        assert updated_wrapper
        self.assertEqual(1111, updated_wrapper[new_type_field.name])

        # Test updating non-existent field
        non_existent_field = MetaField(
            name="non_existent",
            type=MetaTypeEnum.STR,
            kind=MetaKindEnum.USER,
            description="Non-existent field",
            default="Default",
        )
        with self.assertRaises(KeyError):
            self.mgr.batch_update_field([wrapper], non_existent_field)

        field1, field2 = self.mgr.fields[:2]

        # Test updating field kind
        kind_update_field = MetaField(
            name=field1.name,
            type=field1.type,
            kind=MetaKindEnum.USER,
            description=field1.description,
            default=field1.default,
        )
        self.mgr.batch_update_field(
            [wrapper], kind_update_field, update_in_manager=True
        )
        wrapper.save(include_store=False)
        updated_wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        assert updated_wrapper
        self.assertEqual(
            updated_wrapper.meta_ko_dict[kind_update_field.name].meta_kind,
            kind_update_field.kind,
        )

        # convert fields back
        def _convert1(old_value: int) -> datetime:
            return parse_datetime(f"2025-1-{old_value}")

        updated_fields = [
            MetaField(
                name=field1.name,
                type=MetaTypeEnum.DATETIME,
                kind=field1.kind,
                description="pub_date",
                default=datetime.now,
            ),
            MetaField(
                name=field2.name,
                type=MetaTypeEnum.STR,
                kind=field2.kind,
                description="ticker",
                default="",
            ),
        ]
        self.mgr.batch_update_field(
            [wrapper], updated_fields[0], update_in_manager=True, func_convert=_convert1
        )
        self.mgr.batch_update_field(
            [wrapper], updated_fields[1], update_in_manager=True
        )
        wrapper.save(include_store=False)

        updated_wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        assert updated_wrapper
        self.assertEqual(
            updated_wrapper.meta_ko_dict[field1.name].meta_value,
            parse_datetime("2025-1-1"),
        )
        self.assertEqual(updated_wrapper.meta_ko_dict[field2.name].meta_value, "1111")

    def test_45_delete_fields(self) -> None:
        key = self._get_key(0)
        wrapper = self.mgr.get(datum_key=key, datum_owner=self.org.key)
        assert wrapper
        field = self.mgr.fields[0]

        # Try to delete a non-existent field
        with self.assertRaises(KeyError):
            self.mgr.batch_del_field([wrapper], "non_existent_field")

        # Try to delete field from strict datum
        with self.assertRaises(VolitionError):
            self.mgr.batch_del_field([wrapper], field.name, del_from_manager=True)

    def test_90_delete_by_filter(self) -> None:
        n = self.mgr.delete_by_filters(
            ticker="1111", fiscal_quarter=3, include_store=False
        )
        self.assertEqual(1, n)
        wrappers = self.mgr.query(without_metas=False, page_size=100)
        self.assertEqual(2, len(wrappers))
        for wrapper in wrappers:
            self.assertNotEquals(
                (wrapper["ticker"], wrapper["fiscal_quarter"]), ("1111", 3)
            )

    def test_91_delete(self) -> None:
        wrapper = self.mgr.get(datum_owner=self.org.key, datum_key=self._get_key(0))
        self.assertIsNotNone(wrapper)
        assert wrapper
        wrapper.delete(include_store=False)
        wrappers = self.mgr.query(without_metas=False, page_size=100)
        self.assertEqual(len(wrappers), 2)
        for wrapper in wrappers:
            self.assertNotEquals(wrapper.key, self._get_key(0))
