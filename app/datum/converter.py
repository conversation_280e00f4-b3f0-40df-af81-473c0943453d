import mimetypes
import tempfile
from abc import abstractmethod, <PERSON>
from io import <PERSON>ufferedReader
from logging import getLogger
from pathlib import Path
from typing import Protocol, Any, Literal, Union, TypeAlias

import pandoc
import pymupdf4llm
from asgiref.sync import async_to_sync

logger = getLogger(__name__)

# ConvIn = TypeVar("ConvIn", bound=Union[str, bytes, BufferedReader, None])
# ConvOut = TypeVar("ConvOut", bound=Union[str, bytes, BufferedReader, None])
ConvIn: TypeAlias = Union[str, bytes, BufferedReader, None]
ConvOut: TypeAlias = Union[str, bytes, BufferedReader, None]
ConvFormat = Literal["html", "markdown"]


class Converter(Protocol):
    """the interface for any convertion operation"""

    # TODO(datum): move me out to shared package such as biz, common.

    @abstractmethod
    async def async_convert(self) -> ConvOut:
        """convert datum content into string format such as <PERSON><PERSON><PERSON>, Markdown, etc."""

    @abstractmethod
    def convert(self) -> ConvOut:
        """convert datum content into string format such as HTML, Markdown, etc."""


class ConverterBase(Converter, ABC):
    """base class for all kinds of converters"""

    data: ConvIn

    def __init__(
        self,
        data: ConvIn = None,
        *args: Any,
        **kwargs: Any,
    ) -> None:
        if data is None:
            self.data = None
            return

        d1 = data.read() if isinstance(data, BufferedReader) else data
        self.data = d1.decode("utf-8") if isinstance(d1, bytes) else d1

    def convert(self) -> ConvOut:
        return async_to_sync(self.async_convert)()

    @staticmethod
    async def detect_content_type(path: str | Path) -> str:
        # this is guessed from file ext
        # TODO(datum): use python-magic to detect from content/file_ext
        result = mimetypes.guess_type(path)
        content_type = result[0] or ""
        logger.debug(f"detected content_type {content_type} - {path}")
        return content_type


class DatumConvBase(ConverterBase, ABC):
    """base class for all datum converters"""


class DefaultDatumConverter(DatumConvBase):
    """convert datum content to HTML, Markdown"""

    fullpath: Path
    content_type: str
    to_format: ConvFormat

    def __init__(
        self,
        data: ConvIn = None,
        fullpath: str | Path = "",
        content_type: str = "",
        to_format: ConvFormat = "html",
        *args: Any,
        **kwargs: Any,
    ) -> None:
        if not (fullpath or data) or (fullpath and data):
            raise ValueError(
                'arguments "fullpath" and "data" must have one and only one.'
            )
        self.fullpath = fullpath if isinstance(fullpath, Path) else Path(fullpath)
        self.content_type = content_type
        self.to_format = to_format
        super().__init__(data, *args, **kwargs)

    async def async_convert(self) -> str:
        if not self.fullpath:
            with tempfile.NamedTemporaryFile() as tmp_file:
                logger.warning(tmp_file.name)
                self.fullpath = Path(tmp_file.name)
        if not self.content_type:
            self.content_type = await self.detect_content_type(self.fullpath)

        if self.content_type == "application/pdf" or self.fullpath.stem == "pdf":
            text = pymupdf4llm.to_markdown(str(self.fullpath))
            if self.to_format != "markdown":
                doc = pandoc.read(source=text)
                if self.to_format == "html":
                    text = pandoc.write(doc=doc, format="html")
        else:
            doc = pandoc.read(file=str(self.fullpath))
            text = pandoc.write(doc=doc, format=self.to_format)

        logger.debug(f"converted - {self.fullpath}")
        return text
