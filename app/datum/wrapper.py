import traceback
from collections import OrderedDict
from datetime import datetime
from logging import getLogger
from typing import Any, Iterable, Callable

from asgiref.sync import sync_to_async
from django.db import transaction, models
from django.db.models import Value, Q, Count
from django.db.models.functions import Concat

from .enums import MetaKindEnum, DatumStatusEnum
from .exceptions import (
    MetaValidationError,
    ContentExistedError,
    VolitionError,
    FilterValidationError,
)
from .models import (
    DatumModel,
    DatumMetaModel,
    MetaField,
    datum_store,
    DatumMeta,
    DatumOwnerModelMixin,
    SUPPORTED_META_PYTHON_TYPES,
    RESERVED_DATUM_FILTER_NAMES,
    DATUM_FIELD_NAMES,
)

logger = getLogger(__name__)
datum_field_names = RESERVED_DATUM_FILTER_NAMES


class DatumWrapper:
    """wrapper for operations of datum object including datum itself & datum metas.

    Strict Datum:
        If schema (meta fields) specified, ALL datum metas will follow the schema.

    Datum (flex datum):
        If schema (meta fields) specified, The datum MUST have metas following the schema.
        And the datum CAN have metas freely created (no schema).
    """

    __is_strict: bool
    __datum: DatumModel
    __meta_model_dict: OrderedDict[str, DatumMetaModel]
    __field_obj_dict: OrderedDict[str, MetaField]

    # for property
    __prop_meta_obj_list: list[DatumMeta]
    __prop_meta_kv_dict: dict[str, Any]
    __prop_meta_ko_dict: dict[str, DatumMeta]

    # def __init__(self) -> None:
    #     raise NotImplementedError(
    #         f"Use {self.__class__.__name__}.create method to construct a datum wrapper object."
    #     )

    def __init__(
        self,
        datum: DatumModel,
        metas: list[DatumMetaModel] = list(),
        fields: list[MetaField] = list(),
        is_strict: bool = False,
    ) -> None:
        if is_strict:
            if len(fields) == 0:
                raise ValueError("Strict datum should have fields defined.")

        self.__is_strict = is_strict
        self.__datum = datum
        self.__meta_model_dict = OrderedDict({m.meta_name: m for m in metas})
        self.__field_obj_dict = OrderedDict({f.name: f for f in fields})
        self.__on_meta_changed()

    def __str__(self) -> str:
        return (
            f"{self.__datum}<{'strict' if self.__is_strict else 'flex'} "
            f"{[str(m) for m in self.__meta_model_list]}>"
        )

    def __repr__(self) -> str:
        return (
            f"<{self.__class__.__name__} datum={self.__datum} strict={self.__is_strict}"
            f" fields={self.__field_obj_list} metas={self.__meta_model_list}"
        )

    def __getitem__(self, key: str) -> Any:
        if key in self.meta_kv_dict.keys():
            return self.meta_kv_dict[key]
        else:
            raise KeyError(f'"{key}" is not a meta name of {self.__datum}')

    def __setitem__(self, key: str, value: Any) -> None:
        f = self.__field_obj_dict.get(key)
        if not f:
            if self.is_strict:
                raise KeyError(f'"{key}" is not meta name of {self}')
            else:
                if key in self.__meta_model_dict.keys():
                    _type = type(value)
                    if not isinstance(value, tuple(SUPPORTED_META_PYTHON_TYPES)):
                        raise TypeError(
                            f"{_type.__name__} is not supported meta value type"
                        )
                    origin_value = self.meta_kv_dict.get(key)
                    origin_type = type(origin_value)
                    if origin_value and _type != origin_type:
                        logger.warning(
                            f"changing meta {key} from {origin_type.__name__} to {_type.__name__}"
                        )
                else:
                    self.__meta_model_dict[key] = self.__create_meta_model(key, value)
        else:
            f.validate_value(value, for_obj=self)

        meta = self.__meta_model_dict[key]
        meta.meta_value = value
        meta.is_changed = True
        self.__on_meta_changed()

    def __delitem__(self, key: str) -> Any:
        if key not in self.__meta_model_dict.keys():
            raise KeyError(f"{key} is not a meta name.")

        if self.is_strict:
            raise VolitionError("can not delete a meta from strict datum.")

        del self.__meta_model_dict[key]
        self.__on_meta_changed()

    @property
    def is_strict(self) -> bool:
        return self.__is_strict

    @property
    def key(self) -> str:
        return self.__datum.key

    @property
    def owner(self) -> str:
        return self.__datum.owner

    @property
    def status(self) -> DatumStatusEnum:
        return self.__datum.status

    @property
    def created_at(self) -> datetime:
        return self.__datum.created_at

    @property
    def updated_at(self) -> datetime:
        return self.__datum.updated_at

    def get_data(self, auto_load: bool = False) -> Any:
        """get real data"""
        if (
            auto_load
            and not self.__datum.content.get_data()
            and not self.__datum.content.was_loaded
        ):
            self.load_from_store()
        return self.__datum.content.get_data()

    async def async_get_data(self, auto_load: bool = False) -> Any:
        return await sync_to_async(self.get_data)(auto_load=auto_load)

    def set_data(self, data: str | bytes) -> None:
        if not isinstance(data, str):
            raise NotImplementedError(f"{type(data)} as data is not implemented.")

        self.__datum.content.set_data(data)

    def update(self, **attrs: Any) -> None:
        """update datum attrs & meta attrs"""

        names = set(attrs.keys())
        for name in names:
            if name in RESERVED_DATUM_FILTER_NAMES:
                setattr(self, name, attrs.pop(name))
            elif name in self.__meta_model_dict:
                self.__setitem__(name, attrs.pop(name))

        if not self.is_strict:
            for name, value in attrs.items():
                self.__meta_model_dict[name] = self.__create_meta_model(name, value)

        self.__on_meta_changed()

    def update_meta(self, **metas: Any) -> None:
        """update only meta attrs"""
        names = set(metas.keys())
        for name in names:
            if name in self.__meta_model_dict:
                self.__setitem__(name, metas.pop(name))

        if not self.is_strict:
            for name, value in metas.items():
                self.__meta_model_dict[name] = self.__create_meta_model(name, value)

        self.__on_meta_changed()

    def __create_meta_model(
        self,
        name: str,
        value: Any,
        kind: MetaKindEnum | str = MetaKindEnum.NOT_SPECIFIED,
    ) -> DatumMetaModel:
        kind = MetaKindEnum(kind) if isinstance(kind, str) else kind
        return DatumMetaModel(
            meta_name=name,
            meta_value=value,
            meta_kind=kind,
            datum_owner=self.__datum.owner,
            datum_key=self.__datum.key,
        )

    # ---- fields & meta -----

    @property
    def meta_obj_list(self) -> list[DatumMeta]:
        """key to value dict"""
        return self.__prop_meta_obj_list

    @property
    def meta_kv_dict(self) -> dict[str, Any]:
        """key to value dict"""
        return self.__prop_meta_kv_dict

    @property
    def meta_ko_dict(self) -> dict[str, DatumMeta]:
        """key to object dict"""
        return self.__prop_meta_ko_dict

    def add_field(self, field: MetaField) -> None:
        """add a field to field list (at the end).
        As well, add corresponding meta with default value"""
        if field.name in self.__field_obj_dict:
            raise KeyError(f"field {field.name} already exists.")

        self.__field_obj_dict[field.name] = field

        if field.default is None:
            value = field.type.default_factory()
        else:
            value = field.default() if callable(field.default) else field.default
        self.__meta_model_dict[field.name] = self.__create_meta_model(
            field.name, value=value, kind=field.kind
        )

        self.__on_meta_changed()

    def update_field(
        self,
        field: MetaField,
        func_covert: Callable[[Any], Any] | None = None,
    ) -> None:
        """update a field in field list by the field name.
        As well, convert the value of this field."""
        if field.name not in self.__field_obj_dict:
            raise KeyError(f"field {field.name} not exists.")

        old_field = self.__field_obj_dict[field.name]
        self.__field_obj_dict[field.name] = field

        old_meta = self.__meta_model_dict[field.name]
        try:
            func = func_covert or field.type.default_converter
            new_value = func(old_meta.meta_value)
            field.validate_value(new_value)
        except Exception as e:
            self.__field_obj_dict[field.name] = old_field
            raise TypeError(
                f"fail to convert meta '{field.name}' value to {field.type.value}. error: {e}"
            )
        old_meta.meta_value = new_value
        old_meta.meta_kind = field.kind
        old_meta.is_changed = True
        self.__meta_model_dict[field.name] = old_meta

        self.__on_meta_changed()

    def del_field(self, name: str) -> tuple[MetaField | None, Any]:
        """delete a field from the field list. remove the meta as well.
        returns tuple of (field, meta value)"""
        if self.is_strict:
            raise VolitionError("Can not delete field from strict datum.")

        try:
            field = self.__field_obj_dict.pop(name)
        except KeyError:
            # flex datum maybe have not this field
            field = None
        # let it raise KeyError if not found.
        value = self.__meta_model_dict.pop(name)
        self.__on_meta_changed()

        return field, value

    def __on_meta_changed(self) -> None:
        # TODO(datum): timeit & optimize it (find a way to not use it)
        self.__prop_meta_obj_list = [
            DatumMeta.from_orm(m) for m in self.__meta_model_list
        ]
        self.__prop_meta_kv_dict = {
            m.meta_name: m.meta_value for m in self.__meta_model_list
        }
        self.__prop_meta_ko_dict = {
            m.meta_name: DatumMeta.from_orm(m) for m in self.__meta_model_list
        }

    @property
    def __meta_model_list(self) -> list[DatumMetaModel]:
        return list(self.__meta_model_dict.values())

    @property
    def __field_obj_list(self) -> list[MetaField]:
        return list(self.__field_obj_dict.values())

    # ----- data -----

    def validate(self) -> None:
        """validate if the datum metas are match fields schema depends on strict or not."""
        field_names = set(self.__field_obj_dict.keys())
        meta_names = set(self.__meta_model_dict.keys())
        matched_names = field_names & meta_names

        if self.is_strict and not (
            len(matched_names) == len(field_names) == len(meta_names)
        ):
            raise MetaValidationError("strict datum should have metas same as fields.")

        if len(matched_names) != len(field_names):
            raise MetaValidationError(
                f"fields {field_names-matched_names} have no meta value"
            )

        for name in matched_names:
            field = self.__field_obj_dict.get(name)
            meta = self.__meta_model_dict.get(name)
            assert field is not None and meta is not None
            field.validate_obj(meta)

    def load_from_store(self) -> None:
        """load the datum from store"""
        datum_store.load(self.__datum.content)

    async def async_load_from_store(self) -> None:
        await sync_to_async(self.load_from_store)()

    def put_to_store(self, overwrite: bool = False) -> None:
        """put the datum to store"""
        content = self.__datum.content
        if content and content.is_dirty:
            if not overwrite and datum_store.exist(content.get_store_key()):
                raise ContentExistedError(
                    f"{content.get_store_key()} is existed in store."
                )
            datum_store.persist(content)

    async def async_put_to_store(self, overwrite: bool = False) -> None:
        await sync_to_async(self.put_to_store)(overwrite)

    def del_from_store(self) -> None:
        """delete the datum from store"""
        datum_store.delete(self.__datum.content)

    async def async_del_from_store(self) -> None:
        await sync_to_async(self.del_from_store)()

    # def get_from_index(self) -> None:
    #     raise NotImplementedError
    #
    # def put_to_index(self, overwrite: bool = False) -> None:
    #     raise NotImplementedError
    #
    # def del_from_index(self) -> None:
    #     raise NotImplementedError

    def save(
        self,
        include_store: bool = False,
        overwrite_store: bool = False,
    ) -> None:
        self.validate()

        with transaction.atomic():
            self.__datum.save()
            existed_meta_name_set = set(
                DatumMetaModel.objects.filter(
                    datum_owner=self.__datum.owner, datum_key=self.__datum.key
                ).values_list("meta_name", flat=True)
            )
            new_meta_name_set = set(self.__meta_model_dict.keys())
            meta_names_to_del = existed_meta_name_set - new_meta_name_set
            meta_names_to_add = new_meta_name_set - existed_meta_name_set
            meta_names_to_update = new_meta_name_set & existed_meta_name_set
            if meta_names_to_del:
                DatumMetaModel.objects.filter(
                    datum_owner=self.__datum.owner,
                    datum_key=self.__datum.key,
                    meta_name__in=meta_names_to_del,
                ).delete()
            if meta_names_to_add:
                DatumMetaModel.objects.bulk_create(
                    [self.__meta_model_dict[k] for k in meta_names_to_add]
                )
            if meta_names_to_update:
                metas = [
                    self.__meta_model_dict[k]
                    for k in meta_names_to_update
                    if self.__meta_model_dict[k].is_changed
                ]
                DatumMetaModel.objects.bulk_update(
                    metas,
                    fields=["meta_value", "meta_kind"],
                )
                for meta in metas:
                    meta.is_changed = False

            if include_store:
                self.put_to_store(overwrite=overwrite_store)

    async def async_save(
        self,
        include_store: bool = False,
        overwrite_store: bool = False,
    ) -> None:
        await sync_to_async(self.save)(
            include_store=include_store, overwrite_store=overwrite_store
        )

    def delete(
        self,
        include_store: bool = True,
    ) -> None:
        with transaction.atomic():
            if include_store:
                try:
                    self.del_from_store()
                except Exception as e:
                    self.save_last_error_and_status(e)
                    raise e

            for meta in self.__meta_model_list:
                meta.delete()
            self.__datum.delete()

    async def async_delete(self, include_store: bool = True) -> None:
        await sync_to_async(self.delete)(include_store=include_store)

    def save_last_error_and_status(self, error: Exception | str) -> None:
        self.set_last_error_and_status(error)
        datum = self.__datum
        datum.save()

    def set_last_error_and_status(self, error: Exception | str) -> None:
        if isinstance(error, NotImplementedError):
            raise error

        datum = self.__datum
        if isinstance(error, Exception):
            sb = [repr(error)]
            sb.extend(traceback.format_exception(error))
            datum.last_error = "\n".join(sb)
            logger.error(
                f"{repr(error)} -> {traceback.format_stack(limit=3)[0].strip()}"
            )
        else:
            assert error, "argument 'error' can not be empty"
            datum.last_error = error + "\n" + "\n".join(traceback.format_stack(limit=5))
            logger.error(f"{error} -> {traceback.format_stack(limit=3)[0].strip()}")

        if datum.status in [DatumStatusEnum.CREATED, DatumStatusEnum.IMPORT_FAIL]:
            datum.status = DatumStatusEnum.IMPORT_FAIL
        elif datum.status in [DatumStatusEnum.IMPORTED, DatumStatusEnum.STORE_FAIL]:
            datum.status = DatumStatusEnum.STORE_FAIL
        elif datum.status in [DatumStatusEnum.STOPPED, DatumStatusEnum.INDEX_FAIL]:
            datum.status = DatumStatusEnum.INDEX_FAIL
        elif datum.status in [
            DatumStatusEnum.INDEXED,
            DatumStatusEnum.DELETE_FAIL_INDEX,
        ]:
            datum.status = DatumStatusEnum.DELETE_FAIL_INDEX
        elif datum.status in [DatumStatusEnum.STOPPED]:
            pass
        else:
            logger.error(
                f"try setting status {datum.status} to fail with error {error} for {self}"
            )
            raise RuntimeError(error)


class DatumManager:
    """Manager for datums DRUD and query.

    Strict Datum:
        If schema (meta fields) specified, ALL datum metas will follow the schema.

    Datum (flex datum):
        If schema (meta fields) specified, The datum MUST have metas following the schema.
        And the datum CAN have metas freely created (no schema).
    """

    __owner_model_obj: DatumOwnerModelMixin

    # we keep only str owner info in case we will change it one day
    # (use another model or another way) or (more than 1 sys owners)
    __owner: str
    __sys_owner: str
    __is_strict: bool
    __fields: list[MetaField] = []

    def __init__(
        self,
        owner: DatumOwnerModelMixin,
        is_strict: bool = False,
        fields: list[MetaField] = list(),
    ) -> None:
        if (
            self.__class__ is DatumManager
            and owner.key == DatumOwnerModelMixin.get_sys_key()
        ):
            raise ValueError(
                f"Can not create {self.__class__.__name__} with system owner."
            )

        if is_strict and len(fields) == 0:
            raise ValueError("Strict datums should have fields defined.")

        fields = fields or []
        self.__owner_model_obj = owner
        self.__owner = self.__owner_model_obj.key
        self.__sys_owner = DatumOwnerModelMixin.get_sys_key()
        self.__is_strict = is_strict
        self.__fields = fields

    @property
    def i_am_sys_owner(self) -> bool:
        return self.__owner == self.__sys_owner

    @property
    def fields(self) -> list[MetaField]:
        return self.__fields

    @fields.setter
    def fields(self, fields: list[MetaField]) -> None:
        self.__fields = fields

    def _check_ownership(self, owner: str) -> str:
        """check if given owner is permitted to operate on this manager.

        :param owner: given owner's key
        :return: manager's owner if empty, else given owner.
        """
        if not owner:
            return self.__owner
        if not self.i_am_sys_owner:
            if owner != self.__owner:
                raise PermissionError("Can not create Datum for other user.")
        return owner

    def __wrap_new(
        self, datum: DatumModel, metas: list[DatumMetaModel] = list()
    ) -> DatumWrapper:
        """create a datum wrapper object by given datum & meta list.
        this method will NOT validate meta by fields because this method
        is used for initializing a NEW wrapper.

        To wrapper a fetched result, use __wrap_fetched()

        :param datum: the datum to be wrapped
        :param metas: the meta list of given datum to be wrapped
        :return: datum wrapper
        """
        metas = metas or []
        wrapper = DatumWrapper(
            datum=datum, metas=metas, fields=self.__fields, is_strict=self.__is_strict
        )
        return wrapper

    def __wrap_fetched(
        self, datum: DatumModel, metas: list[DatumMetaModel] = list()
    ) -> DatumWrapper:
        """wrapped FETCHED result of datum & meta list in to a datum wrapper.
        this method will VALIDATE meta by fields.

        To create a new wrapper from scratch, use __wrap_new()

        :param datum: the datum to be wrapped
        :param metas: the meta list of given datum to be wrapped
        :return: datum wrapper
        """
        metas = metas or []
        wrapper = DatumWrapper(
            datum=datum, metas=metas, fields=self.__fields, is_strict=self.__is_strict
        )
        if len(metas) > 0:
            wrapper.validate()
        return wrapper

    def create(
        self,
        key: str,
        value: str | bytes,
        owner: str = "",
        meta_dict: dict[str, Any] = dict(),
    ) -> DatumWrapper:
        """create a datum (wrapped) instance

        :param key: datum key
        :param value: datum value (the real content wich will be stored)
        :param owner: datum owner, leave it blank. (only system owner can create for other users.)
        :param meta_dict: the key-value pairs metadata for this datum.
        :return: a wrapper of datum.
        """

        owner = self._check_ownership(owner)

        meta_dict = meta_dict or {}

        datum = DatumModel(owner=owner, key=key, value=value)
        metas = [
            DatumMetaModel(datum_owner=owner, datum_key=key, meta_name=k, meta_value=v)
            for k, v in meta_dict.items()
        ]
        wrapper = self.__wrap_new(datum=datum, metas=metas)
        return wrapper

    async def async_create(
        self,
        key: str,
        value: str | bytes,
        owner: str = "",
        meta_dict: dict[str, Any] = dict(),
    ) -> DatumWrapper:
        return await sync_to_async(self.create)(
            key=key, value=value, owner=owner, meta_dict=meta_dict
        )

    def get(
        self, datum_key: str, datum_owner: str = "", preload_content: bool = False
    ) -> DatumWrapper | None:
        """get a datum from database.

        :param datum_key: datum key
        :param datum_owner: datum owner, leave it blank. (only system owner can get datum of users.)
        :param preload_content: whether load the content from store. default False.
        :return: a wrapper of datum loaded from DB & Store.
        """
        datum_owner = self._check_ownership(datum_owner)

        try:
            datum = DatumModel.objects.get(owner=datum_owner, key=datum_key)
            metas = DatumMetaModel.objects.filter(
                datum_owner=datum_owner, datum_key=datum_key
            )
        except (DatumModel.DoesNotExist, DatumMetaModel.DoesNotExist):
            return None
        try:
            wrapper = self.__wrap_fetched(datum=datum, metas=list(metas))
            if preload_content:
                wrapper.load_from_store()
            return wrapper
        except MetaValidationError as e:
            logger.warning(f"meta of {datum} does not pass validation. {e}")
            return None

    async def async_get(
        self, datum_key: str, datum_owner: str = "", preload_content: bool = False
    ) -> DatumWrapper | None:
        return await sync_to_async(self.get)(
            datum_key=datum_key,
            datum_owner=datum_owner,
            preload_content=preload_content,
        )

    def get_or_create(
        self,
        datum_key: str,
        datum_owner: str = "",
        meta_dict: dict[str, Any] = dict(),
        preload_content: bool = False,
    ) -> tuple[DatumWrapper, bool]:
        datum_owner = self._check_ownership(datum_owner)

        meta_dict = meta_dict or {}
        datum, created = DatumModel.objects.get_or_create(
            owner=datum_owner, key=datum_key
        )
        if created:
            metas = [
                DatumMetaModel(
                    datum_owner=datum_owner,
                    datum_key=datum_key,
                    meta_name=k,
                    meta_value=v,
                )
                for k, v in meta_dict.items()
            ]
            wrapper = self.__wrap_new(datum=datum, metas=metas)
        else:
            metas = DatumMetaModel.objects.filter(
                datum_owner=datum_owner, datum_key=datum_key, **meta_dict
            )
            wrapper = self.__wrap_fetched(datum=datum, metas=list(metas))

        if preload_content:
            wrapper.load_from_store()
        return wrapper, created

    async def async_get_or_create(
        self,
        datum_key: str,
        datum_owner: str = "",
        meta_dict: dict[str, Any] = dict(),
        preload_content: bool = False,
    ) -> tuple[DatumWrapper, bool]:
        return await sync_to_async(self.get_or_create)(
            datum_key=datum_key,
            datum_owner=datum_owner,
            meta_dict=meta_dict,
            preload_content=preload_content,
        )

    def bulk_save(
        self,
        wrappers: Iterable[DatumWrapper],
        include_store: bool = False,
        overwrite_store: bool = False,
        ignore_owner: bool = False,
        ignore_error: bool = False,
    ) -> int:
        """bulk save datums (wrappers)

        :param wrappers: wrappers to be saved
        :param include_store: whether save content to store.
        :param overwrite_store: whether overwrite store content if existed.
        :param ignore_owner: if a wrapper's owner is not same as manager's owner, whether raise exception.
                (NOTE: it's not meant to save other's datum for normal manager)
                When manager is SystemDatumManager
                - if True, can save any owner's datum
                - if False, can only save system-owner's datum
        :param ignore_error: whether raise other exceptions.
        :return: how many datum (wrappers) are saved.
        """
        wrappers_to_save = []
        for w in wrappers:
            if w.owner == self.__owner:
                wrappers_to_save.append(w)
            else:
                if ignore_owner:
                    if self.i_am_sys_owner:
                        wrappers_to_save.append(w)
                    else:
                        logger.debug(
                            f"ignore saving datum (wrapper) of other user. {w}"
                        )
                else:
                    raise PermissionError(
                        f"Can not save datum (wrapper) for other user. {w}"
                    )

        n = 0
        for w in wrappers_to_save:
            try:
                w.save(include_store=include_store, overwrite_store=overwrite_store)
                n += 1
            except Exception as e:
                if ignore_error:
                    logger.error(f"error saving datum (wrapper) {w}. {e}")
                else:
                    raise e
        return n

    async def async_bulk_save(
        self,
        wrappers: Iterable[DatumWrapper],
        include_store: bool = False,
        overwrite_store: bool = False,
        ignore_owner: bool = False,
        ignore_error: bool = False,
    ) -> int:
        """bulk save datums (wrappers)

        :param wrappers: wrappers to be saved
        :param include_store: whether save content to store.
        :param overwrite_store: whether overwrite store content if existed.
        :param ignore_owner: if a wrapper's owner is not same as manager's owner, whether raise exception.
                (NOTE: it's not meant to save other's datum for normal manager)
                When manager is SystemDatumManager
                - if True, can save any owner's datum
                - if False, can only save system-owner's datum
        :param ignore_error: whether raise other exceptions.
        :return: how many datum (wrappers) are saved.
        """
        return await sync_to_async(self.bulk_save)(
            wrappers=wrappers,
            include_store=include_store,
            overwrite_store=overwrite_store,
            ignore_owner=ignore_owner,
            ignore_error=ignore_error,
        )

    def delete_by_full_key(
        self,
        full_key: str,
        include_store: bool = True,
    ) -> int:
        """delete by datum full key (pattern '{owner}:{key}')"""
        owner, key = full_key.split(":")
        assert owner and key
        owner = self._check_ownership(owner)
        return self.delete_by_filters(
            datum_owner=owner, datum_key=key, include_store=include_store
        )

    async def async_delete_by_full_key(
        self,
        full_key: str,
        include_store: bool = True,
    ) -> int:
        return await sync_to_async(self.delete_by_full_key)(
            full_key=full_key, include_store=include_store
        )

    def delete_by_filters(
        self,
        include_store: bool = True,
        **filters: Any,
    ) -> int:
        if not filters:
            raise ValueError("No filter specified. You are deleting ALL datums!")

        # owner = filters.get("owner") or ""
        # owner = self._check_ownership(owner)
        # filters["owner"] = owner

        # TODO(datum): to be optimized (use queryset to delete)
        count = 0
        with transaction.atomic():
            while True:
                wrappers = self.query(page_size=100, **filters)
                if not wrappers:
                    break
                for wrapper in wrappers:
                    wrapper.delete(include_store=include_store)
                    count += 1
        return count

    async def async_delete_by_filters(
        self,
        include_store: bool = True,
        **filters: Any,
    ) -> int:
        return await sync_to_async(self.delete_by_filters)(
            include_store=include_store, **filters
        )

    def batch_delete(
        self, datum_wrappers: Iterable[DatumWrapper], include_store: bool = True
    ) -> None:
        """batch delete multiple wrappers."""
        # TODO(datum): do not use cls.delete(). use queryset to delete.
        owner = self.__owner
        with transaction.atomic():
            for wrapper in datum_wrappers:
                assert wrapper.owner == owner
                wrapper.delete(include_store=include_store)

    async def async_batch_delete(
        self, datum_wrappers: Iterable[DatumWrapper], include_store: bool = True
    ) -> None:
        return await sync_to_async(self.batch_delete)(
            datum_wrappers=datum_wrappers, include_store=include_store
        )

    def batch_add_field(
        self,
        datum_wrappers: Iterable[DatumWrapper],
        field: MetaField,
        add_to_manager: bool = False,
    ) -> None:
        """batch add a field to all given datum wrappers."""
        for wrapper in datum_wrappers:
            wrapper.add_field(field)
        if add_to_manager:
            self.fields.append(field)

    async def async_batch_add_field(
        self,
        datum_wrappers: Iterable[DatumWrapper],
        field: MetaField,
        add_to_manager: bool = False,
    ) -> None:
        return await sync_to_async(self.batch_add_field)(
            datum_wrappers=datum_wrappers, field=field, add_to_manager=add_to_manager
        )

    def batch_update_field(
        self,
        datum_wrappers: Iterable[DatumWrapper],
        field: MetaField,
        func_convert: Callable[[Any], Any] | None = None,
        update_in_manager: bool = False,
    ) -> None:
        """batch update a field in all given datum wrappers."""
        for wrapper in datum_wrappers:
            wrapper.update_field(field, func_covert=func_convert)
        if update_in_manager:
            for i, f in enumerate(self.__fields):
                if f.name == field.name:
                    self.__fields[i] = field
                    break

    async def async_batch_update_field(
        self,
        datum_wrappers: Iterable[DatumWrapper],
        field: MetaField,
        func_convert: Callable[[Any], Any] | None = None,
        update_in_manager: bool = False,
    ) -> None:
        return await sync_to_async(self.batch_update_field)(
            datum_wrappers=datum_wrappers,
            field=field,
            func_convert=func_convert,
            update_in_manager=update_in_manager,
        )

    def batch_del_field(
        self,
        datum_wrappers: Iterable[DatumWrapper],
        field_name: str,
        del_from_manager: bool = False,
    ) -> list[tuple[MetaField | None, Any]]:
        """batch delete a field from all given datum wrappers.
        And must be deleted in strict manager

        returns list of [tuple of (field, meta value)]
        """
        field_to_del, idx_to_del = None, -1
        for i, f in enumerate(self.__fields):
            if f.name == field_name:
                field_to_del = self.__fields[i]
                idx_to_del = i
                break

        if self.__is_strict and not field_to_del:
            raise KeyError(f"{field_name} is not a field of strict manager")

        deleted_fields_metas = [
            wrapper.del_field(field_name) for wrapper in datum_wrappers
        ]

        if del_from_manager and idx_to_del >= 0:
            del self.__fields[idx_to_del]

        return deleted_fields_metas

    async def async_batch_del_field(
        self,
        datum_wrappers: Iterable[DatumWrapper],
        field_name: str,
        del_from_manager: bool = False,
    ) -> list[tuple[MetaField | None, Any]]:
        return await sync_to_async(self.batch_del_field)(
            datum_wrappers=datum_wrappers,
            field_name=field_name,
            del_from_manager=del_from_manager,
        )

    # def __query_1_DEPRECATED_to_be_fixed(
    #     self,
    #     without_metas: bool = False,
    #     order_by: list[str] = list(),
    #     page_size: int = 20,
    #     page: int = 0,
    #     **filters: Any,
    # ) -> list[DatumWrapper]:
    #     """query datums by Django queryset filters
    #
    #     :param without_metas: do not query metas. only returns wrapper with datums.
    #     :param order_by: which Datum fields will be used as order. Django filter like syntax. e.g. "+created_at", "-name"
    #     :param page_size: how many datums will be returned. 0 < limit <= 100. default 20
    #     :param page: which page should return. 0 based. default is 0
    #     :param filters: Django queryset-like filters.
    #     :return: list or datum wrappers.
    #     """
    #
    #     # TODO(datum): This implementation need to be fixed. Debug info :
    #     #   Django ORM run returns error:
    #     #   E       django.db.utils.OperationalError: no such column: datum_datummetamodel.datum_owner
    #     #   .
    #     #   Copy the query and made some required change, it works as below.
    #     """
    #         Worked SQL:
    #
    #         SELECT "datum_datummodel"."id",
    #     "datum_datummodel"."created_at",
    #     "datum_datummodel"."updated_at",
    #     "datum_datummodel"."key",
    #     "datum_datummodel"."owner",
    #     "datum_datummodel"."status",
    #     "datum_datummodel"."last_error",
    #     CONCAT("datum_datummodel"."owner", ':', "datum_datummodel"."key") AS "fullkey"
    # FROM "datum_datummodel"
    # WHERE (
    #        	"datum_datummodel"."owner" = 'eval_user'
    #         AND EXISTS(
    #             SELECT 1 AS 'a'
    #             FROM "datum_datummetamodel" U0
    #             WHERE (
    #             		concat(U0."datum_owner", ':', U0."datum_key") = concat("datum_datummodel"."owner", ':', "datum_datummodel"."key")
    #                 	AND (
    # 	                    ( U0."meta_name" = 'fiscal_year' AND U0."meta_value" = 2023 )
    # 	                    OR
    # 	                	( U0."meta_name" = 'ticker' AND U0."meta_value" = '"DASH"' )
    # 	               )
    # 	        )
    #         	GROUP BY concat(U0.datum_owner, ':', U0.datum_key)
    #             HAVING count(1) >=2
    # 			LIMIT 1
    #         )
    #     )
    # LIMIT 20
    # ;
    #
    #         """
    #
    #     owner = filters.get("owner") or ""
    #     owner = self._check_ownership(owner)
    #     filters["owner"] = owner
    #
    #     order_by = order_by or []
    #
    #     if not 0 < page_size <= 100:
    #         raise ValueError('"page_size" must be between range (0, 100] .')
    #     if 0 > page:
    #         raise ValueError('"page" must be >= 0.')
    #
    #     datum_filters, meta_filters, _ = self._separate_filters(**filters)
    #
    #     meta_subquery = (
    #         DatumMetaModel.objects.annotate(
    #             datum_fullkey=Concat(
    #                 "datum_owner",
    #                 Value(":"),
    #                 "datum_key",
    #                 output_field=models.CharField(),
    #             )
    #         )
    #         .filter(datum_fullkey=OuterRef("fullkey"))
    #         .filter(self.mf2q(**meta_filters))
    #         .values_list("datum_fullkey", flat=True)
    #         .annotate(cnt=Count(1))
    #         .filter(cnt__gte=len(meta_filters))
    #     )
    #
    #     qs_datum = (
    #         DatumModel.objects.annotate(
    #             fullkey=Concat(
    #                 "owner", Value(":"), "key", output_field=models.CharField()
    #             )
    #         )
    #         .filter(**datum_filters)
    #         .filter(Exists(meta_subquery))
    #         .order_by(*order_by)
    #     )[page * page_size : (page + 1) * page_size]
    #
    #     if without_metas:
    #         wrappers = [self.__wrap_fetched(datum=d) for d in qs_datum]
    #     else:
    #         metas_dict = {}
    #         datums_dict = OrderedDict({d.full_key: d for d in qs_datum})
    #         for meta in DatumMetaModel.objects.filter(
    #             datum_key__in=qs_datum.values("key"),
    #             datum_owner__in=qs_datum.values("owner"),
    #         ):
    #             meta_list = metas_dict.get(meta.datum_full_key) or []
    #             meta_list.append(meta)
    #             metas_dict[meta.datum_full_key] = meta_list
    #
    #         wrappers = []
    #         for datum_full_key, datum in datums_dict.items():
    #             meta_list = metas_dict[datum_full_key]
    #             try:
    #                 wrapper = self.__wrap_fetched(datum=datum, metas=meta_list)
    #                 wrappers.append(wrapper)
    #             except MetaValidationError as e:
    #                 logger.debug(f"meta of {datum} not pass validation. {e}")
    #
    #     return wrappers

    def _query_backup(
        self,
        q_meta: Q = Q(),
        without_metas: bool = False,
        order_by: list[str] = list(),
        page_size: int = 20,
        page: int = 0,
        **filters: Any,
    ) -> list[DatumWrapper]:
        """query datums by Django queryset or filters.

        `q_meta` is only for meta filter. never use MF() AF() together by & or |.
        Meta filter and Attr filter can be mixed in **filters ONLY.

        `datum_owner` is a special filter.
        1. If `datum_owner` not in filter, means query from all current user's datum. (manager's owner)
        2. If `datum_owner` in filter:
            2.1 If manager's owner is system-owner, query from this owner's datum.
            2.2 If manager's owner is not system-owner, raise PermissionError if `datum_owner` is not self (same as manager's owner).
        3. If manager is SystemDatumManger (means owner is system-owner):
            3.1 filter `owner=None`, means query from all owner's datums.
            3.2 filter `owner__in=['owner1', 'owner2', ...]`, means query from specified owners' datums.
            3.3 filter `owner__<lookup_filter>=<lookup_expression>`, same as Django filter lookup syntax.
        4. A manager with system-owner can only be created from SystemDatumManager.

        :param q_meta: Django Q object query for meta (no datum filter). Generated by MF() method.
        :param without_metas: do not query metas. only returns wrapper with datums.
        :param order_by: which Datum attributes (not meta) will be used as order. Django filter like syntax. e.g. "+datum_created_at", "-datum_key"
        :param page_size: how many datums will be returned. 0 < limit <= 100. default 20
        :param page: which page should return. 0 based. default is 0
        :param filters: Django queryset-like filters.
        :return: list or datum wrappers.
        """

        # self._check_filters(**filters)
        # owner = filters.get("owner") or ""
        # owner = self._check_ownership(owner)
        # filters["owner"] = owner

        order_by = self._resolve_order_by(order_by)

        if not 0 < page_size <= 100:
            raise ValueError('"page_size" must be between range (0, 100] .')
        if 0 > page:
            raise ValueError('"page" must be >= 0.')

        datum_filters, meta_filters, meta_count = self._separate_filters(**filters)
        q_meta = q_meta & MF(**meta_filters)
        q_attr = AF(**datum_filters)

        qs_datum_fullkey = (
            DatumMetaModel.objects.annotate(
                datum_fullkey=Concat(
                    "datum_owner",
                    Value(":"),
                    "datum_key",
                    output_field=models.CharField(),
                )
            )
            .filter(q_meta)
            .values_list("datum_fullkey", flat=True)
            .annotate(cnt=Count(1))
            .filter(cnt__gte=meta_count)
        )

        datum_fullkeys = {fk for fk in qs_datum_fullkey.all()}

        qs_datum = (
            DatumModel.objects.annotate(
                fullkey=Concat(
                    "owner", Value(":"), "key", output_field=models.CharField()
                )
            )
            .filter(q_attr)
            .filter(fullkey__in=datum_fullkeys)
            .order_by(*order_by)
        )[page * page_size : (page + 1) * page_size]

        if without_metas:
            wrappers = [self.__wrap_fetched(datum=d) for d in qs_datum]
        else:
            metas_dict: dict[str, list[DatumMetaModel]] = {}
            datums_dict = OrderedDict({d.full_key: d for d in qs_datum})
            for meta in DatumMetaModel.objects.annotate(
                datum_fullkey=Concat(
                    "datum_owner",
                    Value(":"),
                    "datum_key",
                    output_field=models.CharField(),
                )
            ).filter(datum_fullkey__in=datums_dict.keys()):
                meta_list = metas_dict.get(meta.datum_full_key) or []
                meta_list.append(meta)
                metas_dict[meta.datum_full_key] = meta_list

            wrappers = []
            for datum_full_key, datum in datums_dict.items():
                meta_list = metas_dict[datum_full_key]
                try:
                    wrapper = self.__wrap_fetched(datum=datum, metas=meta_list)
                    wrappers.append(wrapper)
                except MetaValidationError as e:
                    logger.debug(f"meta of {datum} not pass validation. {e}")

        return wrappers

    def query(
        self,
        without_metas: bool = False,
        order_by: list[str] = list(),
        page_size: int = 20,
        page: int = 0,
        **filters: Any,
    ) -> list[DatumWrapper]:
        """query datums by Django queryset or filters.

        `datum_owner` is a special filter.
        1. If `datum_owner` not in filter, means query from all current user's datum. (manager's owner)
        2. If `datum_owner` in filter:
            2.1 If manager's owner is system-owner, query from this owner's datum.
            2.2 If manager's owner is not system-owner, raise PermissionError if `datum_owner` is not self (same as manager's owner).
        3. If manager is SystemDatumManger (means owner is system-owner):
            3.1 filter `owner=None`, means query from all owner's datums.
            3.2 filter `owner__in=['owner1', 'owner2', ...]`, means query from specified owners' datums.
            3.3 filter `owner__<lookup_filter>=<lookup_expression>`, same as Django filter lookup syntax.
        4. A manager with system-owner can only be created from SystemDatumManager.

        :param without_metas: do not query metas. only returns wrapper with datums.
        :param order_by: which Datum attributes (not meta) will be used as order. Django filter like syntax. e.g. "+datum_created_at", "-datum_key"
        :param page_size: how many datums will be returned. 0 < limit <= 100. default 20
        :param page: which page should return. 0 based. default is 0
        :param filters: Django queryset-like filters.
        :return: list or datum wrappers.
        """

        # self._check_filters(**filters)
        # owner = filters.get("owner") or ""
        # owner = self._check_ownership(owner)
        # filters["owner"] = owner

        order_by = self._resolve_order_by(order_by)

        if not 0 < page_size <= 100:
            raise ValueError('"page_size" must be between range (0, 100] .')
        if 0 > page:
            raise ValueError('"page" must be >= 0.')

        datum_filters, meta_filters, meta_count = self._separate_filters(**filters)
        q_meta = MF(**meta_filters)
        q_attr = AF(**datum_filters)

        qs_datum_fullkey = (
            DatumMetaModel.objects.annotate(
                datum_fullkey=Concat(
                    "datum_owner",
                    Value(":"),
                    "datum_key",
                    output_field=models.CharField(),
                )
            )
            .filter(q_meta)
            .values_list("datum_fullkey", flat=True)
            .annotate(cnt=Count(1))
            .filter(cnt__gte=meta_count)
        )

        datum_fullkeys = {fk for fk in qs_datum_fullkey.all()}

        qs_datum = (
            DatumModel.objects.annotate(
                fullkey=Concat(
                    "owner", Value(":"), "key", output_field=models.CharField()
                )
            )
            .filter(q_attr)
            .filter(fullkey__in=datum_fullkeys)
            .order_by(*order_by)
        )[page * page_size : (page + 1) * page_size]

        if without_metas:
            wrappers = [self.__wrap_fetched(datum=d) for d in qs_datum]
        else:
            metas_dict: dict[str, list[DatumMetaModel]] = {}
            datums_dict = OrderedDict({d.full_key: d for d in qs_datum})
            for meta in DatumMetaModel.objects.annotate(
                datum_fullkey=Concat(
                    "datum_owner",
                    Value(":"),
                    "datum_key",
                    output_field=models.CharField(),
                )
            ).filter(datum_fullkey__in=datums_dict.keys()):
                meta_list = metas_dict.get(meta.datum_full_key) or []
                meta_list.append(meta)
                metas_dict[meta.datum_full_key] = meta_list

            wrappers = []
            for datum_full_key, datum in datums_dict.items():
                meta_list = metas_dict[datum_full_key]
                try:
                    wrapper = self.__wrap_fetched(datum=datum, metas=meta_list)
                    wrappers.append(wrapper)
                except MetaValidationError as e:
                    logger.debug(f"meta of {datum} not pass validation. {e}")

        return wrappers

    async def async_query(
        self,
        without_metas: bool = False,
        order_by: list[str] = list(),
        page_size: int = 20,
        page: int = 0,
        **filters: Any,
    ) -> list[DatumWrapper]:
        f"""{DatumManager.query.__doc__}"""
        return await sync_to_async(self.query)(
            without_metas=without_metas,
            order_by=order_by,
            page_size=page_size,
            page=page,
            **filters,
        )

    def query_or(
        self,
        *or_filters: dict[str, Any],
        without_metas: bool = False,
        order_by: list[str] = list(),
        page_size: int = 20,
        page: int = 0,
    ) -> list[DatumWrapper]:
        """query datums by Django queryset or filters.

        `datum_owner` is a special filter.
        1. If `datum_owner` not in filter, means query from all current user's datum. (manager's owner)
        2. If `datum_owner` in filter:
            2.1 If manager's owner is system-owner, query from this owner's datum.
            2.2 If manager's owner is not system-owner, raise PermissionError if `datum_owner` is not self (same as manager's owner).
        3. If manager is SystemDatumManger (means owner is system-owner):
            3.1 filter `owner=None`, means query from all owner's datums.
            3.2 filter `owner__in=['owner1', 'owner2', ...]`, means query from specified owners' datums.
            3.3 filter `owner__<lookup_filter>=<lookup_expression>`, same as Django filter lookup syntax.
        4. A manager with system-owner can only be created from SystemDatumManager.

        :param or_filters: multiple filters which are combine as OR logic.
        :param without_metas: do not query metas. only returns wrapper with datums.
        :param order_by: which Datum attributes (not meta) will be used as order. Django filter like syntax. e.g. "+datum_created_at", "-datum_key"
        :param page_size: how many datums will be returned. 0 < limit <= 100. default 20
        :param page: which page should return. 0 based. default is 0
        :return: list or datum wrappers.
        """

        # self._check_filters(**filters)
        # owner = filters.get("owner") or ""
        # owner = self._check_ownership(owner)
        # filters["owner"] = owner

        order_by = self._resolve_order_by(order_by)

        if not 0 < page_size <= 100:
            raise ValueError('"page_size" must be between range (0, 100] .')
        if 0 > page:
            raise ValueError('"page" must be >= 0.')

        datum_fullkeys = set()
        for filters in or_filters:
            datum_fullkeys |= self._query_full_keys_by_filter(**filters)

        qs_datum = (
            DatumModel.objects.annotate(
                fullkey=Concat(
                    "owner", Value(":"), "key", output_field=models.CharField()
                )
            )
            .filter(fullkey__in=datum_fullkeys)
            .order_by(*order_by)
        )[page * page_size : (page + 1) * page_size]

        if without_metas:
            wrappers = [self.__wrap_fetched(datum=d) for d in qs_datum]
        else:
            metas_dict: dict[str, list[DatumMetaModel]] = {}
            datums_dict = OrderedDict({d.full_key: d for d in qs_datum})
            for meta in DatumMetaModel.objects.annotate(
                datum_fullkey=Concat(
                    "datum_owner",
                    Value(":"),
                    "datum_key",
                    output_field=models.CharField(),
                )
            ).filter(datum_fullkey__in=datums_dict.keys()):
                meta_list = metas_dict.get(meta.datum_full_key) or []
                meta_list.append(meta)
                metas_dict[meta.datum_full_key] = meta_list

            wrappers = []
            for datum_full_key, datum in datums_dict.items():
                meta_list = metas_dict[datum_full_key]
                try:
                    wrapper = self.__wrap_fetched(datum=datum, metas=meta_list)
                    wrappers.append(wrapper)
                except MetaValidationError as e:
                    logger.debug(f"meta of {datum} not pass validation. {e}")

        return wrappers

    async def async_query_or(
        self,
        *or_filters: dict[str, Any],
        without_metas: bool = False,
        order_by: list[str] = list(),
        page_size: int = 20,
        page: int = 0,
    ) -> list[DatumWrapper]:
        """see query_or()"""
        return await sync_to_async(self.query_or)(
            *or_filters,
            without_metas=without_metas,
            order_by=order_by,
            page_size=page_size,
            page=page,
        )

    def _query_full_keys_by_filter(self, **filters: Any) -> set[str]:
        datum_filters, meta_filters, meta_count = self._separate_filters(**filters)
        q_meta = MF(**meta_filters)

        qs_datum_fullkey = (
            DatumMetaModel.objects.annotate(
                datum_fullkey=Concat(
                    "datum_owner",
                    Value(":"),
                    "datum_key",
                    output_field=models.CharField(),
                )
            )
            .filter(q_meta)
            .values_list("datum_fullkey", flat=True)
            .annotate(cnt=Count(1))
            .filter(cnt__gte=meta_count)
        )

        datum_fullkeys = {fk for fk in qs_datum_fullkey.all()}

        q_attr = AF(**datum_filters)
        qs_datum = (
            DatumModel.objects.annotate(
                fullkey=Concat(
                    "owner", Value(":"), "key", output_field=models.CharField()
                )
            )
            .filter(q_attr)
            .filter(fullkey__in=datum_fullkeys)
        )
        for datum in qs_datum:
            datum_fullkeys.add(datum.full_key)

        return datum_fullkeys

    def _separate_filters(self, **filters: Any) -> tuple[dict, dict, int]:  # noqa:C901
        """separate user specified filters into datum filters & meta filters

        :param filters:
        :return: datum_filters, meta_filters, count of how many filters for meta
        """
        datum_filters = {}
        meta_filters = {}

        exists_owner = False
        meta_filter_count = 0

        if self.__is_strict:
            names = {f.name for f in self.__fields}

        for k, v in filters.items():
            parts = k.split("__", 1)
            name = parts[0]
            if name == "datum_owner":
                exists_owner = True
                if self.i_am_sys_owner:
                    if v is None:
                        # owner=None, means query all owner.
                        continue
                else:
                    if v != self.__owner:
                        raise PermissionError(
                            f"Can not query datums of other owner ({k}={v})."
                        )

            if name in DATUM_FIELD_NAMES:
                logger.warning(
                    f'You are using filter "{name}". Note it is a datum meta filter, not datum attribute filter. '
                    f'Please use "datum_{name}" (add prefix "datum_") if you want to filter by datum attributes.'
                )

            if name in RESERVED_DATUM_FILTER_NAMES:
                datum_filters[k] = v
                if name in ["datum_owner", "datum_key"]:
                    # add to meta filters as well.
                    meta_filters[k] = v
            else:
                if self.__is_strict:
                    if name not in names:
                        raise KeyError(f"{name} is not a field of strict manager")
                meta_filters[k] = v
                meta_filter_count += 1

        if not exists_owner:
            datum_filters["datum_owner"] = self.__owner
            meta_filters["datum_owner"] = self.__owner

        return datum_filters, meta_filters, meta_filter_count

    @staticmethod
    def _resolve_order_by(order_by: list[str]) -> list[str]:
        attr_order_by = []
        for f in order_by or []:
            s, name = (
                (f[:1], f[1:]) if f.startswith("-") or f.startswith("+") else ("", f)
            )
            if name not in RESERVED_DATUM_FILTER_NAMES:
                raise KeyError(
                    f'invalid order_by field "{f}". you may forget to add "datum_" prefix ?'
                )
            attr_order_by.append(s + name.removeprefix("datum_"))
        return attr_order_by


class SysDatumManager(DatumManager):
    def __init__(
        self,
        is_strict: bool = False,
        fields: list[MetaField] = list(),
    ) -> None:
        owner_cls = DatumOwnerModelMixin.get_registered_class()
        if owner_cls is None:
            raise VolitionError(
                "You must create an owner class (inherits DatumOwnerModelMixin) first."
            )
        super().__init__(
            owner=owner_cls.get_sys_inst(), is_strict=is_strict, fields=fields
        )


def MF(q: Q = Q(), **meta_filters: Any) -> Q:
    """Datum meta filter generator. Generates Django Q express style filter by given filters.
    Note:
        1. this method will generate filters by AND logic.
        2. Django Q object can only be at first argument. It's AND logic with **meta_filters.
        3. **meta_filters are key-value argument of "meta_name"__lookup_expression -> "meta_value" pairs.

    - For OR logic, try:

    q1 = MF(a1=1)
    q2 = MF(b1='a')
    q = MF( q1 | q2 )

    - For NOT logic, try
    q = MF(~q1 & q2)
    q = MF(~ (q1 & q2))

    - complex example
    q = MF((MF(a=1) | MF(b='b'))
    q = MF(q1 & MF( MF(c='ccc', d=134) | MF( ~MF(e=546) ) ), f__icontains='foo', g__lgt=23)

    This is Django Q object logic.
    ref: https://docs.djangoproject.com/en/5.1/topics/db/queries/#complex-lookups-with-q

    """

    q1 = Q()
    for k, v in meta_filters.items():
        parts = k.split("__", 1)
        if len(parts) == 2:
            name, condition = parts[0], parts[1]
        elif len(parts) == 1:
            name, condition = parts[0], ""
        else:
            raise FilterValidationError(f'Invalid datum meta filter "{k}"')
        if name in ["datum_owner", "datum_key"]:
            q = q & Q(**{k: v})
            continue
        elif name in RESERVED_DATUM_FILTER_NAMES:
            raise KeyError(f'"{name}" is invalid meta_name for filter.')
        elif name in DATUM_FIELD_NAMES:
            logger.warning(
                f'"{name}" is a attribute of datum. You are using it as meta filter. Ignore if you pretty sure its a meta name.'
                f'If you want to filter by datum attributes, remember to add "datum_" prefix and AF() method.'
            )

        d = {
            "meta_name": name,
            f"meta_value__{condition}" if condition else "meta_value": v,
        }
        q1 = q1 | Q(**d)
    return q & q1


def AF(q: Q = Q(), **attr_filters: Any) -> Q:
    """Datum attribute filter generator. Generates Django Q express style filter by given filters.

    see MF().
    """

    for k, v in attr_filters.items():
        parts = k.split("__", 1)
        if len(parts) == 2:
            name, _ = parts[0], parts[1]
        elif len(parts) == 1:
            name, _ = parts[0], ""
        else:
            raise FilterValidationError(f'Invalid datum attribute filter "{k}"')
        if name not in RESERVED_DATUM_FILTER_NAMES:
            raise KeyError(f'Invalid datum attribute filter "{name}".')
        q = q & Q(**{k.removeprefix("datum_"): v})

    return q
