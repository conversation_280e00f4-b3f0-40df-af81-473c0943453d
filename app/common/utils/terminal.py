import os
import sys

from colorama import Fore, Back, Style
from colorama import init

if sys.platform.startswith("win"):
    from colorama import just_fix_windows_console

    just_fix_windows_console()

init()


def debug(s: str, reset: bool = True, newline: bool = True, flush: bool = True) -> None:
    print(
        Fore.LIGHTBLACK_EX + s + Style.RESET_ALL if reset else "",
        end=os.linesep if newline else None,
        flush=flush,
    )


def info(s: str, reset: bool = True, newline: bool = True, flush: bool = True) -> None:
    print(s, end=os.linesep if newline else None, flush=flush)


def warn(s: str, reset: bool = True, newline: bool = True, flush: bool = True) -> None:
    print(
        Fore.YELLOW + s + Style.RESET_ALL if reset else "",
        end=os.linesep if newline else None,
        flush=flush,
    )


def error(s: str, reset: bool = True, newline: bool = True, flush: bool = True) -> None:
    print(
        Fore.RED + s + Style.RESET_ALL if reset else "",
        end=os.linesep if newline else None,
        flush=flush,
    )


def critical(
    s: str, reset: bool = True, newline: bool = True, flush: bool = True
) -> None:
    print(
        Fore.RED + Back.WHITE + s + Style.RESET_ALL if reset else "",
        end=os.linesep if newline else None,
        flush=flush,
    )


def tip(s: str, reset: bool = True, newline: bool = True, flush: bool = True) -> None:
    print(
        Fore.BLUE + s + Style.RESET_ALL if reset else "",
        end=os.linesep if newline else None,
        flush=flush,
    )


def success(
    s: str, reset: bool = True, newline: bool = True, flush: bool = True
) -> None:
    print(
        Fore.GREEN + s + Style.RESET_ALL if reset else "",
        end=os.linesep if newline else None,
        flush=flush,
    )


def fail(s: str, reset: bool = True, newline: bool = True, flush: bool = True) -> None:
    print(
        Fore.LIGHTRED_EX + s + Style.RESET_ALL if reset else "",
        end=os.linesep if newline else None,
        flush=flush,
    )
