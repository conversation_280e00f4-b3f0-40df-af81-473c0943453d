import asyncio
import time
from contextlib import contextmanager, asynccontextmanager
from datetime import timezone, timedelta, datetime, tzinfo
from functools import wraps
from logging import getLogger
from time import perf_counter
from typing import Callable, Any, Iterator, AsyncIterator

import dateparser
from tzlocal import get_localzone

NYC = timezone(timedelta(hours=-12), "NYC")
CST = timezone(timedelta(hours=+8), "CST")
UTC = timezone.utc
__local_tz = get_localzone()
__offset = datetime.now(__local_tz).utcoffset()
assert __offset is not None
LOCAL_TZ = timezone(__offset, __local_tz.key)

logger = getLogger(__name__)


def parse_datetime(
    dt_str_or_ts: str | int | float | None, tz: tzinfo = UTC
) -> datetime | None:
    if not dt_str_or_ts:
        return None

    dt: datetime | None
    if isinstance(dt_str_or_ts, (int, float)):
        dt = datetime.fromtimestamp(dt_str_or_ts)
    else:
        assert isinstance(dt_str_or_ts, str)
        dt = dateparser.parse(dt_str_or_ts)

    if dt and dt.tzinfo is None:
        dt = dt.replace(tzinfo=tz)
    return dt


def format_datetime(
    dt: datetime | int | float, tz: tzinfo = LOCAL_TZ, for_persist: bool = False
) -> str:
    if isinstance(dt, (int, float)):
        dt = datetime.fromtimestamp(dt, tz=UTC)
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=tz)
    if for_persist:
        return dt.astimezone(tz=tz).isoformat(timespec="microseconds")
    else:
        return dt.astimezone(tz=tz).strftime("%Y-%m-%d %H:%M:%S %Z")


def is_tz_aware(dt: datetime) -> bool:
    return dt.tzinfo is not None and dt.tzinfo.utcoffset(dt) is not None


def timeit(log_func: Callable | None = None) -> Callable:
    """decorator to log func running time"""

    log_func = log_func or logger.debug

    def decorator(func: Callable) -> Callable:
        if not asyncio.iscoroutinefunction(func):

            @wraps(func)
            def wrapper(*args: Any, **kwargs: Any) -> Any:
                start_time = perf_counter()
                try:
                    result = func(*args, **kwargs)
                finally:
                    elapsed_time = perf_counter() - start_time
                    log_func(
                        f"{elapsed_time:.6f} sec - {func.__name__}"  # args:[{args}, {kwargs}]"
                    )
                return result

        else:

            @wraps(func)
            async def wrapper(*args: Any, **kwargs: Any) -> Any:
                start_time = perf_counter()
                try:
                    result = await func(*args, **kwargs)
                finally:
                    elapsed_time = perf_counter() - start_time
                    log_func(
                        f"{elapsed_time:.6f} sec - {func.__name__}"  # args:[{args}, {kwargs}]"
                    )
                return result

        return wrapper

    return decorator


@contextmanager
def total_sleep(total_seconds: float = 1.0) -> Iterator[None]:
    now = time.monotonic()
    try:
        yield
    finally:
        used_seconds = time.monotonic() - now
    time.sleep(total_seconds - used_seconds)


@asynccontextmanager
async def async_total_sleep(total_seconds: float = 1.0) -> AsyncIterator[None]:
    now = time.monotonic()
    try:
        yield
    finally:
        used_seconds = time.monotonic() - now
    await asyncio.sleep(total_seconds - used_seconds)
