import os
import subprocess
from logging import getLogger

logger = getLogger(__name__)


def git_commit_hash(path: str | None = None, short: bool = False) -> str:
    try:
        return (
            subprocess.check_output(
                ["git", "rev-parse", "--short" if short else "--verify", "HEAD"],
                cwd=path or os.path.dirname(os.path.abspath(__file__)),
                shell=True,
            )
            .strip()
            .decode()
        )
    except Exception as e:
        logger.debug(f"error: {e}")
        return "0000000"


def git_log(path: str | None = None, n: int = 1) -> str:
    try:
        return (
            subprocess.check_output(
                ["git", "log", f"-{n}"],
                cwd=path or os.path.dirname(os.path.abspath(__file__)),
                shell=True,
            )
            .strip()
            .decode()
        )
    except Exception as e:
        logger.debug(f"error: {e}")
        return f"Error when getting git log. {e}"
