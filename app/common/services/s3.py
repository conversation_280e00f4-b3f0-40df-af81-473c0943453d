import json
import os
from io import Buffered<PERSON>eader
from logging import get<PERSON>ogger
from typing import Any

import boto3
from botocore.exceptions import ClientError

from common.utils.confutil import get_int_env

logger = getLogger(__name__)

VERBOSE = get_int_env("VERBOSE", 0)

ERR_PREFIX = "S3:"


class S3Exception(Exception):
    pass


def get_client() -> boto3.client:
    return boto3.client(
        "s3",
        aws_access_key_id=os.environ["AWS_S3_ACCESS_KEY"],
        aws_secret_access_key=os.environ["AWS_S3_SECRET_ACCESS_KEY"],
        region_name=os.environ["AWS_DEFAULT_REGION"],
    )


def get_object(bucket: str, key: str) -> Any:
    cli = get_client()
    try:
        resp = cli.get_object(Bucket=bucket, Key=key)
    except ClientError as e:
        if e.response["Error"]["Code"] == "NoSuchKey":
            logger.debug(f"{ERR_PREFIX} No such key {key}") if VERBOSE > 1 else None
            return ""
        else:
            logger.exception(e)
            raise
    data = resp["Body"].read()
    return data


def get_str_object(bucket: str, key: str) -> str:
    res = get_object(bucket=bucket, key=key)
    return res.decode("utf-8") if res else ""


def get_json_object(bucket: str, key: str) -> dict:
    s = get_str_object(bucket, key)
    return {} if not s else json.loads(s)


def get_object_reader(bucket: str, key: str) -> BufferedReader | None:
    cli = get_client()
    try:
        resp = cli.get_object(Bucket=bucket, Key=key)
    except ClientError as e:
        if e.response["Error"]["Code"] == "NoSuchKey":
            logger.debug(f"{ERR_PREFIX} No such key {key}") if VERBOSE > 1 else None
            return None
        else:
            logger.exception(e)
            raise

    return resp["Body"]


def exists_object(bucket: str, object_key: str) -> bool:
    cli = get_client()
    try:
        cli.head_object(Bucket=bucket, Key=object_key)
    except ClientError as e:
        if e.response["Error"]["Code"] == "404":
            return False
        else:
            raise
    return True


def del_object(bucket: str, object_key: str) -> bool:
    cli = get_client()
    try:
        cli.delete_object(Bucket=bucket, Key=object_key)
    except ClientError as e:
        if e.response["Error"]["Code"] == "404":
            return False
        else:
            raise
    return True


def put_str_object(bucket: str, key: str, data: str) -> None:
    return put_object(bucket=bucket, key=key, data=data, mime_type="text/plain")


def put_object(
    bucket: str, key: str, data: Any, mime_type: str = "application/octet-stream"
) -> None:
    cli = get_client()
    resp = cli.put_object(Bucket=bucket, Key=key, Body=data, ContentType=mime_type)

    if (
        resp["ResponseMetadata"]["HTTPStatusCode"] != 200
        or resp["ResponseMetadata"]["HTTPStatusCode"] != 200
    ):
        msg = 'Failed to persist object {key}. {resp["ResponseMetadata"]}'
        logger.error(f"{ERR_PREFIX} {msg}")
        raise S3Exception(msg)


def put_object_from_buffer(
    bucket: str,
    key: str,
    buffer: BufferedReader,
) -> None:
    cli = get_client()
    cli.upload_fileobj(buffer, Bucket=bucket, Key=key)
    # print(json.dumps(resp, indent=2, default=str))

    # if (
    #     resp["ResponseMetadata"]["HTTPStatusCode"] != 200
    #     or resp["ResponseMetadata"]["HTTPStatusCode"] != 200
    # ):
    #     msg = 'Failed to persist object {key}. {resp["ResponseMetadata"]}'
    #     logger.error(f"{ERR_PREFIX} {msg}")
    #     raise S3Exception(msg)


def gen_pre_signed_url_put(
    bucket: str,
    key: str,
    content_type: str = "application/octet-stream",
    expiration: int = 900,
) -> str:
    return _gen_pre_signed_url(
        bucket=bucket,
        key=key,
        method="put_object",
        content_type=content_type,
        expiration=expiration,
    )


def gen_pre_signed_url_get(
    bucket: str, key: str, expiration: int = 900, **kwargs: Any
) -> str:
    return _gen_pre_signed_url(
        bucket=bucket,
        key=key,
        method="get_object",
        content_type="",
        expiration=expiration,
        **kwargs,
    )


def _gen_pre_signed_url(
    bucket: str,
    key: str,
    method: str = "put_object",
    content_type: str = "application/octet-stream",
    expiration: int = 900,
    **kwargs: Any,
) -> str:
    """
    Generate a presigned URL for uploading a file to S3 within a user-specific folder.

    :param bucket: S3 bucket name
    :param key: S3 key
    :param method: operation name to be signed
    :param content_type: content type
    :param expiration: Expiration time in seconds (default is 1800 seconds, i.e., 30 minutes)
    :return: pre-signed URL as a string
    """
    cli = get_client()
    params = {"Bucket": bucket, "Key": key}
    if content_type:
        params["ContentType"] = content_type

    if "Params" in kwargs:
        params.update(kwargs.pop("Params") or {})

    try:
        url = cli.generate_presigned_url(
            method, Params=params, ExpiresIn=expiration, **kwargs
        )
        return url
    except ClientError as e:
        msg = f"Error generating pre-signed URL for {bucket}/{key}"
        logger.exception(f"{ERR_PREFIX} {msg}: {e}")
        raise S3Exception(f"{msg}: {e}")


def put_json(bucket: str, key: str, data: str) -> None:
    cli = get_client()
    resp = cli.put_object(
        Bucket=bucket, Key=key, Body=data, ContentType="application/json"
    )
    if (
        resp["ResponseMetadata"]["HTTPStatusCode"] != 200
        or resp["ResponseMetadata"]["HTTPStatusCode"] != 200
    ):
        msg = 'Failed to persist object {key}. {resp["ResponseMetadata"]}'
        logger.error(f"{ERR_PREFIX} {msg}")
        raise S3Exception(msg)


def get_obj_meta(bucket: str, key: str) -> dict:
    cli = get_client()
    resp = cli.head_object(Bucket=bucket, Key=key)

    # print(f"S3 Object Metadata for {bucket}/{key}:")
    # print(f"Last Modified: {resp['LastModified']}")
    # print(f"Content Length: {resp['ContentLength']} bytes")
    # print(f"Content Type: {resp['ContentType']}")
    # print("Metadata:")
    # for k, v in resp['Metadata'].items():
    #     print(f"  {k}: {v}")

    return resp["Metadata"]


def list_objects(bucket: str, prefix: str) -> list[dict]:
    """list S3 object with meta

    format:
        {
            "Key": "my_object_key/",
            "LastModified": datetime.datetime(2025, 1, 15, 8, 53, 37, tzinfo=tzutc()),
            "ETag": '"d41d8cd98f00b204e9801998ecf8427e"',
            "Size": 0,
            "StorageClass": "STANDARD",
        }

    """

    cli = get_client()
    try:
        response = cli.list_objects_v2(Bucket=bucket, Prefix=prefix)
        return response.get("Contents", [])
    except ClientError as e:
        logger.exception(
            f"{ERR_PREFIX} Error listing objects in {bucket}/{prefix}: {e}"
        )
        raise S3Exception(f"Error listing objects in {bucket}/{prefix}: {e}")
