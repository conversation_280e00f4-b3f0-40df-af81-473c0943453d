"""request to index pages"""

import os
from logging import getLogger
from typing import Any, Dict, Optional

from google.oauth2 import service_account
from googleapiclient.discovery import build

logger = getLogger(__name__)

# Scopes for the Indexing API
SCOPES = ["https://www.googleapis.com/auth/indexing"]
SERVICE_ACCOUNT_FILE = os.getenv("GOOGLE_SERVICE_ACCOUNT_CREDENTIALS_JSON_PATH")

# Authenticate and build the service
credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE, scopes=SCOPES
)
service = build("indexing", "v3", credentials=credentials)


def check_url_indexing_status(url: str) -> Optional[Dict[str, Any]]:
    try:
        response = service.urlNotifications().getMetadata(url=url).execute()
        return response
    except Exception as e:
        logger.error(f"Error checking {url}: {e}")
        return None


def index_url(url: str) -> None:
    body = {"url": url, "type": "URL_UPDATED"}
    response = service.urlNotifications().publish(body=body).execute()
    logger.info(response)


# Example usage
# check_url_indexing_status(
#     "https://www.onwish.ai/insights/can-amd-challenge-nvidias-dominance-in-the-ai-gpu-market"
# )
