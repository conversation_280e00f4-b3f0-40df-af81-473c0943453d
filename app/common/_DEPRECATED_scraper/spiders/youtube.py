import asyncio
import json
import re
from abc import ABC
from datetime import datetime
from logging import getLogger
from queue import Queue
from typing import Optional, Literal
from urllib.parse import quote

import playwright.async_api
from bs4 import BeautifulSoup
from playwright.async_api import (
    Response as AsyncResponse,
)

from common.scraper.models import (
    YoutubeChannelProfile,
    YoutubePost,
    parse_count,
    SocialProfile,
)
from common.scraper.spiders.base import (
    PlaywrightSpiderConfig,
    PlaywrightSpider,
    PlaywrightSocialSearchSpiderConfig,
    PlaywrightSocialSearchSpider,
    SpiderNavAnchor,
)
from common.utils.asyncioutil import gather_with_error, with_concurrency_limit
from common.utils.datetimeutils import UTC
from common.utils.strutil import parse_int

logger = getLogger(__name__)


class YoutubeSpiderConfig(PlaywrightSpiderConfig):
    # override parents configs
    browser_type: Literal["chromium", "firefox", "webkit"] = "chromium"


class YoutubeSearchSpiderConfig(
    PlaywrightSocialSearchSpiderConfig, YoutubeSpiderConfig
):
    # whether crawl channel profile alone with post data
    with_profile: bool = False


class YoutubeSpider(PlaywrightSpider, ABC):
    """abstract base of Youtube spider built by Playwright"""

    config: YoutubeSpiderConfig

    def _convert_duration_to_seconds(self, duration_str: str) -> int | None:
        """
        Convert duration string to seconds
        Example: "3:02" -> 182
        """
        if not duration_str:
            return None

        parts = duration_str.split(":")
        if len(parts) == 2:  # MM:SS
            return int(parts[0]) * 60 + int(parts[1])
        elif len(parts) == 3:  # HH:MM:SS
            return int(parts[0]) * 3600 + int(parts[1]) * 60 + int(parts[2])
        return None

    def _extract_item(self, data: dict | list) -> list:
        results = []

        def recursive_search(obj: list | dict) -> None:
            if isinstance(obj, dict):
                if "videoRenderer" in obj:
                    video_data = obj["videoRenderer"]
                    duration_str = video_data.get("lengthText", {}).get("simpleText")
                    published_str = video_data.get("publishedTimeText", {}).get(
                        "simpleText"
                    )

                    # Extract key information
                    video_info = {
                        "id": video_data.get("videoId"),
                        "detail_url": "https://www.youtube.com/watch?v="
                        + video_data.get("videoId"),
                        "title": video_data.get("title", {})
                        .get("runs", [{}])[0]
                        .get("text"),
                        "thumbnails": video_data.get("thumbnail", {}).get(
                            "thumbnails", []
                        ),
                        "duration": duration_str,
                        "duration_seconds": self._convert_duration_to_seconds(
                            duration_str
                        ),
                        "views": video_data.get("viewCountText", {}).get("simpleText"),
                        "published": published_str,
                        "channel_name": video_data.get("ownerText", {})
                        .get("runs", [{}])[0]
                        .get("text"),
                        "channel_url": video_data.get("ownerText", {})
                        .get("runs", [{}])[0]
                        .get("navigationEndpoint", {})
                        .get("browseEndpoint", {})
                        .get("canonicalBaseUrl"),
                        "description_snippet": "\n".join(
                            f"{i + 1}. {run.get('text', '')}"
                            for i, run in enumerate(
                                video_data.get("descriptionSnippet", {}).get("runs", [])
                            )
                        ),
                        "accessibility_label": video_data.get("title", {})
                        .get("accessibility", {})
                        .get("accessibilityData", {})
                        .get("label"),
                    }
                    results.append(video_info)
                # Continue searching all values of the current dictionary
                for value in obj.values():
                    recursive_search(value)
            elif isinstance(obj, list):
                # If it's a list, recursively search each element
                for item in obj:
                    recursive_search(item)

        recursive_search(data)
        return results

    async def async_crawl_profile(self, channel_name: str) -> dict:  # noqa: C901
        # we need to create a new page for it to avoid overriding main page.

        if not channel_name:
            return {}

        context = await self.config.async_get_browser_context()
        page = await context.new_page()
        await page.route(
            "**/*",
            lambda route, request: (
                route.abort()
                if request.resource_type in self.config.ignored_resources
                else route.continue_()
            ),
        )

        channel_data = {
            "timestamp": datetime.now(tz=UTC).isoformat(),
        }
        timeout = self.config.timeout * 1000
        if not channel_name.startswith("@"):
            channel_name = f"@{channel_name}"
        url = f"https://m.youtube.com/{channel_name}"

        try:
            await page.goto(url, wait_until="load", timeout=timeout)
            logger.debug(f"{url} loaded")

            html = await page.content()

            soup = BeautifulSoup(html, "html.parser")
            channel_data["channel_url"] = url

            for script in soup.find_all("script"):
                if script.string and "var ytInitialData = " in script.string:
                    # Extract JSON data more carefully
                    data_str = script.string.split("var ytInitialData = ")[1]
                    # Find the end of the JSON object by looking for semicolon
                    data_str = data_str.split("</script>")[0]
                    # Clean up any trailing commas which might cause JSON parsing errors
                    data_str = re.sub(r",\s*}", "}", data_str)
                    data_str = re.sub(r",\s*]", "]", data_str)
                    data_str = data_str.strip()
                    # Remove any trailing semicolon from the data string
                    if data_str.endswith(";"):
                        data_str = data_str[:-1]
                    try:
                        data = json.loads(data_str)
                    except json.JSONDecodeError as e:
                        print(f"Failed to parse JSON data: {data_str}")
                        raise Exception(f"Failed to parse JSON data: {str(e)}")

                    # Extract channel metadata
                    metadata = data.get("metadata", {}).get(
                        "channelMetadataRenderer", {}
                    )

                    header = data.get("header", {}).get("pageHeaderRenderer", {})

                    # Extract subscribers and videos count
                    metadata_rows = (
                        header.get("content", {})
                        .get("pageHeaderViewModel", {})
                        .get("metadata", {})
                        .get("contentMetadataViewModel", {})
                        .get("metadataRows", [])
                    )
                    subscribers_count = ""
                    videos_count = ""

                    # Find the row containing subscriber and video counts
                    for row in metadata_rows:
                        parts = row.get("metadataParts", [])
                        for part in parts:
                            text_content = part.get("text", {}).get("content", "")
                            if "subscribers" in text_content:
                                subscribers_count = text_content
                            elif "videos" in text_content:
                                videos_count = text_content

                    channel_data.update(
                        {
                            "title": metadata.get("title", ""),
                            "description": metadata.get("description", ""),
                            "externalId": metadata.get("externalId", ""),
                            "rssUrl": metadata.get("rssUrl", ""),
                            "keywords": metadata.get("keywords", ""),
                            "avatar_url": metadata.get("avatar", {})
                            .get("thumbnails", [{}])[0]
                            .get("url", ""),
                            "subscribers_count": subscribers_count,
                            "videos_count": videos_count,
                        }
                    )
                    break
        except playwright.async_api.TimeoutError:
            logger.error(f"timeout after {timeout}ms for {url}")
        except playwright._impl._errors.TargetClosedError:
            logger.debug(f"bypass {channel_name} due to current search is stopped")
        except Exception as e:
            # logger.exception(e)
            logger.error(e)
        finally:
            await page.close()

        if not channel_data.get("title"):
            logger.warning(f"Failed to extract {channel_name} profile")
            return {}

        return channel_data

    def _parse_result_profile(self, item: dict) -> YoutubeChannelProfile:
        """
        Parse a raw YouTube search result dictionary into a YoutubeChannelProfile object.
        """
        # Extract channel profile data
        channel_profile = item.get("channel_profile", {})

        # Basic profile fields
        handle = item["channel_url"].removeprefix("/@")
        full_name = channel_profile.get("title", "")
        url = channel_profile.get("channel_url", "").replace(
            "https://m.youtube", "https://www.youtube"
        )
        biography = channel_profile.get("description", "")
        avatar_url = channel_profile.get("avatar_url", "")

        # Parse counts
        subscribers = parse_count(channel_profile.get("subscribers_count") or 0)
        posts_count = parse_count(channel_profile.get("videos_count") or 0)

        # Create relevant post from the video data
        relevant_post = YoutubePost(
            id=item["id"],
            url=item["detail_url"],
            caption=item["title"],
            published_time_str=item.get("published") or "",
            like_count=None,
            comment_count=None,
            duration_str=item["duration"] or "",
            duration=item["duration_seconds"] or 0,
            view_count=parse_int(item["views"]),
            author_id=channel_profile.get("externalId", ""),
            author_handle=handle,
            author_display_name=full_name,
        )

        return YoutubeChannelProfile(
            handle=handle,
            caption=full_name,
            external_id=channel_profile["externalId"],
            url=url,
            biography=biography,
            avatar_url=avatar_url,
            followers=subscribers,
            posts_count=posts_count,
            keywords=channel_profile.get("keywords", ""),
            relevant_post=relevant_post,
            email=None,  # To be populated in influencer agent
        )


class YoutubeSearchSpider(PlaywrightSocialSearchSpider, YoutubeSpider):
    """Youtube spider for search"""

    config: YoutubeSearchSpiderConfig

    __q_entries: Optional[Queue] = None

    async def _async_search(  # noqa: C901
        self, query: str, q_iter: Optional[Queue] = None
    ) -> dict[str, SocialProfile]:
        current_keyword = None

        sem = asyncio.Semaphore(2)
        pending = 0

        async def handle_result_item(item: dict) -> None:
            channel_name = ""
            try:
                if self.config.with_profile:
                    channel_name = item["channel_url"].removeprefix("/")
                    profile_d = self._cached_profiles.get(
                        channel_name
                    ) or await self.async_crawl_profile(channel_name)
                    if profile_d:
                        item["channel_profile"] = profile_d
                        self._cached_profiles[channel_name] = profile_d

                profile = self._parse_result_profile(item)

                if q_iter is None:
                    self.all_entries[item["id"]] = profile
                else:
                    q_iter.put(profile)
                self.item_count += 1

                await self.download_social_avatar(profile.avatar_url, profile.url)

            except playwright._impl._errors.TargetClosedError:
                logger.debug(
                    f"bypass post {item['id']} of {channel_name} due to current search is stopped"
                )
            except Exception as e:
                logger.error(f"Error handling post {item['id']}: <{type(e)}> {e}")

        async def handle_response(response: AsyncResponse) -> None:
            url = response.url
            nonlocal current_keyword, pending

            # Handle search results
            rest_count = 0
            if "/youtubei/v1/search?" in url and current_keyword:
                logger.debug(f"handling youtube search API: {url}")
                self.result.visited_pages.append(url)
                try:
                    data = await response.json()
                    items = self._extract_item(data)
                    rest_count = len(items)
                    pending += rest_count
                    for item in items:
                        async with sem:
                            await handle_result_item(item)
                            pending -= 1
                            rest_count -= 1
                except Exception as e:
                    logger.error(f"Error handling search response: {e}")
                finally:
                    pending -= rest_count

        page = await self.async_get_page()
        page.on("response", handle_response)

        current_keyword = query
        search_url = f"https://www.youtube.com/results?search_query={quote(query)}"  # &sp=CAASAhAB"
        self.result.visited_pages.append(search_url)

        anchor = 0
        await self.async_go_to(search_url)
        # page = await self._async_get_page()
        # await page.wait_for_load_state("networkidle")
        yt_init_data = await page.evaluate("window.ytInitialData")
        if yt_init_data:
            await gather_with_error(
                *with_concurrency_limit(
                    [
                        handle_result_item(item)
                        for item in self._extract_item(yt_init_data)
                    ],
                    limit=1,
                ),
                log_with_traceback=False,
            )
        logger.debug(f"crawled entries: {max(len(self.all_entries), self.item_count)}")
        anchor += 1

        consecutive_no_new_data = 0
        while True:
            if await self.async_shall_stop(query):
                break

            if pending > 3:
                logger.debug(f"hold on (not go next) for {pending} pending profiles.")
            else:
                last_item_count = self.item_count

                anchor = await self.async_go_next(anchor, is_infinity_page=True)
                logger.info(
                    f"crawled entries: {max(len(self.all_entries), self.item_count)}"
                )

                if anchor is None:
                    # reach the end
                    break
                if 0 < self.config.items <= self.item_count:
                    logger.info(
                        f"reach config items count {self.item_count}>={self.config.items}"
                    )
                    break
                if self.item_count == last_item_count:
                    if pending <= 0:
                        consecutive_no_new_data += 1
                        if consecutive_no_new_data > 2:
                            logger.info(
                                f"finish '{query}' due to items count {self.item_count} not increased for {consecutive_no_new_data} times."
                            )
                            break
                else:
                    consecutive_no_new_data = 0

            await self.async_sleep()

        self.result.entries = self.all_entries
        return self.all_entries

    async def async_check_if_has_next_page(self) -> bool:
        page = await self.async_get_page()
        rc = await page.locator("text=No results found").count() <= 0
        rc = rc and await page.locator("text=No more results").count() <= 0
        return rc

    async def async_scroll_a_page(
        self,
        anchor: SpiderNavAnchor = None,
        timeout: int = 0,
    ) -> SpiderNavAnchor:
        if anchor is None:
            logger.info("reach the end of the page.")
            return anchor

        assert isinstance(anchor, int)

        if 0 < self.config.pages <= anchor:
            # reach pages filter limitation
            logger.info("reach pages limitation.")
            return None

        timeout = 15

        page = await self.async_get_page()
        # await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await page.keyboard.press("End")
        try:
            await page.wait_for_load_state("networkidle", timeout=timeout)
        except playwright.async_api.TimeoutError:
            logger.warning(
                f"YoutubeSearchSpider ignore timeout during scroll to next page {anchor}"
            )
        no_more_results = not await self.async_check_if_has_next_page()
        if no_more_results:
            logger.info("No more results found")
            return None

        return anchor + 1


if __name__ == "__main__":
    import logging
    import colorlog

    colorlog.basicConfig(
        level=logging.INFO,
        format="%(log_color)s[%(asctime)s] [%(levelname).01s] [%(name)s:%(lineno)d] %(message)s",
    )

    async def __async_search() -> None:
        cfg = YoutubeSearchSpiderConfig(
            term="Elon Musk",
            name="youtube_spider",
            browser_type="chromium",
            start_url="https://www.youtube.com",
            proxy={"server": "http://127.0.0.1:1087"},
            # proxy={
            #     "server": "http://auto:<EMAIL>:8000"
            # },
            is_headless=False,
            timeout=300,
            # credential={
            #     "email": os.getenv("EMAIL", ""),
            #     "password": os.getenv("PASSWORD", ""),
            # },
            ignored_resources=["font", "image"],  # [, "stylesheet"],
            # user_data_dir=str(
            #     (Path(__file__).parent.parent / "chrom-user-data-tiktok").absolute()
            # ),
            pages=0,
            items=5,
            with_profile=True,
        )
        spdr = YoutubeSearchSpider(config=cfg)
        async for profile in spdr.async_iterate():
            print("----------------------------")
            print(json.dumps(profile.to_dict(), indent=2, default=str))
        await spdr.async_stop()
        # s = await asyncio.run(spd._async_download("https://www.baidu.com"))
        # print(s)

    asyncio.run(__async_search())
