import asyncio
import re
from abc import ABC
from logging import getLogger
from queue import Queue
from typing import Literal, Optional
from urllib.parse import quote

import playwright.async_api
from bs4 import BeautifulSoup
from playwright.async_api import Page
from playwright.async_api import Response as AsyncResponse

from common.scraper.agents.social_contact import (
    SocialContactAgent,
    SocialContactAgentConfig,
)
from common.scraper.models import (
    InstagramProfile,
    InstagramPost,
    parse_count,
    SocialProfile,
)
from common.scraper.spiders.base import (
    PlaywrightSocialSearchSpider,
    PlaywrightSocialSearchSpiderConfig,
    PlaywrightSpider,
    PlaywrightSpiderConfig,
    SpiderNavAnchor,
)
from common.scraper.spiders.fetch_instagram_profile import (
    async_fetch_profile,
    async_fetch_user_info,
    async_get_profile_posts,
)
from common.utils.asyncioutil import gather_with_error, with_concurrency_limit

logger = getLogger(__name__)


class InstagramSpiderConfig(PlaywrightSpiderConfig):
    pass


class InstagramSearchSpiderConfig(
    PlaywrightSocialSearchSpiderConfig, InstagramSpiderConfig
):
    # whether crawl user profile alone with post data
    with_profile: bool = False


class InstagramSpider(PlaywrightSpider, ABC):
    """abstract base of Instagram spider built by Playwright"""

    config: InstagramSpiderConfig

    def _extract_item(self, data: dict | list) -> list:
        results = []

        def recursive_search(obj: dict | list) -> None:
            if isinstance(obj, dict):
                if "media" in obj and isinstance(obj["media"], dict):
                    media_data = obj["media"] or {}
                    user_data = media_data.get("user") or {}

                    # Extract key information from Instagram media data
                    media_info = {
                        "id": media_data.get("id"),
                        "pk": media_data.get("pk"),
                        # This is used in Instagram URLs
                        "code": media_data.get("code"),
                        "media_type": media_data.get("media_type"),  # 2 for video
                        "taken_at": media_data.get("taken_at"),
                        "detail_url": f"https://www.instagram.com/p/{media_data.get('code')}/",
                        "caption_text": (media_data.get("caption") or {}).get("text"),
                        "play_count": media_data.get("play_count"),
                        "like_count": media_data.get("like_count"),
                        "user": {
                            "id": user_data.get("id"),
                            "pk": user_data.get("pk"),
                            "username": user_data.get("username"),
                            "full_name": user_data.get("full_name"),
                            "is_verified": user_data.get("is_verified"),
                            "profile_pic_url": user_data.get("profile_pic_url"),
                        },
                    }

                    # Get video specific information if available
                    if media_data.get("video_versions"):
                        media_info["video_url"] = media_data.get(
                            "video_versions", [{}]
                        )[0].get("url")
                        media_info["video_duration"] = media_data.get("video_duration")

                    # Get thumbnail information
                    if media_data.get("image_versions2"):
                        thumbnails = media_data.get("image_versions2", {}).get(
                            "candidates", []
                        )
                        if len(thumbnails) > 0:
                            media_info["thumbnail"] = thumbnails[0]

                    results.append(media_info)

                # Continue searching all values of the current dictionary
                for value in obj.values():
                    recursive_search(value)
            elif isinstance(obj, list):
                # If it's a list, recursively search each element
                for item in obj:
                    recursive_search(item)

        recursive_search(data)
        return results

    # async def async_crawl_profile0(self, username: str, page: Page) -> SocialProfile:
    #     """crawl instagram profile by hidden IG API"""
    #     profile_url = f"https://www.instagram.com/api/v1/users/web_profile_info/?username={username}"
    #     logger.info(f"fetch instagram profile from API - {profile_url}")
    #     try:
    #         profile_response = await page.evaluate(
    #             """async (url) => {
    #                                 return new Promise((resolve, reject) => {
    #                                     const xhr = new XMLHttpRequest();
    #                                     xhr.open('GET', url, true);
    #                                     xhr.withCredentials = true;
    #                                     xhr.setRequestHeader('x-ig-app-id', '936619743392459');
    #                                     xhr.onload = function() {
    #                                         if (xhr.status === 200) {
    #                                             resolve(JSON.parse(xhr.responseText));
    #                                         } else {
    #                                             reject(new Error('Request failed'));
    #                                         }
    #                                     };
    #                                     xhr.onerror = function() {
    #                                         reject(new Error('Request failed'));
    #                                     };
    #                                     xhr.send();
    #                                 });
    #                             }""",
    #             profile_url,
    #         )
    #     except Exception as e:
    #         logger.error(e)
    #         return {}
    #
    #     if profile_response and "data" in profile_response:
    #         user_data = profile_response["data"]
    #         profile_info = {
    #             "biography": user_data.get("user", {}).get("biography", ""),
    #             "avatar": user_data.get("user", {}).get("profile_pic_url_hd"),
    #             "home_page": f"https://www.instagram.com/{username}",
    #             "followers": user_data.get("user", {})
    #             .get("edge_followed_by", {})
    #             .get("count", 0),
    #             "following": user_data.get("user", {})
    #             .get("edge_follow", {})
    #             .get("count", 0),
    #             "full_name": user_data.get("user", {}).get("full_name", ""),
    #             "is_private": user_data.get("user", {}).get("is_private", False),
    #             "is_verified": user_data.get("user", {}).get("is_verified", False),
    #             "posts_count": user_data.get("user", {})
    #             .get("edge_owner_to_timeline_media", {})
    #             .get("count", 0),
    #             "html": "",
    #         }
    #
    #         return profile_info
    #
    #     return {}

    async def async_crawl_profile1(  # noqa:C901
        self,
        username: str,
        page: Page,
        wait_until: (
            Literal["commit", "domcontentloaded", "load", "networkidle"] | None
        ) = "domcontentloaded",
        with_html: bool = False,
    ) -> dict:
        """crawl instagram profile by fetch profile page HTML META description"""

        url = f"https://www.instagram.com/{username}/"
        logger.info(f"fetch instagram profile from HTML Meta - {url}")
        try:
            context = await self.config.async_get_browser_context()
            page = await context.new_page()
            await page.route(
                "**/*",
                lambda route, request: (
                    route.abort()
                    if request.resource_type in self.config.ignored_resources
                    else route.continue_()
                ),
            )
            timeout = self.config.timeout * 1000
            await page.goto(url, wait_until=wait_until, timeout=timeout)
            platform = self.get_platform(url)
            if platform == "instagram":
                if (
                    await page.locator(
                        'body main[role="main"] > div > header > section:nth-child(4) button'
                    ).count()
                    > 0
                ):
                    await page.click(
                        'body main[role="main"] > div > header > section:nth-child(4) button'
                    )
                    await page.wait_for_selector('h1:has-text("Links")')
            logger.info(f"{url} loaded")

            html = await page.content()
            soup = BeautifulSoup(html, "html.parser")
            elem = soup.find("meta", {"name": "description"})
            profile = {}
            if elem:
                # samples:
                #  "685M Followers, 154 Following, 7,926 Posts - Instagram (&#064;instagram) on Instagram: &quot;Discover what&#039;s new on Instagram &#x1f50e;&#x2728;&quot;"
                #  "271K Followers, 522 Following, 493 Posts - @iredefh on Instagram: "💌: <EMAIL>"
                # """717K Followers, 865 Following, 870 Posts - MAQUIAGEM TUTORIAL | MAKEUP TUTORIAL (@felineofc) on Instagram: "🌹| Se Maquiar Não é Difícil, Eu te Ajudo!
                #     🍃| Curso Online de Maquiagem
                #     🍃| Do básico ao avançado
                #     🌹| Acessa o link aqui embaixo👇🏻👇🏻"
                # """
                raw = elem["content"]
                content, desc = re.split(
                    r"on\s*Instagram:", raw, flags=re.I | re.MULTILINE
                )
                m = re.match(
                    r"(?P<followers>[0-9,KMB]+)\s*Followers,\s(?P<following>[0-9,KMB]+)\s*Following,\s(?P<posts>[0-9,KMB]+)\s*Posts\s*\-\s*(?P<display_name>.*?)\s*(\((?P<username>.*)\))?\s*",
                    content,
                    flags=re.I | re.MULTILINE,
                )
                if m:
                    logger.debug(f"instagram profile (raw) - @{username} - {raw}")
                    uname = m.group("username")
                    profile = {
                        "full_name": m.group("display_name").strip(),
                        "followers": m.group("followers"),
                        "following": m.group("following"),
                        "posts_count": m.group("posts"),
                        "biography": desc.strip(),
                        "home_page": url,
                        "avatar": "",
                        "html": html if with_html else "",
                    }
                    logger.debug(f"instagram profile (parsed) - @{uname} - {profile}")
                else:
                    logger.warning(f"fail to parse instagram profile {content}")

            for user_id in re.findall(r"\"profile_id\"\s*\:\s*\"(?P<s>\d+)\"", html):
                if user_id:
                    p = await async_fetch_user_info(user_id)
                    if p:
                        profile["email"] = p.get("public_email") or ""
                        posts = await async_get_profile_posts(user_id)
                        profile["posts"] = posts
                        break

            return profile

        finally:
            await page.close()

    async def async_crawl_contact(self, url: str, html: str = "") -> SocialProfile:
        """crawl contact info by recursively crawling links on profile"""
        # avoid depends on agent or circle importing
        from agent.observer import Observer

        cfg = SocialContactAgentConfig(max_depths=4)
        agent = SocialContactAgent(config=cfg, observer=Observer())
        content = agent.html_to_text(html)
        rc = await agent.run(url, content)
        return rc

    async def async_crawl_profile_mix(self, username: str, page: Page) -> dict:
        """crawl profile by mix all kinds of methods"""
        coroutines = [
            self.async_crawl_profile1(username, page, with_html=False),
        ]
        # idx = random.randint(0, 1)
        idx = 0
        profile = await coroutines[idx]

        return profile


class InstagramSearchSpider(PlaywrightSocialSearchSpider, InstagramSpider):
    """Instagram spider for search"""

    config: InstagramSearchSpiderConfig

    __q_entries: Optional[Queue] = None

    def _parse_result_profile(self, item: dict) -> InstagramProfile:
        """
        Parse a raw Instagram search spider dictionary into an InstagramProfile object.
        """
        user_info = item["user"]  # top-level "user" dict
        profile_info = item["user_profile"]  # "user_profile" dict

        # Basic profile fields
        handle = user_info["username"]
        # If user_profile["full_name"] is empty, fallback to user_info["full_name"]
        full_name = profile_info["full_name"] or user_info["full_name"]
        url = profile_info["home_page"].rstrip("/")
        biography = profile_info["biography"]

        followers = parse_count(profile_info["followers"])
        following = parse_count(profile_info["following"])
        posts_count = parse_count(profile_info["posts_count"])

        # Sometimes user_profile avatar is missing or empty;
        # We use user_info["profile_pic_url"] as a fallback
        avatar_url = user_info["profile_pic_url"]

        email = profile_info.get("email")

        relevant_post = InstagramPost(
            id=item["id"],
            url=item["detail_url"],
            caption=item["caption_text"],
            published_time_str=str(item.get("taken_at")),
            like_count=item.get("like_count"),
            pk=item["pk"],
        )

        # Build recent_posts from user_profile["posts"]
        recent_posts = []
        for p in profile_info.get("posts", []):
            recent_posts.append(
                InstagramPost(
                    id=p["id"],
                    url=p["url"],
                    caption=p["caption"],
                    published_time_str=str(p["taken_at"]),
                    like_count=p["like_count"],
                    comment_count=p["comment_count"],
                    pk=p["pk"],
                    share_count=p.get("reshare_count", 0),
                    view_count=p.get("play_count"),
                )
            )

        return InstagramProfile(
            handle=handle,
            caption=full_name,
            url=url,
            biography=biography,
            followers=followers,
            avatar_url=avatar_url,
            posts_count=posts_count,
            email=email,
            following=following,
            relevant_post=relevant_post,
            recent_posts=recent_posts,
        )

    async def _async_search(  # noqa: C901
        self, query: str, q_iter: Optional[Queue] = None
    ) -> dict[str, InstagramProfile]:
        page = await self.async_get_page()

        current_keyword = None

        pending = 0

        async def handle_result_item(item: dict) -> None:
            nonlocal page, pending

            username = (item.get("user") or {}).get("username") or ""

            try:
                if self.config.with_profile:
                    profile_d = (
                        self._cached_profiles.get(username)
                        or await self.async_crawl_profile_mix(username, page=page)
                        or await async_fetch_profile(username)
                    )
                    item["user_profile"] = profile_d
                    if profile_d:
                        self._cached_profiles[username] = profile_d

                profile = self._parse_result_profile(item)
                if q_iter is None:
                    self.all_entries[item["id"]] = profile
                else:
                    q_iter.put(profile)
                self.item_count += 1

                await self.download_social_avatar(profile.avatar_url, profile.url)

            except playwright._impl._errors.TargetClosedError:
                logger.debug(
                    f"bypass post {item['id']} of {username} due to current search is stopped"
                )
            finally:
                pending -= 1

        async def handle_response(response: AsyncResponse) -> None:
            url = response.url
            nonlocal current_keyword, pending

            # Handle search results
            if "/api/v1/fbsearch/web/top_serp" in url and current_keyword:
                logger.debug(f"handling instagram search API: {url}")
                self.result.visited_pages.append(url)

                items = []
                try:
                    data = await response.json() or {}
                    sections = data.get("media_grid", {}).get("sections", [])
                    if sections:
                        items = self._extract_item(sections)
                except Exception as e:
                    logger.error(f"Error handling search response: {e}")

                pending += len(items)
                await gather_with_error(
                    *with_concurrency_limit(
                        [handle_result_item(item) for item in items],
                        limit=2,
                    ),
                    log_with_traceback=False,
                )

        page.on("response", handle_response)

        current_keyword = query
        search_url = (
            f"https://www.instagram.com/explore/search/keyword/?q={quote(query)}"
        )
        self.result.visited_pages.append(search_url)

        anchor = 0
        await self.async_go_to(search_url)
        logger.debug(f"crawled entries: {max(len(self.all_entries), self.item_count)}")
        anchor += 1

        consecutive_no_new_data = 0
        while True:
            if await self.async_shall_stop(query):
                break

            if pending > 3:
                logger.debug(
                    f"hold on '{query}' (not go next) for {pending} pending profiles."
                )
            else:
                last_item_count = self.item_count

                anchor = await self.async_go_next(anchor, is_infinity_page=True)
                logger.info(
                    f"crawled entries: {max(len(self.all_entries), self.item_count)}"
                )
                if anchor is None:
                    # reach the end
                    break
                if 0 < self.config.items <= self.item_count:
                    logger.info(
                        f"reach config items count {self.item_count}>={self.config.items}"
                    )
                    break

                if self.item_count == last_item_count:
                    if pending <= 0:
                        consecutive_no_new_data += 1
                        if consecutive_no_new_data > 2:
                            logger.info(
                                f"finish '{query}' due to items count ({self.item_count}) not increased for {consecutive_no_new_data} times."
                            )
                            break
                else:
                    consecutive_no_new_data = 0

            await self.async_sleep()

        self.result.entries = self.all_entries
        return self.all_entries

    async def async_check_if_has_next_page(self) -> bool:
        page = await self.async_get_page()
        return await page.locator("text=No results").count() <= 0

    async def async_scroll_a_page(
        self,
        anchor: SpiderNavAnchor = None,
        timeout: int = 0,
    ) -> SpiderNavAnchor:
        timeout = 2
        try:
            anchor = await super().async_scroll_a_page(anchor, timeout)
        except playwright.async_api.TimeoutError:
            logger.debug(
                f"InstagramSearchSpider ignore timeout during scroll to next page {anchor}"
            )
        return anchor


if __name__ == "__main__":
    import logging

    import colorlog

    colorlog.basicConfig(
        level=logging.DEBUG,
        format="%(log_color)s[%(levelname).05s] [%(asctime)s] [%(name)s:%(lineno)d] %(message)s",
    )

    async def __async_search() -> None:
        cfg = InstagramSearchSpiderConfig(
            term="Fashion 2024",
            name="instagram_spider",
            browser_type="chromium",
            start_url="https://www.instagram.com",
            proxy={"server": "http://127.0.0.1:1087"},
            # proxy={
            #     "server": "http://auto:<EMAIL>:8000"
            # },
            is_headless=False,
            timeout=300,
            # credential={
            #     "email": os.getenv("EMAIL", ""),
            #     "password": os.getenv("PASSWORD", ""),
            # },
            ignored_resources=["font", "image"],  # ["font", "image", "stylesheet"],
            # user_data_dir=str(
            #     (Path(__file__).parent.parent / "chrom-user-data-tiktok").absolute()
            # ),
            pages=0,
            items=50,
            with_profile=True,
        )
        spdr = InstagramSearchSpider(config=cfg)
        await spdr.async_start()
        print(spdr.result)
        await spdr.async_stop()
        # s = await asyncio.run(spd._async_download("https://www.baidu.com"))
        # print(s)

    asyncio.run(__async_search())
