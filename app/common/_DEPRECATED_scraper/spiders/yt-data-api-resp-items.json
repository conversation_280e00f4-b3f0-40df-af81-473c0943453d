[{"kind": "youtube#video", "etag": "dLdPLLLxIxiJSrEjWZXR2lHJONo", "id": "frzE0zIKs4w", "snippet": {"publishedAt": "2025-02-06T17:40:21Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Unlock more value with Google Wallet Passes: New upsells, links, and more!", "description": "Join us for a live deep dive into the latest Google Wallet feature: Value Added Opportunities! This livestream will explore how you can get more out of the passes your users save in Google Wallet, and how your business can leverage these features to reach more customers.\n\nWith this feature, you’ll be able to provide you users convenient access to relevant offers, upgrades, and information directly from their saved passes.\n\nIf you’re looking to maximize your users’ engagement with Google Wallet, this livestream is for you. Get all your questions answered live and discover how Value Added Opportunities can enhance your experience!\n\nResources:\nSubscribe to Google for Developers → https://goo.gle/developers\nSubscribe to the Google Wallet Developer Newsletter → https://g.co/wallet/newsletter\n\nSpeaker: <PERSON><PERSON> Yanaga\nProducts Mentioned: Google Wallet", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/frzE0zIKs4w/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/frzE0zIKs4w/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/frzE0zIKs4w/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/frzE0zIKs4w/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/frzE0zIKs4w/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["pr_pr: Pay;", "Purpose: Learn;", "gds:Yes;", "Video Type:Livestream;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "Unlock more value with Google Wallet Passes: New upsells, links, and more!", "description": "Join us for a live deep dive into the latest Google Wallet feature: Value Added Opportunities! This livestream will explore how you can get more out of the passes your users save in Google Wallet, and how your business can leverage these features to reach more customers.\n\nWith this feature, you’ll be able to provide you users convenient access to relevant offers, upgrades, and information directly from their saved passes.\n\nIf you’re looking to maximize your users’ engagement with Google Wallet, this livestream is for you. Get all your questions answered live and discover how Value Added Opportunities can enhance your experience!\n\nResources:\nSubscribe to Google for Developers → https://goo.gle/developers\nSubscribe to the Google Wallet Developer Newsletter → https://g.co/wallet/newsletter\n\nSpeaker: <PERSON><PERSON> Yanaga\nProducts Mentioned: Google Wallet"}, "defaultAudioLanguage": "en-US"}, "contentDetails": {"duration": "PT38M51S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "1156", "likeCount": "62", "favoriteCount": "0", "commentCount": "3"}}, {"kind": "youtube#video", "etag": "X0kywJVpzEP8TX8ifvNK1ymou7k", "id": "PupBvJqIEcY", "snippet": {"publishedAt": "2025-02-04T00:00:15Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "The key to better AI? Prioritize diversity, ethics, and inclusivity.", "description": "AI isn’t just “a computer.” 🤔 We’re debunking common myths and highlighting the human foundation that shapes AI at #DevFest with GDG Silicon Valley organizer <PERSON><PERSON><PERSON>. Find her on LinkedIn!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nSpeaker: <PERSON><PERSON><PERSON> \nProducts Mentioned: Gemini", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/PupBvJqIEcY/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/PupBvJqIEcY/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/PupBvJqIEcY/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/PupBvJqIEcY/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/PupBvJqIEcY/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "pr_pr: GDG;", "Purpose: Learn;", "Campaign: ;", "type:Social First - Educational;", "gds:Yes;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "The key to better AI? Prioritize diversity, ethics, and inclusivity.", "description": "AI isn’t just “a computer.” 🤔 We’re debunking common myths and highlighting the human foundation that shapes AI at #DevFest with GDG Silicon Valley organizer <PERSON><PERSON><PERSON>. Find her on LinkedIn!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nSpeaker: <PERSON><PERSON><PERSON> \nProducts Mentioned: Gemini"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT48S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "1193", "likeCount": "26", "favoriteCount": "0", "commentCount": "3"}}, {"kind": "youtube#video", "etag": "AsNsOKxQAfhPrm3Sb9O-nqlRwBE", "id": "JRBTDmX-pXg", "snippet": {"publishedAt": "2025-02-03T14:00:22Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Can you efficiently figure out when this painting theft happened? Go!", "description": "Calling all problem-solvers: A museum guard needs to pinpoint the exact minute a painting was stolen from a 12-hour security recording. They can check one timestamp at a time, and it takes a minute per check. How would you approach this?\n\nBonus: How does having two guards change things?\nLet’s see your solutions in the comments!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nSpeaker: <PERSON><PERSON>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/JRBTDmX-pXg/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/JRBTDmX-pXg/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/JRBTDmX-pXg/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/JRBTDmX-pXg/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/JRBTDmX-pXg/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "pr_pr: <PERSON><PERSON>;", "Purpose: Learn;", "Campaign: ;", "type:G4D SV: Educational ;", "gds:Yes;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "Can you efficiently figure out when this painting theft happened? Go!", "description": "Calling all problem-solvers: A museum guard needs to pinpoint the exact minute a painting was stolen from a 12-hour security recording. They can check one timestamp at a time, and it takes a minute per check. How would you approach this?\n\nBonus: How does having two guards change things?\nLet’s see your solutions in the comments!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nSpeaker: <PERSON><PERSON>"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT33S", "dimension": "2d", "definition": "hd", "caption": "false", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "2228", "likeCount": "89", "favoriteCount": "0", "commentCount": "16"}}, {"kind": "youtube#video", "etag": "R0T7z6y1oAgeGXaZ6F-keY6IxdY", "id": "6_gK5KvlmGg", "snippet": {"publishedAt": "2025-01-31T23:30:03Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Level Up Your Google Wallet Integration: Pass Sharing Best Practices for Developers", "description": "Ready to take your Google Wallet sharing game to the next level? Join us LIVE as we dive deep into best practices for creating and sharing passes. We'll cover everything from pre-creating passes with the API, creating embedded passes in JWTs, sharing multiple passes, and more! This session is packed with practical tips and tricks specifically for developers, including code examples and Q&A. Get your questions ready!", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/6_gK5KvlmGg/default_live.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/6_gK5KvlmGg/mqdefault_live.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/6_gK5KvlmGg/hqdefault_live.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/6_gK5KvlmGg/sddefault_live.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/6_gK5KvlmGg/maxresdefault_live.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "categoryId": "28", "liveBroadcastContent": "upcoming", "defaultLanguage": "en", "localized": {"title": "Level Up Your Google Wallet Integration: Pass Sharing Best Practices for Developers", "description": "Ready to take your Google Wallet sharing game to the next level? Join us LIVE as we dive deep into best practices for creating and sharing passes. We'll cover everything from pre-creating passes with the API, creating embedded passes in JWTs, sharing multiple passes, and more! This session is packed with practical tips and tricks specifically for developers, including code examples and Q&A. Get your questions ready!"}, "defaultAudioLanguage": "en-US"}, "contentDetails": {"duration": "P0D", "dimension": "2d", "definition": "sd", "caption": "false", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "0", "likeCount": "2", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "a4bAWtLZfsQsTMT58hVAVodNYak", "id": "85vwk-Sqa5E", "snippet": {"publishedAt": "2025-01-29T22:58:32Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Being a Great Google Summer of Code Mentor", "description": "Subscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/85vwk-Sqa5E/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/85vwk-Sqa5E/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/85vwk-Sqa5E/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/85vwk-Sqa5E/sddefault.jpg", "width": 640, "height": 480}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "Being a Great Google Summer of Code Mentor", "description": "Subscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT3M42S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "368", "likeCount": "13", "favoriteCount": "0", "commentCount": "2"}}, {"kind": "youtube#video", "etag": "cReyiBzFhcv7Vjho6jNeUP2ONAo", "id": "sL5yfpoTZZo", "snippet": {"publishedAt": "2025-01-29T22:58:16Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Congrats! You're a Google Summer of Code Contributor!", "description": "Congratulations! You've been accepted as a GSoC contributor! Here's how you make the most of the experience. Listen for advice and tips from mentors on how to be successful in GSoC.\n\n(2022 update: we say student a lot in here, please ignore that and know we now mean GSoC Contributor!)\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/sL5yfpoTZZo/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/sL5yfpoTZZo/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/sL5yfpoTZZo/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/sL5yfpoTZZo/sddefault.jpg", "width": 640, "height": 480}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "Congrats! You're a Google Summer of Code Contributor!", "description": "Congratulations! You've been accepted as a GSoC contributor! Here's how you make the most of the experience. Listen for advice and tips from mentors on how to be successful in GSoC.\n\n(2022 update: we say student a lot in here, please ignore that and know we now mean GSoC Contributor!)\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M36S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "151", "likeCount": "5", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "Oyb2Z3FnWzqftxgHG5bM4_FUE1U", "id": "6pSSTVUwGPM", "snippet": {"publishedAt": "2025-01-29T22:57:54Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "How to Choose a Google Summer of Code Contributor", "description": "It's important to pick GSoC Contributors who are good a good fit for your organization. Learn about best practices and things to avoid.\n\n(2022 Update: Potential GSoC Contributors may have other commitments since they could be choosing a medium (~175 hour) or a large (~350 hour) project.)\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/6pSSTVUwGPM/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/6pSSTVUwGPM/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/6pSSTVUwGPM/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/6pSSTVUwGPM/sddefault.jpg", "width": 640, "height": 480}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "How to Choose a Google Summer of Code Contributor", "description": "It's important to pick GSoC Contributors who are good a good fit for your organization. Learn about best practices and things to avoid.\n\n(2022 Update: Potential GSoC Contributors may have other commitments since they could be choosing a medium (~175 hour) or a large (~350 hour) project.)\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M42S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "131", "likeCount": "10", "favoriteCount": "0", "commentCount": "1"}}, {"kind": "youtube#video", "etag": "eDdKIlaAFhTXUFSg9IwHY6RBDSM", "id": "ciTOo8D2kTk", "snippet": {"publishedAt": "2025-01-29T22:56:05Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Google Summer of Code Mentor Summit", "description": "Get a look at the Google Summer of Code Mentor Summit. \n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/ciTOo8D2kTk/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/ciTOo8D2kTk/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/ciTOo8D2kTk/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/ciTOo8D2kTk/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/ciTOo8D2kTk/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "Google Summer of Code Mentor Summit", "description": "Get a look at the Google Summer of Code Mentor Summit. \n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT3M54S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "74", "likeCount": "1", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "p3EnH0Ykg2rStKdmDNG2TKkTy7o", "id": "Wxjxwx7mqaI", "snippet": {"publishedAt": "2025-01-29T22:55:44Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "An introduction to Google Summer of Code", "description": "New contributors to open source can apply to participate in Google Summer of Code. Spend 12+ weeks programming and learning about Open Source! \n\nResources: \nFor information about Google Summer of Code visit → https://g.co/gsoc\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/Wxjxwx7mqaI/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/Wxjxwx7mqaI/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/Wxjxwx7mqaI/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/Wxjxwx7mqaI/sddefault.jpg", "width": 640, "height": 480}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "An introduction to Google Summer of Code", "description": "New contributors to open source can apply to participate in Google Summer of Code. Spend 12+ weeks programming and learning about Open Source! \n\nResources: \nFor information about Google Summer of Code visit → https://g.co/gsoc\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT3M19S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "184", "likeCount": "8", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "LDIcjQoXxxF9BL0KrbF-eDv8LEY", "id": "EJFdPmFNIjk", "snippet": {"publishedAt": "2025-01-29T22:55:25Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC Trailer", "description": "Check out our website for updates on the program → https://goo.gle/4jlty8i\n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/EJFdPmFNIjk/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/EJFdPmFNIjk/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/EJFdPmFNIjk/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/EJFdPmFNIjk/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/EJFdPmFNIjk/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC Trailer", "description": "Check out our website for updates on the program → https://goo.gle/4jlty8i\n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT31S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "71", "likeCount": "2", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "7WGbD58_J5jVD8PF5JxLLB5YXwU", "id": "118H9_7xQGY", "snippet": {"publishedAt": "2025-01-29T22:55:11Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC Program Overview", "description": "Check out our website for updates on the program → https://goo.gle/4jlty8i\n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/118H9_7xQGY/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/118H9_7xQGY/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/118H9_7xQGY/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/118H9_7xQGY/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/118H9_7xQGY/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC Program Overview", "description": "Check out our website for updates on the program → https://goo.gle/4jlty8i\n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT5M4S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "72", "likeCount": "1", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "cOwUQ8lKaEqeY4yKrBPkh2yCDBA", "id": "p6xdQInKZh8", "snippet": {"publishedAt": "2025-01-29T22:54:51Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: <PERSON><PERSON>p", "description": "In this video, meet the Zulip organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/p6xdQInKZh8/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/p6xdQInKZh8/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/p6xdQInKZh8/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/p6xdQInKZh8/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/p6xdQInKZh8/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: <PERSON><PERSON>p", "description": "In this video, meet the Zulip organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M47S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "72", "likeCount": "1", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "07gRa60uS04PgwDoHZvgLzggYE4", "id": "_3O0ukCY6D4", "snippet": {"publishedAt": "2025-01-29T22:54:34Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: Wikimedia Foundation", "description": "In this video, meet the Wikimedia Foundation! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/_3O0ukCY6D4/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/_3O0ukCY6D4/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/_3O0ukCY6D4/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/_3O0ukCY6D4/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/_3O0ukCY6D4/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: Wikimedia Foundation", "description": "In this video, meet the Wikimedia Foundation! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M56S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "50", "likeCount": "1", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "mAqc5qreiSaREKFxNkW33FQ7e3o", "id": "LQkqBYOREgE", "snippet": {"publishedAt": "2025-01-29T22:54:18Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: <PERSON><PERSON><PERSON>", "description": "In this video, meet the Wagtail organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/LQkqBYOREgE/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/LQkqBYOREgE/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/LQkqBYOREgE/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/LQkqBYOREgE/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/LQkqBYOREgE/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: <PERSON><PERSON><PERSON>", "description": "In this video, meet the Wagtail organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT3M7S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "29", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "jjSTjC9Ctc9wv-yDXHjA5oE9u7w", "id": "kUH7W-yBZlk", "snippet": {"publishedAt": "2025-01-29T22:53:54Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: Unikraft", "description": "In this video, meet the Unikraft organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/kUH7W-yBZlk/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/kUH7W-yBZlk/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/kUH7W-yBZlk/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/kUH7W-yBZlk/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/kUH7W-yBZlk/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: Unikraft", "description": "In this video, meet the Unikraft organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M19S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "27", "likeCount": "1", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "imZDnnzNPVoZc5jDH4RZlR4GQ-M", "id": "penAnj9RyUo", "snippet": {"publishedAt": "2025-01-29T22:53:35Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: The Julia Language", "description": "In this video, meet the Julia Language organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/penAnj9RyUo/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/penAnj9RyUo/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/penAnj9RyUo/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/penAnj9RyUo/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/penAnj9RyUo/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: The Julia Language", "description": "In this video, meet the Julia Language organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT3M22S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "94", "likeCount": "6", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "Lv8Hd6TeIEtfPI-RoP4nBIVHiPk", "id": "YZ32MoVvYSw", "snippet": {"publishedAt": "2025-01-29T22:53:22Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: <PERSON><PERSON>Chat", "description": "In this video, meet the Rocket.Chat organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/YZ32MoVvYSw/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/YZ32MoVvYSw/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/YZ32MoVvYSw/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/YZ32MoVvYSw/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/YZ32MoVvYSw/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: <PERSON><PERSON>Chat", "description": "In this video, meet the Rocket.Chat organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M24S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "41", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "v5JtuQdy9SZhV1ZnmMRCXPPXZ9U", "id": "yk5j3FLY1Jo", "snippet": {"publishedAt": "2025-01-29T22:52:57Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: RADAR base", "description": "In this video, meet the RADAR-base organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/yk5j3FLY1Jo/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/yk5j3FLY1Jo/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/yk5j3FLY1Jo/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/yk5j3FLY1Jo/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/yk5j3FLY1Jo/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: RADAR base", "description": "In this video, meet the RADAR-base organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT3M8S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "27", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "aKnUQvSJMRDvtNqhVH3Dsd_2uoU", "id": "vDr60eStjkk", "snippet": {"publishedAt": "2025-01-29T22:52:37Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: QEMU", "description": "In this video, meet the QEMU organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/vDr60eStjkk/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/vDr60eStjkk/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/vDr60eStjkk/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/vDr60eStjkk/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/vDr60eStjkk/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: QEMU", "description": "In this video, meet the QEMU organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT1M44S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "47", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "6KnyprxMIcti5ly4rVEBtnDB-dc", "id": "vy0WeyoVM9g", "snippet": {"publishedAt": "2025-01-29T22:52:09Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: Processing Foundation", "description": "In this video, meet the Processing Foundation! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/vy0WeyoVM9g/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/vy0WeyoVM9g/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/vy0WeyoVM9g/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/vy0WeyoVM9g/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/vy0WeyoVM9g/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: Processing Foundation", "description": "In this video, meet the Processing Foundation! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT57S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "17", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "_7oF8pacC7MusHqC6MDc6YUWls8", "id": "mc_VJcX-zUI", "snippet": {"publishedAt": "2025-01-29T22:51:42Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: <PERSON>man", "description": "In this video, meet the Postman organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/mc_VJcX-zUI/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/mc_VJcX-zUI/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/mc_VJcX-zUI/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/mc_VJcX-zUI/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/mc_VJcX-zUI/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: <PERSON>man", "description": "In this video, meet the Postman organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M55S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "42", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "vHaC3C-2N1fyhNkiCjKPD83RXMQ", "id": "wI1Jgg5Ub7Q", "snippet": {"publishedAt": "2025-01-29T22:51:24Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: Oppia Foundation", "description": "In this video, meet the Oppia Foundation! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/wI1Jgg5Ub7Q/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/wI1Jgg5Ub7Q/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/wI1Jgg5Ub7Q/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/wI1Jgg5Ub7Q/sddefault.jpg", "width": 640, "height": 480}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: Oppia Foundation", "description": "In this video, meet the Oppia Foundation! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M31S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "20", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "MBEZ8IR0KtwMbgN86jsqvuVmgCY", "id": "bS9Gmlp6v2E", "snippet": {"publishedAt": "2025-01-29T22:50:59Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: OpenWISP", "description": "In this video, meet the OpenWISP organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/bS9Gmlp6v2E/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/bS9Gmlp6v2E/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/bS9Gmlp6v2E/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/bS9Gmlp6v2E/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/bS9Gmlp6v2E/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: OpenWISP", "description": "In this video, meet the OpenWISP organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT3M43S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "54", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "MwqYHM0okZxJqof-Fm5IgFduiPs", "id": "OhB_PZ0tnr4", "snippet": {"publishedAt": "2025-01-29T22:50:33Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: OpenVINO Toolkit", "description": "In this video, meet the OpenVINO Toolkit organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/OhB_PZ0tnr4/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/OhB_PZ0tnr4/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/OhB_PZ0tnr4/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/OhB_PZ0tnr4/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/OhB_PZ0tnr4/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: OpenVINO Toolkit", "description": "In this video, meet the OpenVINO Toolkit organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M39S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "24", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "hSyr9WqIdEQJpFS3UCXUcB-3CXc", "id": "BcxjXwAHojM", "snippet": {"publishedAt": "2025-01-29T22:50:15Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: <PERSON><PERSON><PERSON>", "description": "In this video, meet the Mathesar organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/BcxjXwAHojM/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/BcxjXwAHojM/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/BcxjXwAHojM/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/BcxjXwAHojM/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/BcxjXwAHojM/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: <PERSON><PERSON><PERSON>", "description": "In this video, meet the Mathesar organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M23S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "44", "likeCount": "1", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "jaoF7T8zvMcwzcAiWY6EqeSJGEA", "id": "ZgbBDIzQL_U", "snippet": {"publishedAt": "2025-01-29T22:49:35Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: Machine Learning for Science (ML4SCI) Umbrella Organization", "description": "In this video, meet the Machine Learning for Science (ML4SCI) Umbrella organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/ZgbBDIzQL_U/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/ZgbBDIzQL_U/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/ZgbBDIzQL_U/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/ZgbBDIzQL_U/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/ZgbBDIzQL_U/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: Machine Learning for Science (ML4SCI) Umbrella Organization", "description": "In this video, meet the Machine Learning for Science (ML4SCI) Umbrella organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT3M39S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "32", "likeCount": "1", "favoriteCount": "0", "commentCount": "1"}}, {"kind": "youtube#video", "etag": "8J7F3VGKKRoara-9Je-EvCrfumw", "id": "pkgkddzkbJs", "snippet": {"publishedAt": "2025-01-29T22:49:16Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: Liquid Galaxy Project", "description": "In this video, meet the Liquid Galaxy Project organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/pkgkddzkbJs/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/pkgkddzkbJs/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/pkgkddzkbJs/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/pkgkddzkbJs/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/pkgkddzkbJs/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: Liquid Galaxy Project", "description": "In this video, meet the Liquid Galaxy Project organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M2S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "29", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "6Rer_rSXTd_TC52qSr4DRY81SXA", "id": "VXaCAcgbILc", "snippet": {"publishedAt": "2025-01-29T22:48:53Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: Learning Equality", "description": "In this video, meet the Learning Equality organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/VXaCAcgbILc/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/VXaCAcgbILc/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/VXaCAcgbILc/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/VXaCAcgbILc/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/VXaCAcgbILc/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: Learning Equality", "description": "In this video, meet the Learning Equality organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT3M10S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "18", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "f664hzP69GkPce1GiplIbJm2FBg", "id": "1ODHmGOB6PU", "snippet": {"publishedAt": "2025-01-29T22:48:33Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: JdeRobot", "description": "In this video, meet the JdeRobot organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/1ODHmGOB6PU/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/1ODHmGOB6PU/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/1ODHmGOB6PU/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/1ODHmGOB6PU/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/1ODHmGOB6PU/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: JdeRobot", "description": "In this video, meet the JdeRobot organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M17S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "23", "likeCount": "1", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "Ojw6vXIRw0z_90B65p3pGQ3r-Hg", "id": "BTOm9P-LqQo", "snippet": {"publishedAt": "2025-01-29T22:48:17Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: INCF", "description": "In this video, meet the INCF organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/BTOm9P-LqQo/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/BTOm9P-LqQo/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/BTOm9P-LqQo/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/BTOm9P-LqQo/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/BTOm9P-LqQo/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: INCF", "description": "In this video, meet the INCF organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT3M44S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "18", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "sW2B3hl4UvUjPtBimQ802xcTTvY", "id": "iN6r3HRENAs", "snippet": {"publishedAt": "2025-01-29T22:47:59Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: Google Responsible AI and Human Centered Technology", "description": "In this video, meet the Google Responsible AI and Human Centred Technology organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/iN6r3HRENAs/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/iN6r3HRENAs/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/iN6r3HRENAs/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/iN6r3HRENAs/sddefault.jpg", "width": 640, "height": 480}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: Google Responsible AI and Human Centered Technology", "description": "In this video, meet the Google Responsible AI and Human Centred Technology organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT1M34S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "25", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "lDJcNGDYeHFOoA4O1i4sqXCnl2o", "id": "VGfgm5kksH4", "snippet": {"publishedAt": "2025-01-29T22:47:34Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC Genome Assembly and Annotation", "description": "In this video, meet the Genome Assembly and Annotation organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/VGfgm5kksH4/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/VGfgm5kksH4/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/VGfgm5kksH4/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/VGfgm5kksH4/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/VGfgm5kksH4/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC Genome Assembly and Annotation", "description": "In this video, meet the Genome Assembly and Annotation organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT1M47S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "28", "likeCount": "1", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "jXZZeVdN4cuqCUcgdH_pXxwifZg", "id": "af1T_Se7J5Q", "snippet": {"publishedAt": "2025-01-29T22:47:22Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: FreeBSD Project", "description": "In this video, meet the FreeBSD Project organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/af1T_Se7J5Q/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/af1T_Se7J5Q/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/af1T_Se7J5Q/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/af1T_Se7J5Q/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/af1T_Se7J5Q/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: FreeBSD Project", "description": "In this video, meet the FreeBSD Project organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M21S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "166", "likeCount": "7", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "kn9Ou_-2MTOwlqgQjlwmu21K7q4", "id": "Rio91Ml15s8", "snippet": {"publishedAt": "2025-01-29T22:47:00Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: Forschungszentrum Jülich", "description": "In this video, meet the Forschungszentrum Jülich organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/Rio91Ml15s8/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/Rio91Ml15s8/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/Rio91Ml15s8/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/Rio91Ml15s8/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/Rio91Ml15s8/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: Forschungszentrum Jülich", "description": "In this video, meet the Forschungszentrum Jülich organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT1M52S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "48", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "yYzM0tg9bkvgx1JAe3PYC3Lvh_Y", "id": "zp-uxRShBlU", "snippet": {"publishedAt": "2025-01-29T22:46:38Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: CNCF", "description": "In this video, meet the CNCF organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/zp-uxRShBlU/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/zp-uxRShBlU/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/zp-uxRShBlU/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/zp-uxRShBlU/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/zp-uxRShBlU/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: CNCF", "description": "In this video, meet the CNCF organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M42S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "82", "likeCount": "2", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "P7NhacCXalCDG5Ya-JzZD5VpT7A", "id": "qPYbYaG53T4", "snippet": {"publishedAt": "2025-01-29T22:46:24Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: Chromium", "description": "In this video, meet the Chromium organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/qPYbYaG53T4/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/qPYbYaG53T4/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/qPYbYaG53T4/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/qPYbYaG53T4/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/qPYbYaG53T4/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: Chromium", "description": "In this video, meet the Chromium organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M25S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "82", "likeCount": "0", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "I6XkujVOvMeb08QAYlJi_GzGIN8", "id": "bQbY3XEcFDA", "snippet": {"publishedAt": "2025-01-29T22:46:07Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "GSoC: 52°North", "description": "In this video, meet the 52°North organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/bQbY3XEcFDA/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/bQbY3XEcFDA/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/bQbY3XEcFDA/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/bQbY3XEcFDA/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/bQbY3XEcFDA/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "GSoC: 52°North", "description": "In this video, meet the 52°North organization! \n\nGoogle Summer of Code (GSoC) is a global, online program focused on bringing new contributors into open source software development. GSoC Contributors work with an open source organization on a 12+ week programming project under the guidance of mentors.  View the videos and get to know our mentoring organizations!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n \nProducts Mentioned: Google Open Source"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M38S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "82", "likeCount": "1", "favoriteCount": "0", "commentCount": "0"}}, {"kind": "youtube#video", "etag": "rpN8E6uidC-md9-koBCuTPzYROg", "id": "bhgrUBBnpWo", "snippet": {"publishedAt": "2025-01-27T14:00:46Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "How many unique states can you create with combinations of three lights? Go!", "description": "This week’s developer challenge: You have three lights—red, green, and blue. Each light can be ON or OFF. Your task? Use these lights to signal exactly 7 unique commands. Here’s a bonus to stretch your thinking: If you needed to signal N commands, how many lights would you need?\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nSpeaker: <PERSON><PERSON>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/bhgrUBBnpWo/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/bhgrUBBnpWo/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/bhgrUBBnpWo/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/bhgrUBBnpWo/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/bhgrUBBnpWo/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "pr_pr: <PERSON><PERSON>;", "Purpose: Learn;", "Campaign: ;", "type:G4D SV: Educational ;", "gds:Yes;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "How many unique states can you create with combinations of three lights? Go!", "description": "This week’s developer challenge: You have three lights—red, green, and blue. Each light can be ON or OFF. Your task? Use these lights to signal exactly 7 unique commands. Here’s a bonus to stretch your thinking: If you needed to signal N commands, how many lights would you need?\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nSpeaker: <PERSON><PERSON>"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT29S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "3210", "likeCount": "123", "favoriteCount": "0", "commentCount": "24"}}, {"kind": "youtube#video", "etag": "AwSNAcl54tgOgd1iyUPw2TX4P3I", "id": "L3JlB7ogKQY", "snippet": {"publishedAt": "2025-01-23T17:32:03Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Keep it safe! Google Wallet security features for developers", "description": "Say goodbye to ticket fraud and hello to peace of mind with Google Wallet's enhanced security features for passes!  We're talking about account-restricted passes that are locked to a user's Google account, making sharing impossible for those pesky scalpers.  Plus, with  dynamic, rotating barcodes that change regularly, counterfeiting becomes a nightmare. And to top it off, a shimmering security animation adds an extra layer of \"wow\" and protection. Developers can now create and manage passes that are even more secure, boosting user trust and leaving fraudsters in the dust.\n\nResources:\n\nSubscribe to Google for Developers → https://goo.gle/developers\nSubscribe to the Google Wallet Developer Newsletter → https://g.co/wallet/newsletter\n\n\nSpeaker: <PERSON><PERSON> Mentioned: Google Wallet", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/L3JlB7ogKQY/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/L3JlB7ogKQY/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/L3JlB7ogKQY/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/L3JlB7ogKQY/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/L3JlB7ogKQY/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["pr_pr: Pay;", "Purpose: Learn;", "gds:Yes;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "Keep it safe! Google Wallet security features for developers", "description": "Say goodbye to ticket fraud and hello to peace of mind with Google Wallet's enhanced security features for passes!  We're talking about account-restricted passes that are locked to a user's Google account, making sharing impossible for those pesky scalpers.  Plus, with  dynamic, rotating barcodes that change regularly, counterfeiting becomes a nightmare. And to top it off, a shimmering security animation adds an extra layer of \"wow\" and protection. Developers can now create and manage passes that are even more secure, boosting user trust and leaving fraudsters in the dust.\n\nResources:\n\nSubscribe to Google for Developers → https://goo.gle/developers\nSubscribe to the Google Wallet Developer Newsletter → https://g.co/wallet/newsletter\n\n\nSpeaker: <PERSON><PERSON> Mentioned: Google Wallet"}, "defaultAudioLanguage": "en-US"}, "contentDetails": {"duration": "PT29M17S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "1918", "likeCount": "90", "favoriteCount": "0", "commentCount": "9"}}, {"kind": "youtube#video", "etag": "vb1_qy7yLD_gwnj-BH9X9y9XOic", "id": "bHSoCSJadQM", "snippet": {"publishedAt": "2025-01-22T17:55:13Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Integrate Google Pay with Jetpack Compose on Android", "description": "In this Google Pay livestream, your hosts <PERSON> and <PERSON><PERSON><PERSON> give you a deep dive on how to integrate Google Pay using Kotlin and Jetpack Compose.\n\nConnect with us:\n\nJoin the conversation in the #payments channel on Discord → https://goo.gle/payments-dev-community \nWatch more Live Google Pay integrations on Android → https://goo.gle/live-google-pay  \nFollow @GooglePayDevs on X for more content like this → https://goo.gle/GooglePayDevs   \nSubscribe to Google for Developers → https://goo.gle/developers   \n\nGet support: \n\nFor assistance with your implementation, create a support ticket from the Google Pay & Wallet Console →  https://goo.gle/4g36MQn \n\nSpeaker: <PERSON><PERSON><PERSON>, Jose <PERSON>gia\nProducts Mentioned: Google Pay\n\n#GooglePay #Android #payments #developer", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/bHSoCSJadQM/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/bHSoCSJadQM/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/bHSoCSJadQM/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/bHSoCSJadQM/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/bHSoCSJadQM/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "Integrate Google Pay with Jetpack Compose on Android", "description": "In this Google Pay livestream, your hosts <PERSON> and <PERSON><PERSON><PERSON> give you a deep dive on how to integrate Google Pay using Kotlin and Jetpack Compose.\n\nConnect with us:\n\nJoin the conversation in the #payments channel on Discord → https://goo.gle/payments-dev-community \nWatch more Live Google Pay integrations on Android → https://goo.gle/live-google-pay  \nFollow @GooglePayDevs on X for more content like this → https://goo.gle/GooglePayDevs   \nSubscribe to Google for Developers → https://goo.gle/developers   \n\nGet support: \n\nFor assistance with your implementation, create a support ticket from the Google Pay & Wallet Console →  https://goo.gle/4g36MQn \n\nSpeaker: <PERSON><PERSON><PERSON>, Jose <PERSON>gia\nProducts Mentioned: Google Pay\n\n#GooglePay #Android #payments #developer"}, "defaultAudioLanguage": "en-US"}, "contentDetails": {"duration": "PT51M51S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "2081", "likeCount": "89", "favoriteCount": "0", "commentCount": "4"}}, {"kind": "youtube#video", "etag": "Y1YD667E695vEGfI_tT79FzSyec", "id": "EnLPiVPYYos", "snippet": {"publishedAt": "2025-01-16T17:00:28Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "What's new in the pay plugin for Flutter", "description": "Enhance your Flutter app's payment flow with the latest updates to the Flutter pay plugin! This video guides you through the new features and improvements, including the new Google Pay button, customizable payment buttons, and a more straightforward integration experience.\n\nChapters: \n\n0:00 - Intro \n0:12 - Introduction\n0:23 - What’s new\n2:03 - Quick integration tutorial\n3:01 - Conclusion\n\nResources\nPay package in pub.dev –https://goo.gle/40kXV64 \nExample application in Flutter – https://goo.gle/flutter-pay-example \n\nConnect with us:\n\nJoin the conversation in the #payments channel on Discord → https://goo.gle/payments-dev-community\nWatch more Live Google Pay integrations on Android → https://goo.gle/live-google-pay \nFollow @GooglePayDevs on X for more content like this → https://goo.gle/GooglePayDevs  \nSubscribe to Google for Developers → https://goo.gle/developers  \n\nGet support: \n\nFor assistance with your implementation, create a support ticket from the Google Pay & Wallet Console → https://goo.gle/4hSy658 \n\nSpeaker: <PERSON> Mentioned: Google Pay", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/EnLPiVPYYos/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/EnLPiVPYYos/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/EnLPiVPYYos/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/EnLPiVPYYos/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/EnLPiVPYYos/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "pr_pr: Pay;", "Purpose: Learn;", "gds:Yes;", "Campaign: Pay Campaigns;", "ct: AIG;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "What's new in the pay plugin for Flutter", "description": "Enhance your Flutter app's payment flow with the latest updates to the Flutter pay plugin! This video guides you through the new features and improvements, including the new Google Pay button, customizable payment buttons, and a more straightforward integration experience.\n\nChapters: \n\n0:00 - Intro \n0:12 - Introduction\n0:23 - What’s new\n2:03 - Quick integration tutorial\n3:01 - Conclusion\n\nResources\nPay package in pub.dev –https://goo.gle/40kXV64 \nExample application in Flutter – https://goo.gle/flutter-pay-example \n\nConnect with us:\n\nJoin the conversation in the #payments channel on Discord → https://goo.gle/payments-dev-community\nWatch more Live Google Pay integrations on Android → https://goo.gle/live-google-pay \nFollow @GooglePayDevs on X for more content like this → https://goo.gle/GooglePayDevs  \nSubscribe to Google for Developers → https://goo.gle/developers  \n\nGet support: \n\nFor assistance with your implementation, create a support ticket from the Google Pay & Wallet Console → https://goo.gle/4hSy658 \n\nSpeaker: <PERSON> Mentioned: Google Pay"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT3M38S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "2874", "likeCount": "138", "favoriteCount": "0", "commentCount": "25"}}, {"kind": "youtube#video", "etag": "zrB-vWUJCvsKm-e8qEiz-7sLSwg", "id": "dVEcqpw17aY", "snippet": {"publishedAt": "2025-01-16T02:00:32Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "How can both teams signal task completion with cards?  Go!", "description": "This week’s developer challenge is all about communication. How would you signal “we’re done” when two teams can only communicate with playing cards? 🃏 Drop your solutions in the comments!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nSpeaker: <PERSON><PERSON>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/dVEcqpw17aY/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/dVEcqpw17aY/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/dVEcqpw17aY/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/dVEcqpw17aY/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/dVEcqpw17aY/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "pr_pr: <PERSON><PERSON>;", "Purpose: Learn;", "type:G4D SV: Educational ;", "gds:Yes;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "How can both teams signal task completion with cards?  Go!", "description": "This week’s developer challenge is all about communication. How would you signal “we’re done” when two teams can only communicate with playing cards? 🃏 Drop your solutions in the comments!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nSpeaker: <PERSON><PERSON>"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT48S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "2235", "likeCount": "92", "favoriteCount": "0", "commentCount": "15"}}, {"kind": "youtube#video", "etag": "dCw11k5w8yJUunqA-pf41zMotNs", "id": "Z9JF2KKGhoA", "snippet": {"publishedAt": "2025-01-15T00:00:47Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Build with Go in Project IDX", "description": "Discover how to use Project IDX, a cloud-based development environment, to create a Go web service. This tutorial provides a step-by-step guide from environment setup to coding and previewing your application, empowering you to build with Go efficiently.\n\nChapters:\n0:00 - Introduction\n0:27 - Setting up the Go development environment\n2:15 - Building the Go web server\n4:27 - Previewing the application\n5:54 - Exploring Project IDX templates\n6:31- Wrap up\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nSpeaker: <PERSON><PERSON>s Saba\nProducts Mentioned: Google AI Studio, Google AI, Go, Gemini", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/Z9JF2KKGhoA/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/Z9JF2KKGhoA/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/Z9JF2KKGhoA/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/Z9JF2KKGhoA/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/Z9JF2KKGhoA/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "pr_pr: <PERSON><PERSON>;", "Purpose: Learn;", "ct: AIG;;", "gds:Yes;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "Build with Go in Project IDX", "description": "Discover how to use Project IDX, a cloud-based development environment, to create a Go web service. This tutorial provides a step-by-step guide from environment setup to coding and previewing your application, empowering you to build with Go efficiently.\n\nChapters:\n0:00 - Introduction\n0:27 - Setting up the Go development environment\n2:15 - Building the Go web server\n4:27 - Previewing the application\n5:54 - Exploring Project IDX templates\n6:31- Wrap up\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nSpeaker: <PERSON><PERSON>s Saba\nProducts Mentioned: Google AI Studio, Google AI, Go, Gemini"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT7M1S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "7245", "likeCount": "236", "favoriteCount": "0", "commentCount": "11"}}, {"kind": "youtube#video", "etag": "RI6SPmy0lSE7Uuh-037QTMtTJTU", "id": "NoWqPZIZnG4", "snippet": {"publishedAt": "2025-01-10T17:31:15Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Better prompts = better code. 🤖", "description": "Help Gemini help you! Check out this tip for writing better prompts to get back more in-depth responses.\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nProducts Mentioned: Gemini", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/NoWqPZIZnG4/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/NoWqPZIZnG4/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/NoWqPZIZnG4/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/NoWqPZIZnG4/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/NoWqPZIZnG4/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "pr_pr: <PERSON><PERSON>;", "Purpose: Learn;", "Campaign: ;", "type:G4D SV: Educational ;", "gds:Yes;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "Better prompts = better code. 🤖", "description": "Help Gemini help you! Check out this tip for writing better prompts to get back more in-depth responses.\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nProducts Mentioned: Gemini"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT25S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "3611", "likeCount": "238", "favoriteCount": "0", "commentCount": "11"}}, {"kind": "youtube#video", "etag": "ty_a5A6--HhmHT3rICmUXPE7ey0", "id": "UDw35AXdx4w", "snippet": {"publishedAt": "2025-01-07T17:00:30Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Tip: See your webpage through different eyes on Chrome 👀", "description": "Make accessibility part of your design workflow. Chrome’s \"Emulate Vision Deficiencies\" tool helps you see your site through different vision types — and improve usability for everyone.  Try it out and start designing for all.\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nProducts Mentioned: Chrome", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/UDw35AXdx4w/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/UDw35AXdx4w/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/UDw35AXdx4w/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/UDw35AXdx4w/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/UDw35AXdx4w/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "pr_pr: <PERSON><PERSON>;", "Purpose: Learn;", "Campaign: ;", "type:G4D SV: Educational ;", "gds:Yes;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "Tip: See your webpage through different eyes on Chrome 👀", "description": "Make accessibility part of your design workflow. Chrome’s \"Emulate Vision Deficiencies\" tool helps you see your site through different vision types — and improve usability for everyone.  Try it out and start designing for all.\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nProducts Mentioned: Chrome"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT30S", "dimension": "2d", "definition": "hd", "caption": "false", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "2617", "likeCount": "135", "favoriteCount": "0", "commentCount": "5"}}, {"kind": "youtube#video", "etag": "kFHj67UAoJZtgTCVkLeGqqMUvio", "id": "NLhG3W0dDJw", "snippet": {"publishedAt": "2025-01-03T20:00:36Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "2024 Google AI highlights - Google Developer News December 2024", "description": "Join us on a trip down memory lane as we recap the biggest Google Developer news from 2024, including groundbreaking AI advancements like Gemini, Gemma, and more! See how Google for Developers has helped usher in a new era of generative AI and AI-augmented development, and stay tuned for some fun shoutouts!\n\nChapters:\n0:00 - Introduction\n1:05 - February 2024: Gemini 1.5\n1:24- April 2024: PaLM API and MakerSuite\n1:39 - May 2024: Gemma 2\n1:59 - August 2024: Gemini 1.5 Flash\n2:19 - October 2024: Gemini in OpenAI Library and grounding with Search\n2:41 - December 2024: Gemini 2\n2:51 - Wrap up \n\nResources:\nLearn more about Gemini 2.0 → https://goo.gle/3ZED514 \nGrounding in Gemini → https://goo.gle/3YCfuxH \nGemini 1.5 Flash in AI Studio → https://goo.gle/3BGy0xh \n\nWatch more Google Developer News → https://goo.gle/4e8Rysd  \nSubscribe to Google for Developers → https://goo.gle/developers \n\n#GoogleDeveloperNews \n\n\nProducts Mentioned: Gemini, Gemma 2, Gemini API, Gemini 1.5 Flash, Generative AI", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/NLhG3W0dDJw/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/NLhG3W0dDJw/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/NLhG3W0dDJw/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/NLhG3W0dDJw/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/NLhG3W0dDJw/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "pr_pr: <PERSON><PERSON>;", "Purpose: Learn;", "ct: AIG;;", "gds:Yes;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "2024 Google AI highlights - Google Developer News December 2024", "description": "Join us on a trip down memory lane as we recap the biggest Google Developer news from 2024, including groundbreaking AI advancements like Gemini, Gemma, and more! See how Google for Developers has helped usher in a new era of generative AI and AI-augmented development, and stay tuned for some fun shoutouts!\n\nChapters:\n0:00 - Introduction\n1:05 - February 2024: Gemini 1.5\n1:24- April 2024: PaLM API and MakerSuite\n1:39 - May 2024: Gemma 2\n1:59 - August 2024: Gemini 1.5 Flash\n2:19 - October 2024: Gemini in OpenAI Library and grounding with Search\n2:41 - December 2024: Gemini 2\n2:51 - Wrap up \n\nResources:\nLearn more about Gemini 2.0 → https://goo.gle/3ZED514 \nGrounding in Gemini → https://goo.gle/3YCfuxH \nGemini 1.5 Flash in AI Studio → https://goo.gle/3BGy0xh \n\nWatch more Google Developer News → https://goo.gle/4e8Rysd  \nSubscribe to Google for Developers → https://goo.gle/developers \n\n#GoogleDeveloperNews \n\n\nProducts Mentioned: Gemini, Gemma 2, Gemini API, Gemini 1.5 Flash, Generative AI"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT3M17S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "6026", "likeCount": "195", "favoriteCount": "0", "commentCount": "18"}}, {"kind": "youtube#video", "etag": "FjC1-OTin0saKzrSaTBc_oTl1GM", "id": "YYt_RwTrkaI", "snippet": {"publishedAt": "2024-12-30T22:06:06Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "What are your developer New Year’s resolutions? 💭", "description": "Try this prompt in Gemini to get on track with your coding goals for 2025. 💻✨\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nProducts Mentioned: Gemini", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/YYt_RwTrkaI/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/YYt_RwTrkaI/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/YYt_RwTrkaI/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/YYt_RwTrkaI/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/YYt_RwTrkaI/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "pr_pr: <PERSON><PERSON>;", "Purpose: Influence;", "Campaign: ;", "type:G4D SV: Educational ;", "gds:Yes;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "What are your developer New Year’s resolutions? 💭", "description": "Try this prompt in Gemini to get on track with your coding goals for 2025. 💻✨\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nProducts Mentioned: Gemini"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT31S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "2534", "likeCount": "104", "favoriteCount": "0", "commentCount": "12"}}, {"kind": "youtube#video", "etag": "xR3n_M0mxlRqQtI4NQOZ4_NwsAM", "id": "aTkBV0w6zX4", "snippet": {"publishedAt": "2024-12-26T14:00:01Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Tip: Keep an 👁️ on your app’s file sizes", "description": "Smaller APK, bigger impact. Use Android Studio’s APK comparison tool to analyze file sizes and make every MB count. Your users will appreciate the difference. 📱💾\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nProducts Mentioned: Android Studio, Android", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/aTkBV0w6zX4/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/aTkBV0w6zX4/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/aTkBV0w6zX4/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/aTkBV0w6zX4/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/aTkBV0w6zX4/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "pr_pr: <PERSON><PERSON>;", "Purpose: Influence;", "Campaign: ;", "type:G4D SV: Educational ;", "gds:Yes;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "Tip: Keep an 👁️ on your app’s file sizes", "description": "Smaller APK, bigger impact. Use Android Studio’s APK comparison tool to analyze file sizes and make every MB count. Your users will appreciate the difference. 📱💾\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nProducts Mentioned: Android Studio, Android"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT40S", "dimension": "2d", "definition": "hd", "caption": "false", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "2511", "likeCount": "114", "favoriteCount": "0", "commentCount": "7"}}, {"kind": "youtube#video", "etag": "-FK-8WsL0f6kFlrBC56NjfA5LMk", "id": "AcZINTsuIWI", "snippet": {"publishedAt": "2024-12-23T22:00:08Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Can you predict if this code will compile in C#? Go!", "description": "C# devs, we've got a new challenge for you! Can you predict the output of this code snippet involving interfaces and default implementations? Share your thoughts in the comments!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nSpeaker: <PERSON><PERSON>", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/AcZINTsuIWI/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/AcZINTsuIWI/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/AcZINTsuIWI/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/AcZINTsuIWI/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/AcZINTsuIWI/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "pr_pr: <PERSON><PERSON>;", "Purpose: Influence;", "Campaign: ;", "Type: G4D SV:Educational;", "gds:Yes;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "Can you predict if this code will compile in C#? Go!", "description": "C# devs, we've got a new challenge for you! Can you predict the output of this code snippet involving interfaces and default implementations? Share your thoughts in the comments!\n\nSubscribe to Google for Developers → https://goo.gle/developers \n\nSpeaker: <PERSON><PERSON>"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT23S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "5367", "likeCount": "124", "favoriteCount": "0", "commentCount": "21"}}, {"kind": "youtube#video", "etag": "-krAb37i7DF2T86j7hu1Tv66eQ8", "id": "gbObKqfqdlM", "snippet": {"publishedAt": "2024-12-19T19:23:20Z", "channelId": "UC_x5XG1OV2P6uZZ5FSM9Ttw", "title": "Multimodal Live API demo: GenList", "description": "The Multimodal Live API lets developers build real-time multimodal applications using Gemini 2.0 Flash Experimental. This is a starter demo that shows how you can call external APIs as part of your streaming application. Start building with Gemini 2.0 at ai.google.dev. \n\nResources:\nRead our Gemini 2.0 developer blog post→https://goo.gle/3Bl23dQ \nGet the code for this demo at →https://goo.gle/3ZOoUbd\n\nSubscribe to Google for Developers → https://goo.gle/developers  \n\nProducts Mentioned: Gemini", "thumbnails": {"default": {"url": "https://i.ytimg.com/vi/gbObKqfqdlM/default.jpg", "width": 120, "height": 90}, "medium": {"url": "https://i.ytimg.com/vi/gbObKqfqdlM/mqdefault.jpg", "width": 320, "height": 180}, "high": {"url": "https://i.ytimg.com/vi/gbObKqfqdlM/hqdefault.jpg", "width": 480, "height": 360}, "standard": {"url": "https://i.ytimg.com/vi/gbObKqfqdlM/sddefault.jpg", "width": 640, "height": 480}, "maxres": {"url": "https://i.ytimg.com/vi/gbObKqfqdlM/maxresdefault.jpg", "width": 1280, "height": 720}}, "channelTitle": "Google for Developers", "tags": ["Google", "developers", "pr_pr: Generative AI;", "Purpose: Learn;", "gds:N/A;"], "categoryId": "28", "liveBroadcastContent": "none", "defaultLanguage": "en", "localized": {"title": "Multimodal Live API demo: GenList", "description": "The Multimodal Live API lets developers build real-time multimodal applications using Gemini 2.0 Flash Experimental. This is a starter demo that shows how you can call external APIs as part of your streaming application. Start building with Gemini 2.0 at ai.google.dev. \n\nResources:\nRead our Gemini 2.0 developer blog post→https://goo.gle/3Bl23dQ \nGet the code for this demo at →https://goo.gle/3ZOoUbd\n\nSubscribe to Google for Developers → https://goo.gle/developers  \n\nProducts Mentioned: Gemini"}, "defaultAudioLanguage": "en"}, "contentDetails": {"duration": "PT2M14S", "dimension": "2d", "definition": "hd", "caption": "true", "licensedContent": false, "contentRating": {}, "projection": "rectangular"}, "statistics": {"viewCount": "5883", "likeCount": "202", "favoriteCount": "0", "commentCount": "17"}}]