import asyncio
import json
import random
import re
import threading
from abc import ABC, abstractmethod
from datetime import datetime
from logging import getLogger
from queue import Queue, Empty
from typing import (
    Any,
    Protocol,
    runtime_checkable,
    Optional,
    Callable,
    TypeAlias,
)
from urllib.parse import urlparse

import playwright.async_api
from asgiref.sync import async_to_sync
from playwright.async_api import Page
from pydantic import BaseModel
from typing_extensions import AsyncGenerator

from common.scraper.downloader import (
    PlaywrightDownloaderConfig,
)
from common.scraper.exceptions import ConfigError
from common.scraper.models import CrawledEntry, SocialProfile
from common.scraper.spiders import PossibleExpiredOrBannedException
from common.services import slack
from common.utils.datetimeutils import timeit
from common.utils.lang import get_caller_loc

logger = getLogger(__name__)


# -----  base configs  -----
class SpiderConfig(BaseModel, ABC):
    """the abstract base config for ALL spiders"""

    # name of the spider
    name: str = "default_spider"

    # url of the start point to crawl from
    start_url: str

    # credential for login (dict)
    credential: dict[str, str] = dict()

    # random sleep time range (seconds)
    sleep_min: int = 2
    sleep_max: int = 4

    # how many pages to crawl. <= 0 means no limit.
    pages: int = 10
    # how many items to crawl. <= 0 means no limit.
    items: int = 100

    class Config:
        arbitrary_types_allowed = True


class SearchSpiderConfig(SpiderConfig, ABC):
    """the abstract base config for all SEARCH like spiders"""

    # search term
    term: str = ""

    # crawl only date greater or equal than this date.
    min_ts: Optional[datetime] = None

    # control when to stop the search from the caller
    stop_callback: Callable[[str], bool] | None = None


class PlaywrightSpiderConfig(PlaywrightDownloaderConfig, SpiderConfig):
    """the base config for all Playwright implemented spiders"""

    pass


class PlaywrightSearchSpiderConfig(PlaywrightSpiderConfig, SearchSpiderConfig):
    """the base config for Playwright implemented SEARCH like spiders"""

    def __init__(self, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        if self.concurrency > 0:
            logger.debug(
                f"config 'concurrency' is ignored in {self.__class__.__name__}"
            )

    def check(self) -> bool:
        if not self.min_ts and self.pages <= 0 and self.items <= 0:
            raise ConfigError(
                "You must specify at least one filter condition. (min_ts, pages, items)"
            )
        return True


class PlaywrightSocialSearchSpiderConfig(
    PlaywrightSearchSpiderConfig, SearchSpiderConfig
):
    """the base config for Playwright implemented social media spiders"""

    # whether use lambda to process avatar pic
    use_lambda_avatar_processor: bool = True


SpiderNavAnchor: TypeAlias = Any


class SpiderResult(BaseModel):
    entries: dict[str, CrawledEntry] = dict()
    visited_pages: list[str] = list()
    ex: Optional[Exception] = None

    class Config:
        arbitrary_types_allowed = True

    def __str__(self) -> str:
        return f"error: {self.ex}" if self.ex else json.dumps(self.entries, default=str)


# -----  base spiders  -----


@runtime_checkable
class Spider(Protocol):
    """the abstracted interface for all spiders"""

    @abstractmethod
    def start(self) -> None:
        """start to crawl"""

    @timeit()
    @abstractmethod
    async def async_start(self) -> None:
        """start to crawl"""

    @abstractmethod
    def async_iterate(self) -> AsyncGenerator[CrawledEntry, None]:
        """start to crawl and iterate results (returns iterator)"""

    @abstractmethod
    def stop(self) -> None:
        """stop the crawling"""

    @abstractmethod
    async def async_stop(self) -> None:
        """stop the crawling"""


class BaseSpider(Spider, ABC):
    """the abstract base spider"""

    config: SpiderConfig

    _stop_event: asyncio.Event
    # _paging_event: asyncio.Event = asyncio.Event()

    _stop_callback: Callable[[str], bool] | None = None

    def __init__(self, config: SpiderConfig, *args: Any, **kwargs: Any) -> None:
        self.config = config
        self._stop_event = asyncio.Event()

    # def __del__(self) -> None:
    #     self.stop()
    #     traceback.print_stack()

    def start(self) -> None:
        """start to crawl"""
        return async_to_sync(self.async_start)()

    def stop(self) -> None:
        """stop the crawling"""
        logger.warning(f"setting stop event '{self.config.name}' at {get_caller_loc()}")
        self._stop_event.set()

    async def async_stop(self) -> None:
        """stop the crawling"""
        logger.debug(f"setting stop event '{self.config.name}' at {get_caller_loc()}")
        self._stop_event.set()

    @staticmethod
    def get_platform(url: str) -> str:
        r = urlparse(url)
        hostname = r.hostname or ""
        if hostname.endswith(".instagram.com"):
            platform = "instagram"
        elif hostname.endswith(".youtube.com"):
            platform = "youtube"
        elif hostname.endswith(".tiktok.com"):
            platform = "tiktok"
        elif hostname.endswith(".facebook.com"):
            platform = "facebook"
        else:
            platform = hostname
        return platform


class PlaywrightSpider(BaseSpider, ABC):
    """the abstract base spider built by Playwright"""

    config: PlaywrightSpiderConfig

    result: SpiderResult

    def __init__(
        self, config: PlaywrightSpiderConfig, *args: Any, **kwargs: Any
    ) -> None:
        super().__init__(config, *args, **kwargs)
        self.result = SpiderResult()

    @timeit()
    async def async_go_to(self, url: str, timeout: int = 0) -> bool:
        """go to a web page.

        :param url:
        :param timeout: seconds
        :return:
        """
        logger.info(f"visiting {url}")
        page = await self.async_get_page()

        device_info = await self.get_device_info(page)
        logger.debug(f"device info: {json.dumps(device_info, indent=2, default=str)}")

        timeout = (timeout if timeout > 0 else self.config.timeout) * 1000
        await page.goto(url, wait_until="load", timeout=timeout)
        logger.info(
            f"loaded {url}. ({'redirected to -> '+ page.url if page.url != url else''})"
        )
        await self.async_handle_login_expires(shall_raise=True)
        return True

    async def async_go_next(
        self, anchor: SpiderNavAnchor, is_infinity_page: bool = False, timeout: int = 0
    ) -> SpiderNavAnchor:
        logger.info(
            f"go next page {'(infinity)' if is_infinity_page else ''} - {anchor}, {self.config.name}"
        )
        if is_infinity_page:
            anchor = await self.async_scroll_a_page(anchor, timeout=timeout)
            await self.async_handle_login_expires(shall_raise=True)
        else:
            next_url = self.get_next_page_url(anchor)
            if next_url:
                self.result.visited_pages.append(next_url)
                anchor = await self.async_go_to(next_url, timeout=timeout)
        return anchor

    async def async_login(self) -> None:
        logger.debug("logged in")

    async def async_handle_login_expires(self, shall_raise: bool = False) -> None:
        if not self.config.is_auto_login:
            return

        page = await self.async_get_page()
        platform = self.get_platform(page.url)
        if platform not in ["instagram", "tiktok"]:
            return

        locator_timeout = 3000
        regex_login = re.compile(r"\blog\s*in\b", flags=re.I)
        try:
            login_in_url = page.url.index("login") >= 0
        except ValueError:
            login_in_url = False
        try:
            title = (
                await page.locator("head > title").text_content(timeout=locator_timeout)
                or ""
            )
            login_in_title = regex_login.search(title) is not None
        except playwright.async_api.TimeoutError as e:
            logger.warning(f"fail to locate title while handling login expiration. {e}")
            login_in_title = False

        login_button_visible = (
            await page.get_by_role("button", name=regex_login).count() > 0
            or await page.get_by_role("link", name=regex_login).count() > 0
        )

        if login_in_url or login_in_title or login_button_visible:
            msg = f'session {platform} possible expired or banned. "login" word in url or title. Or "login" button shows.'
            await self._async_send_alert(msg, force=shall_raise)
            await self.config.async_auto_login(platform=platform, rotate_session=True)
            if shall_raise:
                raise PossibleExpiredOrBannedException(msg)

    @staticmethod
    async def _async_send_alert(
        msg: str, trace_stack_back: int = 1, force: bool = False
    ) -> None:
        msg = f"{msg} - {get_caller_loc(trace_stack_back)}"
        slack.send_alert(msg, channel=slack.ENGINEERING_CHANNEL, force=force)

    async def async_scroll_a_page(
        self,
        anchor: SpiderNavAnchor = None,
        timeout: int = 0,
    ) -> SpiderNavAnchor:
        """simulate to scroll a page for infinity page.

        timeout: seconds.
        """

        if anchor is None:
            logger.info(f"reach the end of the page - {self.config.name}")
            return anchor

        assert isinstance(anchor, int)

        if 0 < self.config.pages <= anchor:
            # reach pages filter limitation
            logger.info("reach pages limitation.")
            return None

        timeout = (timeout if timeout > 0 else self.config.timeout) * 1000

        page = await self.async_get_page()
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        # await page.keyboard.press("End")
        await page.wait_for_load_state("networkidle", timeout=timeout)

        no_more_results = not await self.async_check_if_has_next_page()
        if no_more_results:
            logger.info("No more results found")
            return None

        return anchor + 1

    @abstractmethod
    async def async_check_if_has_next_page(self) -> bool:
        """check if there is a next page we can go"""

    @abstractmethod
    def get_next_page_url(self, anchor: SpiderNavAnchor = None) -> str:
        """get url for next page"""

    @staticmethod
    async def get_device_info(page: Page) -> dict:
        device_info = await page.evaluate(
            """
            () => ({
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                browserName: navigator.appName,
                browserVersion: navigator.appVersion,
                vendor: navigator.vendor,
                language: navigator.language,

                screenWidth: window.screen.width,
                screenHeight: window.screen.height,
                colorDepth: window.screen.colorDepth,
                pixelRatio: window.devicePixelRatio
            })
        """
        )
        device_info["viewport_size"] = page.viewport_size
        return device_info

    async def async_sleep(
        self, low: Optional[int] = None, high: Optional[int] = None
    ) -> None:
        low = low or self.config.sleep_min
        high = high or self.config.sleep_max
        v = round(
            random.randrange(low * 100, high * 100) / 100,
            4,
        )
        logger.debug(f"sleeping {v} seconds")
        await asyncio.sleep(v)

    # @timeit()
    async def async_get_page(self, create_new: bool = False) -> Page:
        """to get an active tab page. managed.

        :param create_new: whether create a new page (default False)
        :param auto_gc: whether create a new page if reach page usage limit (for gc, default True)
        :return:
        """
        return await self.config.async_get_browser_page(
            create_new=create_new, auto_gc=False
        )


class PlaywrightSearchSpider(PlaywrightSpider, ABC):
    """the abstract search spider built by Playwright"""

    config: PlaywrightSearchSpiderConfig


class PlaywrightSocialSearchSpider(PlaywrightSearchSpider, ABC):
    """the abstract social media search spider built by Playwright"""

    config: PlaywrightSocialSearchSpiderConfig
    all_entries: dict[str, SocialProfile] = {}
    item_count: int = 0
    _cached_profiles: dict[str, SocialProfile] = {}

    def _check_config(self) -> None:
        if not self._stop_callback:
            self.config.check()

    def _reset(self) -> None:
        self._stop_event.clear()
        self.all_entries = dict()
        self.item_count = 0
        self._cached_profiles = dict()

    async def async_start(self) -> None:
        """start the search. Get result at self.result .
        :return:
        """
        logger.debug(f"searching '{self.config.term}'")

        self._check_config()
        self._reset()
        self.result = r = SpiderResult()

        try:
            await self.config.async_get_browser()
            r.entries = await self._async_search(self.config.term)
        except Exception as ex:
            r.ex = ex
        finally:
            await self.config.async_close()

    async def async_iterate(self) -> AsyncGenerator[CrawledEntry, None]:  # noqa: C901
        # We HAVE to use threading to perform searching (async not work)
        # So we need to make playwright, browser, tab page created in same thread.
        if self.config._playwright is not None:
            raise RuntimeError("please use NEW config & spider to run async_iterate.")

        self._check_config()
        self._reset()
        query = self.config.term
        logger.info(f"iterating search '{query}' results ...")

        q_iter: Queue = Queue()
        finished = asyncio.Event()

        async def __inner_search() -> dict[str, CrawledEntry]:
            rc = {}
            await self.config.async_get_browser()
            try:
                rc = await self._async_search(query, q_iter=q_iter)
            except PossibleExpiredOrBannedException:
                # logger.exception(e)
                logger.error(
                    f"possible login expired or banned. stopping {self.config.name}."
                )
                await self.async_stop()
            except Exception as e:
                # logger.exception(e)
                logger.warning(f'stop "{query}" search due to error: {e}')
                q_iter.put(e)
            finally:
                logger.info(f'search "{query}" thread finished')
                finished.set()
                await self.config.async_close()

            return rc

        def __inner_thread_run_search(name: str = "") -> None:
            if name:
                threading.current_thread().name = name
            asyncio.run(__inner_search())

        # Start the search in a separate thread without blocking
        asyncio.create_task(asyncio.to_thread(__inner_thread_run_search, query))

        try:
            while not finished.is_set():
                if await self.async_shall_stop(query):
                    logger.warning(f"stop triggered ({query}).")
                    break
                try:
                    obj = q_iter.get_nowait()
                    if isinstance(obj, Exception):
                        logger.warning(f"got error: {obj}")
                        # traceback.print_exception(obj)
                    else:
                        logger.debug(f"got {str(obj)}")
                        yield obj
                    q_iter.task_done()
                except Empty:
                    pass
                await asyncio.sleep(0.01)
        except Exception as e:
            logger.warning(f"error while iterate search '{query}' results. {e}")
        finally:
            logger.info(f"-- finished iterating search '{query}' results")

    @abstractmethod
    async def _async_search(
        self, query: str, q_iter: Optional[Queue] = None
    ) -> dict[str, CrawledEntry]:
        """search for the query

        if q_iter (Queue) is not None, put results into the queue.
        Otherwise, append results to self.result.entries.

        :param query: the search query
        :param q_iter: optional queue for the result entries
        :return: search result entries. empty if q_iter is not None.
        """

    def get_next_page_url(self, anchor: SpiderNavAnchor = None) -> str:
        # so far, we do not use this
        raise NotImplementedError

    async def async_shall_stop(self, term: str = "") -> bool:
        loc = get_caller_loc()

        if self._stop_event.is_set():
            logger.info(f"stop event is set for '{self.config.name}'. check from {loc}")
            return True

        if 0 < self.config.items <= self.item_count:
            logger.info(
                f"reach config items count {self.item_count}>={self.config.items} for '{self.config.name}'. check from {loc}"
            )
            return True

        if self.config.stop_callback and term:
            if asyncio.iscoroutinefunction(self.config.stop_callback):
                _shall_stop = await self.config.stop_callback(term)
            else:
                _shall_stop = self.config.stop_callback(term)
            if _shall_stop:
                logger.info(f"stop callback returns True. check from {loc}")
            return _shall_stop
        return False

    async def download_social_avatar(self, avatar_url: str, handle_url: str) -> None:
        try:
            # Process avatar using Lambda in a separate task
            from common.scraper.utils.lambda_invoker import LambdaInvoker

            if self.config.use_lambda_avatar_processor and avatar_url and handle_url:
                asyncio.create_task(
                    LambdaInvoker.invoke_avatar_processor(
                        avatar=avatar_url, home_page=handle_url
                    )
                )
        except Exception as e:
            logger.warning(f"error while download avatar. {e}")
