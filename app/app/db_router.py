from logging import getLogger
from typing import Any

logger = getLogger(__name__)


class DatasvcDbRouter:
    """
    A router to control all database operations on models in the "datasvc" application.
    """

    app_to_db = {
        "agent": "default",
        "datasvc": "datasvc",
    }

    def db_for_read(self, model: Any, **hints: Any) -> str:
        """
        Attempts to read auth and contenttypes models.
        """
        return self.app_to_db.get(model._meta.app_label, "default")

    def db_for_write(self, model: Any, **hints: Any) -> str:
        """
        Attempts to write auth and contenttypes models
        """
        return self.app_to_db.get(model._meta.app_label, "default")

    def allow_migrate(
        self, db: str, app_label: str, model_name: str = "", **hints: Any
    ) -> bool:
        """
        Make sure the tables of apps only appear in the correct database.
        """
        if self.app_to_db.get(app_label) == db:
            return True
        if self.app_to_db.get(app_label) is None and db == "default":
            return True
        return False
