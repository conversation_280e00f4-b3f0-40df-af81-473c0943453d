"""
URL configuration for app project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
import os
import re

from django.conf import settings
from django.contrib import admin
from django.urls import include, path, re_path
from django.views.static import serve

from agent.restapi.v1 import restapi as api_v1

urlpatterns = [
    path("api/v1/", api_v1.urls),
    path("admin/", admin.site.urls),
    path("accounts/", include("accounts.urls")),
    path("account/", include("allauth.urls")),
    path("_allauth/", include("allauth.headless.urls")),
    path("tools/", include("tools.urls")),
    path("p/", include("payment.urls")),
]


if not settings.DEBUG and os.getenv("ENV") != "prod":
    urlpatterns += (
        re_path(
            r"^%s(?P<path>.*)$" % re.escape(settings.MEDIA_URL.lstrip("/")),
            serve,
            kwargs={"document_root": settings.MEDIA_ROOT},
        ),
    )
