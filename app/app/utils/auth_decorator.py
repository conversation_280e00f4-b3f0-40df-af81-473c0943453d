# your_project/decorators.py

from functools import wraps
from django.http import HttpRequest, JsonResponse
from typing import Any, Callable


def login_required(view_func: Callable[..., Any]) -> Callable[..., Any]:
    @wraps(view_func)
    def _wrapped_view(request: HttpRequest, *args: Any, **kwargs: Any) -> Any:
        if not request.session.get("id"):
            return JsonResponse({"message": "user not logged in"}, status=401)
        return view_func(request, *args, **kwargs)

    return _wrapped_view
