# type: ignore
"""
Django settings for app project.

Generated by 'django-admin startproject' using Django 4.2.2.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
from pathlib import Path

import openai
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# conf must be imported after env loaded.
from agent import conf  # noqa:E402

openai.api_key = os.getenv("OPENAI_API_KEY")
openai.proxy = os.getenv("https_proxy", os.getenv("http_proxy", None))

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv(
    "SECRET_KEY", "django-insecure-%-m$lx!h+7!d(m!(1^um69d#!!brzeq)k#m&jvt^$1x8hhsv!*"
)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = conf.DEBUG

ALLOWED_HOSTS = [
    "*.onwish.ai",
    "*.autogather.ai",
    "app.autogather.ai",
    "autogather-backends",
    "admin.autogather.ai",
    "atg-nginx-0",
    "atg0",
    "atg1",
]
CSRF_TRUSTED_ORIGINS = [
    "http://*.onwish.ai",
    "https://*.onwish.ai",
    "https://*.autogather.ai",
]

if DEBUG:
    ALLOWED_HOSTS += ["*"]
    CSRF_TRUSTED_ORIGINS += [
        "https://*.ngrok-free.app",
        "http://127.0.0.1",
        "http://localhost",
        "http://localhost:5173",
    ]

# Application definition

INSTALLED_APPS = [
    "daphne",
    "chat",
    "aiflow",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "agent",
    "tools",
    "django_json_widget",
    "django_extensions",
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "allauth.socialaccount.providers.google",
    "datasvc",
    "datum",
    "payment",
    "jobs",
    # "django_celery_results",
]

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer",
        "CONFIG": {
            "capacity": 9999999,
            "expiry": 86400,
        },
    }
}


AUTHENTICATION_BACKENDS = [
    # Needed to login by username in Django admin, regardless of `allauth`
    "django.contrib.auth.backends.ModelBackend",
    # `allauth` specific authentication methods, such as login by email
    "allauth.account.auth_backends.AuthenticationBackend",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "allauth.account.middleware.AccountMiddleware",
]

# Cookie settings
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = "Lax"

ROOT_URLCONF = "app.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

# Daphne
ASGI_APPLICATION = "app.asgi.application"

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases
# multi-db: https://docs.djangoproject.com/en/5.0/topics/db/multi-db/#topics-db-multi-db-hints

# obtain db configs from env variables.

# default database
_DATABASES = {
    "sqlite": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db.sqlite3",
    },
    "postgresql": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.getenv("PG_DB", ""),
        "USER": os.getenv("PG_USER", ""),
        "PASSWORD": os.getenv("PG_PASSWORD"),
        "HOST": os.getenv("PG_HOST", ""),
        "PORT": os.getenv("PG_PORT", "5432"),
    },
}
# datasvc database
_DATABASES_DS = {
    "sqlite": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "datasvc.sqlite3",
    },
    "postgresql": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.getenv("PG_DB.DS", ""),
        "USER": os.getenv("PG_USER.DS", ""),
        "PASSWORD": os.getenv("PG_PASSWORD.DS"),
        "HOST": os.getenv("PG_HOST.DS", ""),
        "PORT": os.getenv("PG_PORT.DS", "5432"),
    },
}

# configure db by selecting a engine.
_DB_ENGINE = os.getenv("DB_ENGINE", "sqlite")
_DB_ENGINE_DS = os.getenv("DB_ENGINE.DS", "sqlite")
DATABASES = {
    "default": _DATABASES[_DB_ENGINE],
    "datasvc": _DATABASES_DS[_DB_ENGINE_DS],
}
DATABASE_ROUTERS = ["app.db_router.DatasvcDbRouter"]


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"
STATIC_ROOT = "/opt/onwish/static/"
STATICFILES_DIRS = [
    BASE_DIR.parent / "static",
]

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# temp cache for dev. run `python app/manage.py createcachetable`
_CACHE_ENGINES = {
    "database": "django.core.cache.backends.db.DatabaseCache",
    "redis": "django.core.cache.backends.redis.RedisCache",
    "filesystem": "django.core.cache.backends.filebased.FileBasedCache",
}
CACHES = {
    "default": {
        "BACKEND": _CACHE_ENGINES[os.getenv("CACHE_ENGINE.0", "database")],
        "LOCATION": os.getenv("CACHE_LOCATION.0", "cache_default"),
    },
    "download": {
        "BACKEND": _CACHE_ENGINES[os.getenv("CACHE_ENGINE.1", "database")],
        "LOCATION": os.getenv("CACHE_LOCATION.1", "cache_download"),
        "TIMEOUT": 3600 * 24 * 30,
        "OPTIONS": (
            {"MAX_ENTRIES": 100000} if os.getenv("CACHE_ENGINE.1") != "redis" else {}
        ),
    },
    "llm_complete": {
        "BACKEND": _CACHE_ENGINES[os.getenv("CACHE_ENGINE.2", "database")],
        "LOCATION": os.getenv("CACHE_LOCATION.2", "cache_llm_complete"),
    },
    "llm_streaming": {
        "BACKEND": _CACHE_ENGINES[os.getenv("CACHE_ENGINE.3", "database")],
        "LOCATION": os.getenv("CACHE_LOCATION.3", "cache_llm_streaming"),
    },
}


LOG_FORMATTER = os.getenv("LOG_FORMATTER", "standard")
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_COLOR_DEBUG = os.getenv("LOG_COLOR_DEBUG", "thin_black")
LOG_COLOR_INFO = os.getenv("LOG_COLOR_INFO", "white")
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "[%(levelname)s]%(asctime)s|%(name)s:%(lineno)d| %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        "colorful": {
            "format": "%(log_color)s[%(asctime)s] %(levelname).1s [%(processName).04s][%(threadName).016s][%(name)s:%(lineno)d] %(message)s",
            "()": "colorlog.ColoredFormatter",
            "reset": True,
            "log_colors": {
                "DEBUG": LOG_COLOR_DEBUG,
                "INFO": LOG_COLOR_INFO,
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "red,bg_white",
            },
            "secondary_log_colors": {},
        },
    },
    "filters": {
        "supervisorNameFilter": {
            "()": "common.libenhance.supervisor.SupervisorProcessFilter",
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": LOG_FORMATTER,
            "filters": ["supervisorNameFilter"],
        },
    },
    "root": {
        "handlers": ["console"],
        "level": LOG_LEVEL,
    },
    "loggers": {
        **{
            name: {"level": "INFO"}
            for name in [
                "django.db.backends",
                "stripe",
            ]
        },
        **{
            name: {"level": "WARNING"}
            for name in [
                "httpx",
                "urllib3",
                "charset_normalizer",
                "telegram",
                "httpcore",
                "asyncio",
                "googleapiclient",
                "apify_client",
                "nltp_data",
                "daphne",
                "anthropic_bedrock",
                "botocore",
                "boto3",
                "s3transfer",
                "anthropic",
                "openai",
                "markdown_it",
                "selenium",
                "stripe",
                "websockets.server",
                "websockets.protocol",
                "django.channels.server",
            ]
        },
    },
}

# ----- allauth ----

SOCIALACCOUNT_PROVIDERS = {
    "google": {
        # For each OAuth based provider, either add a ``SocialApp``
        # (``socialaccount`` app) containing the required client
        # credentials, or list them here:
        "APP": {
            "client_id": os.environ["GOOGLE_CLIENT_ID"],
            "secret": os.environ["GOOGLE_CLIENT_SECRET"],
            "key": os.environ["GOOGLE_API_KEY"],
        }
    }
}

LOGIN_REDIRECT_URL = "/accounts/login_complete/"
DEFAULT_FROM_EMAIL = "<EMAIL>"

ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = True
ACCOUNT_AUTHENTICATION_METHOD = "email"
ACCOUNT_CONFIRM_EMAIL_ON_GET = True
ACCOUNT_EMAIL_SUBJECT_PREFIX = f"[{os.getenv('SITE_NAME')}] "
ACCOUNT_EMAIL_CONFIRMATION_COOLDOWN = 10
ACCOUNT_ADAPTER = "agent.allauth.CustomAccountAdapter"
ACCOUNT_EMAIL_CONFIRMATION_ANONYMOUS_REDIRECT_URL = "/email-confirmation-anonymous"
ACCOUNT_EMAIL_CONFIRMATION_AUTHENTICATED_REDIRECT_URL = (
    "/email-confirmation-authenticated"
)
ACCOUNT_USERNAME_BLACKLIST = [
    "admin",
    "superuser",
    "onwish",
    "administrator",
    "root",
    "service",
    "services",
]
ACCOUNT_USERNAME_MIN_LENGTH = 4
ACCOUNT_EMAIL_UNKNOWN_ACCOUNTS = False
SOCIALACCOUNT_ADAPTER = "agent.allauth.CustomSocialAccountAdapter"
ACCOUNT_DEFAULT_HTTP_PROTOCOL = "https"

# EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"
EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = os.environ.get("EMAIL_SMTP_HOST", "")
EMAIL_PORT = int(os.environ.get("EMAIL_SMTP_PORT", "0"))
EMAIL_USE_SSL = os.getenv("EMAIL_USE_SSL", "").lower() in ["true", "on", "1", "yes"]
EMAIL_HOST_USER = os.environ.get("EMAIL_HOST_USER", "")
EMAIL_HOST_PASSWORD = os.environ.get("EMAIL_HOST_PASSWORD", "")

# AIFLOW
AIFLOW_REQUEST_LIMIT = int(os.getenv("AIFLOW_REQUEST_LIMIT", 2))
