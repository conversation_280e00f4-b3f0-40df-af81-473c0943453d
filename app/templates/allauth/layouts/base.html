{% load i18n %}
<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/static/bootstrap.min.css" />
    <style>
      body {
        padding: 1em;
      }
      :root {
        --bs-body-font-size: 12px;
      }
      .btn {
        font-size: 12px;
      }
      .message-wrapper {
        border: solid 1px #ccc;
        background-color: #f2f2f2;
        padding: 1em;
        border-radius: 12px;
        margin-bottom: 1em;
      }

      .message-wrapper ul {
        width: auto;
        margin: 0 auto;
      }
      ul {
        list-style-type: none;
      }
      ul.errorlist {
        color: red;
      }
    </style>
    <title>{% block head_title %} {% endblock head_title %}</title>
    {% block extra_head %} {% endblock extra_head %}
  </head>
  <body>
    {% block body %} {% if messages %}
    <div class="message-wrapper">
      <strong>{% trans "Messages:" %}</strong>
      <ul>
        {% for message in messages %}
        <li>{{ message }}</li>
        {% endfor %}
      </ul>
    </div>
    {% endif %} {% block content %} {% endblock content %} {% endblock body %}
    {% block extra_body %} {% endblock extra_body %}
  </body>
</html>
