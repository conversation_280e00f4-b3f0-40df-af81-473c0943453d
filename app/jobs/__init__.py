"""
Job System
===========

This is a monitor-able job system to have immediate, scheduled and etc jobs to
be run in distributed system.

We have all kinds of jobs or tasks to be run. They need to be implemented with
various, complex and heterogeneous libs, frameworks, tools.

Introducing this system to unify all of this. It has the goals & benefits:

- Immediate Background Task - (***Any heavy blocking task***)
- Scheduled Task - (***Importing jobs***)
- Periodic Task - (***Downloading & Indexing jobs***)
- Distributed Tasks - (***Any task which wants to be scalable***)
- Pipeline - (***Scheduled Task & Pipeline can replace `Airflow`***)


# References

Since `Django` is introducing a built-in task app in 5.x core. But it will not
replace `Celery` in see-able future (maybe 3-5 years). So we built this based on Celery.


- [django-tasks - bringing background workers in to Django core](https://forum.djangoproject.com/t/django-tasks-bringing-background-workers-in-to-django-core/32967)
- [`django-task` on GitHub](https://github.com/realOrangeOne/django-tasks)
- [DEP 0014: Background workers](https://github.com/django/deps/blob/main/accepted/0014-background-workers.rst)


# Get Started

## requirements

install with pip. (`pip install celery flower
- required
    - celery - https://docs.celeryq.dev/en/stable/index.html
- optional
    - flower - for monitoring
        - https://flower.readthedocs.io/en/latest/index.html
    - django-celery-results - for store results
        - https://django-celery-results.readthedocs.io/en/latest/getting_started.html

"""

from .base import JobBase
from .celery_impl import CeleryJobber

jobber = CeleryJobber("onwish")
celery = jobber._celery  # for celery only
__all__ = ["jobber"]


@jobber.task(name="example", ignore_result=True)
def example_task(self: JobBase) -> None:
    print(f"Request: {self.request!r}")


# # example_task.delay()
