# Register your models here.
from typing import Any

import django.db.models
from django.conf import settings
from django.contrib import admin
from django.core.exceptions import ValidationError
from django.utils.safestring import mark_safe

from common.django_ext.model import CreateTimeMixin, UpdateTimeMixin
from common.utils.datetimeutils import LOCAL_TZ
from common.utils.lang import import_from_module
from common.utils.strutil import ellipse
from datasvc.models import (
    EarningCall,
    News,
    SECFiling,
    DocModel,
    Conference,
    PressRelease,
    Tweet,
    YoutubeVideo,
    CustomerDoc,
    EventTranscript,
)


# TODO(data): fix the admin pages.


@admin.display(description="created at")
def created_at_disp(obj: CreateTimeMixin) -> str:
    dt = obj.created_at.astimezone(LOCAL_TZ)
    # TODO: add UTC, NYT, CST
    return mark_safe(dt.strftime("%y-%m-%d<br/>%H:%M:%S"))


@admin.display(description="updated at")
def updated_at_disp(obj: UpdateTimeMixin) -> str:
    # TODO: Timezone
    return mark_safe(obj.created_at.strftime("%y-%m-%d<br/>%H:%M:%S"))


@admin.display(description="publish date")
def pub_date_disp(obj: DocModel) -> str:
    # TODO: Timezone
    return mark_safe(obj.pub_date.strftime("%y-%m-%d %H:%M:%S"))


@admin.display(description="content (truncated 500)")
def content_disp(obj: DocModel) -> str:
    return mark_safe(obj.content.get_data())


@admin.display(description="content (truncated 500)")
def content_ellipse_disp(obj: DocModel) -> str:
    return mark_safe(ellipse(obj.content.get_data(), 500))


@admin.display(description="score")
def quality_score_disp(obj: DocModel) -> str:
    return f"{round(obj.quality_score, 4)}" if obj.quality_score is not None else "-"


def _get_db_using(model: django.db.models.Model) -> tuple[str, str]:
    """get db setting names
    returns tuple[str,str] like (db_for_read, db_for_write)
    """
    db_for_read, db_for_write = "", ""
    for type_name in settings.DATABASE_ROUTERS:
        cls = import_from_module(type_name)
        if cls:
            db_router = cls()
            r = db_router.db_for_read(model)
            w = db_router.db_for_write(model)
            if r:
                if db_for_read and db_for_read != r:
                    raise ValidationError(
                        f"db_for_read has conflict value {db_for_read} and {r}"
                    )
                else:
                    db_for_read = r
            if r:
                if db_for_write and db_for_write != w:
                    raise ValidationError(
                        f"db_for_write has conflict value {db_for_write} and {w}"
                    )
                else:
                    db_for_write = w

    return db_for_read or "default", db_for_write or "default"


class MultiDBModelAdmin(admin.ModelAdmin):
    # A handy constant for the name of the alternate database.
    db_for_read = "default"
    db_for_write = "default"

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.db_for_read, self.db_for_write = _get_db_using(self.model)

    def save_model(self, request: Any, obj: Any, form: Any, change: Any) -> None:
        # Tell Django to save objects to the 'other' database.
        obj.save(using=self.db_for_write)

    def delete_model(self, request: Any, obj: Any) -> None:
        # Tell Django to delete objects from the 'other' database
        obj.delete(using=self.db_for_write)

    def get_queryset(self, request: Any) -> django.db.models.QuerySet:
        # Tell Django to look for objects on the 'other' database.
        return super().get_queryset(request).using(self.db_for_read)

    def formfield_for_foreignkey(
        self, db_field: Any, request: Any, **kwargs: Any
    ) -> django.forms.Field:
        # Tell Django to populate ForeignKey widgets using a query
        # on the 'other' database.
        return super().formfield_for_foreignkey(
            db_field, request, using=self.db_for_read, **kwargs
        )

    def formfield_for_manytomany(
        self, db_field: Any, request: Any, **kwargs: Any
    ) -> django.forms.Field:
        # Tell Django to populate ManyToMany widgets using a query
        # on the 'other' database.
        return super().formfield_for_manytomany(
            db_field, request, using=self.db_for_read, **kwargs
        )


class EarningCallAdmin(MultiDBModelAdmin):
    list_display = [
        "id",
        quality_score_disp,
        "ticker",
        "fiscal_year",
        "fiscal_quarter",
        pub_date_disp,
    ]
    list_display_links = ["id", quality_score_disp, "ticker"]
    list_filter = ["ticker"]
    search_fields = ["url"]
    date_hierarchy = "pub_date"
    readonly_fields = [
        "id",
        "quality_score",
        "ticker",
        "fiscal_year",
        "fiscal_quarter",
        "pub_date",
        "created_at",
    ]


class NewsAdmin(MultiDBModelAdmin):
    list_display = [
        "id",
        quality_score_disp,
        "ticker",
        "title",
        "provider",
        pub_date_disp,
    ]
    list_display_links = ["id", quality_score_disp, "ticker", "title"]
    list_filter = ["provider"]
    search_fields = ["title", "original_url"]
    date_hierarchy = "pub_date"
    readonly_fields = [
        "id",
        "quality_score",
        "ticker",
        "title",
        "provider",
        "pub_date",
        "original_url",
        "created_at",
    ]


class SecFilingAdmin(MultiDBModelAdmin):
    # form = SecFilingForm
    exclude = ["content"]
    list_display = [
        "id",
        quality_score_disp,
        "ticker",
        "fiscal_year",
        "fiscal_quarter",
        pub_date_disp,
    ]
    list_display_links = ["id", quality_score_disp, "ticker"]
    list_filter = ["filing_type"]
    search_fields = ["url"]
    date_hierarchy = "pub_date"
    readonly_fields = [
        "id",
        "quality_score",
        "ticker",
        "fiscal_year",
        "fiscal_quarter",
        "pub_date",
        content_ellipse_disp,
        "created_at",
        "filing_type",
    ]


class ConferenceAdmin(MultiDBModelAdmin):
    pass


class PressReleaseAdmin(MultiDBModelAdmin):
    pass


class TweetAdmin(MultiDBModelAdmin):
    pass


class YoutubeVideoAdmin(MultiDBModelAdmin):
    pass


class EventTranscriptAdmin(MultiDBModelAdmin):
    pass


class CustomerDocAdmin(MultiDBModelAdmin):
    pass


admin.site.register(EarningCall, EarningCallAdmin)
admin.site.register(SECFiling, SecFilingAdmin)
admin.site.register(News, NewsAdmin)
admin.site.register(Conference, ConferenceAdmin)
admin.site.register(PressRelease, PressReleaseAdmin)
admin.site.register(Tweet, TweetAdmin)
admin.site.register(YoutubeVideo, YoutubeVideoAdmin)
admin.site.register(CustomerDoc, CustomerDocAdmin)
admin.site.register(EventTranscript, EventTranscriptAdmin)
