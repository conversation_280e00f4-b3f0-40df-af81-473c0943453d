import asyncio
import itertools
from datetime import datetime
from logging import getLogger
from typing import Any, Dict, Iterable, Sequence, ClassVar

import requests
from langchain.text_splitter import RecursiveCharacterTextSplitter

from biz.enums import DocTypeEnum, DataProviderEnum
from biz.structs import Doc<PERSON>hunk, Period
from common.utils.asyncioutil import gather_with_error, with_concurrency_limit
from datasvc import conf
from datasvc.indexing.indexer.base import Indexer
from datasvc.models import SECFiling
from datasvc.providers.financial_modeling_prep import FMPAPI
from datasvc.providers.sec_api import SEC_SECTIONS, SECAPI

logger = getLogger(__name__)


class SECFilingIndexer(Indexer):
    DEFAULT_FETCH_DAYS: ClassVar[int] = 5 * 365 + 2  # 5 years

    def get_doc_type(self) -> str:
        return DocTypeEnum.SEC_FILING.value

    async def _update(
        self, ticker: str | None, min_ts: datetime, **kwargs: Any
    ) -> list[SECFiling]:
        # TODO(data): deprecated
        assert (
            ticker is not None
        ), f"{self.get_doc_type()} does not support update across tickers (ticker is None)."

        # TODO(data): update from both Quartr and SeekingAlpha as well.

        # check if there is fresh update
        periods_by_ticker = await FMPAPI().get_most_recent_financial_periods(
            [ticker], min_ts=min_ts
        )
        if ticker not in periods_by_ticker:
            return []

        docs = []
        periods = periods_by_ticker[ticker]
        filing_date_to_period = {period.filing_date: period for period in periods}

        # let's raise the error due to only 1 ticker, no need to distinguish doc or errs.
        result = await FMPAPI().download_sec_filings([ticker], min_ts)

        for d in result:
            pub_date = d["filing_date"]
            dt_str = pub_date.strftime("%Y-%m-%d")
            period = filing_date_to_period.get(dt_str, None)
            if not period:
                self.append_error(
                    f"period not found for {ticker} SEC Filing on {dt_str}"
                )
                continue

            doc = SECFiling(
                ticker=ticker,
                quality_score=d.get("quality_score", 0.0),
                pub_date=pub_date,
                provider=DataProviderEnum.FMP,
                original_url=d["url"],
                fiscal_year=period.y,
                fiscal_quarter=period.q,
                filing_type=d["filing_type"],
                key=SECFiling.build_doc_key(ticker, period.y, period.q),
            )
            docs.append(doc)

        return docs

    async def _update_v2(
        self, ticker_min_ts_mapping: dict[str, datetime], **kwargs: Any
    ) -> tuple[list[SECFiling], list[BaseException]]:
        # check if there is fresh update
        periods_by_ticker = await FMPAPI().get_most_recent_financial_periods_v2(
            ticker_min_ts_mapping
        )

        async def __inner_fetch(
            _ticker: str, _periods: list[Period]
        ) -> list[SECFiling]:
            assert _ticker in ticker_min_ts_mapping
            _min_ts = ticker_min_ts_mapping.get(_ticker)

            _docs: list[SECFiling] = []

            filing_date_to_period = {period.filing_date: period for period in _periods}
            result = await FMPAPI().download_sec_filings([_ticker], _min_ts)
            logger.debug(
                f"fetched {len(_docs)} {_ticker} {self.get_doc_type()} docs since {_min_ts} ..."
            )
            for d in result:
                pub_date = d["filing_date"]
                dt_str = pub_date.strftime("%Y-%m-%d")
                period = filing_date_to_period.get(dt_str, None)
                if not period:
                    self.append_error(
                        f"period not found for {_ticker} SEC Filing on {dt_str}"
                    )
                    continue

                doc = SECFiling(
                    ticker=_ticker,
                    quality_score=d.get("quality_score", 0.0),
                    pub_date=pub_date,
                    provider=DataProviderEnum.FMP,
                    original_url=d["url"],
                    fiscal_year=period.y,
                    fiscal_quarter=period.q,
                    filing_type=d["filing_type"],
                    key=SECFiling.build_doc_key(_ticker, period.y, period.q),
                )
                _docs.append(doc)
            return _docs

        results, errs = await gather_with_error(
            *with_concurrency_limit(
                [
                    __inner_fetch(ticker, periods)
                    for ticker, periods in periods_by_ticker.items()
                ],
                limit=15,
            ),
            log_title=f"update {self.get_doc_type()} across tickers",
            log_errors=True,
        )
        return list(itertools.chain(*results)), errs

    async def _download_docs(
        self, docs: Sequence[SECFiling], **kwargs: Any
    ) -> list[SECFiling]:
        async def _fetch(doc: SECFiling) -> SECFiling:
            e: str | BaseException = ""
            try:
                resp = requests.get(
                    doc.original_url, headers={"User-Agent": "ow <EMAIL>"}
                )
                if resp.status_code == 200:
                    doc.content.set_data(resp.text)
                else:
                    e = f"{resp.status_code} {resp.reason}"
                    await asyncio.sleep(2)  # avoid ban
            except Exception as err:
                e = err

            if e:
                logger.error(f"fail to download {doc}. {e}")
                doc.set_last_error_and_status(e)
            return doc

        coroutines = [_fetch(doc) for doc in docs]
        new_docs, errs = await gather_with_error(
            *with_concurrency_limit(coroutines, limit=self.DOWNLOAD_CONCURRENT_LIMIT),
            log_errors=True,
            log_title="downloading conference",
            log_with_traceback=conf.DEBUG,
        )
        return new_docs

    async def _split_doc_contents(
        self, docs: Iterable[SECFiling]
    ) -> dict[str, tuple[SECFiling, list[DocChunk]]]:
        # TODO: actually split the filings, extract management notes

        doc_key_to_chunks = {}

        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
        )
        for doc in docs:
            try:
                sec_sections_to_data = await self._fanout_sec_sections(doc)
                doc_chunks = []

                i = 0
                for section, text in sec_sections_to_data.items():
                    for chunk in text_splitter.split_text(text):
                        meta = doc.to_meta()
                        meta.sec_section = section
                        doc_chunk = DocChunk(
                            chunk_id=i,
                            meta=meta,
                            data=chunk,
                        )
                        doc_chunks.append(doc_chunk)
                        i += 1

                if not doc_chunks:
                    logger.debug(f"no chunks for {doc}. data: {sec_sections_to_data}")
                    doc.set_last_error_and_status(
                        f"no chunks after splitting.\ndata: {sec_sections_to_data}"
                    )
                    continue
                doc_key_to_chunks[doc.key] = doc, doc_chunks
            except Exception as e:
                doc.set_last_error_and_status(e)
                logger.error(f"error while splitting {doc}. {e}")
                continue

        return doc_key_to_chunks

    async def _fanout_sec_sections(self, doc: SECFiling) -> Dict[str, str]:
        """Given an SEC Type (10-Q or 10-K) send a API request to SECAPI for all sections
        we care about for that type.

        This response is a dict mapping SEC filings => results from the API.
        This function is per filing."""
        sections = SEC_SECTIONS[doc.filing_type]
        tasks = {
            section: SECAPI().extract_sec_sections_with_retries(
                doc.original_url, doc.filing_type, section, "text"
            )
            for section in sections.keys()
        }
        responses = await asyncio.gather(*tasks.values())
        return dict(zip(tasks.keys(), responses))
