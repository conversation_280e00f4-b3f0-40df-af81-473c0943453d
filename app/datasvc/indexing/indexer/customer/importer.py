import asyncio
import mimetypes
from abc import ABC, abstractmethod
from datetime import datetime
from logging import getLogger
from pathlib import Path
from typing import Any, ClassVar, AsyncGenerator, Literal, TypeVar, Protocol

import pandoc
import pymupdf4llm
from anthropic import BaseModel
from asgiref.sync import async_to_sync

from biz.constants import TICKER_PLACE_HOLDER
from biz.enums import DataProviderEnum, TickerScopeEnum
from biz.structs import ClassifiedDocMeta
from common.utils.asyncioutil import gather_with_error, with_concurrency_limit
from common.utils.datetimeutils import UTC
from datasvc import conf
from datasvc.enums import DocStatus
from datasvc.indexing.indexer.customer.classifier import (
    ReFilenameDocMetaClassifier,
    DocMetaClassifier,
)
from datasvc.indexing.indexer.customer.docs import CustomerDocIndexer
from datasvc.models import CustomerDoc
from datasvc.providers.onwish.scope import get_ticker_set_by_scope
from datasvc.store import Store, CustomerRawDataS3Store, get_customer_raw_data_store

logger = getLogger(__name__)

supported_ticker_set = get_ticker_set_by_scope(TickerScopeEnum.FULL)


class KeyExistedError(Exception):
    pass


# -----  args  -----


class ImportArg(BaseModel):
    # to identify current data to be imported
    key: str

    # content type of current data to be imported
    content_type: str = ""


ImportArgT = TypeVar("ImportArgT", contravariant=True, bound=ImportArg)


class CustomerDataImportArg(ImportArg):
    pass


class CustomerDocImportArg(CustomerDataImportArg):
    pass


class CustomerDocFEImportArg(CustomerDocImportArg):
    # filename of the doc to be imported
    filename: str


class CustomerDocFileImportArg(CustomerDocImportArg):
    # path related to source path of the doc to be imported
    relative_path: str


#  -----  config  -----


class ImporterConfig(ABC, BaseModel):
    """base configuration for importers"""

    # data store for persisting imported data
    store: ClassVar[Store]

    # the target path in the store (if need)
    target_path: str = ""

    # whether recursively walk through all children paths.
    recursive: bool = False

    # whether auto-detect the content type of importing doc if not specified.
    detect_content_type: bool = True

    # max count of the limitation for concurrent importing tasks
    concurrency: int = 4

    # whether overwrite if key exists
    overwrite: bool = False


class CustomerDataImporterConfig(ImporterConfig):
    """configuration for importing customer data"""

    user_id: int

    store: ClassVar[CustomerRawDataS3Store] = get_customer_raw_data_store()


class CustomerDocImporterConfig(CustomerDataImporterConfig):
    convert_format: Literal["html", "markdown"] = "html"


class CustomerDocFEImporterConfig(CustomerDocImporterConfig):
    """configuration for importing directly from FE"""

    kwargs_list: list[CustomerDocFEImportArg]


class CustomerDocFileImporterConfig(CustomerDocImporterConfig):
    """configuration for importing file-like datums"""

    source_path: str


#  -----  importer  -----


class Importer(Protocol[ImportArgT]):
    """the importer interface for creating all kinds of importers."""

    # this is an interface. do NOT add variables.

    def connect(self) -> Any:
        """connect the doc container"""
        return async_to_sync(self.connect)()

    @abstractmethod
    async def async_connect(self) -> Any:
        """asynchronously connect the doc container"""

    def disconnect(self) -> Any:
        """disconnect the doc container"""
        return async_to_sync(self.disconnect)()

    @abstractmethod
    async def async_disconnect(self) -> Any:
        """asynchronously disconnect the doc container"""

    def import_all(self) -> dict:
        """import all docs"""
        return async_to_sync(self.async_import_all)()

    @abstractmethod
    async def async_import_all(self) -> dict:
        """asynchronously import all docs"""
        pass

    def import_one(self, arg: ImportArgT) -> Any:
        """copy one doc to store"""
        return async_to_sync(self.async_import_one)(arg)

    @abstractmethod
    async def async_import_one(self, arg: ImportArgT) -> Any:
        """asynchronously copy one doc to store"""


class ImporterBase(Importer[ImportArgT], ABC):
    """the abstracted importer base class which implements some methods"""

    config: ImporterConfig

    class Config:
        arbitrary_types_allowed = True

    def __init__(self, config: ImporterConfig) -> None:
        self.config = config

    async def async_connect(self) -> Any:
        pass

    async def async_disconnect(self) -> Any:
        pass

    async def async_import_all(self) -> dict:
        await self.async_connect()
        import_arg_list = await self.async_walkthrough_all()
        results, _ = await gather_with_error(
            *with_concurrency_limit(
                [self.async_import_one(arg) for arg in import_arg_list],
                limit=self.config.concurrency,
            ),
            log_errors=True,
            log_with_traceback=conf.DEBUG,
            log_title="import docs",
            split_result_errors=False,
        )
        await self.async_disconnect()

        rc = {}
        for arg, r in zip(import_arg_list, results):
            rc[arg.key] = str(r) if isinstance(r, Exception) else r

        return rc

    def walkthrough_all(self) -> list[ImportArgT]:
        """walk through to get all doc import arguments"""
        return async_to_sync(self.async_walkthrough_all)()

    async def async_walkthrough_all(self) -> list[ImportArgT]:
        """asynchronously walk through to get all doc import arguments"""
        return await self._async_walkthrough_all()

    @abstractmethod
    async def _async_walkthrough_all(self) -> list[ImportArgT]:
        """For subclass, will be invoked by async_walkthrough_all"""

    def calc_md5(self) -> str:
        raise NotImplementedError


class CustomerDocImporter(ImporterBase[ImportArgT], ABC):
    """import customer documents"""

    config: CustomerDocImporterConfig

    # @property
    # def organization(self) -> Any:
    #     if len(self.user.organization_set) == 0:
    #         return None
    #
    #     return self.user.organization_set[0]

    def _gen_key(self, ticker: str, fullpath: str, pub_date: datetime) -> str:
        return CustomerDoc.build_doc_key(
            ticker=ticker,
            owner=str(self.config.user_id),
            fullpath=fullpath,
            pub_date=pub_date,
        )

    async def async_create_and_persist(
        self,
        content_type: str,
        raw_fullpath: str,
        key: str = "",
        tickers: list[str] = [],
        title: str = "",
        pub_date: datetime = datetime.now(tz=UTC),
        data: str = "",
    ) -> Any:
        # TODO: consider better way for unsupported ticker
        ticker = tickers[0] if tickers else TICKER_PLACE_HOLDER
        if ticker not in supported_ticker_set:
            ticker = TICKER_PLACE_HOLDER

        key = self._gen_key(ticker, raw_fullpath, pub_date) if not key else key
        filename = Path(raw_fullpath).name
        if not title:
            title = filename

        doc = None
        if self.config.overwrite:
            doc = await CustomerDoc.aget_object_by_key(key)
        if not doc:
            doc = CustomerDoc(
                user_id=self.config.user_id,
                status=DocStatus.CREATED,
                key=key,
                ticker=ticker,
                pub_date=pub_date,
                original_filename=filename,
                title=title,
                content_type=content_type,
                provider=DataProviderEnum.CUSTOMER,
                original_url=raw_fullpath,
                tickers=tickers,
            )
        else:
            doc.status = DocStatus.CREATED
            doc.ticker = ticker
            doc.pub_date = pub_date
            doc.original_filename = filename
            doc.title = title
            doc.content_type = content_type
            doc.original_url = raw_fullpath
            doc.tickers = tickers

        if data:
            doc.content.set_data(data)
        await doc.persist_then_save(overwrite_content=self.config.overwrite)
        logger.info(f"persisted doc - {doc}")
        return doc

    async def async_convert_doc(
        self, fullpath: str | Path, content_type: str = ""
    ) -> str:
        assert fullpath

        fullpath = str(fullpath.resolve()) if isinstance(fullpath, Path) else fullpath
        p = Path(fullpath)

        if content_type == "application/pdf" or p.stem == "pdf":
            text = pymupdf4llm.to_markdown(fullpath)
            if self.config.convert_format != "markdown":
                doc = pandoc.read(source=text)
                if self.config.convert_format == "html":
                    text = pandoc.write(doc=doc, format="html")
        else:
            doc = pandoc.read(file=fullpath)
            text = pandoc.write(doc=doc, format=self.config.convert_format)

        logger.debug(f"converted - {fullpath}")
        return text

    async def async_classify_doc_meta(
        self, filename: str | Path = "", data: bytes | str = ""
    ) -> ClassifiedDocMeta:
        classifier_cls = ReFilenameDocMetaClassifier
        classifier = classifier_cls(filename=filename, data=data)
        try:
            meta = await classifier.classify_meta()
        except Exception as e:
            logger.error(
                f"fallback for failure {classifier_cls} classification - {filename}. {e}"
            )
            classifier = DocMetaClassifier(filename=filename, data=data)
            meta = await classifier.classify_meta()

        return meta


class CustomerDocFEImporter(CustomerDocImporter[CustomerDocFEImportArg]):
    """customer doc - front end importer. Just generate and return a PUT url for front end."""

    config: CustomerDocFEImporterConfig

    async def _async_walkthrough_all(self) -> list[CustomerDocFEImportArg]:
        return self.config.kwargs_list

    async def async_import_one(self, arg: CustomerDocFEImportArg) -> Any:
        """return an upload url"""
        assert isinstance(arg, CustomerDocFEImportArg)
        # filename: str = arg.filename
        # assert filename
        # content_type: str = arg.get("content_type", "")
        # logger.debug(
        #     f"user({self.config.user_id}) is importing {filename} ({content_type})"
        # )
        #
        # dt_path = datetime.now(tz=UTC).strftime("%Y/%m")
        # temp_key = f"{self.config.user_id}/{dt_path}/{normalize_uri(filename)}"
        # return await self.config.store.agen_secret_put_url(
        #     key=temp_key, content_type=content_type
        # )
        raise NotImplementedError


class CustomerDocFileImporter(CustomerDocImporter[CustomerDocFileImportArg]):
    """import customer docs from file-like system"""

    config: CustomerDocFileImporterConfig

    async def async_connect(self) -> Any:
        logger.debug(
            f"user({self.config.user_id}) is importing from {self.config.source_path}."
        )

    async def _async_walkthrough_all(self) -> list[CustomerDocFileImportArg]:
        args = []
        source_p = Path(self.config.source_path)
        prefix = str(
            source_p.resolve() if source_p.is_dir() else source_p.parent.resolve()
        )
        async for p in self.async_walk_fs(self.config.source_path):
            relative_path = str(p.resolve()).removeprefix(prefix).removeprefix("/")
            if relative_path.startswith("."):
                logger.debug(f"ignore {p}")
                continue
            arg = CustomerDocFileImportArg(
                key=relative_path, relative_path=relative_path
            )
            args.append(arg)
        return args

    async def async_import_one(self, arg: CustomerDocFileImportArg) -> Any:
        relative_path: str = arg.relative_path
        assert relative_path
        content_type: str = arg.content_type

        source_p = Path(self.config.source_path)
        fullpath = source_p / relative_path
        if source_p.is_file():
            assert (
                not relative_path
            ), f'filename is "{relative_path}". but it should be empty.'
        logger.debug(f"user({self.config.user_id}) is importing {fullpath}")

        content_type = content_type or await self.detect_content_type(fullpath)
        text = await self.async_convert_doc(
            fullpath=str(fullpath), content_type=content_type
        )
        meta = await self.async_classify_doc_meta(filename=fullpath, data=text)
        logger.debug(f"classified {meta.model_dump_json()} - {fullpath}")

        key = self._gen_key(meta.tickers[0], str(fullpath.resolve()), meta.pub_date)

        doc = await CustomerDoc.aget_object_by_key(key=key)
        if doc and not self.config.overwrite:
            raise KeyExistedError(f"doc was existed. {doc}")

        with fullpath.open(mode="rb") as fp:
            data: bytes = fp.read()

        _, doc = await asyncio.gather(
            # put raw data to raw_store
            self.config.store.aput_data(key, data),
            # save doc to db & persist to doc_store (handled by model)
            self.async_create_and_persist(
                key=key,
                content_type=content_type,
                raw_fullpath=str(fullpath),
                title=meta.title,
                pub_date=meta.pub_date,
                tickers=meta.tickers,
                data=text,
            ),
        )
        logger.debug(f"persisted raw - {key}")  # for store.aput_data only

        if doc.status == DocStatus.PERSISTED:
            indexer = CustomerDocIndexer()
            await indexer.split_index_one(doc.key, overwrite=self.config.overwrite)
        logger.info(f"indexed - {doc}")

        return doc.key

    async def async_walk_fs(self, path: str | Path) -> AsyncGenerator[Path, None]:
        path = Path(path) if not isinstance(path, Path) else path
        if path.is_file():
            self.config.source_path = str(path.parent.resolve())
            yield path
            return
        for p in path.iterdir():
            if p.is_dir():
                if self.config.recursive:
                    async for p1 in self.async_walk_fs(p):
                        yield p1
            else:
                yield p

    async def detect_content_type(self, path: str | Path) -> str:
        if not self.config.detect_content_type:
            return ""

        result = mimetypes.guess_type(path)
        content_type = result[0] or ""
        logger.debug(f"detected content_type {content_type} - {path}")
        return content_type
