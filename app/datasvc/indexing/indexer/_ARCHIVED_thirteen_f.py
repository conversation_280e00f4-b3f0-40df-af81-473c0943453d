""" LEGACY code.
# TODO: remove. (or refactor before re-using)"""
#
# import asyncio
# import json
# from collections import defaultdict
# from datetime import datetime
# from logging import getLogger
# from typing import Union, List, Dict, Any, Tuple
#
# from biz.enums import DocTypeEnum
# from biz.structs import FetchedThirteenF
# from datasvc.indexing.indexer.base import Indexer, DocType
# from datasvc.models import ThirteenF, ReversedThirteenF
# from datasvc.providers.financial_modeling_prep import FMPAPI
#
# logger = getLogger(__name__)
# semaphore = asyncio.Semaphore(1)
#
#
# class ThirteenFIndexer(Indexer[Union[ThirteenF, ReversedThirteenF], FetchedThirteenF]):
#     def get_doc_type(self) -> str:
#         # TODO: Technically also REVERSE_THIRTEEN_F but this is only used for
#         # pinecone and 13F does not touch pinecone.
#         return DocTypeEnum.THIRTEEN_F.value
#
#     def _filter_dates(self, periods: List[str], min_ts: datetime) -> List[str]:
#         return [
#             date_str
#             for date_str in periods
#             if datetime.strptime(date_str, "%Y-%m-%d")
#             >= min_ts.replace(hour=0, minute=0, second=0, microsecond=0)
#         ]
#
#     async def _fetch_cik_to_periods(
#         self, cik_to_name: Dict[Any, Any], min_ts: datetime
#     ) -> Dict[str, List[str]]:
#         tasks = {
#             cik: FMPAPI().fetch_filing_dates_for_cik(cik, semaphore)
#             for cik in cik_to_name.keys()
#         }
#         all_periods = await asyncio.gather(*tasks.values())
#         cik_to_periods = dict(zip(tasks.keys(), all_periods))
#
#         valid_cik_to_periods: Dict[str, List[str]] = {}
#         for cik, periods in cik_to_periods.items():
#             if not periods:
#                 continue
#             try:  # sometimes there will be error responses instead of periods.
#                 filtered_dates = self._filter_dates(periods, min_ts)
#             except Exception as e:
#                 logger.error(f"Error filtering dates for CIK {cik}: {e}")
#                 continue
#             if filtered_dates:
#                 valid_cik_to_periods[cik] = filtered_dates
#
#         logger.info(
#             f"{len(valid_cik_to_periods)} CIKs contain non-null valid periods, fetching 13Fs now."
#         )
#         return valid_cik_to_periods
#
#     async def _get_reverse_13f(
#         self, cik_to_13f: Dict[Tuple[str, str], ThirteenF], cik_to_name: Dict[str, str]
#     ) -> Dict[Tuple[str, str], ReversedThirteenF]:
#         cusip_to_reverse_13f: Dict[Tuple[str, str], ReversedThirteenF] = {}
#         cusip_to_cik_mapping = defaultdict(list)
#         for cik_period_tuple, thirteen_f in cik_to_13f.items():
#             cik, period = cik_period_tuple
#             assert datetime.strptime(period, "%Y-%m-%d") == thirteen_f.quarter_date
#             portfolio = json.loads(thirteen_f.content)
#             for holding in portfolio:
#                 ticker_cusip = holding["tickercusip"]
#                 reverse_key = (ticker_cusip, period)
#                 if ticker_cusip not in cusip_to_reverse_13f:
#                     cusip_to_reverse_13f[reverse_key] = ReversedThirteenF(
#                         cusip=holding["cusip"],
#                         ticker_cusip=holding["tickercusip"],
#                         quarter_date=thirteen_f.quarter_date,
#                         link=thirteen_f.link,
#                         final_link=thirteen_f.final_link,
#                         content="",  # this will inserted later in the index code
#                     )
#                 cusip_to_cik_mapping[reverse_key].append(
#                     {
#                         "cik": cik,
#                         "cik_name": cik_to_name[cik],
#                         "shares": holding["shares"],
#                         "value_usd": holding["value_usd"],
#                         "title_of_class": holding["title_of_class"],
#                         "name_of_issuer": holding["name_of_issuer"],
#                     }
#                 )
#
#         for cusip_period_tup, reverse_13f in cusip_to_reverse_13f.items():
#             reverse_13f.content = json.dumps(cusip_to_cik_mapping[cusip_period_tup])
#
#         return cusip_to_reverse_13f
#
#     async def _download_new_data_no_ticker(
#         self,
#         min_ts: datetime,
#         **kwargs: Any,
#     ) -> List[Union[ThirteenF, ReversedThirteenF]]:
#         logger.info(f"Downloading ThirteenF since time: {min_ts} ...")
#         cik_and_names = await FMPAPI().fetch_cik_list()
#         cik_to_name = {dictobj["cik"]: dictobj["name"] for dictobj in cik_and_names}
#         valid_cik_to_periods = await self._fetch_cik_to_periods(cik_to_name, min_ts)
#
#         fetch_13f_tasks = {}
#         for cik, periods in valid_cik_to_periods.items():
#             for period in periods:
#                 fetch_13f_tasks[(cik, period)] = FMPAPI().fetch_13f_for_quarter(
#                     cik, period, semaphore
#                 )
#         all_13f = await asyncio.gather(*fetch_13f_tasks.values())
#
#         cik_to_13f: Dict[Tuple[str, str], ThirteenF] = {}
#         for cik_period_tuple, filings_per_cik in zip(fetch_13f_tasks.keys(), all_13f):
#             cik, period = cik_period_tuple
#
#             if not filings_per_cik:
#                 continue
#             sample_obj = filings_per_cik[0]
#             portfolio = [
#                 {
#                     "value_usd": filing[
#                         "value"
#                     ],  # make the key name clearer for LLM planning later.
#                     "shares": filing["shares"],
#                     "tickercusip": filing["tickercusip"],
#                     "cusip": filing["cusip"],
#                     "name_of_issuer": filing["nameOfIssuer"],
#                     "title_of_class": filing["titleOfClass"],
#                 }
#                 for filing in filings_per_cik
#             ]
#             cik_dict = {
#                 "cik": cik,
#                 "cik_name": cik_to_name[cik],
#                 "quarter_date": period,
#                 "link": sample_obj["link"],
#                 "final_link": sample_obj["finalLink"],
#                 "source": sample_obj["source"],
#                 "portfolio_json": json.dumps(portfolio),
#             }
#             cik_to_13f[(cik, period)] = await ThirteenF.new_from_doc_data(cik, cik_dict)
#
#         logger.info(
#             f"Downloaded {len(cik_to_13f)} ThirteenF! expected {len(fetch_13f_tasks)}"
#         )
#
#         cusip_to_reverse_13f: Dict[
#             Tuple[str, str], ReversedThirteenF
#         ] = await self._get_reverse_13f(cik_to_13f, cik_to_name)
#         logger.info(f"Created {len(cusip_to_reverse_13f)} ReversedThirteenF!")
#
#         return list(cik_to_13f.values()) + list(cusip_to_reverse_13f.values())
#
#     async def download_persist_db_agg(self, min_ts: datetime, **kwargs: Any) -> None:
#         """Same as download_persist_db for but doctypes that are not ticker based"""
#         downloaded = await self._download_new_data_no_ticker(min_ts=min_ts, **kwargs)
#         print(f"Downloaded {len(downloaded)} new docs. Persisting in S3")
#         self._persist_s3(downloaded, merge_content=True)
#
#     async def _download_new_data(
#         self, min_ts: datetime, ticker: str, **kwargs: Any
#     ) -> List[DocType]:
#         raise ValueError("This download variant not used for ThirteenF")
#
#     async def _fetch_persisted_data(
#         self, min_ts: datetime, ticker: str
#     ) -> List[DocType]:
#         raise ValueError("fetch persisted not needed for ThirteenF")
#
#     async def _split_data(self, data: List[ThirteenF]) -> List[FetchedThirteenF]:
#         raise ValueError("split data not needed for ThirteenF")
