from biz.constants import PRELOADED_MODELS_DIR

# KEY_FINANCIALS = [
#     "reportedCurrency",
#     "revenue",
#     "costOfRevenue",
#     "grossProfit",
#     "grossProfitRatio",
#     "researchAndDevelopmentExpenses",
#     "sellingAndMarketingExpenses",
#     "operatingExpenses",
#     "costAndExpenses",
#     "ebitda",
#     "operatingIncome",
#     "netIncome",
#     "eps",
#     "cashAndCashEquivalents",
#     "inventory",
#     "goodwillAndIntangibleAssets",
#     "totalAssets",
#     "totalDebt",
#     "depreciationAndAmortization",
#     "changeInWorkingCapital",
#     "capitalExpenditure",
#     "freeCashFlow",
#     # also include sources
#     "finalLink",
# ]
GENERAL_FINANCIAL_METRICS = [
    "revenue",
    "costOfRevenue",
    "grossProfit",
    "grossProfitRatio",
    "researchAndDevelopmentExpenses",
    "generalAndAdministrativeExpenses",
    "sellingAndMarketingExpenses",
    "sellingGeneralAndAdministrativeExpenses",
    "otherExpenses",
    "operatingExpenses",
    "costAndExpenses",
    "ebitda",
    "ebitdaratio",
    "interestIncome",
    "interestExpense",
    "operatingIncome",
    "operatingIncomeRatio",
    "totalOtherIncomeExpensesNet",
    "incomeBeforeTax",
    "incomeBeforeTaxRatio",
    "incomeTaxExpense",
    "netIncome",
    "netIncomeRatio",
    "eps",
    "epsdiluted",
    "weightedAverageShsOut",
    "weightedAverageShsOutDil",
    "cashAndCashEquivalents",
    "inventory",
    "goodwillAndIntangibleAssets",
    "totalAssets",
    "totalDebt",
    "depreciationAndAmortization",
    "changeInWorkingCapital",
    "capitalExpenditure",
    "freeCashFlow",
    "shortTermInvestments",
    "cashAndShortTermInvestments",
    "netReceivables",
    "otherCurrentAssets",
    "totalCurrentAssets",
    "propertyPlantEquipmentNet",
    "goodwill",
    "intangibleAssets",
    "longTermInvestments",
    "taxAssets",
    "otherNonCurrentAssets",
    "totalNonCurrentAssets",
    "otherAssets",
    "accountPayables",
    "shortTermDebt",
    "taxPayables",
    "deferredRevenue",
    "otherCurrentLiabilities",
    "totalCurrentLiabilities",
    "longTermDebt",
    "deferredRevenueNonCurrent",
    "deferredTaxLiabilitiesNonCurrent",
    "otherNonCurrentLiabilities",
    "totalNonCurrentLiabilities",
    "otherLiabilities",
    "capitalLeaseObligations",
    "totalLiabilities",
    "preferredStock",
    "commonStock",
    "retainedEarnings",
    "accumulatedOtherComprehensiveIncomeLoss",
    "othertotalStockholdersEquity",
    "totalStockholdersEquity",
    "totalEquity",
    "totalLiabilitiesAndStockholdersEquity",
    "minorityInterest",
    "totalLiabilitiesAndTotalEquity",
    "totalInvestments",
    "netDebt",
    "deferredIncomeTax",
    "stockBasedCompensation",
    "accountsPayables",
    "otherWorkingCapital",
    "otherNonCashItems",
    "netCashProvidedByOperatingActivities",
    "investmentsInPropertyPlantAndEquipment",
    "acquisitionsNet",
    "purchasesOfInvestments",
    "salesMaturitiesOfInvestments",
    "otherInvestingActivites",
    "netCashUsedForInvestingActivites",
    "debtRepayment",
    "commonStockIssued",
    "commonStockRepurchased",
    "dividendsPaid",
    "otherFinancingActivites",
    "netCashUsedProvidedByFinancingActivities",
    "effectOfForexChangesOnCash",
    "netChangeInCash",
    "cashAtEndOfPeriod",
    "cashAtBeginningOfPeriod",
    "operatingCashFlow",
    "revenuePerShare",
    "netIncomePerShare",
    "operatingCashFlowPerShare",
    "freeCashFlowPerShare",
    "cashPerShare",
    "bookValuePerShare",
    "tangibleBookValuePerShare",
    "shareholdersEquityPerShare",
    "interestDebtPerShare",
    "marketCap",
    "enterpriseValue",
    "peRatio",
    "priceToSalesRatio",
    "pocfratio",
    "pfcfRatio",
    "pbRatio",
    "ptbRatio",
    "evToSales",
    "enterpriseValueOverEBITDA",
    "evToOperatingCashFlow",
    "evToFreeCashFlow",
    "earningsYield",
    "freeCashFlowYield",
    "debtToEquity",
    "debtToAssets",
    "netDebtToEBITDA",
    "currentRatio",
    "interestCoverage",
    "incomeQuality",
    "dividendYield",
    "payoutRatio",
    "salesGeneralAndAdministrativeToRevenue",
    "researchAndDdevelopementToRevenue",
    "intangiblesToTotalAssets",
    "capexToOperatingCashFlow",
    "capexToRevenue",
    "capexToDepreciation",
    "stockBasedCompensationToRevenue",
    "grahamNumber",
    "roic",
    "returnOnTangibleAssets",
    "grahamNetNet",
    "workingCapital",
    "tangibleAssetValue",
    "netCurrentAssetValue",
    "investedCapital",
    "averageReceivables",
    "averagePayables",
    "averageInventory",
    "daysSalesOutstanding",
    "daysPayablesOutstanding",
    "daysOfInventoryOnHand",
    "receivablesTurnover",
    "payablesTurnover",
    "inventoryTurnover",
    "roe",
    "capexPerShare",
    # "dividendYielTTM",
    # "dividendYielPercentageTTM",
    # "peRatioTTM",
    # "pegRatioTTM",
    # "payoutRatioTTM",
    # "currentRatioTTM",
    # "quickRatioTTM",
    # "cashRatioTTM",
    # "daysOfSalesOutstandingTTM",
    # "daysOfInventoryOutstandingTTM",
    # "operatingCycleTTM",
    # "daysOfPayablesOutstandingTTM",
    # "cashConversionCycleTTM",
    # "grossProfitMarginTTM",
    # "operatingProfitMarginTTM",
    # "pretaxProfitMarginTTM",
    # "netProfitMarginTTM",
    # "effectiveTaxRateTTM",
    # "returnOnAssetsTTM",
    # "returnOnEquityTTM",
    # "returnOnCapitalEmployedTTM",
    # "netIncomePerEBTTTM",
    # "ebtPerEbitTTM",
    # "ebitPerRevenueTTM",
    # "debtRatioTTM",
    # "debtEquityRatioTTM",
    # "longTermDebtToCapitalizationTTM",
    # "totalDebtToCapitalizationTTM",
    # "interestCoverageTTM",
    # "cashFlowToDebtRatioTTM",
    # "companyEquityMultiplierTTM",
    # "receivablesTurnoverTTM",
    # "payablesTurnoverTTM",
    # "inventoryTurnoverTTM",
    # "fixedAssetTurnoverTTM",
    # "assetTurnoverTTM",
    # "operatingCashFlowPerShareTTM",
    # "freeCashFlowPerShareTTM",
    # "cashPerShareTTM",
    # "operatingCashFlowSalesRatioTTM",
    # "freeCashFlowOperatingCashFlowRatioTTM",
    # "cashFlowCoverageRatiosTTM",
    # "shortTermCoverageRatiosTTM",
    # "capitalExpenditureCoverageRatioTTM",
    # "dividendPaidAndCapexCoverageRatioTTM",
    # "priceBookValueRatioTTM",
    # "priceToBookRatioTTM",
    # "priceToSalesRatioTTM",
    # "priceEarningsRatioTTM",
    # "priceToFreeCashFlowsRatioTTM",
    # "priceToOperatingCashFlowsRatioTTM",
    # "priceCashFlowRatioTTM",
    # "priceEarningsToGrowthRatioTTM",
    # "priceSalesRatioTTM",
    # "enterpriseValueMultipleTTM",
    # "priceFairValueTTM",
    # "dividendPerShareTTM",
]
SEGMENT_FINANCIAL_METRICS = [
    "revenue-product-segmentation",
    "revenue-geographic-segmentation",
]
ALL_FINANCIAL_METRICS = GENERAL_FINANCIAL_METRICS + SEGMENT_FINANCIAL_METRICS
BM25_ENCODER_PATH = PRELOADED_MODELS_DIR / "bm25/bm25_encoder.json"
PINECONE_INDEX_DIM = 1536
