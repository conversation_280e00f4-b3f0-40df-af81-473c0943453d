import logging
import os
from datetime import datetime
from typing import Any, List, Dict

import aiohttp

logger = logging.getLogger(__name__)


class BenzingaAPI:
    BASE_API: str = "https://api.benzinga.com/api/v2/news"

    def __init__(self) -> None:
        self.api_key = os.environ["BENZINGA_KEY"]
        self.querystring = {
            "token": self.api_key,
            "displayOutput": "full",
            "dateTo": datetime.now().strftime("%Y-%m-%d"),
            "sort": "created:desc",
        }
        self.headers = {"accept": "application/json"}

    async def get_press_releases(
        self, symbol: str, min_ts: datetime, page_size: int
    ) -> List[Dict[str, Any]]:
        async with aiohttp.ClientSession() as session:
            new_query = dict(self.querystring)
            new_query["tickers"] = symbol
            new_query["dateFrom"] = min_ts.strftime("%Y-%m-%d")
            new_query["pageSize"] = str(page_size)
            async with session.get(
                self.BASE_API, headers=self.headers, params=new_query
            ) as response:
                resp = await response.json()
                for doc in resp:
                    doc["ticker"] = symbol
                    doc["content"] = doc["body"]
                    pub_date = datetime.strptime(
                        doc["created"], "%a, %d %b %Y %H:%M:%S %z"
                    )
                    doc["pub_date"] = pub_date
                    doc["source"] = doc["author"]
                    doc["url"] = doc["url"]
                return resp
