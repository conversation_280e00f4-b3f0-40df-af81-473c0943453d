import json
import logging
import os
from datetime import UTC, datetime, timedelta
from typing import Any, Dict, List, Tuple

from biz.structs import Period
from common.utils.asyncioutil import gather_with_error, with_concurrency_limit
from common.utils.datetimeutils import parse_datetime
from datasvc.providers.utils import get_json_parsed_data

logger = logging.getLogger(__name__)


class QuartrAPI:
    API_BASE: str = "https://api.quartr.com"

    def __init__(self) -> None:
        self.api_key = os.environ["QUARTR_API_KEY"]
        self.querystring = {"country": "US"}
        self.headers = {"accept": "application/json", "x-api-key": self.api_key}

    async def get_most_recent_earnings_periods(
        self,
        tickers: List[str],
        min_ts: datetime = datetime.now() - timedelta(days=365),
        max_ts: datetime = datetime.now(),
    ) -> Dict[str, List[Period]]:
        async def fetch_ticker(ticker: str) -> Tuple[str, List[Period]]:
            api_url = self.API_BASE + f"/public/v1/companies/ticker/{ticker}"
            try:
                data = await get_json_parsed_data(api_url, headers=self.headers)
            except Exception as e:
                logger.error(f"Failed to fetch {ticker}: {e}")
                return ticker, []
            _periods = sorted(
                [
                    Period(
                        q=int(entry["fiscalPeriod"][-1]),
                        y=int(entry["fiscalYear"]),
                        filing_date=entry["eventDate"],
                    )
                    for entry in data.get("events", [])
                    if (
                        entry.get("eventType", {}).get("secondaryType")
                        == "Earnings Call"
                    )
                    and (
                        datetime.strptime(
                            entry["eventDate"], "%Y-%m-%dT%H:%M:%S.%fZ"
                        ).replace(tzinfo=UTC)
                        >= min_ts
                    )
                    and (
                        datetime.strptime(
                            entry["eventDate"], "%Y-%m-%dT%H:%M:%S.%fZ"
                        ).replace(tzinfo=UTC)
                        <= max_ts.replace(tzinfo=UTC)
                    )
                ],
                key=lambda x: x.y * 10 + x.q,
                reverse=True,
            )
            return ticker, _periods

        results, errs = await gather_with_error(
            *with_concurrency_limit(
                [fetch_ticker(ticker) for ticker in tickers], limit=30
            ),
            log_title="quartr get most recent earnings periods",
        )
        return {ticker: periods for ticker, periods in results if periods}

    async def _download_earning_call(
        self, url: str, filing_date: datetime | str
    ) -> tuple[str, dict]:
        content = ""
        paragraphs = {}
        dt = (
            datetime.strptime(filing_date, "%Y-%m-%dT%H:%M:%S.%fZ")
            if isinstance(filing_date, str)
            else filing_date
        )

        if not url:
            # TODO: put this check & msg to FE ?
            if dt is not None and dt > datetime.utcnow():
                content = f"Content will be available at <b>{dt}</b>. <br/>Please come and visit in the future."
        else:
            _data = await get_json_parsed_data(url, headers=self.headers)
            paragraphs = _data.get("transcript", {})
            if paragraphs:
                paragraphs.pop("text")
                for p in paragraphs.get("paragraphs", []):
                    p.pop("sentences")

        return content, paragraphs

    async def fetch_earning_call(
        self,
        ticker: str,
        year: str,
        quarter: str,
        with_content: bool = True,
        with_paragraphs: bool = False,
    ) -> dict:
        api_url = self.API_BASE + f"/public/v1/companies/ticker/{ticker}"
        data = await get_json_parsed_data(api_url, headers=self.headers)
        for entry in data.get("events", []):
            if (
                entry.get("eventType", {}).get("secondaryType") == "Earnings Call"
                and entry["fiscalYear"] == str(year)
                and entry["fiscalPeriod"][-1] == str(quarter)
            ):
                content, paragraphs = (
                    await self._download_earning_call(
                        entry["transcripts"]["transcriptUrl"], entry["eventDate"]
                    )
                    if with_content or with_paragraphs
                    else ("", {})
                )
                return {
                    "quarter": quarter,
                    "year": year,
                    "ticker": ticker,
                    "filing_date": entry["eventDate"],
                    "content": content,
                    "paragraphs": paragraphs,
                    "content_url": entry["transcripts"]["transcriptUrl"],
                }
        return {}

    async def fetch_company_events(self, ticker: str, min_ts: datetime) -> list[dict]:
        api_url = self.API_BASE + f"/public/v1/companies/ticker/{ticker}"
        data = await get_json_parsed_data(api_url, headers=self.headers)
        max_ts = datetime.now(tz=UTC) + timedelta(minutes=1)
        events = []
        for entry in data.get("events", []):
            if entry.get("eventType", {}).get("secondaryType") != "Earnings Call":
                pub_date = parse_datetime(entry["eventDate"])
                if pub_date < min_ts or pub_date > max_ts:
                    continue
                event_type = entry.get("eventType", {}).get("type")
                events.append(
                    {
                        "event_type": event_type,
                        "title": entry["eventTitle"],
                        "fiscal_year": entry["fiscalYear"],
                        "ticker": ticker,
                        "pub_date": pub_date,
                        "content_url": entry["transcripts"]["transcriptUrl"],
                    }
                )
        return events

    async def download_event_transcript(self, url: str) -> dict:
        return await self._download_transcript(url)

    async def _download_transcript(self, url: str) -> dict:
        """

        :return:
        {
            "number_of_speakers": 15,
            "paragraphs": [
                {
                    'text': "Good day, ...",
                    'speaker': 0
                }
                ...
            ]
        }
        """
        _data = await get_json_parsed_data(url, headers=self.headers)
        paragraphs = _data.get("transcript", {})
        if paragraphs:
            paragraphs.pop("text")
            for p in paragraphs.get("paragraphs", []):
                p.pop("sentences")
                p.pop("start")
                p.pop("end")

        return paragraphs

    # async def tidy_up_transcript(  # noqa: C901
    #     self,
    #     speaker_count: int,
    #     paragraphs: list[dict],
    #     shall_clarify_speakers: bool = False,
    # ) -> dict:  # noqa: C901
    #     """
    #     tidy up the event transcript from Quartr API. Organize speakers and paragraphs.
    #     :param speaker_count:
    #     :param paragraphs:
    #     :param shall_clarify_speakers: use LLM to clarify each speaker's name.
    #     :return:
    #     """
    #     from agent.agents.tidy_up_doc import TidyUpEarningCallAgent
    #     from agent.conf import LLM_MODEL
    #     from agent.config.agent import LLMAgentConfig
    #     from agent.observer import Observer
    #     from agent.utils import truncate_data_by_token_limit
    #
    #     async def __call_agent(
    #         _speaker_count: int,
    #         _paragraphs: list,
    #         _identified_speakers: dict,
    #     ) -> dict:
    #         config = LLMAgentConfig(
    #             answer_max_complete_tokens=1500,
    #             answer_model_name=LLM_MODEL or "gpt-4-1106-preview",
    #         )
    #
    #         agent = TidyUpEarningCallAgent(
    #             config=config,  # type: ignore
    #             observer=Observer(notifiers=[]),
    #             streaming=False,
    #         )
    #         answer = await agent.run(
    #             speaker_count=_speaker_count,
    #             paragraphs=_paragraphs,
    #             identified_speakers=_identified_speakers,
    #         )
    #         resp = json.loads(answer)
    #         if "speakers" in resp:
    #             resp = resp["speakers"]
    #         return resp
    #
    #     # merge paragraphs by speaker
    #     merged: list[dict] = []
    #     last_speaker = -1
    #     for p in paragraphs:
    #         if p["speaker"] == last_speaker:
    #             merged[-1]["text"] += p["text"]
    #         else:
    #             last_speaker = p["speaker"]
    #             merged.append({"speaker": p["speaker"], "text": p["text"]})
    #
    #     paragraphs = merged
    #
    #     # identify speakers
    #     speakers: dict[str, str] = {}
    #     if shall_clarify_speakers:
    #         i = 0
    #         while i < len(paragraphs):
    #             paras = truncate_data_by_token_limit(paragraphs[i:], 10000)
    #             if len(paras) == 0:
    #                 break
    #             i += len(paras)
    #
    #             spker = await __call_agent(speaker_count, paras, speakers)
    #             for num, name in spker.items():
    #                 if num in speakers:
    #                     if name != speakers[num]:
    #                         logger.warning(
    #                             f"speaker adjusted: {speakers[num]} -> {name}"
    #                         )
    #                 speakers[num] = name
    #
    #     sb = []
    #     for p in paragraphs:
    #         num = str(p["speaker"])
    #         name = speakers.get(num) or "unknown"
    #         text = f'{name}: {p["text"]}'
    #         sb.append(text)
    #     data = {
    #         "speakers": speakers,
    #         "content": "\n".join(sb),
    #     }
    #     return data

    async def tidy_up_transcript(self, data: dict) -> dict:  # noqa: C901
        """
        tidy up the event or conference transcript . Organize speakers and paragraphs.

        :param data:        {
            "number_of_speakers": 15,
            "paragraphs": [
                {
                    'text': "Good day, ...",
                    'speaker': 0
                }
                ...
            ]
        }

        :return:
        """
        from agent.agents.tidy_up_doc import TidyUpTranscriptAgent
        from agent.observer import Observer

        agent = TidyUpTranscriptAgent(
            observer=Observer(notifiers=[]),
            streaming=False,
        )
        raw = await agent.run(data=data)
        res = json.loads(raw)

        speaker_names = ["Anonymous" for _ in range(data["number_of_speakers"])]
        # assert data["number_of_speakers"] == len(res["all_speaker_names"])
        for speaker_index, speaker_name in res["all_speaker_names"].items():
            speaker_names[int(speaker_index)] = speaker_name

        data = agent.merge_speaker_texts(data)
        data["paragraphs"] = [
            {
                "speaker": speaker_names[p["speaker"]],
                "content": p["text"],
            }
            for p in data["paragraphs"]
        ]
        data = await agent.fix_broken_sentences(data)

        sb = []
        for p in data["paragraphs"]:
            text = f'{p["speaker"]}: {p["content"]}'
            sb.append(text)
        data = {
            "speakers": speaker_names,
            "content": "\n".join(sb),
        }
        return data

    async def fetch_earnings_call_by_date(
        self,
        ticker: str,
        min_ts: datetime,
        with_content: bool = True,
        with_paragraphs: bool = False,
    ) -> list[dict]:
        items = []
        api_url = self.API_BASE + f"/public/v1/companies/ticker/{ticker}"

        data = await get_json_parsed_data(api_url, headers=self.headers)
        for entry in data.get("events", []):
            dt = datetime.strptime(entry["eventDate"], "%Y-%m-%dT%H:%M:%S.%fZ")
            if (
                entry.get("eventType", {}).get("secondaryType") == "Earnings Call"
                and dt > min_ts
            ):
                content, paragraphs = (
                    await self._download_earning_call(
                        entry["transcripts"]["transcriptUrl"], entry["eventDate"]
                    )
                    if with_content or with_paragraphs
                    else ("", {})
                )
                items.append(
                    {
                        "quarter": entry["fiscalPeriod"][-1],
                        "year": entry["fiscalYear"],
                        "ticker": ticker,
                        "filing_date": entry["eventDate"],
                        "content": content,
                        "paragraphs": paragraphs,
                        "content_url": entry.get("transcript", {}).get("text"),
                    }
                )
        return items

    async def fetch_conferences(
        self, min_ts: datetime, ticker: str
    ) -> List[Dict[str, Any]]:
        """fetch all conference by given ticker and min timestamp.

        NOTE: I separated fetch & download into 2 diff methods.
        """
        # TODO(data): to be investigated. find how to fetch by min_ts or other filter. not full.
        api_url = self.API_BASE + "/public/v1/events/conferences"
        # logger.debug(f"min_ts={min_ts}, ticker={ticker}")
        new_query = dict(self.querystring)
        new_query["ticker"] = ticker
        resp = await get_json_parsed_data(
            api_url, headers=self.headers, params=new_query
        )
        new_docs = []
        for doc in resp:
            # TODO(data): is eventData UTC ? If not, should not compare directly.
            pub_date = datetime.strptime(doc["eventDate"], "%Y-%m-%dT%H:%M:%S.%fZ")
            if pub_date.timestamp() < min_ts.timestamp():
                # filter out conferences that are older than min_ts
                continue

            content_url = doc["transcripts"]["transcriptUrl"]
            doc["pub_date"] = pub_date.strftime("%Y-%m-%d %H:%M:%S")
            doc["url"] = content_url
            doc["title"] = doc["eventTitle"]
            new_docs.append(doc)
        return new_docs

    async def download_conference(self, url: str) -> dict:
        """download conference content by url"""
        return await self._download_transcript(url)
