from collections import OrderedDict

from biz.constants import TICKER_PLACE_HOLDER
from biz.enums import TickerScopeEnum
from common.utils.lang import LazyObject
from .company import __COMPANY_DICTS_BY_TICKERS

# fmt: off
_TOP_TICKER_LIST = ["MSFT", "GOOG", "AMZN", "NVDA", "META", "TSLA", "AAPL"]
_TOP_TICKER_SET = set(_TOP_TICKER_LIST)
# fmt: on

# fmt: off
_KEY_TICKER_LIST = [
    "AAPL", "MSFT", "GOOG", "AMZN", "NVDA", "TSLA", "BRK.B", "META", "LLY", "V",
    "XOM", "UNH", "WMT", "JPM", "MA", "JNJ", "PG", "AVGO", "HD", "CVX",
    "OR<PERSON>", "MRK", "ABBV", "KO", "PEP", "COST", "ADBE", "BAC", "CSCO", "CRM"
]
_KEY_TICKER_SET = set(_KEY_TICKER_LIST)
# fmt: on

# fmt: off
_SNP_500_TICKER_LIST = list(
    OrderedDict.fromkeys([
        "AAPL", "MSFT", "GOOG", "AMZN", "NVDA", "META", "BRK.B", "TSLA", "LLY", "V",
        "AVGO", "JPM", "UNH", "WMT", "XOM", "MA", "JNJ", "PG", "HD", "MRK",
        "COST", "ORCL", "ABBV", "CVX", "BAC", "ADBE", "KO", "CRM", "AMD", "PEP",
        "ACN", "NFLX", "MCD", "TMO", "CSCO", "INTC", "LIN", "ABT", "TMUS", "WFC",
        "CMCSA", "DHR", "VZ", "DIS", "INTU", "PFE", "AMGN", "NKE", "QCOM", "MS",
        "TXN", "PM", "CAT", "BX", "IBM", "UNP", "NOW", "GE", "BA", "AXP",
        "SPGI", "UPS", "COP", "HON", "NEE", "GS", "AMAT", "LOW", "T", "RTX",
        "PLD", "UBER", "BKNG", "SCHW", "BLK", "ISRG", "MDT", "SYK", "LMT", "ELV",
        "DE", "VRTX", "SBUX", "TJX", "BMY", "GILD", "CVS", "C", "AMT", "MDLZ",
        "LRCX", "ADP", "PGR", "REGN", "ETN", "MMC", "ADI", "MU", "CB", "CI",
        "PANW", "ABNB", "ZTS", "BSX", "FI", "SO", "ITW", "SHW", "DUK", "ANET",
        "KLAC", "SNPS", "EQIX", "HCA", "MO", "CME", "ICE", "SLB", "WM", "BDX",
        "NOC", "CDNS", "GD", "MCO", "EOG", "CSX", "USB", "MAR", "PYPL", "CL",
        "TGT", "LULU", "MCK", "PNC", "FDX", "CMG", "APD", "FCX", "MNST", "MMM",
        "CTAS", "MPC", "AON", "PH", "PSX", "APH", "HUM", "ROP", "CHTR", "ECL",
        "TT", "TDG", "ORLY", "NXPI", "EMR", "PXD", "PSA", "NSC", "MSI", "RSG",
        "MET", "OXY", "COF", "DHI", "TFC", "ADSK", "GM", "DXCM", "PCAR", "CCI",
        "EL", "WELL", "AJG", "SRE", "AFL", "AIG", "CARR", "F", "FTNT", "KHC",
        "HLT", "SPG", "NEM", "MCHP", "STZ", "EW", "ROST", "CPRT", "IDXX", "KDP",
        "AEP", "HES", "AZO", "MRNA", "VLO", "TRV", "WMB", "O", "PAYX", "MSCI",
        "ODFL", "NUE", "COR", "LEN", "KMB", "TEL", "OKE", "KVUE", "D", "CNC",
        "IQV", "GWW", "BK", "DLR", "KMI", "AMP", "HSY", "LHX", "ALL", "A",
        "LVS", "JCI", "DOW", "PCG", "SYY", "ADM", "PRU", "FIS", "BIIB", "AME",
        "URI", "CEG", "CTSH", "GIS", "EA", "EXC", "YUM", "OTIS", "FAST", "GEHC",
        "XEL", "ROK", "PPG", "GPN", "CMI", "IT", "CSGP", "EXR", "VRSK", "ON",
        "VICI", "CTVA", "KR", "DD", "NDAQ", "BKR", "ED", "RCL", "HAL", "LYB",
        "PEG", "HPQ", "MLM", "IR", "EFX", "ANSS", "DLTR", "PWR", "VMC", "DG",
        "CDW", "DVN", "ACGL", "FICO", "MPWR", "DFS", "EIX", "FANG", "WBD", "XYL",
        "WEC", "TTWO", "BF.B", "CBRE", "SBAC", "DAL", "KEYS", "GLW", "AVB", "WST",
        "CAH", "AWK", "ZBH", "RMD", "FTV", "MTD", "WTW", "WY", "HIG", "FITB",
        "STT", "TSCO", "APTV", "TROW", "GRMN", "BR", "RJF", "ULTA", "EQR", "CHD",
        "HPE", "DTE", "MTB", "WAB", "ARE", "NVR", "PHM", "HWM", "EBAY", "ETR",
        "WBA", "MOH", "FE", "ES", "STE", "ILMN", "CCL", "ALGN", "INVH", "TDY",
        "EXPE", "LYV", "ROL", "DOV", "BAX", "PPL", "BRO", "FLT", "IFF", "VRSN",
        "PTC", "BLDR", "JBHT", "IRM", "AEE", "VTR", "DRI", "GPC", "K", "CTRA",
        "PFG", "LH", "VLTO", "HBAN", "STLD", "AXON", "TRGP", "WRB", "CNP", "COO",
        "WAT", "EXPD", "RF", "MKC", "BALL", "ATO", "FSLR", "NTAP", "CLX", "HRL",
        "AKAM", "FDS", "OMC", "TYL", "NTRS", "CMS", "LUV", "HUBB", "EPAM", "HOLX",
        "SWKS", "STX", "CINF", "JBL", "ESS", "WDC", "BBY", "J", "CE", "SYF",
        "TER", "EG", "ALB", "IEX", "DGX", "L", "ENPH", "AVY", "TSN", "EQT",
        "MGM", "CFG", "MAA", "LW", "TXT", "LDOS", "CF", "PKG", "SWK", "MAS",
        "SNA", "POOL", "INCY", "GEN", "BEN", "NDSN", "BG", "HST", "FOX", "NWSA",
        "NWS", "FOXA", "AMCR", "PODD", "UAL", "VTRS", "DPZ", "KIM", "MRO", "KEY",
        "CAG", "SJM", "RVTY", "TAP", "ZBRA", "LNT", "CBOE", "CPB", "IP", "TRMB",
        "AES", "LKQ", "IPG", "UDR", "EVRG", "JKHY", "NI", "REG", "NRG", "AOS",
        "PAYC", "TFX", "CRL", "MOS", "PNR", "KMX", "TECH", "GL", "BXP", "WYNN",
        "PEAK", "WRK", "ALLE", "CPT", "UHS", "EMN", "MKTX", "FFIV", "APA", "BBWI",
        "MTCH", "QRVO", "CDAY", "CHRW", "HII", "CZR", "DVA", "HSIC", "PARA", "JNPR",
        "AAL", "RL", "BIO", "ETSY", "RHI", "AIZ", "TPR", "PNW", "CTLT", "FRT",
        "BWA", "IVZ", "FMC", "XRAY", "NCLH", "CMA", "GNRC", "HAS", "VFC", "MHK",
        "WHR", "ZION",
    ]).keys()
)
_SNP_500_TICKER_SET = set(_SNP_500_TICKER_LIST)
# fmt: on

# fmt: off
_RUSSELL_2000_TICKER_LIST = list(
    OrderedDict.fromkeys([
        "SMCI", "MSTR", "ELF", "FIX", "VKTX", "SSD", "LNW", "ONTO", "PCVX", "BRBR",
        "FN", "WFRD", "NXT", "AIT", "APG", "CVNA", "MTDR", "RHP", "HQY", "UFPI",
        "CHRD", "CYTK", "ENSG", "SPSC", "ITCI", "MARA", "CMC", "SSB", "SIGI", "GTLS",
        "SFM", "ATI", "QLYS", "ATKR", "RMBS", "ANF", "CHX", "PR", "NOVT", "MUR",
        "AAON", "BPMC", "TMHC", "MLI", "DUOL", "ESNT", "MTH", "FTAI", "IBP", "CIVI",
        "VRNS", "WTS", "TENB", "NSIT", "ASO", "BECN", "BCC", "OPCH", "ALTM", "TRNO",
        "FLR", "HALO", "SPXC", "PBF", "SM", "SUM", "MMS", "CWST", "STNE", "ALKS",
        "FSS", "BCPC", "ONB", "EXLS", "COOP", "NE", "ASGN", "MTSI", "KRG", "MOD",
        "BMI", "APPF", "CBT", "ZWS", "FELE", "ALTR", "SIG", "KBH", "MMSI", "ETRN",
        "HOMB", "UBSI", "AEL", "LNTH", "GATX", "SKY", "HRI", "FCFS", "SLAB", "ABG",
        "RVMD", "ITRI", "AEO", "GKOS", "MOGA", "HLNE", "FUL", "CVLT", "WK", "POR",
        "AMR", "SWX", "CADE", "PECO", "TNET", "GBCI", "PTEN", "KNF", "FFIN", "BBIO",
        "NJR", "LANC", "MGY", "IOVA", "EXPO", "COKE", "JXN", "POWI", "HGV", "ACA",
        "ARWR", "SYNA", "BIPC", "SHAK", "INSM", "KAI", "RDN", "VAL", "ALIT", "HP",
        "MDC", "TEX", "MDGL", "MATX", "HAE", "EPRT", "ENS", "EVH", "ESGR", "NEOG",
        "GMS", "CRC", "HWC", "AEIS", "BCO", "OTTR", "FOLD", "CBAY", "DY", "IRTC",
        "UMBF", "ACLS", "WDFC", "ALRM", "ITGR", "AVNT", "GPI", "BDC", "BOX", "NSP",
        "PCH", "VLY", "CBZ", "MAC", "BKH", "MQ", "SANM", "WIRE", "KRYS", "CCOI",
        "APLE", "PBH", "CSWI", "LBRT", "RPD", "ORA", "AMKR", "BXMT", "AXNX", "ACIW",
        "PIPR", "PSN", "REZI", "NPO", "ARCB", "IRT", "GT", "TPH", "MHO", "ALE",
        "OGS", "AVAV", "FRSH", "HI", "TDW", "SBRA", "CNX", "KFY", "SWTX", "KTB",
        "SHOO", "BL", "BHVN", "MC", "ASB", "JBT", "SMPL", "PNM", "ADNT", "FORM",
        "STNG", "ZD", "CVCO", "STRL", "CRS", "BEAM", "SFBS", "NARI", "VC", "WD",
        "SR", "FLYW", "IOSP", "NOG", "LCII", "CNO", "DIOD", "MGRC", "VRRM", "NWE",
        "HELE", "LIVN", "RIOT", "WHD", "ABCB", "BRZE", "AXSM", "HCC", "BLKB", "DBRG",
        "GFF", "RUSHA", "SPT", "NTLA", "ARCH", "SLG", "AIN", "SKT", "DOOR", "CERE",
        "ACAD", "TCBI", "ICFI", "AX", "VSH", "BTU", "MYRG", "FL", "IDCC", "TGNA",
        "ABM", "SEM", "BNL", "CRNX", "RELY", "IGT", "HUBG", "SXT", "IBOC", "DOC",
        "UCBI", "IIPR", "PAGS", "KOS", "RRR", "PGNY", "KWR", "CLSK", "CDP", "HASI",
        "MTRN", "IDYA", "BOOT", "EVTC", "IPAR", "KLIC", "SITC", "KTOS", "SHLS", "CWT",
        "APAM", "QTWO", "URBN", "FBP", "AVA", "TMDX", "AWR", "OI", "PATK", "ESE",
        "GHC", "ACLX", "UNF", "LXP", "PJT", "PRMW", "PLXS", "CRDO", "WERN", "OSCR",
        "NMIH", "BGC", "CEIX", "USD", "YELP", "AROC", "WSFS", "FTDR", "NHI", "SKYW",
        "GNW", "LRN", "PRVA", "CATY", "TGTX", "CNMD", "DORM", "BLMN", "CLDX", "AI",
        "DNLI", "CCS", "CSTM", "UEC", "MWA", "SONO", "PI", "RXO", "DOCN", "PGTI",
        "MTX", "ROCK", "SGRY", "CALM", "RAMP", "CVBF", "PRGS", "SHO", "PDCO", "TBBK",
        "PZZA", "MGEE", "ENV", "TWST", "LGIH", "CTRE", "RYTM", "GVA", "RDNT", "NUVL",
        "PLAY", "VCEL", "AIR", "ACVA", "SFNC", "ARVN", "PFSI", "FIBK", "PLUS", "AMBA",
        "MLKN", "XRX", "PRFT", "FULT", "SLVM", "STRA", "JJSF", "FCPT", "PD", "ARDX",
        "AUB", "JBLU", "MBC", "SDRL", "FOXF", "CRVL", "CARG", "OUT", "ARRY", "AMN",
        "CPK", "GLNG", "BOH", "GOLF", "GH", "INSW", "PRIM", "CBU", "AMPH", "OSIS",
        "VIAV", "HL", "TNC", "PSMT", "IRWD", "OII", "KMT", "XTSLA", "WGO", "PARR",
        "EAT", "EBC", "CALX", "COUR", "ENR", "MODG", "ABR", "ALG", "IMVT", "BANC",
        "PTCT", "INDB", "PPBI", "HNI", "FRME", "TGH", "TRN", "VRNT", "PRCT", "UE",
        "DRH", "ROG", "NEO", "SXI", "STEP", "BANF", "PRKS", "SCL", "CNS", "TOWN",
        "CENTA", "ATGE", "SBCF", "EPC", "FFBC", "AVDX", "MIR", "CPE", "ROAD", "CORT",
        "CNK", "WOR", "PLMR", "UCTT", "PEB", "UPST", "EQC", "PRK", "ASTH", "SNEX",
        "THRM", "AGIO", "ENVA", "OMI", "RLJ", "THS", "UPBD", "ODP", "HBI", "EPAC",
        "HLMN", "RCKT", "ANDE", "IONQ", "JOE", "STR", "AZZ", "BKU", "SIX", "EYE",
        "HURN", "LAUR", "HEES", "VSTO", "RXRX", "CSGS", "TALO", "VECO", "SJW", "MYGN",
        "IVT", "TROX", "BE", "PTGX", "VCYT", "PLAB", "SDGR", "B", "FSLY", "BATRK",
        "CAKE", "AMWD", "NGVT", "DVAX", "CMPR", "INMD", "WSBC", "CXW", "BRP", "OPEN",
        "SQSP", "DK", "KAR", "LZB", "AGM", "TFIN", "VRTS", "KYMR", "GSHD", "LUMN",
        "JBGS", "UPWK", "SFL", "RNST", "ROIC", "VGR", "PRO", "HIMS", "XPRO", "SITM",
        "DFIN", "SATS", "XHR", "POWL", "DAN", "SNDX", "EXTR", "RXST", "GOGL", "AKR",
        "GPOR", "WKC", "ATRC", "ARI", "OFG", "VERA", "AGYS", "CUBI", "CNNE", "DNOW",
        "GEF", "YOU", "IBTX", "ASAN", "STAA", "JOBY", "TRMK", "GBX", "KROS", "OXM",
        "DRS", "SUPN", "STC", "LKFN", "WAFD", "JELD", "TTMI", "MSGE", "NTB", "KURA",
        "NTCT", "EFSC", "CODI", "CBRL", "HLIO", "NMRK", "TDS", "ACMR", "GEO", "NBTB",
        "ZETA", "JACK", "GRBK", "NSSC", "BYON", "USPH", "UFPT", "KN", "DDS", "DHT",
        "LGND", "COHU", "CPRX", "HTH", "MXL", "BANR", "GNL", "ADUS", "HLIT", "MGNI",
        "APPN", "NAVI", "CHCO", "RC", "NVEE", "AKRO", "PACB", "LZ", "HTLF", "MORF",
        "LMAT", "PCRX", "UNIT", "LOB", "LESL", "NWN", "CWK", "NWBI", "CTS", "CXM",
        "PHR", "APOG", "SBH", "LNN", "KFRC", "WNC", "XNCR", "GIII", "MCY", "CHEF",
        "SMTC", "PRG", "ENVX", "HWKN", "TNK", "BORR", "HMN", "HLX", "UTZ", "LTC",
        "ATEC", "FBNC", "SYBT", "ESRT", "FBK", "ALEX", "KAMN", "RKLB", "JAMF", "CASH",
        "MGPI", "CAL", "DEI", "INBX", "SVC", "GTY", "FCF", "COLL", "SCS", "LPG",
        "SOVO", "BKE", "BUSE", "TWO", "NHC", "PMT", "OEC", "PRTA", "ADEA", "WLY",
        "LADR", "ICHR", "FIZZ", "SG", "CARS", "SAGE", "JBI", "OMCL", "UVV", "PWSC",
        "ECPG", "SSTK", "AVPT", "ALGT", "XPEL", "TRUP", "NNI", "SAFT", "DEA", "ELME",
        "STEL", "VTLE", "PAR", "EVBG", "CMCO", "HOPE", "BMBL", "VRE", "NWLI", "ZUO",
        "NX", "KOP", "PRDO", "ADMA", "ALPN", "IMKTA", "CWH", "DO", "RNA", "NTST",
        "PFS", "FDMT", "OSW", "DYN", "KALU", "SPHR", "VCTR", "NBHC", "WT", "WABC",
        "STBA", "DNUT", "SCSC", "SCHL", "INTA", "MRTN", "COMP", "MFA", "WRBY", "EIG",
        "AMSF", "TARS", "TRS", "HRMY", "BCRX", "BXC", "TCBK", "BHE", "BKD", "AAT",
        "ALKT", "DAWN", "CTKB", "PAYO", "CVI", "MNRO", "NIC", "MGNX", "AIV", "WINA",
        "PEBO", "KW", "WMK", "FBRT", "XMTR", "DGII", "RCUS", "UUUU", "INFN", "SANA",
        "GSAT", "FWRD", "MNKD", "SGH", "SASR", "SAFE", "QCRH", "SP", "AMLX", "GERN",
        "PRAA", "BFH", "PDFS", "CHGG", "UDMY", "HIBB", "AHCO", "VIR", "HCSG", "LGFB",
        "CIM", "MRC", "BASE", "PRLB", "SKWD", "PLRX", "MDXG", "ARR", "VERV", "EVRI",
        "GABC", "VSEC", "SPNS", "ARCT", "MODN", "ATEN", "MCRI", "SRRK", "MEG", "WS",
        "PLYM", "UNFI", "RLAY", "VBTX", "LFST", "SAVA", "EXPI", "ACT", "RYI", "PRM",
        "EDIT", "LC", "FLNC", "INDI", "TGI", "ZNTL", "INVA", "FA", "JBSS", "NABL",
        "OBK", "MBUU", "BRSP", "MATV", "DCPH", "THR", "ARLO", "RVLV", "SOUN", "MMI",
        "IESC", "AUR", "ASTE", "MSEX", "BZH", "GNK", "TILE", "HPP", "SABR", "ECVT",
        "ASPN", "SHEN", "MTUS", "HAIN", "SXC", "OCFC", "VRDN", "CMTG", "SPNT", "SRCE",
        "CRGY", "AVNS", "MATW", "CSR", "HLF", "ANIP", "UMH", "PUBM", "VZIO", "SLCA",
        "ERII", "NAT", "ARQT", "LILAK", "SBSI", "TRNS", "PAX", "BGS", "EFC", "DLX",
        "BFC", "BLBD", "DFH", "QTRX", "ZIP", "VICR", "BHLB", "AMBC", "RGNX", "FDP",
        "CDE", "FBMS", "CRAI", "EMBC", "LMND", "GRC", "AMPL", "DHC", "JANX", "BRKL",
        "PGRE", "AMK", "DOLE", "OCUL", "TTGT", "BJRI", "GDYN", "CABA", "CNOB", "AUPH",
        "BBSI", "SPTN", "LBAI", "IMAX", "BLX", "PDM", "QNST", "CNXN", "FIGS", "KELYA",
        "WWW", "RDFN", "UTL", "BSIG", "CRK", "MD", "DRQ", "ETNB", "CFFN", "AXL",
        "CMP", "SAH", "EYPT", "GDEN", "ASIX", "AORT", "ETD", "RGR", "TWI", "VVI",
        "RPAY", "FLNG", "LPRO", "APGE", "DIN", "COCO", "CHCT", "ETWO", "MEI", "FMBH",
        "ACHR", "PFC", "FG", "HAYN", "NBR", "CYRX", "CLB", "GPRE", "ACCD", "AHH",
        "MBIN", "HA", "GES", "ALT", "EWTX", "SAVE", "BDN", "MIRM", "INN", "HOV",
        "MYE", "PUMP", "NOVA", "DX", "HZO", "ATSG", "CTBI", "WTTR", "SIBN", "HSII",
        "RWT", "PTLO", "SILK", "CDRE", "CASS", "LQDA", "NUS", "ASC", "REX", "AMRX",
        "IIIN", "AMRC", "ZEUS", "TMP", "CRBU", "NVRI", "SMP", "CECO", "HY", "CCRN",
        "DCO", "REVG", "THRY", "SLP", "DCOM", "ZYME", "LAZR", "VREX", "NYMT", "NXRT",
        "HCI", "EGBN", "HOUS", "PCT", "RES", "HSTM", "CCO", "YEXT", "HDSN", "KNSA",
        "NVTS", "MBWM", "UVSP", "OSBC", "VTOL", "IMXI", "CBL", "CLW", "LTH", "VMEO",
        "HCKT", "OLO", "FATE", "YMAB", "EGLE", "KNTK", "IBRX", "FOR", "TMCI", "COGT",
        "SWBI", "SWI", "CSTL", "CFB", "CRNC", "CHUY", "SNCY", "WSR", "PTVE", "FCBC",
        "SCVL", "BLFS", "SBOW", "EOLS", "HTLD", "BBUC", "CNDT", "BYND", "PWP", "EU",
        "LGFA", "AMTB", "PFBC", "FUBO", "MSBI", "IRON", "HBNC", "NRDS", "PRA", "BY",
        "SPCE", "NRIX", "AGX", "DMRC", "LASR", "OABI", "NG", "IDT", "TVTX", "HFWA",
        "STKL", "NNOX", "GLDD", "PLOW", "NRC", "VTS", "GIC", "OSUR", "FCEL", "CENX",
        "USNA", "GMRE", "UHT", "MTTR", "GOGO", "MLAB", "MRSN", "BFST", "KRUS", "FNA",
        "SVV", "KREF", "SMRT", "CMRE", "FMNB", "ARKO", "DDD", "ACEL", "RDUS", "NVRO",
        "BRY", "FWRG", "HTBK", "BELFB", "EVLV", "EQBK", "WRLD", "CLBK", "SMMT", "UFCS",
        "VITL", "IIIV", "KE", "GSBC", "BIGC", "TITN", "WASH", "AMRK", "SBGI", "TNGX",
        "CEVA", "CATC", "ATRO", "TTI", "CDNA", "UVE", "MLNK", "ZIMV", "ACCO", "PETQ",
        "CLDT", "LEU", "ATXS", "EBF", "AOSL", "MRNS", "RVNC", "GOOD", "THFF", "ATRI",
        "CAC", "GTN", "CVGW", "AVO", "XPER", "USLM", "URGN", "LYEL", "CLNE", "CRSR",
        "PKST", "TRTX", "ANAB", "HVT", "RICK", "MITK", "CDMO", "IAS", "LWLG", "ALHC",
        "ALLO", "MCBS", "AMNB", "MLR", "NKLA", "FLGT", "PGC", "PNTG", "SRI", "MAX",
        "FPI", "TBPH", "ADPT", "DXPE", "MTW", "PSFE", "ALX", "AMAL", "DHIL", "IE",
        "IBCP", "EWCZ", "UTI", "NR", "CGEM", "ATEX", "TK", "HCAT", "RUSHB", "EGY",
        "OFIX", "CCNE", "SVRA", "DENN", "ADTN", "ORIC", "BATRA", "EHAB", "TRST", "HONE",
        "RBCAA", "CCB", "PBI", "LMB", "EB", "YORW", "CSTR", "HLVX", "SRDX", "AEHR",
        "BFS", "ALEC", "MOV", "NTGR", "MCB", "MED", "RGP", "DBI", "BAND", "DH",
        "BHB", "FC", "HTBI", "MPLN", "CWCO", "VVX", "FFWM", "ALRS", "SGHC", "KIDS",
        "REPL", "TIPT", "INST", "ORC", "RCEL", "FARO", "RILY", "CENT", "AXGN", "STGW",
        "WEAV", "NVAX", "DJCO", "KALV", "CVLG", "LAND", "TPB", "FIP", "AMPS", "SMBC",
        "VPG", "TELL", "NAPA", "CPF", "KRNY", "MVIS", "ACRE", "HAFC", "AMCX", "JRVR",
        "XERS", "LXU", "OPK", "NPK", "MXCT", "NFBK", "TPC", "SEMR", "AVNW", "ALXO",
        "IVR", "CLFD", "COMM", "CRMT", "KGS", "ATMU", "LYTS", "STEM", "LQDT", "CCBG",
        "ELVN", "CERS", "STER", "MOFG", "NVEC", "RMR", "TTEC", "METCV", "FRPH", "OSPN",
        "FFIC", "CTO", "AROW", "AVXL", "TREE", "MCS", "CTLP", "PL", "AMSWA", "SHBI",
        "BOC", "LBPH", "PRME", "NBN", "MNTK", "BHRB", "SMBK", "RSI", "AESI", "TCMD",
        "MODV", "ANIK", "VTYX", "BMEA", "TBI", "APLD", "GNE", "BV", "NEXT", "UIS",
        "DOMO", "SNBR", "ATNI", "ESQ", "MCFT", "PX", "SHYF", "ALNT", "PBPB", "GCO",
        "SPFI", "OLMA", "GDOT", "TARO", "EVER", "CYH", "GCMG", "TAST", "ABUS", "LUNG",
        "PHAT", "DSKE", "NKTX", "AGS", "OSG", "CTOS", "OIS", "GRND", "OLP", "ODC",
        "CARE", "SMMF", "MBI", "CNSL", "AVIR", "CSV", "BOOM", "HROW", "ADV", "DAKT",
        "ENFN", "EGHT", "BWMN", "MPB", "RBB", "LOVE", "LIND", "PLPC", "ACNB", "TERN",
        "WULF", "GLRE", "SB", "CCSI", "ORRF", "FSBC", "STRO", "LMNR", "INZY", "BBW",
        "EAF", "APPS", "SPOK", "WTBA", "IRBT", "KIND", "TRC", "SD", "GEFB", "GCI",
        "ENTA", "TH", "DCGO", "BALY", "FRST", "PFIS", "FRBA", "TWKS", "SHCR", "DSGR",
        "FBIZ", "ARIS", "BMRC", "TRUE", "AGTI", "CZNC", "ARTNA", "PAHC", "GPRO", "WVE",
        "HBT", "AVD", "HIFS", "CDLX", "ONEW", "NEWT", "FISI", "TSVT", "LRMR", "IRMD",
        "WTI", "AGEN", "PANL", "SFIX", "BSRR", "FLIC", "SPWR", "ITOS", "EVGO", "NUVB",
        "SFST", "HBCP", "MCBC", "SLRN", "MLYS", "VYGR", "ULCC", "ZUMZ", "HRTX", "SKIN",
        "BWB", "EBTC", "NRIM", "TNYA", "GNTY", "HUMA", "PKE", "ACIC", "FMAO", "TRDA",
        "TYRA", "AAN", "OMER", "WSBF", "VNDA", "ALTG", "NWPX", "MVBF", "VMD", "FLWS",
        "CIVB", "SENEA", "ASTS", "WEST", "RBBN", "HMST", "RIGL", "CELC", "AVTE", "REFI",
        "SLDP", "SLQT", "JAKK", "PSTX", "RAPT", "AMWL", "CDXS", "OPRX", "FNLC", "BCML",
        "TCBX", "LXFR", "GRTS", "MSFUT", "ORGO", "BTBT", "NMRA", "IPI", "ANNX", "HOFT",
        "FIHL", "HYLN", "PSTL", "LOCO", "FNKO", "RRBI", "PLL", "GPMT", "XPOF", "FORR",
        "WLDN", "SMHI", "EE", "LXRX", "ASLE", "SOI", "AURA", "SIGA", "ARAY", "ZVRA",
        "SSP", "GEVO", "BLFY", "IHRT", "CLMB", "ZYXI", "CHRS", "IAUX", "CPS", "PLCE",
        "LSEA", "CRCT", "OBT", "JOUT", "FDBC", "SPRY", "EVCM", "CBAN", "WALD", "HLLY",
        "OFLX", "OOMA", "ULH", "NATR", "DGICA", "ONTF", "RYAM", "BOWL", "ATNM", "SMLR",
        "SES", "VLGEA", "BFLY", "ONL", "STOK", "PGEN", "ANGO", "CVRX", "LEGH", "NN",
        "ACDC", "SKYT", "LILA", "EVC", "LINC", "UNTY", "PKOH", "DXLG", "CBNK", "JMSB",
        "LUNA", "FRGE", "FSBW", "TSBK", "CVLY", "AMPY", "WOW", "RCKY", "IMMR", "KRO",
        "CVGI", "ASUR", "TTSH", "WW", "NECB", "PKBK", "POWW", "PACK", "RLGT", "FVCB",
        "INSE", "UTMD", "PINE", "BRT", "HRT", "REPX", "GCBC", "THRD", "NWFL", "LPSN",
        "INOD", "KODK", "FF", "MGTX", "NXDT", "ITI", "PCB", "BBCP", "OVID", "ALCO",
        "VRA", "HPK", "ERAS", "RM", "MYPS", "NKSH", "CRMD", "BCBP", "CRDA", "PFMT",
        "ITIC", "KOD", "TCX", "BLDE", "CVCY", "GLUE", "GRNT", "QUAD", "ME", "AFCG",
        "PLBC", "BWFG", "EHTH", "GBTG", "FGEN", "LBC", "COFS", "CZFS", "RDVT", "FET",
        "MBCN", "EPM", "CMTL", "SSBK", "ATLO", "PCYO", "WEYS", "BLUE", "EBS", "IGMS",
        "OM", "BKSY", "SMR", "CIO", "OCN", "ALDX", "CFFI", "EVBN", "PDLB", "QSI",
        "HEAR", "MVST", "CLPT", "PDSB", "SCPH", "HBIO", "HSHP", "SSTI", "RMAX", "XOMA",
        "CHMG", "REI", "BLNK", "RMNI", "PLSE", "MASS", "NRGV", "ATLC", "BIG", "KRT",
        "OVLY", "NATH", "MG", "DM", "HNRG", "STHO", "WISH", "PEPG", "FLL", "BARK",
        "NRDY", "FHTX", "TSE", "SGMO", "IBEX", "TLYS", "CMPX", "OB", "INGN", "SEAT",
        "SAMG", "LAW", "LVWR", "MACK", "DOUG", "BRCC", "INFU", "BHR", "FENC", "NGVC",
        "CLAR", "III", "QIPT", "KPTI", "BPRN", "TDUP", "SNPO", "FSR", "GENC", "RNGR",
        "RRGB", "HIPO", "LCNB", "GWRS", "SMTI", "VEL", "ISPR", "SCLX", "LE", "CMT",
        "HFFG", "VABK", "ESSA", "JYNT", "GNLX", "RSVR", "ATOM", "TGAN", "NAUT", "CMCL",
        "PWOD", "CIFR", "PRTS", "RGCO", "IMRX", "ACTG", "SPWH", "WLFC", "OPTN", "RXT",
        "OPI", "MAXN", "MEC", "ESCA", "NC", "CPSI", "ALTI", "STRS", "EGAN", "MYFW",
        "AOMR", "INTT", "TPIC", "DSP", "LCTX", "DHX", "PTSI", "BH", "MLP", "TG",
        "AKYA", "NREF", "BGFV", "VRCA", "PETS", "NODK", "USCB", "AVAH", "GRWG", "GBIO",
        "TRVI", "TSQ", "CATO", "EXFY", "FCCO", "BCAB", "LLAP", "XFOR", "CDZI", "PBFS",
        "GAMB", "SWIM", "MCRB", "DNMR", "KLTR", "EOSE", "SEER", "MNSB", "DC", "CTGO",
        "TELA", "MHLD", "NDLS", "SBT", "VOXX", "SNFCA", "ALLK", "WLLAW", "RELL", "KLXE",
        "VUZI", "MOND", "PPTA", "PRPL", "JILL", "EVEX", "BBAI", "PLX", "GLT", "OMGA",
        "BSVN", "CTXR", "EVI", "KFS", "DSGN", "PAYS", "ACRS", "NOTE", "BCOV", "INNV",
        "EEX", "GRPH", "BIRD", "MURA", "IVAC", "SGHT", "VTNR", "KVHI", "FEAM", "TUSK",
        "MPX", "OBIO", "PMTS", "CCRD", "ASRT", "STKS", "IPSC", "BW", "COOK", "AEVA",
        "RNAC", "BKKT", "BTMD", "ATRA", "GWH", "CUE", "CMPO", "CNTY", "CPSS", "KZR",
        "EP", "VOR", "AIRS", "SCWO", "PROK", "ACET", "PMVP", "NGM", "CLPR", "SKYX",
        "OTLK", "CARM", "BTAI", "NVCT", "CURV", "ORGN", "EYEN", "RLYB", "EGRX", "LNZA",
        "DLTH", "VERI", "HQI", "WKHS", "KRMD", "LICY", "GOCO", "SST", "XAIR", "MKTW",
        "ZURA", "PRLD", "METCB", "VIGL", "DTC", "BRBS", "FOSL", "PNRG", "CMBM", "SKIL",
        "TCI", "CARA", "AKTS", "NL", "PIII", "BHIL", "IKNA", "VHI", "FOA", "GORV",
        "SGMT", "ALVR", "VALU", "ACRV", "TWOU", "OPFI", "ZVIA", "CUTR", "VATE", "VAXX",
        "AADI", "DZSI", "RDW", "WLLBW", "RPHM", "CIX", "PRTH", "ADRO", "SWKH", "UONEK",
        "UHG", "FTCI", "UONE", "ARL", "RBOT", "VLD", "ADRO", "AMPX", "TSBX", "AFRI",
        "RENT", "CMAX", "ELA", "LPTV", "VGAS", "DFLI", "EVA", "BGXX", "PRST", "MDRX",
        "TIO", "GTXI", "CAD", "PDLI",
    ]).keys()
)
_RUSSELL_2000_TICKER_SET = set(_RUSSELL_2000_TICKER_LIST)
# fmt: on

# fmt: off
_EXTRA_TICKER_LIST = list(
    OrderedDict.fromkeys([
        "AMD", "DELL", "ASML", "ARM", "RDDT", "MRVL", "IFF", "DHR", "AVGO", "NIO",
        "RIVN", "XPEV", "LI", "DDOG", "MDB", "ESTC", "PLTR", "WDAY", "TEAM", "SHOP",
        "SNOW", "DASH", "RBLX", "BABA", "PDD", "TSM", "MSTR", "COIN", "BIDU", "MU",
        "SE", "BA", "PFE", "GRAB", "HOOD", "AI", "JD", "NTES", "TME", "TCOM",
        "BILI", "DIDIY", "IQ", "LU", "YUMC", "DOYU", "HUYA", "ZTO", "NIU", "VIPS",
        "WB", "WIMI", "ZH"
    ]).keys()
)
_EXTRA_TICKER_SET = set(_EXTRA_TICKER_LIST)
# fmt: on

_FULL_TICKER_LIST = list(LazyObject(lambda: __COMPANY_DICTS_BY_TICKERS.keys()))
_FULL_TICKER_SET = set(LazyObject(lambda: _FULL_TICKER_LIST))


_TICKER_SCOPE: dict[str, tuple[list, set]] = {
    # ----- debug, test purpose
    TickerScopeEnum.TOP: (_TOP_TICKER_LIST, _TOP_TICKER_SET),
    TickerScopeEnum.KEY: (_KEY_TICKER_LIST, _KEY_TICKER_SET),
    # ----- prod list
    TickerScopeEnum.SNP: (_SNP_500_TICKER_LIST, _SNP_500_TICKER_SET),
    TickerScopeEnum.RUSSELL: (_RUSSELL_2000_TICKER_LIST, _RUSSELL_2000_TICKER_SET),
    TickerScopeEnum.EXTRA: (_EXTRA_TICKER_LIST, _EXTRA_TICKER_SET),
    # ----- combo
    TickerScopeEnum.FULL: (_FULL_TICKER_LIST, _FULL_TICKER_SET),
}


def get_ticker_list_by_scope(
    scope: str | TickerScopeEnum, without_placeholder: bool = False
) -> list:
    assert scope in TickerScopeEnum.values()
    if without_placeholder:
        r = _TICKER_SCOPE[scope][0]
    else:
        r = _TICKER_SCOPE[scope][0] + [TICKER_PLACE_HOLDER]
    return r


def get_ticker_set_by_scope(
    scope: str | TickerScopeEnum, without_placeholder: bool = False
) -> set:
    assert scope in TickerScopeEnum.values()
    if without_placeholder:
        r = _TICKER_SCOPE[scope][1]
    else:
        r = _TICKER_SCOPE[scope][1] | {TICKER_PLACE_HOLDER}
    return r
