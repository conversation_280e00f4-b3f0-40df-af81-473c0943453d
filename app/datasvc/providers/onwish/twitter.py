"""twitter related social data."""
import asyncio
import json
import traceback
from datetime import datetime, UTC
from logging import getLogger

from apify_client import ApifyClientAsync
from pydantic import BaseModel

from common.utils.lang import LazyObject
from common.utils.structutil import StructDictMixin
from .company import get_company_name_by_ticker
from .person import list_all_executives, CompanyExecutive
from ..utils import PREPARED_DATA_PATH
from ... import conf
from ...store import get_prepared_data_store

BATCH_SIZE = 20
ITEMS_PER_CHANNEL = 10
MAX_ITEM_PER_LIST_LIMITATION = 5000

TWITTER_STORE_LOCK_KEY = "twitter_channels.lock"
TWITTER_FILE_NAME = "twitter_channels.json"
TWITTER_INFO_FILE_NAME = "twitter_info.json"

logger = getLogger(__name__)
prepared_store = get_prepared_data_store()


class SocialChannel(StructDictMixin, BaseModel):
    search_term: str = ""
    ticker: str = ""
    url: str = ""
    person_name: str = ""


TwitterChannel = SocialChannel

# ----- public functions -----


# ----- internal functions ------


def __clean_url(url: str) -> str:
    # TODO(data): channels need to be filtered and cleaned. (maybe the same to Youtube) e.g.
    #       -  this should be filtered out: "https://twitter.com/Microsoft/status/1021432250137751552",
    #           simply we can check if there more than 1 "/" after ".com"
    #       -  this should be cleaned:   "https://twitter.com/satyanadella#:~:text=Satya%20Nadella%20(%40satyanadella)%20%2F%20X",
    return url


async def _fetch_and_upload_twitter_channels() -> dict:
    """fetch latest twitter channels for company executives."""

    logger.info("fetching & upload twitter channels ...")

    if not prepared_store.lock(TWITTER_STORE_LOCK_KEY):
        logger.warning("fail to lock twitter data in store")
        return {}

    try:
        dt = datetime.now(tz=UTC)
        info = {"updated_at": dt.isoformat(), "timestamp": dt.timestamp()}

        all_executives = list_all_executives()
        data, errs = await __fetch_twitter_executives_channels(all_executives)
        if errs:
            logger.error(json.dumps(errs, indent=2, default=str))

        diff = __compare_channels(__TWITTER_CHANNELS, data)
        logger.debug(
            f"diffs: {json.dumps(diff, indent=2) if diff['count'] < 50 else diff['count']}"
        )
        if diff:
            info["diff"] = diff

            # upload
            s = json.dumps(data, indent=2, default=lambda o: o.to_dict())
            logger.debug(f"Misc Storage putting: {TWITTER_FILE_NAME}")
            await prepared_store.aput_data(TWITTER_FILE_NAME, s)
            s = json.dumps(info, indent=2)
            logger.debug(f"Misc Storage putting: {TWITTER_INFO_FILE_NAME}")
            await prepared_store.aput_data(TWITTER_INFO_FILE_NAME, s)

        logger.info(f"fetched & uploaded {len(data)} channels.")
    finally:
        prepared_store.unlock(TWITTER_STORE_LOCK_KEY)

    return diff


def _download_twitter_channels_data() -> None:
    """download executives data from storage (not fetch from API)"""
    local_info_path = PREPARED_DATA_PATH / TWITTER_INFO_FILE_NAME
    local_data_path = PREPARED_DATA_PATH / TWITTER_FILE_NAME

    local_info = __load_channel_data_info()
    local_ts = int(local_info.get("timestamp", 0))

    logger.debug(f"Misc Storage getting: {TWITTER_INFO_FILE_NAME}")
    latest_info_str = prepared_store.get_data(TWITTER_INFO_FILE_NAME) or "{}"
    latest_info = json.loads(latest_info_str)
    latest_ts = int(latest_info.get("timestamp", 0))

    if latest_ts > local_ts:
        logger.info(
            f"downloading twitter channel data from store. "
            f'(store time: {latest_info.get("updated_at")}, local time: {local_info.get("updated_at")})'
        )
        logger.debug(f"Misc Storage getting: {TWITTER_FILE_NAME}")
        s = prepared_store.get_data(TWITTER_FILE_NAME)
        with local_data_path.open("w") as f:
            f.write(s)
        with local_info_path.open("w") as f:
            f.write(latest_info_str)


def _unlock() -> bool:
    return prepared_store.unlock(TWITTER_STORE_LOCK_KEY)


# ----- private functions ------


async def __fetch_twitter_executives_channels(
    all_executives: list[CompanyExecutive],
) -> tuple[list[TwitterChannel], list[BaseException]]:
    """fetch twitter executive person channels by searching company executive names"""
    # TODO(data): make return values dict[TICKER, TwitterChannel]
    total_items = len(all_executives)
    total_twitter_channels = []
    errs: list[BaseException] = []
    for i in range(0, total_items, BATCH_SIZE):
        batch = list(range(i, min(i + BATCH_SIZE, total_items)))
        executives = [all_executives[i] for i in batch]

        try:
            result = await __download_batch(executives)
        except Exception as e:
            logger.error(f"Error downloading batch: {batch}. {e}")
            traceback.print_exc()
            errs.append(e)
            continue

        logger.debug(
            f"Processed {i + BATCH_SIZE} out of {total_items} twitter channels"
        )
        total_twitter_channels.extend(result)

    logger.info(f"Total channels: {len(total_twitter_channels)} expected {total_items}")
    return total_twitter_channels, errs


async def __download_batch(persons: list[CompanyExecutive]) -> list[TwitterChannel]:
    client = ApifyClientAsync(conf.APIFY_TOKEN)
    tasks = {}
    for person in persons:
        company_name = get_company_name_by_ticker(person.ticker)
        term_name = f"{person.name} of {company_name} ({person.ticker})"
        run_input = {
            "queries": f"{term_name} Twitter official account",
        }
        run_options = {
            "build": "latest",
            "timeout_secs": 600,
        }
        run = client.actor("nFJndFXA5zjCTuudP").call(
            run_input=run_input,
            **run_options,  # type:ignore
        )
        tasks[term_name] = {"person": person, "result": run}

    results = await asyncio.gather(
        *[task["result"] for task in tasks.values()]
    )  # type:ignore

    all_twitter_results = []
    for term_name, result in zip(tasks.keys(), results):
        person = tasks[term_name]["person"]  # type:ignore
        resp = await client.dataset(result["defaultDatasetId"]).list_items()
        data = resp.items
        if not data or len(data) == 0:
            continue
        organic_results = data[0].get("organicResults", [])
        twitter_results = [
            result
            for result in organic_results
            if result.get("url", "").startswith("https://x.com")
            or result.get("url", "").startswith("https://twitter.com")
        ]
        if not twitter_results or len(twitter_results) == 0:
            continue
        twitter_obj = TwitterChannel(
            search_term=term_name,
            ticker=person.ticker,
            url=__clean_url(twitter_results[0].get("url", "")),
            person_name=person.name,
        )
        all_twitter_results.append(twitter_obj)

    return all_twitter_results


def __compare_channels(current: list, latest: list) -> dict:
    """compare newly update data with current version data."""
    # so far we do not compare channels
    return {"count": len(latest)}


def __load_channel_data_info() -> dict:
    """load twitter channels data info from local"""
    local_info = {}
    local_info_path = PREPARED_DATA_PATH / TWITTER_INFO_FILE_NAME

    if local_info_path.exists():
        with local_info_path.open() as f:
            s = f.read()
        local_info = json.loads(s)
    return local_info


def __load_channels_data() -> list:
    """load twitter channels data from local"""
    _download_twitter_channels_data()

    local_data = []
    local_data_path = PREPARED_DATA_PATH / TWITTER_INFO_FILE_NAME

    if local_data_path.exists():
        with local_data_path.open() as f:
            s = f.read()
        local_data = json.loads(s)
    logger.debug("loaded executives data.")
    return local_data


__TWITTER_CHANNELS: list[dict] = LazyObject(lambda: __load_channels_data())  # type: ignore
