# Generated by Django 4.2.2 on 2024-06-03 14:15

from django.db import migrations, models

import datasvc.enums


class Migration(migrations.Migration):
    dependencies = [
        ("datasvc", "0010_alter_conference_key_alter_earningcall_key_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="conference",
            name="status",
            field=models.IntegerField(
                db_index=True,
                default=datasvc.enums.DocStatus["CREATED"],
                help_text="doc status, see `DocStatus`.",
            ),
        ),
        migrations.AlterField(
            model_name="earningcall",
            name="status",
            field=models.IntegerField(
                db_index=True,
                default=datasvc.enums.DocStatus["CREATED"],
                help_text="doc status, see `DocStatus`.",
            ),
        ),
        migrations.AlterField(
            model_name="news",
            name="status",
            field=models.IntegerField(
                db_index=True,
                default=datasvc.enums.DocStatus["CREATED"],
                help_text="doc status, see `DocStatus`.",
            ),
        ),
        migrations.AlterField(
            model_name="pressrelease",
            name="status",
            field=models.IntegerField(
                db_index=True,
                default=datasvc.enums.DocStatus["CREATED"],
                help_text="doc status, see `DocStatus`.",
            ),
        ),
        migrations.AlterField(
            model_name="secfiling",
            name="status",
            field=models.IntegerField(
                db_index=True,
                default=datasvc.enums.DocStatus["CREATED"],
                help_text="doc status, see `DocStatus`.",
            ),
        ),
        migrations.AlterField(
            model_name="tweet",
            name="status",
            field=models.IntegerField(
                db_index=True,
                default=datasvc.enums.DocStatus["CREATED"],
                help_text="doc status, see `DocStatus`.",
            ),
        ),
        migrations.AlterField(
            model_name="youtubevideo",
            name="status",
            field=models.IntegerField(
                db_index=True,
                default=datasvc.enums.DocStatus["CREATED"],
                help_text="doc status, see `DocStatus`.",
            ),
        ),
    ]
