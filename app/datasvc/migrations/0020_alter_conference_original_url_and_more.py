# Generated by Django 4.2.2 on 2024-06-10 08:45

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("datasvc", "0019_alter_earningcall_fiscal_quarter_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="conference",
            name="original_url",
            field=models.Char<PERSON><PERSON>(
                help_text="the original link where the doc downloaded", max_length=250
            ),
        ),
        migrations.AlterField(
            model_name="conference",
            name="provider",
            field=models.CharField(
                choices=[
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="conference",
            name="ticker",
            field=models.<PERSON>r<PERSON><PERSON>(
                db_index=True, help_text="the doc related company ticker", max_length=10
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="conference",
            name="title",
            field=models.CharField(
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=250,
            ),
        ),
        migrations.AlterField(
            model_name="earningcall",
            name="original_url",
            field=models.CharField(
                help_text="the original link where the doc downloaded", max_length=250
            ),
        ),
        migrations.AlterField(
            model_name="earningcall",
            name="provider",
            field=models.CharField(
                choices=[
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="earningcall",
            name="ticker",
            field=models.CharField(
                db_index=True, help_text="the doc related company ticker", max_length=10
            ),
        ),
        migrations.AlterField(
            model_name="earningcall",
            name="title",
            field=models.CharField(
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=250,
            ),
        ),
        migrations.AlterField(
            model_name="news",
            name="original_url",
            field=models.CharField(
                help_text="the original link where the doc downloaded", max_length=250
            ),
        ),
        migrations.AlterField(
            model_name="news",
            name="provider",
            field=models.CharField(
                choices=[
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="news",
            name="ticker",
            field=models.CharField(
                db_index=True, help_text="the doc related company ticker", max_length=10
            ),
        ),
        migrations.AlterField(
            model_name="news",
            name="title",
            field=models.CharField(
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=250,
            ),
        ),
        migrations.AlterField(
            model_name="pressrelease",
            name="original_url",
            field=models.CharField(
                help_text="the original link where the doc downloaded", max_length=250
            ),
        ),
        migrations.AlterField(
            model_name="pressrelease",
            name="provider",
            field=models.CharField(
                choices=[
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="pressrelease",
            name="ticker",
            field=models.CharField(
                db_index=True, help_text="the doc related company ticker", max_length=10
            ),
        ),
        migrations.AlterField(
            model_name="pressrelease",
            name="title",
            field=models.CharField(
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=250,
            ),
        ),
        migrations.AlterField(
            model_name="secfiling",
            name="original_url",
            field=models.CharField(
                help_text="the original link where the doc downloaded", max_length=250
            ),
        ),
        migrations.AlterField(
            model_name="secfiling",
            name="provider",
            field=models.CharField(
                choices=[
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="secfiling",
            name="ticker",
            field=models.CharField(
                db_index=True, help_text="the doc related company ticker", max_length=10
            ),
        ),
        migrations.AlterField(
            model_name="secfiling",
            name="title",
            field=models.CharField(
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=250,
            ),
        ),
        migrations.AlterField(
            model_name="tweet",
            name="channel_name",
            field=models.CharField(
                help_text="channel or account name of the social media post. usually it is unique.",
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="tweet",
            name="original_url",
            field=models.CharField(
                help_text="the original link where the doc downloaded", max_length=250
            ),
        ),
        migrations.AlterField(
            model_name="tweet",
            name="post_id",
            field=models.CharField(
                help_text="id of the social media post. usually it is the unique identity of the post on the social platform.",
                max_length=32,
            ),
        ),
        migrations.AlterField(
            model_name="tweet",
            name="provider",
            field=models.CharField(
                choices=[
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="tweet",
            name="ticker",
            field=models.CharField(
                db_index=True, help_text="the doc related company ticker", max_length=10
            ),
        ),
        migrations.AlterField(
            model_name="tweet",
            name="title",
            field=models.CharField(
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=250,
            ),
        ),
        migrations.AlterField(
            model_name="youtubevideo",
            name="channel_id",
            field=models.CharField(max_length=32),
        ),
        migrations.AlterField(
            model_name="youtubevideo",
            name="channel_name",
            field=models.CharField(
                help_text="channel or account name of the social media post. usually it is unique.",
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="youtubevideo",
            name="desc",
            field=models.TextField(default=""),
        ),
        migrations.AlterField(
            model_name="youtubevideo",
            name="original_url",
            field=models.CharField(
                help_text="the original link where the doc downloaded", max_length=250
            ),
        ),
        migrations.AlterField(
            model_name="youtubevideo",
            name="post_id",
            field=models.CharField(
                help_text="id of the social media post. usually it is the unique identity of the post on the social platform.",
                max_length=32,
            ),
        ),
        migrations.AlterField(
            model_name="youtubevideo",
            name="provider",
            field=models.CharField(
                choices=[
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="youtubevideo",
            name="ticker",
            field=models.CharField(
                db_index=True, help_text="the doc related company ticker", max_length=10
            ),
        ),
        migrations.AlterField(
            model_name="youtubevideo",
            name="title",
            field=models.CharField(
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=250,
            ),
        ),
    ]
