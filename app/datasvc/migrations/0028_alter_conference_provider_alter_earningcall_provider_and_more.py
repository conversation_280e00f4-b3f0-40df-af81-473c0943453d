# Generated by Django 4.2.2 on 2024-08-07 07:09

import biz.enums
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("datasvc", "0027_remove_eventtranscript_fiscal_year_str"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="conference",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="earningcall",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="eventtranscript",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="news",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="pressrelease",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="secfiling",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="tweet",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="youtubevideo",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
    ]
