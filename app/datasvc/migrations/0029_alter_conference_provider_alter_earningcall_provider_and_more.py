# Generated by Django 4.2.2 on 2024-10-21 10:50

import biz.enums
import biz.structs
import datasvc.enums
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        (
            "datasvc",
            "0028_alter_conference_provider_alter_earningcall_provider_and_more",
        ),
    ]

    operations = [
        migrations.AlterField(
            model_name="conference",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("customer", "CUSTOMER"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="earningcall",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("customer", "CUSTOMER"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="eventtranscript",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("customer", "CUSTOMER"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="news",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("customer", "CUSTOMER"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="pressrelease",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("customer", "CUSTOMER"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="secfiling",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("customer", "CUSTOMER"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="tweet",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("customer", "CUSTOMER"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="youtubevideo",
            name="provider",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "NOT_SPECIFIED"),
                    ("fmp", "FMP"),
                    ("quartr", "QUARTR"),
                    ("benzinga", "BENZINGA"),
                    ("seeking_alpha", "SEEKING_ALPHA"),
                    ("bot", "BOT"),
                    ("apify", "APIFY"),
                    ("customer", "CUSTOMER"),
                    ("twitter", "TWITTER"),
                    ("youtube", "YOUTUBE"),
                ],
                default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.CreateModel(
            name="CustomerDoc",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "key",
                    models.CharField(
                        help_text="the unique identifier of the doc",
                        max_length=510,
                        unique=True,
                    ),
                ),
                (
                    "ticker",
                    models.CharField(
                        db_index=True,
                        help_text="the doc related company ticker",
                        max_length=10,
                    ),
                ),
                (
                    "pub_date",
                    models.DateTimeField(
                        db_index=True,
                        help_text="when the doc published, filed, or released.",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        blank=True,
                        default="",
                        help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                        max_length=510,
                    ),
                ),
                (
                    "provider",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("", "NOT_SPECIFIED"),
                            ("fmp", "FMP"),
                            ("quartr", "QUARTR"),
                            ("benzinga", "BENZINGA"),
                            ("seeking_alpha", "SEEKING_ALPHA"),
                            ("bot", "BOT"),
                            ("apify", "APIFY"),
                            ("customer", "CUSTOMER"),
                            ("twitter", "TWITTER"),
                            ("youtube", "YOUTUBE"),
                        ],
                        default=biz.enums.DataProviderEnum["NOT_SPECIFIED"],
                        help_text="which data provider the data comes from",
                        max_length=50,
                    ),
                ),
                (
                    "original_url",
                    models.CharField(
                        blank=True,
                        default="",
                        help_text="the original link where the doc downloaded",
                        max_length=510,
                    ),
                ),
                (
                    "quality_score",
                    models.FloatField(
                        default=0.0,
                        help_text="the score of the doc quality which evaluated by ourselves",
                    ),
                ),
                ("tickers", models.JSONField(default=list)),
                ("user_id", models.IntegerField(db_index=True)),
                (
                    "org_id",
                    models.CharField(
                        blank=True, db_index=True, default="", max_length=50
                    ),
                ),
                (
                    "original_filename",
                    models.CharField(
                        help_text="raw file name without folder", max_length=255
                    ),
                ),
                (
                    "content_type",
                    models.CharField(help_text="raw content type", max_length=255),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "status",
                    models.CharField(
                        default=datasvc.enums.DocStatus["CREATED"],
                        help_text="doc status, see `DocStatus`.",
                        max_length=20,
                    ),
                ),
                (
                    "last_error",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="last error for process doc content.",
                    ),
                ),
            ],
            options={
                "unique_together": {("user_id", "key")},
            },
            bases=(biz.structs.DocDisplayTitleMixin, models.Model),
        ),
    ]
