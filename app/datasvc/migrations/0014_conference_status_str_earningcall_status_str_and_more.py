# Generated by Django 4.2.2 on 2024-06-05 13:08

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("datasvc", "0013_remove_conference_ref_url_remove_conference_source_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="conference",
            name="status_str",
            field=models.CharField(
                default="created",
                help_text="doc status, see `DocStatus`.",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="earningcall",
            name="status_str",
            field=models.CharField(
                default="created",
                help_text="doc status, see `DocStatus`.",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="news",
            name="status_str",
            field=models.CharField(
                default="created",
                help_text="doc status, see `DocStatus`.",
                max_length=20,
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="pressrelease",
            name="status_str",
            field=models.Char<PERSON><PERSON>(
                default="created",
                help_text="doc status, see `DocStatus`.",
                max_length=20,
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="secfiling",
            name="status_str",
            field=models.Char<PERSON>ield(
                default="created",
                help_text="doc status, see `DocStatus`.",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="tweet",
            name="status_str",
            field=models.CharField(
                default="created",
                help_text="doc status, see `DocStatus`.",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="youtubevideo",
            name="status_str",
            field=models.CharField(
                default="created",
                help_text="doc status, see `DocStatus`.",
                max_length=20,
            ),
        ),
        *[
            migrations.RunSQL(
                f"""update {t} set status_str=(
                case when status = 0 then 'created'
                    when status > 0 and status < 100 then 'persist_fail'
                    when status = 100 then 'persisted'
                    when status > 100 and status < 200 then 'index_fail'
                    when status = 200 then 'indexed'
                    when status = -10000 then 'created'
                    else 'created'
                end);"""
            )
            for t in [
                "datasvc_earningcall",
                "datasvc_secfiling",
                "datasvc_conference",
                "datasvc_pressrelease",
                "datasvc_news",
                "datasvc_tweet",
                "datasvc_youtubevideo",
            ]
        ],
    ]
