# Generated by Django 4.2.2 on 2024-05-27 08:12

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("datasvc", "0004_remove_conference_source_remove_earningcall_source_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="conference",
            name="source",
            field=models.Char<PERSON>ield(
                help_text="where the doc form (site or platform name, domain, etc)",
                max_length=100,
            ),
        ),
        migrations.AddField(
            model_name="earningcall",
            name="source",
            field=models.Char<PERSON>ield(
                help_text="where the doc form (site or platform name, domain, etc)",
                max_length=100,
            ),
        ),
        migrations.AddField(
            model_name="news",
            name="source",
            field=models.Char<PERSON>ield(
                help_text="where the doc form (site or platform name, domain, etc)",
                max_length=100,
            ),
        ),
        migrations.AddField(
            model_name="pressrelease",
            name="source",
            field=models.CharField(
                help_text="where the doc form (site or platform name, domain, etc)",
                max_length=100,
            ),
        ),
        migrations.AddField(
            model_name="reversedthirteenf",
            name="source",
            field=models.Char<PERSON>ield(
                help_text="where the doc form (site or platform name, domain, etc)",
                max_length=100,
            ),
        ),
        migrations.AddField(
            model_name="secfiling",
            name="source",
            field=models.CharField(
                help_text="where the doc form (site or platform name, domain, etc)",
                max_length=100,
            ),
        ),
        migrations.AddField(
            model_name="socialmedia",
            name="source",
            field=models.CharField(
                help_text="where the doc form (site or platform name, domain, etc)",
                max_length=100,
            ),
        ),
        migrations.AddField(
            model_name="thirteenf",
            name="source",
            field=models.CharField(
                help_text="where the doc form (site or platform name, domain, etc)",
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="conference",
            name="provider",
            field=models.CharField(
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="earningcall",
            name="provider",
            field=models.CharField(
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="news",
            name="provider",
            field=models.CharField(
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="pressrelease",
            name="provider",
            field=models.CharField(
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="reversedthirteenf",
            name="provider",
            field=models.CharField(
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="secfiling",
            name="provider",
            field=models.CharField(
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="socialmedia",
            name="provider",
            field=models.CharField(
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="thirteenf",
            name="provider",
            field=models.CharField(
                help_text="which data provider the data comes from",
                max_length=50,
            ),
        ),
    ]
