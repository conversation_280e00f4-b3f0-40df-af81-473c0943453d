# Generated by Django 4.2.2 on 2024-06-19 05:00

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("datasvc", "0024_alter_conference_key_alter_conference_original_url_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="conference",
            name="title",
            field=models.CharField(
                blank=True,
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=510,
            ),
        ),
        migrations.AlterField(
            model_name="earningcall",
            name="title",
            field=models.CharField(
                blank=True,
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=510,
            ),
        ),
        migrations.AlterField(
            model_name="news",
            name="title",
            field=models.Char<PERSON>ield(
                blank=True,
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=510,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="pressrelease",
            name="title",
            field=models.CharField(
                blank=True,
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=510,
            ),
        ),
        migrations.AlterField(
            model_name="secfiling",
            name="title",
            field=models.CharField(
                blank=True,
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=510,
            ),
        ),
        migrations.AlterField(
            model_name="tweet",
            name="title",
            field=models.CharField(
                blank=True,
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=510,
            ),
        ),
        migrations.AlterField(
            model_name="youtubevideo",
            name="title",
            field=models.CharField(
                blank=True,
                default="",
                help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                max_length=510,
            ),
        ),
    ]
