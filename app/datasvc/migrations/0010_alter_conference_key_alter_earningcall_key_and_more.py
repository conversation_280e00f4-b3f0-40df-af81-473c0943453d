# Generated by Django 4.2.2 on 2024-06-03 09:08

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("datasvc", "0009_rename_author_name_tweet_channel_name_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="conference",
            name="key",
            field=models.Char<PERSON><PERSON>(
                help_text="the unique identifier of the doc",
                max_length=255,
                unique=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="earningcall",
            name="key",
            field=models.Char<PERSON><PERSON>(
                help_text="the unique identifier of the doc",
                max_length=255,
                unique=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="news",
            name="key",
            field=models.Char<PERSON><PERSON>(
                help_text="the unique identifier of the doc",
                max_length=255,
                unique=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="pressrelease",
            name="key",
            field=models.<PERSON>r<PERSON><PERSON>(
                help_text="the unique identifier of the doc",
                max_length=255,
                unique=True,
            ),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name="secfiling",
            name="key",
            field=models.Char<PERSON><PERSON>(
                help_text="the unique identifier of the doc",
                max_length=255,
                unique=True,
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="tweet",
            name="key",
            field=models.CharField(
                help_text="the unique identifier of the doc",
                max_length=255,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="youtubevideo",
            name="key",
            field=models.CharField(
                help_text="the unique identifier of the doc",
                max_length=255,
                unique=True,
            ),
        ),
    ]
