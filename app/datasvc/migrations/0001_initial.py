# Generated by Django 4.2.2 on 2024-05-23 07:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []  # type:ignore

    operations = [
        migrations.CreateModel(
            name="Company",
            fields=[
                (
                    "ticker",
                    models.CharField(max_length=10, primary_key=True, serialize=False),
                ),
                ("name", models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ("market", models.<PERSON>r<PERSON><PERSON>(max_length=50)),
                ("industries", models.Char<PERSON>ield(max_length=250)),
                ("tags", models.CharField(max_length=250)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Conference",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTime<PERSON><PERSON>(auto_now=True)),
                (
                    "key",
                    models.<PERSON><PERSON><PERSON><PERSON>(
                        help_text="the unique identifier of the doc",
                        max_length=200,
                        unique=True,
                    ),
                ),
                (
                    "ticker",
                    models.CharField(
                        db_index=True,
                        help_text="the doc related company ticker",
                        max_length=10,
                    ),
                ),
                (
                    "fiscal_year",
                    models.IntegerField(
                        help_text="The fiscal year of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types as news, we just fill the calendar year."
                    ),
                ),
                (
                    "fiscal_quarter",
                    models.IntegerField(
                        choices=[(1, "Q1"), (2, "Q2"), (3, "Q3"), (4, "Q4")],
                        help_text="The fiscal quarter of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types such as news, we just fill the calendar quarter (calculated).",
                    ),
                ),
                (
                    "pub_date",
                    models.DateTimeField(
                        db_index=True,
                        help_text="when the doc published, filed, or released.",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                        max_length=100,
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        db_index=True,
                        help_text="where the doc form (site or platform name, domain, etc)",
                        max_length=100,
                    ),
                ),
                (
                    "url",
                    models.CharField(
                        help_text="the original link where the doc downloaded",
                        max_length=250,
                    ),
                ),
                (
                    "ref_url",
                    models.CharField(
                        help_text="the url where we got the original doc url. Generally it is the API link or platform link",
                        max_length=250,
                    ),
                ),
                (
                    "quality_score",
                    models.FloatField(
                        default=0.0,
                        help_text="the score of the doc quality which evaluated by ourselves",
                    ),
                ),
                (
                    "downloads",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc downloading status. 1 - downloaded; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
                (
                    "indexes",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc indexing status. 1 - indexed; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="EarningCall",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "key",
                    models.CharField(
                        help_text="the unique identifier of the doc",
                        max_length=200,
                        unique=True,
                    ),
                ),
                (
                    "ticker",
                    models.CharField(
                        db_index=True,
                        help_text="the doc related company ticker",
                        max_length=10,
                    ),
                ),
                (
                    "fiscal_year",
                    models.IntegerField(
                        help_text="The fiscal year of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types as news, we just fill the calendar year."
                    ),
                ),
                (
                    "fiscal_quarter",
                    models.IntegerField(
                        choices=[(1, "Q1"), (2, "Q2"), (3, "Q3"), (4, "Q4")],
                        help_text="The fiscal quarter of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types such as news, we just fill the calendar quarter (calculated).",
                    ),
                ),
                (
                    "pub_date",
                    models.DateTimeField(
                        db_index=True,
                        help_text="when the doc published, filed, or released.",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                        max_length=100,
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        db_index=True,
                        help_text="where the doc form (site or platform name, domain, etc)",
                        max_length=100,
                    ),
                ),
                (
                    "url",
                    models.CharField(
                        help_text="the original link where the doc downloaded",
                        max_length=250,
                    ),
                ),
                (
                    "ref_url",
                    models.CharField(
                        help_text="the url where we got the original doc url. Generally it is the API link or platform link",
                        max_length=250,
                    ),
                ),
                (
                    "quality_score",
                    models.FloatField(
                        default=0.0,
                        help_text="the score of the doc quality which evaluated by ourselves",
                    ),
                ),
                (
                    "downloads",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc downloading status. 1 - downloaded; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
                (
                    "indexes",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc indexing status. 1 - indexed; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
            ],
            options={
                "unique_together": {("ticker", "fiscal_quarter", "fiscal_year")},
            },
        ),
        migrations.CreateModel(
            name="IndexStatistics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("index_name", models.CharField(max_length=400)),
                ("index_namespace", models.CharField(max_length=400)),
                ("data", models.JSONField(default=dict)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="NewsPage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "key",
                    models.CharField(
                        help_text="the unique identifier of the doc",
                        max_length=200,
                        unique=True,
                    ),
                ),
                (
                    "ticker",
                    models.CharField(
                        db_index=True,
                        help_text="the doc related company ticker",
                        max_length=10,
                    ),
                ),
                (
                    "fiscal_year",
                    models.IntegerField(
                        help_text="The fiscal year of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types as news, we just fill the calendar year."
                    ),
                ),
                (
                    "fiscal_quarter",
                    models.IntegerField(
                        choices=[(1, "Q1"), (2, "Q2"), (3, "Q3"), (4, "Q4")],
                        help_text="The fiscal quarter of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types such as news, we just fill the calendar quarter (calculated).",
                    ),
                ),
                (
                    "pub_date",
                    models.DateTimeField(
                        db_index=True,
                        help_text="when the doc published, filed, or released.",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                        max_length=100,
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        db_index=True,
                        help_text="where the doc form (site or platform name, domain, etc)",
                        max_length=100,
                    ),
                ),
                (
                    "url",
                    models.CharField(
                        help_text="the original link where the doc downloaded",
                        max_length=250,
                    ),
                ),
                (
                    "ref_url",
                    models.CharField(
                        help_text="the url where we got the original doc url. Generally it is the API link or platform link",
                        max_length=250,
                    ),
                ),
                (
                    "quality_score",
                    models.FloatField(
                        default=0.0,
                        help_text="the score of the doc quality which evaluated by ourselves",
                    ),
                ),
                (
                    "downloads",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc downloading status. 1 - downloaded; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
                (
                    "indexes",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc indexing status. 1 - indexed; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
                ("classification", models.JSONField(default=list)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PressRelease",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "key",
                    models.CharField(
                        help_text="the unique identifier of the doc",
                        max_length=200,
                        unique=True,
                    ),
                ),
                (
                    "ticker",
                    models.CharField(
                        db_index=True,
                        help_text="the doc related company ticker",
                        max_length=10,
                    ),
                ),
                (
                    "fiscal_year",
                    models.IntegerField(
                        help_text="The fiscal year of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types as news, we just fill the calendar year."
                    ),
                ),
                (
                    "fiscal_quarter",
                    models.IntegerField(
                        choices=[(1, "Q1"), (2, "Q2"), (3, "Q3"), (4, "Q4")],
                        help_text="The fiscal quarter of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types such as news, we just fill the calendar quarter (calculated).",
                    ),
                ),
                (
                    "pub_date",
                    models.DateTimeField(
                        db_index=True,
                        help_text="when the doc published, filed, or released.",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                        max_length=100,
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        db_index=True,
                        help_text="where the doc form (site or platform name, domain, etc)",
                        max_length=100,
                    ),
                ),
                (
                    "url",
                    models.CharField(
                        help_text="the original link where the doc downloaded",
                        max_length=250,
                    ),
                ),
                (
                    "ref_url",
                    models.CharField(
                        help_text="the url where we got the original doc url. Generally it is the API link or platform link",
                        max_length=250,
                    ),
                ),
                (
                    "quality_score",
                    models.FloatField(
                        default=0.0,
                        help_text="the score of the doc quality which evaluated by ourselves",
                    ),
                ),
                (
                    "downloads",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc downloading status. 1 - downloaded; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
                (
                    "indexes",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc indexing status. 1 - indexed; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="ReversedThirteenF",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "key",
                    models.CharField(
                        help_text="the unique identifier of the doc",
                        max_length=200,
                        unique=True,
                    ),
                ),
                (
                    "ticker",
                    models.CharField(
                        db_index=True,
                        help_text="the doc related company ticker",
                        max_length=10,
                    ),
                ),
                (
                    "fiscal_year",
                    models.IntegerField(
                        help_text="The fiscal year of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types as news, we just fill the calendar year."
                    ),
                ),
                (
                    "fiscal_quarter",
                    models.IntegerField(
                        choices=[(1, "Q1"), (2, "Q2"), (3, "Q3"), (4, "Q4")],
                        help_text="The fiscal quarter of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types such as news, we just fill the calendar quarter (calculated).",
                    ),
                ),
                (
                    "pub_date",
                    models.DateTimeField(
                        db_index=True,
                        help_text="when the doc published, filed, or released.",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                        max_length=100,
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        db_index=True,
                        help_text="where the doc form (site or platform name, domain, etc)",
                        max_length=100,
                    ),
                ),
                (
                    "url",
                    models.CharField(
                        help_text="the original link where the doc downloaded",
                        max_length=250,
                    ),
                ),
                (
                    "ref_url",
                    models.CharField(
                        help_text="the url where we got the original doc url. Generally it is the API link or platform link",
                        max_length=250,
                    ),
                ),
                (
                    "quality_score",
                    models.FloatField(
                        default=0.0,
                        help_text="the score of the doc quality which evaluated by ourselves",
                    ),
                ),
                (
                    "downloads",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc downloading status. 1 - downloaded; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
                (
                    "indexes",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc indexing status. 1 - indexed; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
                ("cusip", models.CharField(max_length=40)),
                ("ticker_cusip", models.CharField(max_length=20)),
                ("quarter_date", models.DateTimeField()),
                ("link", models.URLField()),
                ("final_link", models.URLField()),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="SECFiling",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "key",
                    models.CharField(
                        help_text="the unique identifier of the doc",
                        max_length=200,
                        unique=True,
                    ),
                ),
                (
                    "ticker",
                    models.CharField(
                        db_index=True,
                        help_text="the doc related company ticker",
                        max_length=10,
                    ),
                ),
                (
                    "fiscal_year",
                    models.IntegerField(
                        help_text="The fiscal year of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types as news, we just fill the calendar year."
                    ),
                ),
                (
                    "fiscal_quarter",
                    models.IntegerField(
                        choices=[(1, "Q1"), (2, "Q2"), (3, "Q3"), (4, "Q4")],
                        help_text="The fiscal quarter of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types such as news, we just fill the calendar quarter (calculated).",
                    ),
                ),
                (
                    "pub_date",
                    models.DateTimeField(
                        db_index=True,
                        help_text="when the doc published, filed, or released.",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                        max_length=100,
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        db_index=True,
                        help_text="where the doc form (site or platform name, domain, etc)",
                        max_length=100,
                    ),
                ),
                (
                    "url",
                    models.CharField(
                        help_text="the original link where the doc downloaded",
                        max_length=250,
                    ),
                ),
                (
                    "ref_url",
                    models.CharField(
                        help_text="the url where we got the original doc url. Generally it is the API link or platform link",
                        max_length=250,
                    ),
                ),
                (
                    "quality_score",
                    models.FloatField(
                        default=0.0,
                        help_text="the score of the doc quality which evaluated by ourselves",
                    ),
                ),
                (
                    "downloads",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc downloading status. 1 - downloaded; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
                (
                    "indexes",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc indexing status. 1 - indexed; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
                (
                    "filing_type",
                    models.CharField(
                        choices=[("10-Q", "10-Q"), ("10-K", "10-K")], max_length=100
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="SocialMedia",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "key",
                    models.CharField(
                        help_text="the unique identifier of the doc",
                        max_length=200,
                        unique=True,
                    ),
                ),
                (
                    "ticker",
                    models.CharField(
                        db_index=True,
                        help_text="the doc related company ticker",
                        max_length=10,
                    ),
                ),
                (
                    "fiscal_year",
                    models.IntegerField(
                        help_text="The fiscal year of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types as news, we just fill the calendar year."
                    ),
                ),
                (
                    "fiscal_quarter",
                    models.IntegerField(
                        choices=[(1, "Q1"), (2, "Q2"), (3, "Q3"), (4, "Q4")],
                        help_text="The fiscal quarter of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types such as news, we just fill the calendar quarter (calculated).",
                    ),
                ),
                (
                    "pub_date",
                    models.DateTimeField(
                        db_index=True,
                        help_text="when the doc published, filed, or released.",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                        max_length=100,
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        db_index=True,
                        help_text="where the doc form (site or platform name, domain, etc)",
                        max_length=100,
                    ),
                ),
                (
                    "url",
                    models.CharField(
                        help_text="the original link where the doc downloaded",
                        max_length=250,
                    ),
                ),
                (
                    "ref_url",
                    models.CharField(
                        help_text="the url where we got the original doc url. Generally it is the API link or platform link",
                        max_length=250,
                    ),
                ),
                (
                    "quality_score",
                    models.FloatField(
                        default=0.0,
                        help_text="the score of the doc quality which evaluated by ourselves",
                    ),
                ),
                (
                    "downloads",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc downloading status. 1 - downloaded; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
                (
                    "indexes",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc indexing status. 1 - indexed; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
                ("author_name", models.CharField(max_length=500)),
                ("desc", models.TextField()),
                ("post_key", models.CharField(max_length=500)),
                ("s3_key", models.CharField(max_length=500)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="ThirteenF",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "key",
                    models.CharField(
                        help_text="the unique identifier of the doc",
                        max_length=200,
                        unique=True,
                    ),
                ),
                (
                    "ticker",
                    models.CharField(
                        db_index=True,
                        help_text="the doc related company ticker",
                        max_length=10,
                    ),
                ),
                (
                    "fiscal_year",
                    models.IntegerField(
                        help_text="The fiscal year of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types as news, we just fill the calendar year."
                    ),
                ),
                (
                    "fiscal_quarter",
                    models.IntegerField(
                        choices=[(1, "Q1"), (2, "Q2"), (3, "Q3"), (4, "Q4")],
                        help_text="The fiscal quarter of the doc. it's common to financial docs such as earning call, SEC filing, etc. For other doc types such as news, we just fill the calendar quarter (calculated).",
                    ),
                ),
                (
                    "pub_date",
                    models.DateTimeField(
                        db_index=True,
                        help_text="when the doc published, filed, or released.",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="The title or the doc. If the original doc does not have a title, let's generate for it.",
                        max_length=100,
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        db_index=True,
                        help_text="where the doc form (site or platform name, domain, etc)",
                        max_length=100,
                    ),
                ),
                (
                    "url",
                    models.CharField(
                        help_text="the original link where the doc downloaded",
                        max_length=250,
                    ),
                ),
                (
                    "ref_url",
                    models.CharField(
                        help_text="the url where we got the original doc url. Generally it is the API link or platform link",
                        max_length=250,
                    ),
                ),
                (
                    "quality_score",
                    models.FloatField(
                        default=0.0,
                        help_text="the score of the doc quality which evaluated by ourselves",
                    ),
                ),
                (
                    "downloads",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc downloading status. 1 - downloaded; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
                (
                    "indexes",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="doc indexing status. 1 - indexed; 0 - not started; -N - tried N times but fail.",
                    ),
                ),
                ("cik", models.CharField(max_length=40)),
                ("cik_name", models.CharField(max_length=300)),
                ("quarter_date", models.DateTimeField()),
                ("link", models.URLField()),
                ("final_link", models.URLField()),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="EarningCallParsingRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("line_count", models.IntegerField()),
                ("avg_chunk_lines", models.FloatField()),
                ("avg_question_length", models.FloatField()),
                ("operator", models.CharField(max_length=50)),
                ("speakers", models.JSONField(default=dict)),
                ("analysis_records", models.JSONField(default=list)),
                ("chunks", models.JSONField(default=list)),
                (
                    "transcript",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="datasvc.earningcall",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
