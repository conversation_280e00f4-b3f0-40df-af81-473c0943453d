from pathlib import Path

from common.utils.confutil import (
    get_bool_env,
    get_int_env,
    get_str_env,
    MultiValueConfItem,
    get_obj_env,
)

# general
PACKAGE_ROOT = Path(__file__).parent
ENV = get_str_env("ENV", "prod").lower()

# debug
DEBUG = get_bool_env("DEBUG", False)
VERBOSE = get_int_env("VERBOSE", 0)

# indexing
PINECONE_API_KEY = get_str_env("PINECONE_API_KEY")
PINECONE_NAMESPACE = get_str_env("PINECONE_NAMESPACE")
PINECONE_INDEX_NAMES = get_obj_env("PINECONE_INDEX_NAME")
# defined in .env as below (.A .B .C .D)
# PINECONE_INDEX_NAME.A=cocktail-dev-a-financial
# PINECONE_INDEX_NAME.B=cocktail-dev-b-company
# PINECONE_INDEX_NAME.C=cocktail-dev-c-media
# PINECONE_INDEX_NAME.D=cocktail-dev-d-social
# PINECONE_INDEX_NAME.E=cocktail-dev-e-customer

OPENAI_API_KEY = get_str_env("OPENAI_API_KEY")  # for index embedding

# storage
S3_DOC_BUCKET = get_str_env("S3_DOC_BUCKET")
if not S3_DOC_BUCKET:
    raise EnvironmentError("S3_DOC_BUCKET environment variable not set.")

S3_CUSTOMER_DATA_BUCKET = get_str_env("S3_CUSTOMER_DATA_BUCKET")
if not S3_CUSTOMER_DATA_BUCKET:
    raise EnvironmentError("S3_CUSTOMER_DATA_BUCKET environment variable not set.")


S3_PREPARED_DATA_STORE = get_str_env("S3_PREPARED_DATA_STORE", "onwish-prepared-dev")

# spider
TWITTER_BOT_SERVER_ADDRESS = get_str_env("TWITTER_BOT_SERVER_ADDRESS", "")
TWITTER_BOT_SERVER_IS_WIN = get_bool_env("TWITTER_BOT_SERVER_IS_WIN", default=False)
TWITTER_BOT_SERVER_IS_HEADLESS = get_bool_env(
    "TWITTER_BOT_SERVER_IS_HEADLESS", default=False
)

# 3rd party data providers
SEC_API = MultiValueConfItem("SEC_API")
APIFY_TOKEN = get_str_env("APIFY_TOKEN")
SEEKING_ALPHA_USERNAME = get_str_env("SEEKING_ALPHA_USERNAME")
SEEKING_ALPHA_PASSWORD = get_str_env("SEEKING_ALPHA_PASSWORD")
SA_COOKIE = get_obj_env("SA_COOKIE")
