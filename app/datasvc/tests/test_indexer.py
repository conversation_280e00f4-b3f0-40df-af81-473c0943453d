from typing import Any
from unittest.mock import patch, MagicMock

import pytest

from biz.enums import Doc<PERSON>ype<PERSON>num, IndexProviderEnum
from biz.search.base import DOC_TYPE_TO_UPSERT_ID
from datasvc.indexing.pinecone import PineconeIndexConfig, PineconeHybridIndex


@pytest.fixture
def pinecone_hybrid_cfg() -> PineconeIndexConfig:
    return PineconeIndexConfig(
        provider=IndexProviderEnum.PINECONE_HYBRID,
        name="test_index",
        api_key="test_api_key",
        env="test_env",
        namespace="test_namespace",
    )


@pytest.fixture
def pinecone_hybrid_index(
    pinecone_hybrid_cfg: PineconeIndexConfig,
) -> PineconeHybridIndex:
    return PineconeHybridIndex(config=pinecone_hybrid_cfg)


def test_upsert(pinecone_hybrid_index: PineconeHybridIndex) -> None:
    # Mock dependencies
    with patch("pinecone.Pinecone.Index") as mock_index, patch(
        "datasvc.indexing.pinecone.PineconeHybridIndex.dense_embeddings",
        MagicMock(),
    ) as mock_dense_embeddings, patch(
        "datasvc.indexing.pinecone.PineconeHybridIndex.sparse_encoder",
        MagicMock(),
    ) as mock_sparse_encoder:
        mock_dense_embeddings.name = "mock.embeddings"
        mock_sparse_encoder.name = "mock.bm25_encoder"
        dense_encode_doc_func = mock_dense_embeddings.encode_doc
        sparse_encode_doc_func = mock_sparse_encoder.encode_doc

        # Configure mock for embeddings and encoder
        dense_encode_doc_func.return_value = [
            [0.1, 0.2, 0.3],
            [0.4, 0.5, 0.6],
        ]  # Example dense embeddings for two documents
        sparse_encode_doc_func.return_value = [
            {"values": [1, 2, 3]},
            {"values": [4, 5, 6]},
        ]  # Example sparse embeddings for two documents

        mock_pinecone_index: MagicMock = MagicMock()
        mock_index.return_value = mock_pinecone_index

        # Test data for two documents
        ids: list[str] = ["doc1", "doc2"]
        texts: list[str] = [
            "This is a test document.",
            "This is another test document.",
        ]
        metadatas: list[dict[str, Any]] = [{"key": "value"}, {"key": "another value"}]
        doc_type: str = DocTypeEnum.EARNING_CALL.value

        # Call the method under test
        pinecone_hybrid_index._upsert(ids, texts, metadatas, doc_type, overwrite=True)

        # Assertions
        mock_index.assert_called_once_with("test_index", pool_threads=10)
        dense_encode_doc_func.assert_called_once_with(texts)
        sparse_encode_doc_func.assert_called_once_with(texts)
        mock_pinecone_index.upsert.assert_called()

        # Verify that the upsert was called with expected parameters, this is a detailed check
        args, kwargs = mock_pinecone_index.upsert.call_args
        assert len(args[0]) == 2  # Assuming two documents were upserted
        assert kwargs["namespace"] == "test_namespace"
        upserted_document_1 = args[0][0]
        upserted_document_2 = args[0][1]
        assert upserted_document_1["id"] == ids[0], "First document ID does not match"
        assert upserted_document_2["id"] == ids[1], "Second document ID does not match"
        assert upserted_document_1["values"] == [
            0.1,
            0.2,
            0.3,
        ], "Dense embeddings for the first document do not match"
        assert upserted_document_2["values"] == [
            0.4,
            0.5,
            0.6,
        ], "Dense embeddings for the second document do not match"
        assert upserted_document_1["sparse_values"] == {
            "values": [1, 2, 3]
        }, "Sparse embeddings for the first document do not match"
        assert upserted_document_2["sparse_values"] == {
            "values": [4, 5, 6]
        }, "Sparse embeddings for the second document do not match"
        expected_metadata_1 = {
            **metadatas[0],
            **{"context": texts[0]},
            **{
                "index_upsert_id": DOC_TYPE_TO_UPSERT_ID[
                    DocTypeEnum.EARNING_CALL.value
                ].value,
                "dense_embeddings": "mock.embeddings",
                "sparse_encoder": "mock.bm25_encoder",
            },
        }
        expected_metadata_2 = {
            **metadatas[1],
            **{"context": texts[1]},
            **{
                "index_upsert_id": DOC_TYPE_TO_UPSERT_ID[
                    DocTypeEnum.EARNING_CALL.value
                ].value,
                "dense_embeddings": "mock.embeddings",
                "sparse_encoder": "mock.bm25_encoder",
            },
        }

        upserted_document_1["metadata"].pop("index_timestamp")
        assert (
            upserted_document_1["metadata"] == expected_metadata_1
        ), "Metadata for the first document does not match"

        upserted_document_2["metadata"].pop("index_timestamp")
        assert (
            upserted_document_2["metadata"] == expected_metadata_2
        ), "Metadata for the second document does not match"
