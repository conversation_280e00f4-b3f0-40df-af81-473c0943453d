from pathlib import Path

from biz.search.snippet import SnippetSearcher, SnippetMatcher
from common.utils.ioutil import load_url

cwd = Path(__file__).parent

SEARCHER_TEST_DATA = [
    # HTML examples (SEC filings)
    {
        "snippet": "Headcount was 66,185 as of September 30, 2023, a decrease of 24% year-over-year. A substantial majority of the employees impacted by the layoffs are no longer included in our reported headcount as of September 30, 2023.",
        "original": '<div style="padding-left:27pt;text-align:justify;text-indent:-18pt"><span style="background-color:#ffffff;color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%">&#8226;</span><span style="color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%;padding-left:14.5pt">Headcount was 66,185 as of September&#160;30, 2023, a decrease of 24% year-over-year. A substantial majority of the employees impacted by the layoffs are no longer included in our reported headcount as of </span><span style="background-color:#ffffff;color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%">September&#160;30, 2023. </span></div><div style="text-align:justify"><span><br/></span></div><div style="text-align:justify">',
        "expected_exists_verdict": True,
        "expected_highlighted": '<div style="padding-left:27pt;text-align:justify;text-indent:-18pt"><span style="background-color:#ffffff;color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%">&#8226;</span><span style="color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%;padding-left:14.5pt"><!-- quotes-0 -->Headcount was 66,185 as of September&#160;30, 2023, a decrease of 24% year-over-year.<!-- /quotes-0 --> <!-- quotes-1 -->A substantial majority of the employees impacted by the layoffs are no longer included in our reported headcount as of </span><span style="background-color:#ffffff;color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%">September&#160;30, 2023<!-- /quotes-1 -->. </span></div><div style="text-align:justify"><span><br/></span></div><div style="text-align:justify">',
    },
    {
        "snippet": "Headcount was 71,469 as of June 30, 2023, a decrease of 14% year-over-year.",
        "original": "<span style=\"color:#000000;font-family:'Times New Roman',sans-serif;font-size:10pt;font-weight:400;line-height:120%;padding-left:14.5pt\">Headcount was 71,469 as of June&#160;30, 2023, a decrease of 14% year-over-year. Approximately half of the employees impacted by the 2023 layoffs are included in our reported headcount as of </span>",
        "expected_exists_verdict": True,
        "expected_highlighted": "<span style=\"color:#000000;font-family:'Times New Roman',sans-serif;font-size:10pt;font-weight:400;line-height:120%;padding-left:14.5pt\"><!-- quotes-0 -->Headcount was 71,469 as of June&#160;30, 2023, a decrease of 14% year-over-year.<!-- /quotes-0 --> Approximately half of the employees impacted by the 2023 layoffs are included in our reported headcount as of </span>",
    },
    {
        "snippet": "In addition, year-over-year advertising revenue growth was driven mainly by marketer spending in online commerce, consumer packaged goods, and gaming while online commerce and gaming benefited from marketers based in China.",
        "original": 'Other factors are also discussed in the section entitled "&#8212;Executive Overview of Third Quarter Results." In addition, year-over-year advertising revenue growth was driven mainly by marketer spending in online commerce, consumer packaged goods, and gaming while online commerce and gaming benefited from marketers based in China. We anticipate that future advertising revenue will be driven by a combination of price and the number of ads delivered.</span>',
        "expected_exists_verdict": True,
        "expected_highlighted": 'Other factors are also discussed in the section entitled "&#8212;Executive Overview of Third Quarter Results." <!-- quotes-0 -->In addition, year-over-year advertising revenue growth was driven mainly by marketer spending in online commerce, consumer packaged goods, and gaming while online commerce and gaming benefited from marketers based in China.<!-- /quotes-0 --> We anticipate that future advertising revenue will be driven by a combination of price and the number of ads delivered.</span>',
    },
    {
        "snippet": "We anticipate making capital expenditures of approximately $27 billion to $29 billion and $30 billion to $35 billion in 2023 and 2024, respectively.",
        "original": '<div style="text-align:justify;text-indent:27pt"><span><br/></span></div><div style="text-align:justify;text-indent:27pt"><span style="color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%">We anticipate making capital expenditures of approximately $27 billion to $29&#160;billion</span><span style="background-color:#ffffff;color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%"> and </span><span style="color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%">$30 billion to $35&#160;billion</span><span style="background-color:#ffffff;color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%"> in 2023 and 2024, respectively.</span></div>',
        "expected_exists_verdict": True,
        "expected_highlighted": '<div style="text-align:justify;text-indent:27pt"><span><br/></span></div><div style="text-align:justify;text-indent:27pt"><span style="color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%"><!-- quotes-0 -->We anticipate making capital expenditures of approximately $27 billion to $29&#160;billion</span><span style="background-color:#ffffff;color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%"> and </span><span style="color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%">$30 billion to $35&#160;billion</span><span style="background-color:#ffffff;color:#000000;font-family:\'Times New Roman\',sans-serif;font-size:10pt;font-weight:400;line-height:120%"> in 2023 and 2024, respectively<!-- /quotes-0 -->.</span></div>',
    },
    # Plain text examples (earnings calls)
    {
        "snippet": "On a user geography basis, ad revenue growth was strongest in rest of world and Europe at 36% and 35%, respectively, followed by Asia-Pacific at 19% and North America at 17%.",
        "original": "Online commerce and gaming benefited from strong spend among advertisers in China reaching customers in other markets. On a user geography basis, ad revenue growth was strongest in Rest of World and Europe at 36% and 35%, respectively, followed by Asia Pacific at 19% and North America at 17%. Foreign currency was a tailwind to advertising revenue growth in all international regions. In Q3, the total number of ad impressions served across our services increased 31% and the average price per ad decreased 6%. Impression growth was mainly driven by Asia-Pacific and Rest of World.",
        "expected_exists_verdict": True,
        "expected_highlighted": "Online commerce and gaming benefited from strong spend among advertisers in China reaching customers in other markets. <!-- quotes-0 -->On a user geography basis, ad revenue growth was strongest in Rest of World and Europe at 36% and 35%, respectively, followed by Asia Pacific at 19% and North America at 17%<!-- /quotes-0 -->. Foreign currency was a tailwind to advertising revenue growth in all international regions. In Q3, the total number of ad impressions served across our services increased 31% and the average price per ad decreased 6%. Impression growth was mainly driven by Asia-Pacific and Rest of World.",
    },
    {
        "snippet": "To give you a little bit of color, North America we saw accelerate by 7 points due primarily to strong demand from China advertisers",
        "original": "themes in our ad revenue, which as you saw this quarter accelerated across all regions. To give you a little bit of color, in North America, we saw accelerate by 7 points due primarily to strong demand from China advertisers. Note that North America didn't experience the same currency tailwinds that drove acceleration in year-over-year growth for the other regions. In the EU or in our EU region, we saw that accelerate 21 points.",
        "expected_exists_verdict": True,
        "expected_highlighted": "themes in our ad revenue, which as you saw this quarter accelerated across all regions. <!-- quotes-0 -->To give you a little bit of color, in North America, we saw accelerate by 7 points due primarily to strong demand from China advertisers.<!-- /quotes-0 --> Note that North America didn't experience the same currency tailwinds that drove acceleration in year-over-year growth for the other regions. In the EU or in our EU region, we saw that accelerate 21 points.",
    },
    {
        "snippet": "We anticipate that our full year 2023 total expenses will be in the range of $87 billion to $89 billion, lowered from our prior range of $88 billion to $91 billion.",
        "original": "Our guidance assumes a foreign currency tailwind of approximately 2% to year-over-year total revenue growth in the fourth quarter, based on current exchange rates. Turning now to the expense outlook. We anticipate that our full-year 2023 total expenses will be in the range of $87 billion to $89 billion, lowered from our prior range of $88 billion to $91 billion. This outlook includes approximately $3.5 billion of restructuring costs related to facilities consolidation charges and severance and other personnel costs. We expect Reality Labs operating losses to increase year-over-year in 2023.",
        "expected_exists_verdict": True,
        "expected_highlighted": "Our guidance assumes a foreign currency tailwind of approximately 2% to year-over-year total revenue growth in the fourth quarter, based on current exchange rates. Turning now to the expense outlook. <!-- quotes-0 -->We anticipate that our full-year 2023 total expenses will be in the range of $87 billion to $89 billion, lowered from our prior range of $88 billion to $91 billion.<!-- /quotes-0 --> This outlook includes approximately $3.5 billion of restructuring costs related to facilities consolidation charges and severance and other personnel costs. We expect Reality Labs operating losses to increase year-over-year in 2023.",
    },
    {
        "snippet": "Impression growth was mainly driven by Asia-Pacific and rest of world. The year-over-year decline in pricing was driven by strong impression growth, especially from lower monetizing services and regions.",
        "original": "In Q3, the total number of ad impressions served across our services increased 31% and the average price per ad decreased 6%. Impression growth was mainly driven by Asia-Pacific and Rest of World. The year-over-year decline in pricing was driven by strong impression growth, especially from lower monetizing surfaces and regions. While overall pricing remains under pressure from these factors, we believe our ongoing improvements to ad targeting and measurement are continuing to drive improved results for advertisers. ",
        "expected_exists_verdict": True,
        "expected_highlighted": "In Q3, the total number of ad impressions served across our services increased 31% and the average price per ad decreased 6%. <!-- quotes-0 -->Impression growth was mainly driven by Asia-Pacific and Rest of World.<!-- /quotes-0 --> <!-- quotes-1 -->The year-over-year decline in pricing was driven by strong impression growth, especially from lower monetizing surfaces and regions.<!-- /quotes-1 --> While overall pricing remains under pressure from these factors, we believe our ongoing improvements to ad targeting and measurement are continuing to drive improved results for advertisers. ",
    },
    # Multi-line examples for which we need to break up the snippet by sentence
    {
        "snippet": "Mark Zuckerberg : Yeah, I can start off by taking the generative AI question. Although there aren't that many details that I'm going to share at this point. More of this will come in focus as we start shipping more of these things over the coming months. So we have always strived to just have an advertiser just be able to tell us what their objective is and then have us to be able to do as much of the work as possible for them and now being able to do more of the creative work there and ourselves for those who want that I think, could be a very exciting opportunity. I also think that there is going to be a very interesting convergence between some of the AI agents in messaging and business messaging, where right now, we see a lot of the places where business messaging is most successful are places where a lot of businesses can afford to basically have people answering a lot of questions for people and engaging with them in chat. So I think that, that could be a pretty big opportunity, too. So those are just a few of the things that we're looking at. I think this is very broad. Like I said, I think this is literally going to touch every single one of our products and services in multiple ways. So this is just a very big wave and new set of technologies that's available, and we're working on.",
        "original": "Mark Zuckerberg : Yeah, I can start off by taking the generative AI question. Although there aren't that many details that I'm going to share at this point, more of this will come in focus as we start shipping more of these things over the coming months.  But I do think that there's a big opportunity here. You asked specifically about advertisers, but I think it's going to also help create more engaging experiences, which should create more engagement, and that, by itself, creates more opportunities for advertisers. But then I think that there's a bunch of opportunities on the visual side to help advertisers create different creative. We don't have the tools to do that over time, eventually making it.  So we've always strived to just have an advertiser just be able to tell us what their objective is and then have us to be able to do as much of the work as possible for them and now being able to do more of the creative work there and ourselves for those who want that, I think, could be a very exciting opportunity. I also think that there's going to be a very interesting convergence between some of the AI agents in messaging and business messaging, where right now, we see a lot of the places where business messaging is most successful are places where a lot of businesses can afford to basically have people answering a lot of questions for people and engaging with them in chat. And obviously, once you light up the ability for tens of millions of small businesses to have AI agents acting on their behalf, you'll have way more businesses that can afford to have someone engaging in chat with customers. So I think that, that could be a pretty big opportunity, too. So those are just a few of the things that we're looking at. I think this is very broad. Like I said, I think this is literally going to touch every single one of our products and services in multiple ways. So -- and this is just a very big wave and new set of technologies that's available, and we're working on it across the whole company.",
        "expected_exists_verdict": True,
        "expected_highlighted": "<!-- quotes-0 -->Mark Zuckerberg : Yeah, I can start off by taking the generative AI question.<!-- /quotes-0 --> <!-- quotes-1 -->Although there aren't that many details that I'm going to share at this point<!-- /quotes-1 -->, <!-- quotes-2 -->more of this will come in focus as we start shipping more of these things over the coming months.<!-- /quotes-2 -->  But I do think that there's a big opportunity here. You asked specifically about advertisers, but I think it's going to also help create more engaging experiences, which should create more engagement, and that, by itself, creates more opportunities for advertisers. But then I think that there's a bunch of opportunities on the visual side to help advertisers create different creative. We don't have the tools to do that over time, eventually making it.  <!-- quotes-3 -->So we've always strived to just have an advertiser just be able to tell us what their objective is and then have us to be able to do as much of the work as possible for them and now being able to do more of the creative work there and ourselves for those who want that, I think, could be a very exciting opportunity.<!-- /quotes-3 --> <!-- quotes-4 -->I also think that there's going to be a very interesting convergence between some of the AI agents in messaging and business messaging, where right now, we see a lot of the places where business messaging is most successful are places where a lot of businesses can afford to basically have people answering a lot of questions for people and engaging with them in chat.<!-- /quotes-4 --> And obviously, once you light up the ability for tens of millions of small businesses to have AI agents acting on their behalf, you'll have way more businesses that can afford to have someone engaging in chat with customers. <!-- quotes-5 -->So I think that, that could be a pretty big opportunity, too.<!-- /quotes-5 --> <!-- quotes-6 -->So those are just a few of the things that we're looking at.<!-- /quotes-6 --> <!-- quotes-7 -->I think this is very broad.<!-- /quotes-7 --> <!-- quotes-8 -->Like I said, I think this is literally going to touch every single one of our products and services in multiple ways.<!-- /quotes-8 --> <!-- quotes-9 -->So -- and this is just a very big wave and new set of technologies that's available, and we're working on<!-- /quotes-9 --> it across the whole company.",
    },
    {
        "snippet": "Business messaging also continues to grow across our services and I believe will be the next major pillar of our business. There are more than 600 million conversations between people and businesses every day on our platforms. A revenue from click to message ads in India has doubled year-over-year. Now I think that this is going to be a really big opportunity for our new business AIs that I talked about earlier that we hope will enable any business to easily set up an AI that people can message to help with commerce and support. And in those countries like Thailand or Vietnam, there's a huge amount of commerce that happens in this way. But in lots of parts of the world, the cost of labor is too expensive for this to be viable. But with business AIs, we have the opportunity to bring down that cost and expand commerce and messaging into larger economies across the world. So making business AIs work for more businesses is going to be an important focus for us into 2024.",
        "original": "Business messaging also continues to grow across our services and I believe will be the next major pillar of our business. There are more than 600 million conversations between people and businesses every day on our platforms. To give you a sense of what this could look like when it's scaled globally, every week now, more than 60% of people on WhatsApp in India message a business app account. A revenue from click to message ads in India has doubled year-over-year. Now I think that this is going to be a really big opportunity for new business AIs that I talked about earlier that we hope will enable any business to easily set up an AI that people can message to help with commerce and support. Today, most commerce and messaging is in countries where the cost of labor is low enough that it makes sense for businesses to have people corresponding with customers over text. And in those countries like Thailand or Vietnam, there's a huge amount of commerce that happens in this way. But in lots of parts of the world, the cost of labor is too expensive for this to be viable. But with business AIs, we have the opportunity to bring down that cost and expand commerce and messaging into larger economies across the world. ",
        "expected_exists_verdict": True,
        "expected_highlighted": "<!-- quotes-0 -->Business messaging also continues to grow across our services and I believe will be the next major pillar of our business.<!-- /quotes-0 --> <!-- quotes-1 -->There are more than 600 million conversations between people and businesses every day on our platforms.<!-- /quotes-1 --> To give you a sense of what this could look like when it's scaled globally, every week now, more than 60% of people on WhatsApp in India message <!-- quotes-2 -->a business app account. A revenue from click to message ads in India has doubled year-over-year.<!-- /quotes-2 --> <!-- quotes-3 -->Now I think that this is going to be a really big opportunity for new business AIs that I talked about earlier that we hope will enable any business to easily set up an AI that people can message to help with commerce and support.<!-- /quotes-3 --> Today, most commerce and messaging is in countries where the cost of labor is low enough that it makes sense for businesses to have people corresponding with customers over text. <!-- quotes-4 -->And in those countries like Thailand or Vietnam, there's a huge amount of commerce that happens in this way.<!-- /quotes-4 --> <!-- quotes-5 -->But in lots of parts of the world, the cost of labor is too expensive for this to be viable.<!-- /quotes-5 --> <!-- quotes-6 -->But with business AIs, we have the opportunity to bring down that cost and expand commerce and messaging into larger economies across the world<!-- /quotes-6 -->. ",
    },
    # multi-matches html - sec filing numbers
    {
        # http://test1.onwish.ai/admin/agent/citation/181/change/
        "snippet": "Revenue $ 29,077 $ 18,687 56%",
        "original": load_url(
            # "https://www.sec.gov/Archives/edgar/data/1326801/000132680121000049/fb-20210630.htm"
            f"{cwd}/https%3A%2F%2Fwww.sec.gov%2FArchives%2Fedgar%2Fdata%2F1326801%2F000132680121000049%2Ffb-20210630.htm.zip"
        ),
        "expected_exists_verdict": True,
        "expected_highlight_span": [(1429768, 1431880)],
    },
    {
        # http://test1.onwish.ai/admin/agent/citation/168/change/
        "snippet": "Revenue $ 27,908 $ 26,171 7%",
        "original": load_url(
            # "https://www.sec.gov/Archives/edgar/data/1326801/000132680122000057/meta-20220331.htm"
            f"{cwd}/https%3A%2F%2Fwww.sec.gov%2FArchives%2Fedgar%2Fdata%2F1326801%2F000132680122000057%2Fmeta-20220331.htm.zip"
        ),
        "expected_exists_verdict": True,
        "expected_highlight_span": [(1340297, 1342408)],
    },
    {
        # http://test1.onwish.ai/admin/agent/citation/167/change/
        "snippet": "Revenue $ 28,822 $ 29,077 (1)%",
        "original": load_url(
            # "https://www.sec.gov/Archives/edgar/data/1326801/000132680122000082/meta-20220630.htm"
            f"{cwd}/https%3A%2F%2Fwww.sec.gov%2FArchives%2Fedgar%2Fdata%2F1326801%2F000132680122000082%2Fmeta-20220630.htm.zip"
        ),
        "expected_exists_verdict": True,
        "expected_highlight_span": [(1714954, 1717061)],
    },
    # highlights combined.
    {
        "snippet": "expect the IDPC to issue a decision in May in its previously disclosed inquiry relating to transatlantic data transfers of Facebook EU EEA user data, including a suspension order for such transfers and a fine. Our ongoing consultations with policymakers on both sides of the Atlantic continue to indicate that the proposed new EU-U.S. data privacy framework will be fully implemented before the deadline for suspension of such transfers, but we cannot exclude the possibility that it will not be completed in time.",
        "original": "Susan Li : Thanks, Mark, and good afternoon, everyone. Let's begin with our consolidated results. All comparisons are on a year-over-year basis unless otherwise noted. Q1 total revenue was $28.6 billion, up 3% or 6% on a constant currency basis. Had foreign exchange rates remained constant with Q1 of last year, total revenue would have been about $816 million higher.  Q1 total expenses were $21.4 billion, up 10% compared to last year. In terms of the specific line items, cost of revenue increased 2%, driven by infrastructure-related costs that were partially offset by lower Reality Labs cost of goods sold. R&D increased 22%, driven primarily by restructuring charges related to facilities consolidation and severance expenses and headcount-related costs from our Reality Labs and Family of Apps segments.  Marketing and sales decreased 8% due mainly to lower marketing spend and headcount-related expenses. And G&A increased 22% due primarily to restructuring charges related to severance and facilities consolidation expenses and legal-related expenses. We ended the first quarter with over 77,100 employees, down 11% from the fourth quarter as our reported headcount no longer includes substantially all of the employees impacted by the November layoffs. Employees that were impacted by layoffs in March are still included in the first quarter headcount.  First quarter operating income was $7.2 billion, representing a 25% operating margin. Our tax rate for the quarter was 22%. Net income was $5.7 billion or $2.20 per share. Capital expenditures, including principal payments on finance leases, were $7.1 billion driven by investments in data centers, servers and network infrastructure. Free cash flow was $6.9 billion, and we repurchased $9.2 billion of our Class A common stock in the first quarter. We ended the quarter with $37.4 billion in cash and marketable securities. Moving now to our segment results. I'll begin with our Family of Apps segment. Our community across the Family of Apps continues to grow. For the first time, we surpassed 3 billion people using at least one of our Family of Apps on a daily basis in March, and approximately 3.8 billion people use at least one on a monthly basis.  Facebook continues to grow globally as well, and engagement remains strong with DAU and MAU growing sequentially across all regions in the first quarter. Facebook daily active users were 2.04 billion, up 4% or 77 million compared to last year. DAUs represented approximately 68% of the 2.99 billion monthly active users in March. MAUs grew by 53 million or 2% compared to last year.  Q1 total Family of Apps revenue was $28.3 billion, up 4% year-over-year, and Q1 Family of Apps ad revenue was $28.1 billion, up 4% or 7% on a constant currency basis. Within ad revenue, the online commerce vertical was the largest contributor to year-over-year growth followed by health care and entertainment and media. Online commerce benefited from strong spend among advertisers in China reaching customers in other markets. However, other verticals remain challenged with financial services and technology verticals being the largest negative contributors to year-over-year growth.  On a user geography basis, ad revenue growth was strongest in Rest of World at 9%, followed by North America and Asia Pacific at 6% and 4%, respectively. Europe declined 1%. Foreign currency remained a headwind to advertising revenue growth in all international regions. In Q1, the total number of ad impressions served across our services increased 26% and the average price per ad decreased 17%.  Impression growth was primarily driven by Asia Pacific and Rest of World. The year-over-year decline in pricing was primarily driven by strong impression growth, especially from lower monetizing services and regions, foreign currency depreciation and lower advertising demand. While overall pricing remains under pressure from these factors, we believe our ongoing improvements to ad targeting and measurement are continuing to drive improved results for advertisers. Family of Apps other revenue was $205 million in Q1, down 5%, as strong business messaging revenue growth from our WhatsApp business platform was more than offset by a decline in other line items. We continue to direct the majority of our investments toward the development and operation of our Family of Apps. In Q1, Family of Apps expenses were $17.1 billion representing approximately 80% of our overall expenses. Family of Apps expenses were up 9% due primarily to restructuring charges and growth in infrastructure-related costs. Family of Apps operating income was $11.2 billion, representing a 40% operating margin. Within our Reality Labs segment, Q1 revenue was $339 million, down 51% due to lower Quest 2 sales. Reality Labs expenses were $4.3 billion, up 18% due mostly to employee-related costs and restructuring charges. Reality Labs operating loss was $4 billion. Next, I'll discuss our ongoing monetization work. As I mentioned last quarter, there are two primary levers to increasing monetization, growing supply and demand. On the ad supply side, our foremost focus remains on building engaging experiences for our community, and we're continuing to make encouraging progress on our product priorities. Our investments in the discovery engine are delivering results.  In Feed, recommendations are contributing to engagement, and we've seen real time become more incremental to overall engagement on our services as we continue to improve our recommendation system. This is an important signal because it demonstrates that people are finding added value from the content we're helping them discover.  Beyond that, we continue to focus on further narrowing the gap in monetization efficiency or monetization per time between Reels and our more mature surfaces, Feed and Stories. There are structural supply constraints with the Reels format as people view a reel for a longer time than a piece of Feed or Stories content, which results in fewer opportunities to serve ads in between posts. That will make it likely more challenging to close the monetization efficiency gap than it was with Stories.  However, the overall economics of Reels will be determined by a combination of our ability to continue growing monetization per time on Reels and our ability to drive incremental engagement from Reels. As Mark noted, we have seen a 24% increase in overall time spent on Instagram from our ranking improvements since launching Reels globally. We continue to expect that Reels will become neutral to overall revenue by end of this year or early next year. The other side of monetization is growing advertiser demand. One area of focus is around driving advertiser performance, and we are seeing continued strong conversion growth for advertisers, which we believe, coupled with lower cost per action, is driving higher return on investment.  We remain focused on continuing to improve ads ranking and measurement with our ongoing AI investments while also leveraging AI to power increased automation for advertisers through products like Advantage+ shopping, which continues to gain adoption and receive positive feedback from advertisers. These investments will help us develop and deploy privacy-enhancing technologies and build new innovative tools that make it easier for businesses to not only find the right audience for their ad, but also optimize and eventually develop their ad creative. Scaling on-site conversions is another important part of our work, and click-to-message ads continue to grow and bring incremental demand onto our platform. This format is mostly used by smaller advertisers today in Southeast Asia and Latin America, and one of the exciting opportunities ahead is to expand adoption to larger advertisers in more markets by investing in increased automation and reporting to help businesses more easily manage messages and measure results at scale. Before turning to our revenue outlook, I'd also like to talk about our operating philosophy given the recent significant changes to our investment plans. We believe increasing our organizational efficiency is vital to our long-term success. This will increase the speed of our execution and agility to ensure that we are constantly innovating for the people who use our services. Narrowing the scope of the projects that we are working on allows us to increase our focus on the highest leverage opportunities for the company, including AI today and the Metaverse longer term. It also enables us to invest in these areas while maintaining a strong financial position.  As we look forward, I also expect that we will modestly evolve our capital structure over time to improve our overall cost of capital. We expect to do so through periodically accessing the debt markets to diversify our funding sources while still maintaining a positive or neutral net cash balance over time. In addition, we continue to monitor ongoing regulatory developments. We expect the IDPC to issue a decision in May in its previously disclosed inquiry relating to transatlantic data transfers of Facebook EU EEA user data, including a suspension order for such transfers and a fine. Our ongoing consultations with policymakers on both sides of the Atlantic continue to indicate that the proposed new EU-U.S. data privacy framework will be fully implemented before the deadline for suspension of such transfers, but we cannot exclude the possibility that it will not be completed in time. We will also evaluate whether and to what extent the IDPC decision could otherwise impact our data processing operations even after a new data privacy framework is in force. Turning now to the revenue outlook. We expect second quarter 2023 total revenue to be in the range of $29.5 billion to $32 billion. Our guidance assumes foreign currency headwinds will be less than 1% to year-over-year total revenue growth in the second quarter based on current exchange rates. Turning now to the expense outlook. We anticipate our full year 2023 total expenses will be in the range of $86 million to $90 billion, updated from our prior outlook provided in March. This outlook includes $3 billion to $5 billion of restructuring costs related to facilities consolidation charges and severance and other personnel costs. We continue to expect Reality Labs operating losses to increase year-over-year in 2023. Turning now to the CapEx outlook. We expect capital expenditures to be in the range of $30 billion to $33 billion, unchanged from our prior estimate. This outlook reflects our ongoing build-out of AI capacity to support ads, Feed and Reels, along with an increased investment in capacity for our generative AI initiatives. On to tax. Absent any changes to U.S. tax law, we expect our full year 2023 tax rate percentage to be around 20%. In closing, Q1 was a solid quarter for our business. Our global community continued to grow. We made important progress on our company priorities and improved the efficiency of our operations while strengthening the financial position of the company which sets us up well to execute on the opportunities ahead.  With that, Dave, let's open up the call for questions.",
        "expected_exists_verdict": True,
        "expected_highlight_span": [(9002, 9516)],
    },
    #  Negative examples
    {
        "snippet": "There are 1 or 2 billion people who have cups today.  in the future they're all going to be smart cups.  the time that we spend on TVs and computers is going to get more immersive and looks something more like VR in the future.",
        "original": "Mark Zuckerberg: Yes, on Threads, it's maybe too early to do this kind of analysis. I mean, I'm -- on the one-hand, we've tried a bunch of standalone experiences over-time. And in general, we haven't had a lot of success with building kind of standalone apps. The biggest exception to that of course is Messenger, but that started-off its functionality inside Facebook and was spun out. So pardon me wonders if this is just a kind of classic venture capital portfolio question where you try a bunch of things and a bunch of them don't work. And then every once in a while, one hits and is a much bigger success. It could be that or it could just be that this is such an idiosyncratic case because of all the factors that are happening around Twitter or X, I guess, it's called now. So it's hard to say. I mean, but I think when something works or doesn't you can often point to the reason why you did or didn't and there I think is an interesting intellectual question of whether you could have known that our priority, but that was actually going to be the case. But -- so I'm not sure, but also rather than trying to kind of analyze that, I'd say we have a lot of work to do to really make Threats to reach its full potential, that's not a foregone conclusion yet, even though I think we're off to a great start and I'm optimistic that over-time this could be a fifth-grade app in the Family of Apps, but we've a lot of -- we've lot of basic work to do. And we have a basic playbook here, which is build an experience, it's got to be something that people like, [indiscernible] product market fit. Once you get that, it's not always retentive, so a lot of people might like an experience, but you need to kind of tune it, that way the numbers works that people who use it are continuing. We feel like we're getting to a good place on that with Threads. There's still lot of basic functionality to build. Once we feel like we're in a very good place on that, then I'm highly confident that we're going to able to pour enough gasoline on this to help it grow once we get to the point where we feel good that everyone who is using it and going to continue using it at a high-rate. And then few years, once we get to the point where it's at hundreds of millions of people, if assuming we can get there, then we'll worry about monetization. But, I mean, that's basically the playbook that I'm -- that we're focused on. And so, rather than thinking about right now, like what does this mean for other things like it that we can build. I'd say we're really just focused on taking this opportunity, which is an awesome one that we didn't expect to this -- the scale and making sure we make the most of this and execute it. But I do think it has been sort of these weird anomalies thing in the tech industry that there hasn't been an app for public discussions like this that has reached to billion people. When I look at all the different social experiences, it just seems like there should be one like this. I think there were a lot of reasons that you can point to why that might have not been the case historically, but it's awesome that we get a chance to work on this and I'm really optimistic about where we are. But it's going to be a long road ahead.\nSusan Li: And Doug, on your second question about our 2023 CapEx forecast and the impact on 2024. So I'll start with 2023. The reduced forecast that we gave for 2023, I mentioned on the call, is driven both by some cost savings, particularly on non-AI servers where we previously had some underutilized capacity and we've been identifying ways to be more efficient in the way that we allocate that capacity towards all of our various needs, as well as shifts in CapEx in 2024 that's coming from delays in data center projects and server deliveries and that'll just push that associated CapEx, which we were planning for in 2023 into 2024. We're still working on our 2024 CapEx plans. We haven't yet finalized that and we'll be working on that through the course of this year. But I mentioned that we expect that CapEx in 2024 will be higher than in 2023. We expect both data center spend to grow in 2024 as we ramp-up construction on sites with the new data center architecture that we announced late last year. And then we certainly also expect to invest more in servers in 2024 for both AI workloads to support all of the AI work that we've talked about across the core AI ranking recommendation work along with the next gen AI efforts. And then, of course, also our non-AI workloads as we refresh some of our servers and add capacity just to support continued growth across the site.",
        "expected_exists_verdict": False,
        "expected_highlighted": "Mark Zuckerberg: Yes, on Threads, it's maybe too early to do this kind of analysis. I mean, I'm -- on the one-hand, we've tried a bunch of standalone experiences over-time. And in general, we haven't had a lot of success with building kind of standalone apps. The biggest exception to that of course is Messenger, but that started-off its functionality inside Facebook and was spun out. So pardon me wonders if this is just a kind of classic venture capital portfolio question where you try a bunch of things and a bunch of them don't work. And then every once in a while, one hits and is a much bigger success. It could be that or it could just be that this is such an idiosyncratic case because of all the factors that are happening around Twitter or X, I guess, it's called now. So it's hard to say. I mean, but I think when something works or doesn't you can often point to the reason why you did or didn't and there I think is an interesting intellectual question of whether you could have known that our priority, but that was actually going to be the case. But -- so I'm not sure, but also rather than trying to kind of analyze that, I'd say we have a lot of work to do to really make Threats to reach its full potential, that's not a foregone conclusion yet, even though I think we're off to a great start and I'm optimistic that over-time this could be a fifth-grade app in the Family of Apps, but we've a lot of -- we've lot of basic work to do. And we have a basic playbook here, which is build an experience, it's got to be something that people like, [indiscernible] product market fit. Once you get that, it's not always retentive, so a lot of people might like an experience, but you need to kind of tune it, that way the numbers works that people who use it are continuing. We feel like we're getting to a good place on that with Threads. There's still lot of basic functionality to build. Once we feel like we're in a very good place on that, then I'm highly confident that we're going to able to pour enough gasoline on this to help it grow once we get to the point where we feel good that everyone who is using it and going to continue using it at a high-rate. And then few years, once we get to the point where it's at hundreds of millions of people, if assuming we can get there, then we'll worry about monetization. But, I mean, that's basically the playbook that I'm -- that we're focused on. And so, rather than thinking about right now, like what does this mean for other things like it that we can build. I'd say we're really just focused on taking this opportunity, which is an awesome one that we didn't expect to this -- the scale and making sure we make the most of this and execute it. But I do think it has been sort of these weird anomalies thing in the tech industry that there hasn't been an app for public discussions like this that has reached to billion people. When I look at all the different social experiences, it just seems like there should be one like this. I think there were a lot of reasons that you can point to why that might have not been the case historically, but it's awesome that we get a chance to work on this and I'm really optimistic about where we are. But it's going to be a long road ahead.\nSusan Li: And Doug, on your second question about our 2023 CapEx forecast and the impact on 2024. So I'll start with 2023. The reduced forecast that we gave for 2023, I mentioned on the call, is driven both by some cost savings, particularly on non-AI servers where we previously had some underutilized capacity and we've been identifying ways to be more efficient in the way that we allocate that capacity towards all of our various needs, as well as shifts in CapEx in 2024 that's coming from delays in data center projects and server deliveries and that'll just push that associated CapEx, which we were planning for in 2023 into 2024. We're still working on our 2024 CapEx plans. We haven't yet finalized that and we'll be working on that through the course of this year. But I mentioned that we expect that CapEx in 2024 will be higher than in 2023. We expect both data center spend to grow in 2024 as we ramp-up construction on sites with the new data center architecture that we announced late last year. And then we certainly also expect to invest more in servers in 2024 for both AI workloads to support all of the AI work that we've talked about across the core AI ranking recommendation work along with the next gen AI efforts. And then, of course, also our non-AI workloads as we refresh some of our servers and add capacity just to support continued growth across the site.",
    },
    {
        "snippet": "We\u2019re helping customer build resilience, anticipate business. We stay agile in visual and consistent ways. Performance Max is an automation products across bidding, creatives, targeting.",
        "original": "Philipp Schindler: Thanks, Sundar, and hello, everyone. It\u2019s great to be joining you all today. Google Services revenues of $61 billion were up 2% year-on-year, negatively affected by a sizable foreign exchange headwind. Search and other revenues grew 4% year-over-year to $40 billion, led by travel and retail, while both YouTube Ads and Network had modest year-over-year revenue declines. Other revenues were up 2% year-over-year with growth in YouTube non-advertising and hardware revenues offset by a decrease in Play revenues. Play revenues were lower due to a number of factors, including a decline in user engagement in gaming from the elevated levels seen earlier in the pandemic. Among other factors, this shift in user behavior also created downward pressure on our advertising revenues, with lower revenues from ad promo spend on YouTube, Network and Play Ads in Search and other. I\u2019ll highlight a couple of other trends that affected our ads business in Q3, and Ruth will provide more detail. On the second quarter earnings call, we noted a pullback in spend by some advertisers in YouTube and Network, and these pullbacks in spend increased in the third quarter. In Search and other, the largest factor in the deceleration in Q3 was lapping the outsized performance in 2021. In the third quarter, we did see a pullback in spend by some advertisers in certain areas and search ads. For example, in financial services, we saw a pullback in the insurance, loan, mortgage and crypto subcategories. There\u2019s no question we\u2019re operating in an uncertain environment, and that businesses big and small continue to get tested in new and different ways, depending on where they are in the world. When it comes to how we\u2019re helping, our focus remains unchanged. The same AI driving breakthroughs in everything from protein folding, flood forecasting and language understanding is also fueling innovation across our Ads product. Via insights, automation and easier-to-use advertising tools and formats, we\u2019re helping businesses stay agile, build resilience, anticipate the future and show up for customers in more connected, visual and consistent ways. We\u2019re helping them understand demand, deal with inventory challenges, increase loyalty and much more. In challenging times like these, advertisers are carefully evaluating the effectiveness of their budgets. Search tends to do relatively well in such an environment, given its strong measurability and focus on delivering ROI. It\u2019s also well suited to quickly adjust to changes in consumer behavior. And when Search is coupled with our automation products across bidding, creatives, targeting or Performance Max, it can drive performance even further. Let\u2019s talk retail, an important vertical for us. No matter where shoppers are buying, whether it\u2019s in store, online or both, we have the solutions to help them deliver results wherever their customers are. Take online fashion retailer REVOLVE, who uses global influencer network along with our category insights and automated tools to acquire new customers at lower costs. As festivals and events return to summer, REVOLVE used lower funnel solutions across search and shopping to engage with consumers in high-demand apparel categories, helping it cross $1 billion in trailing 12-month net sales for the first time. And then, there\u2019s Magazine Luiza, a large Brazilian retailer, who embraced omnichannel by measuring and bidding directly to store sales. Coupled with in-store pickup and notations via PMAX, Magazine Luiza drove a 38% ROAS over 30 days and has since expanded the strategy to all eligible products, including 8 million-plus new offers. And whether shoppers know exactly what they\u2019re looking for or are just seeking inspiration, we\u2019re innovating to make it easier and more engaging for people to shop online across our services. From finding the best deals across all types of merchants, including 200 million-plus available daily deals in Q3 to new ways to search. Now when you type shop followed by whatever item you\u2019re looking for, you\u2019ll unlock a visual content stream of ideas that feel just like window shopping but online. And then there\u2019s YouTube. Not only can users now buy more products and videos, but shopping as entertainment experiences are bringing the magic of our creators to the shopping experience. Like in September, when Kylie and Kris Jenner hosted an exclusive shopping stream event to celebrate the debut of a new Kris collection for Kylie Cosmetics. And at Adweek last week, not only did we announce that product feeds are coming to discovery, but that creators will soon be able to take products from brands across their videos, Shorts and live streams. This means viewers can shop products seamlessly while they watch their favorite content, while merchants can drive incremental reach and engagement for free listing offers when tagged by creators that viewers love and trust. I already touched on YouTube\u2019s performance in the quarter. Let\u2019s deep dive into what we\u2019re focused on. First, we\u2019re helping advertisers understand how they can drive effectiveness with every campaign they run on YouTube. Full funnel is a key way to do this. Buying YouTube across consideration, awareness and action allows marketers to meet customers at different stages in their purchasing journey, which we know are increasingly complex while delivering towards a key business goal. Creative and Media work together across the funnel to create new demand and convert current demand. Castlery, a single furniture retailer activated a full funnel approach as part of its U. S. expansion. It used brand and action formats, identified its most relevant audiences and built a robust measurement framework to boost sales and awareness, including a 65% increase in U.S. brand searches. Castlery is using the same strategy for the upcoming holiday season. Two other areas where we\u2019re continuing to invest, Connected TV and Shorts. First on CTV. Eyeballs keep moving away from traditional TV. On average, global viewers are watching 700 million-plus hours of YouTube content on TV daily. And according to Nielsen, during the 2021-2022 U.S. broadcast seasons, YouTube reached more viewers during primetime on CTV than any linear TV network. Brands continue to take notice. Like Instacart, who tapped into CTV to maximize its TV screen strategy for its The World is Your Cart brand campaign featuring Lizzo. It drove breakout searches for its product and above-average brand lift across awareness, consideration and purchase intent. And it also engaged a significant increase in audience on top of TV with 66% lower CPMs. And then there\u2019s Shorts. 1.5 billion users every month, 30 billion daily views. Engagement is strong. We\u2019ve always said that we focus on building great user and creator experiences first and then follow that with monetization over time. As of September, ads on Shorts have officially launched via video action, app and Performance Max campaigns. As Sundar mentioned, we also extended the YouTube Partner Program and announced our revenue sharing model for Shorts creators, the first of its kind for short-form content. It\u2019s early but we\u2019re encouraged by the progress we\u2019ve made this year in Shorts monetization and support for sustaining the creator ecosystem. I\u2019ll close with something I\u2019ve said before and I think it\u2019s worth reiterating. Our success is only possible when our customers and partners succeed. Whether it\u2019s helping individual YouTube creators or Play developers make a living and build thriving businesses or bringing the best across Google to our partners, our focus on driving growth for our partners and key ecosystems remain steadfast. I\u2019m proud that over the past three years, we\u2019ve paid creators, artists and media companies over $50 billion. In terms of how we\u2019re helping our partners innovate, I\u2019ll share two highlights. First, a transformative partnership with Transsion, the number one OEM in Africa and Pakistan, will help it close the digital divide by doubling annual activations by 2025 and build helpful products for the next billion users. And then in commerce with FEMSA Digital in Mexico, we\u2019re bringing together solutions across Ads, Cloud, Maps, Waze and beyond to bolster its data and analytics capabilities so it can better reach and retain its beverage and retail customers. On behalf of many, a massive thank you to our customers and partners for their collaboration, trust and feedback. And another massive thank you to our sales, partnerships, product, engineering and support teams. The hard work, dedication and ingenuity of Googlers, especially during more challenging times, is truly second to none. On that note, over to you, Ruth.",
        "expected_exists_verdict": False,
        "expected_highlighted": "Philipp Schindler: Thanks, Sundar, and hello, everyone. It\u2019s great to be joining you all today. Google Services revenues of $61 billion were up 2% year-on-year, negatively affected by a sizable foreign exchange headwind. Search and other revenues grew 4% year-over-year to $40 billion, led by travel and retail, while both YouTube Ads and Network had modest year-over-year revenue declines. Other revenues were up 2% year-over-year with growth in YouTube non-advertising and hardware revenues offset by a decrease in Play revenues. Play revenues were lower due to a number of factors, including a decline in user engagement in gaming from the elevated levels seen earlier in the pandemic. Among other factors, this shift in user behavior also created downward pressure on our advertising revenues, with lower revenues from ad promo spend on YouTube, Network and Play Ads in Search and other. I\u2019ll highlight a couple of other trends that affected our ads business in Q3, and Ruth will provide more detail. On the second quarter earnings call, we noted a pullback in spend by some advertisers in YouTube and Network, and these pullbacks in spend increased in the third quarter. In Search and other, the largest factor in the deceleration in Q3 was lapping the outsized performance in 2021. In the third quarter, we did see a pullback in spend by some advertisers in certain areas and search ads. For example, in financial services, we saw a pullback in the insurance, loan, mortgage and crypto subcategories. There\u2019s no question we\u2019re operating in an uncertain environment, and that businesses big and small continue to get tested in new and different ways, depending on where they are in the world. When it comes to how we\u2019re helping, our focus remains unchanged. The same AI driving breakthroughs in everything from protein folding, flood forecasting and language understanding is also fueling innovation across our Ads product. Via insights, automation and easier-to-use advertising tools and formats, we\u2019re helping businesses stay agile, build resilience, anticipate the future and show up for customers in more connected, visual and consistent ways. We\u2019re helping them understand demand, deal with inventory challenges, increase loyalty and much more. In challenging times like these, advertisers are carefully evaluating the effectiveness of their budgets. Search tends to do relatively well in such an environment, given its strong measurability and focus on delivering ROI. It\u2019s also well suited to quickly adjust to changes in consumer behavior. And when Search is coupled with our automation products across bidding, creatives, targeting or Performance Max, it can drive performance even further. Let\u2019s talk retail, an important vertical for us. No matter where shoppers are buying, whether it\u2019s in store, online or both, we have the solutions to help them deliver results wherever their customers are. Take online fashion retailer REVOLVE, who uses global influencer network along with our category insights and automated tools to acquire new customers at lower costs. As festivals and events return to summer, REVOLVE used lower funnel solutions across search and shopping to engage with consumers in high-demand apparel categories, helping it cross $1 billion in trailing 12-month net sales for the first time. And then, there\u2019s Magazine Luiza, a large Brazilian retailer, who embraced omnichannel by measuring and bidding directly to store sales. Coupled with in-store pickup and notations via PMAX, Magazine Luiza drove a 38% ROAS over 30 days and has since expanded the strategy to all eligible products, including 8 million-plus new offers. And whether shoppers know exactly what they\u2019re looking for or are just seeking inspiration, we\u2019re innovating to make it easier and more engaging for people to shop online across our services. From finding the best deals across all types of merchants, including 200 million-plus available daily deals in Q3 to new ways to search. Now when you type shop followed by whatever item you\u2019re looking for, you\u2019ll unlock a visual content stream of ideas that feel just like window shopping but online. And then there\u2019s YouTube. Not only can users now buy more products and videos, but shopping as entertainment experiences are bringing the magic of our creators to the shopping experience. Like in September, when Kylie and Kris Jenner hosted an exclusive shopping stream event to celebrate the debut of a new Kris collection for Kylie Cosmetics. And at Adweek last week, not only did we announce that product feeds are coming to discovery, but that creators will soon be able to take products from brands across their videos, Shorts and live streams. This means viewers can shop products seamlessly while they watch their favorite content, while merchants can drive incremental reach and engagement for free listing offers when tagged by creators that viewers love and trust. I already touched on YouTube\u2019s performance in the quarter. Let\u2019s deep dive into what we\u2019re focused on. First, we\u2019re helping advertisers understand how they can drive effectiveness with every campaign they run on YouTube. Full funnel is a key way to do this. Buying YouTube across consideration, awareness and action allows marketers to meet customers at different stages in their purchasing journey, which we know are increasingly complex while delivering towards a key business goal. Creative and Media work together across the funnel to create new demand and convert current demand. Castlery, a single furniture retailer activated a full funnel approach as part of its U. S. expansion. It used brand and action formats, identified its most relevant audiences and built a robust measurement framework to boost sales and awareness, including a 65% increase in U.S. brand searches. Castlery is using the same strategy for the upcoming holiday season. Two other areas where we\u2019re continuing to invest, Connected TV and Shorts. First on CTV. Eyeballs keep moving away from traditional TV. On average, global viewers are watching 700 million-plus hours of YouTube content on TV daily. And according to Nielsen, during the 2021-2022 U.S. broadcast seasons, YouTube reached more viewers during primetime on CTV than any linear TV network. Brands continue to take notice. Like Instacart, who tapped into CTV to maximize its TV screen strategy for its The World is Your Cart brand campaign featuring Lizzo. It drove breakout searches for its product and above-average brand lift across awareness, consideration and purchase intent. And it also engaged a significant increase in audience on top of TV with 66% lower CPMs. And then there\u2019s Shorts. 1.5 billion users every month, 30 billion daily views. Engagement is strong. We\u2019ve always said that we focus on building great user and creator experiences first and then follow that with monetization over time. As of September, ads on Shorts have officially launched via video action, app and Performance Max campaigns. As Sundar mentioned, we also extended the YouTube Partner Program and announced our revenue sharing model for Shorts creators, the first of its kind for short-form content. It\u2019s early but we\u2019re encouraged by the progress we\u2019ve made this year in Shorts monetization and support for sustaining the creator ecosystem. I\u2019ll close with something I\u2019ve said before and I think it\u2019s worth reiterating. Our success is only possible when our customers and partners succeed. Whether it\u2019s helping individual YouTube creators or Play developers make a living and build thriving businesses or bringing the best across Google to our partners, our focus on driving growth for our partners and key ecosystems remain steadfast. I\u2019m proud that over the past three years, we\u2019ve paid creators, artists and media companies over $50 billion. In terms of how we\u2019re helping our partners innovate, I\u2019ll share two highlights. First, a transformative partnership with Transsion, the number one OEM in Africa and Pakistan, will help it close the digital divide by doubling annual activations by 2025 and build helpful products for the next billion users. And then in commerce with FEMSA Digital in Mexico, we\u2019re bringing together solutions across Ads, Cloud, Maps, Waze and beyond to bolster its data and analytics capabilities so it can better reach and retain its beverage and retail customers. On behalf of many, a massive thank you to our customers and partners for their collaboration, trust and feedback. And another massive thank you to our sales, partnerships, product, engineering and support teams. The hard work, dedication and ingenuity of Googlers, especially during more challenging times, is truly second to none. On that note, over to you, Ruth.",
    },
    {
        "snippet": "these first 2 layers enabling access to large language model I've talked about, what doing to generative AI is model of choice instead. and  lowering the cost of training and running models,",
        "original": "We think of large language models in generative AI as having 3 key layers, all of which are very large in our opinion and all of which AWS is investing heavily in. At the lowest layer is the compute required to train foundational models and do inference or make predictions. Customers are excited by Amazon EC2 P5 instances powered by NVIDIA H100 GPUs to train large models and develop generative AI applications. However, to date, there's only been one viable option in the market for everybody and supply has been scarce. That, along with the chip expertise we've built over the last several years, prompted us to start working several years ago on our own custom AI chips for training called Trainium and inference called Inferentia that are on their second versions already and are a very appealing price performance option for customers building and running large language models. We're optimistic that a lot of large language model training and inference will be run on AWS' Trainium and Inferentia chips in the future. We think of the middle layer as being large language models as a service. Stepping back for a second, to develop these large language models, it takes billions of dollars and multiple years to develop. Most companies tell us that they don't want to consume that resource building themselves. Rather, they want access to those large language models, want to customize them with their own data without leaking their proprietary data into the general model, have all the security, privacy and platform features in AWS work with this new enhanced model and then have it all wrapped in a managed service. This is what our service Bedrock does and offers customers all of these aforementioned capabilities with not just one large language model but with access to models from multiple leading large language model companies like Anthropic, Stability AI, AI21 Labs, Cohere and Amazon's own developed large language models called Titan. Customers, including Bridgewater Associates, Coda, Lonely Planet, Omnicom, 3M, Ryanair, Showpad and Travelers are using Amazon Bedrock to create generative AI application. And we just recently announced new capabilities from Bedrock, including new models from Cohere, Anthropic's Claude 2 and Stability AI's Stable Diffusion XL 1.0 as well as agents for Amazon Bedrock that allow customers to create conversational agents to deliver personalized up-to-date answers based on their proprietary data and to execute actions. If you think about these first 2 layers I've talked about, what we're doing is democratizing access to generative AI, lowering the cost of training and running models, enabling access to large language model of choice instead of there only being one option, making it simpler for companies of all sizes and technical acumen to customize their own large language model and build generative AI applications in a secure and enterprise-grade fashion, these are all part of making generative AI accessible to everybody and very much what AWS has been doing for technology infrastructure over the last 17 years. Then that top layer is where a lot of the publicity and attention have focused, and these are the actual applications that run on top of these large language models.",
        "expected_exists_verdict": False,
        "expected_highlighted": "We think of large language models in generative AI as having 3 key layers, all of which are very large in our opinion and all of which AWS is investing heavily in. At the lowest layer is the compute required to train foundational models and do inference or make predictions. Customers are excited by Amazon EC2 P5 instances powered by NVIDIA H100 GPUs to train large models and develop generative AI applications. However, to date, there's only been one viable option in the market for everybody and supply has been scarce. That, along with the chip expertise we've built over the last several years, prompted us to start working several years ago on our own custom AI chips for training called Trainium and inference called Inferentia that are on their second versions already and are a very appealing price performance option for customers building and running large language models. We're optimistic that a lot of large language model training and inference will be run on AWS' Trainium and Inferentia chips in the future. We think of the middle layer as being large language models as a service. Stepping back for a second, to develop these large language models, it takes billions of dollars and multiple years to develop. Most companies tell us that they don't want to consume that resource building themselves. Rather, they want access to those large language models, want to customize them with their own data without leaking their proprietary data into the general model, have all the security, privacy and platform features in AWS work with this new enhanced model and then have it all wrapped in a managed service. This is what our service Bedrock does and offers customers all of these aforementioned capabilities with not just one large language model but with access to models from multiple leading large language model companies like Anthropic, Stability AI, AI21 Labs, Cohere and Amazon's own developed large language models called Titan. Customers, including Bridgewater Associates, Coda, Lonely Planet, Omnicom, 3M, Ryanair, Showpad and Travelers are using Amazon Bedrock to create generative AI application. And we just recently announced new capabilities from Bedrock, including new models from Cohere, Anthropic's Claude 2 and Stability AI's Stable Diffusion XL 1.0 as well as agents for Amazon Bedrock that allow customers to create conversational agents to deliver personalized up-to-date answers based on their proprietary data and to execute actions. If you think about these first 2 layers I've talked about, what we're doing is democratizing access to generative AI, lowering the cost of training and running models, enabling access to large language model of choice instead of there only being one option, making it simpler for companies of all sizes and technical acumen to customize their own large language model and build generative AI applications in a secure and enterprise-grade fashion, these are all part of making generative AI accessible to everybody and very much what AWS has been doing for technology infrastructure over the last 17 years. Then that top layer is where a lot of the publicity and attention have focused, and these are the actual applications that run on top of these large language models.",
    },
    {
        "snippet": "Headcount was 14% decrease to 71,469 as of June 30, 2023",
        "original": "<span style=\"color:#000000;font-family:'Times New Roman',sans-serif;font-size:10pt;font-weight:400;line-height:120%;padding-left:14.5pt\">Headcount was 71,469 as of June&#160;30, 2023, a decrease of 14% year-over-year. Approximately half of the employees impacted by the 2023 layoffs are included in our reported headcount as of </span>",
        "expected_exists_verdict": False,
        "expected_highlighted": "<span style=\"color:#000000;font-family:'Times New Roman',sans-serif;font-size:10pt;font-weight:400;line-height:120%;padding-left:14.5pt\">Headcount was 71,469 as of June&#160;30, 2023, a decrease of 14% year-over-year. Approximately half of the employees impacted by the 2023 layoffs are included in our reported headcount as of </span>",
    },
]


def test_searcher_check_exists() -> None:
    searcher = SnippetSearcher(is_mentioned_threshold=0.8)
    for data in SEARCHER_TEST_DATA:
        assert (
            searcher.is_mentioned(data["snippet"], data["original"])  # type: ignore
            == data["expected_exists_verdict"]
        )


def test_searcher_highlight() -> None:
    searcher = SnippetSearcher(is_mentioned_threshold=0.8)
    for data in SEARCHER_TEST_DATA:
        if "expected_highlight_span" in data:
            _, span = searcher.simple_search(data["snippet"], data["original"])  # type: ignore
            assert span == data["expected_highlight_span"]
        else:
            hl = searcher.highlight(data["snippet"], data["original"])  # type: ignore
            assert hl == data["expected_highlighted"]


MATCHER_TEST_DATA = [
    {
        "s1": "Headcount was 66185 as of September 30, 2023, A decrease of 24% year over year.",
        "s2": "Headcount was 66,185 as of September 30, 2023, a decrease of 24% year-over-year. A substantial majority of the employees impacted by the layoffs are no longer included in our reported headcount as of September 30, 2023.",
        "expected_result": [
            (
                "Headcount was 66185 as of September 30, 2023, A decrease of 24% year over year.",
                "Headcount was 66,185 as of September 30, 2023, a decrease of 24% year-over-year.",
            )
        ],
    },
    {
        "s1": "... In March 2023, we announced three rounds of planned layoffs to further reduce our company size by approximately 10,000 employees across the Family of Apps (FoA) and Reality Labs (RL) segments. ...",
        "s2": "We anticipate our full year 2024 capital expenditures will be in the range of $30 billion to $35 billion, with growth driven by investments in servers, including both non-AI and AI hardware, and in data centers as we ramp-up construction on sites with the new data center architecture we announced late last year.",
        "expected_result": [],
    },
    {
        "s1": "... YouTube Shorts are now watched by over 2 billion logged-in users every month ...",
        "s2": "YouTube Shorts are now watched by over 2 billion logged in users every month, up from 1.5 billion just one year ago.",
        "expected_result": [
            (
                "... YouTube Shorts are now watched by over 2 billion logged-in users every month ...",
                "YouTube Shorts are now watched by over 2 billion logged in users every month, up from 1.5 billion just one year ago.",
            )
        ],
    },
    # TODO: solve below case that two snippets are very similar but different on critical key words
    # currently it incorrectly matches them
    # {
    #     "s1": "... Headcount was 71,469 as of June 30, 2023, a decrease of 14% year-over-year. ...",
    #     "s2": "Headcount was 66,185 as of September 30, 2023, a decrease of 24% year-over-year. A substantial majority of the employees impacted by the layoffs are no longer included in our reported headcount as of September 30, 2023.",
    #     "expected_result": [],
    # },
]


def test_matcher() -> None:
    matcher = SnippetMatcher(overlap_threshold=0.9)
    for entry in MATCHER_TEST_DATA:
        matched = matcher.find_matching_parts(s1=entry["s1"], s2=entry["s2"])  # type: ignore
        assert set(matched) == set(entry["expected_result"])  # type: ignore
