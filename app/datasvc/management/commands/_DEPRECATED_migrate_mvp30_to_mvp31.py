"""migrate from mvp30 data to mvp31 data
"""

import argparse
from typing import Any

from django.conf import settings
from django.core.management.base import BaseCommand

from common.utils.terminal import info, tip
from datasvc import conf as ds_conf
from datasvc.indexing.pinecone import PINECONE_SELECTIVE_META_FIELDS_SET


class Command(BaseCommand):
    def add_arguments(self, parser: argparse.ArgumentParser) -> None:
        pass

    def handle(self, *args: Any, **kwargs: Any) -> None:
        self._check()

    def _backup(self) -> None:
        pass

    def _check(self) -> bool:
        # ensure env
        pinecone_indexes = ds_conf.PINECONE_INDEX_NAMES
        pinecone_api_key: str = ds_conf.PINECONE_API_KEY
        pinecone_ns: str = ds_conf.PINECONE_NAMESPACE
        s3_bucket: str = ds_conf.S3_DOC_BUCKET

        db_default = settings.DATABASES.get("default")
        db_datasvc = settings.DATABASES.get("datasvc")

        info(
            "=====   Detected configurations   ====="
            "\n"
            f"\n  S3 Bucket   - {s3_bucket}"
            "\n"
            "\n-----   Target pinecone   -----"
            f"\n  Pinecone API Key     - {pinecone_api_key[:2]}******{pinecone_api_key[-2:]}"
            f"\n  Pinecone Namespace   - {pinecone_ns}"
            # f"\n  Pinecone Environment - {pinecone_env}"
            f"\n  Env for Index Names  "
        )
        for k, v in pinecone_indexes.items():
            info(f"                       - PINECONE_INDEX_NAME.{k} = {v}")
        info(
            f"  Fields can be filtered: {sorted(list(PINECONE_SELECTIVE_META_FIELDS_SET))}"
        )
        info(
            "\n-----   TARGET database   -----"
            f"\n  router   - {settings.DATABASE_ROUTERS}"
            f"\n  default  - {settings._DB_ENGINE}"
            f"\n      ENGINE: {db_default.get('ENGINE', '')}"
            f"\n      NAME  : {db_default.get('NAME', '')}"
            f"\n      HOST  : {db_default.get('HOST', '')}"
            f"\n      PORT  : {db_default.get('PORT', '')}"
            f"\n      USER  : {db_default.get('USER', '')}"
            f"\n  datasvc  - {settings._DB_ENGINE_DS}"
            f"\n      ENGINE: {db_datasvc.get('ENGINE', '')}"
            f"\n      NAME  : {db_datasvc.get('NAME', '')}"
            f"\n      HOST  : {db_datasvc.get('HOST', '')}"
            f"\n      PORT  : {db_datasvc.get('PORT', '')}"
            f"\n      USER  : {db_datasvc.get('USER', '')}"
        )
        tip("Please confirm the configurations (enter 'yes' to START migrating) :")
        answer = input()
        if answer != "yes":
            info("Cancelled.")
            return False

        return True

    def _migrate_store_data(self) -> None:
        pass

    def _migrate_index(self) -> None:
        pass
