import itertools
from collections import OrderedDict
from datetime import datetime, timedelta, UTC
from typing import Any

import pytz  # type: ignore
from asgiref.sync import async_to_sync
from django.core.management.base import BaseCommand
from django.utils import timezone

from agent.config.agent import SearchFilterAgentConfig as AgentConfig
from agent.functions.search_across_docs import SearchAcrossDocsFunction
from agent.observer import Observer
from biz.enums import DocTypeEnum, TickerScopeEnum, DataProviderEnum
from common.services import slack
from common.utils.asyncioutil import with_concurrency_limit, gather_with_error
from common.utils.terminal import error, info, success
from datasvc.providers.financial_modeling_prep import FMPAPI
from datasvc.providers.onwish.scope import get_ticker_set_by_scope
from datasvc.providers.quartr_api import QuartrAPI

CHECK_DATE_RANGE_DAYS = 30
ERROR_DELAYED_HOURS = 4
INDEX_SEARCH_CONCURRENT_COUNT = 12

TICKER_SET_BY_SCOPE: OrderedDict[str, set] = OrderedDict(
    {
        "1. Key 30": get_ticker_set_by_scope(TickerScopeEnum.KEY),
        "2. S&P 500": get_ticker_set_by_scope(TickerScopeEnum.SNP),
        "3. Russell 2000": get_ticker_set_by_scope(TickerScopeEnum.RUSSELL),
        "4. Extra": get_ticker_set_by_scope(TickerScopeEnum.EXTRA),
    }
)


class Command(BaseCommand):
    help = "Check index data's completeness, freshness"

    _report: str = ""

    def add_arguments(self, parser: Any) -> None:
        parser.add_argument(
            "--send-to-slack",
            action="store_true",
            help="Send report to slack",
        )

    async def check_fmp_earning_calendar_coverage(self) -> None:
        """Check the coverage of FMP earning calendar data for supported tickers
        for a quarter.
        """
        today = self.current_time.date()
        min_ts = today - timezone.timedelta(days=50)
        max_ts = today + timezone.timedelta(days=50)
        first_half = await FMPAPI().fetch_earning_calendar(min_ts=today, max_ts=max_ts)
        second_half = await FMPAPI().fetch_earning_calendar(min_ts=min_ts, max_ts=today)
        data = first_half + second_half
        earning_date_by_ticker = {entry["symbol"]: entry["date"] for entry in data}
        ticker_set = get_ticker_set_by_scope(
            TickerScopeEnum.SNP, without_placeholder=True
        )
        missing_sp500 = [
            ticker for ticker in ticker_set if ticker not in earning_date_by_ticker
        ]
        ticker_set = get_ticker_set_by_scope(
            TickerScopeEnum.RUSSELL, without_placeholder=True
        )
        missing_russell = [
            ticker for ticker in ticker_set if ticker not in earning_date_by_ticker
        ]
        ticker_set = get_ticker_set_by_scope(
            TickerScopeEnum.EXTRA, without_placeholder=True
        )
        missing_extra = [
            ticker for ticker in ticker_set if ticker not in earning_date_by_ticker
        ]
        self._report += f"Missing earning calendar data for SP500 tickers in ({min_ts} - {max_ts}): {missing_sp500}\n"
        self._report += f"Missing earning calendar data for Russell 2000 tickers in ({min_ts} - {max_ts}): {missing_russell}\n"
        self._report += f"Missing earning calendar data for Extra tickers in ({min_ts} - {max_ts}): {missing_extra}\n"

    def _latest_earning_time(self, entry: dict) -> datetime:
        tz = pytz.timezone("America/New_York")

        if entry["time"] == "bmo":
            dt = datetime.strptime(f"{entry['date']} 09:30", "%Y-%m-%d %H:%M")
            localized_dt = tz.localize(dt)
        elif entry["time"] == "amc":
            dt = datetime.strptime(f"{entry['date']} 20:00", "%Y-%m-%d %H:%M")
            localized_dt = tz.localize(dt)
        else:
            error(
                f"Unknown earning time: {entry['time']} for ticker {entry['symbol']}. Assuming market close time for now."
            )
            dt = datetime.strptime(f"{entry['date']} 20:00", "%Y-%m-%d %H:%M")
            localized_dt = tz.localize(dt)

        return timezone.localtime(localized_dt)

    async def latest_earning_in_index(self, ticker: str) -> bool:
        days_ago = self.current_time - timezone.timedelta(
            days=CHECK_DATE_RANGE_DAYS + 1
        )
        search_function = SearchAcrossDocsFunction()
        result = await search_function.run(
            {
                "tickers": [ticker],
                # this query should be generic enough to retrieve some data
                "query": "earning",
                # CHECK_DATE_RANGE_DAYS + 1 should be ok to check if the latest earning data is in the index
                "min_date": days_ago.strftime("%Y-%m-%d"),
            },
            AgentConfig(doc_type_filter=[DocTypeEnum.EARNING_CALL.value]),
            Observer(),
        )
        return len(result) > 0

    async def check_index(self) -> None:  # noqa: C901
        """For tickers that is supposed to have earning published recently, check
        if the earning data is up-to-date in our index."""
        today = self.current_time.date()
        min_ts = today - timezone.timedelta(days=CHECK_DATE_RANGE_DAYS)
        data = await FMPAPI().fetch_earning_calendar(min_ts=min_ts, max_ts=today)

        supported_ticker_set = get_ticker_set_by_scope(
            TickerScopeEnum.FULL, without_placeholder=True
        )

        latest_time_by_ticker = {
            entry["symbol"]: self._latest_earning_time(entry)
            for entry in data
            if entry["symbol"] in supported_ticker_set
        }
        info(
            "Tickers with earning published/publishing in the last"
            f"{CHECK_DATE_RANGE_DAYS} days (including today) : {latest_time_by_ticker.keys()}"
        )

        # tickers we need to send error if we don't have the latest earning data
        error_if_missing_tickers = set()
        for ticker, latest_time in latest_time_by_ticker.items():
            if latest_time < self.current_time - timezone.timedelta(
                hours=ERROR_DELAYED_HOURS
            ):
                error_if_missing_tickers.add(ticker)

        data_by_ticker = {
            entry["symbol"]: entry
            for entry in data
            if entry["symbol"] in supported_ticker_set
        }
        error_tickers: dict[str, list] = {key: [] for key in TICKER_SET_BY_SCOPE.keys()}

        async def __check(_ticker: str) -> None:
            if not await self.latest_earning_in_index(_ticker):
                for _scope, _ticker_set in TICKER_SET_BY_SCOPE.items():
                    if _ticker in _ticker_set:
                        _missing = error_tickers.get(_scope, [])
                        _missing.append(_ticker)
                        error_tickers[_scope] = _missing
                        break

        await gather_with_error(
            *with_concurrency_limit(
                [__check(ticker) for ticker in error_if_missing_tickers],
                limit=INDEX_SEARCH_CONCURRENT_COUNT,
            )
        )

        # for ticker in error_if_missing_tickers:
        #     # TODO: try concurrently search for multiple tickers
        #     if not await self.latest_earning_in_index(ticker):
        #         if ticker in error_if_missing_tickers:
        #             for scope, ticker_set in TICKER_SET_BY_SCOPE.items():
        #                 if ticker in ticker_set:
        #                     missing = error_tickers.get(scope, [])
        #                     missing.append(ticker)
        #                     error_tickers[scope] = missing
        #                     break

        error_tickers = dict(sorted(error_tickers.items()))

        missed_ticker_to_providers = await self._provider_missing_ticker(
            list(itertools.chain(*error_tickers.values())),
            data_by_ticker,
        )

        self._report += (
            f"Error: Missing earning data for more than {ERROR_DELAYED_HOURS} hours:\n"
        )
        for scope, tickers in error_tickers.items():
            self._report += f"\n  ## {scope} \n"
            if not tickers:
                self._report += "    - N/A\n"
            else:
                for ticker in tickers:
                    d = data_by_ticker[ticker]
                    self._report += (
                        f"    - {ticker} published on {d['date']} ({d['time']})"
                        + (
                            f" (missed in {','.join(missed_ticker_to_providers[ticker])})"
                            if ticker in missed_ticker_to_providers
                            else ""
                        )
                        + "\n"
                    )

        self._report += (
            f"\nThere are total {len(latest_time_by_ticker)} tickers "
            "published/publishing earnings in the last (including today) "
            f"{CHECK_DATE_RANGE_DAYS} days.\n"
        )

        # await self._check_provider_earning_coverage_on_missing_tickers(
        #     list(itertools.chain(*error_tickers.values())),
        #     data_by_ticker,
        # )

    async def _provider_missing_ticker(
        self, error_tickers: list, data_by_ticker: dict
    ) -> dict:
        missing_tickers = error_tickers
        min_ts = datetime.now(tz=UTC) - timedelta(days=CHECK_DATE_RANGE_DAYS)
        fmp_data = await FMPAPI().get_most_recent_earnings_periods(
            missing_tickers, min_ts=min_ts
        )
        providers_by_ticker = {
            ticker: [DataProviderEnum.FMP.value]
            for ticker in missing_tickers
            if ticker not in fmp_data
            or data_by_ticker[ticker]["date"] > fmp_data[ticker][0].filing_date[:10]  # type: ignore
        }
        quartr_data = await QuartrAPI().get_most_recent_earnings_periods(
            missing_tickers, min_ts=min_ts
        )
        providers_by_ticker.update(
            {
                ticker: providers_by_ticker.get(ticker, [])
                + [DataProviderEnum.QUARTR.value]
                for ticker in missing_tickers
                if ticker not in quartr_data
                or data_by_ticker[ticker]["date"] > quartr_data[ticker][0].filing_date[:10]  # type: ignore
            }
        )

        return providers_by_ticker

    async def _check_provider_earning_coverage_on_missing_tickers(
        self, error_tickers: list, data_by_ticker: dict
    ) -> None:
        """Check the earning call coverage of several providers for the missing
        tickers.
        """
        missing_tickers = error_tickers
        min_ts = datetime.now(tz=UTC) - timedelta(days=CHECK_DATE_RANGE_DAYS)
        fmp_data = await FMPAPI().get_most_recent_earnings_periods(
            missing_tickers, min_ts=min_ts
        )
        fmp_missing_tickers = [
            ticker
            for ticker in missing_tickers
            if ticker not in fmp_data
            or data_by_ticker[ticker]["date"] > fmp_data[ticker][0].filing_date[:10]  # type: ignore
        ]
        quartr_data = await QuartrAPI().get_most_recent_earnings_periods(
            missing_tickers, min_ts=min_ts
        )
        quartr_missing_tickers = [
            ticker
            for ticker in missing_tickers
            if ticker not in quartr_data
            or data_by_ticker[ticker]["date"] > quartr_data[ticker][0].filing_date[:10]  # type: ignore
        ]
        self._report += (
            "\n==== Earning call data coverage for missing tickers in the index: ====\n"
        )
        self._report += (
            f"{len(fmp_missing_tickers)}/{len(missing_tickers)} "
            f"missing earning call data in FMP: {fmp_missing_tickers}\n"
        )
        self._report += (
            f"{len(quartr_missing_tickers)}/{len(missing_tickers)} "
            f"missing earning call data in Quartr: {quartr_missing_tickers}\n"
        )

    def handle(self, *args: Any, **kwargs: Any) -> None:
        # Activate NYC timezone
        timezone.activate(pytz.timezone("America/New_York"))
        self.current_time = timezone.localtime(timezone.now())
        self._report += f"Current NYC time: {self.current_time}\n"

        try:
            self._report += "==== Earning calendar data coverage: ====\n"
            async_to_sync(self.check_fmp_earning_calendar_coverage)()
            self._report += "\n==== Latest earning data in index check: ====\n"
            async_to_sync(self.check_index)()
            info(self._report)
            if kwargs.get("send_to_slack"):
                slack.send_message(self._report)
            success("Successfully ran check index!")
        finally:
            timezone.deactivate()
