"""Currently just indexing data in an adhoc way
"""

import argparse
import asyncio
import json
from datetime import datetime
from typing import Any

from django.core.management.base import BaseCommand

from biz.enums import DocTypeEnum, TickerScopeEnum
from common.utils.datetimeutils import parse_datetime, LOCAL_TZ
from common.utils.terminal import warn
from datasvc.indexing.indexer.base import Indexer
from datasvc.indexing.indexer.conference import ConferenceIndexer
from datasvc.indexing.indexer.earning_call import EarningCallIndexer
from datasvc.indexing.indexer.event_transcript import EventTranscriptIndexer
from datasvc.indexing.indexer.news import NewsIndexer
from datasvc.indexing.indexer.press_release import PressReleaseIndexer
from datasvc.indexing.indexer.sec_filing import SECFilingIndexer
from datasvc.indexing.indexer.twitter import TwitterIndexer
from datasvc.indexing.indexer.youtube import YoutubeIndexer
from datasvc.providers.onwish.scope import get_ticker_set_by_scope


class Command(BaseCommand):
    help: str = "Fetch, download and index data in a adhoc way (v2)"

    def add_arguments(self, parser: argparse.ArgumentParser) -> None:
        parser.add_argument("doc_type", type=str, choices=DocTypeEnum.values())
        parser.add_argument(
            "--run-type",
            type=str,
            choices=["update", "fetch", "parse"],
            required=True,
            help="the type of run to do",
        )
        parser.add_argument(
            "--min-ts",
            type=self.valid_date,
            help=(
                "The min timestamp to fetch data from (only available for `update`). "
                "Must be in the format of YYYY-MM-DD (HH:MM:SS)"
            ),
            required=False,
        )
        parser.add_argument(
            "--overwrite",
            action="store_true",
            help=(
                "whether overwrite existed content (for downloading) or chunk (for indexing)"
            ),
        )
        parser.add_argument(
            "--scope",
            type=str,
            choices=TickerScopeEnum.values(),
            required=False,
            default="full",
            help="update tickers in scope. only for 'update'",
        )
        parser.add_argument(
            "--by-list",
            action="store_true",
            help="whether fetch tweets from twitter list. Now support 'fetch' 'twitter'.",
        )
        parser.add_argument(
            "--by-bot",
            action="store_true",
            help="Whether use remote bot to update or fetch docs. Available for 'twitter' on 'update'/'fetch'.",
        )
        parser.add_argument(
            "--raise-for-errors",
            action="store_true",
            help="After successfully ran, if there is errors, raise it.",
        )

    def valid_date(self, date_string: str) -> str:
        if parse_datetime(date_string, tz=LOCAL_TZ) is None:
            raise argparse.ArgumentTypeError(
                f"Given date {date_string} invalid format."
            )
        return date_string

    def handle(self, *args: Any, **kwargs: Any) -> None:  # noqa: C901
        doc_type: str = kwargs["doc_type"]
        run_type: str = kwargs["run_type"]
        overwrite: bool = kwargs["overwrite"]
        raise_for_errors: bool = kwargs["raise_for_errors"]
        min_ts: datetime | None = parse_datetime(kwargs["min_ts"], tz=LOCAL_TZ)
        by_bot: bool = kwargs["by_bot"]
        by_list: bool = kwargs["by_list"]
        scope = kwargs["scope"]

        indexer: Indexer
        if doc_type == DocTypeEnum.EARNING_CALL:
            indexer = EarningCallIndexer()
        elif doc_type == DocTypeEnum.EVENT_TRANSCRIPT:
            indexer = EventTranscriptIndexer()
        elif doc_type == DocTypeEnum.NEWS:
            indexer = NewsIndexer()
        elif doc_type == DocTypeEnum.SEC_FILING:
            indexer = SECFilingIndexer()
        elif doc_type == DocTypeEnum.PRESS_RELEASE:
            indexer = PressReleaseIndexer()
        elif doc_type == DocTypeEnum.CONFERENCE:
            indexer = ConferenceIndexer()
        elif doc_type == DocTypeEnum.TWITTER:
            indexer = TwitterIndexer()
        elif doc_type == DocTypeEnum.YOUTUBE:
            indexer = YoutubeIndexer()

        if run_type == "update":
            if overwrite:
                warn(
                    'argument "--overwrite" is ignored while run-type is "update" except press_release'
                )
            asyncio.run(
                indexer.update_v2(
                    {},
                    within_ticker_set=get_ticker_set_by_scope(scope),
                    by_bot=by_bot,
                    default_min_ts=min_ts,
                    overwrite=overwrite,
                )
            )
        elif run_type == "fetch":
            if overwrite:
                warn(
                    "All content will be downloaded and persisted even it is existed "
                    "due to --overwrite argument is set."
                )
            asyncio.run(
                indexer.download_persist(
                    ticker=None, overwrite=overwrite, by_list=by_list, by_bot=by_bot
                )
            )
        elif run_type == "parse":
            if overwrite:
                warn(
                    "All chunks will be indexed even it was indexed "
                    "due to --overwrite argument is set."
                )
            asyncio.run(indexer.split_index(ticker=None, overwrite=overwrite))
        else:
            raise ValueError(f"Unknown run_type: {run_type}")

        self.stdout.write(
            self.style.SUCCESS(f"Successfully ran '{run_type}' {doc_type} data")
        )

        # raise errors to make airflow task fail
        if raise_for_errors:
            # handle all minor errors
            errors: list[str | BaseException] = indexer.get_errors()
            if len(errors) > 0:
                err_msg = json.dumps([str(e) for e in errors], indent=2)
                raise RuntimeError(
                    f"Successfully finished but with {len(errors)} errors: {err_msg}"
                )
