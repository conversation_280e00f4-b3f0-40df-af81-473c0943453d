"""datasvc statistics report"""

import argparse
from datetime import datetime, UTC
from typing import Any, Literal, Sequence, Iterable

from asgiref.sync import async_to_sync
from django.core.cache import caches
from django.core.management.base import BaseCommand
from tabulate import tabulate

from biz.enums import DocTypeEnum, TickerScopeEnum
from common.services import slack
from common.utils.datetimeutils import LOCAL_TZ, parse_datetime, format_datetime
from common.utils.terminal import warn, success, info
from datasvc import statistic
from datasvc.providers.onwish.scope import get_ticker_set_by_scope
from datasvc.statistic import DocIntegrityChecker

cache = caches["default"]
INTEGRITY_CHECK_DAYS = 3


class Command(BaseCommand):
    def add_arguments(self, parser: argparse.ArgumentParser) -> None:
        parser.add_argument(
            "report_type",
            type=str,
            default="overall",
            choices=["overall", "fail-docs", "period", "daily", "missing", "integrity"],
        )
        parser.add_argument(
            "--scope",
            default="",
            type=str,
            choices=TickerScopeEnum.values(),
            required=False,
            help="Which scope of tickers to run check & report for. default: full scope for all tickers.",
        )
        parser.add_argument(
            "--tickers",
            default=[],
            nargs="+",
            type=str,
            required=False,
            help="group by tickers, limited to 1 ~ 30 tickers. Ignored if '--scope' specified.",
        )
        parser.add_argument(
            "--doc-types",
            default=[],
            nargs="+",
            type=str,
            choices=list(DocTypeEnum.values()),
            required=False,
            help="group by doc types. if not present, retrieve all and NO grouping by doc type.",
        )
        parser.add_argument(
            "--min-ts",
            default="",
            type=str,
            required=False,
            help="minimal date to get data from. if not presents, retrieve from oldest",
        )
        parser.add_argument(
            "--max-ts",
            default="",
            type=str,
            required=False,
            help="maximal date to get data until. if not presents, retrieve from today",
        )
        parser.add_argument("--to", default="", type=str, required=False)
        parser.add_argument(
            "--by-period",
            default="",
            type=str,
            choices=["year", "quarter", "month", ""],
            required=False,
            help="group by period (year|quarter|month). if not present, NO grouping by period.",
        )
        parser.add_argument(
            "--by-status",
            action="store_true",
            help="whether group by status. default False",
        )
        parser.add_argument(
            "--send",
            action="store_true",
            help="send report to pre-defined channel such as Slack, Email, etc.",
        )
        parser.add_argument(
            "--store-result",
            action="store_true",
            help="store result to cache for web using. only for 'integrity'",
        )

    def handle(self, *args: Any, **kwargs: Any) -> None:  # noqa: C901
        send_report: bool = kwargs["send"]
        report_type: str = kwargs["report_type"]
        scope: str = kwargs["scope"]
        store_result: bool = kwargs["store_result"]
        doc_types: list[str] = kwargs["doc_types"] or list(DocTypeEnum.values())
        by_period: Literal["year", "quarter", "month", ""] = kwargs["by_period"]
        by_status: bool = kwargs["by_status"]
        min_ts: datetime | None = parse_datetime(kwargs["min_ts"], tz=LOCAL_TZ)
        max_ts: datetime | None = parse_datetime(kwargs["max_ts"], tz=LOCAL_TZ)
        sb_print = []
        sb_send = []

        if scope:
            if kwargs["tickers"]:
                warn('Ignore --tickers due to "scope" is specified.')
            tickers = get_ticker_set_by_scope(scope)
        else:
            tickers = set(kwargs["tickers"])
            assert 0 < len(tickers) <= 30

        def __inner_check_args(_report_types: list[str], *_args_names: str) -> None:
            if report_type not in _report_types:
                for _n in _args_names:
                    if kwargs.get(_n.replace("-", "_")):
                        warn(
                            f"Ignore --{_n} argument due to it's not supported by {_report_types} report types."
                        )

        __inner_check_args(["integrity"], "store-result")
        __inner_check_args(["period"], "by-period", "by-status", "min-ts", "max-ts")
        __inner_check_args(["period", "missing", "integrity"], "doc-types")
        __inner_check_args(["period", "integrity"], "scope", "tickers")

        if report_type == "period":
            docs = statistic.doc_status_by_ticker_period_or_type(
                tickers,
                min_ts=min_ts,
                max_ts=max_ts,
                doc_types=doc_types,
                by_period=by_period,
                by_status=by_status,
            )
            title = "".join(
                [
                    "doc count ",
                    f"in {doc_types} " if doc_types else "",
                    f"by period '{by_period}' " if by_period else "",
                    "by status " if by_status else "",
                    f"for {tickers if tickers else 'all tickers'} ",
                    f"from {min_ts.strftime('%Y-%m-%d')} " if min_ts else "",
                    f"until {max_ts.strftime('%Y-%m-%d')} " if max_ts else "",
                ]
            )
            sb_print.extend(self._render_print(docs, title=title))
            sb_send.extend(self._render_send(docs, title=title))
        elif report_type == "daily":
            counts = statistic.daily_count()
            sb_print.extend(self._render_print(counts, title="daily counts"))
            sb_send.extend(self._render_send(counts, title="daily counts"))
        elif report_type == "fail-docs":
            # --- failed docs list
            docs = statistic.list_fail_docs(limit_per_doc_type=10)
            sb_print.extend(self._render_print(docs, title="failed docs"))
            sb_send.extend(self._render_send(docs, title="failed docs", fmt="simple"))
        elif report_type == "missing":
            # list missing count
            days = 7
            count_failed = True
            for doc_type in doc_types:
                data = self._list_missing(doc_type, days, count_failed)
                title = f"Missing {DocTypeEnum(doc_type).display_value} in {days} days (failed {'counted' if count_failed else 'not counted'}, {format_datetime(datetime.now())})"
                sb_print.extend(
                    self._render_print(data, title=title, headers="firstrow")
                )
                sb_send.extend(
                    self._render_send(
                        data, title=title, headers="firstrow", fmt="simple"
                    )
                )

                # handle output in loop and return after loop
                print("\n".join(sb_print))
                sb_print = []

                if send_report:
                    slack.send_message("\n".join(sb_send))
                    sb_send = []
            return
        elif report_type == "integrity":
            info(
                f"starting checking {INTEGRITY_CHECK_DAYS} days docs integrity for {scope} tickers in {doc_types} ..."
            )
            self.check_integrity(
                doc_types=doc_types, tickers=tickers, force=True, store=store_result
            )
            if send_report:
                slack.send_message(
                    "Doc integrity checked. Visit <https://app.onwish.ai/tools/data/|_Tools_> to see."
                )
            return
        else:
            """overall"""
            # --- doc counts
            counts = statistic.dict_to_table(statistic.get_doc_counts())
            sb_print.extend(self._render_print(counts, title="doc counts"))
            sb_send.extend(self._render_send(counts, title="doc counts"))

            # --- failure counts
            failures = statistic.dict_to_table(statistic.get_doc_failures())
            sb_print.extend(self._render_print(failures, title="failure counts"))
            sb_send.extend(self._render_send(failures, title="failure counts"))

            # --- status count
            status = statistic.get_doc_status_count()
            sb_print.extend(self._render_print(status, title="status count"))
            sb_send.extend(self._render_send(status, title="status count"))

        print("\n".join(sb_print))

        if send_report:
            slack.send_message("\n".join(sb_send))

    def _render_print(
        self,
        data: list | dict,
        title: str = "",
        headers: str | list = "keys",
        fmt: str = "simple_grid",
    ) -> list:
        sb = []

        msg = tabulate(
            data,
            headers=headers,
            tablefmt=fmt,
        )

        sb.append("")
        sb.append(f"{title}:")
        sb.append(msg)

        return sb

    def _render_send(
        self,
        data: list | dict,
        title: str = "",
        headers: str | list = "keys",
        fmt: str = "simple_grid",
    ) -> list:
        sb = []

        msg = tabulate(
            data,
            headers=headers,
            tablefmt=fmt,
        )

        sb.append("")
        sb.append(f"*{title}*:")
        sb.append("```")
        sb.append(msg)
        sb.append("```")

        return sb

    def _list_missing(
        self, doc_type: str | DocTypeEnum, days: int = 7, count_failed: bool = False
    ) -> list[Sequence]:
        date_field = "pub_date"
        result = statistic.list_missing_docs(
            doc_type, in_n_days=days, date_field=date_field, count_failed=count_failed
        )
        data = result.get(doc_type, {})

        # first row as header (headers='firstrow')
        headers = ["ticker", "duration", date_field, "all", "in progress"]
        if count_failed:
            headers.append("failed")
        table_data: list[Sequence] = [headers]
        for ticker, d in data.items():
            item = [
                ticker,
                str(d["dur"]).split(".")[0],
                format_datetime(d["earliest"]),
                d["all"],
                d.get("progress", 0),
            ]
            if count_failed:
                item.append(d.get("failed", 0))
            table_data.append(item)
        return table_data

    def check_integrity(
        self,
        doc_types: Iterable[str | DocTypeEnum],
        tickers: Iterable[str],
        force: bool = False,
        store: bool = False,
    ) -> dict:
        checking_from = datetime.now(tz=UTC)
        if store:
            cache.set(
                DocIntegrityChecker.CACHE_KEY_CHECKING_FROM,
                checking_from,
                timeout=3600 * 2,
            )

        result: dict = {}
        for doc_type in doc_types:
            checker_cls = DocIntegrityChecker.get_checker_by_doc_type(doc_type)
            checker = checker_cls(days=INTEGRITY_CHECK_DAYS)

            r = async_to_sync(checker.check)(tickers, no_cache=force)
            if not result:
                result = r
            else:
                for scope in r:
                    result[scope]["status"].update(r[scope]["status"])

        result["checking_from"] = checking_from
        result["checked_at"] = datetime.now(tz=UTC)
        result["days"] = INTEGRITY_CHECK_DAYS
        if store:
            cache.delete(DocIntegrityChecker.CACHE_KEY_CHECKING_FROM)
            cache.set(
                DocIntegrityChecker.CACHE_KEY_RESULT, result, timeout=None
            )  # no expiration
        success("finished checking docs integrity.")
        return result
