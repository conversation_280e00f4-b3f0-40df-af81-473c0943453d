# Generated by Django 4.2.2 on 2025-02-28 09:39

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("aiflow", "0025_aiflow_is_low_relevance_shown"),
    ]

    operations = [
        migrations.CreateModel(
            name="InstagramPostModel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "url",
                    models.URLField(help_text="Url of which the entry was crawled"),
                ),
                (
                    "caption",
                    models.TextField(
                        blank=True, help_text="Name, title, caption for display purpose"
                    ),
                ),
                (
                    "value",
                    models.J<PERSON>NField(
                        blank=True,
                        help_text="Original data (html/markdown/text/json/img/video)",
                        null=True,
                    ),
                ),
                (
                    "post_id",
                    models.Char<PERSON>ield(
                        help_text="Instagram post ID", max_length=255, unique=True
                    ),
                ),
                ("author_id", models.CharField(blank=True, max_length=255)),
                ("author_handle", models.CharField(blank=True, max_length=255)),
                ("author_display_name", models.CharField(blank=True, max_length=255)),
                ("description", models.TextField(blank=True)),
                ("published_time_str", models.CharField(max_length=50)),
                ("published_at", models.DateTimeField(null=True)),
                ("duration_str", models.CharField(blank=True, max_length=20)),
                ("duration", models.IntegerField(blank=True, null=True)),
                ("thumbnail", models.JSONField(default=dict)),
                ("view_count", models.IntegerField(blank=True, null=True)),
                ("comment_count", models.IntegerField(blank=True, null=True)),
                ("favorite_count", models.IntegerField(blank=True, null=True)),
                ("like_count", models.IntegerField(blank=True, null=True)),
                ("dislike_count", models.IntegerField(blank=True, null=True)),
                ("share_count", models.IntegerField(blank=True, null=True)),
                ("tags", models.JSONField(default=list)),
                (
                    "platform_id",
                    models.BigIntegerField(help_text="Instagram internal id"),
                ),
                (
                    "platform_pk",
                    models.BigIntegerField(help_text="Instagram internal PK"),
                ),
            ],
            options={
                "db_table": "aiflow_instagram_posts",
                "indexes": [
                    models.Index(
                        fields=["post_id"], name="aiflow_inst_post_id_099986_idx"
                    ),
                    models.Index(
                        fields=["author_handle"], name="aiflow_inst_author__039f9f_idx"
                    ),
                    models.Index(
                        fields=["published_at"], name="aiflow_inst_publish_77467b_idx"
                    ),
                ],
            },
        ),
    ]
