# Generated by Django 4.2.2 on 2025-01-26 06:10

from django.db import migrations, models, transaction


@transaction.atomic
def migrate(apps, schema_editor) -> None:
    AIFLow = apps.get_model("aiflow", "AIFlow")

    for obj in AIFLow.objects.all():
        if obj.is_charged and obj.data:
            rows_cnt = len(obj.data.get("rows") or [])
            if rows_cnt > 0:
                obj.charged_rows = rows_cnt
                obj.save()


class Migration(migrations.Migration):
    dependencies = [
        ("aiflow", "0022_alter_charge_action_alter_charge_flow_id"),
    ]

    operations = [
        migrations.AddField(
            model_name="aiflow",
            name="charged_rows",
            field=models.IntegerField(default=0, help_text="how many rows are charged"),
        ),
        migrations.AddField(
            model_name="aiflow",
            name="is_expended",
            field=models.BooleanField(
                default=False,
                help_text="whether the rest row expended (by default only show/charge 10 rows.",
            ),
        ),
        migrations.RunPython(migrate),
        migrations.RemoveField(
            model_name="aiflow",
            name="is_charged",
        ),
    ]
