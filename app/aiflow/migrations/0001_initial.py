# Generated by Django 4.2.2 on 2024-10-24 03:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AIFlow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("flow_uuid", models.CharField(max_length=255, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name="AIFlowStep",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("step_uuid", models.Char<PERSON>ield(max_length=255, unique=True)),
                ("step_type", models.Char<PERSON>ield(max_length=255)),
                ("data", models.JSONField(blank=True, default=dict, null=True)),
                (
                    "flow",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="aiflow.aiflow"
                    ),
                ),
            ],
        ),
    ]
