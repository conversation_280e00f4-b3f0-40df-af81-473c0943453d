## Table operation

### Add a column

```python
await self.observer.notify_add_column(
    column_id="first_name",
    column_width=150,
)
```

### Add or edit a cell

```python
# Add a cell
await self.observer.notify_add_cell(
    row_id="1",
    column_id="first_name",
    value="Thinking....",
    done=False
)

# Edit a cell
await self.observer.notify_add_cell(
    row_id="1",
    column_id="first_name",
    value="Elon",
    # when done is true, then db will save the cell
    done=True
)
```
