import json
import logging
import re
from abc import ABC
from typing import Any, Literal, Self, ClassVar, Optional

from pydantic import BaseModel

from common.utils.asyncioutil import is_async_callable
from common.utils.lang import import_from_module, iter_subclasses
from common.utils.structutil import StructDictMixin

logger = logging.getLogger(__name__)


# -----  action  -----
class FlowAction(StructDictMixin, BaseModel, ABC):
    """Base flow which represents an event callback for a flow step.
    It will be called after a flow step finished.
    So far we only support backend action.
    """

    # action name is the unique id
    name: str

    # which method to handle the action. for backend example, "myapp.package.handle_method"
    # it must be `func(**kwargs:Any) -> Optional[tuple]` style function.
    handler: str

    # name list of handler's keyword argument. values are from flow context
    input: list[str] = list()

    # name list of handler's return values. which will be put into flow context
    output: list[str] = list()

    class Config:
        arbitrary_types_allowed = True

    @staticmethod
    def create_be_action(**kwargs: Any) -> "FlowAction":
        """create a backend action instance"""
        return FlowBackendAction(**kwargs)


class FlowBackendAction(FlowAction):
    """Backend flow action"""


# -----  component  -----
class FlowComponent(StructDictMixin, BaseModel, ABC):
    """base class for which represents an interactive component of a flow step."""

    # what type of this component. it decides the looks and logic.
    type: ClassVar[str]

    # submit types. it's mapping to submit button action.
    submit: list[Literal["next", "back", "stop"]] = list(["next"])

    # name list of context. Those values will be used in this component
    # It will be replaced by a dict which values are coping from flow context before rendering.
    input: list[str] = list()

    # name list of values in the component's payload.output. which will be put into flow context
    output: list[str] = list()

    # title of the component
    title: str = ""

    # the information text
    text: str = ""

    class Config:
        arbitrary_types_allowed = True

    def __str__(self) -> str:
        return self.type + "(component)"

    @staticmethod
    def create(_type: str, **kwargs: Any) -> "FlowComponent":
        """create a flow component instance by given type"""
        cls = SUPPORTED_COMPONENTS_MAPPING.get(_type)
        if not cls:
            raise TypeError(f"{_type} is unknown FlowComponent.")
        return cls(**kwargs)

    def resolve_vars(self, **ctx: Any) -> None:
        for name in self.model_fields:
            self._resolve_var(self, name, ctx)
        if isinstance(self, CustomizedFlowComponent):
            for name in self.attributes:
                self._resolve_var(self.attributes, name, ctx)

    @staticmethod
    def _resolve_var(_obj: Any, _name: str, _ctx: dict) -> None:
        if isinstance(_obj, dict):
            _v = _obj.get(_name, "")
        else:
            _v = getattr(_obj, _name, "")

        if isinstance(_v, str):
            m = re.match(r"^\$\{(?P<expr>[\w\.]+)\}$", _v.strip())
            if m:
                expr = m.group("expr")
                attrs = expr.split(".")
                value = _ctx.get(attrs[0])
                for attr in attrs[1:]:
                    value = (
                        value[attr] if isinstance(value, dict) else getattr(value, attr)
                    )
                    if not value:
                        break
                if isinstance(_obj, dict):
                    _obj[_name] = value
                else:
                    setattr(_obj, _name, value)

    @classmethod
    def from_dict(cls, d: dict) -> Self:
        _type = d.pop("type")
        return cls.create(_type, **d)

    def to_dict(self) -> dict:
        d = super().to_dict()
        d["type"] = self.type
        return d


class InformationFlowComponent(FlowComponent):
    """the component provides information & submit buttons only."""

    type: ClassVar[str] = "information"


class InputFlowComponent(FlowComponent, ABC):
    """provides text prompts and let user to input a value."""

    # default value of the input
    default: Any = None

    # placeholder to be displayed in input if user has not entered
    place_holder: Any = None


class TextInputFlowComponent(InputFlowComponent):
    """to let user inputting a text value."""

    type: ClassVar[str] = "text_input"

    # override parent's default & place_holder type to str
    default: str = ""
    place_holder: str = ""


class MultiTextInputFlowComponent(InputFlowComponent):
    """to let user inputting multiple text values."""

    type: ClassVar[str] = "multi_text_input"

    # override parent's default & place_holder type to list[str]
    # str for ${} style variable
    default: list[str] | str = []
    place_holder: list[str] | str = []

    # whether user can add / remove inputs.
    can_add_remove: bool = False


class TextWithExampleInputFlowComponent(TextInputFlowComponent):
    """to let user inputting a text value and give some examples to let user choosing."""

    type: ClassVar[str] = "text_with_example_input"

    # example list which is text(str) type
    # it can be str which is for "${context_var}" style replacement
    examples: list[str] | str = list()  # str for var "${}"


class ChoiceFlowComponentItem(StructDictMixin, BaseModel):
    """represents an option item for a choice flow"""

    index: int
    key: str
    value: str
    desc: str


class ChoiceFlowComponent(FlowComponent, ABC):
    """base class for components which shows informational prompts and provide some options.
    To let user choosing form provided options."""

    # text introduction of this card
    text: str = ""

    # list of choice items. the item can be dict (flex) or `ChoiceFlowComponentItem` (fixed)
    # it can be str which is for "${context_var}" style replacement
    choices: list[dict] | str = list()


class SingleChoiceFlowComponent(ChoiceFlowComponent):
    """To let user choosing only one out form provided options."""

    type: ClassVar[str] = "single_choice"


class MultipleChoiceFlowComponent(ChoiceFlowComponent):
    """To let user choosing multiple items form provided options."""

    type: ClassVar[str] = "multiple_choice"


class CustomizedFlowComponent(FlowComponent):
    """To create a customized component."""

    type: ClassVar[str] = "customized"

    # customized attributes which should be handled by handler & UI themselves.
    attributes: dict = dict()


SUPPORTED_COMPONENTS_MAPPING = {
    cls.type: cls
    for cls in iter_subclasses(FlowComponent)
    if hasattr(cls, "type") and cls.type
}

# -----  step  -----


class FlowStep(StructDictMixin, BaseModel, ABC):
    """base class of a flow step."""

    # what type is current step
    type: ClassVar[str]

    # step name is unique ID
    name: str

    # what component will be used to render this step in UI.
    component: FlowComponent

    # what action will be token after user submit
    action: Optional[FlowAction] = None

    class Config:
        arbitrary_types_allowed = True

    def __str__(self) -> str:
        return self.name + "(step)"

    @staticmethod
    def create(_type: str = "common", **kwargs: Any) -> "FlowStep":
        """create a flow step instance by given type"""

        cls = SUPPORTED_STEPS_MAPPING.get(_type)
        if not cls:
            raise TypeError(f"{_type} is unknown FlowStep.")

        d_component = kwargs.pop("component") or {}
        comp_type = d_component.pop("type")
        kwargs["component"] = FlowComponent.create(_type=comp_type, **d_component)

        d_action = kwargs.pop("action") or {}
        kwargs["action"] = FlowAction.create_be_action(**d_action) if d_action else None

        return cls(**kwargs)

    async def async_run(self, **ctx: Any) -> tuple:
        if self.action:
            for name in self.action.input:
                assert name in ctx, f"input {name} not in context."
            try:
                func = import_from_module(self.action.handler)
                if is_async_callable(func):
                    r = await func(**ctx)
                else:
                    r = func(**ctx)
            except Exception as e:
                logger.error(f"error run action {self.action.name}. {e}")
                raise e

            r = r or tuple()
            assert (not r and not self.action.output) or (
                isinstance(r, tuple) and len(r) == len(self.action.output)
            ), "action return value must be a tuple of N values. N is same as size of action.output."
            return r
        return tuple()

    @classmethod
    def from_dict(cls, d: dict) -> Self:
        _type = d.pop("type", "common")
        return cls.create(_type, **d)

    def render_fe_dict(self, **context: Any) -> dict:
        d = self.to_dict()
        d.pop("action")
        d_comp = d["component"]
        input_names = d_comp.pop("input")
        d_comp["input"] = {name: context.get(name) for name in input_names}
        d["component"] = d_comp
        return d


class CommonStep(FlowStep):
    """represents common usage flow step for most scenarios."""

    type: ClassVar[str] = "common"


SUPPORTED_STEPS_MAPPING = {
    cls.type: cls
    for cls in iter_subclasses(FlowStep)
    if hasattr(cls, "type") and cls.type
}
# -----  flow  -----


class FlowSpec(StructDictMixin, BaseModel):
    """represents the design spec of a flow. It handles flow execution as well."""

    # flow name is an unique ID
    name: str

    # what steps are in this flow (in order)
    steps: list[FlowStep]

    class Config:
        arbitrary_types_allowed = True

    __cur_step_idx: int = 0
    __context: dict = dict()

    def __str__(self) -> str:
        return self.name + "(flow)"

    @classmethod
    def load_from_file(cls, fpath: str) -> Self:
        with open(fpath, "r") as f:
            s = f.read()
        return cls.from_json(s)

    def save_to_file(self, fpath: str) -> None:
        s = self.to_json()
        with open(fpath, "w") as f:
            f.write(s)

    @classmethod
    def from_json(cls, spec: str) -> Self:
        d = json.loads(spec)
        return cls.from_dict(d)

    def to_json(self, indent: int = 0) -> str:
        return self.model_dump_json(indent=indent)

    @classmethod
    def from_dict(cls, d: dict) -> Self:
        d["steps"] = [FlowStep.from_dict(d_step) for d_step in d.get("steps", [])]
        return super().from_dict(d)

    @property
    def context(self) -> dict:
        return self.__context

    @property
    def cur_step(self) -> FlowStep:
        return self.steps[self.__cur_step_idx]

    @property
    def is_finished(self) -> bool:
        return self.__cur_step_idx >= len(self.steps)

    @property
    def progress(self) -> float:
        if self.is_finished:
            return 100.00
        else:
            return self.__cur_step_idx / len(self.steps)

    async def async_go_next(self) -> None:
        if self.is_finished:
            raise RuntimeError("flow was finished.")
        await self.async_run_step(self.cur_step)
        self.__cur_step_idx += 1

    async def async_go_back(self) -> None:
        if self.is_finished:
            raise RuntimeError("flow was finished.")
        if self.__cur_step_idx == 0:
            raise RuntimeError("Can not go back due to already at first step.")
        await self.async_run_step(self.cur_step)
        self.__cur_step_idx -= 1

    def stop(self) -> None:
        self.__cur_step_idx = len(self.steps)

    async def async_go(self, cur_step_payload: dict) -> None:
        logger.debug(f"ctx: {self.context.keys()}")
        step: str = cur_step_payload.get("step") or ""
        assert step == self.cur_step.name
        submit: str = cur_step_payload.get("submit") or ""
        output: dict = cur_step_payload.get("output") or {}
        for name in self.cur_step.component.output:
            assert name in output, f"{name} not in current step component payload"
            self.context[name] = output[name]

        if submit == "next":
            if not self.is_finished:
                await self.async_go_next()
        elif submit == "stop":
            if not self.is_finished:
                await self.async_go_next()
            self.__cur_step_idx = len(self.steps)
        else:
            raise NotImplementedError(f"submit type {submit} is not supported yet.")

    async def async_run_step(self, step: FlowStep) -> None:
        """run a specified step"""
        logger.info(f"running step {step} ...")
        results = await step.async_run(**self.context)
        if step.action:
            for name, value in zip(step.action.output, results):
                self.__context[name] = value
        logger.debug(f"finish step {step}")

    def resolve_vars(self) -> None:
        """resolve "${context_var}" style var replacement into real value for current step"""
        self.cur_step.component.resolve_vars(**self.context)
