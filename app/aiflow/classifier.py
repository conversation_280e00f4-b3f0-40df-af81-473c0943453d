from logging import getLogger

from asgiref.sync import sync_to_async

from agent.agents.base import User
from agent.models import Profile
from aiflow.enum import TaskStatus
from aiflow.flows.guide_v2 import key_to_cases
from aiflow.models import AIFlow
from biz.ai_models import PreloadedSentenceTransformer
from payment.models import CreditPtsCard

logger = getLogger(__name__)

# Cache the model instance
_model = None
SIMILARITY_THRESHOLD = 0.85


def get_model() -> PreloadedSentenceTransformer:
    global _model
    if _model is None:
        _model = PreloadedSentenceTransformer()
    return _model


def classify_target_user(user_id: int, flow_id: int) -> dict:
    """classify given user and flow whether user is target user.

    Rules (if user matches all the following):
    1. User's profession is one of following:
        - Brand
        - Sales/Marketing Professional
        - Influencer marketing agency
        - Other types of agency
    2. User has used over 30 credits
    3. User successfully run at least 2 flow/searches.
    4. The task description of flow is not similar to our examples.
    """
    rc = {"is_target_user": False}

    # Get user profile and check profession
    # target_professions = {
    #     "Brand",
    #     "Sales/Marketing Professional",
    #     "Influencer marketing agency",
    #     "Other types of agency",
    #     "Startup founder",
    # }

    try:
        # Get user, profile and flow from database
        user = User.objects.get(id=user_id)

        # check profession
        # profile = Profile.objects.get(user_id=user_id)
        # if profile.profession not in target_professions:
        #     logger.debug(
        #         f"{user}'s profession '{profile.profession}' is not target profession."
        #     )
        #     return rc

        # Check credit usage
        credits_used, _ = CreditPtsCard.get_current_period_usage(user)
        if credits_used < 50:
            logger.debug(f"{user} used {credits_used} is less than 50.")
            return rc

        # Check number of successful flows/searches
        successful_flows = AIFlow.objects.filter(
            user_id=user_id, status=TaskStatus.DONE
        ).count()
        if successful_flows < 2:
            # logger.debug(
            #     f"{user} has {successful_flows} success search which is less than 2."
            # )
            # return rc

            # check if task is similar to examples
            flow = AIFlow.objects.get(id=flow_id)
            usecase = flow.context.get("usecase")  # find_influencers_instagram
            task_desc = flow.context.get("task_desc")
            if is_similar_to_examples(task_desc, usecase):
                logger.debug(f"{user}'s task desc is similar to example.")
                return rc

        # All conditions met
        rc["is_target_user"] = True

    except (User.DoesNotExist, Profile.DoesNotExist, AIFlow.DoesNotExist):
        # Handle case where user or flow doesn't exist
        logger.error(f"User/Profile (user_id={user_id}) or flow {flow_id} is not found")
        return rc

    return rc


def is_similar_to_examples(task_desc: str, usecase: str) -> bool:
    """Check if the task description is similar to examples.

    Args:
        task_desc: The task description to check
        usecase: The usecase type (e.g. "find_influencers_instagram")

    Returns:
        bool: True if task is similar to examples, False otherwise
    """
    if not task_desc or not usecase:
        return False

    # Get example tasks for this usecase
    example_tasks = key_to_cases.get(usecase, [])
    if not example_tasks:
        logger.warning(f"No examples found for usecase: {usecase}")
        return False

    # Get similarity model
    model = get_model()

    # Check similarity with each example
    examples = [example_tasks.get("example")] + example_tasks.get("other_examples", [])
    for example in examples:
        similarity = model.text_similarity(task_desc, example)
        if similarity >= SIMILARITY_THRESHOLD:
            logger.debug(f"Task similar to example with score {similarity:.2f}")
            logger.debug(f"Task: {task_desc}")
            logger.debug(f"Example: {example}")
            return True

    return False


async_classify_target_user = sync_to_async(classify_target_user)
