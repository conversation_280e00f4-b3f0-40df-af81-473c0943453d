"""The guidance flow for GA started from 2025/5/26

changes:
  1. involve new component SingleChoiceWithTextComponent to let user choose platform or input product web page.
"""

import asyncio
import json
from logging import getLogger
from typing import Any

from agent.agents.influencer import InfluencerSchemaInferrerAgent
from agent.agents.research import (
    SchemaInferrerAgent,
    Schema,
)
from agent.config.agent import InfluencerFinderAgentConfig, ResearchAgentConfig
from agent.observer import Observer
from aiflow.engine import (
    CustomizedFlowComponent,
    TextWithExampleInputFlowComponent,
    SingleChoiceWithTextComponent,
)
from aiflow.enum import Method
from aiflow.models import AIFlow
from aiflow.observer import AIFlowObserver

logger = getLogger(__name__)


# [min, default, plan_limit, max]
plan_to_entity_range = {
    "free": [5, 30, 30, 200],
    "pro": [5, 50, 120, 200],
    "business": [5, 50, 200, 200],
}


def get_create_task_flow_spec(plan: str) -> dict:
    return {
        "name": "create_task_guide",
        "steps": [
            # 1st step, let user input his/her task purpose
            {
                "name": "user_select_case",
                "component": {
                    # component general attributes
                    "type": SingleChoiceWithTextComponent.type,
                    "submit": ["next"],
                    "input": [],
                    "output": ["usecase", "prod_intro"],
                    "title": "What kind of influencers you want to find?",
                    # component specific attributes
                    "text_caption": "I have no idea. Let me provide product URL/instruction. Generate for me in next step.",
                    "text_desc": "We will analyze your product to generate task description in next step. (If you’re providing instructions, the more detail you include, the better we can assist you.)",
                    "text_default": "",
                    "text_place_holder": "https://company.com/product/instruction",
                    "choice_caption": "I'm pretty sure. Let me input in next step.",
                    "choice_desc": "In the next step, you will be asked to describe what kind of influencers you wish to target.",
                    "choices": [
                        {
                            "index": 1,
                            "key": "find_influencers_tiktok",
                            "value": "TikTok",
                            "desc": "Find target TikTok influencers.",
                        },
                        {
                            "index": 2,
                            "key": "find_influencers_youtube",
                            "value": "Youtube",
                            "desc": "Find target YouTube influencers.",
                        },
                        {
                            "index": 3,
                            "key": "find_influencers_instagram",
                            "value": "Instagram",
                            "desc": "Find target Instagram influencers.",
                        },
                        {
                            "index": 4,
                            "key": "find_influencers_linkedin",
                            "value": "LinkedIn",
                            "desc": "Find target LinkedIn influencers. (focus on engagement about how many people and what kind of people this person can influence)",
                        },
                        # {
                        #     "index": 5,
                        #     "key": "find_people_linkedin",
                        #     "value": "LinkedIn People",
                        #     "desc": "Find target LinkedIn people. (focus on person himself/herself about industry, company, domain, skill, experience and etc.)",
                        # },
                    ],
                },
                "action": {
                    "name": "select_case",
                    "handler": "aiflow.flows.guide_v3.user_select_case",
                    "input": ["usecase", "prod_intro"],
                    "output": [
                        "case_title",
                        "case_default_desc",
                        "case_examples",
                        "case_type",
                        "case_platform",
                        "tip",
                    ],
                },
            },
            # 2rd step, describe task
            {
                "name": "describe_task",
                "component": {
                    # component general attributes
                    "type": TextWithExampleInputFlowComponent.type,
                    "submit": ["next"],
                    "input": ["tip"],
                    "output": ["task_desc"],
                    "title": "${case_title}",
                    # component specific attributes
                    "default": "${case_default_desc}",
                    "examples": "${case_examples}",
                },
                "action": {
                    "name": "generate_columns",
                    "handler": "aiflow.flows.guide_v3.generate_schema",
                    "input": ["task_desc"],
                    "output": [
                        "task_short_title",
                        "task_schema",
                        "target_entity_definition",
                    ],
                },
            },
            # 3nd step, let user choose enrichment conditions
            {
                "name": "choose_how_to_enrich",
                "component": {
                    # component general attributes
                    "type": CustomizedFlowComponent.type,
                    "submit": ["next"],
                    "input": ["task_desc", "task_short_title"],
                    "output": [
                        "criteria",
                        "enrich_entities_limit",
                        "enrich_entities_past_days",
                    ],
                    "title": "",
                    # component specific attributes
                    "attributes": {
                        "criteria": "${task_schema.criteria}",
                        "criteria_can_add_remove": True,
                        "entity_range": plan_to_entity_range.get(plan, "free"),
                        "entity_past_days": 30,
                        "task_short_title": "${task_short_title}",
                    },
                },
                "action": {
                    "name": "how_to_enrich",
                    "handler": "aiflow.flows.guide_v3.how_to_enrich",
                    "input": [
                        "criteria",
                        "task_schema",
                    ],
                    "output": [
                        "task_schema",
                    ],
                },
            },
        ],
    }


async def user_select_case(
    aiflow: AIFlow,
    observer: Observer,
    usecase: str,
    prod_intro: str,
    **kwargs: Any,
) -> tuple[str, str, list[str]]:
    case = key_to_cases.get(usecase, {})
    if usecase.startswith("find_influencers"):
        case_type = "find_influencers"
        case_platform = usecase.split("_")[-1]
    else:
        case_type = usecase
        case_platform = ""  # TODO: fill if required. So far no need.

    task_desc = ""
    task_tip = ""
    prod_intro = prod_intro.strip()
    if prod_intro:
        from aiflow.agents.InfluencerAddressingAgent import (
            InfluencerAddressingAgentConfig,
            InfluencerAddressingAgent,
        )

        # Create and run the agent
        config = InfluencerAddressingAgentConfig()
        agent = InfluencerAddressingAgent(config=config, observer=observer)

        try:
            # Run the agent
            result = await agent.run(platform=case_platform, prod_intro=prod_intro)
            task_desc = result[0]
            reasons = result[1:]
            if reasons:
                logger.info(
                    f"Reasons for generated influencer description: {json.dumps(reasons, indent=2)}"
                )
        except Exception as e:
            logger.exception(f"Error running InfluencerAddressingAgent: {e}")

    if prod_intro:
        if task_desc:
            case["example"] = task_desc
            task_tip = "We generated the task description for you by analyzing your product. Modify it if you feel it's not accurate."
        else:
            task_tip = "We encountered a problem while analyzing your product. Please close and start a new task, or try the examples."

    return (  # type: ignore
        case.get("title", ""),
        case.get("example", ""),
        case.get("other_examples", list()),
        case_type,
        case_platform,
        task_tip,
    )


async def how_to_enrich(
    aiflow: AIFlow,
    observer: Observer,
    criteria: list[str],
    task_schema: Schema,
    **kwargs: Any,
) -> tuple:
    # check if exceed the plan limit
    enrich_entities_limit = kwargs.get("enrich_entities_limit")
    for step in aiflow.steps:
        if step.get("action", {}).get("name") == "how_to_enrich":
            plan_entity_range = (
                step.get("component", {}).get("attributes", {}).get("entity_range", [])
            )
            if len(plan_entity_range) >= 3:
                # compatible 3 numbers (got max) or 4 numbers (got limit)
                plan_entity_limit = plan_entity_range[2]
                if enrich_entities_limit > plan_entity_limit:
                    kwargs["enrich_entities_limit"] = plan_entity_limit
                    s = f"Try finding {enrich_entities_limit} influencers which is exceeded plan limit {plan_entity_limit}. Changed to limit."
                    logger.error(s)
            break

    task_schema.criteria = criteria
    return (task_schema,)


async def generate_schema(
    aiflow: AIFlow,
    observer: Observer,
    task_desc: str,
    **kwargs: Any,
) -> tuple:
    assert isinstance(observer, AIFlowObserver)

    await observer.notify_text_card(
        title="Your Task",
        text=task_desc,
        pin=True,
    )

    usecase = kwargs.get("usecase", "")
    if usecase in [
        "find_influencers_tiktok",
        "find_influencers_instagram",
        "find_influencers_youtube",
        "find_influencers_linkedin",
    ]:
        platform = usecase.split("_")[-1]
        config = InfluencerFinderAgentConfig(platform=platform)
        # if platform == "linkedin":
        #     config.llm_model_name = "gpt-4o"
        agent = InfluencerSchemaInferrerAgent(
            config=config,
            observer=observer,
        )
        task_schema, task_short_title, target_entity_definition = await agent.run(
            task=task_desc
        )
    else:
        agent = SchemaInferrerAgent(
            config=ResearchAgentConfig(),
            observer=observer,
        )
        task_schema, task_short_title, target_entity_definition = await agent.run(
            task=task_desc
        )
        await observer.notify_text_card(
            text=f"🎯 I've defined each row as: {target_entity_definition}"
        )

    enrich_method = kwargs.get("enrich_key_of_method", "")

    columns = []
    for col in task_schema.fields:
        columns.append(
            {
                "name": col.name,
                "description": col.description or "",
                "editable": col.editable,
            }
        )
        await observer.notify_add_column(
            column_id=col.name,
            column_description=col.description or "",
            column_width=200 if col.name in ["name", "url"] else 320,
            cell_editable=enrich_method == Method.ENRICH_EXISTING,
            editable=col.editable,
        )
        await asyncio.sleep(0.2)

    return task_short_title, task_schema, target_entity_definition


key_to_cases = {
    "company_research": {
        "title": "Describe your company research task",
        "example": "Build a comprehensive list of commercial HVAC companies located in the United States. This list should include company names, addresses, contact information, and management team information.",
        "other_examples": [
            "Identify AI startups in Silicon Valley that have partnerships with major tech giants. Include their names, websites, main products, and what kind of partnerships they have.",
            "Build a list of the largest manufacturing companies that are integrating digital and advanced technologies into their operations. Please gather data on these companies' technological initiatives, the impact on their manufacturing processes, as well as top operations executives.",
        ],
    },
    "leads_generation": {
        "title": "Describe your lead generation task",
        "example": "Build a comprehensive list of commercial HVAC companies located in the United States. This list should include company names, addresses, contact information, and management team information.",
        "other_examples": [
            "Make a list of the small live music venues (bars, nightclubs, event spaces, etc.) in NYC and do online research to find the size of the venue, number of shows per month, type of music, logos, etc.",
            "Create a list of at least 100+ teams in North America that do search and rescue. Include their contact emails. Ground Search and Rescue activities are primarily conducted by volunteer teams that may have a website or social media (often Facebook) presence.",
        ],
    },
    "market_research": {
        "title": "Describe your market research task",
        "example": "Build a comprehensive list of commercial HVAC companies located in the United States. This list should include company names, addresses, contact information, and management team information.",
        "other_examples": [
            "Identify AI startups in Silicon Valley that have partnerships with major tech giants. Include their names, websites, main products, and what kind of partnerships they have.",
            "Build a list of the largest manufacturing companies that are integrating digital and advanced technologies into their operations. Please gather data on these companies' technological initiatives, the impact on their manufacturing processes, as well as top operations executives.",
        ],
    },
    "find_influencers_tiktok": {
        "title": "Describe your TikTok Influencers finding task",
        "example": "Find TikTok influencers who have talked about AI companions or mental health apps, are suitable for promoting my AI friend app, with 50k - 5M followers",
        "other_examples": [
            "Find tiktok influencers in USA with english content who are females, in their late 20s, attractive(important), uploads mostly makeup or beauty related content.",
            "Find TikTok influencers who are moms with toddlers or teenagers and might be able to incorporate skincare products into their content that with kids and have 50k-1M followers, with german speaking audience (preferably in Germany or Austria).",
        ],
    },
    "find_influencers_instagram": {
        "title": "Describe your Instagram Influencers finding task",
        "example": "Find Instagram influencers who are moms with kids, might be able to incorporate skincare products into their content and have 5k-1M followers",
        "other_examples": [
            "Find instagram influencers who post about spirituality, astrology, tarot readings, psychic services or witchcraft. They engage their audience with questions, live sessions, or giveaways. In English speaking or spiritually engaged countries.",
            "Find Instagram influencers who are arabic females in UAE, preferably speak Arabic. Could be interior designers, lifestyle or fashion bloggers. 20K - 300K followers.",
        ],
    },
    "find_influencers_youtube": {
        "title": "Describe your YouTube Influencers finding task",
        "example": "Find YouTube influencers who have talked about AI companion or mental health apps or related fields, good for promoting my AI friend app and have 50k-5M followers, in US.",
        "other_examples": [
            "Find me YouTube influencers who mostly post about makeup or beauty content and have 10k-100K followers, in US.",
            "Find me YouTube influencers who are moms with toddlers or teenagers and might be able to incorporate skincare products into their content that with kids and have 50k-1M followers, in Germany.",
        ],
    },
    "find_influencers_linkedin": {
        "title": "Describe your LinkedIn Influencer finding task",
        "example": "Find LinkedIn influencers who have talked about AI companion or mental health apps or related fields, good for promoting my AI friend app and have 1k-50K followers, in Bay Area.",
        "other_examples": [
            "Find me LinkedIn influencers who mostly post about makeup or beauty content and have 100-10K followers, in North America or Euro.",
            "Find me LinkedIn influencers who are open source evangelist to prompt our project, world wide.",
        ],
    },
    "find_people": {
        "title": "Describe your LinkedIn People finding task",
        "example": "Find medical researchers at publicly listed pharmaceutical companies that have recently announced cancer-related drugs or treatment plans.",
        "other_examples": [
            "Find professionals who are head of marketing in startups which raised founding in 6 months .",
            # "Find tech executives or consultants in senior positions at Fortune 500 companies who specialize in digital transformation.",
            "Find software engineering directors and CTOs from German tech companies in the DACH region.",
        ],
    },
    "web_scraping": {
        "title": "Describe your web scraping task",
        "example": "Scrape all men's shoes information from allbirds.com. Please include product detail page URL, product name, price, color, size, etc.",
        "other_examples": [
            "Extract all job postings from the careers pages of AI companies in the financial industry, including title, location, requirements and posting date.",
            "Scrape all the AI companies info from the AI software listing website soverin.ai, include company name, tagline, pricing and social links",
        ],
    },
    "other": {
        "title": "Describe your data collection task",
        "example": "Build a comprehensive list of commercial HVAC companies located in the United States. This list should include company names, addresses, contact information, and management team information.",
        "other_examples": [
            "Pull all men's shoe information from Allbirds. Please include product detail page URL, product name, price, color, size, etc.",
            "Find me Twitter/X accounts of directors of sales at mid-sized SaaS companies in the data infrastructure industry.",
        ],
    },
}
