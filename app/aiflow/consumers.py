# chat/consumers.py
import asyncio
import functools
import json
import logging
from asyncio import CancelledError, InvalidStateError, Task
from typing import Any, Coroutine, Dict, Optional

from channels.generic.websocket import AsyncJsonWebsocketConsumer
from django.conf import settings
from django.contrib.auth.models import User

from agent.agents.influencer import InfluencerFinderAgent
from agent.agents.linkedin import LinkedinPersonFinderAgent
from agent.agents.research import Entity
from agent.authentication import (
    JwtSecret,
    is_credits_exhausted,
    check_subscription,
    SubscriptionExpiredError,
    TrialExpiredError,
    SubscriptionRequiredError,
)
from agent.observer import Observer
from aiflow.engine import FlowSpec
from aiflow.enum import TaskStatus
from aiflow.flows.guide_v2 import get_create_task_flow_spec
from aiflow.models import AIFlow
from aiflow.observer import AIFlowDbNotifier, AIFlowObserver
from aiflow.websocket_notifier import AIFlowWebsocketNotifier
from common.utils.asyncioutil import RunningTasksManager
from payment.models import Subscription

logger = logging.getLogger(__name__)


# Initialize the task manager
running_tasks_manager = RunningTasksManager()

id_to_flow_engines: dict[int, FlowSpec] = dict()


class AIFlowConsumer(AsyncJsonWebsocketConsumer):
    _CTRL_PING = "PING"
    _CTRL_STOP = "STOP"
    _CTRL_TIMEOUT = "TIMEOUT"
    _CTRL_ERROR = "ERROR"
    flow_id: Optional[int] = None
    flow_engine: FlowSpec | None = None
    user: User | None = None

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(args, kwargs)
        logger.debug(f"consumer {hash(self)}: created. ")

    async def connect(self: "AIFlowConsumer") -> None:
        self._trace("client connected.")

        query_string = self.scope["query_string"].decode()
        query_dict = dict(
            [query.split("=") for query in query_string.split("&") if query]
        )
        if "jwt_token" not in query_dict:
            await self.close()
            return
        try:
            payload = JwtSecret.verify_token(query_dict.get("jwt_token", ""))
        except Exception as e:
            logger.error(e)
            payload = {}

        userid = payload.get("sub", "")

        if userid is None or userid == "":
            await self.close()
            return

        try:
            self.user = await User.objects.aget(id=userid)
        except User.DoesNotExist:
            await self.close()
            return

        await self.accept()

        # Check if the user's credits are exhausted
        if (
            await is_credits_exhausted(self.user)
            and query_dict.get("channel") != "broadcast"
            and not query_dict.get("flow_id")
        ):
            await self.send_json(
                {
                    "error_code": "CREDITS_EXHAUSTED",
                    "message": "Your credit points for the current period are exhausted.",
                }
            )
            await self.close()
            return
        # Users may open multiple tabs/browsers simultaneously
        # A persistent broadcast channel is required to synchronize messages across all sessions
        # This channel is responsible for sending messages only, not receiving them
        # This ensures all open sessions receive the latest state updates
        await self.join_user_channel(user_id=self.user.id)

        flow_id = query_dict.get("flow_id", "")

        # Rejoin the room to allow browser to resume receiving messages
        if flow_id:
            self.flow_id = int(flow_id)
            self.flow_engine = id_to_flow_engines.get(self.flow_id)
            self._trace(f"{self.flow_id} - {self.flow_engine}")
            await self.join_flow_channel(flow_id=self.flow_id)

        # self._trace(json.dumps(id_to_flow_engines, indent=2, default=str))

    async def receive(self, text_data: str) -> None:  # noqa: C901
        self._trace(f"received message. {text_data}")

        if not self.user:
            logger.debug("no user attached for current WS connection.")
            await self.send_json(
                {
                    "error_code": "AIFLOW_REQUEST_INVALID",
                    "message": "You are not logged in.",
                }
            )
            return

        # self._trace(f"groups: {self.groups}")
        received_dict = json.loads(text_data)
        self._trace(json.dumps(id_to_flow_engines, indent=2, default=str))

        # the client requests to end the task
        if received_dict.get("operation") == self._CTRL_STOP:
            flow_id = self.flow_id or received_dict.get("flow_id")
            logger.warning("=====================================================")
            logger.warning(f"     received {self._CTRL_STOP} (flow_id={flow_id})")
            logger.warning("=====================================================")
            self._trace(
                f'self.flow_id={self.flow_id}, data[flow_id]={received_dict.get("flow_id")}'
            )
            if flow_id:
                await self.stop_task_by_flow_id(int(flow_id))
            else:
                logger.warning("ignore: STOP message without flow_id.")
            return

        if received_dict.get("current_step") == "INIT":
            # if the task count exceeds the limit, stop processing
            if await self.check_task_limit() is False:
                return

            # handle resume
            resume_flow_id = received_dict.get("resume_flow")
            if resume_flow_id:
                await self._handle_resume(int(resume_flow_id))
                return

            aiflow = await AIFlow.objects.acreate(user=self.user)
            self.flow_id = aiflow.id
            assert self.flow_id is not None

            plan = Subscription.get_user_current_plan(self.user.id)
            flow_spec = get_create_task_flow_spec(plan)
            flow_engine = FlowSpec.from_dict(flow_spec)
            id_to_flow_engines[self.flow_id] = flow_engine
            flow_engine.context["aiflow"] = aiflow
            self.flow_engine = flow_engine
            await self.join_flow_channel(flow_id=self.flow_id)

        else:
            if self.flow_id is None or not self.flow_engine:
                self._trace("no flow_id") if self.flow_id is None else None
                self._trace("no flow_engine") if not self.flow_engine else None
                await self.send_json(
                    {
                        "error_code": "AIFLOW_REQUEST_INVALID",
                        "message": "Your request is invalid which has not a flow_id or a flow_engine.",
                    }
                )
                return
            aiflow = await AIFlow.objects.aget(id=self.flow_id, user=self.user)
            self.flow_engine.context["aiflow"] = aiflow

        observer = AIFlowObserver(
            notifiers=[
                AIFlowDbNotifier(aiflow=aiflow),
                AIFlowWebsocketNotifier(
                    aiflow=aiflow,
                    channel_group_name=self.ws_flow_channel_group_name,
                    broadcast_group_name=self.ws_user_channel_group_name,
                ),
            ]
        )
        self.flow_engine.context["observer"] = observer

        if aiflow.status == TaskStatus.RUNNING.value:
            logger.info("the task is running. message not processed.")
            return

        if received_dict.get("current_step") == "INIT":
            if not await self.has_permit(self.user):
                return
            # when the flow is initiated, notify the client
            await observer.notify({"card_type": "flow_initiated"})
        else:
            await self.flow_engine.async_go(cur_step_payload=received_dict)

        await self._save_step_and_context(aiflow)

        # after flow_engine.async_go(), cur_step is the next step
        if not self.flow_engine.is_finished:
            self.flow_engine.resolve_vars()
            await observer.notify(
                self.flow_engine.cur_step.render_fe_dict(**self.flow_engine.context)
            )
            return

        task_key = self.get_task_key(aiflow.id)
        task = asyncio.create_task(
            self._answer(**self.flow_engine.context),
            name=task_key,
        )
        task.add_done_callback(functools.partial(self.task_done, task_key, observer))
        await running_tasks_manager.add_task(str(task_key), task)

    async def _handle_resume(self, flow_id: int) -> None:
        aiflow = await AIFlow.objects.filter(user=self.user, id=flow_id).afirst()
        if not aiflow:
            logger.warning(
                f"Fail to resume flow {flow_id}. Not found or it's not paused."
            )
            return

        if not aiflow.can_continue:
            logger.warning(
                f"You can only resume flow {flow_id} due to it's not DONE or has 100% results."
            )
            return

        self.flow_id = aiflow.id
        assert self.user and self.flow_id
        await self.join_flow_channel(flow_id=self.flow_id)
        observer = AIFlowObserver(
            notifiers=[
                AIFlowDbNotifier(aiflow=aiflow),
                AIFlowWebsocketNotifier(
                    aiflow=aiflow,
                    channel_group_name=self.ws_flow_channel_group_name,
                    broadcast_group_name=self.ws_user_channel_group_name,
                ),
            ]
        )

        if not await self.has_permit(self.user):
            return

        context = aiflow.context
        task_key = self.get_task_key(aiflow.id)
        task = asyncio.create_task(
            self._answer(aiflow=aiflow, observer=observer, is_resumed=True, **context),
            name=task_key,
        )
        task.add_done_callback(functools.partial(self.task_done, task_key, observer))
        await running_tasks_manager.add_task(str(task_key), task)

        logger.warning(f"=====   Flow {flow_id} Resumed   =====")

    @staticmethod
    async def _handle_resumed_before_answer(
        flow: AIFlow, agent: InfluencerFinderAgent
    ) -> None:
        """fill agent with existed data before run it."""

        # config agent for resuming
        agent.is_resumed = True
        agent.re_find_not_finished_if_resumed = True

        # make flow expended
        await AIFlow.objects.filter(id=flow.id).aupdate(is_expended=True)

        # generate a relevance dict for check rows
        relevance_d = {
            e["row_id"]: e["relevance"] for e in flow.data.get("evaluations", [])
        }

        # fill agent the existed data
        agent.urls_evaluated = set(flow.evaluated)
        logger.debug(f"resume (evaluated urls): {json.dumps(flow.evaluated, indent=2)}")
        for row in flow.data.get("rows", []):
            if (
                agent.re_find_not_finished_if_resumed
                and agent.config.platform != "linkedin"
            ):
                if row["engagement_rate"].strip() in {"", "-", "calculating ..."} or (
                    "" in {row["name"], row["url"], row["followers"]}
                ):
                    if row["url"] in agent.urls_evaluated:
                        agent.urls_evaluated.remove(row["url"])
                    continue

            row_id = row["row_id"]
            agent.urls_evaluated.add(row["url"])
            if relevance_d.get(row_id) in {"high", "medium"}:
                agent.enriched_entity_count += 1
            if agent.config.platform == "linkedin":
                entity = Entity(
                    name=row["name"],
                    url=row["url"],
                    field_data=[
                        {
                            "field_name": "connections",
                            "field_value": row["connections"],
                        },
                        {"field_name": "followers", "field_value": row["followers"]},
                        {"field_name": "title", "field_value": row["title"]},
                        {"field_name": "company", "field_value": row["company"]},
                        {"field_name": "headline", "field_value": row["headline"]},
                        {"field_name": "experience", "field_value": row["experience"]},
                        {"field_name": "skills", "field_value": row["skills"]},
                        {
                            "field_name": "email",
                            "field_value": row["email"],
                        },
                    ],
                )
            else:
                entity = Entity(
                    name=row["name"],
                    url=row["url"],
                    field_data=[
                        {"field_name": "followers", "field_value": row["followers"]},
                        {
                            "field_name": "engagement_rate",
                            "field_value": row["engagement_rate"],
                        },
                        {
                            "field_name": "content_focus",
                            "field_value": row["content_focus"],
                        },
                        {
                            "field_name": "related_media_posted",
                            "field_value": row["related_media_posted"],
                        },
                        {"field_name": "email", "field_value": row["email"]},
                    ],
                )
            agent.final_table.append(entity)

    async def _save_step_and_context(self, aiflow: AIFlow) -> None:
        if not self.flow_engine:
            return
        aiflow.context = {
            k: v.to_dict() if hasattr(v, "to_dict") else v
            for k, v in self.flow_engine.context.items()
            if k not in {"aiflow", "observer"}
        }
        if not self.flow_engine.is_finished:
            aiflow.steps.append(self.flow_engine.cur_step.to_dict())
        await aiflow.asave()

    @staticmethod
    async def _answer(
        aiflow: AIFlow,
        observer: Observer,
        is_resumed: bool = False,
        **kwargs: Any,
    ) -> None:
        """answer ai flow"""

        assert isinstance(observer, AIFlowObserver)

        from agent.agents.research import ResearchAgent
        from agent.agents.influencer import InfluencerFinderAgent
        from agent.config.agent import ResearchAgentConfig, InfluencerFinderAgentConfig

        try:
            # rows = aiflow.context.get("enrich_user_row_names", [])
            # logger.info(f"Starting research with rows: {rows}")

            # user selected case (instagram/tiktok/youtube influencer, web scraping, leads ...)
            usecase = kwargs.get("usecase")
            # LLM generated schema for the output grid (columns)
            schema = kwargs.get("task_schema")
            # user entered task description (used as prompt)
            task_desc = kwargs.get("task_desc")
            # aiflow.method
            # enrich_method = kwargs.get("enrich_key_of_method")
            # limit of generated entities count
            enrich_entities_limit = kwargs.get("enrich_entities_limit")
            # how may days back then entities (which were generated) will not be generated again.
            # enrich_entities_past_days = kwargs.get("enrich_entities_past_days")
            # aiflow.target_entity_definition
            target_entity_definition = kwargs.get("target_entity_definition")

            task_type = kwargs.get("case_type")
            task_platform = kwargs.get("case_platform")
            task_short_title = kwargs.get("task_short_title") or ""

            aiflow.status = TaskStatus.RUNNING.value
            if not is_resumed:
                aiflow.schema = (
                    (schema if isinstance(schema, dict) else schema.to_dict())
                    if schema
                    else aiflow.schema
                )
                aiflow.type = task_type
                aiflow.platform = task_platform
            await aiflow.asave()

            agent = (
                # NOTE(ruiwang|2024-12-15): a hack for finding influencers. Make it generic later
                InfluencerFinderAgent(
                    config=InfluencerFinderAgentConfig(
                        platform=usecase.split("_")[2],
                        entities_limit=enrich_entities_limit,
                        # no_entities_generated_in_past_days=enrich_entities_past_days,
                    ),
                    task=task_desc,
                    observer=observer,
                    target_entity_definition=target_entity_definition,
                    task_schema=schema,
                    streaming=False,
                )
                if usecase
                in [
                    "find_influencers_tiktok",
                    "find_influencers_instagram",
                    "find_influencers_youtube",
                    "find_influencers_linkedin",
                ]
                else LinkedinPersonFinderAgent(
                    config=InfluencerFinderAgentConfig(
                        platform=usecase.split("_")[2],
                        entities_limit=enrich_entities_limit,
                        # no_entities_generated_in_past_days=enrich_entities_past_days,
                    ),
                    task=task_desc,
                    observer=observer,
                    target_entity_definition=target_entity_definition,
                    task_schema=schema,
                    streaming=False,
                )
                if usecase
                in [
                    "find_people",
                ]
                else ResearchAgent(
                    config=ResearchAgentConfig(entities_limit=enrich_entities_limit),
                    task=task_desc,
                    observer=observer,
                    target_entity_definition=target_entity_definition,
                    task_schema=schema,
                    streaming=False,
                )
            )

            if is_resumed:
                await AIFlowConsumer._handle_resumed_before_answer(
                    flow=aiflow, agent=agent
                )

            user = await aiflow.async_get_user()
            await agent.run(
                username=user.username,
                # existing_data={"rows": []},
                platform=task_platform,
                task_short_title=task_short_title,
            )

            await observer.notify_text_card(
                title="Research Completed",
                text="Your research is complete.",
            )
            await observer.notify_status("Research Completed")
        except Exception as e:
            logger.exception(e)

        try:
            await observer.broadcast(
                {"message": "TASK_COMPLETE", "can_continue": aiflow.can_continue}
            )
            id_to_flow_engines.pop(aiflow.id)
        except Exception as e:
            logger.warning(f"error broadcasting TASK_COMPLETE to {aiflow.id}. {e}")

    async def disconnect(self: "AIFlowConsumer", close_code: int) -> None:
        # logger.info(f"aiflow task ({self.flow_id}) disconnected ({close_code})")
        self._trace(f"disconnect:({close_code})")
        if self.flow_id:
            try:
                aiflow = await AIFlow.objects.aget(id=self.flow_id, user=self.user)
                if aiflow.status == TaskStatus.INIT:
                    await aiflow.adelete()
                await self.channel_layer.group_discard(
                    self.ws_flow_channel_group_name, self.channel_name
                )
            except AIFlow.DoesNotExist:
                pass
        self._trace(f"groups: {self.groups}")

    @property
    def ws_flow_channel_group_name(self) -> str:
        assert self.user and self.flow_id
        return f"ws_user_{self.user.id}_flow_{self.flow_id}"

    @property
    def ws_user_channel_group_name(self) -> str:
        assert self.user
        return f"ws_user_{self.user.id}"

    # return True if the task limit is not exceeded, otherwise return False
    async def check_task_limit(self: "AIFlowConsumer") -> bool:
        running_tasks = [
            {
                "id": o.id,
                "title": o.context.get("task_short_title"),
                "desc": o.context.get("task_desc"),
                "progress": o.progress,
            }
            async for o in AIFlow.objects.filter(
                user=self.user, status=TaskStatus.RUNNING.value
            )  # [: settings.AIFLOW_REQUEST_LIMIT+1]
        ]
        if len(running_tasks) >= settings.AIFLOW_REQUEST_LIMIT:
            await self.send_json(
                {
                    "error_code": "AIFLOW_REQUEST_LIMIT",
                    "message": f"Sorry I can only handle {settings.AIFLOW_REQUEST_LIMIT} tasks at a time. Please let them finish or stop some tasks before starting a new one.",
                    "running_tasks": running_tasks,
                }
            )
            return False
        return True

    async def has_permit(self, user: User) -> bool:
        """check if the user can run a search"""

        is_permitted = True
        code = ""
        message = ""

        # check if user has enough credits
        if await is_credits_exhausted(user):
            is_permitted = False
            code = "CREDITS_EXHAUSTED"
            message = "Out of credits."
        else:
            # if user has enough credits
            # check if user has subscription/trial
            try:
                await check_subscription(self.user)
            except SubscriptionRequiredError:
                # no subscription, but has credits, can use
                pass
            except SubscriptionExpiredError:
                is_permitted = False
                code = "SUBSCRIPTION_EXPIRED"
                message = "Your subscription is expired."
            except TrialExpiredError:
                is_permitted = False
                code = "TRIAL_EXPIRED"
                message = "Your trial is expired."

        if not is_permitted:
            await self.send_json({"error_code": code, "message": message})
            await self.close()
        return is_permitted

    async def join_flow_channel(self: "AIFlowConsumer", flow_id: int) -> None:
        assert flow_id >= 0 and (
            self.flow_id is None or self.flow_id < 0 or self.flow_id == flow_id
        )

        try:
            await AIFlow.objects.aget(id=flow_id, user=self.user)
        except AIFlow.DoesNotExist:
            self._trace(f"{flow_id} is not {self.user}'s flow.")
            await self.close()

        self._trace(f"joining channel {self.ws_flow_channel_group_name}.")

        await self.channel_layer.group_add(
            self.ws_flow_channel_group_name, self.channel_name
        )

    async def join_user_channel(self: "AIFlowConsumer", user_id: int) -> None:
        self._trace(f"joining channel {self.ws_user_channel_group_name}.")
        await self.channel_layer.group_add(
            self.ws_user_channel_group_name, self.channel_name
        )

    def get_task_key(self, flow_id: int) -> str:
        assert self.user
        return f"flow_{self.user.id}_{flow_id}"

    async def stop_task_by_flow_id(self: "AIFlowConsumer", flow_id: int) -> None:
        # try:
        #     flow = await AIFlow.objects.aget(id=flow_id)
        # except AIFlow.DoesNotExist:
        #     flow = None

        await self.channel_layer.group_send(
            self.ws_user_channel_group_name,
            {
                "type": "add.response",
                "message": "TASK_STOPPED",
                "flow_uuid": flow_id,
                # "can_continue": flow.can_continue if flow else False,
            },
        )
        if self.user:
            await self._async_stop_charge_settle(flow_id, user_id=self.user.id)
        else:
            logger.warning("current consumer has no user, so can not stop")
        await self.cancel_task(self.get_task_key(flow_id))

    def task_done(self, key: str, observer: AIFlowObserver, task: Task) -> None:
        if key in running_tasks_manager.running_tasks:
            _task = running_tasks_manager.running_tasks.pop(key)
            assert task == _task

        # we set key to task.name when starting
        _, user_id_str, flow_id_str = task.get_name().split("_")
        flow_id: int = int(flow_id_str)
        user_id: int = int(user_id_str)

        try:
            ex = task.exception()
        except (CancelledError, InvalidStateError) as e:
            ex = e
        if ex is None:
            logger.info(f"Task {task.get_name()} finished.")
        else:
            if isinstance(ex, TimeoutError):
                logger.warning(f"Task {task.get_name()} timeout.")
            elif isinstance(ex, CancelledError):
                logger.warning(f"Task {task.get_name()} cancelled.")
            else:
                logger.exception(f"Task {task.get_name()} failed.", exc_info=ex)

        asyncio.create_task(self._async_stop_charge_settle(flow_id, user_id))

    async def _async_stop_charge_settle(self, flow_id: int, user_id: int) -> None:
        """flow finalize & settle the charge"""
        try:
            aiflow = await AIFlow.objects.prefetch_related("user").aget(
                id=flow_id, user_id=user_id
            )
            if aiflow.status != TaskStatus.DONE.value:
                aiflow.status = TaskStatus.DONE.value
                await aiflow.asave()
            await aiflow.async_charge_and_settle()
        except Exception as e:
            logger.exception(e)
            logger.warning(f"error charging/settling/updating flow {flow_id}. {e}")

    @staticmethod
    async def cancel_task(key: str) -> None:
        task = await running_tasks_manager.get_task(key)
        if task:
            logger.info(f"cancelling task {task.get_name()} ...")
            task.cancel("cancelled.")
            await running_tasks_manager.remove_task(key)
        else:
            logger.warning(f'task "{key}" not exists.')

    async def with_timeout(self, coro: Coroutine, timeout: int = 60) -> Coroutine:
        try:
            async with asyncio.timeout(timeout):
                return await coro
        except TimeoutError:
            await self.send(self._CTRL_TIMEOUT)
            await self.disconnect(1000)
            raise

    async def add_response(self: "AIFlowConsumer", payload: Dict[str, Any]) -> None:
        try:
            await self.send_json(
                {
                    "flow_uuid": payload.get("flow_uuid", ""),
                    **payload,
                }
            )
        except Exception as e:
            logger.error(e)

    async def add_column(self: "AIFlowConsumer", payload: Dict[str, Any]) -> None:
        await self.send_json(
            {
                "column_id": payload["column_id"],
                "column_width": payload["column_width"],
                "column_description": payload["column_description"],
                "flow_uuid": payload["flow_uuid"],
                "editable": payload["editable"],
                "cell_editable": payload["cell_editable"],
                "step_type": "add_column",
            }
        )

    async def add_cell(self: "AIFlowConsumer", payload: Dict[str, Any]) -> None:
        await self.send_json(
            {
                "row_id": payload["row_id"],
                "column_id": payload["column_id"],
                "value": payload["value"],
                "done": payload["done"],
                "flow_uuid": payload["flow_uuid"],
                "sources": payload["sources"],
                "step_type": "add_cell",
            }
        )

    async def placeholder(self: "AIFlowConsumer", payload: Dict[str, Any]) -> None:
        await self.send_json(
            {
                "key": payload["key"],
                "action": payload["action"],
                "flow_uuid": payload["flow_uuid"],
                "step_type": "placeholder",
                "name": payload["name"],
                "url": payload["url"],
                "text": payload["text"],
            }
        )

    async def add_evaluation(self: "AIFlowConsumer", payload: Dict[str, Any]) -> None:
        await self.send_json(
            {
                "row_id": payload["row_id"],
                "evaluation": payload["evaluation"],
                "flow_uuid": payload["flow_uuid"],
                "step_type": "add_evaluation",
            }
        )

    async def progress(self: "AIFlowConsumer", payload: Dict[str, Any]) -> None:
        await self.send_json(
            {
                "total": payload["total"],
                "current": payload["current"],
                "flow_uuid": payload["flow_uuid"],
                "step_type": "progress",
            }
        )

    async def operation(self: "AIFlowConsumer", payload: Dict[str, Any]) -> None:
        await self.send_json(
            {
                "flow_uuid": payload["flow_uuid"],
                "operation": payload["operation"],
            }
        )

    def _trace(self, msg: str, *args: Any, **kwargs: Any) -> Any:
        # stacks = traceback.extract_stack(limit=20)
        # stacks.reverse()
        # for stack in stacks:
        #     if stack.filename.startswith(__file__):
        #         loc = f"{stack.filename}:{stack.lineno}"
        #         break
        s = f"c {hash(self)}"
        if self.user is not None:
            s += f"|{self.user}({self.user.id})"
        if self.flow_id is not None:
            s += f"|{self.flow_id}"
        s += f"|: {msg}"
        return logger.debug(s, *args, **kwargs)
