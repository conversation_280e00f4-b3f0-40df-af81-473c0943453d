"""Django management command to run InfluencerAddressingAgent"""

import argparse
import asyncio
import json
from typing import Any

from django.core.management.base import BaseCommand

from agent.observer import Observer
from aiflow.agents.InfluencerAddressingAgent import (
    InfluencerAddressingAgent,
    InfluencerAddressingAgentConfig,
)
from scraper.base import INFLUENCER_PLATFORMS


class Command(BaseCommand):
    help = "Run InfluencerAddressingAgent to generate influencer targeting descriptions"

    def add_arguments(self, parser: argparse.ArgumentParser) -> None:
        parser.add_argument(
            "platform",
            type=str,
            choices=["instagram", "tiktok", "youtube", "linkedin"],
            help="The social media platform to target",
        )
        parser.add_argument(
            "product_intro",
            type=str,
            help="Product introduction text or URL to analyze",
        )

    async def async_handle(self, **kwargs: Any) -> None:
        platform = kwargs["platform"]
        product_intro = kwargs["product_intro"]

        config = InfluencerAddressingAgentConfig()

        # Create observer for logging
        observer = Observer()

        # Create and run the agent
        self.stdout.write(f"Running InfluencerAddressingAgent for {platform}...")
        self.stdout.write(f"Product introduction: {product_intro[:100]}...")

        agent = InfluencerAddressingAgent(config=config, observer=observer)

        try:
            # Run the agent
            result = await agent.run(platform=platform, prod_intro=product_intro)
            result = result[0] if isinstance(result, list) and result else str(result)

            # Display results
            self.stdout.write(self.style.SUCCESS("\nInfluencer targeting descriptions generated:"))
            self.stdout.write(result)

            # Display cost information if available
            if observer.llm_costs:
                self.stdout.write(f"\n{self.style.WARNING('Cost Information:')}")
                self.stdout.write(observer.get_llm_costs_str())

            # Display any errors if they occurred
            if observer.errors:
                self.stdout.write(f"\n{self.style.WARNING('Errors encountered:')}")
                for error_key, error_value in observer.errors.items():
                    self.stdout.write(f"  {error_key}: {error_value}")

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error running InfluencerAddressingAgent: {str(e)}")
            )
            raise

        self.stdout.write(
            self.style.SUCCESS("\nInfluencerAddressingAgent completed successfully!")
        )

    def handle(self, **kwargs: Any) -> None:
        asyncio.run(self.async_handle(**kwargs))
