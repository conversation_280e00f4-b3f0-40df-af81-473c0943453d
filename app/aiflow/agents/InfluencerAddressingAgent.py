import json
from typing import List

from pydantic import Field

from agent.agents.base import Agent
from agent.conf import LLM_MODEL
from agent.config.agent import AgentConfig
from agent.cost import LlmCostTracker
from agent.enum import CostCategory
from agent.llms import create_llm
from agent.prompts import create_prompt_builder


class InfluencerAddressingAgentConfig(AgentConfig):
    agent_name: str = Field(default="address_influencer")
    llm_model_name: str = Field(default=LLM_MODEL or "gpt-4o-mini")
    max_tokens: int = Field(default=200)


class InfluencerAddressingAgent(Agent):
    """This agent addresses influencers to be searched from a given product instruction"""

    config: InfluencerAddressingAgentConfig

    async def run(self, query: str) -> List[str]:
        prompt_msgs = create_prompt_builder(
            sys_prompt=_SYS_PROMPT,
            user_msg=query,
            llm_model_name=self.config.llm_model_name,
        ).build()
        params = {
            "log_prefix": self.config.agent_name,
            "observer": self.observer,
            "cost_tracker": LlmCostTracker(
                cost_category=CostCategory.LLM_CHAT_CONTEXT,
            ),
            "llm_model_name": self.config.llm_model_name,
            "max_tokens": self.config.max_tokens,
            "enforce_json_output": True,
        }
        llm_output = await create_llm(**params).chat_complete(prompt_msgs)  # type: ignore
        return json.loads(llm_output)["all_synonyms"]


_SYS_PROMPT = ""
