import logging
from typing import Any, Dict, List, Optional, Literal

from channels.layers import get_channel_layer

from aiflow.enum import AIFollowCardType
from aiflow.models import AI<PERSON><PERSON>, AIFlowCellPlaceholder, is_valid_email
from aiflow.observer import AIFlowNotifier

logger = logging.getLogger(__name__)


class AIFlowWebsocketNotifier(AIFlowNotifier):
    def __init__(
        self, aiflow: AIFlow, channel_group_name: str, broadcast_group_name: str
    ):
        self.aiflow = aiflow
        self.channel_group_name: str = channel_group_name
        self.broadcast_group_name: str = broadcast_group_name
        self.channel_layer = get_channel_layer()

    async def _send_channel(self, **kwargs: Any) -> None:
        event = {
            "flow_uuid": self.aiflow.id,
        }
        event.update(kwargs)
        await self.channel_layer.group_send(self.channel_group_name, event)

    async def _send_to_group(self, **kwargs: Any) -> None:
        event = {
            "flow_uuid": self.aiflow.id,
        }
        event.update(kwargs)
        await self.channel_layer.group_send(self.broadcast_group_name, event)

    async def broadcast(self, data: Dict) -> None:
        await self._send_to_group(type="add.response", **data)

    async def notify(self, data: Dict) -> None:
        await self._send_channel(type="add.response", **data)

    async def notify_text_card(
        self, text: str, title: Optional[str] = None, pin: Optional[bool] = False
    ) -> None:
        data = {
            "card_type": AIFollowCardType.TEXT_CARD,
            "text": text,
            "pin": pin,
        }
        if title:
            data["title"] = title
        await self.notify(data)

    async def notify_status(self, status: str) -> None:
        await self.notify(
            {
                "card_type": AIFollowCardType.OPERATION_STATUS,
                "status": status,
            }
        )

    async def notify_add_column(
        self,
        column_id: str,
        column_description: str,
        column_width: int,
        editable: bool,
        cell_editable: bool,
    ) -> None:
        await self._send_channel(
            type="add.column",
            column_id=column_id,
            column_description=column_description,
            editable=editable,
            cell_editable=cell_editable,
            column_width=column_width,
        )

    async def notify_add_cell(
        self,
        row_id: str,
        column_id: str,
        value: str,
        sources: Optional[List[Dict]] = [],
        done: bool = False,
    ) -> None:
        if column_id == "email":
            if is_valid_email(value):
                value = AIFlowCellPlaceholder.EMAIL
        await self._send_channel(
            type="add.cell",
            row_id=row_id,
            column_id=column_id,
            value=value,
            sources=sources,
            done=done,
        )

    async def notify_placeholder(
        self,
        key: str,
        action: Literal["add", "remove"],
        name: Optional[str] = "",
        url: Optional[str] = "",
        text: Optional[str] = "",
    ) -> None:
        await self._send_channel(
            type="placeholder", key=key, action=action, name=name, url=url, text=text
        )

    async def notify_evaluation(self, row_id: str, evaluation: dict) -> None:
        await self._send_channel(
            type="add.evaluation", row_id=row_id, evaluation=evaluation
        )

    async def notify_progress(self, total: int, current: int) -> None:
        await self._send_channel(
            type="progress", card_type="progress", total=total, current=current
        )
