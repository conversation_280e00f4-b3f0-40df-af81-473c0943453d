from datetime import datetime
from logging import getLogger
from typing import List

from django.core.exceptions import ValidationError
from django.db.models import Case, When, Value, BooleanField
from django.http.request import HttpRequest
from ninja import Query, Schema
from ninja.router import Router

from agent import conf
from agent.restapi.auth import Head<PERSON><PERSON><PERSON>, BearAuth
from agent.restapi.response import ApiResponse, ApiResults, PaginationSchema
from aiflow import conf as aiflow_conf
from aiflow.classifier import async_classify_target_user
from aiflow.enum import ChargeActionEnum, TaskStatus, charge_prices
from aiflow.models import (
    AIFlow,
    AIFlowCellPlaceholder,
    Charge,
    is_valid_email,
    Msg,
)
from aiflow.redeem import (
    async_redeem_code,
    RedeemCode,
)
from payment.models import CreditPtsCard
from payment.provider import StripePayment

logger = getLogger(__name__)

aiflow_router = Router(tags=["aiflows"], auth=[<PERSON>A<PERSON>(), HeaderAuth()])

payment = StripePayment(api_key=conf.STRIPE_SECRET_KEY)


@aiflow_router.get("/", summary="to list aiflow")
async def chat_list(
    request: HttpRequest,
    paging: Query[PaginationSchema],
) -> ApiResponse:
    base_queryset = AIFlow.objects.filter(
        user=request.user,
        status__in=[TaskStatus.DONE, TaskStatus.RUNNING],
        is_deleted=False,
    ).order_by("-id")
    paginated_queryset = await paging.filter(base_queryset)
    qs = paginated_queryset.values(
        "id",
        "context",
        "status",
        "progress",
        "created_at",
    )
    items = [item async for item in qs]
    response = ApiResults.SUCCESS(data=items)
    return await response.with_pagination(paging)


@aiflow_router.get(
    "/prices/default/",
    auth=None,
    summary="to retrieve the default price of a provider",
)
async def get_default_price(request: HttpRequest) -> ApiResponse:
    prices = payment._provider_list_prices(aiflow_conf.STRIPE_PRODUCT_ID)
    return ApiResults.SUCCESS(data=prices)


@aiflow_router.post(
    "/flow_status/",
    auth=None,
    summary="show flow status. ",
)
async def status(request: HttpRequest) -> ApiResponse:
    if request.headers.get("signature") != "1ik8qrp350.dqv8vsfbj1o0.e91e8bf9jc3c":
        return ApiResults.NOT_FOUND()
    # Single query to get all platforms and their active status
    platforms_query = (
        AIFlow.objects.values("platform")
        .distinct()
        .annotate(
            is_active=Case(
                When(
                    platform__in=AIFlow.objects.filter(
                        status__in=[TaskStatus.INIT.value, TaskStatus.RUNNING.value]
                    ).values("platform"),
                    then=Value(True),
                ),
                default=Value(False),
                output_field=BooleanField(),
            )
        )
    )

    # Convert to dictionary using async iteration
    platform_status = {
        item["platform"]: item["is_active"]
        async for item in platforms_query
        if item["platform"]  # Filter out empty platform names
    }

    return ApiResults.SUCCESS(data=platform_status)


@aiflow_router.get("/{int:aiflow_id}/", summary="to get aiflow details")
async def aiflow_detail(
    request: HttpRequest,
    aiflow_id: int,
) -> ApiResponse:
    try:
        aiflow = await AIFlow.objects.aget(
            id=aiflow_id, user=request.user, is_deleted=False
        )
        with_profiles_query = (request.GET.get("with_profiles") or "false").lower()
        with_profiles = with_profiles_query in ["1", "yes", "true", "on"]

        msgs = Msg.objects.filter(aiflow=aiflow).order_by("created_at")
        msgs_list = [msg async for msg in msgs.values()]

        aiflow_dict = aiflow.to_dict()
        if not with_profiles:
            aiflow_dict.get("data", {}).pop("profiles", None)
        # Process email field in data rows
        for row in aiflow_dict["data"].get("rows", []):
            if "email" in row:
                if is_valid_email(row["email"]):
                    row["email"] = AIFlowCellPlaceholder.EMAIL

        return ApiResults.SUCCESS(data={"flow": aiflow_dict, "msgs": msgs_list})
    except AIFlow.DoesNotExist:
        return ApiResults.NOT_FOUND(detail="AIFlow not found")


@aiflow_router.delete("/{int:aiflow_id}/", summary="to delete aiflow")
async def aiflow_delete(
    request: HttpRequest,
    aiflow_id: int,
) -> ApiResponse:
    try:
        aiflow = (
            await AIFlow.objects.filter(id=aiflow_id, user=request.user)
            .prefetch_related("user")
            .aget()
        )
        # if the task has not started, delete it directly to remove invalid data
        if aiflow.status == TaskStatus.INIT.value:
            await aiflow.adelete()
        else:
            await AIFlow.objects.filter(id=aiflow.id).aupdate(
                is_deleted=True, status=TaskStatus.DONE.value
            )
            logger.info(f"flow {aiflow_id} deleted. settling the rest charges ...")
            await aiflow.async_charge_and_settle()
        return ApiResults.SUCCESS()
    except AIFlow.DoesNotExist:
        return ApiResults.NOT_FOUND(detail="AIFlow not found")


@aiflow_router.get(
    "/{int:flow_id}/profiles/{handle}/",
    summary="to get a profile (snapshot) details of a flow",
)
async def aiflow_profile_detail(
    request: HttpRequest, flow_id: int, handle: str
) -> ApiResponse:
    try:
        aiflow = await AIFlow.objects.aget(
            id=flow_id, user=request.user, is_deleted=False
        )

        handle_to_profile = aiflow.data.get("profiles") or {}
        profile_d = handle_to_profile.get(handle)
        if not profile_d:
            return ApiResults.NOT_FOUND(detail="Profile not found")

        return ApiResults.SUCCESS(data=profile_d)
    except AIFlow.DoesNotExist:
        return ApiResults.NOT_FOUND(detail="AIFlow not found")


@aiflow_router.get(
    "/{int:flow_id}/evaluations/{row_id}/",
    summary="to get the evaluation assessment (snapshot) of a row in flow",
)
async def aiflow_row_evaluation(
    request: HttpRequest, flow_id: int, row_id: str
) -> ApiResponse:
    try:
        aiflow = await AIFlow.objects.aget(
            id=flow_id, user=request.user, is_deleted=False
        )

        evaluations = aiflow.data.get("evaluations") or []
        evaluation = None
        for i in range(len(evaluations)):
            if row_id == evaluations[i]["row_id"]:
                evaluation = evaluations[i]
                break

        if evaluation:
            return ApiResults.SUCCESS(data=evaluation)
        else:
            return ApiResults.NOT_FOUND(detail=f"Evaluation '{row_id}' not found")

    except AIFlow.DoesNotExist:
        return ApiResults.NOT_FOUND(detail="AIFlow not found")


@aiflow_router.get(
    "/credits/usage/",
    summary="to retrieve current quota usage of the user",
)
async def get_current_quota_usage(request: HttpRequest) -> ApiResponse:
    user = request.user

    try:
        await RedeemCode.async_check_and_grant_credits(user.id)
    except Exception as e:
        logger.exception(e)

    try:
        used_quota, total_quota = await CreditPtsCard.async_get_current_period_usage(
            user
        )

        current_quota = {
            "total_quota": total_quota,
            "used_quota": used_quota,
            # "free_credits": 0,
            # "free_credits_used": 0,
            # "credits_list": [],
        }
        return ApiResults.SUCCESS(data=current_quota)
    except Exception as e:
        logger.error(f"Error retrieving quota usage: {e}")
        return ApiResults.FAIL(detail="Error retrieving quota usage")


class UnlockEmailScheme(Schema):
    row_ids: List[str] = []


@aiflow_router.post(
    "/{int:flow_id}/unlock_email/",
    summary="to unlock the emails",
    auth=[
        BearAuth(member_required=True, credits_required=True),
        HeaderAuth(member_required=True, credits_required=True),
    ],
)
async def unlock_email(
    request: HttpRequest, flow_id: int, playload: UnlockEmailScheme
) -> ApiResponse:
    user_id = request.user.id

    used_credits, total_credits = await CreditPtsCard.async_get_current_period_usage(
        request.user
    )
    rest_credits = total_credits - used_credits

    data = []
    row_id_to_email: dict[str, str] = {}
    aiflow = (
        await AIFlow.objects.filter(id=flow_id, user=request.user, is_deleted=False)
        .values("data__rows", "table_actions")
        .afirst()
    )

    # rows has email (and only in payload)
    rows = aiflow["data__rows"]
    rows_d: dict[str, dict] = {d["row_id"]: d for d in rows}

    for row_id in playload.row_ids:
        if row_id not in rows_d:
            logger.warning(f"invalid row id {row_id} of flow {flow_id}.")
            continue

        email = rows_d[row_id]["email"]
        if not is_valid_email(email):
            continue
        data.append({"row_id": row_id, "value": email})
        row_id_to_email[row_id] = email

    # unlocked rows
    table_actions = aiflow.get("table_actions", [])
    unlocked_row_ids: set[str] = {
        action.get("row_id")
        for action in table_actions
        if action.get("action") == "unlock_email"
    }

    # rows to be unlocked
    for row_id in unlocked_row_ids:
        if row_id in row_id_to_email:
            row_id_to_email.pop(row_id)

    # check if credits enough
    unlock_price = charge_prices[ChargeActionEnum.UNLOCK_EMAIL]
    required_credits = len(row_id_to_email) * unlock_price
    if required_credits > rest_credits:
        return ApiResults.OUT_OF_CREDITS(
            detail=f"need {required_credits} credit points to unlock emails but you have only {rest_credits}."
        )

    # unlock them
    if len(row_id_to_email) > 0:
        logger.info(f"unlocking email rows: {row_id_to_email.keys()}")
        for row_id, email in row_id_to_email.items():
            table_actions.append(
                {
                    "action": "unlock_email",
                    "value": email,
                    "row_id": row_id,
                    "unlock_at": datetime.now().isoformat(),
                }
            )
        await AIFlow.objects.filter(id=flow_id).aupdate(table_actions=table_actions)
        await Charge.async_create(
            user_id,
            action=ChargeActionEnum.UNLOCK_EMAIL,
            quantity=len(row_id_to_email),
            flow_id=flow_id,
            data={"row_ids": list(row_id_to_email.keys())},
        )
    await Charge.async_settle(user_id)

    return ApiResults.SUCCESS(data=data)


class ReportMistakeScheme(Schema):
    report_type: str
    row_id: str
    reason: str


@aiflow_router.post(
    "/{int:flow_id}/report_mistake/",
    summary="Report a mistake",
)
async def report_mistake(
    request: HttpRequest, body: ReportMistakeScheme, flow_id: int
) -> ApiResponse:
    # Log the mistake report in table_actions
    action_data = {
        "action": "report_mistake",
        "flow_id": flow_id,
        "report_type": body.report_type,
        "row_id": body.row_id,
        "reason": body.reason,
        "reported_at": datetime.now().isoformat(),
    }
    aiflow = await AIFlow.objects.aget(id=flow_id, user=request.user)
    current_actions = aiflow.table_actions
    current_actions.append(action_data)
    await AIFlow.objects.filter(id=flow_id, user=request.user).aupdate(
        table_actions=current_actions
    )

    # Check if a charge already exists for this user, flow_id, and row_id
    existing_charge = await Charge.objects.filter(
        user_id=request.user.id,
        flow_id=flow_id,
        action=ChargeActionEnum.REPORT_MISTAKE,
        data__row_id=body.row_id,
    ).aexists()

    # If no existing charge, create a new charge
    if not existing_charge:
        await Charge.async_create(
            user_id=request.user.id,
            action=ChargeActionEnum.REPORT_MISTAKE,
            flow_id=flow_id,
            quantity=1,
            data={
                "row_id": body.row_id,
                "report_type": body.report_type,
                "reason": body.reason,
            },
        )
        await Charge.async_settle(user_id=request.user.id)

    return ApiResults.SUCCESS(data={"message": "Mistake reported successfully."})


# @aiflow_router.post(
#     "/{int:flow_id}/expend/",
#     summary="expend rest rows. ",
# )
# async def expend_rows(request: HttpRequest, flow_id: int) -> ApiResponse:
#     flow: AIFlow = (
#         await AIFlow.objects.filter(user=request.user, id=flow_id, is_expended=False)
#         .prefetch_related("user")
#         .afirst()
#     )
#     if flow and not flow.is_expended:
#         flow.is_expended = True
#         await flow.asave()
#         if flow.status == TaskStatus.DONE:
#             await flow.async_charge_and_settle()
#         return ApiResults.SUCCESS(data=True)
#     return ApiResults.SUCCESS(data=False)


@aiflow_router.post(
    "/{int:flow_id}/show_low_relevance/",
    summary="show low relevant rows. ",
)
async def show_low_relevance_rows(request: HttpRequest, flow_id: int) -> ApiResponse:
    flow: AIFlow = await AIFlow.objects.filter(
        user=request.user, id=flow_id, is_low_relevance_shown=False
    ).afirst()
    if flow and not flow.is_low_relevance_shown:
        flow.is_low_relevance_shown = True
        await flow.asave()
    return ApiResults.SUCCESS(data=False)


class RedeemCodeSchema(Schema):
    code: str


@aiflow_router.post(
    "/redeem/{platform}/",
    summary="Redeem a code to get credits",
)
async def redeem_code(
    request: HttpRequest, platform: str, body: RedeemCodeSchema
) -> ApiResponse:
    try:
        await async_redeem_code(
            platform=platform,
            code=body.code,
            user_id=request.user.id,
            username=request.user.username,
        )
        return ApiResults.SUCCESS()
    except ValidationError as e:
        logger.warning(f"Invalid code/platform pair or used code. {e}")
        return ApiResults.FAIL(detail="Invalid code/platform pair")
    except Exception as e:
        logger.exception(f"Error redeeming code: {e}")
        return ApiResults.FAIL(detail="Error redeeming code")


@aiflow_router.get(
    "/{int:flow_id}/is_target_user/",
    summary="Classify if current user is a target user for given flow",
)
async def classify_target_user_api(request: HttpRequest, flow_id: int) -> ApiResponse:
    """Classify if the current user is a target user based on their usage patterns and flow.

    Returns:
        ApiResponse with data containing:
            - is_target_user: bool indicating if user is classified as target
    """
    try:
        result = await async_classify_target_user(
            user_id=request.user.id, flow_id=flow_id
        )
        return ApiResults.SUCCESS(data=result)
    except Exception as e:
        logger.exception(f"Error classifying target user: {e}")
        return ApiResults.FAIL(detail="Error classifying target user")


@aiflow_router.get(
    "/charge_prices/",
    auth=None,
    summary="List all charging prices for different actions",
)
async def list_charge_prices(request: HttpRequest) -> ApiResponse:
    """Returns a dictionary of all charging prices for different actions.

    Returns:
        ApiResponse with data containing:
            - Dictionary mapping ChargeActionEnum values to their credit costs
    """
    return ApiResults.SUCCESS(data=charge_prices)
