{% extends "tools/inc/base.html" %}

{% block title %}Vector DB (Pinecone){% endblock %}

{% block content %}
<h1>Vector DB - Pinecone Embedding Index</h1>

<div id="content-main">
	<h2>Search</h2>
	<div class="form module">
		<form action="" method="get">
			<table>{{ form.as_table }}</table>
			<input type="submit" value="Submit">
			{% csrf_token %}
		</form>
	</div>
	<hr/>
	<div id="result module">
		<h2>Result</h2>
		<div class="results">
			<table id="result_list">
				<thead>
				<tr>
					<th>score</th>
					<th>meta</th>
					<th width="50%">content</th>
				</tr>
				</thead>
				{% for doc, score in result %}
				<tr>
					<td>{{score|floatformat:3}}</td>
					<td>
						<pre>{{doc.metadata|pprint}}</pre>
					</td>
					<td>{{doc.page_content|safe}}</td>
				</tr>
				{% endfor %}
			</table>
		</div>
	</div>
	<hr/>
	<div id="debug module">
		<h2>DEBUG INFO</h2>
		<h4>Requests:</h4>
		<pre>
{{query|pprint}}
		</pre>
		<h4>Results:</h4>
		<pre>
{{result|pprint}}
		</pre>
	</div>
</div>


<div id="content-related">
	&nbsp;
</div>

{% endblock %}
