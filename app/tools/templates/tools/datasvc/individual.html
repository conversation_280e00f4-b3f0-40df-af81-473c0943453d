{% extends "tools/inc/base.html" %}

{% block title %}Data Service - Data Integrity{% endblock %}

{% block head %}
<style>
    .colMS {
        margin-right: 10px;
    }
    .results p {
        color: gray;
    }
    .guide ol li {
        color: gray
    }
</style>
{% endblock %}

{% block content %}
<h1>Data Service - Dashboard</h1>

<div id="content-main">

    <div class="results">
        <h2>Individual Document Check </h2>
        {% if error %}<p><span style="color:red">{{error}}</span></p>{% endif %}
        <div class="form module">
            <form action="" method="get">
                <table>{{ form.as_table }}</table>
                <div class="submit-row">
                    <input type="submit" value="Submit">
                </div>
                {% csrf_token %}
            </form>
        </div>

        <p>Guide to check:</p>
        <ol class="guide">
            <li>1. If a doc presents in both <b>Provider Updated</b> and <b>We Updated</b>, it means the doc is created
                by us.
                <ul>
                    <li>Check <b>status</b> of the doc. We know which step the doc goes. Only <b>indexed</b> means a doc
                        is ready.
                    </li>
                </ul>
            </li>
            <li>2. If a doc presents in <b>Provider Updated</b> only, it means this doc is <b>not updated (created) by
                us</b>. Check <b>Airflow</b> job to see if there is any error occurs. Find it and fix it.
            </li>
            <li>3. If a doc presents in <b>We Updated</b>, it is possible an issue of checking logic or API. Try finding
                it.
            </li>
        </ol>

        <div style="float:left;margin-right:5px;margin-bottom:5px;border-right:dotted gray 1px">
            <h3>Provider Updated ({{ provider_data|length }})</h3>
            <hr/>
            <ul>
                <li style="list-style-type: none;">&nbsp;</li>
                {% for line in provider_data %}
                <li>{{line}}</li>
                {% endfor %}
            </ul>
        </div>

        <div style="float:left;margin-left:5px;margin-bottom:5px;">
            <h3>We Updated ({{ data|length }})</h3>
            <table>
                <thead>
                <tr>
                    <th>Doc Key</th>
                    <th>Status</th>
                    <th>Publish Date</th>
                    <th>Created At</th>
                    <th>Last Error</th>
                </tr>
                <tr></tr>
                </thead>
                {% for d in data %}
                <tr>
                    <td>{{d.key}}</td>
                    <td>{{d.status}}</td>
                    <td>{{d.pub_date}}</td>
                    <td>{{d.created_at}}</td>
                    <td>{{d.last_error}}</td>
                </tr>
                {% endfor %}
            </table>
            <hr/>
        </div>

    </div>


    {% if debug %}
    <hr style="clear:both"/>

    <div id="debug-panel">
        <h2>DEBUG INFO</h2>
        <h4>Requests:</h4>
        <pre>
{{query|pprint}}
		</pre>
        <h4>Results:</h4>
        <pre>
{{provider_data|pprint}}
		</pre>
        <pre>
{{data|pprint}}
		</pre>
    </div>
    {% endif %}
</div>


<!--<div id="content-related">-->
<!--    &nbsp;-->
<!--</div>-->

{% endblock %}
