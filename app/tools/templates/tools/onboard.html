{% extends "tools/inc/base.html" %}
{% load static %}

{% block head %}
<style>
    .form-container {
        max-width: 1000px;
        margin: 0 auto;
    }

    .form-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .form-table th,
    .form-table td {
        padding: 8px;
        text-align: left;
        vertical-align: middle;
        border: 1px solid #ddd;
    }

    .form-table th {
        background-color: #f2f2f2;
        font-weight: bold;
    }

    .form-table input[type="checkbox"] {
        margin-right: 5px;
    }

    .invalid-emails {
        color: red;
        margin-bottom: 20px;
    }

    .password-toggle {
        cursor: pointer;
        margin-left: 5px;
    }

    textarea {
        width: 100%;
        height: 100px;
    }

    .password-field,
    .password-value {
        display: inline-block;
        vertical-align: middle;
    }

    .password-container {
        display: flex;
        align-items: center;
    }

    .password-container input {
        flex-grow: 1;
        margin-right: 5px;
    }

    .password-toggle-all {
        cursor: pointer;
        margin-left: 5px;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }

    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    .alert-warning {
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeeba;
    }

    .text-success {
        color: #155724;
    }
</style>
{% endblock %}

{% block title %}
Customer Onboard - {{ step|title }}
{% endblock %}

{% block content %}
<div class="form-container">
    <h1>Customer Onboard - {{ step|title }}</h1>

    {% if step == 'initial' %}
    <form method="post">
        {% csrf_token %}
        <input type="hidden" name="step" value="initial">
        <table class="form-table">
            <tr>
                <th>{{ form.emails.label_tag }}</th>
                <td>
                    {{ form.emails }}
                    {% if form.emails.help_text %}
                    <p class="help">{{ form.emails.help_text|safe }}</p>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>{{ form.password.label_tag }}</th>
                <td>
                    <div class="password-container">
                        {{ form.password }}
                        <span class="password-toggle" onclick="togglePassword(this)">👁️</span>
                    </div>
                    {% if form.password.help_text %}
                    <p class="help">{{ form.password.help_text|safe }}</p>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>{{ form.internal_subscriber.label_tag }}</th>
                <td>
                    {{ form.internal_subscriber }}
                    {% if form.internal_subscriber.help_text %}
                    <p class="help">{{ form.internal_subscriber.help_text|safe }}</p>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>{{ form.can_upload_file.label_tag }}</th>
                <td>
                    {{ form.can_upload_file }}
                    {% if form.can_upload_file.help_text %}
                    <p class="help">{{ form.can_upload_file.help_text|safe }}</p>
                    {% endif %}
                </td>
            </tr>
        </table>
        <input type="submit" value="Next">
    </form>
    {% elif step == 'submit' %}
    {% if invalid_emails %}
    <div class="invalid-emails">
        <p>The following emails are invalid or already in use and will be skipped:</p>
        <ul>
            {% for email, reason in invalid_emails.items %}
            <li>{{ email }}: {{ reason }}</li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}
    <form method="post">
        {% csrf_token %}
        <input type="hidden" name="step" value="submit">
        <input type="hidden" name="invalid_emails" value="{{ invalid_emails }}">
        <input type="hidden" name="default_password" value="{{ default_password }}">
        <input type="hidden" name="default_internal_subscriber" value="{{ default_internal_subscriber }}">
        <input type="hidden" name="default_can_upload_file" value="{{ default_can_upload_file }}">
        {% for email in valid_emails %}
        <input type="hidden" name="valid_emails" value="{{ email }}">
        {% endfor %}
        <table class="form-table">
            <thead>
            <tr>
                <th>Email</th>
                <th>Internal Subscriber</th>
                <th>Can Upload File</th>
                <th>Password</th>
            </tr>
            </thead>
            <tbody>
            {% for form in forms %}
            <tr>
                <td>
                    {{ form.email.value }}
                    {{ form.email }}
                </td>
                <td>
                    <label>
                        {{ form.internal_subscriber }}
                    </label>
                </td>
                <td>
                    <label>
                        {{ form.can_upload_file }}
                    </label>
                </td>
                <td>
                    <div class="password-container">
                        {{ form.password }}
                        <span class="password-toggle" onclick="togglePasswordSubmit(this)">👁️</span>
                    </div>
                </td>
            </tr>
            {% endfor %}
            </tbody>
        </table>
        <input type="submit" name="submit" value="Review">
    </form>
    {% elif step == 'review' %}
    <table class="form-table">
        <thead>
        <tr>
            <th>Email</th>
            <th>Internal Subscriber</th>
            <th>Can Upload File</th>
            <th>
                Password
                <span class="password-toggle-all" onclick="toggleAllPasswords(this)">👁️</span>
            </th>
        </tr>
        </thead>
        <tbody>
        {% for form in review_forms %}
        <tr>
            <td>{{ form.email.value }}</td>
            <td>{{ form.internal_subscriber.value }}</td>
            <td>{{ form.can_upload_file.value }}</td>
            <td>
                <span class="password-field">••••••••</span>
                <span class="password-value" style="display: none;">{{ form.password.value }}</span>
            </td>
        </tr>
        {% endfor %}
        </tbody>
    </table>
    <form method="post">
        {% csrf_token %}
        <input type="hidden" name="step" value="review">
        <input type="hidden" name="submit_data" value="{{ submit_data }}">
        <input type="submit" value="Final Submit">
    </form>
    {% elif step == 'display_result' %}
        {% if errors %}
        <div class="alert alert-danger">
            <h4>Errors occurred during onboarding:</h4>
            <ul>
                {% for error in errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        {% if results %}
        <div class="alert alert-success">
            <h4 class="text-success">Successfully onboarded {{ results|length }} user{% if results|length != 1 %}s{% endif %}:</h4>
            <ul>
                {% for result in results %}
                <li class="text-success">{{ result.email }}</li>
                {% endfor %}
            </ul>
        </div>

        <h2 class="text-success">Results:</h2>
        <table class="form-table">
            <thead>
            <tr>
                <th>Username</th>
                <th>Email</th>
                <th>Is Internal Subscriber</th>
                <th>Can Upload File</th>
                <th>
                    Password
                    <span class="password-toggle-all" onclick="toggleAllPasswords(this)">👁️</span>
                </th>
            </tr>
            </thead>
            <tbody>
            {% for result in results %}
            <tr>
                <td>{{ result.username }}</td>
                <td>{{ result.email }}</td>
                <td>{{ result.is_internal_subscriber }}</td>
                <td>{{ result.can_upload_file }}</td>
                <td>
                    <span class="password-field">••••••••</span>
                    <span class="password-value" style="display: none;">{{ result.password }}</span>
                </td>
            </tr>
            {% endfor %}
            </tbody>
        </table>
        <form id="exportForm" method="post">
            {% csrf_token %}
            <input type="hidden" name="step" value="review">
            <input type="hidden" name="export_csv" value="true">
            <input type="hidden" id="resultsData" name="results" value="">
            <input type="submit" value="Export as CSV" onclick="setResultsData()">
        </form>
        {% else %}
        <div class="alert alert-warning">
            <h4>No users were successfully onboarded.</h4>
        </div>
        {% endif %}

        <script>
            function setResultsData() {
                var results = [
                    {% for result in results %}
                        {
                            username: "{{ result.username }}",
                            email: "{{ result.email }}",
                            is_internal_subscriber: {{ result.is_internal_subscriber|lower }},
                            can_upload_file: {{ result.can_upload_file|lower }},
                            password: "{{ result.password }}",
                        }{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ];
                document.getElementById('resultsData').value = JSON.stringify(results);
            }

            function toggleAllPasswords(element) {
                const passwordFields = document.querySelectorAll('.password-field');
                const passwordValues = document.querySelectorAll('.password-value');
                
                if (element.textContent === "👁️") {
                    passwordFields.forEach(field => field.style.display = "none");
                    passwordValues.forEach(value => value.style.display = "inline-block");
                    element.textContent = "🙈";
                } else {
                    passwordFields.forEach(field => field.style.display = "inline-block");
                    passwordValues.forEach(value => value.style.display = "none");
                    element.textContent = "👁️";
                }
            }
        </script>
    {% endif %}
</div>

<script>
    function togglePassword(element) {
        const passwordInput = element.previousElementSibling;
        if (passwordInput.type === "password") {
            passwordInput.type = "text";
            element.textContent = "🙈";
        } else {
            passwordInput.type = "password";
            element.textContent = "👁️";
        }
    }

    function togglePasswordSubmit(element) {
        const passwordInput = element.previousElementSibling;
        if (passwordInput.type === "password") {
            passwordInput.type = "text";
            element.textContent = "🙈";
        } else {
            passwordInput.type = "password";
            element.textContent = "👁️";
        }
    }

    // Hide passwords by default when the page loads
    document.addEventListener('DOMContentLoaded', function () {
        const passwordInputs = document.querySelectorAll('input[type="password"]');
        const toggleButtons = document.querySelectorAll('.password-toggle');
        const toggleAllButton = document.querySelector('.password-toggle-all');

        passwordInputs.forEach((input) => {
            input.type = "password";
        });

        toggleButtons.forEach((button) => {
            button.textContent = "👁️";
        });

        if (toggleAllButton) {
            toggleAllButton.textContent = "👁️";
        }
    });
</script>
{% endblock %}
