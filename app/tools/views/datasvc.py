import json
from datetime import datetime, UTC, timedelta
from logging import getLogger

from asgiref.sync import async_to_sync, sync_to_async
from django.contrib.auth.decorators import login_required
from django.core.cache import caches
from django.http import HttpRequest, HttpResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods

from biz.enums import DocTypeEnum, TickerScopeEnum
from common.utils.datetimeutils import format_datetime, parse_datetime
from common.utils.strutil import ellipse
from datasvc.models import DocModel
from datasvc.providers.benzinga_api import BenzingaAP<PERSON>
from datasvc.providers.financial_modeling_prep import FMPAPI
from datasvc.providers.onwish.scope import get_ticker_set_by_scope
from datasvc.providers.quartr_api import QuartrAPI
from tools.forms.ds import IndividualDocForm

logger = getLogger(__name__)
cache = caches["default"]
supported_ticker_set = get_ticker_set_by_scope(TickerScopeEnum.FULL)


@sync_to_async
@csrf_exempt
@login_required
@require_http_methods(["GET"])
@async_to_sync
async def ds_index(request: HttpRequest) -> HttpResponse:
    from datasvc.statistic import DocIntegrityChecker

    result = await cache.aget(DocIntegrityChecker.CACHE_KEY_RESULT) or {}
    checked_at = result.pop("checked_at")
    days = result.pop("days")
    result.pop("checking_from")
    ongoing_checking_from = await cache.aget(
        DocIntegrityChecker.CACHE_KEY_CHECKING_FROM
    )

    for scope, d in result.items():
        for disp_doc_type, stats in d["status"].items():
            stats["provider_all"] = sum(stats["provider_tickers"].values())
            less_tickers = set()
            more_tickers = set()
            for ticker, pcnt in stats["provider_tickers"].items():
                cnt = stats["tickers"].get(ticker, 0)
                if pcnt > cnt:
                    less_tickers.add(ticker)
            for ticker, cnt in stats["tickers"].items():
                pcnt = stats["provider_tickers"].get(ticker, 0)
                if pcnt < cnt:
                    more_tickers.add(ticker)
            stats["less_tickers"] = less_tickers
            stats["more_tickers"] = more_tickers

    context = {
        # "debug": conf.DEBUG,
        "days": days,
        "checked_at": format_datetime(checked_at) if checked_at else "",
        "ongoing_checking": format_datetime(ongoing_checking_from)
        if ongoing_checking_from
        else "",
        "result": result,
    }

    logger.debug(json.dumps(context, indent=2, default=str))
    return render(request, "tools/datasvc/index.html", context)


@sync_to_async
@csrf_exempt
@login_required
@require_http_methods(["GET"])
@async_to_sync
async def ds_individual(request: HttpRequest) -> HttpResponse:  # noqa: C901
    form_cls = IndividualDocForm

    if request.GET:
        form = form_cls(request.GET)
    else:
        form = form_cls()

    async def __inner_fetch_provider(
        _doc_type: str, _ticker: str, _min_ts: datetime
    ) -> list:
        if _doc_type == DocTypeEnum.EARNING_CALL:
            _data = await FMPAPI().get_most_recent_earnings_periods(
                [_ticker], min_ts=_min_ts
            )
            return [
                f"{_doc_type}/{_ticker}/{period.y}/Q{period.q} - {parse_datetime(period.filing_date)}"
                for periods in _data.values()
                for period in periods
            ]
        elif _doc_type == DocTypeEnum.SEC_FILING:
            _data = await FMPAPI().get_most_recent_financial_periods(
                [_ticker], min_ts=_min_ts
            )
            return [
                f"{_doc_type}/{_ticker}/{period.y}/Q{period.q} - {parse_datetime(period.filing_date)}"
                for periods in _data.values()
                for period in periods
            ]
        elif _doc_type == DocTypeEnum.EVENT_TRANSCRIPT:
            _data = await QuartrAPI().fetch_company_events(_ticker, min_ts=_min_ts)
            return [f"{_doc_type} - {d['title']} - {d['pub_date']}" for d in _data]
        elif _doc_type == DocTypeEnum.CONFERENCE:
            _data = await QuartrAPI().fetch_conferences(_min_ts, _ticker)
            return [f"{_doc_type} - {d['title']} - {d['pub_date']}" for d in _data]
        elif _doc_type == DocTypeEnum.PRESS_RELEASE:
            _data = await BenzingaAPI().get_press_releases(_ticker, _min_ts, 200)
            return [
                f"{_doc_type} - {ellipse(d['title'])} - {d['pub_date']}" for d in _data
            ]
        elif _doc_type == DocTypeEnum.NEWS:
            _data = await FMPAPI().fetch_stock_news([_ticker], _min_ts, 200)
            return [
                f"{_doc_type} - {ellipse(d['title'])} - {d['pub_date']}" for d in _data
            ]
        return []

    async def __inner_fetch_db(_doc_type: str, _ticker: str, _min_ts: datetime) -> list:
        doc_model_cls = DocModel.get_cls_by_type(_doc_type)
        qs = doc_model_cls.objects.filter(
            ticker=_ticker, pub_date__gte=_min_ts
        ).order_by("-pub_date")
        docs = []
        async for doc in qs.all():
            d = doc.to_dict(
                keys=["key", "pub_date", "status", "last_error", "created_at"]
            )
            d["last_error"] = doc.last_error.partition("\n")[0]
            docs.append(d)
        return docs

    context = {"form": form}
    if form.is_valid():
        doc_type = form.cleaned_data.get("doc_type")
        ticker = form.cleaned_data.get("ticker")
        days = form.cleaned_data.get("days")
        min_ts = datetime.now(tz=UTC) - timedelta(days=days)
        # print(doc_type, ticker, days, min_ts)
        if ticker not in supported_ticker_set:
            context["error"] = f"{ticker} is not supported ticker."
        if doc_type in [DocTypeEnum.TWITTER, DocTypeEnum.YOUTUBE]:
            context["error"] = f"{doc_type} is not supported to get provider data."

        provider_data = await __inner_fetch_provider(doc_type, ticker, min_ts)
        data = await __inner_fetch_db(doc_type, ticker, min_ts)
        context.update({"provider_data": provider_data, "data": data})
        # print(json.dumps(provider_data, indent=2, default=str))
        # print("-" * 80)
        # print(json.dumps(data, indent=2, default=str))

    # context.update({"debug": conf.DEBUG, "query": request.GET})
    return render(request, "tools/datasvc/individual.html", context)


# @sync_to_async
# @csrf_exempt
# @login_required
# @require_http_methods(["GET"])
# def ds_index(request: HttpRequest) -> HttpResponse:
#     date_field = "pub_date"
#     days = 7
#     count_failed = False
#     result: dict[str, dict[str, dict]] = {}
#     for doc_type in DocTypeEnum.values():
#         result[doc_type] = {}
#         data = statistic.list_missing_docs(
#             doc_type=doc_type,
#             in_n_days=days,
#             date_field=date_field,
#             count_failed=count_failed,
#         )
#         result.update(data)
#
#     context = {
#         "result": result,
#         "date_field": date_field,
#         "days": days,
#         "count_failed": count_failed,
#     }
#
#     return render(request, "tools/datasvc/index.html", context)
