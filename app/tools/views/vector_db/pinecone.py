import asyncio
import itertools
import json
from logging import getLogger

from asgiref.sync import sync_to_async, async_to_sync
from django.contrib.auth.decorators import login_required
from django.http import HttpRequest, HttpResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from langchain.docstore.document import Document

from biz.enums import DocTypeEnum, IndexProviderEnum
from biz.search import <PERSON>coneHybridSearcher, PineconeEmbeddingSearcher
from datasvc import conf
from datasvc.indexing import get_index, Index
from datasvc.indexing.pinecone import (
    PineconeIndexConfig,
    PineconeDenseIndex,
)
from tools.forms.vector_db import PineconeHybridSearchForm, PineconeEmbeddingSearchForm
from tools.views.general import _gen_resp_context

logger = getLogger(__name__)


@sync_to_async
@csrf_exempt
@login_required
@require_http_methods(["GET"])
def pinecone_hybrid(request: HttpRequest) -> HttpResponse:
    klass = PineconeHybridSearchForm

    if request.GET:
        form = klass(request.GET)
    else:
        form = klass()

    context = _gen_resp_context({"form": form, "query": request.GET})
    if form.is_valid():
        filters = form.get_pinecone_meta_filter()
        result = _pinecone_hybrid_search(form, filters)
        context.update({"result": result, "filters": filters})

    return render(request, "tools/vector_db/pinecone_hybrid.html", context)


@async_to_sync
async def _pinecone_hybrid_search(
    form: PineconeHybridSearchForm, filters: dict
) -> list[tuple[Document, float]]:
    data: dict = form.cleaned_data
    doc_type = data.get("doc_type")

    # search criteria
    q = data["q"]
    top_k = int(data.get("top_k", 10))

    # pinecone parameters
    alpha = data.get("pinecone_hybrid_alpha", 0.5)
    if data["pinecone_hybrid_index"] or data["pinecone_ns"]:
        index = Index.new_by_config(
            config=PineconeIndexConfig(
                name=data["pinecone_hybrid_index"],
                # consider that the following to be configured for each index.
                provider=IndexProviderEnum.PINECONE_HYBRID,
                api_key=conf.PINECONE_API_KEY,
                namespace=data["pinecone_ns"],
            )
        )
        searchers = [PineconeHybridSearcher(index=index, alpha=alpha)]

    else:
        if doc_type == DocTypeEnum.NOT_SPECIFIED:
            searchers = []
            for doc_type in DocTypeEnum.values():
                index = get_index(doc_type)
                searchers.append(PineconeHybridSearcher(index=index, alpha=alpha))
        else:
            index = get_index(doc_type)
            searchers = [PineconeHybridSearcher(index=index, alpha=alpha)]

    logger.info(f"meta filter: {json.dumps(filters, indent=2)}")
    coroutines = []
    for searcher in searchers:
        coroutines.append(
            searcher.query_with_scores(
                query=q,
                top_k=top_k,
                metadata_filter=filters,
            )
        )
    docs_with_scores = list(itertools.chain(*(await asyncio.gather(*coroutines))))

    return docs_with_scores


@sync_to_async
@csrf_exempt
@login_required
@require_http_methods(["GET"])
def pinecone_embedding(request: HttpRequest) -> HttpResponse:
    klass = PineconeEmbeddingSearchForm

    if request.GET:
        form = klass(request.GET)
    else:
        form = klass()

    context = _gen_resp_context({"form": form, "query": request.GET})
    if form.is_valid():
        filters = form.get_pinecone_meta_filter()
        result = _pinecone_embedding_search(form, filters)
        context.update({"result": result, "filters": filters})
    return render(request, "tools/vector_db/pinecone_embedding.html", context)


@async_to_sync
async def _pinecone_embedding_search(
    form: PineconeEmbeddingSearchForm, filters: dict
) -> list[tuple[Document, float]]:
    data: dict = form.cleaned_data

    # search criteria
    q = data["q"]
    top_k = int(data.get("top_k", 10))

    # pinecone parameters
    index_config = PineconeIndexConfig(
        index_name=data["pinecone_index"], namespace=data["pinecone_ns"]
    )
    searcher = PineconeEmbeddingSearcher()
    searcher._index = PineconeDenseIndex(index_config=index_config)

    result = await searcher.query_with_scores(
        query=q,
        top_k=top_k,
        metadata_filter=filters,
    )
    return result
