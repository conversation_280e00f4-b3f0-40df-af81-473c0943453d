{"last_updated": "2024-09-09", "samples": [{"input_paragraphs": [{"speaker": "Anonymous", "content": "Okay. So, <PERSON>, maybe let's to start it off, let's talk about Computex and some of the talk, what do you find the most interesting and exciting as you look at growth prospects over the"}, {"speaker": "<PERSON>", "content": "next few years? Yes, Computex is an important conference for NVIDIA and for AI now. The world's systems, they get their machines, their hardware from this small island of Taiwan versus their chips as well. So, we're there a very important ecosystem for us."}], "expected_output": [{"speaker": "Anonymous", "content": "Okay. So, <PERSON>, maybe let's to start it off, let's talk about Computex and some of the talk, what do you find the most interesting and exciting as you look at growth prospects over the next few years?"}, {"speaker": "<PERSON>", "content": "Yes, Computex is an important conference for NVIDIA and for AI now. The world's systems, they get their machines, their hardware from this small island of Taiwan versus their chips as well. So, we're there a very important ecosystem for us."}]}, {"input_paragraphs": [{"speaker": "Anonymous", "content": "Since you have been so intimately involved with CUDA since its founding, right, how do you address the pushback that people have is that software abstraction is being done away from CUDA and it will make CUDA obsolete at some point that that's not really a sustainable moat for NVIDIA. How do you address that pushback?"}, {"speaker": "<PERSON>", "content": "Yes, I think the moat is a complicated word and what does it mean? The innovation come what makes the platform useful is how many developers it has on it, how many users are base people can get the access so that next AI invention can be make sure it's compatible with that architecture what it can do. These foundation, these new next generation, they're often they're not academic exercises. They are designed to what the limits of the capability are trained. And many of the models that we are enjoying today are actually trained like or started training like 2 years ago.\n\nThere's a lag unfortunately in terms of how long it takes to when the data center gets stood up to when this they're obviously thinking we're explaining what we're building to try to shorten this process, but it is directly influencing the scale of what they can build. Not just the number with every generation, we also improved the performance on a per GPU basis by X factors. <PERSON> is like 4 or 5 times better at training per GPU than Hopper was, 30 times better on inference for trillion parameter models. And so that sets the bar for them how big of a model and then they look at the architecture of the NVMe, what they can build. So it is a symbiosis between what we're building, what they're inventing and then keep riding that wave.\n\nAnd that really helps us define helps the next generation AI model."}], "expected_output": [{"speaker": "Anonymous", "content": "Since you have been so intimately involved with CUDA since its founding, right, how do you address the pushback that people have is that software abstraction is being done away from CUDA and it will make CUDA obsolete at some point that that's not really a sustainable moat for NVIDIA. How do you address that pushback?"}, {"speaker": "<PERSON>", "content": "Yes, I think the moat is a complicated word and what does it mean? The innovation come what makes the platform useful is how many developers it has on it, how many users are base people can get the access so that next AI invention can be make sure it's compatible with that architecture what it can do. These foundation, these new next generation, they're often they're not academic exercises. They are designed to what the limits of the capability are trained. And many of the models that we are enjoying today are actually trained like or started training like 2 years ago.\n\nThere's a lag unfortunately in terms of how long it takes to when the data center gets stood up to when this they're obviously thinking we're explaining what we're building to try to shorten this process, but it is directly influencing the scale of what they can build. Not just the number with every generation, we also improved the performance on a per GPU basis by X factors. <PERSON> is like 4 or 5 times better at training per GPU than Hopper was, 30 times better on inference for trillion parameter models. And so that sets the bar for them how big of a model and then they look at the architecture of the NVMe, what they can build. So it is a symbiosis between what we're building, what they're inventing and then keep riding that wave.\n\nAnd that really helps us define helps the next generation AI model."}]}, {"input_paragraphs": [{"speaker": "<PERSON>", "content": "There are some geographic benefits, differences between training and inference. Most folks don't can do training anywhere on globe. So we see big training clusters, which we know for they can get the data center space and can tap into the grid, having good access to power and the economics there is very important. But training doesn't need to be localized. It's remote desktop, it's halfway around the world.\n\nYou can feel the lag and latency, training sign for that. But inference, you kind of do need to be near the user. Inference workloads might be fine. Batch processing inference, fine. Doing a longer chatbot might be okay.\n\nBut if you're doing Gen AI search, you're asking your browser, you want that answer quickly. If it's too slow, then it just immediately your quality of service plummets. So we often see that training clusters, our own capability of inference tends to be either in those same clusters and they'll divide it up. Just like the clouds are providing regions, they'll folks will and they can then serve it with both training and inference. I would just say the training part is a little bit more specialized because super big clusters can be wherever that makes the most sense for them to build and invest in the building.\n\nYes. But they are largely using more and more the same training and for training and inference, the same infrastructure. That again goes to the value they can be using it for training and flip it over to inference. If you saw what we launched our Blackwell GB200, MBL72, we talked a lot about inference because those they got to run that mixture of experts work through and they also have the same infrastructure that can be used for training as well. And that's fair.\n\nAt the same time, we also make sure that they can take that the same building blocks and vary the sizes and capabilities. GB200, the NVL 72 is designed for trillion parameter. The more modest size 70B or 7B, we have an NVL 2, which is just 2 Grace Blackwells tied together, which fit nicely in the standard server design and could be deployed anywhere. With each telco often will have a they'll have a cage. The cage has 100 kilowatts, can't exceed that.\n\nSo the metric is, can I put what kind of GPUs or what kind of servers can I put in there that maybe models like hand at the edge, you'll do something different than you'll do for a big OpenAI data center or some such? And that's why we have both kind of"}, {"speaker": "Anonymous", "content": "Since you have been so intimately involved with CUDA since its founding, right, how do you address the pushback that people have is that software abstraction is being done away from CUDA and it will make CUDA obsolete at some point that that's not really a sustainable moat for NVIDIA. How do you address that pushback?"}, {"speaker": "<PERSON>", "content": "Yes, I think the moat is a complicated word and what does it mean? The innovation come what makes the platform useful is how many developers it has on it, how many users are base people can get the access so that next AI invention can be make sure it's compatible with that architecture what it can do. These foundation, these new next generation, they're often they're not academic exercises. They are designed to what the limits of the capability are trained. And many of the models that we are enjoying today are actually trained like or started training like 2 years ago.\n\nThere's a lag unfortunately in terms of how long it takes to when the data center gets stood up to when this they're obviously thinking we're explaining what we're building to try to shorten this process, but it is directly influencing the scale of what they can build. Not just the number with every generation, we also improved the performance on a per GPU basis by X factors. <PERSON> is like 4 or 5 times better at training per GPU than Hopper was, 30 times better on inference for trillion parameter models. And so that sets the bar for them how big of a model and then they look at the architecture of the NVMe, what they can build. So it is a symbiosis between what we're building, what they're inventing and then keep riding that wave.\n\nAnd that really helps us define helps the next generation AI model."}], "expected_output": [{"speaker": "<PERSON>", "content": "There are some geographic benefits, differences between training and inference. Most folks don't can do training anywhere on globe. So we see big training clusters, which we know for they can get the data center space and can tap into the grid, having good access to power and the economics there is very important. But training doesn't need to be localized. It's remote desktop, it's halfway around the world.\n\nYou can feel the lag and latency, training sign for that. But inference, you kind of do need to be near the user. Inference workloads might be fine. Batch processing inference, fine. Doing a longer chatbot might be okay.\n\nBut if you're doing Gen AI search, you're asking your browser, you want that answer quickly. If it's too slow, then it just immediately your quality of service plummets. So we often see that training clusters, our own capability of inference tends to be either in those same clusters and they'll divide it up. Just like the clouds are providing regions, they'll folks will and they can then serve it with both training and inference. I would just say the training part is a little bit more specialized because super big clusters can be wherever that makes the most sense for them to build and invest in the building.\n\nYes. But they are largely using more and more the same training and for training and inference, the same infrastructure. That again goes to the value they can be using it for training and flip it over to inference. If you saw what we launched our Blackwell GB200, MBL72, we talked a lot about inference because those they got to run that mixture of experts work through and they also have the same infrastructure that can be used for training as well. And that's fair.\n\nAt the same time, we also make sure that they can take that the same building blocks and vary the sizes and capabilities. GB200, the NVL 72 is designed for trillion parameter. The more modest size 70B or 7B, we have an NVL 2, which is just 2 Grace Blackwells tied together, which fit nicely in the standard server design and could be deployed anywhere. With each telco often will have a they'll have a cage. The cage has 100 kilowatts, can't exceed that.\n\nSo the metric is, can I put what kind of GPUs or what kind of servers can I put in there that maybe models like hand at the edge, you'll do something different than you'll do for a big OpenAI data center or some such? And that's why we have both kind of"}, {"speaker": "Anonymous", "content": "Since you have been so intimately involved with CUDA since its founding, right, how do you address the pushback that people have is that software abstraction is being done away from CUDA and it will make CUDA obsolete at some point that that's not really a sustainable moat for NVIDIA. How do you address that pushback?"}, {"speaker": "<PERSON>", "content": "Yes, I think the moat is a complicated word and what does it mean? The innovation come what makes the platform useful is how many developers it has on it, how many users are base people can get the access so that next AI invention can be make sure it's compatible with that architecture what it can do. These foundation, these new next generation, they're often they're not academic exercises. They are designed to what the limits of the capability are trained. And many of the models that we are enjoying today are actually trained like or started training like 2 years ago.\n\nThere's a lag unfortunately in terms of how long it takes to when the data center gets stood up to when this they're obviously thinking we're explaining what we're building to try to shorten this process, but it is directly influencing the scale of what they can build. Not just the number with every generation, we also improved the performance on a per GPU basis by X factors. <PERSON> is like 4 or 5 times better at training per GPU than Hopper was, 30 times better on inference for trillion parameter models. And so that sets the bar for them how big of a model and then they look at the architecture of the NVMe, what they can build. So it is a symbiosis between what we're building, what they're inventing and then keep riding that wave.\n\nAnd that really helps us define helps the next generation AI model."}]}, {"input_paragraphs": [{"speaker": "<PERSON>", "content": "Yes. So first, we support all different Amazon has their EFA networking, which we support and execute toward. Each of the hyper scalers has different flavors of their own Ethernet or networking or some have taken the decision to get the best you see that with Microsoft and they're matching our performance 1 to 1 in benchmarks like MLPerf connecting 10,000 GPUs with InfiniBand and they have a 10,000 GPU plus you get the same score. They know they're getting the best on MLPerf. Ethernet is tricky in the sense that the standard Ethernet infrastructure Ethernet is really important, networking technology.\n\nIt has a huge ecosystem of software capabilities for managing at scale. <PERSON>ther<PERSON> was originally designed for sort of that north south use case. You have a server, he wants to talk to the rest of the world, you have a CPU core, he wants to talk to the rest of the world. That's what Ethernet did. But it was for the traditional use cases.\n\nWhen you get to AI, it's a different kind of problem. It's kind of a supercomputing problem. You have these 1,000,000,000 of dollars, we're all trying to train a model like LAMA 3 and the LAMA and now we're going to 100,000 all trying to train even a bigger model. And so that if one of these packets kind of slows down, one of these links gets lost or has a blip, you can take the entire infrastructure slows down because it's waiting for the slowest guy. And <PERSON><PERSON> did this data and make sure that performance was the max possible so everyone could talk to everybody else.\n\nAnd that's the difference between designing for east west versus north south. You don't connection to the person next to you, everybody's happy. But if your connection slowed everybody down, that would be a problem. And if you look at it from a data center standpoint, that's 1,000,000,000 of dollars of wasted GPU that's probably soon to go to town. So that's what SpectrumX is addressing.\n\nTo provide a standard Ethernet support of the standard Ethernet ecosystem, which many hypersize on, but add the technologies that support the east west traffic, the adaptive routing and the congestion control technique, all the stuff that you need to do to make sure that you have that deterministic performance that AI can progress your GPU stay utilized and it's a really hard problem. We've been accelerating our SpectrumX roadmap as a result. We still have InfiniBand, which is obviously very important in performance. But to provide that kind of Ethernet that can go and train giant models. It requires that technology to be embedded, integrated, Ethernet ecosystem.\n\nSo that's what SpectrumX is. Do you"}, {"speaker": "Anonymous", "content": "see the attach rate of your Ethernet switch going up? Because I think NVIDIA has outlined like several $1,000,000,000 of which includes the NICs as well, right? Yes."}, {"speaker": "<PERSON>", "content": "There's a 100,000 GPU training project that's being put together right now, which will be sector mix."}, {"speaker": "Anonymous", "content": "And then as Blackwell rolls out next year, do you see your attach rate of Ethernet?"}, {"speaker": "<PERSON>", "content": "Yes, you'll see a mix of both Ethernet and InfiniBand."}], "expected_output": [{"speaker": "<PERSON>", "content": "Yes. So first, we support all different Amazon has their EFA networking, which we support and execute toward. Each of the hyper scalers has different flavors of their own Ethernet or networking or some have taken the decision to get the best you see that with Microsoft and they're matching our performance 1 to 1 in benchmarks like MLPerf connecting 10,000 GPUs with InfiniBand and they have a 10,000 GPU plus you get the same score. They know they're getting the best on MLPerf. Ethernet is tricky in the sense that the standard Ethernet infrastructure Ethernet is really important, networking technology.\n\nIt has a huge ecosystem of software capabilities for managing at scale. <PERSON>ther<PERSON> was originally designed for sort of that north south use case. You have a server, he wants to talk to the rest of the world, you have a CPU core, he wants to talk to the rest of the world. That's what Ethernet did. But it was for the traditional use cases.\n\nWhen you get to AI, it's a different kind of problem. It's kind of a supercomputing problem. You have these 1,000,000,000 of dollars, we're all trying to train a model like LAMA 3 and the LAMA and now we're going to 100,000 all trying to train even a bigger model. And so that if one of these packets kind of slows down, one of these links gets lost or has a blip, you can take the entire infrastructure slows down because it's waiting for the slowest guy. And <PERSON><PERSON> did this data and make sure that performance was the max possible so everyone could talk to everybody else.\n\nAnd that's the difference between designing for east west versus north south. You don't connection to the person next to you, everybody's happy. But if your connection slowed everybody down, that would be a problem. And if you look at it from a data center standpoint, that's 1,000,000,000 of dollars of wasted GPU that's probably soon to go to town. So that's what SpectrumX is addressing.\n\nTo provide a standard Ethernet support of the standard Ethernet ecosystem, which many hypersize on, but add the technologies that support the east west traffic, the adaptive routing and the congestion control technique, all the stuff that you need to do to make sure that you have that deterministic performance that AI can progress your GPU stay utilized and it's a really hard problem. We've been accelerating our SpectrumX roadmap as a result. We still have InfiniBand, which is obviously very important in performance. But to provide that kind of Ethernet that can go and train giant models. It requires that technology to be embedded, integrated, Ethernet ecosystem.\n\nSo that's what SpectrumX is."}, {"speaker": "Anonymous", "content": "Do you see the attach rate of your Ethernet switch going up? Because I think NVIDIA has outlined like several $1,000,000,000 of which includes the NICs as well, right?"}, {"speaker": "<PERSON>", "content": "Yes. There's a 100,000 GPU training project that's being put together right now, which will be sector mix."}, {"speaker": "Anonymous", "content": "And then as Blackwell rolls out next year, do you see your attach rate of Ethernet?"}, {"speaker": "<PERSON>", "content": "Yes, you'll see a mix of both Ethernet and InfiniBand."}]}, {"input_paragraphs": [{"speaker": "<PERSON>", "content": "Good morning, everyone, and thank"}, {"speaker": "<PERSON><PERSON><PERSON>", "content": "you for joining us at Oppenheimer's 24th Annual Consumer Growth and E Commerce Conference. My name is <PERSON><PERSON><PERSON>. I'm the Senior Food, Grocery and Consumer Products Analyst here at Oppenheimer. I'm very excited to introduce our next presenting company, Walmart. Walmart remains a top pick for us, and we continue to see a strong outperformance case over the next 12 to 18 months, driven by share gains and the potential for margin expansion over time.\n\nJoining us today are President and CEO of Walmart U. S, <PERSON> and Senior Director of 2 of Investor Relations, <PERSON>. So thank you all for being here today. The format of today's session will be a fireside chat. We'll go through a number of questions I prepared.\n\nSo <PERSON>, I wanted to kick it off with a few a couple of macro questions. So Walmart U. S. Continues to see strong top line results in traffic. However, discretionary has remained relatively soft in recent quarters as consumers manage budgets in the face of multiple quarters of inflationary pressures and our protection is still lapping pandemic buying.\n\nSo how would you describe the overall health of your consumer as we sit here today? And what are your expectations for the balance of the year?"}, {"speaker": "<PERSON>", "content": "Hey, Rupesh. Well, first, thanks for having us and thanks for the interest in the company. It was great to see you in person last week as well for our Annual Shareholders Meeting and Associate event. We are I think let me talk about the business and I'll definitely address your question."}], "expected_output": [{"speaker": "<PERSON><PERSON><PERSON>", "content": "Good morning, everyone, and thank you for joining us at Oppenheimer's 24th Annual Consumer Growth and E Commerce Conference. My name is <PERSON><PERSON><PERSON>. I'm the Senior Food, Grocery and Consumer Products Analyst here at Oppenheimer. I'm very excited to introduce our next presenting company, Walmart. Walmart remains a top pick for us, and we continue to see a strong outperformance case over the next 12 to 18 months, driven by share gains and the potential for margin expansion over time.\n\nJoining us today are President and CEO of Walmart U. S, <PERSON> and Senior Director of 2 of Investor Relations, <PERSON>. So thank you all for being here today. The format of today's session will be a fireside chat. We'll go through a number of questions I prepared.\n\nSo <PERSON>, I wanted to kick it off with a few a couple of macro questions. So Walmart U. S. Continues to see strong top line results in traffic. However, discretionary has remained relatively soft in recent quarters as consumers manage budgets in the face of multiple quarters of inflationary pressures and our protection is still lapping pandemic buying.\n\nSo how would you describe the overall health of your consumer as we sit here today? And what are your expectations for the balance of the year?"}, {"speaker": "<PERSON>", "content": "Hey, Rupesh. Well, first, thanks for having us and thanks for the interest in the company. It was great to see you in person last week as well for our Annual Shareholders Meeting and Associate event. We are I think let me talk about the business and I'll definitely address your question."}]}, {"input_paragraphs": [{"speaker": "<PERSON><PERSON><PERSON>", "content": "Okay, great. So I'll have to wait for the future additions."}, {"speaker": "<PERSON>", "content": "Yes. There's always more coming. Okay. Right in the middle of the quarter, so And"}, {"speaker": "<PERSON><PERSON><PERSON>", "content": "then moving on to a few other areas that are topical in our investor conversations lately. So your general merchandise category remains in negative territory, but there appears to be underlying strength on the unit front and bright spots coming out of marketplace. Is it possible to see an inflection back to positive trends this year? Or is there anything else you can share with us in regards to how you think about the recovery in general merchandise from here? And then what are the green shoots you'd call out within your general merchandise categories?"}], "expected_output": [{"speaker": "<PERSON><PERSON><PERSON>", "content": "Okay, great. So I'll have to wait for the future additions."}, {"speaker": "<PERSON>", "content": "Yes. There's always more coming. Okay. Right in the middle of the quarter, so"}, {"speaker": "<PERSON><PERSON><PERSON>", "content": "And then moving on to a few other areas that are topical in our investor conversations lately. So your general merchandise category remains in negative territory, but there appears to be underlying strength on the unit front and bright spots coming out of marketplace. Is it possible to see an inflection back to positive trends this year? Or is there anything else you can share with us in regards to how you think about the recovery in general merchandise from here? And then what are the green shoots you'd call out within your general merchandise categories?"}]}, {"input_paragraphs": [{"speaker": "<PERSON>", "content": "Yes. Well, thank you for saying that. The team will be thrilled to hear that. They work really hard at it and then they get up every day and want to do a great job. And so having people that have covered us like you for so long, they can say that I think will be a really nice compliment for them.\n\nThere are a few things on inventory, starting with what do we think demand is. We have an intelligence product that's much better than what we've ever had in terms of being able to determine what we think demand will be by item, by zip code, we optimize it in a number of ways. The second is the inventory management team here in the home office have done a really good job using new technologies and understanding demand forecasting assortments. Our markdown discipline is another thing that's been really strong and you wouldn't want to go into a period where you get possible deflation with extra inventory because it puts pressure on your ability to take prices down because you have more markdowns than what you need. So, the buying has been much closer.\n\nAnd then the stores that I think the change we made this January for the store managers and then last week with our hourly associates puts ownership back in their hands to merchandise their store. We'll never know the store at the market level, at the zip level, at city level the way a store manager does. And we want them to have the flexibility, the tools and participate in the upside. So we increased their incentive. It includes the entire P and L.\n\nSo, their incentive on sales growing and profit growing and we made them shareholders. So, they're owners of the business. We said, we want you to think it like an owner and act like an owner and they're owners. So we want them to be a part of it. So it's all those things put together.\n\nOf course, we'll continue to watch and push and learn. We have a meeting every Friday where our entire field can bring up any feedback they want. And when there's something they bring up they need, then we have a team of people here who are ready to respond"}, {"speaker": "<PERSON><PERSON><PERSON>", "content": "and serve them on behalf of our customers. So, it sounds like the manager and other wage investments, you guys are already seeing a return from those investments you made earlier this year?"}], "expected_output": [{"speaker": "<PERSON>", "content": "Yes. Well, thank you for saying that. The team will be thrilled to hear that. They work really hard at it and then they get up every day and want to do a great job. And so having people that have covered us like you for so long, they can say that I think will be a really nice compliment for them.\n\nThere are a few things on inventory, starting with what do we think demand is. We have an intelligence product that's much better than what we've ever had in terms of being able to determine what we think demand will be by item, by zip code, we optimize it in a number of ways. The second is the inventory management team here in the home office have done a really good job using new technologies and understanding demand forecasting assortments. Our markdown discipline is another thing that's been really strong and you wouldn't want to go into a period where you get possible deflation with extra inventory because it puts pressure on your ability to take prices down because you have more markdowns than what you need. So, the buying has been much closer.\n\nAnd then the stores that I think the change we made this January for the store managers and then last week with our hourly associates puts ownership back in their hands to merchandise their store. We'll never know the store at the market level, at the zip level, at city level the way a store manager does. And we want them to have the flexibility, the tools and participate in the upside. So we increased their incentive. It includes the entire P and L.\n\nSo, their incentive on sales growing and profit growing and we made them shareholders. So, they're owners of the business. We said, we want you to think it like an owner and act like an owner and they're owners. So we want them to be a part of it. So it's all those things put together.\n\nOf course, we'll continue to watch and push and learn. We have a meeting every Friday where our entire field can bring up any feedback they want. And when there's something they bring up they need, then we have a team of people here who are ready to respond and serve them on behalf of our customers."}, {"speaker": "<PERSON><PERSON><PERSON>", "content": "So, it sounds like the manager and other wage investments, you guys are already seeing a return from those investments you made earlier this year?"}]}, {"input_paragraphs": [{"speaker": "<PERSON>", "content": "I think that question is broad and it's a great question. Why don't you address the financials and profitability? But I think the segment leaders ought to talk about the question in a broader"}, {"speaker": "<PERSON>", "content": "sense. Yes. So it's different by segment, 1st of all. In Sam's today, we're profitable in e commerce, really excited about that business. Before I answer though, <PERSON>, like we are seeing in our business that e commerce and brick and mortar are increasingly converging, and it's going to be more and more difficult over time to separate those out and really scrutinize the profitability of each of those segments."}], "expected_output": [{"speaker": "<PERSON>", "content": "I think that question is broad and it's a great question. Why don't you address the financials and profitability? But I think the segment leaders ought to talk about the question in a broader sense."}, {"speaker": "<PERSON>", "content": "Yes. So it's different by segment, 1st of all. In Sam's today, we're profitable in e commerce, really excited about that business. Before I answer though, <PERSON>, like we are seeing in our business that e commerce and brick and mortar are increasingly converging, and it's going to be more and more difficult over time to separate those out and really scrutinize the profitability of each of those segments."}]}, {"input_paragraphs": [{"speaker": "<PERSON>", "content": "So if you looked at it through the lens of Medicare, Medicare Advantage, Medicaid, cash, commercial payers to have this conversation, you would see that with Medicare Advantage 65 and above, there is a business model there that has a return on investment. So, conceptually, the idea that you can build out an ability to serve that population and have a set of financials that looks good, but then layer on some of the other forms of payment in a way where you're leveraging the fixed costs and you can afford to provide care for them as well so that more lives are impacted, that was the path we were going down. Margins are being driven by CMS reimbursement rates and other things that are beyond commercial or business issues. And as that changed, that changes the model. And then the future doesn't look like what you thought it was going to look like as we were building this out.\n\nDoes that help?"}, {"speaker": "<PERSON>", "content": "Yes, exactly"}, {"speaker": "<PERSON><PERSON>", "content": "right. Did you want me to talk"}, {"speaker": "<PERSON>", "content": "about <PERSON><PERSON><PERSON>?"}, {"speaker": "<PERSON>", "content": "Yes. Anything you want to say about healthcare?"}, {"speaker": "<PERSON>", "content": "Well, I"}, {"speaker": "<PERSON><PERSON>", "content": "would just say what we have transitioned to in Walmart is a membership. What we found was pharmacy and OTC is really important part of the store. And we had the facility available in the stores for customers to meet with a doctor, but it was being massively underutilized. And so we rolled it into a membership, and last year, we had over a 1000000 visits to the doctor, and we have a lot more capacity to be able to sell more memberships, but it's effectively allowing people to deal with acute and chronic illness, and it's about $3.50 a month. And, you know, what what we're seeing is we'd love to be able to meet those needs, but then move into preventative care as well to a nutrition.\n\nBut it all starts with actually, 1st of all, helping them deal with acute and chronic."}], "expected_output": [{"speaker": "<PERSON>", "content": "So if you looked at it through the lens of Medicare, Medicare Advantage, Medicaid, cash, commercial payers to have this conversation, you would see that with Medicare Advantage 65 and above, there is a business model there that has a return on investment. So, conceptually, the idea that you can build out an ability to serve that population and have a set of financials that looks good, but then layer on some of the other forms of payment in a way where you're leveraging the fixed costs and you can afford to provide care for them as well so that more lives are impacted, that was the path we were going down. Margins are being driven by CMS reimbursement rates and other things that are beyond commercial or business issues. And as that changed, that changes the model. And then the future doesn't look like what you thought it was going to look like as we were building this out.\n\nDoes that help?"}, {"speaker": "<PERSON>", "content": "Yes, exactly right."}, {"speaker": "<PERSON><PERSON>", "content": "Did you want me to talk about W<PERSON><PERSON>?"}, {"speaker": "<PERSON>", "content": "Yes. Anything you want to say about healthcare?"}, {"speaker": "<PERSON><PERSON>", "content": "Well, I would just say what we have transitioned to in Walmart is a membership. What we found was pharmacy and OTC is really important part of the store. And we had the facility available in the stores for customers to meet with a doctor, but it was being massively underutilized. And so we rolled it into a membership, and last year, we had over a 1000000 visits to the doctor, and we have a lot more capacity to be able to sell more memberships, but it's effectively allowing people to deal with acute and chronic illness, and it's about $3.50 a month. And, you know, what what we're seeing is we'd love to be able to meet those needs, but then move into preventative care as well to a nutrition.\n\nBut it all starts with actually, 1st of all, helping them deal with acute and chronic."}]}, {"input_paragraphs": [{"speaker": "<PERSON>", "content": "Hi, <PERSON>, Melius Research. Thanks for today. That was very fun. It's never a letdown, that's for sure. So my question is that, you obviously have a very strong focus on ROIC, and that's what led you to the closure of the health centers.\n\nBut I guess my question is, when you look at your portfolio, how many more assets do you think are opportunities to either improve ROIC or exit, 1 or the other? Like how much low hanging fruit is there? And then the second I just want to ask the second related not related, but where are you at with respect to financial, generally in your flywheel? Because it hasn't really been something that you've talked about."}, {"speaker": "<PERSON>", "content": "Financial services?"}, {"speaker": "<PERSON>", "content": "Yes."}], "expected_output": [{"speaker": "<PERSON>", "content": "Hi, <PERSON>, Melius Research. Thanks for today. That was very fun. It's never a letdown, that's for sure. So my question is that, you obviously have a very strong focus on ROIC, and that's what led you to the closure of the health centers.\n\nBut I guess my question is, when you look at your portfolio, how many more assets do you think are opportunities to either improve ROIC or exit, 1 or the other? Like how much low hanging fruit is there? And then the second I just want to ask the second related not related, but where are you at with respect to financial, generally in your flywheel? Because it hasn't really been something that you've talked about."}, {"speaker": "<PERSON>", "content": "Financial services?"}, {"speaker": "<PERSON>", "content": "Yes."}]}, {"input_paragraphs": [{"speaker": "<PERSON>", "content": "Yes. Let me take that. We're really focused on price and opening price. We start our meetings every Monday, I say, talking about units and price gaps versus the market at the item level. That's really important that we maintain opening price.\n\nAnd in fashion, yes, they've definitely improved the perception, the brands, the look and feel of the remodeled stores. But if you get into the basic categories, you'll find great value on men's basics, kids basics. That hasn't changed and that won't change. Better goods is a replacement for a brand we had for years called Sam's Choice. A reminder that 70% of the product is under $5 And the team worked really hard on development.\n\nI think some of you got to go to the culinary center. But the teams worked really hard on ingredients, the taste profile, they've they've vegan options. They have options that are not vegan, but again, 70% under $5 Just a few items have launched. Pleased with the results so far. We have about 10 categories over the next week or 2 that will launch with up to about 300 items by September.\n\nSo there is a balance. As far as balancing all the customers that we get the privilege of serving today and new customers, what I really like about the Walmart offer today is it's much more flexible. And there will always be <PERSON> said this earlier, there will always be headwinds, there will always be tailwinds. But we want to be great at the counter at opening price. We can you can pick up for the same price as the counter.\n\nYou can have it delivered for the same price. Service offerings like Walmart Plus enable a lower cost of delivery. But the merchandise price, we are really proud of our everyday low price. We're proud of our rollbacks. And but the channel flexibility, just keep that in mind that if different customers are looking for different things, different times of the week and different times of the year.\n\nAnd I'll just finish this with where John David started with the number of customers who are jumping in to the sub-three hour option has been really helpful. So you may do your stock up trip. Several of us probably do in home delivery, and we keep you in stock at home. But there's always that day when you miss something, you forgot something, you have a birthday party, you ran out of milk for cereal, and we can have it there in in many times under an hour, but we we we market it as sub-three hours,"}, {"speaker": "<PERSON>", "content": "and that's growing really well. The way we were taught to be merchants was to take care of good and then better and then best. So if you imagine an 8 foot section or 12 foot section, go look at any category in the store, but the same concepts would apply to the broader e commerce offer too. And you'll see a buyer thinking through, I started with a nail opening price point. Do I have the right amount packed out?\n\nDo I have the right price point? Do I have the right quality offer? Now that that's taken care of, now let's go to better. And frequently, what happens is that merchants tend to over assort better and they fail to get to best when really they could be tighter on better, which would enable more room for best. And when you coordinate and move across whether it's better goods or what <PERSON> and the team have done with the remodels and apparel, and you kind of all move at once, so that apparel gets better at the same time home gets better at the same time better goods happens at the same time a Walmart Plus membership enables convenience and time savings and delivery.\n\nIt actually is more complete as an offer for a higher income customer. And in the past, I think we've sometimes tried to move to best in some categories, but not as coordinated as what the is doing today. And I think it's one of the reasons why we're doing well with people that have more money. And I think it's totally possible to do that and still have the best opening price point offer because you did that first and it was foundational."}], "expected_output": [{"speaker": "<PERSON>", "content": "Yes. Let me take that. We're really focused on price and opening price. We start our meetings every Monday, I say, talking about units and price gaps versus the market at the item level. That's really important that we maintain opening price.\n\nAnd in fashion, yes, they've definitely improved the perception, the brands, the look and feel of the remodeled stores. But if you get into the basic categories, you'll find great value on men's basics, kids basics. That hasn't changed and that won't change. Better goods is a replacement for a brand we had for years called Sam's Choice. A reminder that 70% of the product is under $5 And the team worked really hard on development.\n\nI think some of you got to go to the culinary center. But the teams worked really hard on ingredients, the taste profile, they've they've vegan options. They have options that are not vegan, but again, 70% under $5 Just a few items have launched. Pleased with the results so far. We have about 10 categories over the next week or 2 that will launch with up to about 300 items by September.\n\nSo there is a balance. As far as balancing all the customers that we get the privilege of serving today and new customers, what I really like about the Walmart offer today is it's much more flexible. And there will always be <PERSON> said this earlier, there will always be headwinds, there will always be tailwinds. But we want to be great at the counter at opening price. We can you can pick up for the same price as the counter.\n\nYou can have it delivered for the same price. Service offerings like Walmart Plus enable a lower cost of delivery. But the merchandise price, we are really proud of our everyday low price. We're proud of our rollbacks. And but the channel flexibility, just keep that in mind that if different customers are looking for different things, different times of the week and different times of the year.\n\nAnd I'll just finish this with where John David started with the number of customers who are jumping in to the sub-three hour option has been really helpful. So you may do your stock up trip. Several of us probably do in home delivery, and we keep you in stock at home. But there's always that day when you miss something, you forgot something, you have a birthday party, you ran out of milk for cereal, and we can have it there in in many times under an hour, but we we we market it as sub-three hours, and that's growing really well."}, {"speaker": "<PERSON>", "content": "The way we were taught to be merchants was to take care of good and then better and then best. So if you imagine an 8 foot section or 12 foot section, go look at any category in the store, but the same concepts would apply to the broader e commerce offer too. And you'll see a buyer thinking through, I started with a nail opening price point. Do I have the right amount packed out?\n\nDo I have the right price point? Do I have the right quality offer? Now that that's taken care of, now let's go to better. And frequently, what happens is that merchants tend to over assort better and they fail to get to best when really they could be tighter on better, which would enable more room for best. And when you coordinate and move across whether it's better goods or what <PERSON> and the team have done with the remodels and apparel, and you kind of all move at once, so that apparel gets better at the same time home gets better at the same time better goods happens at the same time a Walmart Plus membership enables convenience and time savings and delivery.\n\nIt actually is more complete as an offer for a higher income customer. And in the past, I think we've sometimes tried to move to best in some categories, but not as coordinated as what the is doing today. And I think it's one of the reasons why we're doing well with people that have more money. And I think it's totally possible to do that and still have the best opening price point offer because you did that first and it was foundational."}]}, {"input_paragraphs": [{"speaker": "<PERSON>", "content": "<PERSON>, Goldman Sachs. Thanks for having us these last two days. It's been great. We talked a little bit in some of the other questions. It's been touched upon, but I wanted to ask about pricing.\n\nI know there's been maybe some hesitation more recently from suppliers to negotiate on price just given the inflationary environment seems to be easing. You mentioned"}, {"speaker": "<PERSON>", "content": "the rollbacks. But where are"}, {"speaker": "<PERSON>", "content": "you with those conversations now with their other pricing actions being taken in the market influencing your conversations? Yes. I think other pricing actions being taken in the market influencing your conversations?"}], "expected_output": [{"speaker": "<PERSON>", "content": "<PERSON>, Goldman Sachs. Thanks for having us these last two days. It's been great. We talked a little bit in some of the other questions. It's been touched upon, but I wanted to ask about pricing.\n\nI know there's been maybe some hesitation more recently from suppliers to negotiate on price just given the inflationary environment seems to be easing. You mentioned the rollbacks. But where are you with those conversations now with their other pricing actions being taken in the market influencing your conversations? Yes. I think other pricing actions being taken in the market influencing your conversations?"}]}, {"input_paragraphs": [{"speaker": "<PERSON><PERSON>", "content": "Sure. So, look, this is a time when technology is actually changing a lot"}, {"speaker": "<PERSON>", "content": "of areas."}, {"speaker": "<PERSON><PERSON>", "content": "And especially in retail, if you look, we start with the customer. You look at it from the customer perspective. We look at how technology can actually improve our customer experience, make them make it a lot more seamless and a lot more delightful. So the technologies that we are investing in, obviously Gen AI and ML and AI, we have been investing in for quite some time. The same things also apply to how we empower our associates to be able to help our customers, whether it's out in the field or in the home office or in corporate.\n\nAnd then the last part of it is around how do we put data to work. So Walmart has this unique advantage that we touch our customers in more ways than just about anybody else at scale. And that gives us the ability to be able to drive insights about our customers and be able to serve them in more ways than anybody else can do. And so these are sort of the 3 major areas where we look at, delighting customers, empowering associates to be more efficient to be able to serve our customers better and then using the power of data and intelligence to actually intelligently drive efficiencies and automation throughout how we move merchandise, how we orchestrate the moment of inventory as well. So that's the lens by which we look at all technology.\n\nAnd of course, the most recent ones are obviously around Gen AI and the LLMs and all the new things that have started coming out."}], "expected_output": [{"speaker": "<PERSON><PERSON>", "content": "Sure. So, look, this is a time when technology is actually changing a lot of areas. And especially in retail, if you look, we start with the customer. You look at it from the customer perspective. We look at how technology can actually improve our customer experience, make them make it a lot more seamless and a lot more delightful. So the technologies that we are investing in, obviously Gen AI and ML and AI, we have been investing in for quite some time. The same things also apply to how we empower our associates to be able to help our customers, whether it's out in the field or in the home office or in corporate.\n\nAnd then the last part of it is around how do we put data to work. So Walmart has this unique advantage that we touch our customers in more ways than just about anybody else at scale. And that gives us the ability to be able to drive insights about our customers and be able to serve them in more ways than anybody else can do. And so these are sort of the 3 major areas where we look at, delighting customers, empowering associates to be more efficient to be able to serve our customers better and then using the power of data and intelligence to actually intelligently drive efficiencies and automation throughout how we move merchandise, how we orchestrate the moment of inventory as well. So that's the lens by which we look at all technology.\n\nAnd of course, the most recent ones are obviously around Gen AI and the LLMs and all the new things that have started coming out."}]}]}