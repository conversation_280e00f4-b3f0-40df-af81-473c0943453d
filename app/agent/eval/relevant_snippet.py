import json
import logging
import traceback
from typing import Any, Dict, List, Optional, Tuple

from pydantic import BaseModel
from sklearn.metrics import f1_score, precision_score, recall_score

from agent.agents.search_filter import SearchFilterAgent
from agent.config.agent import SearchFilterAgentConfig as AgentConfig
from agent.eval.base import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>
from agent.models import Cha<PERSON>, ChatTurn, aget_eval_user
from agent.observer import Observer
from agent.structs import MockChunk
from biz.search.snippet import SnippetSearcher

logger = logging.getLogger(__name__)


class DocChunk(BaseModel):
    source: str
    content: str
    is_relevant: bool
    reason: Optional[str] = None
    relevant_snippet: Optional[str] = None
    # score from the snippet classifier when generating this sample
    score: Optional[float] = None

    # These fields are filled in by the eval
    predicted_score: Optional[float] = None
    predicted_rel_snippet: Optional[str] = None
    predicted_reason: Optional[str] = None


class RelevantSnippetSample(Sample):
    chunks: List[DocChunk]


class RelevantSnippetResult(EvalResult[RelevantSnippetSample]):
    sample: RelevantSnippetSample

    true_labels: List[bool]
    predicted_labels: List[bool]
    num_positives: int
    predicted_positives: int
    precision: float
    recall: float
    f1: float
    false_positives: List[DocChunk]
    false_negatives: List[DocChunk]

    class Config:
        """Configuration for this pydantic object."""

        arbitrary_types_allowed = True

    @classmethod
    def empty_result(cls, sample: RelevantSnippetSample) -> "RelevantSnippetResult":
        return cls(
            sample=sample,
            true_labels=[],
            predicted_labels=[],
            num_positives=0,
            predicted_positives=0,
            precision=0,
            recall=0,
            f1=0,
            false_positives=[],
            false_negatives=[],
        )


class RelevantSnippetEval(Eval[RelevantSnippetSample, RelevantSnippetResult]):
    """Eval for determining if a snippet from a doc is relevant to a question."""

    config: AgentConfig = AgentConfig(
        new_design=True,
        # snippet_classifier_model_name="cross-encoder/ms-marco-MiniLM-L-6-v2-finetuned-silver30-epochs4-2024-06-11",
        # snippet_classifier_model_name="cross-encoder/ms-marco-MiniLM-L-6-v2",
        snippet_classifier_model_name="cross-encoder/ms-marco-TinyBERT-L-2-v2-finetuned-silver50-epochs10-2024-06-12",
        # snippet_classifier_model_name="gpt-4o",
        relevance_threshold=0.3,
    )
    snippet_searcher: SnippetSearcher = SnippetSearcher(is_mentioned_threshold=0.8)

    def load_sample(self, sample_dict: Dict) -> RelevantSnippetSample:
        return RelevantSnippetSample(
            question=sample_dict["question"],
            chunks=[
                DocChunk(
                    source=chunk["source"],
                    content=chunk["content"],
                    is_relevant=chunk["is_relevant"],
                    relevant_snippet=chunk.get("relevant_snippet"),
                    score=chunk.get("score"),
                )
                for chunk in sample_dict["chunks"]
            ],
        )

    async def run_for_one_sample(
        self, sample: RelevantSnippetSample
    ) -> RelevantSnippetResult:
        try:
            chat_turn = await ChatTurn.objects.acreate(
                chat=self.chat, user_msg=sample.question
            )
            agent = SearchFilterAgent(
                chat_turn=chat_turn,
                observer=self.observer,
                config=self.config,
                streaming=False,
            )
            data_chunks = [
                MockChunk(
                    index_id=str(i),  # just use a counter as index_id
                    source=snippet.source,
                    content=snippet.content,
                    score=snippet.score,
                )
                for i, snippet in enumerate(sample.chunks)
            ]

            reason_by_id = {}
            if self.config.snippet_classifier_model_name.startswith("gpt-"):
                _, rel_snippet_by_id, reason_by_id = await agent.score_and_filter_gpt(
                    data_chunks, sample.question
                )
            else:
                _, rel_snippet_by_id = await agent.score_and_filter(
                    data_chunks, sample.question
                )

            # fill in predicted fields
            for (i, chunk), d in zip(enumerate(sample.chunks), data_chunks):
                # TODO: fix this
                if str(i) not in rel_snippet_by_id:
                    import ipdb

                    ipdb.set_trace()
                    pass
                chunk.predicted_rel_snippet, snippet_score = rel_snippet_by_id.get(
                    str(i)
                )
                chunk.predicted_score = min(d.score, snippet_score)
                chunk.predicted_reason = reason_by_id.get(str(i))

            return await self.eval_sample(sample=sample)

        except Exception:
            logger.error(f"** Error when answering : '{sample.question}' **")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return RelevantSnippetResult.empty_result(sample)

    async def eval_sample(
        self, sample: RelevantSnippetSample, **kwargs: Any
    ) -> RelevantSnippetResult:
        true_labels = []
        predicted_labels = []
        false_positives = []
        false_negatives = []

        for chunk in sample.chunks:
            true_labels.append(chunk.is_relevant)
            if chunk.predicted_score is None:
                raise ValueError("Predicted score is not set")
            threshold = self.config.relevance_threshold
            predicted_labels.append(chunk.predicted_score >= threshold)
            if not chunk.is_relevant and chunk.predicted_score >= threshold:
                false_positives.append(chunk)
            if chunk.is_relevant and chunk.predicted_score < threshold:
                false_negatives.append(chunk)

        precision = precision_score(true_labels, predicted_labels)
        recall = recall_score(true_labels, predicted_labels)
        f1 = f1_score(true_labels, predicted_labels)

        return RelevantSnippetResult(
            sample=sample,
            true_labels=true_labels,
            predicted_labels=predicted_labels,
            num_positives=sum(true_labels),
            predicted_positives=sum(predicted_labels),
            precision=precision,
            recall=recall,
            f1=f1,
            false_positives=false_positives,
            false_negatives=false_negatives,
        )

    def get_f1_score(self, eval_results: List[RelevantSnippetResult]) -> float:
        all_true_labels = [label for res in eval_results for label in res.true_labels]
        all_predicted_labels = [
            label for res in eval_results for label in res.predicted_labels
        ]
        return f1_score(all_true_labels, all_predicted_labels)

    def gen_eval_report(
        self,
        eval_results: List[RelevantSnippetResult],
        verbose: bool = False,
    ) -> str:
        lines = []
        lines.append("===== Agent Config / Env Vars =====\n")
        lines.append(
            f"snippet_classifier_model_name:\n{str(self.config.snippet_classifier_model_name)}"
        )
        lines.append(f"relevance_threshold: {self.config.relevance_threshold}")
        lines.append("\n")

        for i, res in enumerate(eval_results, 1):
            lines.append(f"=========== Sample {i} ===========")
            lines.append(f"【Question】:\n {res.sample.question}\n")
            lines.append("【Metrics】:")
            lines.append(f"Total chunks: {len(res.sample.chunks)}")
            lines.append(f"Num positives: {res.num_positives}")
            lines.append(f"Predicted positives: {res.predicted_positives}")
            lines.append(f"Precision: {res.precision}")
            lines.append(f"Recall: {res.recall}")
            lines.append(f"F1: {res.f1}")
            if verbose:
                lines.append("False positives:\n")
                for fp in res.false_positives:
                    lines.append(f"{json.dumps(fp.model_dump(), indent=2)}")
                lines.append("False negatives:\n")
                for fn in res.false_negatives:
                    lines.append(f"{json.dumps(fn.model_dump(), indent=2)}")
            lines.append("\n")

        # Aggregate results and output final scores
        all_num_positives = sum(res.num_positives for res in eval_results)
        all_predicted_positives = sum(res.predicted_positives for res in eval_results)
        all_true_labels = [label for res in eval_results for label in res.true_labels]
        all_predicted_labels = [
            label for res in eval_results for label in res.predicted_labels
        ]
        lines.append("======= Final Score =======\n")
        lines.append(
            f"Total chunks: {sum(len(res.sample.chunks) for res in eval_results)}"
        )
        lines.append(f"Num positives: {all_num_positives}")
        lines.append(f"Predicted positives: {all_predicted_positives}")
        lines.append(
            f"Precision: {precision_score(all_true_labels, all_predicted_labels)}"
        )
        lines.append(f"Recall: {recall_score(all_true_labels, all_predicted_labels)}")
        lines.append(f"F1: {f1_score(all_true_labels, all_predicted_labels)}")
        lines.append("======= LLM Cost =======\n")
        lines.append(self.observer.get_llm_costs_str())
        return "\n".join(lines)


async def run_eval_for_best_score(
    model_name: str,
    test_data_file: str,
    concurrency: int = 8,
    thresholds: List[float] = [0.2, 0.3, 0.4, 0.5],
) -> Tuple[float, float]:
    """Run eval on several threshold values and get the best score
    along with the threshold for a model
    NOTE: maybe refactor to make it generic for other evals
    """
    user = await aget_eval_user()
    chat = await Chat.objects.acreate(user=user)
    t_with_scores = []
    for t in thresholds:
        eval_instance = RelevantSnippetEval(
            observer=Observer(),
            chat=chat,
            config=AgentConfig(
                new_design=True,
                snippet_classifier_model_name=model_name,
                relevance_threshold=t,
            ),
        )

        data_dir = f"relevant_snippet/{test_data_file}"
        results = await eval_instance.run(data_dir, concurrency)
        f1 = eval_instance.get_f1_score(results)
        logger.info(f"Threshold: {t}, F1: {f1}")
        t_with_scores.append((t, f1))

    best_t, best_score = max(t_with_scores, key=lambda x: x[1])
    logger.info(f"Best threshold: {best_t}, Best F1: {best_score}")
    return best_t, best_score
