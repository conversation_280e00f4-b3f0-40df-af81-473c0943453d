"""ARCHIVE"""
# import logging
# import os
# import traceback
# from abc import abstractmethod
# from typing import Any, Dict, List, Tuple
#
# from pydantic import BaseModel
#
# from agent.agents.planner import FetcherPlan
# from agent.agents.retrieval import PlanRetrievalAgent
# from agent.config.agent import PlanRetrievalAgentConfig as AgentConfig
# from agent.eval.base import <PERSON><PERSON>, <PERSON>l<PERSON><PERSON><PERSON>, <PERSON>ple
# from agent.models import ChatTurn, ChatTurnStates
# from biz.search.snippet import SnippetSearcher
# from biz.structs import DocChunk
#
# logger = logging.getLogger(__name__)
#
#
# class Snippet(BaseModel):
#     original_url: str
#     snippet: str
#
#
# class RetrievalSample(Sample):
#     expected_retrieval: List[Snippet]
#     func_calls: List[Dict[str, Any]] = []
#
#
# class RetrievalEvalResult(EvalResult[RetrievalSample]):
#     sample: RetrievalSample
#     retrieved_snippets: List[Snippet] = []
#     func_calls: List[Dict[str, Any]] = []
#
#     @property
#     def num_retrieved(self) -> int:
#         return len(self.retrieved_snippets)
#
#     @property
#     def num_relevant(self) -> int:
#         return len(self.sample.expected_retrieval)
#
#     @property
#     def missed_snippets(self) -> List[Snippet]:
#         return [
#             snippet
#             for snippet in self.sample.expected_retrieval
#             if snippet not in self.retrieved_snippets
#         ]
#
#     @property
#     def recall(self) -> float:
#         assert self.num_retrieved <= self.num_relevant
#         return round(self.num_retrieved / self.num_relevant, 2)
#
#     @classmethod
#     def empty_result(cls, sample: RetrievalSample) -> "RetrievalEvalResult":
#         return cls(sample=sample)
#
#
# class RetrievalEval(Eval[RetrievalSample, RetrievalEvalResult]):
#     """Base class for evaluation on the retrieval part of the agent."""
#
#     snippet_searcher: SnippetSearcher = SnippetSearcher(is_mentioned_threshold=0.8)
#     # TODO(rui): Make this configurable from eval
#     config: AgentConfig = AgentConfig(
#         searcher_type="pinecone_hybrid",
#     )
#
#     def load_sample(self, sample_dict: Dict) -> RetrievalSample:
#         return RetrievalSample(
#             question=sample_dict["question"],
#             as_of_date=sample_dict["as_of_date"],
#             expected_retrieval=[
#                 Snippet(
#                     original_url=snippet["original_url"], snippet=snippet["snippet"]
#                 )
#                 for snippet in sample_dict["expected_retrieval"]
#             ],
#             func_calls=sample_dict["func_calls"],
#         )
#
#     @abstractmethod
#     async def _run_inner(
#         self, sample: RetrievalSample, agent: PlanRetrievalAgent
#     ) -> Tuple[FetcherPlan, List[DocChunk], List[DocChunk]]:
#         pass
#
#     async def run_for_one_sample(self, sample: RetrievalSample) -> RetrievalEvalResult:
#         try:
#             chat_turn = await ChatTurn.objects.acreate(
#                 chat=self.chat, user_msg=sample.question
#             )
#             agent = PlanRetrievalAgent(
#                 chat_turn=chat_turn,
#                 observer=self.observer,
#                 config=self.config,
#                 streaming=False,
#             )
#             plan, fetched_data, ranked_data = await self._run_inner(sample, agent)
#             await ChatTurnStates.objects.acreate(
#                 chat_turn=chat_turn,
#                 extra_data={
#                     "plan": plan.as_logging_dict(),
#                 },
#                 retrieval={
#                     "fetched_data": [d.as_logging_dict() for d in fetched_data],
#                     "ranked_data": [d.as_logging_dict() for d in ranked_data],
#                 },
#                 perf=self.observer.perf,
#                 llm_prompts=self.observer.llm_prompts,
#                 llm_outputs=self.observer.llm_outputs,
#             )
#         except Exception:
#             logger.error(f"** Error when answering : '{sample.question}' **")
#             logger.error(f"Traceback: {traceback.format_exc()}")
#             return RetrievalEvalResult.empty_result(sample)
#         return await self.eval_sample(sample, ranked_data=ranked_data, plan=plan)
#
#     async def eval_sample(
#         self, sample: RetrievalSample, **kwargs: Any
#     ) -> RetrievalEvalResult:
#         ranked_data: List[DocChunk] = kwargs["ranked_data"]
#         plan: FetcherPlan = kwargs["plan"]
#         # For each snippet in the sample, check if it is in the ranked data
#         retrieved_snippets = []
#         for snippet in sample.expected_retrieval:
#             for data in ranked_data:
#                 if not isinstance(data, DocChunk):
#                     continue
#                 # TODO(lucas): this is temporary, after s3 migration we will
#                 # only have source comparisons
#                 is_s3_index = True if data.original_url else False
#                 if is_s3_index:
#                     match_clause = snippet.original_url == data.original_url
#                 else:
#                     match_clause = snippet.original_url == data.key
#                 if match_clause and self.snippet_searcher.is_mentioned(
#                     snippet.snippet, data.content
#                 ):
#                     retrieved_snippets.append(snippet)
#                     break
#         return RetrievalEvalResult(
#             sample=sample,
#             retrieved_snippets=retrieved_snippets,
#             func_calls=plan.as_logging_dict()["functions_with_params"],
#         )
#
#     def gen_eval_report(
#         self,
#         eval_results: List[RetrievalEvalResult],
#         verbose: bool = False,
#     ) -> str:
#         eval_results = [e for e in eval_results if e is not None]
#         final_recall = round(
#             sum([e.recall for e in eval_results]) / len(eval_results), 3
#         )
#
#         lines = []
#
#         lines.append("===== Agent Config / Env Vars =====\n")
#         lines.append(str(self.config))
#         pinecone_vars = [
#             f"{k}={v}" for k, v in os.environ.items() if k.startswith("PINECONE")
#         ]
#         lines.append("\n".join(pinecone_vars))
#         lines.append("\n")
#
#         for i, res in enumerate(eval_results, 1):
#             lines.append(f"=========== Sample {i} ===========")
#             lines.append(f"【Question】:\n {res.sample.question}\n")
#             if verbose:
#                 lines.append("【Func Calls】:\n")
#                 for func_call in res.func_calls:
#                     lines.append(f" - Function: {func_call['func_name']}")
#                     lines.append(f"   Params  : {func_call['params']}")
#                 lines.append("\n")
#             lines.append("【Retrieved Snippets】:\n")
#             for snippet in res.retrieved_snippets:
#                 lines.append(f" - Original url : {snippet.original_url}")
#                 lines.append(f"   Snippet: {snippet.snippet}")
#             lines.append("\n")
#             lines.append("【Missed Snippets】:\n")
#             for snippet in res.missed_snippets:
#                 lines.append(f" - Original url : {snippet.original_url}")
#                 lines.append(f"   Snippet: {snippet.snippet}")
#             lines.append(f"\n【Recall】: {res.recall}\n")
#         lines.append("======= Final Score =======\n")
#         lines.append(f"Recall: {final_recall}\n")
#         lines.append("(All scores are out of 1.0)")
#         lines.append("======= LLM Cost =======\n")
#         lines.append(self.observer.get_llm_costs_str())
#         return "\n".join(lines)
#
#
# class PlanRetrievalEval(RetrievalEval):
#     """Eval on only the planning and retrieval part by checking recall."""
#
#     async def _run_inner(
#         self, sample: RetrievalSample, agent: PlanRetrievalAgent
#     ) -> Tuple[FetcherPlan, List[DocChunk], List[DocChunk]]:
#         plan: FetcherPlan = await agent.plan(
#             sample.question, memory=None, as_of_date=sample.as_of_date
#         )
#         fetched_data = await agent.fetch_data(plan)
#         ranked_data = agent.ranker.rank(fetched_data, self.config.answer_token_limit)
#         return plan, fetched_data, ranked_data
#
#
# class RetrievalOnlyEval(RetrievalEval):
#     """Eval ONLY the retrieval part by calling data fetcher functions directly."""
#
#     async def _run_inner(
#         self, sample: RetrievalSample, agent: PlanRetrievalAgent
#     ) -> Tuple[FetcherPlan, List[DocChunk], List[DocChunk]]:
#         plan: FetcherPlan = FetcherPlan.from_func_calls_dict(sample.func_calls)
#         fetched_data = await agent.fetch_data(plan)
#         ranked_data = agent.ranker.rank(fetched_data, self.config.answer_token_limit)
#         return plan, fetched_data, ranked_data
