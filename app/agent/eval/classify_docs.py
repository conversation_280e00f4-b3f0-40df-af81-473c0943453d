import json
import logging
import traceback
from typing import Any, Dict, List

from colorama import Fore, Style

from agent.agents.classify_docs import ClassifyDocsAgent
from agent.config.agent import ClassifyDocsAgentConfig
from agent.eval.base import Eval, EvalResult, Sample

logger = logging.getLogger(__name__)

# _ICON = {"yes": "✅", "no": "❌", "maybe": ""}
_ICON = {
    "yes": Fore.GREEN + "yes" + Style.RESET_ALL,
    "no": Fore.RED + "no " + Style.RESET_ALL,
    "maybe": Fore.YELLOW + "may" + Style.RESET_ALL,
}


# TODO: compare & score with following
"""
yes -> maybe
no -> maybe
yes -> no
no -> yes
maybe -> yes
maybe -> no


positive:
yes -> maybe
    -> no

negative:
no -> maybe
    -> yes

# configurable which is we expected
maybe -> yes
maybe -> no

"""


class ClassifyDocsEvalSample(Sample):
    question: str = ""
    ticker: str
    titles: list[str]
    expected_result: List[dict]


class ClassifyDocsEvalResult(EvalResult[ClassifyDocsEvalSample]):
    sample: ClassifyDocsEvalSample
    results: list = []

    class Config:
        arbitrary_types_allowed = True

    @classmethod
    def empty_result(cls, sample: ClassifyDocsEvalSample) -> "ClassifyDocsEvalResult":
        return cls(sample=sample)


class ClassifyDocsEval(Eval[ClassifyDocsEvalSample, ClassifyDocsEvalResult]):
    """Eval for Classify Docs Agent"""

    config: ClassifyDocsAgentConfig = ClassifyDocsAgentConfig(
        with_reason=True, light_mode=False
    )

    def load_samples(self, filename: str) -> List:
        """Load a list of samples from a jsonl file."""
        from agent.constants import EVAL_SAMPLE_DATA_DIR

        with open(EVAL_SAMPLE_DATA_DIR / f"{filename}.json", "r") as f:
            data = json.loads(f.read())
            samples = []
            for line in data["samples"]:
                samples.append(self.load_sample(line))
                break
        return samples

    def load_sample(self, sample_dict: Dict) -> ClassifyDocsEvalSample:
        return ClassifyDocsEvalSample(
            question=f'classify {sample_dict["ticker"]} docs: {sample_dict["titles"]}',
            as_of_date=sample_dict["as_of_date"],
            ticker=sample_dict["ticker"],
            titles=sample_dict["titles"],
            expected_result=sample_dict["expected_result"],
        )

    async def run_for_one_sample(
        self, sample: ClassifyDocsEvalSample
    ) -> ClassifyDocsEvalResult:
        try:
            agent = ClassifyDocsAgent(
                observer=self.observer,
                config=self.config,
                streaming=False,
            )
            answer = await agent.run(
                self.chat.user,
                ticker=sample.ticker,
                titles=sample.titles,
                # Override date to as of date to make eval samples not time sensitive
                as_of_date=sample.as_of_date,
            )
        except Exception:
            logger.error(f"** Error when answering : '{sample.question}' **")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return ClassifyDocsEvalResult.empty_result(sample)

        return await self.eval_sample(sample, answer=answer)

    async def eval_sample(
        self, sample: ClassifyDocsEvalSample, **kwargs: Any
    ) -> ClassifyDocsEvalResult:
        answer = json.loads(kwargs.get("answer", "{}"))
        results = answer["results"] if "results" in answer else answer

        eval_rslt = []
        i = 1
        for exp, rslt in zip(sample.expected_result, results):
            eval = exp["is_based_on_corporate_event"]
            if self.config.light_mode:
                rval, reason = rslt if self.config.with_reason else (rslt, "")
            else:
                assert exp["title"] == rslt["title"]
                rval = rslt["is_based_on_corporate_event"]
                reason = rslt["reason"] if self.config.with_reason else ""

            d = {
                "num": i,
                "title": exp["title"] if self.config.light_mode else rslt["title"],
                "is_based_on_corporate_event": rval,
                "reason": reason if self.config.with_reason else "",
                "score": 1 if eval == rval else 0,
            }
            eval_rslt.append(d)

        return ClassifyDocsEvalResult(sample=sample, results=eval_rslt)

    def gen_eval_report(
        self,
        results: List[ClassifyDocsEvalResult],
        verbose: bool = False,
    ) -> str:
        expected_total_score = 0
        total_score = 0
        lines = []

        lines.append("===== Agent Config / Env Vars =====\n")
        lines.append(str(self.config))
        lines.append("\n")

        for i, eval_rslt in enumerate(results, 1):
            sample_score = 0

            lines.append(f"=========== Sample {i} ===========")
            lines.append("【Scores】:")
            lines.append("match | exp | actual ")
            for idx, r in enumerate(eval_rslt.results):
                icon = "✅" if r["score"] == 1 else "❌"  # "✴️"
                exp = eval_rslt.sample.expected_result[idx][
                    "is_based_on_corporate_event"
                ]
                compare = (
                    f"{Fore.LIGHTBLACK_EX}{exp:3} | {r['is_based_on_corporate_event']:3}"
                    if r["score"] == 1
                    else f"{_ICON[exp]} | {_ICON[r['is_based_on_corporate_event']]}"
                )
                line = f"  {icon}  | {compare} - {idx+1}.{eval_rslt.sample.titles[idx]}"
                if r["reason"]:
                    line += f" ({r['reason']})"
                line += f"{Style.RESET_ALL}"
                lines.append(line)

                sample_score += r["score"]

            total_score += sample_score
            expected_total_score += len(eval_rslt.results)

            lines.append(f" Total {sample_score}/{len(eval_rslt.results)}.\n ")

        lines.append("======= Final Score =======\n")
        lines.append(f"Total Scores: {total_score}/{expected_total_score}\n")
        lines.append("======= LLM Cost =======\n")
        lines.append(self.observer.get_llm_costs_str())

        return "\n".join(lines)

    def _log_result(self, results: List[ClassifyDocsEvalResult]) -> None:
        try:
            from agent.constants import EVAL_SAMPLE_DATA_DIR
            from datetime import datetime

            filename = results[0].sample.ticker

            dt = datetime.now().strftime("%Y-%m-%d_%H.%M.%S")

            with open(
                EVAL_SAMPLE_DATA_DIR / f"classify_docs/{filename}_{dt}.log", "w"
            ) as f:
                sb = []
                for eval_rslt in results:
                    for score, r in eval_rslt.results:
                        r = r[0] if self.config.with_reason else r
                        sb.append(r)
                f.write("\n".join(sb))
                f.write("\n")
        except Exception as e:
            logger.error(e)
