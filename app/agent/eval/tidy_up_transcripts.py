"""
This module is for evaluating the TidyUpTranscriptAgent.
"""

import json
import difflib
from typing import Any, Dict, List

from agent.agents.tidy_up_doc import TidyUpTranscriptAgent
from agent.config.agent import TidyUpTranscriptAgentConfig
from agent.eval.base import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>
from agent.utils import timer


class TidyUpTranscriptSample(Sample):
    """Sample for tidy up transcript eval."""

    question: str = ""
    input_paragraphs: list = []
    expected_output: list = []


def is_x_percent_similar(str1: str, str2: str, x: int = 90) -> bool:
    matcher = difflib.SequenceMatcher(None, str1, str2)
    similarity_ratio = matcher.ratio()
    return similarity_ratio >= x / 100


class TidyUpTranscriptResult(EvalResult[TidyUpTranscriptSample]):
    """Result for tidy up transcript eval."""

    sample: TidyUpTranscriptSample
    actual_output: list = []

    @classmethod
    def empty_result(cls, sample: TidyUpTranscriptSample) -> "TidyUpTranscriptResult":
        return cls(sample=sample)

    @property
    def is_exactly_same(self) -> bool:
        return self.actual_output == self.sample.expected_output

    @property
    def is_mostly_same(self) -> bool:
        """all the speakers match and all the content match 99%"""
        if len(self.actual_output) != len(self.sample.expected_output):
            return False
        all_speakers_match = [entry["speaker"] for entry in self.actual_output] == [
            entry["speaker"] for entry in self.sample.expected_output
        ]
        all_content_match = all(
            [
                is_x_percent_similar(actual["content"], expected["content"])
                for actual, expected in zip(
                    self.actual_output, self.sample.expected_output
                )
            ]
        )
        return all_speakers_match and all_content_match


class TidyUpTranscriptEval(Eval[TidyUpTranscriptSample, TidyUpTranscriptResult]):
    """Eval for tidy up transcript Agent (currently only fix broken sentences part)."""

    repeat_count: int = 1
    config: TidyUpTranscriptAgentConfig = TidyUpTranscriptAgentConfig(
        fix_broken_sentences_model_name="gpt-4o-mini",
    )

    def load_sample(self, sample_dict: Dict) -> TidyUpTranscriptSample:
        return TidyUpTranscriptSample(
            input_paragraphs=sample_dict["input_paragraphs"],
            expected_output=sample_dict["expected_output"],
        )

    @timer(key="run")
    async def run_for_one_sample(
        self, sample: TidyUpTranscriptSample
    ) -> TidyUpTranscriptResult:
        try:
            agent = TidyUpTranscriptAgent(observer=self.observer)
            actual_output = await agent.fix_broken_sentences(
                {"paragraphs": sample.input_paragraphs}
            )
        except Exception:
            return TidyUpTranscriptResult.empty_result(sample)
        return await self.eval_sample(sample, actual_output=actual_output["paragraphs"])

    async def eval_sample(
        self, sample: TidyUpTranscriptSample, **kwargs: Any
    ) -> TidyUpTranscriptResult:
        return TidyUpTranscriptResult(
            sample=sample, actual_output=kwargs["actual_output"]
        )

    def gen_eval_report(
        self,
        eval_results: List[TidyUpTranscriptResult],
        verbose: bool = False,
    ) -> str:
        lines = []  # type: ignore
        lines.append("===== Agent Config / Env Vars =====\n")
        lines.append(str(self.config))
        lines.append("\n")
        lines.append("======= Results (only bad ones) =======\n")
        for i, res in enumerate(eval_results, 1):
            if not res.is_mostly_same:
                lines.append(f"=========== Sample {i} ===========")
                lines.append(
                    f"【Expected Output】:\n {json.dumps(res.sample.expected_output, indent=2)}\n"
                )
                lines.append(
                    f"【Actual Output】:\n {json.dumps(res.actual_output, indent=2)}\n"
                )
                lines.append(f"【Is Exactly Same】:\n {res.is_exactly_same}\n")
                lines.append(f"【Is Mostly Same】:\n {res.is_mostly_same}\n")
        lines.append("======= Final Results =======\n")
        lines.append(
            f"Exactly Same/Total: {sum(res.is_exactly_same for res in eval_results)}/{len(eval_results)}\n"
        )
        lines.append(
            f"Mostly Same/Total: {sum(res.is_mostly_same for res in eval_results)}/{len(eval_results)}\n"
        )
        return "\n".join(lines)
