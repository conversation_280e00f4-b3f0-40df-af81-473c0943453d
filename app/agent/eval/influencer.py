import json
import logging
from typing import Any, Dict, List

from agent.agents.influencer import Influencer<PERSON><PERSON><PERSON><PERSON>, InfluencerSchemaInferrerAgent
from agent.config.agent import InfluencerFinderAgentConfig
from aiflow.observer import AIFlowObserver
from agent.eval.base import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sample

logger = logging.getLogger(__name__)


class InfluencerSample(Sample):
    task: str
    platform: str


class InfluencerResult(EvalResult[InfluencerSample]):
    sample: InfluencerSample
    config: InfluencerFinderAgentConfig
    search_queries_in_order: List[str] = []
    search_results: Dict[str, Any] = {}
    all_retrieved_with_eval: List[Dict[str, Any]] = []

    def qualification_rate_top_k(self, k: int = 10) -> float:
        """Calculate the rate of qualified influencers among top k retrieved ones"""
        if len(self.all_retrieved_with_eval) == 0:
            return 0.0
        qualified_count = sum(
            1 for item in self.all_retrieved_with_eval[:k] if item["qualified"]
        )
        return round(qualified_count / k, 3)

    def relevance(self, evaluation: Dict) -> str:
        """Calculate the relevance of the retrieved influencers to the criteria"""
        match_count = 0
        no_match_count = 0
        for criterion in evaluation.values():
            if criterion["match"] == "yes":
                match_count += 1
            elif criterion["match"] in ["no", "unknown"]:
                no_match_count += 1
        rate = match_count / (match_count + no_match_count)
        if rate >= 0.99:
            return "high"
        elif rate >= 0.49999999:
            return "half"
        else:
            return "low"

    def relevance_count_top_k(self, relevance: str, k: int = 10) -> float:
        """Calculate the rate of high relevance influencers among top k retrieved ones"""
        if len(self.all_retrieved_with_eval) == 0:
            return 0.0
        relevance_count = sum(
            1
            for item in self.all_retrieved_with_eval[:k]
            if item["qualified"] and self.relevance(item["evaluation"]) == relevance
        )
        return relevance_count

    def total_qualified_count(self) -> int:
        return sum(1 for item in self.all_retrieved_with_eval if item["qualified"])

    @classmethod
    def empty_result(
        cls, sample: InfluencerSample, config: InfluencerFinderAgentConfig
    ) -> "InfluencerResult":
        return cls(sample=sample, config=config)


class InfluencerFinderEval(Eval[InfluencerSample, InfluencerResult]):
    """Eval for influencer finder Agent"""

    observer: AIFlowObserver

    def load_sample(self, sample_dict: Dict) -> InfluencerSample:
        return InfluencerSample(
            task=sample_dict["task"],
            platform=sample_dict["platform"],
        )

    async def run_for_one_sample(self, sample: InfluencerSample) -> InfluencerResult:
        try:
            config = InfluencerFinderAgentConfig(
                platform=sample.platform,
                entities_limit=100,
            )

            observer = AIFlowObserver()
            schema_inferer = InfluencerSchemaInferrerAgent(
                observer=observer,
                config=config,
            )
            schema, task_short_name, target_entity = await schema_inferer.run(
                task=sample.task
            )

            # Then run the actual agent
            agent = InfluencerFinderAgent(
                config=config,
                task=sample.task,
                observer=observer,
                task_schema=schema,
                target_entity_definition=target_entity,
                streaming=False,
            )

            await agent.run(username="eval_user", platform=sample.platform)

            # Get results from observer states
            search_queries_in_order = observer.states["search_queries_in_order"]
            search_results = observer.states["search_results"]

            return await self.eval_sample(
                sample,
                config=config,
                search_queries_in_order=search_queries_in_order,
                search_results=search_results,
                all_retrieved_with_eval=agent.retrieval_eval_states.all_results(),
            )

        except Exception as e:
            logger.error(
                f"Error when running influencer finder for task: '{sample.task}'"
            )
            logger.error(f"Error: {e}", exc_info=True)
            return InfluencerResult.empty_result(sample, config=config)

    async def eval_sample(
        self,
        sample: InfluencerSample,
        config: InfluencerFinderAgentConfig,
        **kwargs: Any,
    ) -> InfluencerResult:
        return InfluencerResult(
            sample=sample,
            config=config,
            search_queries_in_order=kwargs["search_queries_in_order"],
            search_results=kwargs["search_results"],
            all_retrieved_with_eval=kwargs["all_retrieved_with_eval"],
        )

    def gen_eval_report(
        self,
        eval_results: List[InfluencerResult],
        verbose: bool = False,
    ) -> str:
        eval_results = [e for e in eval_results if e is not None]

        def avg_qualification_rate(k: int) -> float:
            return round(
                sum(e.qualification_rate_top_k(k=k) for e in eval_results)
                / len(eval_results),
                3,
            )

        def avg_relevance_rate(relevance: str, k: int) -> float:
            return round(
                sum(
                    round(
                        e.relevance_count_top_k(relevance, k=k) / k,
                        3,
                    )
                    for e in eval_results
                )
                / len(eval_results),
                3,
            )

        lines = []
        lines.append("===== Overall Metrics =====\n")
        for k in [20, 100, 500]:
            lines.append(
                f"Top {k} Average Qualification Rate: {avg_qualification_rate(k)}"
            )
            lines.append(
                f"Top {k} Average High Relevance Rate: {avg_relevance_rate('high', k)}"
            )

        lines.append(f"\nTotal Samples: {len(eval_results)}\n\n")

        for i, res in enumerate(eval_results, 1):
            lines.append(f"=========== Sample {i} ===========")
            lines.append(f"【Task】: {res.sample.task}")
            lines.append(f"【Config】: {res.config}")
            lines.append(f"【Platform】: {res.sample.platform}")
            q = res.total_qualified_count()
            t = len(res.all_retrieved_with_eval)
            lines.append(
                f"【Qualified / Total Retrieved】: {q} / {t} ({round(q / t if t > 0 else 0, 3)})"
            )

            for k in [20, 100, 500]:
                lines.append(f"【Top {k} Results】:")
                high = res.relevance_count_top_k("high", k)
                lines.append(f"  High Relevance: {high} / {k} ({round(high / k, 3)})")
                half = res.relevance_count_top_k("half", k)
                lines.append(f"  Half Relevance: {half} / {k} ({round(half / k, 3)})")

            if verbose:
                lines.append("【Search Queries】:")
                lines.append(f"{res.search_queries_in_order}")
                lines.append("【Search Results】:")
                lines.append(f"  {res.search_results}")
                lines.append("【All Retrieved】:")
                lines.append(json.dumps(res.all_retrieved_with_eval, indent=2))
            lines.append("\n")

        # TBD
        lines.append("\n======= LLM Cost =======")

        return "\n".join(lines)
