import tempfile
import urllib.parse
from logging import getLogger
from pathlib import Path

from django.http import HttpRequest
from dotenv import load_dotenv
from ninja import Query
from ninja.router import Router

from biz import datasdk
from biz.enums import DocTypeEnum
from datasvc.indexing.indexer.customer.importer import (
    CustomerDocFileImporter,
    CustomerDocFileImporterConfig,
)
from .schema import GetDocFilterSchema
from ..response import ApiResponse, ApiResults

load_dotenv()

customer_data_router = Router(tags=["customer data"])

logger = getLogger(__name__)


@customer_data_router.get(
    "/docs/upload-url/", summary="generate upload data url for HTTP PUT."
)
async def gen_doc_upload_url_put(request: HttpRequest) -> ApiResponse:
    # filename = request.headers.get("X-File-Name")
    # content_type = request.headers.get("X-File-MIME-Type")
    # if not filename:
    #     return ApiResults.BAD_REQUEST(
    #         "no filename provided.", detail="need to set header X-File-Name"
    #     )
    # filename = urllib.parse.unquote(filename)
    # args = [{"key": filename, "content_type": content_type}]
    # cfg = CustomerDocFEImporterConfig(user_id=request.user.id, kwargs_list=args)
    # importer = CustomerDocFEImporter(config=cfg)
    # results, errs = await importer.async_import_all()
    #
    # return ApiResults.SUCCESS(
    #     data={
    #         "results": results,
    #         "errors": [str(e) for e in errs],
    #     }
    # )

    url = request.get_full_path()
    parts = url.split("/")
    url = "/".join(parts[:-2] + ["upload/"])
    return ApiResults.SUCCESS(data=url)


@customer_data_router.put(
    "/docs/upload/", summary="directly upload a single customer doc by PUT."
)
async def upload_doc(request: HttpRequest) -> ApiResponse:
    filename = request.headers.get("X-File-Name")
    # content_type = request.headers.get("X-File-MIME-Type")
    if not filename:
        return ApiResults.BAD_REQUEST(
            "no filename provided.", detail="need to set header X-File-Name"
        )
    filename = urllib.parse.unquote(filename)

    with tempfile.TemporaryDirectory() as tmp_folder:
        p = Path(tmp_folder) / filename
        with p.open(mode="wb") as fp:
            fp.write(request.read())

        cfg = CustomerDocFileImporterConfig(
            user_id=request.user.id,
            source_path=str(p),
            # target_path="_uploaded",
            detect_content_type=True,
            # TODO: make overwrite as an API param
            overwrite=True,
        )
        importer = CustomerDocFileImporter(config=cfg)
        key_to_results: dict = await importer.async_import_all()

    return ApiResults.SUCCESS(data=key_to_results)


@customer_data_router.post("/docs/import/fs/", summary="handle uploaded file")
async def import_doc_from_fs(request: HttpRequest) -> ApiResponse:
    cfg = CustomerDocFileImporterConfig(
        user_id=request.user.id,
        source_path=request.body,
        recursive=True,
        detect_content_type=True,
        # TODO: make overwrite as an API param
        overwrite=True,
    )
    importer = CustomerDocFileImporter(config=cfg)
    key_to_results: dict = await importer.async_import_all()
    return ApiResults.SUCCESS(data=key_to_results)


@customer_data_router.get("/docs/", summary="list customer doc")
async def list_customer_doc(request: HttpRequest) -> ApiResponse:
    docs = await datasdk.alist_customer_doc_meta_dicts(user_id=request.user.id)
    docs = sorted(docs, key=lambda d: d["pub_date"], reverse=True)

    return ApiResults.SUCCESS(data=docs)


@customer_data_router.delete("/docs/", summary="delete customer doc")
async def del_customer_doc(
    request: HttpRequest, doc_filter: Query[GetDocFilterSchema]
) -> ApiResponse:
    key = doc_filter.key
    meta = datasdk.doc_key_to_meta(key)
    if meta.user_id != request.user.id:
        return ApiResults.NOT_FOUND
    if meta.doc_type != DocTypeEnum.CUSTOMER:
        return ApiResults.BAD_REQUEST("Invalid doc type to delete.")

    try:
        docs = await datasdk.adelete_doc_by_key(
            key=key, is_customer_doc=True, i_am_pretty_sure=True
        )
    except datasdk.DocNotFoundError as e:
        logger.debug(e)
        return ApiResults.NOT_FOUND

    return ApiResults.SUCCESS(data=docs)
