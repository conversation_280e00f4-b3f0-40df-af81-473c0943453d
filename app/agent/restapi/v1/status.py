import asyncio
import json
import subprocess
from logging import getLogger

import pinecone
from django.http import HttpRequest, HttpResponse
from ninja import Router

from common.django_ext.model import User
from common.services import slack
from datasvc.indexing.pinecone import PineconeIndexConfig
from ..response import Api<PERSON><PERSON><PERSON><PERSON>, ApiResponse
from ...llms import LLM

status_router = Router(tags=["status"])
logger = getLogger(__name__)


@status_router.api_operation(
    ["HEAD", "GET"], "/healthz/", auth=None, summary="API health status"
)
async def health(request: HttpRequest, response: HttpResponse) -> ApiResponse:
    status: dict = {}
    lock = asyncio.Lock()
    if request.method == "HEAD":
        tasks = [_check_system(status, response, lock)]
    else:
        tasks = [
            _check_database(status, response, lock),
            _check_llm(status, response, lock),
            # _check_vectordb(status, response, lock),
            _check_search_available(status, response, lock),
            _check_system(status, response, lock),
        ]
    results = await asyncio.gather(*tasks)
    succeed = all(results)

    status["overall"] = succeed
    if not succeed:
        slack.send_alert(
            "*** Healthy Alert *** ", f"{json.dumps(status,indent=2)}", force=True
        )

    return ApiResults.SUCCESS(data=status)


async def _check_database(
    status: dict, response: HttpResponse, lock: asyncio.Lock
) -> bool:
    try:
        User.objects.afirst()
        succeed = True
    except Exception as e:
        succeed = False
        logger.error(e)

    async with lock:
        status["database"], response["X-STATUS-DATABASE"] = (
            (True, "OK") if succeed else (False, "FAIL")
        )

    logger.debug(f"database checked. {succeed}")
    return succeed


async def _check_llm(status: dict, response: HttpResponse, lock: asyncio.Lock) -> bool:
    succeed = True
    llm_status = {}

    try:
        for kls in LLM.__subclasses__():
            llm_status[kls.__name__] = kls.is_online()
            succeed = succeed and llm_status[kls.__name__]
    except Exception as e:
        succeed = False
        logger.error(e)

    async with lock:
        status["llm"], response["X-STATUS-LLM"] = (
            (llm_status, "OK") if succeed else (False, "FAIL")
        )

    logger.debug(f"llm checked. {llm_status}")
    return succeed


async def _check_vectordb(
    status: dict, response: HttpResponse, lock: asyncio.Lock
) -> bool:
    succeed = False

    try:
        index_config = PineconeIndexConfig()
        pinecone.init(api_key=index_config.api_key, environment=index_config.env)
        desc = pinecone.describe_index(index_config.name)
        succeed = desc.status["ready"]
    except Exception as e:
        logger.error(e)

    async with lock:
        status["vectordb"], response["X-STATUS-VECTORDB"] = (
            (True, "OK") if succeed else (False, "FAIL")
        )

    logger.debug(f"vector db checked. {succeed}")
    return succeed


async def _check_search_available(
    status: dict, response: HttpResponse, lock: asyncio.Lock
) -> bool:
    from agent.tests.integration_tests.test_search import (
        check_search_available,
    )

    succeed = False

    try:
        await check_search_available(
            ticker="NVDA",
            company_name="nvidia",
            doc_type="earning_call",
            min_dates_days=100,
        )
        succeed = True
    except Exception as e:
        logger.error(e)

    async with lock:
        status["search"], response["X-STATUS-SEARCH"] = (
            (True, "OK") if succeed else (False, "FAIL")
        )

    logger.debug(f"search checked. {succeed}")
    return succeed


async def _check_system(
    status: dict, response: HttpResponse, lock: asyncio.Lock
) -> bool:
    succeed = True
    sys_stats = {}
    cmd = "df /|awk 'NR>1{print $5}'"
    try:
        proc = subprocess.Popen(
            [cmd], stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True
        )
        o, err = proc.communicate()
        if proc.returncode == 0:
            sys_stats["disk"] = du = o.decode().strip()
            du = du[:-1]
            if int(du) < 85:
                succeed = True
        else:
            raise Exception(err)
    except Exception as e:
        succeed = False
        logger.error(e)

    async with lock:
        status["system"], response["X-STATUS-SYSTEM"] = (
            (True, "OK") if succeed else (False, "FAIL")
        )
        status["system_states"] = sys_stats
    logger.debug(f"system checked. {sys_stats}")
    return succeed
