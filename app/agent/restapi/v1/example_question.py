from django.http import HttpRequest
from ninja.router import Router

from ..response import ApiResults, ApiResponse
from ...models import ExampleQuestion

example_question_router = Router(tags=["example_question"])


@example_question_router.get("/", summary="to list example questions", auth=None)
async def example_question_list(request: HttpRequest) -> ApiResponse:
    qs = ExampleQuestion.objects.all().order_by("order")
    lists = [item.to_dict() async for item in qs]
    r = ApiResults.SUCCESS(data=lists)
    return r
