from django.http import HttpRequest
from dotenv import load_dotenv
from ninja import Query
from ninja.router import Router

from datum.wrapper import DatumManager
from .schema import (
    DatumCreateSchema,
    DatumUpdateSchema,
    DatumQuerySchema,
)
from ..response import ApiResponse, ApiResults, PaginationSchema

load_dotenv()

datum_router = Router(tags=["datum"])


@datum_router.post("/", summary="Create a new datum")
async def create_datum(request: HttpRequest, body: DatumCreateSchema) -> ApiResponse:
    manager = DatumManager(owner=request.user.organization)
    wrapper = manager.create(key=body.key, value=body.value, meta_dict=body.meta_dict)
    await wrapper.asave(include_store=True)
    return ApiResults.SUCCESS(data=wrapper.to_dict())


@datum_router.get("/{key}", summary="Get a datum by key")
async def get_datum(request: HttpRequest, key: str) -> ApiResponse:
    manager = DatumManager(owner=request.user.organization)
    wrapper = await manager.aget(key=key, owner=request.user.organization.key)
    if wrapper is None:
        return ApiResults.NOT_FOUND
    return ApiResults.SUCCESS(data=wrapper.to_dict())


@datum_router.put("/{key}", summary="Update a datum")
async def update_datum(
    request: HttpRequest, key: str, body: DatumUpdateSchema
) -> ApiResponse:
    manager = DatumManager(owner=request.user.organization)
    wrapper = await manager.async_get(key=key, owner=request.user.organization.key)
    if wrapper is None:
        return ApiResults.NOT_FOUND

    if body.value is not None:
        wrapper.set_data(body.value)
    if body.meta_dict:
        for meta_key, meta_value in body.meta_dict.items():
            wrapper[meta_key] = meta_value

    await wrapper.asave(include_store=True)
    return ApiResults.SUCCESS(data=wrapper.to_dict())


@datum_router.delete("/{key}", summary="Delete a datum")
async def delete_datum(request: HttpRequest, key: str) -> ApiResponse:
    manager = DatumManager(owner=request.user.organization)
    full_key = f"{request.user.organization.key}:{key}"
    deleted_count = await manager.async_delete_by_full_key(
        full_key=full_key, include_store=True
    )
    if deleted_count == 0:
        return ApiResults.NOT_FOUND
    return ApiResults.SUCCESS()


@datum_router.get("/", summary="Query datums")
async def query_datums(
    request: HttpRequest,
    query: Query[DatumQuerySchema],
    paging: Query[PaginationSchema],
) -> ApiResponse:
    manager = DatumManager(owner=request.user.organization)
    wrappers = await manager.async_query(
        owner=request.user.organization.key,
        without_metas=query.without_metas,
        order_by=query.order_by,
        page_size=paging.limit,
        page=paging.offset // paging.limit,
        **query.filters,
    )
    data = [wrapper.to_dict() for wrapper in wrappers]
    response = ApiResults.SUCCESS(data=data)
    return await response.with_pagination(paging)
