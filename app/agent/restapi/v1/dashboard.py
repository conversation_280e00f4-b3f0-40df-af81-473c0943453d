import logging
from datetime import timedelta, datetime
from typing import Any, List, Optional
from typing import Dict
from typing import <PERSON><PERSON>
from datetime import date

from asgiref.sync import sync_to_async
from dateutil import parser
from django.db import connection
from django.db.models import Count, DateField
from django.db.models.functions import Trunc
from django.db.models.functions import TruncDay
from django.http import HttpRequest
from ninja import Query
from ninja.router import Router

from .group_auth import GroupAuth
from .schema import (
    MessageFilterSchema,
)
from ..response import ApiResults, ApiResponse
from ...models import (
    ChatTurn,
    ChatTurnStates,
    ChatMsg,
    FetchedSource,
    Citation,
    Chart,
    History,
    MessageRoleEnum,
)

logger = logging.getLogger(__name__)


dashboard_router = Router(tags=["dashboard"], auth=GroupAuth("see_dashboard"))


def get_date_range(start: str | None, end: str | None) -> Tuple[datetime, datetime]:
    if start and end:
        start_datetime = parser.parse(start)
        end_datetime = parser.parse(end)
    else:
        now = datetime.now()
        start_datetime = now - timedelta(days=30)
        end_datetime = now
    return start_datetime, end_datetime


def chat_turns_per_day_counts() -> List[dict]:
    result = (
        ChatTurnStates.objects.annotate(
            date=Trunc("created_at", "day", output_field=DateField())
        )
        .values("date")
        .annotate(count=Count("id"))
        .order_by("date")
    )
    result_list: List[dict] = list(result)
    return result_list


def chat_turns_count_group_by_source(
    start: Optional[str], end: Optional[str]
) -> List[dict]:
    start_date, end_date = get_date_range(start, end)
    result = (
        ChatTurnStates.objects.filter(
            chat_turn__created_at__range=(start_date, end_date)
        )
        .values("chat_turn__message_source")
        .annotate(count=Count("chat_turn__id"))
        .order_by("chat_turn__message_source")
    )
    result_list: List[dict] = list(result)
    return result_list


@dashboard_router.get("/chat_turns_per_day_counts", summary="Dashboard")
async def get_chat_turns_per_day_counts(request: HttpRequest) -> ApiResponse:
    per_day = await sync_to_async(chat_turns_per_day_counts)()
    return ApiResults.SUCCESS(per_day)


@dashboard_router.get(
    "/chat_turn_counts_grouped_by_message_source", summary="Dashboard"
)
async def get_chat_turns_count_group_by_source(request: HttpRequest) -> ApiResponse:
    start = request.GET.get("start")
    end = request.GET.get("end")
    source = await sync_to_async(chat_turns_count_group_by_source)(start, end)
    return ApiResults.SUCCESS(source)


@dashboard_router.get("/chat_turns/{date}", summary="Get ChatTurns by date")
async def get_chat_turns_by_date(request: HttpRequest, date: str) -> ApiResponse:
    start_datetime = datetime.strptime(date, "%Y-%m-%d")
    end_datetime = start_datetime + timedelta(days=1, milliseconds=-1)
    chat_turns = await sync_to_async(
        lambda: list(
            ChatTurnStates.objects.filter(
                chat_turn__created_at__range=(start_datetime, end_datetime)
            )
            .select_related("chat_turn")
            .values(
                "id",
                "chat_turn__id",
                "chat_turn__chat__id",
                "chat_turn__user_msg",
                "chat_turn__message_source",
            )
        )
    )()
    return ApiResults.SUCCESS(chat_turns)


@dashboard_router.get(
    "/queries_per_user", summary="Number of queries per user in a specific time range"
)
def queries_per_user(request: HttpRequest) -> Any:
    start = request.GET.get("start")
    end = request.GET.get("end")
    start_date, end_date = get_date_range(start, end)

    queries_per_user = (
        ChatTurn.objects.filter(created_at__range=(start_date, end_date))
        .values("chat__user_id")
        .annotate(query_count=Count("id"))
        .order_by("-query_count")
    )

    total_queries = sum(item["query_count"] for item in queries_per_user)
    average_queries = total_queries / len(queries_per_user) if queries_per_user else 0

    result = {
        "queries_per_user": list(queries_per_user),
        "average_queries": average_queries,
    }

    return ApiResults.SUCCESS(result)


def calculate_retention() -> Any:
    now = datetime.now()
    start_date = now - timedelta(weeks=9)
    date_range = [(start_date + timedelta(weeks=i)).date() for i in range(10)]
    with connection.cursor() as cursor:
        # pre-fetch all user activity data
        cursor.execute(
            """
            SELECT user_id, created_at
            FROM (
                SELECT ac.user_id, act.created_at
                FROM agent_chatturn act
                JOIN agent_chat ac ON act.chat_id = ac.id
                WHERE act.created_at >= %s
                UNION
                SELECT user_id, updated_at AS created_at
                FROM agent_history
                WHERE updated_at >= %s
            ) AS user_activity
            """,
            [start_date, start_date],
        )
        user_activities = cursor.fetchall()

    # group user activity data by week
    activity_dict: Dict[date, set[int]] = {}
    retention_list = []
    for user_id, created_at in user_activities:
        week_start = created_at - timedelta(days=created_at.weekday())
        week_start = week_start.date()
        if week_start not in activity_dict:
            activity_dict[week_start] = set()
        activity_dict[week_start].add(user_id)

    for cohort_start in date_range:
        # convert cohort_start to the first day of the week
        cohort_start = cohort_start - timedelta(days=cohort_start.weekday())
        cohort_end = cohort_start + timedelta(days=6)
        cohort_users = activity_dict.get(cohort_start, set())
        cohort_size = len(cohort_users)

        weekly_active_users = []
        for week_offset in range(10):
            week_start = cohort_start + timedelta(weeks=week_offset)
            active_users = activity_dict.get(week_start, set())
            active_users_count = len(cohort_users & active_users)
            weekly_active_users.append(active_users_count)

        retention_list.append(
            {
                "cohort_start": cohort_start,
                "cohort_end": cohort_end,
                "size": cohort_size,
                "weekly_active_users": weekly_active_users,
            }
        )

    return retention_list


@dashboard_router.get("/retention", summary="Calculate user retention")
async def get_retention(request: HttpRequest) -> ApiResponse:
    retention = await sync_to_async(calculate_retention)()
    return ApiResults.SUCCESS(retention)


def get_dau() -> List[dict]:
    # Calculate DAU for both ChatTurn and History, then merge the results
    chat_turn_dau = (
        ChatTurn.objects.annotate(day=TruncDay("created_at"))
        .values("day")
        .annotate(
            dau=Count("chat__user_id", distinct=True)
        )  # Access user_id from Chat using chat__user_id
    )

    history_dau = (
        History.objects.annotate(day=TruncDay("updated_at"))
        .values("day")
        .annotate(
            dau=Count("user_id", distinct=True)
        )  # Directly use user_id in History model
    )

    results: Dict = {}
    for entry in list(chat_turn_dau) + list(history_dau):
        day = entry["day"]
        if day in results:
            results[day] += entry["dau"]
        else:
            results[day] = entry["dau"]

    # Convert results to the desired format
    final_results = [{"label": day, "value": dau} for day, dau in results.items()]
    return final_results


def get_new_users() -> List[dict]:
    # Assuming User is the user model with a field named date_joined indicating the registration date
    from common.django_ext.model import User

    # Get the registration date of each user, group by date, and calculate the number of new users per day
    new_users_per_day = (
        User.objects.annotate(day=TruncDay("date_joined"))
        .values("day")
        .annotate(new_users_count=Count("id"))
    )
    results = [
        {"label": result["day"], "value": result["new_users_count"]}
        for result in new_users_per_day
    ]
    return results


@dashboard_router.get("/dau", summary="Get DAU")
async def get_dau_route(request: HttpRequest) -> ApiResponse:
    dau_results = await sync_to_async(get_dau)()
    return ApiResults.SUCCESS(dau_results)


@dashboard_router.get("/new_users", summary="Get new users")
async def get_new_users_route(request: HttpRequest) -> ApiResponse:
    new_users_results = await sync_to_async(get_new_users)()
    return ApiResults.SUCCESS(new_users_results)


@dashboard_router.get(
    "/{chat_id}/all_messages/",
    tags=["chat"],
    summary="to list all kinds of messages (message, citation) of a chat",
    description=(
        "Note this API is a *shortcut*. "
        "It returns a list combined by objects in different formats."
    ),
)
async def chat_all_message_list(
    request: HttpRequest,
    chat_id: int,
    filters: Query[MessageFilterSchema],
) -> ApiResponse:
    # messages
    qs = filters.filter(ChatMsg.objects.order_by("id").filter(chat_id=chat_id))
    messages = [msg.to_dict() async for msg in qs]

    # sources
    qs = filters.filter(
        FetchedSource.objects.order_by("number")
        .select_related("chat_turn", "chat_turn__chat")
        .filter(chat_turn__chat_id=chat_id)
    )
    sources = [s.to_dict() async for s in qs]

    # citations
    qs = (
        Citation.objects.order_by("number")
        .select_related("chat_turn", "chat_turn__chat")
        .filter(chat_turn__chat_id=chat_id)
    )
    citations = [c.to_dict() async for c in qs]

    # charts
    qs = (
        Chart.objects.select_related("chat_turn", "chat_turn__chat")
        .filter(chat_turn__chat_id=chat_id)
        .all()
    )

    charts = [c.to_dict() async for c in qs]

    all_msgs = sorted(
        messages + sources + citations + charts,
        key=lambda x: (
            x["chat_turn_id"],
            MessageRoleEnum.values().index(x["role"]),
            x["id"],
        ),
    )

    r = ApiResults.SUCCESS(data=all_msgs)
    return r
