import json
from logging import getLogger
from typing import Any

from asgiref.sync import sync_to_async
from django.http import HttpRequest
from dotenv import load_dotenv
from ninja.router import Router

from biz import datasdk
from jobs import jobber
from ..auth import BearAuth, HeaderAuth
from ..response import ApiResponse, ApiResults

load_dotenv()

# NOTE: misc router is directly added to /api/v1/
misc_router = Router(tags=["misc"])

logger = getLogger(__name__)


@jobber.task
def debug_task(*args: Any) -> None:
    print(args)


@misc_router.get("/test/", auth=None, summary="test")
async def test_api(request: HttpRequest) -> ApiResponse:
    logger.warning("-----       >>>>>     test      <<<<<       --------")

    debug_task.delay("Hello")

    return ApiResults.SUCCESS


@misc_router.get("/indicator/{ticker}/", summary="to retrieve moving indicators")
async def get_moving_indicator(request: HttpRequest, ticker: str) -> ApiResponse:
    content = datasdk.doc_store.get_data(f"indicators/{ticker}/content")

    if not content:
        return ApiResults.NOT_FOUND

    # TODO: undo this hack. Make +1 in indicators agent side
    data = json.loads(content)
    for indicator in data:
        for quote in indicator["quotes"]:
            quote["number"] += 1

    return ApiResults.SUCCESS(data=data)


@misc_router.get("/settings/", auth=[BearAuth(), HeaderAuth()], summary="get settings")
async def get_settings(request: HttpRequest) -> ApiResponse:
    user_groups = await sync_to_async(lambda: list(request.user.groups.values_list("name", flat=True)))()  # type: ignore
    return ApiResults.SUCCESS(
        data={
            "DEBUG": "fe_debug_mode" in user_groups,
            "CAN_UPLOAD_PRIVATE_FILES": "can_upload_private_files" in user_groups,
        }
    )
