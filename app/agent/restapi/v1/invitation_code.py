from datetime import datetime
from django.http.request import HttpRequest
from ninja.router import Router
from agent.restapi.response import A<PERSON><PERSON><PERSON>ponse, ApiResults
from agent.restapi.v1.schema import BindInvitationCodeSchema
from agent.models import InvitationCode, Profile

invitation_code_router = Router(tags=["invitation_code"])


@invitation_code_router.post("/bind/", summary="to bind invitation code")
async def bind_invitation_code(
    request: HttpRequest, body: BindInvitationCodeSchema
) -> ApiResponse:
    try:
        invitation_code = await InvitationCode.objects.aget(code=body.code)

        # Check if the code has reached its maximum usage
        if len(invitation_code.users) >= invitation_code.max_count:
            await Profile.objects.filter(user=request.user).aupdate(is_in_waitlist=True)
            return ApiResults.FAIL(
                detail="This invitation code has reached its maximum usage"
            )

        # Check if current user's id is already in the users dict
        user_id_str = str(request.user.id)
        if user_id_str in invitation_code.users:
            return ApiResults.FAIL(detail="You have already bound this invitation code")

        invitation_code.users[user_id_str] = {
            "email": request.user.email,
            "bind_date": datetime.now().isoformat(),
        }

        if len(invitation_code.users) <= invitation_code.max_count:
            invitation_code.used_at = datetime.now()

        await Profile.objects.filter(user=request.user).aupdate(is_in_waitlist=False)
        await invitation_code.asave()
        return ApiResults.SUCCESS(data=None)
    except InvitationCode.DoesNotExist:
        return ApiResults.NOT_FOUND(detail="Invalid invitation code")
