from typing import List

from asgiref.sync import sync_to_async
from django.db.models import F
from django.db.models.expressions import Window
from django.db.models.functions import RowNumber
from django.http.request import HttpRequest
from ninja import Query
from ninja.router import Router

from agent.models import CompanyWatchList
from agent.restapi.v1.schema import CompanyWatchListDocumentListParams
from biz import datasdk
from datasvc.enums import DocStatus
from datasvc.models import DocModel
from ..response import ApiResults, ApiResponse, PaginationSchema

company_dashboard_router = Router(tags=["company-dashboard"])


@company_dashboard_router.get("/watch-list")
async def list(
    request: HttpRequest,
    paging: Query[PaginationSchema],
) -> ApiResponse:
    qs = (
        CompanyWatchList.objects.all().filter(user=request.user).order_by("-created_at")
    )
    list = [item.to_dict() async for item in qs]
    response = ApiResults.SUCCESS(data=list)
    return await response.with_pagination(paging)


async def fetch_documents(
    tickers: List[str], doc_type: str, max_docs_per_ticker: int
) -> ApiResponse:
    doc_model_cls = DocModel.get_cls_by_type(doc_type)

    if not doc_model_cls:
        return ApiResults.BAD_REQUEST(data="Invalid document type")

    annotated_qs = (
        doc_model_cls.objects.filter(ticker__in=tickers, status__in=[DocStatus.INDEXED])
        .annotate(
            rn=Window(
                expression=RowNumber(),
                partition_by=F("ticker"),
                order_by=F("pub_date").desc(),
            )
        )
        .filter(rn__lte=max_docs_per_ticker)
    )

    documents = [
        {**doc.to_meta().to_dict(), "display_title": doc.display_title}
        async for doc in annotated_qs
    ]
    documents.sort(key=lambda x: x["pub_date"], reverse=True)

    return ApiResults.SUCCESS(data=documents)


@company_dashboard_router.get("/watch-list/documents/{doc_type}")
async def list_documents(
    request: HttpRequest,
    payload: Query[CompanyWatchListDocumentListParams],
    doc_type: str,
) -> ApiResponse:
    # TODO: actually it's to get docs by multiple tickers. should use /docs/ with payload {"doc_types": [], "tickers": []}
    qs = CompanyWatchList.objects.filter(user=request.user).order_by("-created_at")
    tickers = [item.ticker async for item in qs]
    return await fetch_documents(tickers, doc_type, payload.max_docs_per_ticker)


# @company_dashboard_router.get("/{ticker}/documents/{doc_type}")
# async def list_documents_for_ticker(
#     request: HttpRequest,
#     payload: Query[CompanyWatchListDocumentListParams],
#     doc_type: str,
#     ticker: str,
# ) -> ApiResponse:
#     return await fetch_documents(
#         [ticker.upper()], doc_type, payload.max_docs_per_ticker
#     )


@company_dashboard_router.post("/watch-list/{ticker}")
async def add(request: HttpRequest, ticker: str) -> ApiResponse:
    tickers_list = [str.strip().upper() for str in ticker.split(",")]
    results = []

    total_watch_list_count = await CompanyWatchList.objects.filter(
        user=request.user
    ).acount()

    if total_watch_list_count >= 30:
        return ApiResults.FAIL(
            data="You have reached the maximum number of watch list items"
        )

    for ticker in tickers_list:
        company_name = await sync_to_async(datasdk.company_name_by_ticker)(ticker)
        if not company_name:
            results.append({"ticker": ticker, "status": "Company not found"})
            continue

        if await CompanyWatchList.objects.filter(
            user=request.user, ticker=ticker
        ).aexists():
            results.append(
                {"ticker": ticker, "status": "Company already in watch list"}
            )
            continue

        company_watch_list = await sync_to_async(CompanyWatchList.objects.create)(
            user=request.user,
            ticker=ticker,
            company_name=company_name,
        )
        results.append(
            {"ticker": ticker, "status": "Added", "data": company_watch_list.to_dict()}
        )

    return ApiResults.SUCCESS(data=results)


@company_dashboard_router.delete("/watch-list/{ticker}")
async def delete(request: HttpRequest, ticker: str) -> ApiResponse:
    ticker = ticker.upper()
    try:
        company = await sync_to_async(CompanyWatchList.objects.get)(
            user=request.user, ticker=ticker
        )
        await sync_to_async(company.delete)()
    except CompanyWatchList.DoesNotExist:
        return ApiResults.BAD_REQUEST(data="Company not found")
    return ApiResults.SUCCESS()
