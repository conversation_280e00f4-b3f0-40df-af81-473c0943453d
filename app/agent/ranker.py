import itertools
import json
import logging
from abc import abstractmethod
from typing import List

from biz.enums import DocTypeEnum
from biz.structs import Doc<PERSON>hunk


logger = logging.getLogger(__name__)


class Ranker:
    @abstractmethod
    def rank(
        self,
        fetched_data: List[DocChunk],
        token_limit: int = 7000,
    ) -> List[DocChunk]:
        """Rank the fetched data and truncate."""


class ReverseChronoCompanyDiversityRanker:
    def rank(
        self,
        fetched_data: List[DocChunk],
    ) -> List[DocChunk]:
        """Rank first by reverse chronological order, then ensure company
        diversity."""

        def score_func(item: DocChunk) -> float:
            """First by chronological order, then by doc type."""
            doc_type_score = 0.0
            dt = item.meta.pub_date
            ts_score = dt.timestamp() if dt else 0.0
            if item.meta.doc_type in [
                DocTypeEnum.EARNING_CALL,
                DocTypeEnum.EVENT_TRANSCRIPT,
            ]:
                doc_type_score = 30
            elif item.meta.doc_type == DocTypeEnum.SEC_FILING:
                doc_type_score = 20
            elif item.meta.doc_type == DocTypeEnum.NEWS:
                doc_type_score = 10

            return doc_type_score + ts_score

        ticker_chunks: dict[str, list[DocChunk]] = {}
        for doc in fetched_data:
            ticker = doc.meta.ticker
            chunks = ticker_chunks.get(ticker, [])
            chunks.append(doc)
            ticker_chunks[ticker] = chunks

        if len(ticker_chunks) > 1:
            # ensure diversity for multi companies
            docs_per_company = 15
            ticker_chunks = {
                t: sorted(d, key=score_func, reverse=True)[:docs_per_company]
                for t, d in ticker_chunks.items()
            }

        chunks = list(itertools.chain(*[d for t, d in ticker_chunks.items()]))
        chunks = sorted(chunks, key=score_func, reverse=True)
        logger.info(f"ranker truncated to {len(chunks)} chunks.")
        logger.debug(
            f'{json.dumps([d.meta.doc_type.value + ":" + d.meta.key for d in chunks], indent=2)}'
        )
        return chunks
