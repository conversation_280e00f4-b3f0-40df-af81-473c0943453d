# Generated by Django 4.2.2 on 2023-12-28 11:00
import json

from django.db import migrations
from django.db.models import Count, Min


def remove_duplicated_fetched_source(apps, schema_editor):
    FetchedSource = apps.get_model("agent", "FetchedSource")

    duplicated = FetchedSource.objects.exclude(
        id__in=(
            FetchedSource.objects.values("chat_turn", "source")
            .annotate(id_min=Min("id"), cnt=Count("*"))
            .values_list("id_min", flat=True)
        )
    ).all()
    print("\n----- following duplicated fetched sources will be deleted -----")
    print(
        json.dumps(
            [f"{d.id} [turn/{d.chat_turn.id}] : {d.key}" for d in duplicated],
            indent=2,
        )
    )
    duplicated.delete()


class Migration(migrations.Migration):
    dependencies = [
        ("agent", "0075_alter_chatmsg_role_chart"),
    ]

    operations = [
        migrations.RunPython(remove_duplicated_fetched_source),
        migrations.AlterUniqueTogether(
            name="fetchedsource",
            unique_together={("chat_turn", "source")},
        ),
    ]
