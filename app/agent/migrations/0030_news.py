# Generated by Django 4.2.2 on 2023-10-10 07:44

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("agent", "0029_logdata_perf_logdata_retrieval"),
    ]

    operations = [
        migrations.CreateModel(
            name="News",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=500)),
                ("content", models.TextField()),
                ("url", models.<PERSON>r<PERSON>ield(max_length=500)),
                ("site", models.Char<PERSON>ield(max_length=200)),
                ("published_at", models.DateTimeField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
