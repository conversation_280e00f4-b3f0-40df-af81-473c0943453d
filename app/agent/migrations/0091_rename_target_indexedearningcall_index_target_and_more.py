# Generated by Django 4.2.2 on 2024-01-09 10:41

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("agent", "0090_merge_20240109_0804"),
    ]

    operations = [
        migrations.RenameField(
            model_name="indexedearningcall",
            old_name="target",
            new_name="index_target",
        ),
        migrations.RenameField(
            model_name="indexednews",
            old_name="target",
            new_name="index_target",
        ),
        migrations.RenameField(
            model_name="indexedsecfiling",
            old_name="target",
            new_name="index_target",
        ),
        migrations.AlterUniqueTogether(
            name="indexedearningcall",
            unique_together=set(),
        ),
        migrations.AlterUniqueTogether(
            name="indexednews",
            unique_together=set(),
        ),
        migrations.AlterUniqueTogether(
            name="indexedsecfiling",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="indexedearningcall",
            name="index_name",
            field=models.<PERSON>r<PERSON><PERSON>(default="", max_length=50),
        ),
        migrations.Add<PERSON>ield(
            model_name="indexednews",
            name="index_name",
            field=models.Char<PERSON>ield(default="", max_length=50),
        ),
        migrations.AddField(
            model_name="indexedsecfiling",
            name="index_name",
            field=models.CharField(default="", max_length=50),
        ),
        migrations.AlterUniqueTogether(
            name="indexedearningcall",
            unique_together={("index_target", "index_name", "doc")},
        ),
        migrations.AlterUniqueTogether(
            name="indexednews",
            unique_together={("index_target", "index_name", "doc")},
        ),
        migrations.AlterUniqueTogether(
            name="indexedsecfiling",
            unique_together={("index_target", "index_name", "doc")},
        ),
    ]
