# Generated by Django 4.2.2 on 2024-09-26 16:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("agent", "0152_alter_llmcost_category"),
    ]

    operations = [
        migrations.CreateModel(
            name="CompanyWatchList",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("ticker", models.Char<PERSON>ield(max_length=10)),
                ("company_name", models.<PERSON>r<PERSON>ield(max_length=255)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Companies Watch List",
                "verbose_name_plural": "Companies Watch List",
                "unique_together": {("user", "ticker")},
            },
        ),
    ]
