# Generated by Django 4.2.2 on 2024-01-22 13:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("agent", "0104_indexstatistics_updated_at_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="LlmCost",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "subject",
                    models.CharField(
                        choices=[("n/a", "NONE"), ("llm", "LLM")],
                        default="llm",
                        max_length=50,
                    ),
                ),
                (
                    "category",
                    models.Char<PERSON>ield(
                        choices=[
                            ("n/a", "NONE"),
                            ("llm_chat_intent", "LLM_CHAT_INTENT"),
                            ("llm_chat_planning", "LLM_CHAT_PLANNING"),
                            ("llm_chat_answer", "LLM_CHAT_ANSWER"),
                            ("llm_chat_clarify", "LLM_CHAT_CLARIFY"),
                            ("llm_chat_chart", "LLM_CHAT_CHART"),
                            ("llm_eval", "LLM_EVAL"),
                            ("llm_test", "LLM_TEST"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=4, help_text="unit: US$", max_digits=16
                    ),
                ),
                ("llm_model_name", models.CharField(max_length=50)),
                ("prompt_tokens", models.IntegerField()),
                ("completion_tokens", models.IntegerField()),
                (
                    "input_amount",
                    models.DecimalField(
                        decimal_places=4, help_text="unit: US$", max_digits=16
                    ),
                ),
                (
                    "output_amount",
                    models.DecimalField(
                        decimal_places=4, help_text="unit: US$", max_digits=16
                    ),
                ),
                (
                    "chat_turn",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="agent.chatturn",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
