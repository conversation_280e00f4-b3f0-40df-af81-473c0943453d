# Generated by Django 4.2.2 on 2024-10-28 16:20

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("agent", "0153_companywatchlist"),
    ]

    operations = [
        migrations.AddField(
            model_name="agentstates",
            name="recorded_function_calls",
            field=models.JSONField(default=list, max_length=100000),
        ),
        migrations.AlterField(
            model_name="llmcost",
            name="category",
            field=models.CharField(
                choices=[
                    ("n/a", "NONE"),
                    ("llm_chat_intent", "LLM_CHAT_INTENT"),
                    ("llm_chat_planning", "LLM_CHAT_PLANNING"),
                    ("llm_chat_answer", "LLM_CHAT_ANSWER"),
                    ("llm_chat_clarify", "LLM_CHAT_CLARIFY"),
                    ("llm_chat_chart", "LLM_CHAT_CHART"),
                    ("llm_chat_context", "LLM_CHAT_CONTEXT"),
                    ("llm_eval", "LLM_EVAL"),
                    ("llm_test", "LLM_TEST"),
                    ("llm_indicators", "LLM_INDICATORS"),
                    ("llm_blog", "LLM_BLOG"),
                    ("llm_research", "LLM_RESEARCH"),
                ],
                max_length=50,
            ),
        ),
    ]
