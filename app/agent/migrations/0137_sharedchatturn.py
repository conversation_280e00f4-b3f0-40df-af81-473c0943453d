# Generated by Django 4.2.2 on 2024-06-14 16:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("agent", "0136_alter_citation_doc_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="SharedChatTurn",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("uuid", models.CharField(max_length=255, unique=True)),
                ("title", models.CharField(max_length=255)),
                ("pub_date", models.DateTimeField(auto_now_add=True)),
                (
                    "chat_turn",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shared_chat_turns",
                        to="agent.chatturn",
                    ),
                ),
                (
                    "shared_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Shared Chat Turn",
                "verbose_name_plural": "Shared Chat Turns",
            },
        ),
    ]
