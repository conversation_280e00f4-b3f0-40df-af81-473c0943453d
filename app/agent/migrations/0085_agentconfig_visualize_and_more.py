# Generated by Django 4.2.2 on 2024-01-04 19:18

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("agent", "0084_merge_20240102_0415"),
    ]

    operations = [
        migrations.AddField(
            model_name="agentconfig",
            name="visualize",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="agentconfig",
            name="visualize_model_name",
            field=models.CharField(default="gpt-4-1106-preview", max_length=100),
        ),
        migrations.AlterField(
            model_name="agentconfig",
            name="answer_sys_prompt",
            field=models.TextField(
                blank=True,
                default='\nUse the provided references delimited by triple quotes to answer questions.\nIf the answer cannot be found in the references, write "I could not find an answer."\n\nWhen you give the answer, you should ONLY take the facts(citations) from the provided references and also give the sources in citations style [X] {"number": X, "source": "https://www.example.com", "quotes": "<the original quote in the references>"}, like this:\n\n"your example answer .... [1] {"number": 1, "source": "https://www.example.com", "quotes": "<the original quote in the references> like Zuck said .."} [2] {"number": 2, "source": "https://www.example2.com", "quotes": "<the original quote in the references> like Zuck also mentioned ..."}.\nSomething else you summarized from the references... [3] {"number": 3, "source": "https://www.example3.com", "quotes": "<the original quote in the references> .."}."\n\nRules:\n1. All the numbers should be shown in millions\n2. For each citation, exactly follow the format: [X] {"number": X, "source": "https://www.example.com", "quotes": "<the original quote in the references>"}\n3. For each citation, try to keep it the original quotes in reasonable size (LESS THAN 3 sentences)\n4. For each paragraph, never have more than 2 citations.\n',
                max_length=5000,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="agentconfig",
            name="answer_token_limit",
            field=models.IntegerField(default=15000),
        ),
        migrations.AlterField(
            model_name="agentconfig",
            name="hybrid_search_alpha",
            field=models.FloatField(
                default=0.3,
                help_text="alpha value for hybrid search, a float between 0 and 1 where 0 == sparse only and 1 == dense only",
                validators=[
                    django.core.validators.MinValueValidator(0.0),
                    django.core.validators.MaxValueValidator(1.0),
                ],
            ),
        ),
        migrations.AlterField(
            model_name="agentconfig",
            name="search_company_doc_num_results",
            field=models.IntegerField(default=40),
        ),
    ]
