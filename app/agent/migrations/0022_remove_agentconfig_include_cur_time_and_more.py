# Generated by Django 4.2.2 on 2023-09-20 03:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agent', '0021_earningcalltranscript_content'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='agentconfig',
            name='include_cur_time',
        ),
        migrations.AddField(
            model_name='agentconfig',
            name='short_term_memory_mins',
            field=models.IntegerField(default=5),
        ),
        migrations.AlterField(
            model_name='agentconfig',
            name='sys_prompt',
            field=models.TextField(blank=True, default='\nUse the provided references delimited by triple quotes to answer questions.\nIf the answer cannot be found in the references, write "I could not find an answer."\n\nWhen you give the answer, also give the sources in citations style like this:\n"your example answer .... [1]"\n[1] https://www.example.com\n(Please group same citations together)\n', max_length=5000, null=True),
        ),
    ]
