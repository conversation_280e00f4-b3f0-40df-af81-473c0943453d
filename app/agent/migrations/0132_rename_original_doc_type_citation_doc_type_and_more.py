# Generated by Django 4.2.2 on 2024-06-03 07:24

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("agent", "0131_alter_citation_original_doc_type"),
    ]

    operations = [
        migrations.RenameField(
            model_name="citation",
            old_name="original_doc_type",
            new_name="doc_type",
        ),
        migrations.RemoveField(
            model_name="chatturnstates",
            name="sources",
        ),
        migrations.RemoveField(
            model_name="citation",
            name="original_doc_date",
        ),
        migrations.RemoveField(
            model_name="citation",
            name="original_doc_ref",
        ),
        migrations.RemoveField(
            model_name="citation",
            name="source",
        ),
        migrations.RemoveField(
            model_name="citation",
            name="source_title",
        ),
        migrations.AddField(
            model_name="citation",
            name="display_title",
            field=models.CharField(
                default="", help_text="display title", max_length=255
            ),
            preserve_default=False,
        ),
        migrations.Add<PERSON><PERSON>(
            model_name="citation",
            name="doc_key",
            field=models.<PERSON>r<PERSON><PERSON>(default="", help_text="the doc key", max_length=100),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="citation",
            name="pub_date",
            field=models.DateTimeField(
                default=None, help_text="the original doc publish date", null=True
            ),
        ),
        migrations.AddField(
            model_name="citation",
            name="url",
            field=models.CharField(
                default="",
                help_text="url of the original doc comes from.",
                max_length=255,
            ),
            preserve_default=False,
        ),
    ]
