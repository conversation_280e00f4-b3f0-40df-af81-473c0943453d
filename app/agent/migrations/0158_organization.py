# Generated by Django 4.2.2 on 2024-11-26 13:59

import re

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


def create_org_for_existed_user(apps, schema_editor) -> None:
    orgs = []
    user_cls = apps.get_model("auth", "User")
    organization_cls = apps.get_model("agent", "Organization")
    db_alias = schema_editor.connection.alias

    for user in user_cls.objects.using(db_alias).all():
        # parts = user.username.split("@", 1)
        # if len(parts) == 2:
        #     key = parts[0]
        # else:
        #     key = user.username
        org = organization_cls(
            key=user.username,
            name=f"{user.username}'s default team",
            creator=user.username,
            holder=user,
            is_user_default=True,
        )
        orgs.append(org)

    organization_cls.objects.using(db_alias).bulk_create(orgs)


def create_org_for_existed_user_rollback(apps, schema_editor):
    organization_cls = apps.get_model("agent", "Organization")
    db_alias = schema_editor.connection.alias
    organization_cls.objects.using(db_alias).delete()


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("agent", "0157_remove_invitationcode_is_super_code_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "key",
                    models.CharField(
                        help_text="unique identity key",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                re.compile("^[-\\w]+\\Z"),
                                "Enter a valid “slug” consisting of Unicode letters, numbers, underscores, or hyphens.",
                                "invalid",
                            )
                        ],
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        db_index=True,
                        help_text="name to display or human reading",
                        max_length=255,
                    ),
                ),
                (
                    "creator",
                    models.CharField(
                        help_text="who created this organization", max_length=150
                    ),
                ),
                (
                    "is_user_default",
                    models.BooleanField(
                        default=False,
                        help_text="whether is user's default organization",
                    ),
                ),
                (
                    "holder",
                    models.ForeignKey(
                        help_text="who is holding this organization",
                        on_delete=django.db.models.deletion.SET,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "member_users",
                    models.ManyToManyField(
                        help_text="The member users this organization has.",
                        related_name="belongs_to_set",
                        related_query_name="organization",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sub_organization_set",
                        related_query_name="sub_organization",
                        to="agent.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.RunPython(
            create_org_for_existed_user, create_org_for_existed_user_rollback
        ),
    ]
