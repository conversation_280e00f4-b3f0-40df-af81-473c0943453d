# Generated by Django 4.2.2 on 2023-12-26 18:38

import django
import django.db.models.deletion
from django.db import migrations, models

import biz.enums


def create_default_doc_indexing_config(apps, schema_editor):
    DocIndexingConfig = apps.get_model("agent", "DocIndexingConfig")
    try:
        config = DocIndexingConfig(
            name="default",
            download_eval_threshold=170,
            download_config={"foo": "bar"},
            extract_eval_threshold=270,
            extract_config={"foo": "bar"},
            index_eval_threshold=370,
            index_config={
                "type": "pinecone_hybrid",
                "index_name": "onwish-dev",
                "api_key": "123435",
                "env": "dev",
                "namespace": "dev",
                "openai_api_key": "123456",
            },
        )
        config.save()
    except django.db.utils.OperationalError as e:
        if "no such table" in str(e):
            print(
                "    Ignore DocIndexingConfig due to it was deleted, renamed or moved away. (no such table)"
            )


class Migration(migrations.Migration):
    dependencies = [
        ("agent", "0074_agentconfig_planner_model_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="DocIndexingConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=50, unique=True)),
                (
                    "download_target",
                    models.CharField(
                        choices=[("db", "DB")], default="db", max_length=20
                    ),
                ),
                (
                    "download_eval_threshold",
                    models.IntegerField(
                        choices=[
                            (175, "DOWNLOAD_EVAL_OK | 175"),
                            (179, "DOWNLOAD_EVAL_GOOD | 179"),
                            (180, "DOWNLOAD_EVAL_PASS | 180"),
                            (189, "DOWNLOAD_EVAL_BEST | 189"),
                        ],
                        default=180,
                        help_text="indicate whether the doc can finish eval of downloading and go next phrase extracting",
                    ),
                ),
                ("download_config", models.JSONField(default=dict)),
                (
                    "extract_target",
                    models.CharField(
                        choices=[("db", "DB")], default="db", max_length=20
                    ),
                ),
                (
                    "extract_eval_threshold",
                    models.IntegerField(
                        choices=[
                            (275, "EXTRACT_EVAL_OK | 275"),
                            (279, "EXTRACT_EVAL_GOOD | 279"),
                            (280, "EXTRACT_EVAL_PASS | 280"),
                            (285, "EXTRACT_EVAL_BETTER | 285"),
                            (289, "EXTRACT_EVAL_BEST | 289"),
                        ],
                        default=280,
                        help_text="indicate whether the doc can finish eval of extracting and go next phrase indexing",
                    ),
                ),
                ("extract_config", models.JSONField(default=dict)),
                (
                    "index_target",
                    models.CharField(
                        choices=[
                            ("pinecone_embedding", "PINECONE_EMBEDDING"),
                            ("pinecone_hybrid", "PINECONE_HYBRID"),
                        ],
                        default=biz.enums.IndexProviderEnum["PINECONE_HYBRID"],
                        max_length=50,
                    ),
                ),
                (
                    "index_eval_threshold",
                    models.IntegerField(
                        choices=[
                            (375, "INDEX_EVAL_OK | 375"),
                            (379, "INDEX_EVAL_GOOD | 379"),
                            (380, "INDEX_EVAL_PASS | 380"),
                            (385, "INDEX_EVAL_BETTER | 385"),
                            (389, "INDEX_EVAL_BEST | 389"),
                        ],
                        default=380,
                        help_text="indicate whether the doc can finish eval of indexing and go next phrase",
                    ),
                ),
                ("index_config", models.JSONField(default=dict)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.RunPython(create_default_doc_indexing_config),
        migrations.RemoveField(
            model_name="agentconfig",
            name="searcher_type",
        ),
        migrations.AddField(
            model_name="agentconfig",
            name="doc_indexing_config",
            field=models.ForeignKey(
                db_column="doc_indexing_config",
                default="default",
                on_delete=django.db.models.deletion.SET_DEFAULT,
                to="agent.docindexingconfig",
                to_field="name",
            ),
        ),
        migrations.CreateModel(
            name="IndexedDoc",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "doc_type",
                    models.CharField(
                        choices=[
                            ("earning_call", "EARNING_CALL"),
                            ("news", "NEWS"),
                            ("sec_filing", "SEC_FILING"),
                            ("not_specified", "NOT_SPECIFIED"),
                        ],
                        default=biz.enums.DocTypeEnum["NOT_SPECIFIED"],
                        max_length=50,
                    ),
                ),
                ("source", models.CharField(max_length=500)),
                (
                    "status",
                    models.IntegerField(
                        choices=[
                            (0, "FETCH_READY | 0"),
                            (100, "FETCH_SUCCESS | 100"),
                            (110, "DOWNLOAD_IN_PROGRESS | 110"),
                            (150, "DOWNLOAD_FAIL | 150"),
                            (151, "DOWNLOAD_FAIL_TIMEOUT | 151"),
                            (170, "DOWNLOAD_DONE | 170"),
                            (175, "DOWNLOAD_EVAL_OK | 175"),
                            (179, "DOWNLOAD_EVAL_GOOD | 179"),
                            (180, "DOWNLOAD_EVAL_PASS | 180"),
                            (189, "DOWNLOAD_EVAL_BEST | 189"),
                            (200, "DOWNLOAD_SUCCESS | 200"),
                            (210, "EXTRACT_IN_PROGRESS | 210"),
                            (250, "EXTRACT_FAIL | 250"),
                            (270, "EXTRACT_DONE | 270"),
                            (275, "EXTRACT_EVAL_OK | 275"),
                            (279, "EXTRACT_EVAL_GOOD | 279"),
                            (280, "EXTRACT_EVAL_PASS | 280"),
                            (285, "EXTRACT_EVAL_BETTER | 285"),
                            (289, "EXTRACT_EVAL_BEST | 289"),
                            (300, "EXTRACT_SUCCESS | 300"),
                            (310, "INDEX_IN_PROGRESS | 310"),
                            (350, "INDEX_FAIL | 350"),
                            (370, "INDEX_DONE | 370"),
                            (375, "INDEX_EVAL_OK | 375"),
                            (379, "INDEX_EVAL_GOOD | 379"),
                            (380, "INDEX_EVAL_PASS | 380"),
                            (385, "INDEX_EVAL_BETTER | 385"),
                            (389, "INDEX_EVAL_BEST | 389"),
                            (400, "INDEX_SUCCESS | 400"),
                            (1000, "PROD_READY | 1000"),
                        ]
                    ),
                ),
                ("ids", models.JSONField(default=list)),
                ("scores", models.JSONField(default=list)),
                (
                    "config",
                    models.ForeignKey(
                        default="default",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="agent.docindexingconfig",
                    ),
                ),
            ],
            options={
                "unique_together": {("config", "source")},
            },
        ),
    ]
