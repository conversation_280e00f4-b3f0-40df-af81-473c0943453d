# Generated by Django 4.2.2 on 2024-03-08 10:11

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("agent", "0109_chatturn_guide"),
    ]

    operations = [
        migrations.CreateModel(
            name="Metric",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("ticker", models.Char<PERSON>ield(max_length=20)),
                ("name", models.CharField(max_length=100)),
                (
                    "description",
                    models.Char<PERSON>ield(blank=True, max_length=500, null=True),
                ),
                ("data", models.J<PERSON><PERSON>ield(default=dict)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AlterField(
            model_name="citation",
            name="original_doc_type",
            field=models.<PERSON>r<PERSON><PERSON>(
                choices=[
                    ("financials", "FINANCIALS"),
                    ("earning_call", "EARNING_CALL"),
                    ("news", "NEWS"),
                    ("sec_filing", "SEC_FILING"),
                    ("press_release", "PRESS_RELEASE"),
                    ("not_specified", "NOT_SPECIFIED"),
                ],
                max_length=50,
            ),
        ),
    ]
