# Generated by Django 4.2.2 on 2024-01-12 10:23

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("agent", "0093_remove_invitationcode_created_at_alter_chart_table_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ContactUs",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("email", models.EmailField(max_length=254)),
                ("name", models.Char<PERSON>ield(max_length=100)),
                ("message", models.TextField()),
                ("is_handled", models.<PERSON><PERSON><PERSON><PERSON>ield(default=False)),
                ("remark", models.TextField(null=True)),
                ("ip", models.Char<PERSON>ield(max_length=30, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="agentconfig",
            name="answer_max_complete_tokens",
            field=models.Integer<PERSON>ield(default=1500),
        ),
    ]
