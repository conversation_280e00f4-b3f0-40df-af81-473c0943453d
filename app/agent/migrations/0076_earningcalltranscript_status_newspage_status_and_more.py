# Generated by Django 4.2.2 on 2023-12-26 19:30

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("agent", "0075_docindexingconfig_remove_agentconfig_searcher_type_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="earningcalltranscript",
            name="status",
            field=models.IntegerField(
                choices=[
                    (0, "FETCH_READY | 0"),
                    (100, "FETCH_SUCCESS | 100"),
                    (110, "DOWNLOAD_IN_PROGRESS | 110"),
                    (150, "DOWNLOAD_FAIL | 150"),
                    (151, "DOWNLOAD_FAIL_TIMEOUT | 151"),
                    (170, "DOWNLOAD_DONE | 170"),
                    (175, "DOWNLOAD_EVAL_OK | 175"),
                    (179, "DOWNLOAD_EVAL_GOOD | 179"),
                    (180, "DOWNLOAD_EVAL_PASS | 180"),
                    (189, "DOWNLOAD_EVAL_BEST | 189"),
                    (200, "DOWNLOAD_SUCCESS | 200"),
                    (210, "EXTRACT_IN_PROGRESS | 210"),
                    (250, "EXTRACT_FAIL | 250"),
                    (270, "EXTRACT_DONE | 270"),
                    (275, "EXTRACT_EVAL_OK | 275"),
                    (279, "EXTRACT_EVAL_GOOD | 279"),
                    (280, "EXTRACT_EVAL_PASS | 280"),
                    (285, "EXTRACT_EVAL_BETTER | 285"),
                    (289, "EXTRACT_EVAL_BEST | 289"),
                    (300, "EXTRACT_SUCCESS | 300"),
                ],
                default=270,
                help_text="indicate whether the doc can finish eval of downloading and go next phrase extracting",
            ),
        ),
        migrations.AddField(
            model_name="newspage",
            name="status",
            field=models.IntegerField(
                choices=[
                    (0, "FETCH_READY | 0"),
                    (100, "FETCH_SUCCESS | 100"),
                    (110, "DOWNLOAD_IN_PROGRESS | 110"),
                    (150, "DOWNLOAD_FAIL | 150"),
                    (151, "DOWNLOAD_FAIL_TIMEOUT | 151"),
                    (170, "DOWNLOAD_DONE | 170"),
                    (175, "DOWNLOAD_EVAL_OK | 175"),
                    (179, "DOWNLOAD_EVAL_GOOD | 179"),
                    (180, "DOWNLOAD_EVAL_PASS | 180"),
                    (189, "DOWNLOAD_EVAL_BEST | 189"),
                    (200, "DOWNLOAD_SUCCESS | 200"),
                    (210, "EXTRACT_IN_PROGRESS | 210"),
                    (250, "EXTRACT_FAIL | 250"),
                    (270, "EXTRACT_DONE | 270"),
                    (275, "EXTRACT_EVAL_OK | 275"),
                    (279, "EXTRACT_EVAL_GOOD | 279"),
                    (280, "EXTRACT_EVAL_PASS | 280"),
                    (285, "EXTRACT_EVAL_BETTER | 285"),
                    (289, "EXTRACT_EVAL_BEST | 289"),
                    (300, "EXTRACT_SUCCESS | 300"),
                ],
                default=270,
                help_text="indicate whether the doc can finish eval of downloading and go next phrase extracting",
            ),
        ),
        migrations.AddField(
            model_name="secfiling",
            name="status",
            field=models.IntegerField(
                choices=[
                    (0, "FETCH_READY | 0"),
                    (100, "FETCH_SUCCESS | 100"),
                    (110, "DOWNLOAD_IN_PROGRESS | 110"),
                    (150, "DOWNLOAD_FAIL | 150"),
                    (151, "DOWNLOAD_FAIL_TIMEOUT | 151"),
                    (170, "DOWNLOAD_DONE | 170"),
                    (175, "DOWNLOAD_EVAL_OK | 175"),
                    (179, "DOWNLOAD_EVAL_GOOD | 179"),
                    (180, "DOWNLOAD_EVAL_PASS | 180"),
                    (189, "DOWNLOAD_EVAL_BEST | 189"),
                    (200, "DOWNLOAD_SUCCESS | 200"),
                    (210, "EXTRACT_IN_PROGRESS | 210"),
                    (250, "EXTRACT_FAIL | 250"),
                    (270, "EXTRACT_DONE | 270"),
                    (275, "EXTRACT_EVAL_OK | 275"),
                    (279, "EXTRACT_EVAL_GOOD | 279"),
                    (280, "EXTRACT_EVAL_PASS | 280"),
                    (285, "EXTRACT_EVAL_BETTER | 285"),
                    (289, "EXTRACT_EVAL_BEST | 289"),
                    (300, "EXTRACT_SUCCESS | 300"),
                ],
                default=270,
                help_text="indicate whether the doc can finish eval of downloading and go next phrase extracting",
            ),
        ),
        migrations.AlterField(
            model_name="docindexingconfig",
            name="download_eval_threshold",
            field=models.IntegerField(
                choices=[
                    (170, "DOWNLOAD_DONE | 170"),
                    (175, "DOWNLOAD_EVAL_OK | 175"),
                    (179, "DOWNLOAD_EVAL_GOOD | 179"),
                    (180, "DOWNLOAD_EVAL_PASS | 180"),
                    (189, "DOWNLOAD_EVAL_BEST | 189"),
                ],
                default=180,
                help_text="indicate whether the doc can finish eval of downloading and go next phrase extracting",
            ),
        ),
        migrations.AlterField(
            model_name="docindexingconfig",
            name="extract_eval_threshold",
            field=models.IntegerField(
                choices=[
                    (270, "EXTRACT_DONE | 270"),
                    (275, "EXTRACT_EVAL_OK | 275"),
                    (279, "EXTRACT_EVAL_GOOD | 279"),
                    (280, "EXTRACT_EVAL_PASS | 280"),
                    (285, "EXTRACT_EVAL_BETTER | 285"),
                    (289, "EXTRACT_EVAL_BEST | 289"),
                ],
                default=280,
                help_text="indicate whether the doc can finish eval of extracting and go next phrase indexing",
            ),
        ),
        migrations.AlterField(
            model_name="docindexingconfig",
            name="index_eval_threshold",
            field=models.IntegerField(
                choices=[
                    (370, "INDEX_DONE | 370"),
                    (375, "INDEX_EVAL_OK | 375"),
                    (379, "INDEX_EVAL_GOOD | 379"),
                    (380, "INDEX_EVAL_PASS | 380"),
                    (385, "INDEX_EVAL_BETTER | 385"),
                    (389, "INDEX_EVAL_BEST | 389"),
                ],
                default=380,
                help_text="indicate whether the doc can finish eval of indexing and go next phrase",
            ),
        ),
    ]
