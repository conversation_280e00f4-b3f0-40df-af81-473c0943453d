# Generated by Django 4.2.2 on 2023-09-21 03:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agent', '0023_agentconfig_news_fetch_per_query_num_results_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='agentconfig',
            name='answer_token_limit',
            field=models.IntegerField(default=7000),
        ),
        migrations.AlterField(
            model_name='agentconfig',
            name='search_earning_num_results',
            field=models.IntegerField(default=4),
        ),
    ]
