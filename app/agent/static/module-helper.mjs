export function loadModule() {
  if (location.host.includes("localhost")) {
    return import(
      "http://localhost:5178/static/django-admin-vue/src/main.ts"
    ).catch(() => {
      return loadStaticFile();
    });
  } else {
    return loadStaticFile();
  }
}

function loadStaticFile() {
  if (!document.querySelector("#django-admin-vue-style")) {
    let link = document.createElement("link");
    link.setAttribute("rel", "stylesheet");
    link.setAttribute("type", "text/css");
    link.href = `/static/django-admin-vue/src/style.css?v=3`;
    link.setAttribute("id", "django-admin-vue-style");
    document.querySelector("head").appendChild(link);
  }
  return import("/static/django-admin-vue/src/main.js?v=4").catch(() => {
    console.info(
      `%cIt seems that some FE modules failed to load. There are two possible solutions:\n1. cd fe && npm run dev\n2. cd fe && npm run build`,
      "font-size: 20px;"
    );
    console.info(
      `%cThe first method is used for development mode. and the second method is used for production builds.\nAdditionally, if Node.js is not installed, please go to https://nodejs.org/en to download the LTS version`,
      "font-size: 18px;"
    );
  });
}
