import json
from abc import ABC, abstractmethod
from typing import ClassV<PERSON>, List, Literal, Optional

from pydantic import BaseModel

from agent.conf import S3_BLOG_ENV
from common.services import s3
from common.services.google.index import index_url

BLOG_BUCKET = "onwish-content"
PAGE_PREFIX = "https://www.onwish.ai"


class BlogPageMeta(BaseModel):
    title: str
    file_name: str  # the filename in s3 that contains this data
    published_date: str  # in format like "2024-06-27T15:51:44.786Z"
    description: str
    cover: str  # the url of the cover image
    labels: List[str] = []  # labels for the blog

    def as_dict(self) -> dict:
        return {
            "title": self.title,
            "file_name": self.file_name,
            "published_date": self.published_date,
            "description": self.description,
            "cover": self.cover,
            "labels": self.labels,
        }


class BlogPage(BaseModel, ABC):
    content_type: ClassVar[str]

    meta: BlogPageMeta
    content: str  # in md or json format
    content_file_type: Literal["md", "json"] = "md"

    @property
    @abstractmethod
    def page_url(self) -> str:
        pass

    def persist_s3(self, suffix: Optional[str] = None) -> None:
        if suffix:
            # add a suffix to the file name and title for testing
            self.meta.file_name = f"{self.meta.file_name}-{suffix}"
            self.meta.title = f"{self.meta.title} ({suffix})"

        if self.content_file_type == "md":
            s3.put_str_object(
                BLOG_BUCKET,
                f"{S3_BLOG_ENV}/{self.content_type}/detail/{self.meta.file_name}/content.{self.content_file_type}",
                self.content,
            )
        else:
            s3.put_json(
                BLOG_BUCKET,
                f"{S3_BLOG_ENV}/{self.content_type}/detail/{self.meta.file_name}/content.{self.content_file_type}",
                self.content,
            )
        s3.put_str_object(
            BLOG_BUCKET,
            f"{S3_BLOG_ENV}/{self.content_type}/detail/{self.meta.file_name}/meta.json",
            json.dumps(self.meta.as_dict(), indent=4),
        )
        # also update blog-list.json for the blog list index page
        blog_list = s3.get_str_object(
            BLOG_BUCKET, f"{S3_BLOG_ENV}/{self.content_type}/list.json"
        )
        blog_list = json.loads(blog_list) if blog_list else []
        blog_by_file_name = {b["file_name"]: b for b in blog_list}
        blog_by_file_name[self.meta.file_name] = self.meta.as_dict()
        blog_list = list(blog_by_file_name.values())
        s3.put_json(
            BLOG_BUCKET,
            f"{S3_BLOG_ENV}/{self.content_type}/list.json",
            json.dumps(blog_list, indent=4),
        )

    def request_google_indexing(self) -> None:
        index_url(self.page_url)


class InsightsBlogPage(BlogPage):
    content_type: ClassVar[str] = "insights"

    @property
    def page_url(self) -> str:
        return f"{PAGE_PREFIX}/insights/{self.meta.file_name}"


class EarningCallBlogPageMeta(BlogPageMeta):
    ticker: str
    company_name: str
    year: int
    quarter: int
    earning_call_published_date: str

    def as_dict(self) -> dict:
        base_dict = super().as_dict()
        base_dict["ticker"] = self.ticker
        base_dict["company_name"] = self.company_name
        base_dict["year"] = self.year
        base_dict["quarter"] = self.quarter
        base_dict["earning_call_published_date"] = self.earning_call_published_date
        return base_dict


class EarningCallBlogPage(BlogPage):
    meta: EarningCallBlogPageMeta
    content_type: ClassVar[str] = "earning-call"
    content_file_type: Literal["json"] = "json"

    @property
    def page_url(self) -> str:
        return f"{PAGE_PREFIX}/resources/earning-call-summary/{self.meta.file_name}"


class AutogatherInfluencersBlogPageMeta(BlogPageMeta):
    platform: str
    summary: str
    by_niche: list[str]
    by_country: list[str]
    by_city: list[str]
    relevant_tags: list[str]

    def as_dict(self) -> dict:
        base_dict = super().as_dict()
        base_dict["platform"] = self.platform
        base_dict["summary"] = self.summary
        base_dict["by_niche"] = self.by_niche
        base_dict["by_country"] = self.by_country
        base_dict["by_city"] = self.by_city
        base_dict["relevant_tags"] = self.relevant_tags
        return base_dict


class MarketMovingBlogPageMeta(BlogPageMeta):
    ticker: str
    company_name: str
    change_percent: float
    market_moving_date: str

    def as_dict(self) -> dict:
        base_dict = super().as_dict()
        base_dict["ticker"] = self.ticker
        base_dict["company_name"] = self.company_name
        base_dict["market_moving_date"] = self.market_moving_date
        base_dict["change_percent"] = round(self.change_percent * 100, 1)
        return base_dict


class MarketMovingBlogPage(BlogPage):
    meta: MarketMovingBlogPageMeta
    content_type: ClassVar[str] = "market-moving"
    content_file_type: Literal["json"] = "json"

    @property
    def page_url(self) -> str:
        return f"{PAGE_PREFIX}/resources/market-moving/{self.meta.file_name}"


class BlogPageList(BaseModel):
    blog_pages: List[BlogPage]

    def to_meta_dict(self) -> List[dict]:
        return [blog_page.meta.as_dict() for blog_page in self.blog_pages]
