import asyncio
import json
import logging
import os
import time
import traceback
from collections import OrderedDict
from collections.abc import AsyncGenerator
from typing import Any, Dict, List, Tuple

from apify_client import ApifyClientAsync
from asgiref.sync import sync_to_async
from bitarray import bitarray
from django.utils.datetime_safe import datetime
from future.backports.datetime import timed<PERSON>ta

from agent.agents.base import Agent
from agent.agents.research import (
    <PERSON><PERSON><PERSON>,
    Field,
    NameField,
    ResearchAgentBase,
    Schema,
    URLField,
    convert_evaluation_to_criteria,
)
from agent.config.agent import InfluencerFinderAgentConfig
from agent.cost import LlmCostTracker
from agent.enum import CostCategory
from agent.llms import create_llm
from agent.prompts import create_prompt_builder
from agent.prompts.influencer import (
    INFLUENCER_ENRICH_RESPONSE_FORMAT,
    INFLUENCER_ENRICH_SYS_PROMPT,
    INFLUENCER_FINDER_INFER_CRITERIA_FEW_SHOTS,
    INFLUENCER_FINDER_INFER_CRITERIA_RESPONSE_FORMAT,
    INFLUENCER_FINDER_INFER_CRITERIA_SYS_PROMPT,
    INFLUENCER_FINDER_PLAN_GOOGLE_FEW_SHOTS,
    INFLUENCER_FINDER_PLAN_GOOGLE_PROMPT,
    INFLUENCER_FINDER_PLAN_GOOGLE_RESPONSE_FORMAT,
    INFLUENCER_FINDER_PLAN_HASHTAG_FEW_SHOTS,
    INFLUENCER_FINDER_PLAN_HASHTAG_PROMPT,
    INFLUENCER_FINDER_PLAN_HASHTAG_RESPONSE_FORMAT,
    INFLUENCER_FINDER_PLAN_YOUTUBE_PROMPT,
    INFLUENCER_FINDER_PLAN_YOUTUBE_RESPONSE_FORMAT,
)
from agent.utils import timer
from aiflow.models import AIFlow, InstagramPostModel
from aiflow.observer import AIFlowObserver
from common.django_ext.model import User
from common.services import slack
from common.utils.confutil import get_bool_env
from common.utils.datetimeutils import LOCAL_TZ
from common.utils.lang import get_caller_loc
from scraper import scraper_sdk
from scraper.base import INFLUENCER_PLATFORMS
from scraper.engagement.tiktok import TiktokEngagementBuilder
from scraper.models import (
    SocialProfile,
    InstagramPost,
)
from scraper.spiders.base import Spider
from scraper.spiders.google import InstagramGoogleSearchSpider
from scraper.spiders.instagram import (
    InstagramSearchSpiderConfig,
)
from scraper.spiders.linkedin import (
    LinkedinGoogleSearchSpider,
    LinkedinSearchSpiderConfig,
)
from scraper.spiders.tiktok import TiktokSearchSpiderConfig, TiktokSearchSpider
from scraper.spiders.youtube import (
    YoutubeSearchSpider,
    YoutubeSearchSpiderConfig,
)

logger = logging.getLogger(__name__)


# TODO(ruiwang): remove this after hacking
from pathlib import Path

with open(
    os.path.join(
        Path(__file__).resolve().parent.parent.parent.parent, "mock_tiktok.json"
    ),
    "r",
) as f:
    mock_apify_data: List[Dict[str, Any]] = json.load(f)


class RetrievalEvalStates(OrderedDict):
    """Stores search results and their evaluations in order of execution.

    Key: search query
    Value: list of dicts containing influencer profile and evaluation results
    """

    def add(
        self, query: str, profile: SocialProfile, qualified: bool, evaluation: dict
    ) -> None:
        """Add a search result with its evaluation for a query"""
        if query not in self:
            self[query] = []

        self[query].append(
            {
                "profile": {
                    "handle": profile.handle,
                    "full_name": profile.full_name,
                    "url": profile.url,
                    "followers": profile.followers,
                    "posts_count": profile.posts_count,
                    "biography": profile.biography,
                    "email": profile.email,
                    "relevant_post": {
                        "url": profile.relevant_post.url,
                        "caption": profile.relevant_post.caption,
                    }
                    if profile.relevant_post
                    else None,
                },
                "qualified": qualified,
                "evaluation": evaluation,
            }
        )

    def all_results(self) -> List[Dict[str, Any]]:
        return [r for results in self.values() for r in results]

    def as_prompt_str(self) -> str:
        output = []
        for query, results in self.items():
            qualified_count = sum(1 for r in results if r["qualified"])
            total_count = len(results)

            output.append(f"== Search query: {query} ==")
            output.append(
                f"Overview: {qualified_count}/{total_count} qualified results"
            )
            output.append("Selected search results (last 5):")
            output.append(f"{json.dumps(results[-5:], indent=2)}\n")

        return "\n".join(output)


class QualifiedCounter:
    __recent_limit: int
    __bits: bitarray

    __total: int
    __total_qualified: int

    def __init__(self, recent_limit: int = 20) -> None:
        self.__recent_limit = recent_limit
        self.__bits = bitarray(recent_limit)
        self.__total = 0
        self.__total_qualified = 0

    def __str__(self) -> str:
        return f"{self.__bits[-self.__recent_limit:].to01()} (Recent qualified/Recent total: {self.recent_qualified_count}/{self.recent_count}, Total qualified/Total: {self.total_qualified_count}/{self.total_count})"

    def append(self, qualified: bool) -> None:
        self.__bits = self.__bits << 1
        self.__total += 1
        if qualified:
            self.__bits[-1] = 1
            self.__total_qualified += 1

    @property
    def recent_count(self) -> int:
        return self.__recent_limit

    @property
    def recent_qualified_count(self) -> int:
        return self.__bits.count(1)

    @property
    def recent_qualify_rate(self) -> float:
        if self.total_count >= self.__recent_limit:
            return self.__bits.count(1) / self.__recent_limit
        else:
            return 1.0

    @property
    def total_count(self) -> int:
        return self.__total

    @property
    def total_qualified_count(self) -> int:
        return self.__total_qualified

    @property
    def total_qualify_rate(self) -> float:
        if self.__total > 0:
            return self.__total_qualified / self.__total
        else:
            return 1.0


class InfluencerSchemaInferrerAgent(Agent):
    config: InfluencerFinderAgentConfig

    async def run(self, **kwargs: Any) -> Tuple[Schema, str, str]:
        assert "task" in kwargs, "Task is required"
        task: str = kwargs["task"]

        prompt_msgs = create_prompt_builder(
            sys_prompt=INFLUENCER_FINDER_INFER_CRITERIA_SYS_PROMPT,
            user_msg=task,
            llm_model_name=self.config.llm_model_name,
            few_shots_msgs=INFLUENCER_FINDER_INFER_CRITERIA_FEW_SHOTS,
        ).build()
        ret = await create_llm(
            log_prefix="infer_criteria",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=INFLUENCER_FINDER_INFER_CRITERIA_RESPONSE_FORMAT,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        data = json.loads(ret)

        influencer_fields = [
            NameField(name="name", description="The full name of the influencer"),
            URLField(
                name="url",
                description="The URL of the influencer's profile page",
            ),
            Field(
                name="followers",
                description="The number of followers of the influencer",
            ),
            Field(
                name="engagement_rate",
                description="engagement statistics such as avg. views, likes, shares and etc. of recent posts",
            ),
            Field(
                name="email",
                description="The email address of the influencer",
            ),
            Field(
                name="content_focus",
                description="The type of content the influencer posts",
            ),
            Field(
                name="related_media_posted",
                description="A selected video posted by this influencer that is related to the task",
            ),
        ]

        linkedin_fields = [
            NameField(name="name", description="The full name of the person"),
            URLField(
                name="url",
                description="The URL of the person's linkedin profile page",
            ),
            Field(
                name="followers",
                description="The number of followers of the person",
            ),
            Field(
                name="engagement_rate",
                description="engagement statistics such as avg. views, likes, shares and etc. of recent posts",
            ),
            Field(
                name="title",
                description="job title of the person",
            ),
            Field(
                name="company",
                description="company name of which the person is current employed",
            ),
            Field(
                name="email",
                description="The email address of the influencer",
            ),
            Field(
                name="headline",
                description="the headline of the person",
            ),
            Field(
                name="connections",
                description="The number of linkedin connections of the person",
            ),
            Field(
                name="experience",
                description="recent work experience of the person",
            ),
            Field(
                name="skills",
                description="skills of the person",
            ),
        ]

        return (
            Schema(
                fields=(
                    linkedin_fields
                    if self.config.platform == "linkedin"
                    else influencer_fields
                ),
                criteria=data["criteria"],
            ),
            data["task_short_name"],
            "influencer",  # TODO(ruiwang|2025-01-05): remove this target entity definition since it's not used in influencer finder
        )


class InfluencerFinderAgent(ResearchAgentBase):
    """Agent to find influencers on social media"""

    observer: AIFlowObserver
    config: InfluencerFinderAgentConfig
    urls_evaluated: set[str] = set()

    counters: OrderedDict[str, QualifiedCounter] = OrderedDict()
    retrieval_eval_states: RetrievalEvalStates = RetrievalEvalStates()
    generated_entities: dict[str, dict] = dict()

    is_resumed: bool = False
    re_find_not_finished_if_resumed: bool = True

    _spider: Spider | None = None
    _engagement_pending: int = 0
    _platform_to_semaphore = {
        "tiktok": asyncio.Semaphore(2),
        "youtube": asyncio.Semaphore(8),
        "instagram": asyncio.Semaphore(20),
        "linkedin": asyncio.Semaphore(20),
    }

    class Config:
        arbitrary_types_allowed = True

    async def plan_instagram(self, task: str) -> List[str]:
        prompt_msgs = create_prompt_builder(
            sys_prompt=INFLUENCER_FINDER_PLAN_GOOGLE_PROMPT,
            user_msg=task,
            llm_model_name=self.config.llm_model_name,
            few_shots_msgs=INFLUENCER_FINDER_PLAN_GOOGLE_FEW_SHOTS,
        ).build()
        ret = await create_llm(
            log_prefix="plan_instagram",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=INFLUENCER_FINDER_PLAN_GOOGLE_RESPONSE_FORMAT,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        data = json.loads(ret)
        search_queries = [
            f"{term} {data['follower_range_term']} followers"
            for term in data["search_terms"]
        ]
        return search_queries

    async def plan_youtube(self, task: str) -> List[str]:
        prompt_msgs = create_prompt_builder(
            sys_prompt=INFLUENCER_FINDER_PLAN_YOUTUBE_PROMPT,
            user_msg=task,
            llm_model_name=self.config.llm_model_name,
            few_shots_msgs=[],
        ).build()
        ret = await create_llm(
            log_prefix="plan_youtube",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=INFLUENCER_FINDER_PLAN_YOUTUBE_RESPONSE_FORMAT,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        data = json.loads(ret)
        return data["search_queries"]

    async def plan_tiktok(self, task: str) -> List[str]:
        prompt_msgs = create_prompt_builder(
            sys_prompt=INFLUENCER_FINDER_PLAN_HASHTAG_PROMPT,
            user_msg=task,
            llm_model_name=self.config.llm_model_name,
            few_shots_msgs=INFLUENCER_FINDER_PLAN_HASHTAG_FEW_SHOTS,
        ).build()
        ret = await create_llm(
            log_prefix="plan_tiktok",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=INFLUENCER_FINDER_PLAN_HASHTAG_RESPONSE_FORMAT,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        data = json.loads(ret)
        return data["search_queries"]

    async def plan_linkedin(self, task: str) -> List[str]:
        """plan to search linkedin profile as Influencer (not professor)"""
        prompt_msgs = create_prompt_builder(
            sys_prompt=INFLUENCER_FINDER_PLAN_GOOGLE_PROMPT,
            user_msg=task,
            llm_model_name=self.config.llm_model_name,
            few_shots_msgs=None,
        ).build()
        ret = await create_llm(
            log_prefix="plan_instagram",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=INFLUENCER_FINDER_PLAN_GOOGLE_RESPONSE_FORMAT,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        data = json.loads(ret)
        return data["search_terms"]

    async def plan(self, task: str) -> List[str]:
        if self.config.platform == "instagram":
            return await self.plan_instagram(task)
        elif self.config.platform == "youtube":
            return await self.plan_youtube(task)
        elif self.config.platform == "tiktok":
            return await self.plan_tiktok(task)
        elif self.config.platform == "linkedin":
            return await self.plan_linkedin(task)
        else:
            raise ValueError(f"Unsupported platform: {self.config.platform}")

    async def _evaluate_and_add(  # noqa: C901
        self,
        task: str,
        schema: Schema,
        profile: SocialProfile,
        username: str = "",
        **kwargs: Any,
    ) -> Tuple[bool, Dict[str, Any]]:
        """Evaluate and add an influencer to the final table.
        Return a tuple of (is_match, evaluation_to_criteria)"""
        logger.info(f"Evaluating {profile.url}")

        # Deduplicate by url
        if profile.url in self.urls_evaluated:
            return False, {"filtered_out_reason": "duplicate"}

        # filter out low followers
        if profile.followers < 1000 and profile.platform not in ["linkedin"]:
            return False, {"filtered_out_reason": "low followers"}

        await self.observer.notify_placeholder(
            key=profile.key,
            action="add",
            name=profile.full_name,
            url=profile.url,
            text=f'Analyzing "{profile.full_name}" ...',
        )

        # filter for generated entity
        # TODO: bypass following if it was generated
        # row_id = len(self.final_table)
        # key = f"{profile.platform}|{profile.url}"
        # if key in generated_entities:
        #     row: dict = generated_entities[key]
        #     for k, v in row.items():
        #         await self.observer.notify_add_cell(
        #             row_id=str(row_id),
        #             column_id=k,
        #             value=v,
        #             sources=[],
        #             done=False,
        #         )
        #     entity = Entity.from_dict(self.task_schema, row)
        #     self.final_table.append(entity)
        #     self.enriched_entity_count += 1
        #     await self.observer.notify_progress(
        #         self.config.entities_limit, self.enriched_entity_count
        #     )
        #     return data["is_match"], assessment

        async def _async_fetch_engagement(_profile: SocialProfile) -> None:
            await self.observer.notify_status(f"Calculating ER for @{_profile.handle}")
            if self.config.engagements_for == "all":
                # for all. (before evaluating, must fetch, so blocking)
                await self._async_fetch_engagements(_profile, username=username)

            else:
                # for qualified only (after evaluating, better to fetch, so not blocking)
                asyncio.create_task(
                    self._async_fetch_engagements(_profile, username=username)
                )

        self.urls_evaluated.add(profile.url)

        if self.config.engagements_for == "all":
            # fetch recent post (engagement) for each profile
            await _async_fetch_engagement(profile)

        await self.observer.notify_status(f"Inspecting @{profile.handle}")

        # user prompt
        extra_eval_info: str = str(kwargs.get("extra_eval_info")) or ""
        profile_info = profile.model_dump_json()
        user_prompt = f"Profile:\n{profile_info}\n\nExtra Info:\n{extra_eval_info}"

        prompt_msgs = create_prompt_builder(
            sys_prompt=INFLUENCER_ENRICH_SYS_PROMPT(
                task=self.task,
                schema_str=schema.as_prompt_str(),
            ),
            user_msg=user_prompt,
            llm_model_name=self.config.llm_model_name,
        ).build()
        ret = await create_llm(
            log_prefix="enrich_influencer",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=INFLUENCER_ENRICH_RESPONSE_FORMAT(criteria=schema.criteria),
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        data = json.loads(ret)

        is_influencer = data["is_influencer"]
        is_influencer_reason = data["is_influencer_reason"]
        if not is_influencer:
            await self.observer.notify_placeholder(
                key=profile.key,
                action="add",
                name=profile.full_name,
                url=profile.url,
                text=f'"~~{profile.full_name}~~" is not qualified. ({is_influencer_reason})',
            )
            return False, {
                "filtered_out_reason": f"not_influencer: {is_influencer_reason}"
            }

        assessment = convert_evaluation_to_criteria(
            data["evaluation_to_criteria"],
            self.task_schema.criteria,
        )

        if await self.async_shall_stop():
            return False, {"filtered_out_reason": "stop_callback"}

        # calculate relevance
        relevance = self._calculate_relevance(assessment)

        if relevance == "no":
            await self.observer.notify_placeholder(
                key=profile.key,
                action="add",
                name=profile.full_name,
                url=profile.url,
                text=f'"~~{profile.full_name}~~" is not qualified.',
            )
            return False, assessment

        # remove qualified
        await self.observer.notify_placeholder(key=profile.key, action="remove")

        await self.observer.notify_profile(
            profile=profile.to_dict(datetime_to_str=True)
        )

        if profile.platform == "linkedin":
            # assert isinstance(profile, LinkedinProfile)
            entity = Entity(
                name=profile.full_name if profile.full_name else profile.handle,
                url=profile.url,
                field_data=[
                    {"field_name": "followers", "field_value": str(profile.followers)},
                    {"field_name": "engagement_rate", "field_value": "calculating ..."},
                    {"field_name": "title", "field_value": profile.job_title},
                    {
                        "field_name": "company",
                        "field_value": profile.company.name if profile.company else "",
                    },
                    {"field_name": "email", "field_value": data["email"]},
                    {"field_name": "headline", "field_value": profile.headline},
                    {
                        "field_name": "experience",
                        "field_value": f"{profile.job_title} {'at ' + profile.company.name if profile.company else ''}, {profile.current_job_duration}\n"
                        + "| Title | Company |        |\n"
                        + "| ----- | ------- | ------ |\n"
                        + "\n".join(
                            [
                                f"| {exp['job_title']} | {exp['company_name']} | {exp['period']} |"
                                for exp in profile.experiences
                            ]
                        ),
                    },
                    {
                        "field_name": "connections",
                        "field_value": str(profile.connections),
                    },
                    {
                        "field_name": "skills",
                        "field_value": f"{profile.top_skills_by_endorsements}\n"
                        + "| Skill | Endorsements |\n"
                        + "| ----- | ------------ |\n"
                        + "\n".join(
                            [
                                f"| {skill['title']} | {skill['endorsements']} |"
                                for skill in profile.skills
                            ]
                        ),
                    },
                ],
            )
        else:
            entity = Entity(
                name=profile.full_name if profile.full_name else profile.handle,
                url=profile.url,
                field_data=[
                    {
                        "field_name": "followers",
                        "field_value": str(profile.followers),
                    },
                    {"field_name": "engagement_rate", "field_value": "calculating ..."},
                    {
                        "field_name": "content_focus",
                        "field_value": data["content_focus"],
                    },
                    {
                        "field_name": "related_media_posted",
                        "field_value": data["relevant_post"],
                    },
                    {
                        "field_name": "email",
                        "field_value": data["email"],
                    },
                ],
            )

        self.final_table.append(entity)
        row_id = profile.key
        await self.observer.notify_evaluation(
            row_id=str(row_id),
            evaluation={
                "row_id": row_id,
                "row_name": entity.name,
                # "is_match": relevance in {"high", "medium"},
                "relevance": relevance,
                "criteria": assessment,
            },
        )
        for column_id, value in entity.all_fields_as_dict().items():
            await self.observer.notify_add_cell(
                row_id=str(row_id),
                column_id=column_id,
                value=value,
                sources=[],
                done=False,
            )

        if self.config.engagements_for == "qualified":
            # fetch recent post (engagement) only qualified profiles
            await _async_fetch_engagement(profile)

        if relevance not in {"high", "medium"}:
            # high, medium will be counted as qualified, low will not.
            return False, assessment

        await self.observer.notify_debug_info(
            row_id=str(row_id),
            debug_info={
                "generated": (
                    True
                    if self.generated_entities
                    and f"{profile.platform}|{profile.url}" in self.generated_entities
                    else False
                ),
            },
        )

        self.enriched_entity_count += 1
        await self.observer.notify_progress(
            self.config.entities_limit, self.enriched_entity_count
        )
        return data["is_match"], assessment

    async def retrieve(
        self, search_queries: List[str], **kwargs: Any
    ) -> AsyncGenerator[Tuple[str, SocialProfile], None]:
        if scraper_sdk.API_ENABLED:
            start_time = time.time()
            async for q, profile in self._search_influencers_local_api(
                search_queries,
                self.config.platform,
                username=kwargs.get("username") or "",
            ):
                elapsed_time = time.time() - start_time
                self.observer.log_perf({"latency_search_iter_per_yield": elapsed_time})
                start_time = time.time()  # Reset timer for next yield
                yield q, profile
        else:
            # if self.config.platform == "instagram":
            #     for query in search_queries:
            #         self.counters[query] = QualifiedCounter(
            #             recent_limit=self.config.recent_quality_window
            #         )
            #         await self.observer.notify_status(f"🔎 Searching {query}")
            #         start_time = time.time()
            #         async for profile in search_influencers_iter(
            #             self.observer,
            #             query,
            #             self.config.platform,
            #             self.config.per_search_items(),
            #             stop_callback=self.async_shall_stop,
            #         ):
            #             elapsed_time = time.time() - start_time
            #             self.observer.log_perf(
            #                 {"latency_search_iter_per_yield": elapsed_time}
            #             )
            #             start_time = time.time()  # Reset timer for next yield
            #             yield query, profile
            # else:
            start_time = time.time()
            async for q, profile in self._search_influencers_local_spider(
                search_queries,
                self.config.platform,
                username=kwargs.get("username") or "",
            ):
                elapsed_time = time.time() - start_time
                self.observer.log_perf({"latency_search_iter_per_yield": elapsed_time})
                start_time = time.time()  # Reset timer for next yield
                yield q, profile

    async def _evaluate(
        self, query: str, profile: SocialProfile, username: str, **kwargs: Any
    ) -> None:
        if await self.async_shall_stop(query):
            return

        # assert profile is not None

        qualified, evaluation_to_criteria = await self._evaluate_and_add(
            self.task, self.task_schema, profile, username=username, **kwargs
        )
        logger.info(
            f"Qualified: {qualified} for {profile.url}. Progress: {self.enriched_entity_count}/{self.config.entities_limit}"
        )

        self.counters[query].append(qualified)
        logger.debug(f"{query} - {self.counters[query]}")

        self.retrieval_eval_states.add(
            query, profile, qualified, evaluation_to_criteria
        )

    @timer(key="inner_run")
    async def inner_run(self, **kwargs: Any) -> List[Entity]:
        self.generated_entities: dict[
            str, dict
        ] = await self._fetch_generated_in_past_days(
            username=kwargs.get("username") or "", platform=kwargs.get("platform")
        )

        self.counters = OrderedDict()

        await self.observer.notify_status("🧠 Making a plan ...")
        search_queries = await self.plan(self.task)
        logger.info(f"Extract output: {json.dumps(search_queries, indent=2)}")
        self.observer.log_states(
            {
                "search_queries_in_order": search_queries,
                "engagements_for": self.config.engagements_for,
                "tiktok_engagements_with": self.config.tiktok_engagements_with,
            }
        )

        sem = asyncio.Semaphore(8)
        async for q, profile in self.retrieve(search_queries, **kwargs):
            # await self.observer.notify_placeholder(
            #     key=profile.key,
            #     action="add",
            #     name=profile.full_name,
            #     url=profile.url,
            #     text=f'Analyzing "{profile.full_name}" ...',
            # )

            if await self.async_shall_stop(q):
                continue

            async with sem:
                task = asyncio.create_task(
                    self._evaluate(q, profile, username=kwargs.get("username") or "")
                )
                task.add_done_callback(
                    lambda t: (
                        logger.error(
                            f"Error evaluating {profile.handle}: {t.exception()}\n"
                            f"{''.join(traceback.format_exception(t.exception()))}"
                        )
                        if t.exception()
                        else None
                    )
                )

        if self.enriched_entity_count <= 5:
            username = kwargs["username"]
            platform = kwargs["platform"]
            task_short_title = kwargs.get("task_short_title") or ""
            slack.send_alert(
                f"Research {self.observer.notifiers[0].aiflow.id} finished with only {self.enriched_entity_count} results. ('{task_short_title}', {platform}, {username})",
                force=True,
            )

        # save evaluated urls for resuming (is_resumed)
        await AIFlow.objects.filter(id=self.observer.notifiers[0].aiflow.id).aupdate(
            evaluated=list(self.urls_evaluated)
        )

        # wait for pending engagement fetching.
        ts = time.perf_counter()
        while self._engagement_pending > 0:
            logger.info(
                f"... waiting for {self._engagement_pending} pending engagements"
            )
            await self.observer.notify_status(
                f"Waiting for {self._engagement_pending} pending engagement calculation."
            )

            await asyncio.sleep(0.5)
            if time.perf_counter() - ts > 600:
                logger.info(
                    f"stop waiting pending engagements ({self._engagement_pending})"
                )
                await self.observer.notify_status(
                    f"Finished. Dropped {self._engagement_pending} pending engagement calculation."
                )
                break

        return self.final_table

    async def run(self, **kwargs: Any) -> List[Entity]:
        assert "username" in kwargs, "Username is required"
        assert "platform" in kwargs, "platform is required"
        assert self.task
        assert self.task_schema.fields

        username = kwargs["username"]
        platform = kwargs["platform"]
        task_short_title = kwargs.get("task_short_title") or ""

        try:
            self.final_table = await self.inner_run(**kwargs)
        except asyncio.CancelledError:
            logger.info("Research task was cancelled")
            raise
        except Exception as e:
            logger.error(f"Error in inner_run: {e}")
            self.observer.log_error(e)
            await self.observer.notify_status(
                "🚨 Sorry there seems to be an error. Please try again later."
            )
            raise
        finally:
            self._stop_event.set()
            if self._spider is not None:
                await self._spider.async_stop()

            # Use latency per generated influencer as the core perf metric
            overall_latency = self.observer.perf["latency_inner_run"][0]
            self.observer.log_perf(
                {
                    "latency_per_influencer": (
                        overall_latency / self.enriched_entity_count
                        if self.enriched_entity_count > 0
                        else 0
                    )
                }
            )
            self.observer.log_states(
                {
                    "search_results": {k: str(v) for k, v in self.counters.items()},
                    "criteria": self.task_schema.criteria,
                }
            )
            self.observer.log_retrieval(
                {
                    "retrieval_eval_states": self.retrieval_eval_states,
                }
            )
            await self.log_states(
                inputs={"username": username, "task": self.task},
                outputs={"table": [e.model_dump() for e in self.final_table]},
            )
            logger.warning(
                f"===== ===== =====     Research finished ('{task_short_title}', {platform}, {username})     ===== ===== ====="
            )

            return self.final_table

    def __spider_stop_call_back(self, spider: Spider, by_obj: Any) -> bool:
        """stop callback used for spider"""
        if not isinstance(by_obj, str):
            by_obj = str(by_obj)
        return self.shall_stop(by_obj, loc=get_caller_loc())

    async def async_shall_stop(self, by_term: str = "") -> bool:
        return await sync_to_async(self.shall_stop)(by_term, loc=get_caller_loc())

    def shall_stop(self, by_term: str = "", loc: str = "") -> bool:
        # logger.warning("--------- stop callback ------------")

        get_loc = lambda: loc if loc else get_caller_loc

        if self._stop_event.is_set():
            logger.info(
                f"{self.__class__.__name__} stop event is set. check from {get_loc()}"
            )
            return True

        if (
            not self.is_resumed
            and self.enriched_entity_count >= self.config.preview_limit
        ):
            logger.warning(
                f"{self.__class__.__name__} stop all due to reach preview limit {self.enriched_entity_count}/{self.config.preview_limit}"
                f" - check from {get_loc()}"
            )
            self._stop_event.set()
            return True

        if self.enriched_entity_count >= self.config.entities_limit:
            logger.warning(
                f"stop all due to reach limit {self.enriched_entity_count}/{self.config.entities_limit}"
                f" - check from {get_loc()}"
            )
            self._stop_event.set()
            return True

        # check per search term.
        if by_term:
            counter = self.counters.get(by_term)
            if (
                counter
                and counter.recent_qualify_rate < self.config.recent_qualify_rate
            ):
                # if qualified rate less than expected, stop current query
                logger.warning(
                    f'stop search "{by_term}" due to recent rate is {counter.recent_qualify_rate} < {self.config.recent_qualify_rate} ({counter.recent_qualified_count}/{counter.recent_count}).'
                    f" - check from {get_loc()}"
                )
                return True
        return False

    async def _search_influencers_local_api(
        self, search_queries: List[str], platform: INFLUENCER_PLATFORMS, username: str
    ) -> AsyncGenerator[Tuple[str, SocialProfile], None]:
        """search by local scraper API and returns async generator"""

        for q in search_queries:
            if await self.async_shall_stop():
                break

            payload = scraper_sdk.InfluencerSearchAPIPayload(
                query=q,
                items=self.config.per_search_items(),
                recent_posts=10,
            )

            logger.warning(f">>>>>>>>>>>   {q}    <<<<<<<<<<<<<")
            self.counters[q] = QualifiedCounter(
                recent_limit=self.config.recent_quality_window
            )
            await self.observer.notify_status(f"🔎 Searching {q}")
            profile: SocialProfile

            coro_for_stop = scraper_sdk.async_streaming_influencers_stop(
                platform=platform, payload=payload, username=username
            )
            async for profile in scraper_sdk.async_streaming_influencers(
                platform=platform,
                payload=payload,
                username=username,
                with_engagements=True if platform == "instagram" else False,
            ):
                if await self.async_shall_stop():
                    logger.info("stop influencer streaming API")
                    await scraper_sdk.async_streaming_influencers_stop(
                        platform=platform, payload=payload, username=username
                    )
                    break
                yield q, profile
            logger.warning(f"<<<<<<<<<<<    END {q}    >>>>>>>>>>")

    async def _search_influencers_local_spider(
        self, search_queries: List[str], platform: INFLUENCER_PLATFORMS, **kwargs: Any
    ) -> AsyncGenerator[Tuple[str, SocialProfile], None]:
        """search by local spider and returns async generator"""
        if platform == "tiktok":
            config_cls = TiktokSearchSpiderConfig
            spider_cls = TiktokSearchSpider
        elif platform == "instagram":
            config_cls = InstagramSearchSpiderConfig
            spider_cls = InstagramGoogleSearchSpider
        elif platform == "youtube":
            config_cls = YoutubeSearchSpiderConfig
            spider_cls = YoutubeSearchSpider
        elif platform == "linkedin":
            config_cls = LinkedinSearchSpiderConfig
            spider_cls = LinkedinGoogleSearchSpider

        for q in search_queries:
            if await self.async_shall_stop():
                logger.info("-- stop the next search due to reach limit")
                break

            logger.warning(f">>>>>>>>>>>   {q}    <<<<<<<<<<<<<")
            config_params = {
                "term": q,
                "name": f"{q}_{platform}_search",
                "browser_type": "chromium",
                "start_url": f"https://www.{platform}.com",
                "timeout": 60,
                "ignored_resources": ["font", "image", "media", "websocket"],
                "pages": 0,
                "items": self.config.per_search_items(),
                "stop_callback": self.__spider_stop_call_back,
            }
            if platform in ["youtube", "instagram"]:
                config_params["with_profile"] = True
            cfg = config_cls(**config_params)
            spider: Spider = spider_cls(config=cfg)
            self._spider = spider

            if platform == "tiktok":
                cfg.with_engagements = self.config.tiktok_engagements_with == "spider"
                self.observer.log_states(
                    {
                        "with_engagement": cfg.with_engagements,
                        "handle_profile_batch_size": cfg.handle_profile_batch_size,
                        "tiktok_engagement_by": TiktokEngagementBuilder(
                            profiles=[]
                        ).post_source,
                    }
                )

            self.counters[q] = QualifiedCounter(
                recent_limit=self.config.recent_quality_window
            )
            await self.observer.notify_status(f"🔎 Searching {q}")
            profile: SocialProfile
            async for profile in spider.async_iterate():
                yield (q, profile)
            logger.warning(f"<<<<<<<<<<<    END {q}    >>>>>>>>>>")

    async def _search_influencers_mock(
        self, search_queries: List[str]
    ) -> List[Dict[str, Any]]:
        return [
            {
                "name": item["authorMeta"]["name"],
                "nickname": item["authorMeta"]["nickName"],
                "url": item["authorMeta"]["profileUrl"],
                "followers": item["authorMeta"]["fans"],
                "signature": item["authorMeta"]["signature"],
                "bio_link": item["authorMeta"]["bioLink"],
                "related_video_posted": item["webVideoUrl"],
                "related_video_description": item["text"],
                "related_video_hashtags": [h["name"] for h in item["hashtags"]],
            }
            for item in mock_apify_data
        ]

    async def _search_influencers_apify(
        self, search_queries: List[str]
    ) -> List[Dict[str, Any]]:
        apify_client = ApifyClientAsync(os.environ["APIFY_TOKEN"])
        run = await apify_client.actor("clockworks/free-tiktok-scraper").call(
            run_input={
                "searchQueries": search_queries,
                "resultsPerPage": self.config.entities_limit,
            }
        )
        if not run:
            raise RuntimeError("Apify returned empty result!")
        dataset = await apify_client.dataset(run["defaultDatasetId"]).list_items()
        logger.info(f"Dataset: {dataset.items}")

        return [
            {
                "name": item["authorMeta"]["name"],
                "nickname": item["authorMeta"]["nickName"],
                "url": item["authorMeta"]["profileUrl"],
                "followers": item["authorMeta"]["fans"],
                "signature": item["authorMeta"]["signature"],
                "bio_link": item["authorMeta"]["bioLink"],
                "related_video_posted": item["webVideoUrl"],
                "related_video_description": item["text"],
                "related_video_hashtags": [h["name"] for h in item["hashtags"]],
            }
            for item in dataset.items
        ]

    async def _fetch_generated_in_past_days(
        self, username: str, platform: INFLUENCER_PLATFORMS
    ) -> dict[str, dict]:
        entities: dict[str, dict] = dict()

        try:
            user = await User.objects.aget(username=username)
        except User.DoesNotExist:
            logger.warning(
                f'user of name "{username}" not found. Can not fetch his/her generated entities.'
            )
            return entities

        days = self.config.no_entities_generated_in_past_days
        since = datetime.now(tz=LOCAL_TZ) - timedelta(days=days)

        qs = AIFlow.objects.filter(
            user=user,
            is_deleted=False,
            created_at__gte=since,
            type="find_influencers",
            platform=platform,
        ).order_by("-created_at")

        async for flow in qs.all():
            for row in flow.data.get("rows", []):
                # TODO: store "handler" to "data.rows" and use it. Now simply use "url"
                url = row.get("url")
                if url:
                    entities[f"{flow.platform}|{url}"] = row
        return entities

    async def _async_fetch_engagements(  # noqa: C901
        self, profile: SocialProfile, username: str = ""
    ) -> None:
        """fetch social engagements data:
        1. recent posts
        """
        try:
            if not scraper_sdk.API_ENABLED:
                # now tiktok engagement is fetched in tiktok search spider
                if (
                    profile.platform == "tiktok"
                    and self.config.tiktok_engagements_with != "agent"
                ):
                    return

            self._engagement_pending += 1

            if scraper_sdk.API_ENABLED:
                async with self._platform_to_semaphore[profile.platform]:
                    profile.engagement = await scraper_sdk.async_build_engagements(
                        platform=profile.platform,
                        handle=profile.handle,
                        username=username,
                    )
            else:
                ebuilder = await scraper_sdk.InfluencerEngagementsBuilder.async_create(
                    platform=profile.platform,
                    influencers=[profile],
                    stop_event=self._stop_event,
                )

                async with self._platform_to_semaphore[profile.platform]:
                    await ebuilder.async_build()

            if profile.engagement and profile.engagement_rate is not None:
                logger.info(f"engagement built: {profile.engagement}")
            else:
                logger.warning(f"fail to build engagement for {profile}")

            await self._save_profile_posts(profile)

        finally:
            self._engagement_pending -= 1
            await self._async_on_engagement_fetched(profile)

    async def _async_on_engagement_fetched(self, profile: SocialProfile) -> None:
        attr_mapping = {
            "avg_view_count": "views",
            "avg_like_count": "likes",
            "avg_comment_count": "comments",
            "avg_favorite_count": "favorites",
            "avg_share_count": "shares",
        }

        _row_id = profile.key
        if _row_id == "":
            logger.warning("fail to get _row_id")
        else:
            stats = [
                f"{attr_mapping[attr]}: {getattr(profile, attr)}"
                for attr in [
                    "avg_view_count",
                    "avg_like_count",
                    "avg_comment_count",
                    "avg_favorite_count",
                    "avg_share_count",
                ]
                if getattr(profile, attr)
            ]
            _rate = (
                "-"
                if not profile.engagement_rate
                else f"{(profile.engagement_rate * 100):.2f}%"
                if profile.engagement_rate >= 0.0001
                else "<0.01"
            )
            _value = f"{_rate}\n" + ("" if _rate == "-" else "\n".join(stats))
            await self.observer.notify_add_cell(
                row_id=_row_id,
                column_id="engagement_rate",
                value=_value,
                sources=[],
                done=False,
            )

            # save profile
            await self.observer.notify_profile(
                profile=profile.to_dict(datetime_to_str=True)
            )

    def _calculate_relevance(self, assessment: dict[str, Any]) -> str:
        """calculate the relevance of profile by given criteria assessments

        We need to define the user's interesting level first, then we can define qualifications by level

        ## level by customer interesting
        - high: Customer EXTREMELY want this
        - medium: Customer MAYBE want this. Extra effort is required to DISCERN.
        - low: Customer MAY NOT want this. But it has some potential possibility to be qualified.
        - no: Customer should NOT want it.


        ## qualification by level
        - high: all criteria matched (all are "yes")
        - medium: at least half criteria matched and no "no" (50% are "yes", rest has NOT 'no')
        - low: at least 1 criterion matched
        - no: no criterion matched

        :param assessment:
        :return:
        """
        relevance = "low"
        if assessment:
            yes_matches = 0
            no_matches = 0
            for criterion in assessment.values():
                if not criterion:
                    continue
                match = criterion.get("match")
                if match == "yes":
                    yes_matches += 1
                elif match == "no":
                    no_matches += 1
            rate = yes_matches / len(assessment)
            if rate >= 0.9999999:
                relevance = "high"
            elif rate >= 0.4999999:
                relevance = "low" if no_matches > 0 else "medium"
            else:
                relevance = "no"
        return relevance

    # @timeit(logger.warning)
    @staticmethod
    async def _save_profile_posts(profile: SocialProfile) -> None:
        """save profile's posts to DB if enabled"""
        if not get_bool_env("SAVE_INSTAGRAM_POSTS"):
            return

        logger.info(f"save recent posts for {profile.handle}")
        if profile.platform == "instagram":
            await InfluencerFinderAgent.__save_instagram_posts(profile)
        # elif profile.platform == "tiktok":
        #     await InfluencerFinderAgent.__save_tiktok_posts(profile)
        # elif profile.platform == "youtube":
        #     await InfluencerFinderAgent.__save_youtube_posts(profile)

    @staticmethod
    async def __save_instagram_posts(profile: SocialProfile) -> None:
        try:
            # TODO: should update, but simply we delete it for debug.
            await InstagramPostModel.objects.filter(
                author_handle=profile.handle
            ).adelete()
            post_db_objs = []
            for post in profile.recent_posts:
                assert isinstance(post, InstagramPost)
                post_db_objs.append(InstagramPostModel.from_obj(post))
            await InstagramPostModel.objects.abulk_create(post_db_objs)
        except Exception as e:
            logger.warning(f"error save instagram posts. {e}")
