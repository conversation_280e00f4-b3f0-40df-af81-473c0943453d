import asyncio
import itertools
import json
import logging
from collections import deque
from typing import Any, Dict, List, Optional, Set, Tuple

from playwright.async_api import Browser, async_playwright
from pydantic import BaseModel

from agent.agents.base import Agent
from agent.config.agent import (
    EntityEnricherAgentConfig,
    HTMLScrapingAgentConfig,
    ResearchAgentConfig,
)
from agent.cost import LlmCostTracker
from agent.enum import CostCategory
from agent.functions import ALL_FUNCTIONS
from agent.llms import create_llm
from agent.prompts import create_prompt_builder
from agent.prompts.research import (
    DETERMINE_OFFICIAL_WEBSITE_RESPONSE_FORMAT,
    DETERMINE_OFFICIAL_WEBSITE_SYS_PROMPT,
    ENRICH_FROM_SCRAPED_WEBPAGES_RESPONSE_FORMAT,
    ENRICH_GEN_SEARCH_QUERIES_RESPONSE_FORMAT,
    ENRICH_GEN_SEARCH_QUERIES_SYS_PROMPT,
    GOO<PERSON>LE_SEARCH_RESPONSE_FORMAT,
    HTML_SCRAPING_SYS_PROMPT,
    INFER_SCHEMA_FEW_SHOTS,
    INFER_SCHEMA_RESPONSE_FORMAT,
    INFER_SCHEMA_SYS_PROMPT,
    INSPECT_WEBPAGE_RESPONSE_FORMAT,
    IS_DUPLICATE_ENTITY_RESPONSE_FORMAT,
    IS_DUPLICATE_ENTITY_SYS_PROMPT,
    enrich_from_scraped_webpages_sys_prompt,
    google_search_sys_prompt,
    inspect_webpage_sys_prompt,
)
from agent.utils import alocal_cache_on_key, record_function, timer
from common.utils.asyncioutil import with_concurrency_limit

logger = logging.getLogger(__name__)


def convert_evaluation_to_criteria(
    evaluation: List[Dict[str, Any]], criteria: List[str]
) -> Dict[str, Any]:
    # TODO(ruiwang): we should adjust FE format to remove this conversion @song
    eval_by_criteria = {
        item["criteria_name"]: {
            "match": item["match"],
            "reason": item["reason"],
        }
        for item in evaluation
    }
    # TODO(ruiwang): Fix this!
    if eval_by_criteria.keys() != set(criteria):
        logger.error(
            f"Evaluation keys {eval_by_criteria.keys()} do not match criteria {criteria}"
        )
    return {criterion: eval_by_criteria.get(criterion) for criterion in criteria}


class EntityLimitReachedException(Exception):
    pass


class Field(BaseModel):
    name: str  # the name should be unique and used as key
    description: Optional[str] = None
    editable: bool = True

    def __str__(self) -> str:
        return f"{self.name}({self.description})" if self.description else self.name


class NameField(Field):
    name: str = "name"
    editable: bool = False


class URLField(Field):
    name: str = "url"
    description: str = "The URL of the home page"
    editable: bool = False


class ShortDescField(Field):
    name: str = "short_description"
    description: str = "A short description or introduction of this entity"


class Schema(BaseModel):
    fields: List[Field]
    criteria: List[str] = list()

    @property
    def all_field_names(self) -> List[str]:
        return [f.name for f in self.fields]

    @classmethod
    def from_dict(cls, data: Dict) -> "Schema":
        fields = data.get("fields") or []

        criteria = data.get("criteria") or []

        return Schema(
            fields=[
                NameField(name="name", description="The name of the entity"),
                URLField(name="url", description="The URL of the home page or website"),
            ]
            + [Field(**f) for f in fields if f["name"] not in ["name", "url"]],
            criteria=criteria,
        )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "fields": [f.model_dump() for f in self.fields],
            "criteria": self.criteria,
        }

    def as_prompt_str(self) -> str:
        fields_str = ", ".join(str(f) for f in self.fields)
        criteria_str = (
            ", ".join(f"{i+1}. {c}" for i, c in enumerate(self.criteria))
            if self.criteria
            else ""
        )
        return f"Fields: {fields_str}\nCriteria: {criteria_str}"

    def as_tsv(self) -> str:
        return "\t".join(f.name for f in self.fields)

    def as_scraping_ql(
        self, add_on_scraping_attrs: List[str] = ["next_page_url"]
    ) -> str:
        def field_to_ql(field: Field) -> str:
            desc_str = f"({field.description})" if field.description else ""
            return f"{field.name}{desc_str}"

        attrs = [field_to_ql(field) for field in self.fields] + add_on_scraping_attrs
        attrs_str = "\n".join([f"    {a}" for a in attrs])

        return f"""
{{
{attrs_str}
}}
"""


class Entity(BaseModel):
    name: str
    url: Optional[str] = None
    # List of {"field_name": XX, "field_value": YY, "source_title", "source_url": ZZ}
    field_data: List[Dict[str, str]] = []

    @classmethod
    def from_dict(cls, schema: Schema, data: Dict) -> "Entity":
        field_data = []
        input_data_by_name = {f["field_name"]: f for f in data["field_data"]}
        all_input_fields = set(input_data_by_name.keys())
        for field in schema.fields:
            if field.name not in ["name", "url"]:
                if field.name not in all_input_fields:
                    logger.warning(f"Field data {data} missing for field {field.name}")
                    field_data.append(
                        {
                            "field_name": field.name,
                            "field_value": "",
                            "source_title": "",
                            "source_url": "",
                        }
                    )
                else:
                    d = input_data_by_name[field.name]
                    field_data.append(
                        {
                            "field_name": field.name,
                            "field_value": d["field_value"] if d["field_value"] else "",
                            "source_title": d.get("source_title", ""),
                            "source_url": d.get("source_url", ""),
                        }
                    )
        return Entity(
            name=data["name"],
            url=data["url"],
            field_data=field_data,
        )

    @classmethod
    def from_user_input_dict(cls, schema: Schema, data: Dict) -> "Entity":
        return Entity(
            name=data["name"],
            url=data["url"],
            field_data=[
                {
                    "field_name": field.name,
                    "field_value": str(data.get(field.name, "")),
                    "source_title": "User Input",
                    "source_url": "",
                }
                for field in schema.fields
                if field.name not in ["name", "url"]
            ],
        )

    def to_tsv(self) -> str:
        return "\t".join(
            [self.name, self.url or ""]
            + [
                v["field_value"].replace("\n", ", ") if v else ""
                for v in self.field_data
            ]
        )

    def all_fields_as_dict(self) -> Dict[str, Any]:
        field_dict = {
            "name": self.name,
            "url": self.url,
        }
        for field_data in self.field_data:
            field_dict[field_data["field_name"]] = field_data["field_value"]
        return field_dict

    @property
    def sources_by_field_name(self) -> Dict[str, List[Dict[str, str]]]:
        """NOTE: currently we only support one source per field."""
        return {
            field_data["field_name"]: [
                {
                    "source_title": field_data["source_title"],
                    "source_url": field_data["source_url"],
                }
            ]
            for field_data in self.field_data
        }


def entities_to_tsv(table: List[Entity]) -> str:
    return "\n".join(e.to_tsv() for e in table)


class Action(BaseModel):
    function_name: str
    parameters: Dict[str, Any]


# DEPRECATED
class HTMLScrapingAgent(Agent):
    """This agent scrapes a given webpage with given query language."""

    config: HTMLScrapingAgentConfig

    async def run(self, **kwargs: Any) -> Any:
        assert "html" in kwargs, "html is required"
        html: str = kwargs["html"]
        assert "ql" in kwargs, "Query language is required"
        ql: str = kwargs["ql"]

        prompt_msgs = create_prompt_builder(
            sys_prompt=HTML_SCRAPING_SYS_PROMPT,
            user_msg=f"HTML:\n{html}\n\nQuery:\n{ql}",
            llm_model_name=self.config.llm_model_name,
        ).build()
        ret = await create_llm(
            log_prefix="web_scraping",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            enforce_json_output=True,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        return json.loads(ret)


class EntityEnricherAgent(Agent):
    """This agent verify and enriches the given entity with additional information.
    Return enriched entity if it is valid, otherwise return None.
    """

    config: EntityEnricherAgentConfig
    task: str
    target_entity_definition: str
    task_schema: Schema
    browser: Optional[Browser] = None

    @record_function(key="determine_official_website")
    async def determine_official_website(self, entity: Entity) -> Optional[str]:
        """do some hack to determine official website"""
        # search google by this entity's name, url and official website
        res = await ALL_FUNCTIONS["google_search"].run(
            {
                "queries": [f"{entity.name}'s official website"],
                # "queries": [f"{entity.name} official website", entity.url or ""],
                "top_k": 7,
            },
            self.config,
            self.observer,
        )
        # determine the official website by asking LLM
        prompt_msgs = create_prompt_builder(
            sys_prompt=DETERMINE_OFFICIAL_WEBSITE_SYS_PROMPT,
            user_msg=f"Entity name: {entity.name}\n\nSearch results:\n{json.dumps(res)}",
            llm_model_name=self.config.llm_model_name,
        ).build()
        ret = await create_llm(
            log_prefix="determine_official_website",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=DETERMINE_OFFICIAL_WEBSITE_RESPONSE_FORMAT,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        return json.loads(ret)["official_website_url"]

    async def enrich_from_scraped_webpages_llm(
        self, entity: Entity, scraped_data: str
    ) -> Dict[str, Any]:
        """Extract relevant information from the scraped webpages using LLM."""
        prompt_msgs = create_prompt_builder(
            sys_prompt=enrich_from_scraped_webpages_sys_prompt(
                self.task,
                self.task_schema.as_prompt_str(),
            ),
            user_msg=f"Entity name: {entity.name}\n\nScraped data:\n{scraped_data}",
            llm_model_name=self.config.llm_model_name,
        ).build()
        ret = await create_llm(
            log_prefix="enrich_from_scraped_webpages",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=ENRICH_FROM_SCRAPED_WEBPAGES_RESPONSE_FORMAT,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        return json.loads(ret)["entity"]

    @timer(key="_search")
    async def _search(self, entity: Entity, search_queries: List[str]) -> List[str]:
        if entity.url:
            search_params = [
                {
                    "queries": [f"site:{entity.url}"],
                    "top_k": 2,
                },
                # TODO: fanout is too big. add these back after optimization
                {
                    "queries": search_queries,
                    "top_k": 2,
                },
                {
                    "queries": [f"site:{entity.url} contact"],
                    "top_k": 1,
                },
                # {
                #     "queries": [f"site:{entity.url} about"],
                #     "top_k": 1,
                # },
            ]
        else:
            search_params = [
                {
                    "queries": [f"{entity.name}"],
                    "top_k": 3,
                },
            ]
        res = await asyncio.gather(
            *[
                ALL_FUNCTIONS["google_search"].run(
                    params,
                    self.config,
                    self.observer,
                )
                for params in search_params
            ]
        )
        res = [r["url"] for r in list(itertools.chain(*res))]
        return list(set(res + [entity.url]))

    @timer(key="scrape")
    async def scrape(self, url: str) -> str:
        return await ALL_FUNCTIONS["inspect_webpage"].scrape(url, browser=self.browser)

    @timer(key="search_and_scrape")
    async def search_and_scrape(self, entity: Entity, search_queries: List[str]) -> str:
        """Search google and scrape the search result webpages."""
        await self.observer.notify_text_card(
            f"🔎 Searching google for {entity.name} ..."
        )
        urls_to_scrape = await self._search(entity, search_queries)
        scraped_data = await asyncio.gather(
            *[self.scrape(url) for url in urls_to_scrape]
        )

        # format scraped data by the url, followed by the scraped data
        return "\n\n".join(
            [
                f"URL: {url}\nScraped:\n{data}"
                for url, data in zip(urls_to_scrape, scraped_data)
            ]
        )

    async def _gen_search_queries(self, entity: Entity) -> List[str]:
        prompt_msgs = create_prompt_builder(
            sys_prompt=ENRICH_GEN_SEARCH_QUERIES_SYS_PROMPT,
            user_msg=f"Entity name: {entity.name}",
            llm_model_name=self.config.llm_model_name,
        ).build()
        ret = await create_llm(
            log_prefix="enrich_gen_search_queries",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=ENRICH_GEN_SEARCH_QUERIES_RESPONSE_FORMAT,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        # NOTE: currently we only output one query due to efficiency
        return [json.loads(ret)["query_to_search_google"]]

    @alocal_cache_on_key(
        key_func=lambda *args, **kwargs: (
            # both entity name and schema define an unique run of this agent
            kwargs["entity"].name,
            args[0].task_schema.all_field_names,
        )
    )
    @record_function(key="entity_enrich_run")
    @timer(key="entity_enrich_run")
    async def run(self, **kwargs: Any) -> Tuple[Entity, Dict[str, Any]]:
        assert "entity" in kwargs, "Entity is required"
        entity: Entity = kwargs["entity"]
        assert "browser" in kwargs, "Browser is required"
        self.browser = kwargs["browser"]

        logger.info(f"Enriching entity: {entity.name}")
        return await self._run_from_entity(entity)

    async def _run_from_entity(self, entity: Entity) -> Tuple[Entity, Dict[str, Any]]:
        url, search_queries = await asyncio.gather(
            self.determine_official_website(entity), self._gen_search_queries(entity)
        )
        if not url:
            return entity, {}

        entity.url = url
        logger.info(f"Official website determined: {url}")

        scraped_data = await self.search_and_scrape(entity, search_queries)
        try:
            await self.observer.notify_text_card(
                f"📖 Reading webpages for {entity.name}..."
            )
            res = await self.enrich_from_scraped_webpages_llm(entity, scraped_data)

            # TODO: validate field data before update
            entity.field_data = res["field_data"]
            logger.info(f"Entity enriched and will be added: {entity.name}")
            return entity, res["evaluation_to_criteria"]
        except (KeyError, json.decoder.JSONDecodeError) as e:
            logger.error(f"Error when updating entity: {e}", exc_info=True)

        return entity, {}

    async def _run_from_url(self, entity: Entity) -> Entity:
        """Scrape only from the entity's url."""
        await self.observer.notify_text_card(f"📖 Reading {entity.url} ...")
        scraped_data = await self.scrape(entity.url)
        res = await self.enrich_from_scraped_webpages_llm(entity, scraped_data)
        entity.field_data = res["field_data"]
        return entity


class SchemaInferrerAgent(Agent):
    config: ResearchAgentConfig

    async def run(self, **kwargs: Any) -> Tuple[Schema, str, str]:
        assert "task" in kwargs, "Task is required"
        task: str = kwargs["task"]

        prompt_msgs = create_prompt_builder(
            sys_prompt=INFER_SCHEMA_SYS_PROMPT,
            user_msg=task,
            llm_model_name=self.config.llm_model_name,
            few_shots_msgs=INFER_SCHEMA_FEW_SHOTS,
        ).build()
        ret = await create_llm(
            log_prefix="infer_schema",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=INFER_SCHEMA_RESPONSE_FORMAT,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        data = json.loads(ret)
        self.observer.log_states(
            {
                "schema": Schema.from_dict(data).model_dump(),
                "task_short_name": data["task_short_name"],
                "target_entity_definition": data["target_entity_definition"],
                "criteria": data["criteria"],
            }
        )
        return (
            Schema.from_dict(data),
            data["task_short_name"],
            data["target_entity_definition"],
        )


class ResearchAgentBase(Agent):
    """This agent is used to do some research on a given task and generate a table of
    entities with given schema.
    """

    config: ResearchAgentConfig
    task: str
    final_table: List[Entity] = []
    task_schema: Schema = Schema(fields=[])
    target_entity_definition: str = ""
    enriched_entity_count: int = 0


class ResearchAgent(ResearchAgentBase):
    """This agent uses google search and scrape to find entities and enrich them."""

    entity_enricher_agent: Optional[EntityEnricherAgent] = None
    browser: Optional[Browser] = None
    row_id_by_entity_name: Dict[str, int] = {}
    urls_inspected: Set[str] = set()

    @timer(key="scrape")
    async def scrape(self, url: str) -> str:
        return await ALL_FUNCTIONS["inspect_webpage"].scrape(url, browser=self.browser)

    @record_function(key="run")
    @timer(key="call_google_search_agent")
    async def call_google_search_agent(
        self, search_results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        prompt_msgs = create_prompt_builder(
            sys_prompt=google_search_sys_prompt(
                self.task,
                self.target_entity_definition,
                self.task_schema.as_prompt_str(),
            ),
            user_msg=json.dumps(search_results),
            llm_model_name=self.config.llm_model_name,
        ).build()
        ret = await create_llm(
            log_prefix="google_search",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=GOOGLE_SEARCH_RESPONSE_FORMAT,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        logger.info(f"Google search agent output: {ret}")
        out = json.loads(ret)
        return out

    @record_function(key="inspect_webpage_llm")
    async def inspect_webpage_llm(self, url: str, scraped_data: str) -> Dict[str, Any]:
        prompt_msgs = create_prompt_builder(
            sys_prompt=inspect_webpage_sys_prompt(
                self.task,
                self.target_entity_definition,
                self.task_schema.as_prompt_str(),
            ),
            user_msg=f"URL of the webpage to inspect: {url}\n\nScraped data:\n{scraped_data}",
            llm_model_name=self.config.llm_model_name,
        ).build()
        ret = await create_llm(
            log_prefix="inspect_webpage",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=INSPECT_WEBPAGE_RESPONSE_FORMAT,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        logger.info(f"Inspect webpage {url} output: {ret}")
        return json.loads(ret)

    @alocal_cache_on_key(key_func=lambda *args, **kwargs: args[1])  # url
    @record_function(key="process_url")
    @timer(key="process_url")
    async def process_url(self, url: str) -> Dict[str, Any]:
        try:
            await self.observer.notify_text_card(f"📖 Reading: {url} ...")
            # Scrape the webpage
            scraped_data = await self.scrape(url)
            logger.info(f"Inspecting webpage: {url}")

            # Analyze the scraped data using LLM
            if not scraped_data:
                logger.warning(f"No scraped data for URL: {url}")
                return {}
            res = await self.inspect_webpage_llm(url, str(scraped_data))
            self.urls_inspected.add(url)
            return res
        except Exception as e:
            logger.error(f"Error processing URL {url}: {e}")
            return {}

    @timer(key="is_duplicate_entity")
    async def _is_duplicate_entity(self, entity_to_add: Entity) -> bool:
        if entity_to_add.name in self.row_id_by_entity_name:
            return True

        cur_entity_names = [e.name for e in self.final_table]
        prompt_msgs = create_prompt_builder(
            sys_prompt=IS_DUPLICATE_ENTITY_SYS_PROMPT(self.task),
            user_msg=f"Target Entity Name: {entity_to_add.name}\n\nCurrent table:\n{cur_entity_names}",
            llm_model_name=self.config.llm_model_name,
        ).build()
        ret = await create_llm(
            log_prefix="is_duplicate_entity",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=IS_DUPLICATE_ENTITY_RESPONSE_FORMAT,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        is_duplicate = json.loads(ret)["is_duplicate"]
        if isinstance(is_duplicate, bool):
            return is_duplicate
        elif isinstance(is_duplicate, str):
            if is_duplicate.lower() == "true":
                return True

        return False

    async def _enrich_and_add(self, task: str, schema: Schema, entity: Entity) -> None:
        if await self._is_duplicate_entity(entity):
            logger.warning(f"Duplicate entity: {entity}, skipping")
            return

        row_id = len(self.final_table)
        self.row_id_by_entity_name[entity.name] = row_id
        self.final_table.append(entity)
        for column_id, value in entity.all_fields_as_dict().items():
            await self.observer.notify_add_cell(
                row_id=str(row_id),
                column_id=column_id,
                value=value if column_id == "name" else "Researching...",
                done=False,
            )
        enriched_entity, evaluation_to_criteria = await self.entity_enricher_agent.run(  # type: ignore
            task=task, schema=schema, entity=entity, browser=self.browser
        )
        self.final_table[row_id] = enriched_entity
        for column_id, value in enriched_entity.all_fields_as_dict().items():
            await self.observer.notify_add_cell(
                row_id=str(row_id),
                column_id=column_id,
                value=value,
                sources=enriched_entity.sources_by_field_name.get(column_id, []),
                done=False,
            )
        self.enriched_entity_count += 1
        await self.observer.notify_evaluation(
            row_id=str(row_id),
            evaluation={
                "row_id": row_id,
                "row_name": entity.name,
                "criteria": convert_evaluation_to_criteria(
                    evaluation_to_criteria, self.task_schema.criteria
                ),
            },
        )

        await self.observer.notify_progress(
            self.config.entities_limit, self.enriched_entity_count
        )
        if self.enriched_entity_count >= self.config.entities_limit:
            await self.observer.notify_text_card(
                "✅ Entity limit reached, stopping research..."
            )
            raise EntityLimitReachedException()

    async def analyze_search_result(  # noqa: C901
        self, analysis_results: List[Dict[str, Any]]
    ) -> None:
        urls_to_inspect: deque[str] = deque([])
        entities_to_enrich: List[Entity] = []
        for result in analysis_results:
            type_analyzed = result["type_of_search_result_item"]
            if type_analyzed == "target_entity":
                entities_to_enrich.append(
                    Entity.from_dict(self.task_schema, result["entity_to_add"])
                )
            elif type_analyzed == "useful_website":
                urls_to_inspect.append(result["url_to_inspect"])
                logger.info(f"Added URL to inspect: {result['url_to_inspect']}")
            elif type_analyzed == "not_useful":
                continue
            else:
                raise ValueError(f"Unknown type of search result item: {type_analyzed}")

        entities_limit = self.config.entities_limit

        while (
            urls_to_inspect or entities_to_enrich
        ) and self.enriched_entity_count < entities_limit:
            # deduplicate entities based on name first
            entities_to_enrich = list({e.name: e for e in entities_to_enrich}.values())

            # prepare only enough entities to enrich to reach the limit
            entities_to_enrich = entities_to_enrich[
                : entities_limit - self.enriched_entity_count
            ]

            # Prepare a batch of URLs to process
            current_batch = []
            for _ in range(
                min(self.config.batch_size_urls_to_process, len(urls_to_inspect))
            ):
                url = urls_to_inspect.popleft()
                if url not in self.urls_inspected:
                    current_batch.append(url)
                else:
                    logger.warning(f"URL already inspected: {url}")

            await self.observer.notify_text_card(
                "🔎 Found some potential targets, let me do some research ..."
            )
            tasks = [
                asyncio.create_task(
                    self._enrich_and_add(self.task, self.task_schema, entity)
                )
                for entity in entities_to_enrich
            ] + [asyncio.create_task(self.process_url(url)) for url in current_batch]
            try:
                results = await asyncio.gather(
                    *with_concurrency_limit(
                        tasks,
                        limit=8,
                    ),
                )
            except EntityLimitReachedException:
                logger.info("Entity limit reached, cancelling all other tasks ...")
                for task in tasks:
                    if not task.done():
                        task.cancel()
                # wait for tasks to be cancelled
                await asyncio.gather(*tasks, return_exceptions=True)
                return

            # Collect entities and new URLs from the results
            all_new_entities = []
            all_new_urls = []
            for res in results:
                if res:
                    all_new_entities.extend(res.get("entities_found", []))
                    all_new_urls.extend(res.get("urls_to_inspect_next", []))

            entities_to_enrich = [
                Entity.from_dict(self.task_schema, e) for e in all_new_entities
            ]

            # Check if we've reached the entities limit
            if self.enriched_entity_count >= entities_limit:
                break

            # Add new URLs to the queue
            urls_to_inspect.extend(all_new_urls)

        logger.info(f"## Number of entities: {self.enriched_entity_count}")

    @timer(key="research_from_scratch")
    async def _research_from_scratch(self) -> None:
        google_search_results: List[Dict[str, Any]] = []
        num_search_loops = 0

        await self.observer.notify_progress(self.config.entities_limit, 0)

        while (
            self.enriched_entity_count < self.config.entities_limit
            and num_search_loops < self.config.max_search_loops
        ):
            if not google_search_results:
                await self.observer.notify_text_card("💡 Let me search google.")
                res = await self.call_google_search_agent([])
                queries_str = ", ".join(res["queries_to_search_google"])
                await self.observer.notify_text_card(
                    f"🔎 Searching google with queries: {queries_str}"
                )
                google_search_results = await ALL_FUNCTIONS["google_search"].run(
                    {
                        "queries": res["queries_to_search_google"],
                        "top_k": 50,
                    },
                    self.config,
                    self.observer,
                )

            # batch loop through google search agent
            batch_size = 10
            for batch in range(0, len(google_search_results), batch_size):
                res = await self.call_google_search_agent(
                    google_search_results[batch : batch + batch_size]
                )
                await self.observer.notify_text_card(
                    f'💡 {res["thoughts_about_next_action"]}'
                )
                await self.analyze_search_result(res["analysis_of_search_results"])
                if self.enriched_entity_count >= self.config.entities_limit:
                    return
                if res["next_action"] == "generate_new_queries":
                    google_search_results = await ALL_FUNCTIONS["google_search"].run(
                        {
                            "queries": res["queries_to_search_google"],
                            "top_k": 50,
                        },
                        self.config,
                        self.observer,
                    )
                    break
                elif res["next_action"] == "next_page":
                    continue
                else:
                    raise ValueError(f"Unknown next action: {res['next_action']}")
            google_search_results = []
            num_search_loops += 1

    async def _enrich_existing_single(
        self, task: str, schema: Schema, entity: Entity
    ) -> None:
        row_id = self.row_id_by_entity_name[entity.name]
        for column_id, value in entity.all_fields_as_dict().items():
            await self.observer.notify_add_cell(
                row_id=str(row_id),
                column_id=column_id,
                value=value if column_id == "name" else "Researching...",
                done=False,
            )
        enriched_entity, evaluation_to_criteria = await self.entity_enricher_agent.run(  # type: ignore
            task=task, schema=schema, entity=entity, browser=self.browser
        )
        self.final_table[row_id] = enriched_entity
        for column_id, value in enriched_entity.all_fields_as_dict().items():
            await self.observer.notify_add_cell(
                row_id=str(row_id),
                column_id=column_id,
                sources=enriched_entity.sources_by_field_name.get(column_id, []),
                value=value,
                done=False,
            )
        self.enriched_entity_count += 1
        await self.observer.notify_evaluation(
            row_id=str(row_id),
            evaluation={
                "row_id": row_id,
                "row_name": entity.name,
                "criteria": convert_evaluation_to_criteria(
                    evaluation_to_criteria, self.task_schema.criteria
                ),
            },
        )
        await self.observer.notify_progress(
            self.config.entities_limit, self.enriched_entity_count
        )

    @timer(key="enrich_existing")
    async def _enrich_existing(self, existing_data: Dict[str, Any]) -> List[Entity]:
        # validate and read existing data into self.final_table and self.row_id_by_entity_name
        for i, row in enumerate(existing_data["rows"]):
            row_id = row.pop("id")
            assert row_id == str(i)

            # TODO(ruiwang|2024.12.5): fix this hack by using a better way to identify entities
            # row["name"] = row["url"] if row["name"] == "" else row["name"]

            self.row_id_by_entity_name[row["name"]] = i
            self.final_table.append(Entity.from_user_input_dict(self.task_schema, row))

        # now the limit should be the number of existing data
        self.config.entities_limit = len(self.final_table)
        await self.observer.notify_progress(self.config.entities_limit, 0)

        # enrich existing data in batch concurrently
        await self.observer.notify_text_card("💡 Let me start by searching google.")
        await asyncio.gather(
            *with_concurrency_limit(
                [
                    self._enrich_existing_single(self.task, self.task_schema, entity)
                    for entity in self.final_table
                ],
                limit=8,
            )
        )

        return self.final_table

    async def run(self, **kwargs: Any) -> List[Entity]:
        # NOTE(2024-10-21): Try using separate agents for google search and inspect webpage
        assert "username" in kwargs, "Username is required"
        assert self.task
        assert self.task_schema.fields

        self.entity_enricher_agent: EntityEnricherAgent = EntityEnricherAgent(
            config=EntityEnricherAgentConfig(),
            task=self.task,
            target_entity_definition=self.target_entity_definition,
            task_schema=self.task_schema,
            observer=self.observer,
            streaming=False,
        )

        async with async_playwright() as playwright:
            # Try Firefox instead of Chromium due to ERR_HTTP2_PROTOCOL_ERROR
            self.browser = await playwright.firefox.launch(headless=True)
            try:
                if self.config.method == "research_from_scratch":
                    await self._research_from_scratch()
                elif self.config.method == "enrich_existing":
                    await self._enrich_existing(kwargs["existing_data"])
                else:
                    raise ValueError(f"Unknown method: {self.config.method}")
            except Exception:
                logger.error(
                    f"Error running research agent: {self.config.method}", exc_info=True
                )
            except asyncio.CancelledError:
                logger.info("Research task was cancelled")
                raise
            finally:
                await self.browser.close()
                # Use latency per entity as the core perf metric
                overall_latency = self.observer.perf[f"latency_{self.config.method}"][0]
                self.observer.log_perf(
                    {
                        "latency_per_entity": (
                            overall_latency / self.enriched_entity_count
                            if self.enriched_entity_count > 0
                            else 0
                        ),
                    }
                )
                await self.log_states(
                    inputs={"username": kwargs["username"], "task": self.task},
                    outputs={"table": [e.model_dump() for e in self.final_table]},
                )

        return self.final_table
