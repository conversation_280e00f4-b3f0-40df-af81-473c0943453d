"""ARCHIVED"""
# import asyncio
# import itertools
# from typing import Optional, Any, List
#
# from agent.agents.base import ChatAgent
# from agent.agents.planner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>tcher<PERSON><PERSON>
# from agent.agents.visualizer import Visualizer
# from agent.config.agent import PlanRetrievalAgentConfig
# from agent.constants import (
#     PLANNING_PARSING_ERROR_MSG,
# )
# from agent.cost import LlmCostTracker
# from agent.enum import PlanningTypeEnum, CostCategory
# from agent.exceptions import OutputParsingError
# from agent.llms import FakeLLM, create_llm
# from agent.llms.fake import FAKE_RESPONSES_BY_EXAMPLE_QUERY
# from agent.llms.output_handlers import Citation<PERSON><PERSON><PERSON><PERSON>Handler
# from agent.memory import ShortTermMemory
# from agent.models import FetchedSource
# from agent.prompts import create_prompt_builder
# from agent.prompts.default import (
#     DEFAULT_ANSWER_SYS_PROMPT,
#     DEFAULT_ANSWER_FEW_SHOTS_MSGS,
#     POST_USER_MSG_SYS_PROMPT,
# )
# from agent.ranker import Default<PERSON>anker
# from agent.utils import timer
# from biz.constants import SUPPORTED_UNIQUE_TICKER_SET
# from biz.search.snippet import SnippetSearcher
# from biz.structs import DocChunk
# from common.django.model import User
#
#
# class PlanRetrievalAgent(ChatAgent):
#     """This Agent plan, fetch data, then answer"""
#
#     planner: FetcherPlanner = FetcherPlanner()
#     ranker: DefaultRanker = DefaultRanker()
#     config: PlanRetrievalAgentConfig
#
#     @timer(key="plan")
#     async def plan(
#         self, msg: str, memory: Optional[ShortTermMemory], **kwargs: Any
#     ) -> FetcherPlan:
#         as_of_date = kwargs.get("as_of_date")
#         plan = await self.planner.plan(
#             self.config,
#             self.observer,
#             self.chat_turn,
#             msg,
#             memory,
#             as_of_date=as_of_date,
#         )
#         self.observer.log_states({"plan": plan.as_logging_dict()})
#         return plan
#
#     async def maybe_handle_out_of_scope(self, plan: FetcherPlan) -> Optional[str]:
#         """Give user a hint if the companies they are asking for are not in SP500
#         Return True if we already handled out of scope case, False otherwise"""
#         if not plan.company_tickers:
#             return None
#         if "ALL" in plan.company_tickers:
#             return None
#         if all(
#             ticker not in SUPPORTED_UNIQUE_TICKER_SET for ticker in plan.company_tickers
#         ):
#             return await FakeLLM(
#                 log_prefix="not_supported_msg",
#                 observer=self.observer,
#                 fake_resp="Sorry but we only support companies that are in S&P 500 right now and seems like the companies you are asking are not in this scope. Try asking for companies in S&P 500. However if you think I made a mistake feel free to correct me.",
#             ).chat_streaming([])
#         return None
#
#     @timer(key="run")
#     async def _run(
#         self, user: User, msg: str, intent: str, memory: ShortTermMemory, **kwargs: Any
#     ) -> str:
#         await self.observer.notify_planning(
#             f"[{PlanningTypeEnum.SEARCH.value}] Thinking hard and doing some planning ..."
#         )
#         try:
#             plan: FetcherPlan = await self.plan(msg, memory, **kwargs)
#             handled = await self.maybe_handle_out_of_scope(plan)
#             if handled:
#                 return handled
#
#             await self.observer.notify_planning(
#                 f"[{PlanningTypeEnum.THOUGHTS.value}] My thoughts: {plan.thoughts}"
#             )
#         except OutputParsingError as e:
#             self.observer.log_error(e)
#             await self.observer.notify_streaming(PLANNING_PARSING_ERROR_MSG, done=True)
#             return PLANNING_PARSING_ERROR_MSG
#
#         fetched_data = await self.fetch_data(plan)
#
#         self.observer.log_retrieval(
#             {"fetched_data": [d.as_logging_dict() for d in fetched_data]}
#         )
#         if not fetched_data:
#             return await self.send_fallback_msg(plan)
#
#         ranked_data = self.ranker.rank(fetched_data, self.config.answer_token_limit)
#         self.observer.log_retrieval(
#             {"ranked_data": [d.as_logging_dict() for d in ranked_data]}
#         )
#         await self.observer.notify_sources(
#             [
#                 FetchedSource(
#                     chat_turn=self.chat_turn,
#                     number=i,
#                     title=d.displayed_title,
#                     source=d.key,
#                 )
#                 for i, d in enumerate(ranked_data)
#             ]
#         )
#
#         await self.observer.notify_typing()
#         resp = await self.answer(self.config, msg, memory, ranked_data=ranked_data)
#
#         if self.config.visualize:
#             visualizer = Visualizer(
#                 observer=self.observer,
#                 chat_turn=self.chat_turn,
#                 config=self.config,
#             )
#             await visualizer.visualize(msg, resp)
#         return resp
#
#     @timer(key="fetch_data")
#     async def fetch_data(self, fetcher_plan: FetcherPlan) -> List[DocChunk]:
#         tasks = [
#             func.run(params, self.config, self.observer)
#             for func, params in fetcher_plan.functions_with_params
#         ]
#         all_data = await asyncio.gather(*tasks)
#         return list(itertools.chain.from_iterable(all_data))
#
#     @timer(key="answer")
#     async def answer(
#         self,
#         config: PlanRetrievalAgentConfig,
#         msg: str,
#         memory: Optional[ShortTermMemory],
#         ranked_data: List[DocChunk],
#     ) -> str:
#         prompt_msgs = create_prompt_builder(
#             sys_prompt=DEFAULT_ANSWER_SYS_PROMPT,
#             user_msg=msg,
#             llm_model_name=self.config.answer_model_name,
#             # TODO: probably make this configurable
#             few_shots_msgs=DEFAULT_ANSWER_FEW_SHOTS_MSGS,
#             memory=memory,
#             fetched_data=ranked_data,
#             post_user_msg_sys_prompt=POST_USER_MSG_SYS_PROMPT,
#         ).build()
#         output_handler = CitationLLMOutputHandler(
#             chat_turn=self.chat_turn, searcher=SnippetSearcher()
#         )
#         params = {
#             "log_prefix": "answer",
#             "observer": self.observer,
#             "cost_tracker": LlmCostTracker(
#                 cost_category=CostCategory.LLM_CHAT_ANSWER, chat_turn=self.chat_turn
#             ),
#             "output_handler": output_handler,
#             "llm_model_name": config.answer_model_name,
#             "max_tokens": config.answer_max_complete_tokens,
#             "verbose": True,
#         }
#         if self.streaming:
#             if (
#                 self.config.fake_example_queries
#                 and msg in FAKE_RESPONSES_BY_EXAMPLE_QUERY
#             ):
#                 return await FakeLLM(
#                     log_prefix="fake_answer",
#                     observer=self.observer,
#                     output_handler=output_handler,
#                     fake_resp=FAKE_RESPONSES_BY_EXAMPLE_QUERY[msg],
#                 ).chat_streaming(prompt_msgs)
#
#             return await create_llm(**params).chat_streaming(prompt_msgs)  # type: ignore
#         else:
#             return await create_llm(**params).chat_complete(prompt_msgs)  # type: ignore
#
#     async def send_fallback_msg(self, plan: FetcherPlan) -> str:
#         msg = ""
#         plan_run_msg = plan.as_human_readable_run_msg()
#         if not plan_run_msg:
#             msg += "There doesn't seem to be relevant data for your question.\n"
#             msg += "You could try expanding the document types or give me more specific instructions."
#         else:
#             msg += "What I have done:\n"
#             msg += plan_run_msg
#             msg += "\nHowever, no relevant data available for your query.\n"
#             msg += "\nSuggestions:\n"
#             msg += " - Correct me if I made a mistake or you can include more document types"
#         await self.observer.notify_streaming(msg, done=True)
#         return msg
#
#
# class FetchNumberRetrievalAgent(PlanRetrievalAgent):
#     pass
