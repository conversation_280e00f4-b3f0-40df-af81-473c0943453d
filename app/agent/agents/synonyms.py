import json
from typing import List

from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS

from agent.config.agent import SynonymsExtractorAgentConfig
from agent.cost import LlmCostTracker
from agent.enum import CostCategory
from agent.llms import create_llm
from agent.prompts import create_prompt_builder
from agent.prompts.synonyms import (
    EXTRACT_SYNONYMS_FEW_SHOTS,
    EXTRACT_SYNONYMS_SYS_PROMPT,
)


class SynonymsExtractorAgent(LLMAgentConfig):
    """This agent extracts synonyms from a given query"""

    config: SynonymsExtractorAgentConfig

    async def run(self, query: str) -> List[str]:
        """Extract synonyms from a given query"""
        prompt_msgs = create_prompt_builder(
            sys_prompt=EXTRACT_SYNONYMS_SYS_PROMPT,
            user_msg=query,
            llm_model_name=self.config.llm_model_name,
            few_shots_msgs=EXTRACT_SYNONYMS_FEW_SHOTS,
        ).build()
        params = {
            "log_prefix": "extract_synonyms",
            "observer": self.observer,
            "cost_tracker": LlmCostTracker(
                cost_category=CostCategory.LLM_CHAT_CONTEXT,
            ),
            "llm_model_name": self.config.llm_model_name,
            "max_tokens": self.config.max_tokens,
            "enforce_json_output": True,
        }
        llm_output = await create_llm(**params).chat_complete(prompt_msgs)  # type: ignore
        return json.loads(llm_output)["all_synonyms"]

    def extract_n_grams(self, query: str, n: int = 3) -> List[str]:
        """Extract all the 1-gram, 2-gram and 3-grams from a given query"""
        # remove any characters that are not alphanumeric or space
        query = "".join([c for c in query if c.isalnum() or c.isspace()]).strip()
        # remove stop words
        words = [
            word for word in query.split() if word.lower() not in ENGLISH_STOP_WORDS
        ]
        n_grams = []
        for i in range(1, n + 1):
            n_grams.extend([" ".join(x) for x in zip(*[words[j:] for j in range(i)])])
        return n_grams
