import random
from asyncio import sleep
from datetime import datetime
from typing import Any, ClassVar, Optional

import dateparser
from asgiref.sync import async_to_sync

from agent.agents.base import ChatAgent
from agent.config.agent import FakeAgentConfig
from agent.enum import ServerOperationEnum
from agent.llms import FakeLLM
from agent.memory import ShortTermMemory
from agent.models import FetchedSource, Citation, RelevantSnippet, Chart
from biz import datasdk
from common.django_ext.model import User
from common.utils.mathutil import fexp

random.seed(datetime.utcnow().timestamp())


class FakeAgent(ChatAgent):
    config: FakeAgentConfig

    async def _run(
        self,
        user: User,
        msg: str,
        intent: str,
        memory: Optional[ShortTermMemory] = None,
        **kwargs: Any,
    ) -> str:
        cfg = self.config
        ob = self.observer
        ex_dict = {"chat_turn": self.chat_turn, "id": None}

        for plan in cfg.resp_plans:
            await ob.notify_planning(plan)
            await self.sleep_random(3)

        if cfg.resp_sources:
            sources = [FetchedSource(**(s | ex_dict)) for s in cfg.resp_sources]
            await ob.notify_sources(sources)
        await self.sleep_random(0.5)

        await self.observer.notify_typing()
        ret = await FakeLLM(
            log_prefix="fake_agent",
            observer=self.observer,
            fake_resp=cfg.resp_answer,
            streaming_token_delay_sec=0.2,
        ).chat_streaming([])

        if cfg.resp_citations:
            for c in cfg.resp_citations:
                citation = Citation(**(c | ex_dict))
                await ob.notify_operation(ServerOperationEnum.PREPARING_CITATION)
                await self.sleep_random(5)
                await ob.notify_citation(citation)
                await ob.notify_operation(ServerOperationEnum.CITATION_COMPLETED)

        if cfg.resp_relevant_snippets:
            for r in cfg.resp_relevant_snippets:
                relevant = RelevantSnippet(**(r | ex_dict))
                await ob.notify_relevant_snippet(relevant)
                await self.sleep_random(1)

        if cfg.resp_charts:
            await self.sleep_random(3)
            await ob.notify_charts([Chart(**(c | ex_dict)) for c in cfg.resp_charts])

        return ret

    @staticmethod
    async def sleep_random(high: float | int) -> None:
        exponent = fexp(high)
        low = high / (10**exponent) - 1
        low = 10**exponent * low
        d = random.randrange(1, 10) / 10 + low
        await sleep(d)

    # --- temporarily keep. code to generate fake data. TODO: delete if not required ---
    @staticmethod
    def load_stock_price(ticker: str, min_ts: str, max_ts: str) -> list[dict]:
        # leave me. this is used to generate fake data
        price_data = async_to_sync(datasdk.fmapi.fetch_historical_stock_price)(
            ticker,
            min_ts=dateparser.parse(min_ts),  # type: ignore
            max_ts=dateparser.parse(max_ts),  # type: ignore
            type="close",
        )
        return price_data

    @staticmethod
    def load_events(events_str: str) -> list[dict]:
        events = []
        for s in events_str.split("\n"):
            s = s.strip()
            if not s:
                continue
            num, s = s.split(".", 1)
            date_str, s = s.split(":", 1)
            dt = dateparser.parse(date_str).strftime("%Y-%m-%d")  # type: ignore
            event = {"id": int(num), "source": "", "text": s, "name": dt}
            events.append(event)
        return events

    evens_str1: ClassVar = """1. July 18, 2019: Microsoft settles U.S. antitrust charges over software developer access, agreeing to make changes to its practices and policies.
    2. October 7, 2019: The U.S. Supreme Court declines to hear Microsoft's appeal in a class-action lawsuit alleging anticompetitive practices, allowing the case to proceed.
    3. June 12, 2020: European antitrust regulators open an investigation into Microsoft's proposed acquisition of ZeniMax Media, the parent company of game publisher Bethesda Softworks.
    4. July 31, 2020: The U.S. House Judiciary Committee questions Microsoft CEO Satya Nadella about the company's business practices as part of a broader antitrust probe into big tech companies.
    5. March 9, 2021: Microsoft is hit with an antitrust complaint in the EU over its Teams integration with Office, filed by workspace messaging app Slack.
    6. March 21, 2021: The European Commission clears Microsoft's acquisition of ZeniMax Media, finding no antitrust concerns after its investigation.
    7. June 24, 2021: A U.S. judge grants class-action status to the antitrust lawsuit against Microsoft that the Supreme Court had declined to hear in 2019.
    8. November 16, 2021: The UK's Competition and Markets Authority orders a probe into Microsoft's $16 billion acquisition of AI company Nuance Communications over antitrust concerns.
    9. December 6, 2021: U.S. antitrust regulators unconditionally approve Microsoft's acquisition of Nuance after their review found no significant concerns.
    10. January 18, 2022: Microsoft announces plans to acquire video game giant Activision Blizzard for $68.7 billion, inviting intense antitrust scrutiny from regulators worldwide.
    """

    evens_str2: ClassVar = """
1. March 14, 2023 (-5.8%):
- Tesla investor day failed to impress analysts, lacking specifics on new models and financial targets
- Concerns about slowing demand and increased competition in the EV market
1. January 3, 2023 (-12.2%):
- Tesla reported lower-than-expected Q4 2022 vehicle deliveries, citing supply chain issues and COVID-related challenges
- Fears of slowing demand and increased competition, particularly in China
1. December 27, 2022 (-11.4%):
- Tesla extended its holiday production shutdown at its Shanghai factory, raising concerns about weak demand
- Elon Musk's controversial tweets and the distraction of his Twitter acquisition weighed on investor sentiment
1. November 9, 2022 (-7.2%):
- Elon Musk sold nearly $4 billion worth of Tesla shares to fund his Twitter acquisition
- Concerns about Musk's divided attention and the potential impact on Tesla's operations and brand
1. September 23, 2022 (-5.6%):
- Tesla's AI Day event failed to impress investors, lacking groundbreaking revelations
- Concerns about the feasibility and timeline of Tesla's autonomous driving plans
1. May 24, 2022 (-7.0%):
- Market-wide selloff driven by concerns about inflation, interest rates, and a potential recession
- Growth stocks like Tesla were particularly hard-hit as investors rotated into more defensive sectors
1. April 26, 2022 (-12.2%):
- Tesla's Q1 2022 earnings report showed strong growth but missed analysts' high expectations
- Concerns about production ramp-up challenges at new factories and the impact of COVID lockdowns in China
1. February 24, 2022 (-7.5%):
- Russia's invasion of Ukraine sparked market-wide selloff and concerns about supply chain disruptions
- Tesla's high valuation and reliance on global supply chains made it vulnerable to geopolitical risks
"""


# NOTE(Rui|2024/05/06): This is for MVP2.0 user testing. Disable for now.
ALL_FAKE_AGENT_CONFIGS = {}  # type: ignore
# ALL_FAKE_AGENT_CONFIGS = {
#     v.user_msg.strip().lower(): v
#     for k, v in FakeAgentConfig.load_all(group="fakes").items()
# }
# print("-" * 80)
# print("following are fake queries:")
# for fake_query in ALL_FAKE_AGENT_CONFIGS.keys():
#     print("-", fake_query)
# print("-" * 80)
