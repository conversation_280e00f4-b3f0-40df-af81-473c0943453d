import asyncio
import json
from abc import ABC
from datetime import datetime, timedelta, UTC
from logging import getLogger
from typing import Any, Dict, List, Optional

from django.http import StreamingHttpResponse
from pydantic import BaseModel

from agent.agents.base import ChatAgent
from agent.agents.synonyms import SynonymsExtractorAgent
from agent.config.agent import (
    SearchAgentConfig,
    SearchDocViewAgentConfig,
    SearchWithPlanAgentConfig,
    SynonymsExtractorAgentConfig,
)
from agent.constants import SEARCH_QUERY_UNDERSTANDING_ERROR_MSG
from agent.cost import LlmCostTracker
from agent.enum import CostCategory, PlanningTypeEnum, MessageSource
from agent.functions.search_across_docs import Search<PERSON>crossDocsFunction
from agent.functions.search_by_doc_keys import SearchByDocKeysFunction
from agent.llms import create_llm
from agent.llms.output_handlers import HttpLLMOutputHandler
from agent.models import <PERSON><PERSON>, Chat<PERSON>urn, RelevantSnippet
from agent.observer import Observer, DbNotifier
from agent.prompts import create_prompt_builder
from agent.relevance import DocValueModel, ScoredChunk
from agent.utils import timer
from biz import datasdk
from biz.enums import DocTypeEnum
from biz.search.keyword import highlight_synonyms
from biz.search.snippet import CrossEncoderSnippetClassifier, SnippetSearcher
from biz.structs import DocChunk
from common.django_ext.model import User
from common.services import slack
from common.utils.asyncioutil import gather_with_error

logger = getLogger(__name__)


class SearchPlan(BaseModel):
    query: str
    tickers: Optional[List[str]] = []
    doc_keys: Optional[List[str]] = []
    min_date: Optional[str] = None
    max_date: Optional[str] = None


class BaseSearchAgent(ChatAgent, ABC):
    config: SearchAgentConfig
    snippet_searcher: SnippetSearcher = SnippetSearcher()
    http_streaming: bool = False

    @timer(key="plan")
    async def plan(
        self,
        query: str,
        **kwargs: Any,
    ) -> SearchPlan:
        prompt_msgs = create_prompt_builder(
            sys_prompt=self.config.planner_prompt,
            user_msg=query,
            llm_model_name=self.config.planner_model_name,
            few_shots_msgs=None,
            fetched_data=None,
            post_user_msg_sys_prompt=None,
            override_datetime=kwargs.get("as_of_date"),
        ).build()
        params = {
            "log_prefix": "plan",
            "observer": self.observer,
            "cost_tracker": LlmCostTracker(
                cost_category=CostCategory.LLM_CHAT_CONTEXT, chat_turn=self.chat_turn
            ),
            "llm_model_name": self.config.planner_model_name,
            "max_tokens": self.config.plan_max_tokens,
            "enforce_json_output": True,
            "verbose": True,
        }
        llm_output = await create_llm(**params).chat_complete(prompt_msgs)  # type: ignore

        # this is actually function arguments
        logger.debug(f"Plan: {llm_output}")

        d: dict = json.loads(llm_output)

        tickers = [
            ticker
            for ticker in d.get("tickers") or []
            if ticker in datasdk.SUPPORTED_UNIQUE_TICKER_SET
        ]

        plan = SearchPlan(
            query=d.get("query") or query,
            tickers=tickers,
            min_date=d.get("min_date"),
            max_date=d.get("max_date"),
        )

        return plan

    @timer(key="fetch_data")
    async def fetch_data(self, plan: SearchPlan) -> List[DocChunk]:
        data_docs, data_tickers = [], []
        if plan.doc_keys:
            data_docs = await SearchByDocKeysFunction().run(
                {
                    "query": plan.query,
                    "doc_keys": plan.doc_keys,
                },
                self.config,
                self.observer,
            )
        if plan.tickers:
            params = {
                "query": plan.query,
                "tickers": plan.tickers,
                "min_date": plan.min_date,
            }
            if plan.max_date:
                params["max_date"] = plan.max_date
            data_tickers = await SearchAcrossDocsFunction().run(
                params,
                self.config,
                self.observer,
            )
        data = data_docs + data_tickers
        self.observer.log_retrieval(
            {"fetched_data": [d.as_logging_dict() for d in data]}
        )
        return data

    @timer(key="score_and_filter")
    async def score_and_filter(
        self, fetched_data: List[DocChunk], query: str
    ) -> List[ScoredChunk]:
        """Score each doc chunk by relevance(in place), filter out irrelevant ones,
        then pick the most relevant snippets(quotes) for each chunk.
        """
        snippet_classifier = CrossEncoderSnippetClassifier(
            model_name=self.config.snippet_classifier_model_name
        )
        await self.observer.notify_planning(
            f"[{PlanningTypeEnum.THOUGHTS.value}] Finding relevant snippets ..."
        )

        # score and filter at chunk level
        scored_chunks: List[ScoredChunk] = []
        chunk_scores = await snippet_classifier._predict(
            query, [d.data for d in fetched_data]
        )
        self.observer.log_retrieval({"chunk_scores": chunk_scores})

        for d, score in zip(fetched_data, chunk_scores):
            if score >= self.config.chunk_score_threshold:
                scored_chunks.append(
                    ScoredChunk(
                        doc_chunk=d,
                        chunk_score=score,
                        # set these fields in below logic
                        most_relevant_snippet="",
                        most_relevant_snippet_score=0,
                        doc_score=0,
                    )
                )

        # score and filter by the most relevant snippet
        scored_snippets = await snippet_classifier.find_most_relevant_snippet_in_docs(
            query, [c.doc_chunk.data for c in scored_chunks]
        )
        self.observer.log_retrieval({"scored_snippets": scored_snippets})
        final_chunks: List[ScoredChunk] = []
        for scored_chunk, (relevant_snippet, snippet_score) in zip(
            scored_chunks, scored_snippets
        ):
            # filter again if the most relevant snippet is below threshold
            if snippet_score >= self.config.snippet_score_threshold:
                scored_chunk.most_relevant_snippet = relevant_snippet
                scored_chunk.most_relevant_snippet_score = snippet_score
                final_chunks.append(scored_chunk)

        return final_chunks

    @timer(key="synthesis")
    async def synthesis(
        self, msg: str, ranked_data: List[ScoredChunk]
    ) -> str | StreamingHttpResponse:
        public_data = []
        private_data = []
        for d in ranked_data:
            s = f"{d.doc_chunk.as_prompt(with_content=False)} SNIPPET: {d.most_relevant_snippet}"
            if d.doc_chunk.meta.doc_type == DocTypeEnum.CUSTOMER:
                private_data.append(s)
            else:
                public_data.append(s)
        referenced_data = (
            ["# PUBLIC DOCS\n\n"] + public_data + ["\n\n# PRIVATE DOCS"] + private_data
        )

        prompt_msgs = create_prompt_builder(
            sys_prompt=self.config.synthesis_prompt,
            user_msg=msg,
            llm_model_name=self.config.synthesis_model_name,
            fetched_data=referenced_data,
            few_shots_msgs=self.config.synthesis_few_shots,
        ).build()
        params = {
            "log_prefix": "synthesis",
            "observer": self.observer,
            "cost_tracker": LlmCostTracker(
                cost_category=CostCategory.LLM_CHAT_ANSWER, chat_turn=self.chat_turn
            ),
            "llm_model_name": self.config.synthesis_model_name,
            "max_tokens": self.config.synthesis_max_tokens,
            "verbose": True,
        }
        if self.http_streaming:
            output_handler = HttpLLMOutputHandler(chat_turn_id=self.chat_turn.id)
            params["output_handler"] = output_handler
            llm_resp = await create_llm(**params).http_streaming(
                prompt_msgs,
                headers={
                    "X-CHAT-TURN-ID": self.chat_turn.id,
                    "X-CHAT-ID": self.chat_turn.chat.id,
                },
            )
        elif self.streaming:
            llm_resp = await create_llm(**params).chat_streaming(prompt_msgs)
        else:
            llm_resp = await create_llm(**params).chat_complete(prompt_msgs)
        return llm_resp


class SearchWithPlanAgent(BaseSearchAgent):
    """Search with a given plan"""

    config: SearchWithPlanAgentConfig

    @timer(key="plan")
    async def plan(self, query: str, **kwargs: Any) -> SearchPlan:
        tickers = kwargs.get("tickers", [])
        doc_keys = kwargs.get("doc_keys", [])

        # NOTE(ruiwang|2024-11-12): expand the query to let the planner know tickers and doc_keys
        if tickers:
            query += f" for {tickers}"
        if doc_keys:
            query += f" in documents: {doc_keys}"

        plan = await super().plan(query, **kwargs)
        if tickers or doc_keys:
            plan.tickers = tickers
            plan.doc_keys = doc_keys
        plan.min_date = (
            kwargs.get("min_date")
            or plan.min_date
            or (
                datetime.now(tz=UTC) - timedelta(days=self.config.min_date_default_days)
            ).strftime("%Y-%m-%d")
        )
        plan.max_date = kwargs.get("max_date") or plan.max_date
        logger.info(f"override plan: {plan.model_dump_json(indent=2)}")

        self.observer.log_states({"plan": plan.model_dump()})
        return plan

    @timer(key="run")
    async def _run(
        self, user: User, query: str, **kwargs: Any
    ) -> str | StreamingHttpResponse:
        if self.streaming and self.http_streaming:
            logger.warning(
                '"http_streaming" will be used when "streaming" and "http_streaming" are all True. '
            )
        plan = await self.plan(query, **kwargs)
        fetched_data = await self.fetch_data(plan)
        filtered_chunks: List[ScoredChunk] = await self.score_and_filter(
            fetched_data, query
        )
        self.observer.log_retrieval(
            {"filtered_chunks": [d.as_logging_dict() for d in filtered_chunks]}
        )

        # TODO: only save cited snippets to reduce latency. 1. DB IO; 2. Pre-built JSON size.
        results, errs = await gather_with_error(
            *[
                self.synthesis(query, filtered_chunks),
                self.save_relevant_snippets(filtered_chunks),
            ]
        )
        return results[0]

    # async def gather_cited_chunks(
    #     self, ranked_data: List[ScoredChunk], answer: str
    # ) -> set[str]:
    #     cited_chunk_ids = set()
    #     for m in re.finditer(r"\[.*?\]", answer, re.I | re.MULTILINE):
    #         print(m)
    #         cited_chunk_ids.add(str(m))
    #     return cited_chunk_ids

    @timer(key="gen_result_page")
    async def save_relevant_snippets(
        self,
        ranked_chunks: List[ScoredChunk],
    ) -> None:
        relevant_list = await self.generate_relevant_snippets(ranked_chunks)
        for relevant in relevant_list:
            await self.observer.notify_relevant_snippet(relevant)

    async def generate_relevant_snippets(
        self,
        ranked_chunks: List[ScoredChunk],
    ) -> list:
        relevant_list: list[RelevantSnippet] = []
        for idx, sc in enumerate(ranked_chunks):
            chunk = sc.doc_chunk
            index_id = chunk.get_index_id()
            if not sc.most_relevant_snippet:
                logger.error(
                    f"Missing most relevant snippet for {index_id} in chunk {idx}"
                )
                continue

            snippet = sc.most_relevant_snippet[:500]
            relevant = RelevantSnippet(
                chat_turn=self.chat_turn,
                number=idx + 1,  # Make it start at 1 instead of 0
                index_id=index_id,
                title=chunk.meta.display_title,
                doc_key=chunk.meta.key,
                snippet=snippet,
                pub_date=chunk.meta.pub_date,
                highlight_spans=[],  # TODO: fill this in
                highlighted_text_spans=[],
                debug_info={
                    "chunk_score": sc.chunk_score,
                    "highlight_score": sc.most_relevant_snippet_score,
                    "doc_score": sc.doc_score,
                    "features": sc.features,
                },
            )
            relevant_list.append(relevant)
            idx += 1

        self._extra_data["relevant_snippets"] = relevant_list
        return relevant_list


class SearchDocViewAgent(BaseSearchAgent):
    """Search and return synthesis + ranked document with relevant quotes"""

    config: SearchDocViewAgentConfig
    doc_value_model: DocValueModel = DocValueModel()

    @timer(key="synonyms")
    async def extract_synonyms(self, query: str) -> List[str]:
        return await SynonymsExtractorAgent(
            config=SynonymsExtractorAgentConfig(),
            observer=self.observer,
        ).run(query)

    @timer(key="gen_result_page")
    async def gen_result_page(
        self,
        query: str,
        ranked_chunks: List[ScoredChunk],
        synonyms: List[str],
    ) -> str:
        for idx, sc in enumerate(ranked_chunks):
            chunk = sc.doc_chunk
            index_id = chunk.get_index_id()
            if not sc.most_relevant_snippet:
                logger.error(
                    f"Missing most relevant snippet for {index_id} in chunk {idx}"
                )
                continue

            # TODO: make debug info only show in debug mode
            snippet = sc.most_relevant_snippet[:500]
            keywords_highlight_spans = highlight_synonyms(synonyms, snippet)
            relevant = RelevantSnippet(
                chat_turn=self.chat_turn,
                number=idx + 1,  # Make it start at 1 instead of 0
                index_id=index_id,
                title=chunk.meta.display_title,
                doc_key=chunk.meta.key,
                snippet=snippet,
                pub_date=chunk.meta.pub_date,
                highlight_spans=[],  # TODO: fill this in
                highlighted_text_spans=keywords_highlight_spans,
                debug_info={
                    "chunk_score": sc.chunk_score,
                    "highlight_score": sc.most_relevant_snippet_score,
                    "doc_score": sc.doc_score,
                    "features": sc.features,
                },
            )
            await self.observer.notify_relevant_snippet(relevant)
            idx += 1

        return ""

    @timer(key="rank_by_doc")
    def rank_by_doc(self, scored_chunks: List[ScoredChunk]) -> List[ScoredChunk]:
        # first group by doc_key
        doc_key_to_chunks: Dict[str, List[ScoredChunk]] = {}
        for c in scored_chunks:
            doc_key = c.doc_chunk.get_doc_key()
            if doc_key not in doc_key_to_chunks:
                doc_key_to_chunks[doc_key] = []
            doc_key_to_chunks[doc_key].append(c)

        # then score each doc by taking the max of chunk scores
        for doc_key, chunks in doc_key_to_chunks.items():
            doc_score = self.doc_value_model.score(doc_key, chunks)
            for c in chunks:
                c.doc_score = doc_score

        # finally, sort first by doc_score, then by chunk_score
        ranked_chunks = sorted(
            scored_chunks,
            key=lambda c: (c.doc_score, c.chunk_score),
            reverse=True,
        )

        return ranked_chunks

    @timer(key="run")
    async def _run(self, user: User, query: str, **kwargs: Any) -> str:
        await self.observer.notify_planning(
            f"[{PlanningTypeEnum.THOUGHTS.value}] Understanding the question ..."
        )

        try:
            plan, synonyms = await asyncio.gather(
                self.plan(query, **kwargs), self.extract_synonyms(query)
            )
            self.observer.log_states({"plan": plan.model_dump(), "synonyms": synonyms})
        except json.decoder.JSONDecodeError as e:
            self.observer.log_error(e)
            await self.observer.notify_streaming(
                SEARCH_QUERY_UNDERSTANDING_ERROR_MSG, done=True
            )
            return SEARCH_QUERY_UNDERSTANDING_ERROR_MSG

        handled = await self.maybe_handle_out_of_scope(plan)
        if handled:
            return handled

        fetched_data = await self.fetch_data(plan)
        if not fetched_data:
            resp = "Sorry but I couldn't find any relevant data for your query."
            await self.observer.notify_streaming(resp, done=True)
            slack.send_alert(
                f"No fetched data for query: {query}",
                ex=f"by user {user}",
                channel=slack.ENGINEERING_CHANNEL,
                force=True,
                icon=":mag:",
            )
            return resp

        filtered_chunks: List[ScoredChunk] = await self.score_and_filter(
            fetched_data, query
        )

        ranked_chunks: List[ScoredChunk] = self.rank_by_doc(filtered_chunks)

        self.observer.log_retrieval(
            {"ranked_chunks": [d.as_logging_dict() for d in ranked_chunks]}
        )

        _, resp = await asyncio.gather(
            self.gen_result_page(query, ranked_chunks, synonyms),
            self.synthesis(
                query,
                ranked_chunks,
            ),
        )

        return resp

    async def maybe_handle_out_of_scope(self, plan: SearchPlan) -> Optional[str]:
        """Give user a hint if the companies they are asking for are not in SP500
        Return True if we already handled out of scope case, False otherwise"""
        tickers = plan.tickers
        if not tickers:
            return None
        if "ALL" in tickers:
            return None
        if all(ticker not in datasdk.SUPPORTED_UNIQUE_TICKER_SET for ticker in tickers):
            resp = "Sorry but we only support companies that are listed in NYSE and NASDAQ right now. However if you think I made a mistake feel free to correct me."
            await self.observer.notify_streaming(resp, done=True)
            return resp

        return None


async def create_search_agent(
    user: User,
    query: str,
    streaming: bool = False,
    http_streaming: bool = False,
    save_relevant_snippets_to_db: bool = True,
    snippet_ranking_level: int = 0,
    message_source: MessageSource | str = MessageSource.API,
) -> SearchWithPlanAgent:
    """
    :param user:
    :param query:
    :param streaming:
    :param http_streaming:
    :param save_relevant_snippets_to_db:
    :param snippet_ranking_level:  negative->loose, positive->strict, balanced->0. 0.05 per level. max +/- 5
    :param message_source:
    :return:
    """
    assert -5 <= snippet_ranking_level <= 5
    chat = await Chat.objects.acreate(user=user, title=query)
    # this is only for logging purpose
    fake_user_msg = query
    message_source = (
        message_source.value
        if isinstance(message_source, MessageSource)
        else message_source
    )
    chat_turn = await ChatTurn.objects.acreate(
        chat=chat, user_msg=fake_user_msg, message_source=message_source
    )
    config = SearchWithPlanAgentConfig(user_id=user.id)
    # TODO: enable snippet_ranking_level
    logger.warning('arg "snippet_ranking_level" is ignored now.')
    # config.snippet_score_threshold = 0.3 + snippet_ranking_level * 0.05
    config.snippet_score_threshold = 0.1
    observer = Observer(
        notifiers=(
            [DbNotifier(chat_turn=chat_turn)] if save_relevant_snippets_to_db else []
        )
    )
    observer.log_states({"config": config.to_dict()})
    return SearchWithPlanAgent(
        chat_turn=chat_turn,
        config=config,
        observer=observer,
        streaming=streaming,
        http_streaming=http_streaming,
    )
