import asyncio
import json
import logging
import time
import traceback
from collections import OrderedDict
from collections.abc import Async<PERSON>enerator
from typing import Any, List, Tuple

from agent.agents import deep_find
from agent.agents.deep_find import DeepFindAgentConfig, DeepFindAgent
from agent.agents.influencer import (
    InfluencerFinderAgent,
)
from agent.cost import LlmCostTracker
from agent.enum import CostCategory
from agent.llms import create_llm
from agent.prompts import create_prompt_builder
from agent.prompts.people import (
    PEOPLE_FINDER_GOOGLE_PROMPT,
    PEOPLE_FINDER_PLAN_GOOGLE_RESPONSE_FORMAT,
)
from agent.utils import timer
from aiflow.models import AIFlow
from common.services import slack
from common.utils.structutil import obj_to_dict_dumps
from scraper import scraper_sdk
from scraper.models import (
    SocialProfile,
)

logger = logging.getLogger(__name__)


class LinkedinPersonFinderAgent(InfluencerFinderAgent):
    """Agent to find persons on LinkedIn"""

    _platform_to_semaphore = {
        "linkedin": asyncio.Semaphore(20),
    }

    class Config:
        arbitrary_types_allowed = True

    async def plan_linkedin(self, task: str) -> tuple[str, List[str]]:
        """plan to search linkedin profile as Professor (not influencer)"""

        prompt_msgs = create_prompt_builder(
            sys_prompt=PEOPLE_FINDER_GOOGLE_PROMPT,
            user_msg=task,
            llm_model_name=self.config.llm_model_name,
            few_shots_msgs=None,
        ).build()
        ret = await create_llm(
            log_prefix="plan_linkedin",
            observer=self.observer,
            llm_model_name=self.config.llm_model_name,
            max_tokens=self.config.max_output_tokens,
            response_format=PEOPLE_FINDER_PLAN_GOOGLE_RESPONSE_FORMAT,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_RESEARCH,
            ),
        ).chat_complete(prompt_msgs)
        data = json.loads(ret)
        return data["organizational_context"], data["search_terms"]

    async def plan(self, task: str) -> tuple[str, List[str]]:
        if self.config.platform == "linkedin":
            return await self.plan_linkedin(task)
        else:
            raise ValueError(f"Unsupported platform: {self.config.platform}")

    async def retrieve_org(
        self, org_context: str, **kwargs: Any
    ) -> AsyncGenerator[deep_find.Entity, None]:
        username = kwargs.get("username")

        max_org_entities = max(3, self.config.entities_limit)
        org_entity_schema = {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Name of the company",
                },
                "description": {
                    "type": "string",
                    "description": "Brief description of the company's business",
                },
                # "size": {
                #     "type": "integer",
                #     "description": "Size of the company (how many employees)",
                # },
                # "industry": {
                #     "type": "string",
                #     "description": "Industry the company operates in",
                # },
                # "founded_year": {
                #     "type": "integer",
                #     "description": "Year the company was founded",
                # },
                # "headquarters": {
                #     "type": "string",
                #     "description": "Location of company headquarters",
                # },
                # "website": {
                #     "type": "string",
                #     "description": "Company's official website URL",
                # },
                # "key_products": {
                #     "type": "array",
                #     "items": {"type": "string"},
                #     "description": "Key products or services offered by the company",
                # },
            },
            "required": ["name", "description"],
        }

        config = DeepFindAgentConfig(
            entities_limit=max_org_entities,
            max_web_research_loops=2,
            entity_schema=org_entity_schema,
            fetch_full_page=False,
        )

        # iterate it
        agent = DeepFindAgent(config=config, observer=self.observer)
        async for entity in agent.iter(search_topic=org_context, username=username):
            yield entity

    async def retrieve(
        self, search_queries: List[str], **kwargs: Any
    ) -> AsyncGenerator[Tuple[str, SocialProfile, deep_find.Entity], None]:
        org_context = kwargs.get("org_context") or ""

        # TODO: need to bring entity reason to evaluate

        async for entity in self.retrieve_org(**kwargs):
            # generate new queries
            new_queries = [f'{q} at "{entity.get_name()}"' for q in search_queries]

            search_func = (
                self._search_influencers_local_api
                if scraper_sdk.API_ENABLED
                else self._search_influencers_local_spider
            )

            start_time = time.time()
            async for q, profile in search_func(
                new_queries, self.config.platform, username=kwargs.get("username") or ""
            ):
                elapsed_time = time.time() - start_time
                self.observer.log_perf({"latency_search_iter_per_yield": elapsed_time})
                start_time = time.time()  # Reset timer for next yield
                yield q, profile, entity

                if await self.async_shall_stop():
                    break

    @timer(key="inner_run")
    async def inner_run(self, **kwargs: Any) -> List[deep_find.Entity]:
        self.generated_entities: dict[
            str, dict
        ] = await self._fetch_generated_in_past_days(
            username=kwargs.get("username") or "", platform=kwargs.get("platform")
        )

        self.counters = OrderedDict()

        await self.observer.notify_status("🧠 Making a plan ...")
        org_context, search_queries = await self.plan(self.task)
        logger.info(f"Organization Context: {json.dumps(org_context, indent=2)}")
        logger.info(f"Search Queries: {json.dumps(search_queries, indent=2)}")
        self.observer.log_states(
            {
                "organization_context": org_context,
                "search_queries_in_order": search_queries,
                "engagements_for": self.config.engagements_for,
            }
        )

        sem = asyncio.Semaphore(8)
        async for q, profile, entity in self.retrieve(
            search_queries, org_context=org_context, **kwargs
        ):
            if await self.async_shall_stop(q):
                continue

            async with sem:
                extra_info = (
                    f"  - relevance reason - {entity.relevance_reason}\n"
                    f"  - relevance citations - {json.dumps(obj_to_dict_dumps(entity.relevance_citations), indent=2)}"
                )
                task = asyncio.create_task(
                    self._evaluate(
                        q,
                        profile,
                        username=kwargs.get("username") or "",
                        extra_eval_info=extra_info,
                    )
                )
                task.add_done_callback(
                    lambda t: (
                        logger.error(
                            f"Error evaluating {profile.handle}: {t.exception()}\n"
                            f"{''.join(traceback.format_exception(t.exception()))}"
                        )
                        if t.exception()
                        else None
                    )
                )

        if self.enriched_entity_count <= 5:
            username = kwargs["username"]
            platform = kwargs["platform"]
            task_short_title = kwargs.get("task_short_title") or ""
            slack.send_alert(
                f"Research {self.observer.notifiers[0].aiflow.id} finished with only {self.enriched_entity_count} results. ('{task_short_title}', {platform}, {username})",
                force=True,
            )

        # save evaluated urls for resuming (is_resumed)
        await AIFlow.objects.filter(id=self.observer.notifiers[0].aiflow.id).aupdate(
            evaluated=list(self.urls_evaluated)
        )

        # wait for pending engagement fetching.
        ts = time.perf_counter()
        while self._engagement_pending > 0:
            logger.info(
                f"... waiting for {self._engagement_pending} pending engagements"
            )
            await self.observer.notify_status(
                f"Waiting for {self._engagement_pending} pending engagement calculation."
            )

            await asyncio.sleep(0.5)
            if time.perf_counter() - ts > 600:
                logger.info(
                    f"stop waiting pending engagements ({self._engagement_pending})"
                )
                await self.observer.notify_status(
                    f"Finished. Dropped {self._engagement_pending} pending engagement calculation."
                )
                break

        return self.final_table
