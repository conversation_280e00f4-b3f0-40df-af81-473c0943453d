import json
import logging
from datetime import datetime, timezone
from typing import Any

from agent.agents.base import Agent
from agent.agents.indicators import IndicatorsAgent
from agent.agents.summarize import SummarizeDocAgent
from agent.config.agent import IndicatorsAgentConfig, WriteEarningCallBlogAgentConfig
from agent.content import EarningCallBlogPage, EarningCallBlogPageMeta
from biz import datasdk

logger = logging.getLogger(__name__)


class WriteEarningCallBlogAgent(Agent):
    """Agent for writing earning call summary blogs. Currently under /resources/earning-call-summary"""

    config: WriteEarningCallBlogAgentConfig

    async def run(self, **kwargs: Any) -> EarningCallBlogPage | None:
        assert "ticker" in kwargs, "ticker is required"
        ticker = kwargs["ticker"]
        year = kwargs["year"]
        quarter = kwargs["quarter"]
        filing_date = kwargs["filing_date"]
        config = kwargs["config"]
        logger.info(f"Writing earning call blog for {ticker}")

        agent = SummarizeDocAgent(
            config=config,  # type: ignore
            observer=self.observer,
            streaming=False,
        )
        summary = await agent.run(
            doc_key=f"earning_call/{ticker.upper()}/{year}/{quarter}"
        )

        config = IndicatorsAgentConfig()
        _, indicators = await IndicatorsAgent(
            config=config, observer=self.observer, streaming=False
        ).run(ticker=ticker)

        company_name = datasdk.company_name_by_ticker(ticker)

        blog = EarningCallBlogPage(
            meta=EarningCallBlogPageMeta(
                title=f"{company_name} ({ticker}) {year} Q{quarter} Earnings Call Summary",
                ticker=ticker,
                company_name=company_name,
                year=year,
                quarter=quarter,
                file_name=f"{ticker}/{year}/{quarter}",
                description=f"Summary of earning call for {ticker}",
                published_date=datetime.now(timezone.utc).isoformat(),
                earning_call_published_date=filing_date,
                cover="",
            ),
            content=json.dumps(
                {
                    "summary": summary,
                    "key_drivers": [ind.to_dict() for ind in indicators],
                },
                indent=4,
            ),
        )
        kwargs["config"] = config.to_dict()
        logger.info(f"Total LLM cost for {ticker}: {self.observer.get_llm_costs_str()}")
        await self.log_states(inputs=kwargs)
        return blog
