"""ARCHIVE"""
# import json
# import logging
# from abc import ABC, abstractmethod
# from datetime import datetime
# from typing import Any, Dict, List, Optional, Tuple
#
# from pydantic import BaseModel
#
# from agent.config.agent import PlanRetrievalAgentConfig as AgentConfig
# from agent.cost import LlmCostTracker
# from agent.enum import CostCategory
# from agent.exceptions import OutputParsingError
# from agent.functions import ALL_FUNCTIONS, FetcherFunction
# from agent.llms import create_llm
# from agent.memory import ShortTermMemory
# from agent.models import ChatTurn
# from agent.observer import Observer
# from agent.prompts import create_prompt_builder
# from agent.prompts.fetcher_planner import FEW_SHOTS_MSGS, get_fetch_planner_prompt
#
# logger = logging.getLogger(__name__)
#
#
# class Plan(BaseModel):
#     pass
#
#
# class FetcherPlan(Plan):
#     """A plan for concurrently fetching data from external sources"""
#
#     company_names: List[str]
#     company_tickers: List[str]
#     thoughts: str
#     functions_with_params: List[Tuple[FetcherFunction, Dict]]
#
#     def as_logging_dict(self) -> Dict:
#         return {
#             "company_names": self.company_names,
#             "company_tickers": self.company_tickers,
#             "thoughts": self.thoughts,
#             "functions_with_params": [
#                 {"func_name": f.name, "params": p}
#                 for (f, p) in self.functions_with_params
#             ],
#         }
#
#     @classmethod
#     def from_func_calls_dict(cls, func_calls: List[Dict[str, Any]]) -> "FetcherPlan":
#         """Parse only function calls and construct a plan"""
#         funcs_with_params = []
#         for func_dict in func_calls:
#             func_name = func_dict["func_name"]
#             if func_name not in ALL_FUNCTIONS:
#                 raise OutputParsingError(f"Unknown function: {func_name}")
#
#             func = ALL_FUNCTIONS[func_name]
#             func_params = func_dict["params"]
#             if not func.validate_param(func_params):
#                 raise OutputParsingError(
#                     f"Invalid parameters {func_params} for function: {func_name}"
#                 )
#             funcs_with_params.append((func, func_params))
#
#         return cls(
#             company_names=[],
#             company_tickers=[],
#             thoughts="",
#             functions_with_params=funcs_with_params,
#         )
#
#     def as_human_readable_run_msg(self) -> str:
#         return "\n".join(
#             [
#                 f" - {func.as_human_readable_run_msg(params)}"
#                 for func, params in self.functions_with_params
#             ]
#         )
#
#
# class Planner(ABC):
#     @abstractmethod
#     async def plan(
#         self,
#         config: AgentConfig,
#         observer: Observer,
#         chat_turn: ChatTurn,
#         msg: str,
#         memory: Optional[ShortTermMemory] = None,
#     ) -> Plan:
#         """Base planner"""
#
#
# class FetcherPlanner(Planner):
#     """
#     Extract entities then plan to fetch data by calling fetcher functions
#     """
#
#     def parse_and_validate(self, output: str) -> FetcherPlan:
#         try:
#             start_index = output.find("{")
#             end_index = output.rfind("}") + 1
#             json_str = output[start_index:end_index]
#             output_dict = json.loads(json_str)
#             logger.info(f"output_dict: {output_dict}")
#             func_calls = output_dict["all_function_calls"]
#         except (json.decoder.JSONDecodeError, ValueError):
#             error_msg = f"Can't parse planning output:\n{output}"
#             logger.error(error_msg)
#             raise OutputParsingError(error_msg)
#
#         funcs_with_params = []
#         for func_dict in func_calls:
#             func_name = func_dict["function_name"]
#             if func_name not in ALL_FUNCTIONS:
#                 raise OutputParsingError(f"Unknown function: {func_name}")
#
#             func = ALL_FUNCTIONS[func_name]
#             func_params = func_dict["parameters"]
#             if not func.validate_param(func_params):
#                 raise OutputParsingError(
#                     f"Invalid parameters {func_params} for function: {func_name}"
#                 )
#             funcs_with_params.append((func, func_params))
#
#         return FetcherPlan(
#             company_names=output_dict["company_names"],
#             company_tickers=output_dict["company_tickers"],
#             thoughts=output_dict["thoughts"],
#             functions_with_params=funcs_with_params,
#         )
#
#     async def plan(
#         self,
#         config: AgentConfig,
#         observer: Observer,
#         chat_turn: ChatTurn,
#         msg: str,
#         memory: Optional[ShortTermMemory] = None,
#         as_of_date: Optional[datetime | str] = None,
#     ) -> FetcherPlan:
#         prompt_msgs = create_prompt_builder(
#             sys_prompt=get_fetch_planner_prompt(config.doc_type_filter),
#             user_msg=msg,
#             llm_model_name=config.planner_model_name,
#             few_shots_msgs=FEW_SHOTS_MSGS,
#             memory=memory,
#             override_datetime=as_of_date,  # depends on `include_cur_time` (default True)
#         ).build()
#
#         output = await create_llm(
#             log_prefix="planner",
#             observer=observer,
#             cost_tracker=LlmCostTracker(
#                 cost_category=CostCategory.LLM_CHAT_PLANNING, chat_turn=chat_turn
#             ),
#             # TODO: Try again gpt-4-1106-preview with JSON mode (tried once but got lower eval score)
#             llm_model_name=config.planner_model_name,
#             max_tokens=700,
#             verbose=True,
#         ).chat_complete(prompt_msgs)
#         plan = self.parse_and_validate(output)
#
#         logger.info(f"Plan: {json.dumps(plan.as_logging_dict(), indent=4)}")
#         return plan
