from logging import getLogger
from typing import Any

from agent.agents.base import Agent
from agent.config.agent import ClassifyDocMetaAgentConfig
from agent.cost import LlmCostTracker
from agent.enum import CostCategory
from agent.llms import create_llm
from agent.prompts import create_prompt_builder
from agent.prompts.classify_doc_meta import get_classify_doc_meta_sys_prompt
from agent.utils import timer

logger = getLogger(__name__)


class ClassifyDocMetaAgent(Agent):
    """classify doc meta"""

    config: ClassifyDocMetaAgentConfig = ClassifyDocMetaAgentConfig()

    @timer(key="run")
    async def run(self, **kwargs: Any) -> str:
        filename = kwargs.get("filename", "")
        data = kwargs.get("data", {})
        meta_keys = kwargs.get("keys", ["tickers", "pub_date", "title"])
        user_msg = f"---\nfilename: {filename}\n---\n\n{data}"
        prompt_msgs = create_prompt_builder(
            sys_prompt=get_classify_doc_meta_sys_prompt(*meta_keys),
            user_msg=user_msg,
            llm_model_name=self.config.answer_model_name,
            few_shots_msgs=None,
            memory=None,
            fetched_data=None,
            post_user_msg_sys_prompt=None,
        ).build()

        params = {
            "log_prefix": "classify_doc_meta",
            "observer": self.observer,
            "cost_tracker": LlmCostTracker(cost_category=CostCategory.LLM_CHAT_ANSWER),
            "output_handler": None,
            "llm_model_name": self.config.answer_model_name,
            "max_tokens": self.config.answer_max_complete_tokens,
            "verbose": True,
            "enforce_json_output": True,
        }
        if self.streaming:
            return await create_llm(**params).http_streaming(prompt_msgs)  # type: ignore
        else:
            return await create_llm(**params).chat_complete(prompt_msgs)  # type: ignore
