import asyncio
import logging
import time
from collections import OrderedDict
from functools import lru_cache, wraps
from typing import Any, Callable, Dict, List

import tiktoken

from agent.observer import Observable

# from datasvc.models import IndexStatistics

logger = logging.getLogger(__name__)

# Reference: https://cookbook.openai.com/examples/how_to_count_tokens_with_tiktoken
# Extra tokens to form a message like {"role": ..., "content": ...}
TOKEN_OVERHEAD_PER_MSG = 5

# root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# indexing_lock = FileLock(
#     os.path.join(root_dir, "indexing_statistics.lock"), timeout=300
# )
# local_run_path = "indexing_statistics.json"


# def filing_date_to_period(filing_date: datetime) -> Period:
#     """Convert filing date to year and quarter.
#     Note 10-K is for the previous year's Q4."""
#     if filing_date.month <= 3:
#         return Period(y=filing_date.year - 1, q=4)
#     elif filing_date.month <= 6:
#         return Period(y=filing_date.year, q=1)
#     elif filing_date.month <= 9:
#         return Period(y=filing_date.year, q=2)
#     else:
#         return Period(y=filing_date.year, q=3)


# def filename_index_summary(index_name: str, index_namespace: str, doc_type: str) -> str:
#     return f"{index_namespace}_{index_name}_{doc_type}_index_summary.json"


cache: OrderedDict = OrderedDict()


def alocal_cache_on_key(
    key_func: Callable[..., str], maxsize: int = 128
) -> Callable[..., Any]:
    """Function level local cache with LRU eviction and a user defined key function.
    Usage:
    @local_cache_on_key(key_func=lambda *args, **kwargs: <how to generate the key>)
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        func_id = id(func)  # Use the function's id as a unique identifier

        if not asyncio.iscoroutinefunction(func):
            raise ValueError("alocal_cache_on_key can only be used on async functions")
        else:

            @wraps(func)
            async def wrapper(*args: Any, **kwargs: Any) -> Any:
                key = f"{func_id}:{key_func(*args, **kwargs)}"

                if key in cache:
                    logger.warning(f"Cache hit: {key}")
                    # Move the accessed item to the end (most recently used)
                    cache.move_to_end(key)
                    return cache[key]

                result = await func(*args, **kwargs)

                if len(cache) >= maxsize:
                    # Remove the first item (least recently used)
                    cache.popitem(last=False)

                cache[key] = result
                return result

        return wrapper

    return decorator


@lru_cache(maxsize=128)
def tokenize(text: str, model: str = "gpt-4") -> List[int]:
    """NOTE: Use tiktoken from OpenAI to count since we are mostly using openai
    models right now. For some complex formatted strings tiktoken and GPT2TokenizerFast
    can have significant difference. See test_utils for details.
    """
    try:
        encoding = tiktoken.encoding_for_model(model)
    except KeyError:
        logger.warn("Warning: model not found. Using cl100k_base encoding.")
        encoding = tiktoken.get_encoding("cl100k_base")
    return encoding.encode(text)


def get_num_tokens(text: str) -> int:
    return len(tokenize(text))


def get_num_tokens_prompt_msgs(prompt_msgs: List[Dict]) -> int:
    return sum(
        [get_num_tokens(msg["content"]) + TOKEN_OVERHEAD_PER_MSG for msg in prompt_msgs]
    )


def truncate_data_by_token_limit(docs: List, max_tokens_limit: int) -> List:
    num_docs = len(docs)

    # TODO(data): basically， if a object can be used as prompt，it should has some common properties or methods.
    #           that says, we should have a interface like LlmPrompt for it.
    tokens = [
        get_num_tokens(doc.as_prompt() if hasattr(doc, "as_prompt") else str(doc))
        for doc in docs
    ]
    token_count = sum(tokens[:num_docs])
    while token_count > max_tokens_limit:
        num_docs -= 1
        token_count -= tokens[num_docs]

    return docs[:num_docs]


def timer(key: str) -> Callable:
    """A timer that logs latency for a function call inside classes that inherit Observable."""

    def decorator(func: Callable) -> Callable:
        if not asyncio.iscoroutinefunction(func):

            @wraps(func)
            def wrapper(*args: Any, **kwargs: Any) -> Any:
                assert isinstance(
                    args[0], Observable
                ), "First argument must be an instance of Observable"
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                finally:
                    elapsed_time = time.time() - start_time
                    obs = args[0].observer
                    obs.log_perf({f"latency_{key}": elapsed_time})
                return result

        else:

            @wraps(func)
            async def wrapper(*args: Any, **kwargs: Any) -> Any:
                assert isinstance(
                    args[0], Observable
                ), "First argument must be an instance of Observable"
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                finally:
                    elapsed_time = time.time() - start_time
                    obs = args[0].observer
                    obs.log_perf({f"latency_{key}": round(elapsed_time, 2)})
                return result

        return wrapper

    return decorator


def record_function(key: str) -> Callable:
    def decorator(func: Callable) -> Callable:
        if not asyncio.iscoroutinefunction(func):

            @wraps(func)
            def wrapper(*args: Any, **kwargs: Any) -> Any:
                assert isinstance(
                    args[0], Observable
                ), "First argument must be an instance of Observable"
                start_time = time.time()
                result = func(*args, **kwargs)
                elapsed_time = time.time() - start_time

                function_data = {
                    "key": key,
                    "function_name": func.__name__,
                    "input": {
                        "args": [str(arg) for arg in args[1:]],  # don't record self
                        "kwargs": {k: str(v) for k, v in kwargs.items()},
                    },
                    "output": str(result),
                    "latency": round(elapsed_time, 2),
                }
                observer = args[0].observer
                observer.log_function_call(function_data)
                return result

        else:

            @wraps(func)
            async def wrapper(*args: Any, **kwargs: Any) -> Any:
                assert isinstance(
                    args[0], Observable
                ), "First argument must be an instance of Observable"
                start_time = time.time()
                result = await func(*args, **kwargs)
                elapsed_time = time.time() - start_time

                function_data = {
                    "key": key,
                    "function_name": func.__name__,
                    "input": {
                        "args": [str(arg) for arg in args[1:]],  # don't record self
                        "kwargs": {k: str(v) for k, v in kwargs.items()},
                    },
                    "output": str(result),
                    "latency": round(elapsed_time, 2),
                }

                observer = args[0].observer
                observer.log_function_call(function_data)
                return result

        return wrapper

    return decorator
