import json
import logging
from typing import Any, Iterable, Optional

from django.contrib.auth.models import User

from agent import conf
from agent.agents import AGENT_CONFIG_BY_INTENT_NAME, ALL_AGENTS
from agent.agents.fake import ALL_FAKE_AGENT_CONFIGS
from agent.agents.search import SearchD<PERSON>ViewAgent, SearchWithPlanAgent
from agent.conf import LLM_MODEL
from agent.config.agent import (
    AgentConfig,
    SearchDocViewAgentConfig,
    SearchWithPlanAgentConfig,
)

# from agent.config.guide import GUIDANCE_INTENT_MAPPING
from agent.config.intent import INTENT_NAMES
from agent.cost import LlmCostTracker
from agent.enum import CostCategory, ProductVersionEnum
from agent.exceptions import OutputParsingError
from agent.llms import create_llm
from agent.memory import ShortTermMemory
from agent.models import ChatMsg, ChatTurn
from agent.observer import Observer
from agent.prompts import create_prompt_builder
from agent.prompts.intent import INTENT_CLASSIFIER_PROMPT, INTENT_FEW_SHOTS
from agent.prompts.search import SYNTHESIS_SYS_PROMPT
from biz.enums import DocTypeEnum

logger = logging.getLogger(__name__)


class ChatEngine:
    fallback_intent_name: str = "analysis"

    def __init__(
        self,
        user: User,
        chat_turn: ChatTurn,
        observer: Observer,
        streaming: bool = True,
    ) -> None:
        self.user = user
        self.chat_turn = chat_turn
        self.chat = chat_turn.chat
        self.observer = observer
        self.streaming = streaming

    async def classify_intent(self, msg: str, memory: ShortTermMemory) -> str:
        # classify from guide context
        # if self.chat_turn.guide:
        #     guide_path = self.chat_turn.guide.get("path", [])
        #     guide = guide_path[-1] if len(guide_path) > 0 else {}
        #     guide_name = guide.get("name", "")
        #     intent = GUIDANCE_INTENT_MAPPING.get(guide_name)
        #     if intent:
        #         logger.warning(
        #             f'classified user intent "{intent}" from guide context "{guide_name}".'
        #         )
        #         assert intent in INTENT_NAMES, f"Unknown intent: {intent}"
        #         return intent

        """TODO: use a cheaper model"""
        prompt_msgs = create_prompt_builder(
            sys_prompt=INTENT_CLASSIFIER_PROMPT,
            user_msg=msg,
            llm_model_name=LLM_MODEL or "gpt-4-1106-preview",
            memory=memory,
            # we need both user and AI msgs for intent classification, especially for
            # clarification questions
            include_memory_msgs_type="all_msgs",
            include_cur_time=False,
            few_shots_msgs=INTENT_FEW_SHOTS,
            enforce_json_output=True,
        ).build()
        llm = create_llm(
            log_prefix="intent_classifier",
            observer=self.observer,
            cost_tracker=LlmCostTracker(
                cost_category=CostCategory.LLM_CHAT_INTENT, chat_turn=self.chat_turn
            ),
            llm_model_name=LLM_MODEL or "gpt-4-1106-preview",
            max_tokens=50,
            enforce_json_output=True,
        )
        try:
            output = await llm.chat_complete(prompt_msgs)
            resp = json.loads(output)
            intent = resp["intent"]
            assert intent in INTENT_NAMES, f"Unknown intent: {intent}"
        except (json.JSONDecodeError, KeyError, AssertionError):
            self.observer.log_error(
                OutputParsingError(f"Can't parse intent classifier output '{output}'")
            )
            intent = self.fallback_intent_name

        logger.warning(f"Classified User Intent: {intent}")
        return intent

    async def answer(
        self,
        msg: str,
        doc_type_filter: Iterable[str] = DocTypeEnum.values(),
        **kwargs: Any,
    ) -> str:
        """answer user inquery based on doc_types"""
        logger.info(f"User {self.user.id} said: {msg}")

        if conf.PRODUCT_VERSION == ProductVersionEnum.MVP20:
            return await self._answer_mvp20(msg, doc_type_filter, **kwargs)
        elif conf.PRODUCT_VERSION == ProductVersionEnum.MVP30:
            return await self._answer_mvp30(msg, doc_type_filter, **kwargs)
        else:
            return await self._answer(msg, doc_type_filter, **kwargs)

    async def _answer(
        self,
        msg: str,
        doc_type_filter: Iterable[str] = DocTypeEnum.values(),
        **kwargs: Any,
    ) -> str:
        """Answer for main branching. current it's mvp3.2 (2024/08/12).
        MVP3.2 is basically a search engine and we still use ChatEngine here
        for fast prototyping. Consider to refactor this to a new class"""
        await ChatMsg.save_user_msg(chat_turn=self.chat_turn, text=msg)

        doc_keys = kwargs.get("doc_keys", [])
        tickers = kwargs.get("tickers", [])

        if doc_keys or tickers:
            config = SearchWithPlanAgentConfig(
                doc_type_filter=list(doc_type_filter), user_id=self.user.id
            )
            config.synthesis_prompt = SYNTHESIS_SYS_PROMPT
            agent = SearchWithPlanAgent(
                chat_turn=self.chat_turn,
                config=config,
                observer=self.observer,
                streaming=self.streaming,
            )
        else:
            config = SearchDocViewAgentConfig(
                doc_type_filter=list(doc_type_filter), user_id=self.user.id
            )
            agent = SearchDocViewAgent(
                chat_turn=self.chat_turn,
                config=config,
                observer=self.observer,
                streaming=self.streaming,
            )
        self.observer.log_states({"config": config.to_dict()})

        resp = await agent.run(self.user, query=msg, **kwargs)
        await ChatMsg.save_ai_msg(chat_turn=self.chat_turn, text=resp)

        logger.info(f"AI resp: {resp}")
        return resp

    async def _answer_mvp30(
        self,
        msg: str,
        doc_type_filter: Iterable[str] = DocTypeEnum.values(),
        **kwargs: Any,
    ) -> str:
        """answer for main branching. current it's mvp3.0 (2024/4/15)
        We use FIXED INTENT "search_filter" in mvp3.0
        """

        memory = ShortTermMemory(self.user, self.chat.id)
        await memory.load(mins=30, only_user_msg=False)

        await ChatMsg.save_user_msg(chat_turn=self.chat_turn, text=msg)

        intent = "search_filter"  # fixed intent. TODO: make intent names Enum
        config = AGENT_CONFIG_BY_INTENT_NAME[intent]
        config.doc_type_filter = doc_type_filter
        config.doc_key = kwargs.get("doc_key")
        self.observer.log_states({"intent": intent, "config": config.to_dict()})
        logger.info(f"Using Agent config: {config}")

        agent_cls = ALL_AGENTS[config.agent_name]
        agent = agent_cls(
            chat_turn=self.chat_turn,
            config=config,
            observer=self.observer,
            streaming=self.streaming,
        )
        resp = await agent.run(
            self.user, memory=memory, msg=msg, intent=intent, **kwargs
        )
        await ChatMsg.save_ai_msg(chat_turn=self.chat_turn, text=resp)

        logger.info(f"AI resp: {resp}")
        return resp

    async def _answer_mvp20(
        self,
        msg: str,
        doc_type_filter: Iterable[str] = DocTypeEnum.values(),
        **kwargs: Any,
    ) -> str:
        """answer for mvp 2.0 branching
        Right now we map Agent(with AgentConfig) based on user's intent."""

        logger.debug(f"Chat guide context: {self.chat_turn.guide}")

        memory = ShortTermMemory(self.user, self.chat.id)
        await memory.load(mins=30, only_user_msg=False)

        await ChatMsg.save_user_msg(chat_turn=self.chat_turn, text=msg)

        config = self._handle_fakes(user_msg=msg)
        if config is not None:
            # take intent from FakeAgentConfig
            intent = getattr(config, "intent", "") + "[fake]"
        else:
            intent = await self.classify_intent(msg, memory)
            config = AGENT_CONFIG_BY_INTENT_NAME[intent]
        config.doc_type_filter = doc_type_filter
        self.observer.log_states({"intent": intent, "config": config.to_dict()})
        logger.info(f"Using Agent config: {config}")

        try:
            agent_cls = ALL_AGENTS[config.agent_name]
        except KeyError as e:
            self.observer.log_error(
                e,
                msg=f"{config.agent_name} is NOT defined as Agent class! Use default.",
            )
            agent_cls = ALL_AGENTS["default"]

        agent = agent_cls(
            chat_turn=self.chat_turn,
            config=config,
            observer=self.observer,
            streaming=self.streaming,
        )
        resp = await agent.run(
            self.user, memory=memory, msg=msg, intent=intent, **kwargs
        )
        await ChatMsg.save_ai_msg(chat_turn=self.chat_turn, text=resp)

        logger.info(f"AI resp: {resp}")
        return resp

    def _handle_fakes(self, user_msg: str) -> Optional[AgentConfig]:
        s = user_msg.strip().lower()
        if s in ALL_FAKE_AGENT_CONFIGS.keys():
            logger.warning("Fake inquery detected. Being Handled by fake agent.")
            config = ALL_FAKE_AGENT_CONFIGS.get(s)
            return config
        return None
