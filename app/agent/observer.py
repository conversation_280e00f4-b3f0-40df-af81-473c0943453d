import logging
import time
import traceback
from abc import ABC
from collections import OrderedDict
from contextlib import contextmanager
from typing import Any, Dict, Generator, List, Optional

from agent import conf
from agent.enum import ServerOperationEnum
from agent.exceptions import AgentError
from agent.models import (
    Chart,
    ChatMsg,
    ChatTurn,
    Citation,
    FetchedSource,
    LlmCost,
    RelevantSnippet,
)
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class Notifier(ABC):
    def __init__(self, chat_turn: ChatTurn):
        self.chat_turn = chat_turn

    class Config:
        arbitrary_types_allowed = True

    async def notify(self, msg: str) -> None:
        pass

    async def notify_streaming(self, data: str, done: bool) -> None:
        pass

    async def notify_planning(self, data: str) -> None:
        pass

    async def notify_typing(self) -> None:
        pass

    async def notify_sources(self, data: List[FetchedSource]) -> None:
        pass

    async def notify_relevant_snippet(self, data: RelevantSnippet) -> None:
        pass

    async def notify_citation(self, citation: Citation) -> None:
        pass

    async def notify_chart(self, charts: List[Chart]) -> None:
        pass

    async def notify_operation(self, operation: ServerOperationEnum) -> None:
        pass


# Used for unit testing, as a notifier is required.
class EmptyNotifier(Notifier):
    def __init__(self) -> None:
        pass


class DbNotifier(Notifier):
    async def notify_planning(self, data: str) -> None:
        await ChatMsg.save_planning_msg(chat_turn=self.chat_turn, text=data)

    async def notify_sources(self, sources: List[FetchedSource]) -> None:
        await FetchedSource.objects.abulk_create(sources)

    async def notify_relevant_snippet(self, relevant: RelevantSnippet) -> None:
        await relevant.asave()

    async def notify_citation(self, citation: Citation) -> None:
        await citation.asave()

    async def notify_chart(self, charts: List[Chart]) -> None:
        for chart in charts:
            chart.chat_turn = self.chat_turn
            await chart.asave()


class Observer(BaseModel):
    # states to log
    states: Dict = {}
    errors: Dict = {}
    perf: Dict[str, List[float]] = {}
    retrieval: Dict = {}
    llm_prompts: Dict = {}
    llm_outputs: Dict = {}
    llm_costs: list[LlmCost | None] = []

    notifiers: List[Notifier] = Field(default=[EmptyNotifier()], exclude=True)
    function_calls: List[Dict[str, Any]] = []

    class Config:
        """Configuration for this pydantic object."""

        arbitrary_types_allowed = True

    def log_states(self, data: Dict) -> None:
        self.states.update(data)

    def log_perf(self, data: Dict[str, float]) -> None:
        """Each key is a list of values indicating the latency of all the calls of the
        key in order.
        """
        for key, value in data.items():
            if key in self.perf:
                self.perf[key].append(round(value, 2))
            else:
                self.perf[key] = [round(value, 2)]

    @contextmanager
    def timer(self, key: str) -> Generator[None, None, None]:
        """Context manager to time a function call and log the latency."""
        start_time = time.time()
        try:
            yield
        finally:
            end_time = time.time()
            duration = end_time - start_time
            self.log_perf({f"latency_{key}": round(duration, 2)})

    def log_retrieval(self, data: Dict) -> None:
        self.retrieval.update(data)

    def log_llm_prompts(self, data: Dict) -> None:
        self.llm_prompts.update(data)

    def log_llm_outputs(self, data: Dict) -> None:
        self.llm_outputs.update(data)

    def log_error(self, error: BaseException, msg: Optional[str] = None) -> None:
        if isinstance(error, AgentError):
            logger.error(error.message)
            self.errors.update({error.message: error.traceback})
        else:
            msg_key = msg if msg else f"{type(error)}:{str(error)}"
            logger.error(msg_key)
            self.errors.update(
                {
                    msg_key: (
                        "".join(traceback.format_exception(error))
                        if error
                        else traceback.format_exc()
                    )
                }
            )
        if conf.DEBUG:
            logger.exception(error)

    def format_error_admin(self) -> str:
        """Show as text field in admin"""
        formatted = ""
        for msg, tb in self.errors.items():
            formatted += f"ERROR: {msg}\n"
            formatted += f"{tb}\n"
        return formatted

    def log_llm_costs(self, *costs: LlmCost | None) -> None:
        # for log only. it does not represent costs of a turn or a chat (somehow its).
        # use LlmCost.get_chat_turn_costs(chat_turn.id) for exactly chat turn llm costs.
        self.llm_costs.extend(costs)

    def log_function_call(self, data: Dict[str, Any]) -> None:
        """Log function call data including input, output, and latency in the order
        they are executed.
        """
        self.function_calls.append(data)

    def get_llm_costs_str(self) -> str:
        costs = self.llm_costs
        in_tokens = sum([c.prompt_tokens for c in costs if c is not None])
        out_tokens = sum([c.completion_tokens for c in costs if c is not None])
        total_tokens = in_tokens + out_tokens
        total_amount = round(sum([c.amount for c in costs if c is not None]), 4)
        return (
            f"Number of calls: {len(costs)}\n"
            f"Prompt tokens: {in_tokens}\n"
            f"Completion tokens: {out_tokens}\n"
            f"Total tokens: {total_tokens}\n"
            f"Total cost(USD): {total_amount}\n"
        )

    async def notify(self, msg: Any) -> None:
        for notifier in self.notifiers:
            await notifier.notify(msg)

    async def notify_streaming(self, data: str, done: bool) -> None:
        for notifier in self.notifiers:
            await notifier.notify_streaming(data, done)

    async def notify_planning(self, data: str) -> None:
        for notifier in self.notifiers:
            await notifier.notify_planning(data)

    async def notify_typing(self) -> None:
        for notifier in self.notifiers:
            await notifier.notify_typing()

    async def notify_sources(self, sources: List[FetchedSource]) -> None:
        merged_sources = list(OrderedDict({s.source: s for s in sources}).values())
        for notifier in self.notifiers:
            await notifier.notify_sources(merged_sources)

    async def notify_relevant_snippet(self, data: RelevantSnippet) -> None:
        for notifier in self.notifiers:
            await notifier.notify_relevant_snippet(data)

    async def notify_citation(self, citation: Citation) -> None:
        for notifier in self.notifiers:
            await notifier.notify_citation(citation)

    async def notify_charts(self, charts: List[Chart]) -> None:
        for notifier in self.notifiers:
            await notifier.notify_chart(charts)

    async def notify_operation(self, operation: ServerOperationEnum) -> None:
        for notifier in self.notifiers:
            await notifier.notify_operation(operation)


class Observable(BaseModel):
    observer: Observer
