from abc import ABC, abstractmethod
from decimal import Decimal
from typing import Any

from asgiref.sync import sync_to_async
from pydantic import BaseModel

from agent.config.llm import LLM_COST_PER_1K_TOKENS
from agent.enum import CostSubject, CostCategory
from agent.models import LlmCost, Cost, ChatTurn


class CostTracker(BaseModel, ABC):
    cost_subject: CostSubject
    cost_category: CostCategory

    _costs: list[Cost] = []

    def track(self, **kwargs: Any) -> Cost | None:
        cost = self.calc(**kwargs)
        if cost:
            assert not cost.amount.is_nan()
            cost.save()
            self._costs.append(cost)
        return cost

    async def atrack(self, **kwargs: Any) -> Cost | None:
        return await sync_to_async(self.track)(**kwargs)

    @abstractmethod
    def calc(self, **kwargs: Any) -> Cost | None:
        pass

    @property
    def costs(self) -> list[Cost]:
        return self._costs


class NoneCostTracker(CostTracker):
    cost_subject: CostSubject = CostSubject.NONE
    cost_category: CostCategory = CostCategory.NONE

    def calc(self, **kwargs: Any) -> Cost | None:
        return None


class LlmCostTracker(CostTracker):
    cost_subject: CostSubject = CostSubject.LLM
    chat_turn: ChatTurn | None = None

    class Config:
        arbitrary_types_allowed = True

    def calc(  # type: ignore
        self,
        model_name: str,
        prompt_tokens: int,
        completion_tokens: int,
    ) -> Cost | None:
        px = LLM_COST_PER_1K_TOKENS[model_name]
        input_amount = Decimal(px["input"] * (prompt_tokens / 1000))
        output_amount = Decimal(px["output"] * (completion_tokens / 1000))
        return LlmCost(
            chat_turn=self.chat_turn,
            user=self.chat_turn.chat.user if self.chat_turn else None,
            subject=self.cost_subject.value,
            category=self.cost_category.value,
            llm_model_name=model_name,
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            input_amount=input_amount,
            output_amount=output_amount,
            amount=input_amount + output_amount,
        )


class CostTrackable(BaseModel):
    cost_tracker: CostTracker
