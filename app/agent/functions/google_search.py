import asyncio
import itertools
from typing import Any, Dict, List

from asgiref.sync import sync_to_async

from agent.config.agent import AgentConfig
from agent.functions import Function, FunctionParameter
from agent.observer import Observer
from common.services.google.search import google_search


class GoogleSearchFunction(Function):
    """
    Search google with a list of queries and return the top k results.
    """

    name: str = "google_search"
    description: str = (
        "Search google with a list of queries and return the top k results."
    )
    param_schema: List[FunctionParameter] = [
        FunctionParameter(
            name="queries",
            type=List[str],
            description="List of queries to search google.",
            required=True,
        ),
        FunctionParameter(
            name="top_k",
            type=int,
            description="Number of top results to return.",
            required=False,
        ),
    ]

    async def _run(
        self, input_dict: Dict[str, Any], config: AgentConfig, observer: Observer
    ) -> List[Dict[str, Any]]:
        queries = input_dict["queries"]
        kwargs = input_dict.get("kwargs", {})
        top_k = input_dict.get("top_k", 10)  # top_k for each query

        results = await asyncio.gather(
            *[
                self._google_search(query, top_k=top_k, **kwargs)
                for query in queries
                if query
            ]
        )

        return [
            {"title": item["title"], "snippet": item["snippet"], "url": item["link"]}
            for item in list(itertools.chain(*results))
        ]

    async def _google_search(self, query: str, top_k: int = 10, **kwargs: Any) -> list:
        r = await sync_to_async(google_search)(query, **kwargs)
        return r[:top_k]

    def as_human_readable_run_msg(self, params: Dict[str, Any]) -> str:
        return f"Searching google with queries: {params['queries']}"
