import asyncio
import json
import logging
import threading
from datetime import datetime, timedelta
from time import perf_counter
from typing import Any, Dict, List, Optional

from agent.config.agent import PlanRetrievalAgentConfig as AgentConfig
from agent.config.agent import SearchConfigMixin
from agent.enum import PlanningTypeEnum
from agent.functions import FetcherFunction, FunctionParameter
from agent.observer import Observer
from biz import datasdk
from biz.enums import DocTypeEnum
from biz.search import IndexSearcherFactory
from biz.search.base import DOC_TYPE_TO_UPSERT_ID
from biz.structs import DocChunk
from common.utils.datetimeutils import LOCAL_TZ
from datasvc.indexing import get_index

logger = logging.getLogger(__name__)


class SearchAcrossDocsFunction(FetcherFunction):
    """Search across all kinds of documents for company"""

    name: str = "search_across_docs"
    description: str = (
        "Search all kinds of documents for specified companies. "
        "You should call this function when user asks for some documents of companies "
        "or you think what user asks can be found in what kinds of documents, "
        "like company's annual report, 10-K, 10-Q, SEC filing, earning calls transcripts, news or media presses."
        "If you cannot determine the tickers, the 'ticker_selection_prompt' parameter will prompt the user to select tickers."
    )
    param_schema: List[FunctionParameter] = [
        FunctionParameter(
            name="tickers",
            type=List[str],
            description=(
                "List of tickers to search company documents for. If the user doesn't "
                "specify companies, you should infer the companies, if they are asking "
                "about an industry or a trend/theme without specific companies, try put "
                "up to 10 most important companies in the industry. If you want to search "
                "all companies, use ['ALL']. But always try to infer the companies if possible. "
                "Note: Alphabet's ticker is GOOG."
            ),
            required=True,
        ),
        FunctionParameter(
            name="query",
            type=str,
            description=(
                "The search query you generate based on user's inquiry for "
                "searching company documents."
            ),
            required=True,
        ),
        FunctionParameter(
            name="min_date",
            type=str,
            description=(
                "The min datetime to search from. Must be in format YYYY-MM-DD. "
            ),
            required=True,
        ),
        FunctionParameter(
            name="max_date",
            type=str,
            description=(
                "The max datetime to search until. Must be in format YYYY-MM-DD. "
                "You should ALWAYS set this param to the current date in system msg."
            ),
            required=False,
        ),
    ]

    def _parse_date(
        self,
        date_str: Optional[str],
        time_str: str = "",
        default: Optional[datetime] = None,
    ) -> Optional[datetime]:
        if date_str:
            if time_str:
                return datetime.strptime(
                    date_str + f" {time_str}", "%Y-%m-%d %H:%M:%S"
                ).replace(tzinfo=LOCAL_TZ)
            else:
                return datetime.strptime(date_str, "%Y-%m-%d").replace(tzinfo=LOCAL_TZ)
        else:
            return default

    async def _gen_search_filter(
        self,
        config: SearchConfigMixin,
        input_dict: Dict[str, Any],
        doc_types: List[str],
    ) -> Dict[str, Any]:
        user_id = config.user_id
        tickers = input_dict.get("tickers")
        if tickers == ["ALL"]:
            tickers = []

        min_date = self._parse_date(
            input_dict.get("min_date"),
            time_str="00:00:01",
            default=datetime.now(tz=LOCAL_TZ) - timedelta(days=62),
        )
        max_date = self._parse_date(
            input_dict.get("max_date"),
            time_str="23:59:59",
            default=datetime.now(tz=LOCAL_TZ),
        )

        def and_clause(doc_type: str) -> List:
            clause = [
                (
                    {"user_id": {"$eq": user_id}}
                    if doc_type == DocTypeEnum.CUSTOMER and user_id > 1
                    else {}
                ),
                {"ticker": {"$in": tickers}} if tickers else {},
                {"doc_type": {"$eq": doc_type}},
                {"index_upsert_id": {"$eq": DOC_TYPE_TO_UPSERT_ID[doc_type].value}},
                (
                    {"pub_date": {"$gte": min_date.timestamp()}}
                    if min_date is not None
                    else {}
                ),
                (
                    {"pub_date": {"$lte": max_date.timestamp()}}
                    if max_date is not None
                    else {}
                ),
            ]
            # NOTE: filter certain sections of SEC filings if needed
            if config.sec_section_filter and doc_type == DocTypeEnum.SEC_FILING.value:
                clause.append({"sec_section": {"$in": config.sec_section_filter}})
            return clause

        return {"$or": [{"$and": and_clause(doc_type)} for doc_type in doc_types]}

    def _get_query(self, input_dict: Dict[str, Any], doc_type: str) -> str:
        query = input_dict["query"]
        if doc_type == DocTypeEnum.NEWS.value:
            tickers = input_dict.get("tickers", [])
            if tickers == ["ALL"]:
                scope = ""
            else:
                scope = ",".join(tickers)
            query = query + " " + scope

        return query

    async def _run(
        self,
        input_dict: Dict[str, Any],
        config: AgentConfig,
        observer: Observer,
    ) -> List[DocChunk]:
        # TODO: maybe we could return a Generator to accelerate output

        assert isinstance(config, SearchConfigMixin)

        lock = threading.Lock()
        docs_with_scores: list[tuple[Any, float]] = []
        finished: list[bool] = []

        def __inner_search(_q: str, _k: int, _f: dict, _tk: str, _dt: str) -> list:
            rc = []
            try:
                index = get_index(_dt)
                searcher = IndexSearcherFactory.create(
                    index, alpha=config.hybrid_search_alpha
                )
                _t1 = perf_counter()
                rc = asyncio.run(
                    searcher.query_with_scores(
                        query=_q,
                        top_k=_k,
                        metadata_filter=_f,
                    )
                )
                _dur = perf_counter() - _t1
                logger.debug(
                    f"{_dur} seconds - found {len(rc)} '{_dt}' doc-chunks about {_tk} for '{_q}'."
                )
            except Exception as e:
                logger.error(e)

            with lock:
                docs_with_scores.extend(rc)
                finished.append(True)

            return rc

        logger.debug(f"input_dict: {json.dumps(input_dict, indent=2)}")
        doc_types = config.doc_type_filter

        # get company name by ticker
        companies_str = ", ".join(
            [
                datasdk.company_name_by_ticker(ticker) or ticker
                for ticker in input_dict["tickers"]
            ]
        )
        await observer.notify_planning(
            f"[{PlanningTypeEnum.SEARCH.value}] Searching documents \"{input_dict['query']}\" "
            f"for companies: {companies_str} "
        )

        tasks = []
        for doc_type in doc_types:
            # TODO(data): search can be optimized:
            #   1. group filter by index
            #   2. filter use "doc_type": "$in" [ ... doc types ... ]
            #   3. parallelize search by indexes not doc types (then it will reduce to 4 instead of 7)
            search_filter = await self._gen_search_filter(
                config, input_dict, [doc_type]
            )
            count = config.num_fetch_result_per_doc_type.get(doc_type) or 1
            thread = threading.Thread(
                target=__inner_search,
                args=(
                    self._get_query(input_dict, doc_type),
                    count,
                    search_filter,
                    companies_str,
                    doc_type,
                ),
            )
            tasks.append(thread)
        # docs_with_scores = list(itertools.chain(*(await asyncio.gather(*tasks))))

        t1 = perf_counter()
        for thread in tasks:
            thread.start()
        while len(finished) < len(tasks):
            await asyncio.sleep(0.1)
        dur = perf_counter() - t1
        logger.info(
            f"Total {dur} seconds - found {len(docs_with_scores)} doc-chunks across {doc_types} "
            f"about {companies_str} on '{input_dict['query']}'"
        )

        return [
            DocChunk.from_langchain_doc(doc, score) for doc, score in docs_with_scores
        ]

    def as_human_readable_run_msg(self, params: Dict[str, Any]) -> str:
        tickers_str = ", ".join(params["tickers"])
        periods_str = (
            "" if "periods" not in params else "on " + ", ".join(params["periods"])
        )
        return f"Searching documents for {tickers_str} {periods_str} with query: '{params['query']}'"
