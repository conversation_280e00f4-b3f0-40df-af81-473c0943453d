import json
import logging
from typing import Any, Dict, List

from agent.config.agent import (
    PlanRetrievalAgentConfig as AgentConfig,
    SearchFilterAgentConfig,
)
from agent.enum import PlanningTypeEnum
from agent.functions import FetcherFunction, FunctionParameter
from agent.observer import Observer
from biz import datasdk
from biz.search import IndexSearcherFactory
from biz.structs import DocChunk
from datasvc.indexing import get_index

logger = logging.getLogger(__name__)


class SearchOneDoc(FetcherFunction):
    """Search in one document"""

    name: str = "search_one_doc"
    description: str = (
        "Search in one document for specified source with query. "
        "You should call this function when user asks for search in a specified document."
    )
    param_schema: List[FunctionParameter] = [
        FunctionParameter(
            name="query",
            type=str,
            description=(
                "The search query you generate based on user's inquiry for "
                "searching in this document."
            ),
            required=True,
        ),
        FunctionParameter(
            name="key",
            type=str,
            description=(
                "The key of the specified document. It's used to identify the only document."
            ),
            required=True,
        ),
    ]

    async def _run(
        self,
        input_dict: Dict[str, Any],
        config: AgentConfig,
        observer: Observer,
    ) -> list[DocChunk]:
        assert isinstance(config, SearchFilterAgentConfig)

        logger.debug(f"input_dict: {json.dumps(input_dict, indent=2)}")
        query = input_dict["query"]
        key = input_dict["key"]
        meta = await datasdk.aget_doc_meta_by_key(key)
        if meta is None:
            return []

        await observer.notify_planning(
            f'[{PlanningTypeEnum.SEARCH.value}] Searching "{query}" in {meta.ticker} {meta.doc_type.display_value}.'
        )

        index = get_index(meta.doc_type)
        searcher = IndexSearcherFactory.create(index, alpha=config.hybrid_search_alpha)
        search_filter = {"key": {"$eq": key}}
        logger.debug(f"Searching '{input_dict['query']}' for doc '{key}'")

        docs_with_scores = await searcher.query_with_scores(
            input_dict["query"],
            top_k=40,
            metadata_filter=search_filter,
        )

        return [
            DocChunk.from_langchain_doc(doc, score) for doc, score in docs_with_scores
        ]

    def as_human_readable_run_msg(self, params: Dict[str, Any]) -> str:
        return f"Searching '{params['query']}' in doc '{params['key']} "
