""" DEPRECATED
TODO: archive or remove"""


# import json
# import logging
# from datetime import datetime
# from typing import Any, Dict, List, Optional, Tuple
#
# from langchain.docstore.document import Document
#
# from agent.config.agent import PlanRetrievalAgentConfig as AgentConfig
# from agent.enum import PlanningTypeEnum
# from agent.functions import FetcherFunction, FunctionParamter
# from agent.observer import Observer
# from biz.enums import DocTypeEnum
# from biz.search import IndexSearcherFactory
# from biz.search.base import DOC_TYPE_TO_UPSERT_ID
# from biz.structs import FetchedNews, FetchDocJSONEncoder
#
# logger = logging.getLogger(__name__)
#
#
# class FetchNewsFunction(FetcherFunction):
#     """Fetch news from external sources"""
#
#     name: str = "fetch_news"
#     description: str = (
#         "Fetch news of specified companies."
#         "You should generate a few designed queries based on the specified question."
#         "The query you generate should be fairly specific to the question."
#         "Call this function when you want to find documents or data for supporting analyzing."
#     )
#     param_schema: List[FunctionParamter] = [
#         FunctionParamter(
#             name="queries",
#             type=List[str],
#             description="list of queries to search web.",
#             required=True,
#         ),
#         FunctionParamter(
#             name="from_date",
#             type=str,
#             description=(
#                 "limit the fetch news after this date. it's optional. "
#                 "It should be in format YYYY-MM-DD"
#             ),
#             required=False,
#         ),
#         FunctionParamter(
#             name="to_date",
#             type=str,
#             description=(
#                 "limit the fetch news before this date. it's optional. "
#                 "It should be in format YYYY-MM-DD"
#             ),
#             required=False,
#         ),
#     ]
#
#     def _parse_date(
#         self,
#         date_str: Optional[str],
#         time_str: str = "",
#         default: Optional[datetime] = None,
#     ) -> Optional[datetime]:
#         if date_str:
#             if time_str:
#                 return datetime.strptime(date_str + f" {time_str}", "%Y-%m-%d %H:%M:%S")
#             else:
#                 return datetime.strptime(date_str, "%Y-%m-%d")
#         else:
#             return default
#
#     async def _run(
#         self, input_dict: Dict[str, Any], config: AgentConfig, observer: Observer
#     ) -> List[FetchedNews]:
#         queries_strs = [f'"{q}"' for q in input_dict["queries"]]
#         await observer.notify_planning(
#             f"[{PlanningTypeEnum.SEARCH.value}] Searching web with {', '.join(queries_strs)}"
#         )
#
#         from_date = self._parse_date(input_dict.get("from_date"), time_str="00:00:01")
#         to_date = self._parse_date(input_dict.get("to_date"), time_str="23:59:59")
#         search_filter = {
#             "$or": [
#                 {
#                     "$and": [
#                         {"doc_type": {"$eq": DocTypeEnum.NEWS.value}},
#                         {
#                             "index_upsert_id": {
#                                 "$eq": DOC_TYPE_TO_UPSERT_ID[
#                                     DocTypeEnum.NEWS.value
#                                 ].value
#                             }
#                         },
#                         {
#                             "$or": [
#                                 {"published_at": {"$eq": from_date.timestamp()}},
#                                 {"published_at": {"$gt": from_date.timestamp()}},
#                             ]
#                         }
#                         if from_date is not None
#                         else {},
#                         {
#                             "$or": [
#                                 {"published_at": {"$eq": to_date.timestamp()}},
#                                 {"published_at": {"$lt": to_date.timestamp()}},
#                             ]
#                         }
#                         if to_date is not None
#                         else {},
#                     ]
#                 }
#             ]
#         }
#
#         logger.info(
#             f"Searching news with queries: {input_dict.get('queries', [])} "
#             f"and filter: {json.dumps(search_filter, indent=2, cls=FetchDocJSONEncoder)}"
#         )
#
#         docs_with_scores: List[Tuple[Document, float]] = []
#         for query in input_dict.get("queries", []):
#             searcher = IndexSearcherFactory.create(
#                 config.searcher_type,
#                 alpha=config.hybrid_search_alpha,
#             )
#             docs = await searcher.query_with_scores(
#                 query,
#                 top_k=config.news_fetch_per_query_num_results,
#                 metadata_filter=search_filter,
#             )
#             docs_with_scores.extend(docs)
#             if len(docs_with_scores) >= config.news_fetch_total_limit:
#                 docs_with_scores = docs_with_scores[: config.news_fetch_total_limit]
#                 break
#
#         return [
#             FetchedNews.from_langchain_doc(doc, score)
#             for doc, score in docs_with_scores
#         ]
#
#     def as_human_readable_run_msg(self, params: Dict[str, Any]) -> str:
#         return f"Searching web with queries: {params['queries']}"
