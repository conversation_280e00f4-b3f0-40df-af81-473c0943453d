""" DEPRECATED
TODO: archive or remove"""

# import logging
# from datetime import datetime
# from typing import Any, Dict, List
#
# from asgiref.sync import sync_to_async
#
# from agent.config.agent import PlanRetrievalAgentConfig as AgentConfig
# from agent.functions import FetcherFunction, FunctionParamter
# from agent.models import Metric
# from agent.observer import Observer
# from biz.constants import SUPPORTED_UNIQUE_TICKER_SET
# from datasvc.constants import GENERAL_FINANCIAL_METRICS, SEGMENT_FINANCIAL_METRICS
#
# logger = logging.getLogger(__name__)
#
#
# class FetchNumbersFunction(FetcherFunction):
#     """Experimental. Fetch metrics/numbers from local data store."""
#
#     name: str = "fetch_numbers"
#     short_desc: str = (
#         "Fetch metrics (financials, business metrics etc.) of specified companies. "
#     )
#
#     description: str = (
#         "Fetch metrics (financials, business metrics etc.) of specified companies. "
#         "You should call this function when user asks for numbers "
#     )
#     param_schema: List[FunctionParamter] = [
#         FunctionParamter(
#             name="tickers",
#             type=List[str],
#             description="list of tickers to fetch numbers for",
#             required=True,
#         ),
#         FunctionParamter(
#             name="metrics",
#             type=List[str],
#             description=(
#                 "list of metrics to fetch. Here are the exact list of metrics."
#                 "For segment of revenue (such as products revenue, service revenue, retail revenue, geo revenue),"
#                 f"try fetching: {SEGMENT_FINANCIAL_METRICS} ."
#                 f"otherwise, fetch: {GENERAL_FINANCIAL_METRICS} ."
#                 "If what user asks is NOT in this list, just output empty list []."
#                 "Don't output something the user doesn't ask!"
#             ),
#             required=True,
#         ),
#     ]
#
#     async def _run(  # noqa: C901
#         self, input_dict: Dict[str, Any], config: AgentConfig, observer: Observer
#     ) -> List[Dict[str, Any]]:
#         metrics = input_dict["metrics"]
#         tickers = [t for t in input_dict["tickers"] if t in SUPPORTED_UNIQUE_TICKER_SET]
#
#         tickers_to_numbers: Dict[str, Dict[str, dict]] = {}
#         async for metric in await sync_to_async(Metric.objects.filter)(
#             ticker__in=tickers, name__in=metrics
#         ):
#             if metric.ticker not in tickers_to_numbers:
#                 tickers_to_numbers[metric.ticker] = {}
#             if metric.name in SEGMENT_FINANCIAL_METRICS:
#                 data: dict[str, dict[str, Any]] = {}
#                 for dt, seg_data in metric.data.items():
#                     for seg, value in seg_data.items():
#                         if seg not in data:
#                             data[seg] = {}
#                         data[seg][dt] = value
#                 tickers_to_numbers[metric.ticker][metric.name] = data
#             else:
#                 tickers_to_numbers[metric.ticker][metric.name] = metric.data
#
#         rc = []
#         for ticker in tickers:
#             for metric in metrics:
#                 if metric in SEGMENT_FINANCIAL_METRICS:
#                     for seg, seg_data in tickers_to_numbers[ticker][metric].items():
#                         rc.append(
#                             {
#                                 "metric": metric,
#                                 "metric_display_name": f"{seg}",
#                                 "ticker": ticker,
#                                 "data": sorted(
#                                     [
#                                         {"name": date, "value": value}
#                                         for date, value in seg_data.items()
#                                     ],
#                                     key=lambda x: datetime.strptime(
#                                         x["name"], "%Y-%m-%d"
#                                     ),
#                                 ),
#                             }
#                         )
#                 else:
#                     rc.append(
#                         {
#                             "metric": metric,
#                             "metric_display_name": f"{metric}",
#                             "ticker": ticker,
#                             "data": sorted(
#                                 [
#                                     {"name": date, "value": value}
#                                     for date, value in tickers_to_numbers[ticker][
#                                         metric
#                                     ].items()
#                                 ],
#                                 key=lambda x: datetime.strptime(x["name"], "%Y-%m-%d"),
#                             ),
#                         }
#                     )
#         return rc
#
#     def as_human_readable_run_msg(self, params: Dict[str, Any]) -> str:
#         return (
#             f"Fetching numbers for {params['tickers']} "
#             f"with metrics {params['metrics']}"
#         )
