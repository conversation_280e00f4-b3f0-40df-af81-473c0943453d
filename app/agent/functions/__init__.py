import logging
import re
from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, TypeVar

from pydantic import BaseModel, validator

from agent.config.agent import PlanRetrievalAgentConfig as AgentConfig
from agent.exceptions import InvalidFuncParamError
from agent.observer import Observer
from biz.structs import DocChunk, Doc

logger = logging.getLogger(__name__)


FetchedDataT = TypeVar("FetchedDataT", bound=DocChunk | Doc)


class FunctionParameter(BaseModel):
    name: str
    type: (
        Any  # NOTE: Only support str, int, List[str], List[int], Dict[str, Any] for now
    )
    description: str
    required: bool

    @validator("type", pre=True, always=True)
    def check_type(cls, type_value: Any) -> Any:
        allowed_types = {str, int, List[str], List[int], Dict[str, Any]}
        assert (
            type_value in allowed_types
        ), f"type must be one of {allowed_types}, but got {type_value}"
        return type_value


class Function(ABC, BaseModel):
    name: str
    description: str
    param_schema: List[FunctionParameter]

    def validate_param(self, input_dict: Dict[str, Any]) -> bool:
        """Validate the function input according to param_schema"""
        for param in self.param_schema:
            if param.name not in input_dict:
                if param.required:
                    return False
                else:
                    continue

            # workaround for generic types like List
            match = re.match(r"(?:\w+\.)?(\w+)(?:\[(\w+)\])?", str(param.type))
            if match:
                generic_type, inner_type = match.groups()
                if generic_type == "List":
                    if not all(
                        isinstance(item, eval(inner_type))
                        for item in input_dict[param.name]
                    ):
                        return False
            # For non-generic types
            else:
                if param.name in input_dict and not isinstance(
                    input_dict[param.name], param.type
                ):
                    return False
        return True

    @abstractmethod
    async def _run(
        self,
        input_dict: Dict[str, Any],
        config: AgentConfig,
        observer: Observer,
    ) -> Any:
        """Run the function"""

    async def run(
        self,
        input_dict: Dict[str, Any],
        config: AgentConfig,
        observer: Observer,
    ) -> Any:
        logger.info(f"Calling function: {self.name} ...")
        if not self.validate_param(input_dict):
            raise InvalidFuncParamError(
                f"Invalid parameters {input_dict} for function: {self.name}"
            )
        return await self._run(input_dict, config, observer)

    def to_dict(self) -> Dict:
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                param.name: {
                    "type": str(param.type),
                    "description": param.description,
                    "required": param.required,
                }
                for param in self.param_schema
            },
        }

    @abstractmethod
    def as_human_readable_run_msg(self, params: Dict[str, Any]) -> str:
        """Generate a human readable message for a run of this function
        with the given parameters. Used for giving user informative feedback.
        """


class FetcherFunction(Function, Generic[FetchedDataT]):
    """A function that fetches/searches/retreives data from external sources"""

    @abstractmethod
    async def _run(
        self,
        input_dict: Dict[str, Any],
        config: AgentConfig,
        observer: Observer,
    ) -> List[FetchedDataT]:
        """Run the function and fetch some data from external sources"""

    async def run(
        self, input_dict: Dict[str, Any], config: AgentConfig, observer: Observer
    ) -> List[FetchedDataT]:
        """Fetch data from external sources and return nothing if there is an error"""
        logger.info(f"Calling fetcher function: {self.name} ...")
        if not self.validate_param(input_dict):
            observer.log_error(
                InvalidFuncParamError(
                    f"Invalid parameters {input_dict} for function: {self.name}"
                )
            )
            return []
        try:
            docs = await self._run(input_dict, config, observer)
            logger.debug(f"{self.__class__.__name__} fetched {len(docs)} docs.")
            return docs
        except Exception as e:
            observer.log_error(e)
            return []


def get_all_functions() -> Dict[str, FetcherFunction]:
    # from agent.functions.fetch_news import FetchNewsFunction

    # from agent.functions.fetch_financials import FetchFinancialsFunction
    # from agent.functions.search_company_doc import SearchCompanyDocFunction
    # from agent.functions.fetch_numbers import FetchNumbersFunction
    from agent.functions.search_across_docs import SearchAcrossDocsFunction
    from agent.functions.search_one_doc import SearchOneDoc
    from agent.functions.load_full_doc import LoadFullDocFunction
    from agent.functions.google_search import GoogleSearchFunction
    from agent.functions.inspect_webpage import InspectWebpageFunction

    return {
        # TODO: deprecate below functions in favor of search_company_doc
        # "fetch_news": FetchNewsFunction(),
        # "fetch_financials": FetchFinancialsFunction(),
        # "search_company_doc": SearchCompanyDocFunction(),
        # "fetch_numbers": FetchNumbersFunction(),
        # "search_earning_calls": SearchEarningCalls(),
        # "search_sec_filings": SearchSECFiling(),
        "search_across_docs": SearchAcrossDocsFunction(),
        "search_one_doc": SearchOneDoc(),
        "load_full_doc": LoadFullDocFunction(),
        "google_search": GoogleSearchFunction(),
        "inspect_webpage": InspectWebpageFunction(),
    }


ALL_FUNCTIONS = get_all_functions()
