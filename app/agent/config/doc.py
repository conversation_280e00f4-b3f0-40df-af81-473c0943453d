from biz.enums import <PERSON><PERSON>ype<PERSON>num

# # TODO: make FILTER and DOC TYPE same
# DOC_FILTER_TO_DOC_TYPE = {
#     "NEWS": DocTypeEnum.NEWS.value,
#     "FINANCIAL_STATEMENT": DocTypeEnum.SEC_FILING.value,
#     "EARNING_CALL": DocTypeEnum.EARNING_CALL.value,
#     "PRESS_RELEASE": DocTypeEnum.PRESS_RELEASE.value,
#     # "SOCIAL_MEDIA": DocTypeEnum.SOCIAL_MEDIA.value,
#     "YOUTUBE": DocTypeEnum.YOUTUBE.value,
#     "TWITTER": DocTypeEnum.TWITTER.value,
#     # "THIRTEEN_F": DocTypeEnum.THIRTEEN_F.value,
#     # "REVERSE_THIRTEEN_F": DocTypeEnum.REVERSE_THIRTEEN_F.value,
#     "CONFERENCE": DocTypeEnum.CONFERENCE.value,
# }


# DOC_TYPE_CLASS_MAPPING = {
#     DocTypeEnum.NEWS: {"doc_class": Doc, "model_class": News},
#     DocTypeEnum.EARNING_CALL: {
#         "doc_class": FetchedEarningCall,
#         "model_class": EarningCall,
#     },
#     DocTypeEnum.SEC_FILING: {
#         "doc_class": FetchedSECFiling,
#         "model_class": SECFiling,
#     },
#     DocTypeEnum.PRESS_RELEASE: {
#         "doc_class": FetchedPressRelease,
#         "model_class": PressRelease,
#     },
#     # DocTypeEnum.SOCIAL_MEDIA: {
#     #     "doc_class": FetchedSocialMedia,
#     #     "model_class": SocialMedia,
#     # },
#     DocTypeEnum.YOUTUBE: {
#         "doc_class": DocChunk,
#         "model_class": YoutubeVideo,
#     },
#     DocTypeEnum.TWITTER: {
#         "doc_class": DocChunk,
#         "model_class": Tweet,
#     },
#     # DocTypeEnum.THIRTEEN_F: {
#     #     "doc_class": FetchedThirteenF,
#     #     "model_class": ThirteenF,
#     # },
#     # DocTypeEnum.REVERSE_THIRTEEN_F: {
#     #     "doc_class": FetchedThirteenF,  # TODO Split out the class
#     #     "model_class": ReversedThirteenF,
#     # },
#     DocTypeEnum.CONFERENCE: {
#         "doc_class": FetchedConference,
#         "model_class": Conference,
#     },
# }


# DOC_TYPE_TO_FUNCTIONS = {
#     "_DEFAULT": ["fetch_financials"],
#     "NEWS": ["fetch_news"],
#     "FINANCIAL_STATEMENT": ["search_company_doc"],
#     "EARNING_CALL": ["search_company_doc"],
#     "PRESS_RELEASE": ["search_across_doc"],
#     "SOCIAL_MEDIA": ["search_across_doc"],
#     "YOUTUBE": ["search_across_doc"],
#     "TWITTER": ["search_across_doc"],
#     "CONFERENCE": ["search_across_doc"],
# }

DOC_TYPE_TO_FUNCTIONS = {
    DocTypeEnum.NOT_SPECIFIED: ["search_across_doc"],  # default
    DocTypeEnum.EARNING_CALL.value: ["search_across_doc"],
    DocTypeEnum.EVENT_TRANSCRIPT.value: ["search_across_doc"],
    DocTypeEnum.SEC_FILING.value: ["search_across_doc"],
    DocTypeEnum.CONFERENCE.value: ["search_across_doc"],
    DocTypeEnum.PRESS_RELEASE.value: ["search_across_doc"],
    DocTypeEnum.NEWS.value: ["search_across_doc"],
    DocTypeEnum.YOUTUBE.value: ["search_across_doc"],
    DocTypeEnum.TWITTER.value: ["search_across_doc"],
    DocTypeEnum.CUSTOMER.value: ["search_across_doc"],
}
