"""ARCHIVED"""
# from collections import OrderedDict
#
# GUIDANCE_LIST = [
#     {
#         "name": "aspects",  # meaningful, guidance name should be enum in BE code
#         "show_user_input_box": False,
#         "is_termination": False,
#         "input": None,
#         "output": [
#             {
#                 "key": "aspect",  # meaningful, FE detect
#                 "type": "str",  # str|num|list|dict
#             },
#             # {"key": "doc_types", "type": "list"},
#         ],
#         "parent": None,
#         "children": [
#             "earning_call_analysis",
#             "search_in_management_voice",
#             "event_analysis",
#             "fetch_numbers",
#             "comparable",
#         ],
#     },
#     # ----- earning call analysis -----
#     {
#         "name": "earning_call_analysis",
#         "show_user_input_box": False,
#         "is_termination": False,
#         "input": None,
#         "output": [
#             {
#                 "key": "action",
#                 "type": "str",
#             },
#         ],
#         "parent": "aspects",
#         "children": [
#             "digest_earning_call",
#             "search_across_earning_call",
#         ],
#     },
#     {
#         "name": "digest_earning_call",
#         "show_user_input_box": False,
#         "is_termination": True,
#         "input": None,
#         "output": [
#             {
#                 "key": "ticker",
#                 "type": "str",
#             },
#             {"key": "year", "type": "int"},
#             {"key": "quarter", "type": "int"},
#         ],
#         "parent": "earning_call_analysis",
#         "children": None,
#     },
#     {
#         "name": "search_across_earning_call",
#         "show_user_input_box": False,
#         "is_termination": True,
#         "input": None,
#         "output": None,
#         "parent": "earning_call_analysis",
#         "children": None,
#     },
#     # ----- search in management voice -----
#     {
#         "name": "search_in_management_voice",
#         "show_user_input_box": False,
#         "is_termination": False,
#         "input": None,
#         "output": None,
#         "parent": "aspects",
#         "children": None,
#     },
#     # ----- event analysis -----
#     {
#         "name": "event_analysis",
#         "show_user_input_box": False,
#         "is_termination": False,
#         "input": None,
#         "output": None,
#         "parent": "aspects",
#         "children": None,
#     },
#     # ----- fetch number -----
#     {
#         "name": "fetch_numbers",
#         "show_user_input_box": False,
#         "is_termination": True,
#         "input": None,
#         "output": None,
#         "parent": "aspects",
#         "children": None,
#     },
#     # ----- comparable -----
#     {
#         "name": "comparable",
#         "show_user_input_box": False,
#         "is_termination": True,
#         "input": None,
#         "output": None,
#         "parent": "aspects",
#         "children": None,
#     },
# ]
#
# GUIDANCE_DICT = OrderedDict({g["name"]: g for g in GUIDANCE_LIST})
# assert len(GUIDANCE_DICT) == len(GUIDANCE_LIST)
#
# GUIDANCE_NAMES = set(GUIDANCE_DICT.keys())
#
# GUIDANCE_INTENT_MAPPING = {
#     "aspects": None,
#     "earning_call_analysis": None,
#     "search_in_management_voice": None,
#     "event_analysis": "analysis",
#     "fetch_numbers": "fetch_numbers",
#     "comparable": "compare_numbers",
#     "digest_earning_call": "load_one_doc",
#     "search_across_earning_call": "search_filter",
# }
# assert len(GUIDANCE_INTENT_MAPPING) == len(GUIDANCE_LIST)
