# import json
# from logging import getLogger
# from typing import Dict, Iterable, List
#
# from agent.config.doc import DOC_TYPE_TO_FUNCTIONS
# from agent.functions import ALL_FUNCTIONS
#
# logger = getLogger(__name__)
#
#
# OUTPUT_FORMAT = """
# {
#     "thoughts": "<str>",
#     "company_names": <array of company names>,
#     "company_tickers": <array of company tickers>,
#     "all_function_calls": [
#         {
#             "function_name": <function_name>,
#             "parameters": {
#                 <parameter_name>: <parameter_value>
#             }
#             ...
#         }
#     ],
# }
# """
#
#
# COMPANY_TICKERS = """Meta or Facebook: META, Google/Alphabet: GOOG)"""
#
#
# def get_ai_functions_str(doc_type_filter: Iterable[str]) -> str:
#     funcs = []
#
#     for category in [*doc_type_filter, ""]:
#         names = DOC_TYPE_TO_FUNCTIONS.get(category, [])
#         logger.error(
#             f'invalid function category "{category}" provided.'
#         ) if not names else None
#         assert len(names) > 0
#         for name in names:
#             func = ALL_FUNCTIONS.get(name)
#             funcs.append(func) if (func and func not in funcs) else None
#
#     return json.dumps([f.to_dict() for f in funcs], indent=4)
#
#
# def get_fetch_planner_prompt(doc_type_filter: Iterable[str]) -> str:
#     func_str = get_ai_functions_str(doc_type_filter)
#
#     # NOTE: Explicitly let LLM do entity extraction to improve accuracy
#     return f"""
#     You are an AI financial analyst designed to accelerate users' financial research.
#     You should take user's inquiry, then follow the below steps:
#     1. (Thoughts) You should think like a professional financial analyst and think about what data to fetch based on the function definitions(your tools) below to answer the user's inquiry. You should break down user's inquiry into research workflow if necessary. Explicitly output your thoughts in the output.
#     2. (Companies and their tickers) Based on user's query and your thought, determine which company and their tickers for which you should fetch data.
#     3. (Planning) Based on your thoughts and company tickers, think step by step how to gather the data needed to answer the question by calling the provided Functions. Make sure you figure out the correct period based on user's query. (All Functions' definitions are provided below)
#
#     ## Function definition(in Python dict):
#     {func_str}
#
#     ## Some Companies and their tickers
#     {COMPANY_TICKERS}
#
#     You should output in JSON format like this (exactly in JSON format, start with {{ and end with }}):
#     {OUTPUT_FORMAT}
#     """
#
#
# FEW_SHOTS_MSGS: List[Dict[str, str]] = [
#     {
#         # "role": "user",
#         "role": "system",
#         "name": "example_user",
#         "content": "How many employees does meta have? QoQ since lay off plan announcement.",
#     },
#     {
#         # "role": "assistant",
#         "role": "system",
#         "name": "example_assistant",
#         "content": """
# {
#     "thoughts": "To answer this question, I need to find out the number of employees Meta has and how it has changed quarter over quarter since the layoff plan announcement. The number of employees is usually disclosed in the company's 10-K or 10-Q filings. Therefore, I need to search the company documents for Meta with key words 'number of employees', 'headcount', 'lay off plan'.",
#     "company_names": ["Meta"],
#     "company_tickers": ["META"],
#     "all_function_calls": [
#         {
#             "function_name": "search_company_doc",
#             "parameters": {
#                 "tickers": ["META"],
#                 "query": "number of employees, headcount, lay off"
#             }
#         }
#     ]
# }""",
#     },
#     #     {
#     #         "role": "user",
#     #         "content": "Was meta guidance range of this quarter wider than the last one?",
#     #     },
#     #     {
#     #         "role": "assistant",
#     #         "content": """
#     # {
#     #     "thoughts": "To answer this question, I need to find out the guidance range for Meta for this quarter and the last one. The guidance range is usually disclosed in the company's earnings call transcripts or 10-Q filings. Therefore, I need to search the company documents for Meta with key words 'guidance', 'forecast', 'projection'. Since the current date time is 2023-12-10 and Q4's earning is not out yet so I need to search for the documents for the current quarter (2023-Q3) and the last quarter (2023-Q2).",
#     #     "company_names": ["Meta"],
#     #     "company_tickers": ["META"],
#     #     "all_function_calls": [
#     #         {
#     #             "function_name": "search_company_doc",
#     #             "parameters": {
#     #                 "tickers": ["META"],
#     #                 "query": "guidance, outlook, projection, anticipate"
#     #             }
#     #         }
#     #     ]
#     # }""",
#     #     },
# ]
