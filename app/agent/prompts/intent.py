from agent.config.intent import INTENTS

INTENTS_STR = "\n".join(
    f"{i+1}. [{intent.name}]: {intent.description}" for i, intent in enumerate(INTENTS)
)

OUTPUT_FORMAT = """
{"intent": <intent_string>}
"""

INTENT_CLASSIFIER_PROMPT = f"""
You are an AI financial analyst designed to accelerate users' financial research.
You should take user's inquiry along with the chat history, then Classify the users' intent into one of the following categories:
{INTENTS_STR}

You should output in JSON format like this:
{OUTPUT_FORMAT}
"""


INTENT_FEW_SHOTS = [
    {
        "role": "user",
        "content": "Summarize the key points of adobe 2023 Q3 earning call, including all details about firefly.",
    },
    {
        "role": "assistant",
        "content": """{"intent": "summarize_with_query"}""",
    },
    {
        "role": "user",
        "content": "Summarize the latest earning call of tesla.",
    },
    {
        "role": "assistant",
        "content": """{"intent": "summarize_no_query"}""",
    },
    {
        "role": "user",
        "content": "What are the consumer spending trends disclosed in earning call?",
    },
    {
        "role": "assistant",
        "content": """{"intent": "search_filter"}""",
    },
    {
        "role": "user",
        "content": "Wrap up latest market view on Tesla.",
    },
    {
        "role": "assistant",
        "content": """{"intent": "analysis"}""",
    },
    {
        "role": "user",
        "content": "What's the major debates on BA now",
    },
    {
        "role": "assistant",
        "content": """{"intent": "analysis"}""",
    },
    {
        "role": "user",
        "content": "what do you think about Terumo",
    },
    {
        "role": "assistant",
        "content": """{"intent": "vague"}""",
    },
]
