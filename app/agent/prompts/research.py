from agent.functions.google_search import GoogleSearchFunction
from agent.functions.inspect_webpage import InspectWebpageFunction


# This is used to any prompt that extracts field data from scraped webpages
def FIELD_DATA_RESPONSE_FORMAT(with_source: bool = False) -> dict:
    properties = {
        "field_name": {"type": "string"},
        "field_value": {
            "type": ["string", "null"],
            "description": "The value of the field, or null if you can't find the value in the input webpages. When outputing numbers like followers, likes, etc, you should output the number in the format of 80K, 1M, etc.",
        },
    }
    required = ["field_name", "field_value"]

    if with_source:
        properties.update(
            {
                "source_title": {"type": "string"},
                "source_url": {"type": "string"},
            }
        )
        required.extend(["source_title", "source_url"])

    return {
        "type": "array",
        "description": "List of Dicts containing field names, values from schema and the source title and url of the webpage containing this information. Make sure you include ALL of the fields other than the name and url in the schema.",
        "items": {
            "type": "object",
            "properties": properties,
            "required": required,
            "additionalProperties": False,
        },
    }


# NOTE(2024-10-21): Try using separate agents for google search and inspect webpage
def google_search_sys_prompt(
    task: str, target_entity_definition: str, schema_str: str
) -> str:
    return f"""You are a web research specialist and your job specifically is to search google. The goal is that given a task, a target entity definition and a schema, you need to find relevant entities that match the target entity definition.

Task: {task}
Target entity definition: {target_entity_definition}
Schema: {schema_str}

You are also given the search results based on the previously generated queries by yourself from the user message.

Your task is:
1. At the start (no search results given), generate a list of queries to search google.
2. After you get a list of search results, analyze each and every single search result item and decide for each:
    a. If the search result item is a potential target entity, think about whether it matches the target entity definition. If yes, you should add the target entity to the final table along with the reason of why you think this entity matches the task and schema and also add the extracted fields as defined in the schema (if some fields are not available, just leave them empty/None, don't fill in any string).
    b. If the search result item is some useful website that might contain useful information about the target entity (e.g. website lists out bunch of target entities or a directory or a blog post recommending target entities ...), you should add the website to the queue to be inspected further. You should add ALL of these kind of websites to the queue by outputing multiple InspectWebpageFunction actions.
    c. If the search result item is not useful, you should ignore it.
3. After this step, you should also think about the analysis of the search results and decide:
    a. If the search results contain either the target entities or useful websites, you should output "next_page" as "next_action".
    b. If the search results don't contain either the target entities or useful websites, you should generate a new list of queries to search google by outputing "generate_new_queries" as "next_action" and a list of queries to search google in "queries_to_search_google".

Please analyze the search results and provide your response in JSON format."""


GOOGLE_SEARCH_RESPONSE_FORMAT = {
    "type": "json_schema",
    "json_schema": {
        "name": "google_search_response_format",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "analysis_of_search_results": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "search_result_item_index": {"type": "integer"},
                            "thoughts": {
                                "type": "string",
                                "description": "Your thoughts on whether this search result item is a target entity, a useful website, or not useful",
                            },
                            "type_of_search_result_item": {
                                "type": "string",
                                "enum": [
                                    "target_entity",
                                    "useful_website",
                                    "not_useful",
                                ],
                            },
                            "entity_to_add": {
                                "type": ["object", "null"],
                                "description": "Include details only if this result item is a target entity. Use null if not applicable.",
                                "properties": {
                                    "name": {"type": "string"},
                                    "url": {"type": "string"},
                                    "why_it_matches_task_and_schema": {
                                        "type": "string"
                                    },
                                    "field_data": FIELD_DATA_RESPONSE_FORMAT(
                                        with_source=False
                                    ),
                                },
                                "required": [
                                    "name",
                                    "url",
                                    "why_it_matches_task_and_schema",
                                    "field_data",
                                ],
                                "additionalProperties": False,
                            },
                            "url_to_inspect": {
                                "type": ["string", "null"],
                                "description": "URL of the website to inspect. Use null if not applicable.",
                            },
                        },
                        "required": [
                            "search_result_item_index",
                            "thoughts",
                            "type_of_search_result_item",
                            "entity_to_add",
                            "url_to_inspect",
                        ],
                        "additionalProperties": False,
                    },
                },
                "thoughts_about_next_action": {
                    "type": "string",
                    "description": "If the search results contain some good results (either the target entities or useful websites), you should say you will inspect further the relevant websites and take in the next page of search results. If the search results are full of not useful results, you should generate a new list of queries to search Google.",
                },
                "next_action": {
                    "type": "string",
                    "enum": ["next_page", "generate_new_queries"],
                },
                "queries_to_search_google": {
                    "type": "array",
                    "description": "Leave empty if you decide on the next page",
                    "items": {"type": "string"},
                },
            },
            "required": [
                "analysis_of_search_results",
                "thoughts_about_next_action",
                "next_action",
                "queries_to_search_google",
            ],
            "additionalProperties": False,
        },
    },
}


# Inspect a webpage that might help us find multiple target entities
def inspect_webpage_sys_prompt(
    task: str, target_entity_definition: str, schema_str: str
) -> str:
    return f"""
    You are a web research specialist and your job specifically is to inspect a webpage. The goal is that given a task, a target entity definition and a schema, you need to find relevant entities that match the target entity definition.

    Task: {task}
    Target entity definition: {target_entity_definition}
    Schema: {schema_str}

    You are also given the url of a webpage and the scraped data of the webpage to inspect from the user message.
    The scraped data is in Markdown format. For example, '[Contact Us](/contact-us "Contact CaptiveAire")' means <a href="/contact-us" title="Contact CaptiveAire">Contact Us</a>, which means a link to the webpage https://<this_webpage_url>/contact-us.

    Your task is:
    1. Determine what kind of webpage this is and output in "page_type". There are 3 types of webpages:
        a) a webpage that might list out target entities, like directory, blog post, article, etc. In this case, output "list_of_entities" in "page_type".
        b) a webpage that is the homepage of a single target entity. In this case, output "single_entity" in "page_type".
        c) a webpage that neither has target entities nor is the homepage of a single target entity, but contains other useful links that might contain useful information about the target entities. In this case, output "other_useful_links" in "page_type".
    2. Inspect the webpage and extract the relevant entities that match the target entity definition (you should think about each entity one by one and decide whether it matches the target entity definition and give your reason if you think it's a match). If the page is "list_of_entities" then extract multiple entities. If the page is "single_entity" then extract only one entity. If the page is "other_useful_links", don't extract anything, just leave "entities_found" empty/null.
    3. If the page is "list_of_entities" or "other_useful_links", extract the useful links to be inspected further that might contain useful information about the target entities (like the next page or related page of the same website, e.g. "https://www.hvacinformed.com/companies/directory.html?page=2"). Make sure you extract the full url that starts with "https://". If the page is "single_entity", don't extract anything, just leave "urls_to_inspect_next" empty.

    NOTE: if you are not sure about the url and field values of the target entities (or there is no clear mention of the url and field values in the webpage), you should leave them empty/None instead of filling in something that might be wrong.
    Please output in the given JSON format.
    NOTE: Be careful and make sure the entities found are actually target entities that match the target entity definition. Don't include articles or websites or anything else that are not target entities.
    """


INSPECT_WEBPAGE_RESPONSE_FORMAT = {
    "type": "json_schema",
    "json_schema": {
        "name": "inspect_webpage",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "page_type": {
                    "type": "string",
                    "enum": ["list_of_entities", "single_entity"],
                },
                "entities_found": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {
                                "type": "string",
                                "description": "Name of the entity found",
                            },
                            "url": {
                                "type": ["string", "null"],
                                "description": "URL of the entity's webpage. Use null if not available.",
                            },
                            "why_it_matches_task_and_schema": {
                                "type": "string",
                                "description": "Explanation of why this entity matches target entity definition. Don't include things are that not target entities.",
                            },
                            "field_data": FIELD_DATA_RESPONSE_FORMAT(with_source=False),
                        },
                        "required": [
                            "name",
                            "url",
                            "why_it_matches_task_and_schema",
                            "field_data",
                        ],
                        "additionalProperties": False,
                    },
                },
                "urls_to_inspect_next": {
                    "type": "array",
                    "items": {
                        "type": "string",
                        "description": "The full list of URLs to inspect in the next iteration (starts with https://). Leave empty if page_type is single_entity",
                    },
                },
            },
            "required": ["page_type", "entities_found", "urls_to_inspect_next"],
            "additionalProperties": False,
        },
    },
}


def enrich_from_scraped_webpages_sys_prompt(task: str, schema_str: str) -> str:
    return f"""
    You are a web research specialist and your job specifically is to evaluate and enrich the input entity based on the input task, schema and criteria by reading, evaluating and extracting relevant information from the scraped data of its webpages.

    Task: {task}
    Schema and criteria: {schema_str}

    You are also given the entity's name and the scraped data of this potential entity's webpages(multiple relevant pages like Home, About, Contact, etc.) to read from the user message.
    The scraped data is in Markdown format. For example, '[Contact Us](/contact-us "Contact CaptiveAire")' means <a href="/contact-us" title="Contact CaptiveAire">Contact Us</a>, which means a link to the webpage https://this_webpage_url/contact-us.

    Your task is:
    1. Inspect the scraped webpages and determine if the target entity matches the given task and criteria. Give your evaluation and reasoning in the "evaluation_to_criteria" field.
    2. If the target entity matches the task and criteria, extract the relevant information from the webpage based on the schema. Try to fill in all the fields in the schema. If some fields are not mentioned, just output empty string "", don't imagine yourself. When filling the fields, you should always give the source url that contains the information as reference.

    Please output in JSON format as defined in response format.
    """


ENRICH_FROM_SCRAPED_WEBPAGES_RESPONSE_FORMAT = {
    "type": "json_schema",
    "json_schema": {
        "name": "enrich_from_scraped_webpages",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "entity": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string", "description": "Name of the entity"},
                        "evaluation_to_criteria": {
                            "type": "array",
                            "description": "Evaluation of whether this entity matches the criteria. For each criterion in the criteria list, you should give whether it's a match and the reasoning. Make sure the list of items have the same criteria as the criteria list.",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "criteria_name": {
                                        "type": "string",
                                        "description": "Name of the criterion",
                                    },
                                    "reason": {
                                        "type": "string",
                                        "description": "Reasoning of whether the criterion is met. You must give detailed reasoning with evidence from the scraped information. If you are not sure or can't find evidence in the scraped information, you should output 'unknown'.",
                                    },
                                    "match": {
                                        "type": "string",
                                        "description": "Whether the criterion is met, should be 'yes', 'no' or 'unknown' if you are not sure",
                                    },
                                },
                                "required": ["criteria_name", "match", "reason"],
                                "additionalProperties": False,
                            },
                        },
                        "field_data": FIELD_DATA_RESPONSE_FORMAT(with_source=True),
                    },
                    "required": [
                        "name",
                        "evaluation_to_criteria",
                        "field_data",
                    ],
                    "additionalProperties": False,
                },
            },
            "required": ["entity"],
            "additionalProperties": False,
        },
    },
}


# NOTE(2024-10-20): First attempt to use a single agent to do both google search and inspect webpage
def gen_research_sys_prompt(task: str, schema_str: str) -> str:
    funcs_str = "\n".join(
        str(func.to_dict())
        for func in [GoogleSearchFunction(), InspectWebpageFunction()]
    )
    return f"""
You are a web research specialist. You are given a task of:
{task}
The final output of this task is a table with rows being the entities described in the task and columns being:
{schema_str}

Here are the functions(i.e. actions) you could take next (you could add multiple actions to the queue to be taken next):
{funcs_str}

Now you should take the past actions, current action and current action's result from the user message as input context, read through the current action's result, and decide what to do next according to:

### Case 1
If the current action's result is google search result (contains a list of search result items with title, url and snippet):
you should carefully read the search result items one by one and for each search result item. If:
1. the search result item is a target entity, you should add the target entity to the final table along with the extracted fields as defined in the schema (if some fields are not available, just leave them empty/None, don't fill in any string). There will be other agents who will take over to inspect further with each entity and you shouldn't take any action to inspect them here.
2. the search result item is some useful website that might contain useful information about the target entity (e.g. website lists out bunch of target entities or a directory or a blog post recommending target entities ...), you should add the website to the queue to be inspected further. You should add ALL of these kind of websites to the queue by outputing multiple InspectWebpageFunction actions.
3. the search result item is not useful, you should ignore it.
IMPORTANT: you should output your thoughts and what to do with every single search result item in your thoughts. Don't skip any search result item.

### Case 2
If the current action's result is inspect webpage result (scraped data from the webpage):
you should carefully read the scraped content, output your understanding about the webpage in your thoughts and:
1. Extract any target entities from the webpage and add them to the final table along with the extracted fields as defined in the schema (if some fields are not available, just leave them empty/None, don't fill in any string). There will be other agents who will take over to inspect further with each entity and you shouldn't take any action to inspect them here.
2. Extract any useful websites from the webpage and add them to the queue to be inspected further (such as next page of the same website). You should add ALL of these kind of websites to the queue by outputing multiple InspectWebpageFunction actions.

You should output in the following JSON format:
{{
    "thoughts_about_current_action_result": "<your understanding and description of the current action's result, and your thoughts on whether the current action's result already contains target entities>",
    "entities_found": [
        {{
            "name": "<name of the entity>",
            "url": "<url of the entity>",
            "field_value_by_name": {{
                "<field_name>": "<field_value>",
                ...
            }}
        }},
        ...
    ],
    "thoughts_about_next_action": "<your thoughts on what actions to take next>",
    "actions_todo": [
        {{
            "function_name": "<function_name>",
            "parameters": {{
                "<parameter_name>": "<parameter_value>"
            }}
        }},
        ...
    ]
}}
"""


def gen_research_user_msg(
    current_action: str, current_action_result: str, past_actions_str: str
) -> str:
    return f"""
Past actions taken:
{past_actions_str}

Current action:
{current_action}

Current action's result as context:
{current_action_result}
"""


# TODO(ruiwang|2024-12-16): right now there are hacks for influencer finder. Refactor later.
INFER_SCHEMA_SYS_PROMPT = """
You are a web research specialist who is gathering information about a list of entities online and putting them into a table. Now Your job is to take a task description from user message and:
1. infer the schema of the output table (columns of the table)
2. output a short name of the task for display purposes.
3. figure out the definition of the target entity from user's input task. The target entity is the key entity user wants to output and represents the row of the output table.
4. Figure out the list of criteria of the target entity user wants to find/gather.

The schema is a list of fields each consists of a name and a description. There always will be two default fields: name, url(the URL of the home page or website) so you don't need to include them in the schema.

NOTES:
1. If the task is to find influencers, the schema should include "followers" for the number of followers, "content_focus" for a short summary of what type of content this influencer posts, "related_video_posted" for a selected video posted by this influencer that is related, "contact" for contact information, and any other columns user wants.

Please output in the given JSON format."""

INFER_SCHEMA_RESPONSE_FORMAT = {
    "type": "json_schema",
    "json_schema": {
        "name": "infer_schema",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "fields": {
                    "type": "array",
                    "description": "List of fields. Don't include name and url here since they are always included.",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {
                                "type": "string",
                                "description": "Name of the field",
                            },
                            "description": {
                                "type": "string",
                                "description": "Optional description if name is not self-explanatory",
                            },
                        },
                        "required": ["name", "description"],
                        "additionalProperties": False,
                    },
                },
                "task_short_name": {
                    "type": "string",
                    "description": "Short name of the task for display purposes. Less than 10 characters.",
                },
                "target_entity_definition": {
                    "type": "string",
                    "description": "Definition of the target entity, representing each row of the output table. Just the description of the target entity, don't include other informaton like the fields user want to enrich. e.g. if the task is 'Pull all men's shoe information from Allbirds and include price and color' then the target entity definition is 'Men's shoe from Allbirds'",
                },
                "criteria": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of criteria of the target entity user wants to find/gather. Think about the description of the target entity given by user and break it down into multiple criteria. Criteria are NOT what user wants to enrich so don't include other informaton like the fields user want to enrich (i.e. you should NOT have a critera that is in the schema you generated above). For example, if the task is 'Find me influencers who have talked about AI companion and have 50k-5M followers in US, including their recent post content and email', the list of criteria should be ['Talked about AI companion', 'Have 50k-5M followers', 'In US']. (recent post content and email are not criteria, they are fields user wants to include in the output table)",
                },
            },
            "required": [
                "fields",
                "task_short_name",
                "target_entity_definition",
                "criteria",
            ],
            "additionalProperties": False,
        },
    },
}


INFER_SCHEMA_FEW_SHOTS = [
    {
        "role": "system",
        "name": "example_user",
        "content": """Make a list of the small live music venues (bars, nightclubs, event spaces, etc.) in NYC and do online research to find the size of the venue, number of shows per month, type of music.""",
    },
    {
        "role": "system",
        "name": "example_assistant",
        "content": """
{
    "fields": [
        {
            "name": "size",
            "description": "Size of the venue"
        },
        {
            "name": "number_of_shows_per_month",
            "description": "Number of shows per month"
        },
        {
            "name": "type_of_music",
            "description": "Type of music"
        }
    ],
    "task_short_name": "Small music venues in NYC",
    "target_entity_definition": "Small live music venues (bars, nightclubs, event spaces, etc.) in NYC",
    "criteria": ["in NYC", "small size", "live music"]
}
""",
    },
]

# DEPRECATED
HTML_SCRAPING_SYS_PROMPT = """
You are a web scraping agent. I will provide you with two inputs: raw HTML content and a query structured in a format defined below. Your job is to process the HTML, locate the relevant elements, and return structured data in JSON format according to the query.

Query format: queries contain single-term or list-term queries enclosed in curly braces {}. A single-term query returns one element, while a list-term query returns multiple elements with the specified fields. Queries can also be nested to indicate structural context or include semantic context to describe the target element(s).

Here's an example of a query and how it corresponds to a JSON output:
{
  hvac_companies[] {
    name
    url
    phone
  }
  contact_info
  next_page_url
}
Expected output (of course the data is fake and only for illustration):
{
  "hvac_companies": [
    {
        "name": "ACI, Associació de Consultors d'Instalacions",
        "url": "https://www.hvacinformed.com/companies/aci-associaci-de-consultors-d-instalacions.html",
        "phone": null
    },
    {
        "name": "Air King America, LLC",
        "url": "https://www.hvacinformed.com/companies/air-king-america-llc.html",
        "phone": ************
    },
    {
        "name": "AFPRO",
        "url": "https://www.hvacinformed.com/companies/afpro.html",
        "phone": null
    },
    ...
  ],
  "contact_info": "Phone: ************, Email: <EMAIL>",
  "next_page_url": "https://www.hvacinformed.com/companies/hvac-consultant/directory.html?page=2"
}

You are responsible for:

1. Parsing the query and understanding its structure.
2. Extracting the relevant data from the provided HTML.
3. Returning a properly formatted JSON object that matches the query's structure.

Now, given the following raw HTML and a query, return the data in the appropriate JSON format.
"""


DETERMINE_OFFICIAL_WEBSITE_SYS_PROMPT = """
You are a web research specialist and your job specifically is to determine the official website of a company. You are given the target company's name and a list of google search results each containing title, url and snippet from the user message.
Your job is to determine the official website of the company from the google search results. You should always choose the url from the results that you think is the official website, NEVER generate a url yourself.
You should output directly the official website url in the following JSON format (if you think none of the search results is the official website, just output null):
{{
    "official_website_url": "<official_website_url>"
}}
"""

DETERMINE_OFFICIAL_WEBSITE_RESPONSE_FORMAT = {
    "type": "json_schema",
    "json_schema": {
        "name": "determine_official_website_url",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "official_website_url": {
                    "type": ["string", "null"],
                    "description": "The official website URL of the target company, or null if no valid match was found.",
                }
            },
            "required": ["official_website_url"],
            "additionalProperties": False,
        },
    },
}


# Only output 1 query for now due to efficiency
ENRICH_GEN_SEARCH_QUERIES_SYS_PROMPT = """
You are a web research specialist and your task is to enrich (gather more information about) the input entity based on the task and schema by generating search queries to search google. Read the task and schema defined below and take the input entity from user message and think about what search queries might result in relevant search results that contain the requested fields defined in the schema. For now ONLY output 1 query.

Task: {task}
Schema: {schema_str}

You should output in the given JSON format.
"""

ENRICH_GEN_SEARCH_QUERIES_RESPONSE_FORMAT = {
    "type": "json_schema",
    "json_schema": {
        "name": "enrich_gen_search_queries",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "query_to_search_google": {
                    "type": "string",
                    "description": "A query to search google",
                }
            },
            "required": ["query_to_search_google"],
            "additionalProperties": False,
        },
    },
}


def IS_DUPLICATE_ENTITY_SYS_PROMPT(task: str) -> str:
    return f"""
You are a web research specialist and your job specifically is to determine whether an entity is a duplicate of an existing entity in the current table. You are given the research task as context. You are also given the target entity's name and the current table in the user message.

Task: {task}

Be careful with multiple names of a same company and the context of the task. For example, if the task is to build a list of companies, Meta and Facebook are the same company but if the task is to build a list of products, Facebook, Instagram and WhatsApp are different products.

Please output in the given JSON format.
"""


IS_DUPLICATE_ENTITY_RESPONSE_FORMAT = {
    "type": "json_schema",
    "json_schema": {
        "name": "is_duplicate_entity",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "is_duplicate": {
                    "type": "boolean",
                    "description": "Whether this entity is a duplicate of an existing entity in the table",
                },
                "duplicate_entity_in_table": {
                    "type": "string",
                    "description": "Name of the duplicate entity found in the table, empty string if no duplicate",
                },
            },
            "required": ["is_duplicate", "duplicate_entity_in_table"],
            "additionalProperties": False,
        },
    },
}
