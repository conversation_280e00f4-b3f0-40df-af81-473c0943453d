EXTRACT_SYNONYMS_SYS_PROMPT = """
You are an AI financial analyst designed to accelerate users' financial research. Your task is to extract relevant keywords and their synonyms from the user's input query for a financial search engine. Follow these steps precisely:

1. **Keyword Extraction:**
   - Break down the input query into individual words.
   - **Remove** words that are:
     a) **Stop Words:** Commonly used words (e.g., "the", "is", "at").
     b) **Time-Related Words:** Terms indicating time ranges (e.g., "last quarter", "recently", "2023").
     c) **Company Names or Tickers:** Exclude specific company identifiers.
     d) **Non-informative Words:** Words like "comments", "opinions" that don't convey substantive information about the topic.

2. **Generate Keywords:**
   - From the remaining words, extract all relevant 1-gram and 2-gram phrases. Ensure that each keyword is significant within a financial context.

3. **Expand Synonyms:**
   - For each extracted keyword, generate **only the most relevant synonyms** commonly used in financial documents. Avoid overly broad or unrelated terms.
   - **Example:**
     - Keyword: "AI"
     - Synonyms: ["Artificial Intelligence"]

4. Identify company names or tickers in the input query and remove them from the synonyms list.

5. **Format Output:**
   - Present the keywords and their synonyms in the following JSON structure:
     ```json
     {
        "company_names_should_be_removed": ["Microsoft"],
         "all_synonyms": [
             "AI",
             "Artificial Intelligence",
             "Azure",
             "revenue",
             "cloud revenue",
             "contribution",
             "AI services",
             "cloud income",
             "cloud earnings",
             "contributed"
         ]
     }
     ```
"""

EXTRACT_SYNONYMS_FEW_SHOTS = [
    {
        "role": "user",
        "content": "What does AI service contribute to Azure cloud revenue?",
    },
    {
        "role": "assistant",
        "content": """
{
    "company_names_should_be_removed": ["Microsoft"],
    "all_synonyms": [
        "AI",
        "AI service",
        "Azure",
        "revenue",
        "cloud revenue",
        "contribution",
        "AI services",
        "artificial intelligence",
        "cloud income",
        "cloud earnings",
        "contributed"
    ]
}
""",
    },
    {
        "role": "user",
        "content": "What do players comment on the tailwind of digital ads market?",
    },
    {
        "role": "assistant",
        "content": """
{
    "company_names_should_be_removed": [],
    "all_synonyms": [
        "tailwind",
        "ads",
        "digital ads",
        "ads market",
        "growth",
        "advertising",
        "digital advertising",
        "advertising market",
        "online advertising"
    ]
}
""",
    },
    {
        "role": "user",
        "content": "Latest comments on FSD by Elon Musk",
    },
    {
        "role": "assistant",
        "content": """
{
    "company_names_should_be_removed": [],
    "all_synonyms": [
        "Elon",
        "Musk",
        "Elon Musk",
        "FSD",
        "Full Self-Driving",
        "self-driving",
        "autonomous driving",
        "autonomous vehicles",
        "autonomous",
        "copilot"
    ]
}
""",
    },
    {
        "role": "user",
        "content": "what is happening in B2B cloud market growth",
    },
    {
        "role": "assistant",
        "content": """
{
    "company_names_should_be_removed": [],
    "all_synonyms": [
        "B2B",
        "cloud",
        "cloud market",
        "growth",
        "business-to-business",
        "market growth",
        "cloud services",
        "enterprise cloud",
        "cloud computing",
        "B2B cloud",
        "business growth",
        "market trends"
    ]
}
""",
    },
    {
        "role": "user",
        "content": "consumer spending trend",
    },
    {
        "role": "assistant",
        "content": """
{
    "company_names_should_be_removed": [],
    "all_synonyms": [
        "consumer",
        "spending",
        "trend",
        "consumer spending",
        "consumer trend",
        "consumer trends",
        "consumer spending trend",
        "consumer spending trends"
    ]
}
""",
    },
    {
        "role": "user",
        "content": "aladdin contribution for revenue by blackrock",
    },
    {
        "role": "assistant",
        "content": """
{
    "company_names_should_be_removed": ["Blackrock"],
    "all_synonyms": [
        "aladdin",
        "revenue",
        "contribution",
        "contributed"
    ]
}
""",
    },
    {
        "role": "user",
        "content": "mentions about robotaxi by tesla",
    },
    {
        "role": "assistant",
        "content": """
{
    "company_names_should_be_removed": ["Tesla"],
    "all_synonyms": [
        "robotaxi",
        "robotic taxi",
        "autonomous taxi",
        "fsd",
        "self-driving",
        "full self-driving",
        "autonomous",
        "autonomous driving",
        "autonomous car",
        "autonomous cars",
        "autonomous vehicle",
        "autonomous vehicles",
        "copilot"
    ]
}
""",
    },
]
