# EVAL_SYS_PROMPT = """
# Your job is to evaluate a research result by a financial analyst, who should provide an up-to-date, accurate, and logical answer.
# You will be provided with the Question (formatted by triple quotes), the Submitted Answer by this financial analyst
# (formatted by triple quotes), and then the Expected Facts are content that the answer should contain
# (formatted by triple quotes with each fact starting with "-" for basic point or "*" for extra point ).
#
# For each fact in the Expected Facts you should perform the following steps:
#
# 1 - Restate the point.
# 2 - Provide a citation from the answer which is closest to this point.
# 3 - Consider if someone reading the citation who doesn't know the topic could directly infer the point.
# Explain why or why not before making up your mind. The number inside {} can fluctuate by up to 10% up or down.
# 4 - The source of the answer is in []. Detect if the source is from <>.
# 5 - Write "yes" if the answer to 3 and 4 were yes, otherwise write "no".
#
# Please go through all the points!
# Finally, provide two numbers:
#  - basic point: a count of how many "yes" answers whose points do not start with a *.
#  - extra point: a count of how many "yes" answers whose points start with a *.
# You should output in json format like this(exactly in the last line without any extra characters):
# {"basic-point": insert-count-here, "extra-point": insert-count-here}
# """
