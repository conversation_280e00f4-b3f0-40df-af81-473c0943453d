PEOPLE_FINDER_GOOGLE_PROMPT = """You are an expert at generating effective Google Search queries to help users find relevant people and organizations based on a task description.

Your output will be 2 parts:
- **"organizational_context"**: a single search query (no `site:`) optimized to retrieve relevant organizations, companies, institutions, or groups that match the task description. This query should be self-contained, specific, and designed for precision in identifying relevant organizational entities. Can be empty if not mentioned in task.
- **"search_terms"**: a list of English search queries (no `site:`) specifically targeting people. These queries should focus on individual traits and roles without including any organization-related context. They should be diverse, non-redundant, and optimized to surface LinkedIn pages, ResearchGate profiles, professional bios, or publicly available personal profiles.

---

## Organizational Context Query Guidelines:
1. Extract relevant organization-related attributes:
   - **Industry**: e.g., "biotech startups", "enterprise SaaS companies"
   - **Size**: e.g., "mid-sized firms", "startups", "Fortune 500 enterprises"
   - **Reputation/Status**: e.g., "top-funded", "high-growth", "leading research institutions"
   - **Region/Country**: e.g., "US", "Germany", "APAC region"
2. Construct a query phrase that can efficiently retrieve lists or mentions of such organizations.
3. Avoid listing named companies unless explicitly mentioned in the task.
4. If the task description has no mention about organization, company, .. related, use empty string.

---

## Person Search Terms Generation Guidelines:
1. Focus only on **person-related attributes**, including:
   - **Titles**: "VP of Marketing", "Head of Data", "Director of HR", "CFO", "AI researcher"
   - **Expertise/Skills**: "B2B SaaS marketing", "bioinformatics", "deep learning", "financial strategy"
   - **Seniority**: "senior", "executive", "junior", "mid-level", "C-level"
   - **Experience**: "10+ years experience", "early-career", "seasoned", "PhD holder", "MBA graduate"
   - **Role in process**: “decision maker”, “buyer”, “lead contact”, “hiring manager”, “technical expert”
2. Do **not** include any organizational or employer context.
3. Ensure each query is distinct in phrasing or emphasis to avoid redundancy.
4. Use natural, fluent English search phrases.
5. Tailor queries to match the user’s intent: whether for hiring, sales outreach, collaboration, or research.
6. Support local language if task refers to non-English speaking regions.
7. Avoid domain-specific filters like `site:linkedin.com`.

---

## Example Output:

{
  "organizational_context": "high-growth B2B SaaS companies in Europe",
  "search_terms": [
    "senior AI researcher with deep learning expertise",
    "VP of marketing with B2B SaaS experience",
    "data science director with bioinformatics background",
    "CFO with expertise in financial strategy and growth planning",
    "hiring manager for enterprise software solutions"
  ]
}

---

## Output strictly in JSON Format.
"""


PEOPLE_FINDER_PLAN_GOOGLE_RESPONSE_FORMAT = {
    "type": "json_schema",
    "json_schema": {
        "name": "influencer_finder_plan_linkedin",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "organizational_context": {
                    "type": "string",
                    "description": "A single search query to retrieve relevant organizations, companies, or groups related to the task description. This query should be self-contained and optimized to surface lists or mentions of such organizations in search engines.",
                },
                "search_terms": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "A diverse and non-redundant list of person-related search queries focused on individual traits (such as titles, expertise, skills, seniority, experience) without organizational context. Be mindful of language and regional considerations from the task description.",
                },
            },
            "required": ["organizational_context", "search_terms"],
            "additionalProperties": False,
        },
    },
}


# --------- back up ------------

# INFLUENCER_ENRICH_SYS_PROMPT
"""
2. IMPORTANT: for linkedin case, the term "influencer" means a person. Do NOT check if a person is influencer. Just make it True.
3. for linkedin case, if a criterion related to company, it means current company not past. (evaluate past work experiences only if specified clearly)
"""


# INFLUENCER_FINDER_LINKEDIN_INFER_CRITERIA_SYS_PROMPT = """
# You are an AI assistant who finds target persons on LinkedIn and put them into a table. Now Your job is to take a person finding task description that contains what kind of person (criteria) you are supposed to find from user message and:
# 1. output a short name of the task for display purposes.
# 2. Extract the list of criteria of the target influencers user wants to find from the task description.
#     2.1 employer criteria that are related to company name, industry, size, employment/job type(such as freelancer, self-employed) and etc of the target persons.
#     2.2 person criteria that are related to title, experience, location, skills, business domain, license or certificates, education and etc.
#     2.3 Please scan the input text for any ambiguous or shorthand references, including informal phrases, acronyms, and nicknames. Replace each with a clear and specific explanation of what the term refers to. If context is insufficient, note the ambiguity explicitly and provide plausible interpretations.
# You should output in the given JSON format.
# """

# 3. If the constraints criterion to person's skills, industry experience, certifications, education and etc, you should split them into separate criteria entries.


# INFLUENCER_FINDER_LINKEDIN_INFER_CRITERIA_FEW_SHOTS = [
#     {
#         "role": "system",
#         "name": "example_user",
#         "content": """Find python developers who work at tech companies in the AI field, with at least 5 years of experience, Bay Area""",
#     },
#     {
#         "role": "system",
#         "name": "example_assistant",
#         "content": """
# {
#     "task_short_name": "LinkedIn Python devs in AI",
#     "criteria": ["Python developer", "works at tech companies in AI field", "at least 5 years of experience", "located in Bay Area"]
# }
# """,
#     },
#     {
#         "role": "system",
#         "name": "example_user",
#         "content": """Find LinkedIn professionals who are CTOs or technical leaders at AI tooling startups with company size less than 50 employees, based in Europe.""",
#     },
#     {
#         "role": "system",
#         "name": "example_assistant",
#         "content": """
# {
#     "task_short_name": "LinkedIn CTOs at AI startups",
#     "criteria": ["CTO or technical leader position", "works at AI tooling startups", "company size less than 50 employees", "based in Europe"]
# }
# """,
#     },
#     {
#         "role": "system",
#         "name": "example_user",
#         "content": """Find head/lead of marketing in public traded company of consuming electronic industry, with 3+ experiences at the postion, China or US so for oppotunities to sell our new model of chips.""",
#     },
#     {
#         "role": "system",
#         "name": "example_assistant",
#         "content": """
# {
#     "task_short_name": "LinkedIn Marketing leads for chips",
#     "criteria": ["head or lead of marketing position", "works at publicly traded company", "consumer electronics industry", "3+ years experience in the position", "located in China or US"]
# }
# """,
#     },
#     {
#         "role": "system",
#         "name": "example_user",
#         "content": """Find leads of purchasing dept in Magnificent 7 so that we can sell our CRM SaaS.""",
#     },
#     {
#         "role": "system",
#         "name": "example_assistant",
#         "content": """
# {
#     "task_short_name": "LinkedIn Purchasing leads at M7",
#     "criteria": ["lead of purchasing department", "works at Magnificent 7 companies (Google/Alphabet, Apple, Amazon, Meta, Microsoft, Nvidia, Tesla)"]
# }
# """,
#     },
# ]
