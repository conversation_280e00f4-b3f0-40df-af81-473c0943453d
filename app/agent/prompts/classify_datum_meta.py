import json

from datum.enums import Meta<PERSON><PERSON><PERSON><PERSON>


def get_classify_datum_meta_sys_prompt(fields: list[dict], desc: str = "") -> str:
    instruction = (
        "You are a financial analyst trained to classify and label meta on document"
    )
    instruction += " by following the given schema." if fields else "."
    instruction += (
        "Read the provided document by customer, analyze and identify the metadata."
    )
    if desc:
        instruction += "Doc description: " + desc

    if fields:
        instruction += f"""

## The schema is a dict as below:
{json.dumps(fields, indent=2)}

Note each field's type, which is python type, output with correct python format. For str field, limit to max_length.
If you can not classify or infer a meta's value, just use None as its meta_value, do NOT guess a value.
"""
    else:
        instruction += f"""

## The schema

We do not have any schema pre-defined. You should try to classify some meta to label the doc.
Metadata of a document are the attributes describe the documents. Do NOT summarize document into points and use the points as metadata.
Try classifying the doc into below meta names (meta values in bracket are examples)
- industry (food, auto, ...)
- company (Intel, Tesla, ...)
- ticker (MU, NVDA, ...)
- country (China, US, ...)
- speakers (<PERSON>, <PERSON>, ...)
- attendees (<PERSON>, May, ...)
- doc_type (transcript, notes, ...)
- market_role (buy-side, sell-side, ...)
- <meta_name_you_classified>
- ...
Do NOT RIGIDLY use the list I provided, classify based on the document content. The MOST IMPORTANT is that your classified meta names by yourself, not only those I listed.

Note we ONLY support these types for meta_value: {MetaTypeEnum.choices()}. Do NOT use `dict` as meta_value.

After classified the meta names, find or infer the meta's value. If not sure, just use null as meta_value, do NOT guess a value.
Limited to no more than 15 metadata pairs.
"""

    instruction += """

## output format
You should output in the following JSON format, note each meta value type:
{
    "<meta_name>": <meta_value>
}
"""
    return instruction
