from abc import abstractmethod
from datetime import datetime
from logging import get<PERSON><PERSON>ger
from typing import TypeVar, Union, List, Dict, Tuple, Generic, Optional, Literal

from pydantic import BaseModel

from agent.config.llm import TOKEN_LIMIT_BY_MODEL_NAME
from agent.memory import ShortTermMemory
from agent.structs import Mock<PERSON>hunk
from biz.structs import DocChunk

logger = getLogger(__name__)

PromptMessages = TypeVar(
    "PromptMessages",
    bound=Union[
        List[Dict[str, str]],  # OpenAI JSON prompts
        List[str],  # Claude Legacy Text Prompts
        Tuple[str, List[Dict[str, str]]],  # <PERSON> JSON prompts
    ],
)

PromptDoc = TypeVar("PromptDoc", bound=Union[DocChunk, str, MockChunk])


class PromptBuilder(BaseModel, Generic[PromptMessages, PromptDoc]):
    """Build prompt and truncate it if it exceeds the token limit"""

    sys_prompt: str
    user_msg: str | List
    llm_model_name: str
    few_shots_msgs: Optional[List[Dict[str, str]]] = None

    # insert memory messages into the prompt
    include_memory_msgs_type: Literal[
        "only_user_msgs", "all_msgs", "none"
    ] = "only_user_msgs"
    memory: Optional[ShortTermMemory] = None
    memory_token_limit: int = 500  # truncate memory messages if it exceeds this limit

    # insert fetched data into the prompt as retrieval augmented generation
    # usually this data should be ranked and truncated before being inserted
    fetched_data: Optional[List[PromptDoc]] = None

    # Sometimes we need to override the current time we tell LLM (e.g. for evaluation)
    override_datetime: Optional[Union[datetime, str]] = None

    class Config:
        arbitrary_types_allowed = True

    @property
    def total_token_limit(self) -> int:
        """Get the total token limit by model name"""
        if self.llm_model_name not in TOKEN_LIMIT_BY_MODEL_NAME:
            def_val = TOKEN_LIMIT_BY_MODEL_NAME["default"]
            logger.error(
                f"Unknown LLM model name: {self.llm_model_name}. "
                "Using default token limit {def_val} in building prompt."
            )
            return def_val

        return TOKEN_LIMIT_BY_MODEL_NAME[self.llm_model_name]

    @abstractmethod
    def build(self) -> PromptMessages:
        """How these data form the prompt should be tested systematically"""
