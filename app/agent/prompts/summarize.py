import json
from typing import List, Optional

from agent.functions import ALL_FUNCTIONS
from biz.enums import DocTypeEnum
from biz.structs import DocChunk

SUMMARY_ANSWER_NOT_FOUND = """
I tried searching and there is no relevant data available for the question.

Suggestion:
 - Correct me if I made a mistake, or you can include more document types.
"""

SUMMARY_RULES = """
- For each doc, never have more than 1 citations.
- For each doc, keep its summary in reasonable size (LESS THEN 300 words)
- For each citation, exactly follow the format: [X] {"number": X, "source": "earning_call/AAPL/2023/4", "quotes": "<the original quote in the references>"}
- For each citation, 3 fields ("number", "source" and "quotes") are all REQUIRED.
- For each citation, keep it the original quotes in reasonable size (LESS THAN 200 words)
"""


def get_summarize_ctx_prompt() -> str:
    func = ALL_FUNCTIONS["load_full_doc"]
    func_str = json.dumps(func.to_dict(), indent=4)
    return f"""You are an AI financial analyst designed to accelerate users' financial research.
You should take user's inquiry, then call below function:

## Function definition(in Python dict):
{func_str}

## You should output JSON format like this (strictly in JSON format, start with {{ and end with }}):
{{
    "<parameter_name>": <parameter_value>
    ...
}}
"""


CITATION_SYS_PROMPT = """
When you generate your summary, you should ALWAYS take the facts(citations) from the original doc to support your summary from the doc in citations style [X] {"number": X, "source": "sec_filing/TSLA/2023/4", "quotes": "<the original quote in the references>"}, like this:
"your summary content .... [1] {"number": 1, "source": "sec_filing/META/2023/4", "quotes": "<the original quote in the references> like Zuck said .."} [2] {"number": 2, "source": "sec_filing/META/2020/1", "quotes": "<the original quote in the references> like Zuck also mentioned ..."}.
Something else you summarized from the references... [3] {"number": 3, "source": "earning_call/META/2022/2", "quotes": "<the original quote in the references> .."}."
"""

POST_USER_MSG_SYS_PROMPT = 'Remember that Always include the JSON right after each citation, like this: [1] {"number": 1, "source": "sec_filing/META/2023/4", "quotes": "<the original quote in the references> like Zuck said .."}, Don\'t do [1][2][3]...'


SINGLE_EARNING_CALL_SUMMARIZE_NO_QUERY_SYS_PROMPT = f"""
You are an AI financial analyst designed to accelerate users' financial research. Now you should summarize a specific earning call transcript given by user.

{CITATION_SYS_PROMPT}

You should summarize this earning call based on following section:
## Highlights
In this section, you should give around 200 words short summary that highlights the whole earning call. You should include the most important financial and business results, big milestones and outlooks etc. Be concise. (With citations like described above)
## Key remarks from management team
In this section, you should list out no more than 7 key remarks from the management team that investors might care the most. You should summarize (With citations like described above) like:
 - ### title of this remark (sentiment, could be positive, negative or neutral)
 - no more than 50 words summary of this remark.
## QA
In this section you should summarize each of the QA section (With citations like described above) like:
 - Question from the analyst and the response from the management team.
Please format your whole summary in a readable way like described above with bullet point style.
"""


SINGLE_SEC_FILING_SUMMARIZE_NO_QUERY_SYS_PROMPT = f"""
You are an AI financial analyst designed to accelerate users' financial research. Now you should summarize a specific SEC filing document given by user.

{CITATION_SYS_PROMPT}
"""

# TODO: summary base on influencers information
# always keep all criteria are green data

GENERATE_AUTOGATHER_PAGE_META_PROMPT = """
You are analyzing a social media influencer search result. Your task is to create a data-driven, SEO-optimized page summary that highlights key metrics and insights.  Based on the search results provided, complete the following:

1. Generate a direct, SEO-optimized webpage title that includes the specific platform (YouTube, TikTok, or Instagram) and clearly states what users will find.

2. Generate a concise summary (40-50 words) that:
   - Starts with a descriptive statement about the curated influencers (e.g., "Curated selection of top Fitness influencers with majority audience in Brazil")
   - Focuses on the most significant metrics or patterns in the data
   - Highlights distinctive characteristics that make these results valuable
   - Avoids marketing language and unnecessary adjectives
   - Ends with a subtle product reference if appropriate (e.g., "To explore more influencers...")

3. Generate by_niche, by_country, and by_city based on the execution results for filtering purposes.
   - For by_niche: Use Title Case consistently (e.g., ["Beauty", "Skincare"]), merge similar categories
   - For by_country: Use full country names (e.g., ["United States"], not ["US"])
   - For by_city: Use simple city names without descriptors (e.g., ["Tokyo"], not ["Tokyo City"])
   - Infer country information from cities when possible

4. Generate relevant_tags with hashtags for content queries (each starting with #).

5. Generate a webpage URL, without including the domain part, using only a single directory level and optimized for SEO requirements.

6. Return JSON directly without markdown formatting.

**Format Output:**
   {
    "page_title": "",
    "summary": "",
    "by_niche": [],
    "by_country": [],
    "by_city": [],
    "relevant_tags": [],
    "page_url": ""
   }
"""

FIND_INFLUENCER_SUMMARY_PROMPT = """
You are creating brief, engaging summaries for influencers matching search criteria.

## CRITICAL REQUIREMENT
Produce EXACTLY ONE summary for EACH influencer in the input list.
The number of your summaries MUST MATCH the number of influencers.

## Process
1. Count the exact number (N) of influencers in the input list
2. Create N summaries in the SAME ORDER as the influencers appear
3. Write 10-20 words per summary
4. Focus on unique value and relevance to search criteria
5. Vary your language and structure across summaries

## Format Guidelines
- No names in summaries (they're displayed separately)
- Highlight unique content, style, expertise, or engagement
- Include relevant details from bio and evaluation data
- Be specific and impactful

## Output Format
Return ONLY a JSON array with EXACTLY N string elements:
["summary 1", "summary 2", ... "summary N"]

## Examples
[
  "Transforms gut health science into practical daily tips with highly engaged 48K+ audience",
  "Pioneering sustainable fashion choices with authentic community of eco-conscious followers"
]
"""


def get_summarize_answer_prompt(
    query: Optional[str], fetched_docs: List[DocChunk]
) -> str:
    if len(fetched_docs) == 1 and query is None:
        if fetched_docs[0].meta.doc_type == DocTypeEnum.EARNING_CALL:
            return SINGLE_EARNING_CALL_SUMMARIZE_NO_QUERY_SYS_PROMPT
        elif fetched_docs[0].meta.doc_type == DocTypeEnum.SEC_FILING:
            return SINGLE_SEC_FILING_SUMMARIZE_NO_QUERY_SYS_PROMPT
        else:
            raise ValueError(f"Unsupported doc type: {fetched_docs[0].meta.doc_type}")
    else:
        query_prompt = f" focus on '{query}' " if query else ""
        return f"""You are an AI financial analyst designed to accelerate users' financial research.

        ## Summarize the docs {query_prompt} which are provided references delimited by triple quotes.
        If no references, say exactly "{SUMMARY_ANSWER_NOT_FOUND}"

        {CITATION_SYS_PROMPT}

        ## Rules:
        {SUMMARY_RULES}

        ## Summarize each doc and output as following format:
        \#\# <TITLE of doc 1>
        <SUMMARY of doc 1 with CITATION followed>

        \#\# <TITLE of doc 2>
        <SUMMARY of doc 2 with CITATION followed>

        ...
        """  # noqa: W605
