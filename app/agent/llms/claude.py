import logging
import traceback
from abc import ABC
from typing import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, ClassV<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>

from anthropic import Anthropic, AsyncAnthropic
from anthropic_bedrock import AnthropicBedrock, AsyncAnthropicBedrock
from anthropic_bedrock._types import NOT_GIVEN

from agent.llms.base import LLM
from common.services import slack
from common.utils.strutil import quick_hash

logger = logging.getLogger(__name__)


class ClaudeLlmBase(LLM, ABC):
    enforce_json_output: bool = False
    llm_model_name: str
    top_p: float = 0.9  # [0,1]
    top_k: int = 300  # [0, 500]

    _claude_cli: ClassVar = None
    _async_claude_cli: ClassVar = None

    def _check_model(self) -> None:
        assert self.max_tokens <= 4096, "<PERSON> limits 4096 max output tokens."

    @classmethod
    def is_online(cls) -> bool:
        return True

    def handle_error(self, e: Exception) -> str:
        # TODO: handle errors from https://docs.anthropic.com/claude/reference/errors
        err_msg = f"LLM Error: (Anthrop<PERSON>) {e}"
        logger.error(err_msg)
        slack.send_alert(e, traceback.format_exc())
        return err_msg


class ClaudeLlm(ClaudeLlmBase):
    top_p: float = 0.9  # [0,1]
    top_k: int = 300  # [0, 500]

    _claude_cli: ClassVar = Anthropic()
    _async_claude_cli: ClassVar = AsyncAnthropic()

    def _check_model(self) -> None:
        super()._check_model()

    async def _chat_complete(self, prompt_msgs: Tuple[str, List[Dict]]) -> str:
        """Chat with the Anthropic Claude model"""
        sys_prompt, other_prompts = prompt_msgs
        self._check_model()
        try:
            cli = self._claude_cli
            ret = cli.messages.create(
                model=self.llm_model_name,
                system=sys_prompt,
                messages=other_prompts,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                top_p=self.top_p,
                top_k=self.top_k,
            )
            output = ret.content[0].text
            cost = await self.cost_tracker.atrack(
                model_name=self.llm_model_name,
                prompt_tokens=ret.usage.input_tokens,
                completion_tokens=ret.usage.output_tokens,
            )
            self.observer.log_llm_costs(cost)
            if self.verbose:
                logger.info(
                    f"Token usage(prompt/completion): "
                    f"{ret.usage.input_tokens}/"
                    f"{ret.usage.output_tokens}"
                )
            return output
        except Exception as e:
            return self.handle_error(e)

    async def _chat_streaming(self, prompt_msgs: Tuple[str, List[Dict]]) -> AsyncGenerator[str, None]:  # type: ignore
        """Chat with the Anthropic Claude model for streaming mode"""
        self._check_model()
        sys_prompt, other_prompts = prompt_msgs
        try:
            cli = self._async_claude_cli
            async with cli.messages.stream(
                model=self.llm_model_name,
                system=sys_prompt,
                messages=other_prompts,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                top_p=self.top_p,
                top_k=self.top_k,
            ) as ret:
                async for chunk in ret.text_stream:
                    yield chunk
        except Exception as e:
            self.handle_error(e)

    def count_tokens(self, prompt_msgs: Tuple[str, List[Dict]]) -> int:
        sys_prompt, msgs = prompt_msgs
        return self._claude_cli.count_tokens(sys_prompt)

    def _get_cache_key(self, prompt_msgs: Tuple[str, List[Dict]]) -> str:
        sys_prompt, msgs = prompt_msgs
        s = self.llm_model_name + "\n".join(
            [
                line
                for line in sys_prompt.split("\n")
                if not line.startswith("Current date and time:")
            ]
        )

        return quick_hash(s)


# TODO: legacy. to be removed if we use official Anthropic.
class AwsClaudeLlm(ClaudeLlmBase):
    _CLAUDE_INSTANCE = "anthropic.claude-instant-v1"
    _CLAUDE_V21 = "anthropic.claude-v2:1"

    enforce_json_output: bool = False
    llm_model_name: str = _CLAUDE_V21
    top_p: float = 0.9  # [0,1]
    top_k: int = 300  # [0, 500]

    legacy_message: bool = True  # new message format not work in AWS yet.

    _claude_cli: ClassVar = AnthropicBedrock()
    _async_claude_cli: ClassVar = AsyncAnthropicBedrock()

    def _check_model(self) -> None:
        super()._check_model()
        if self.llm_model_name != self._CLAUDE_V21:
            logger.warning(
                f'llm model "{self.llm_model_name}" may not support system prompt. "'
                f'"see https://docs.anthropic.com/claude/docs/how-to-use-system-prompts#where-can-i-use-system-prompts'
            )

    async def _chat_complete(self, msgs: List[str]) -> str:
        """Chat with the Anthropic Claude model"""
        self._check_model()
        try:
            cli = self._claude_cli
            prompt = "\n".join(msgs)
            ret = cli.completions.create(
                model=self.llm_model_name,
                prompt=prompt,
                temperature=self.temperature,
                top_p=self.top_p,
                top_k=self.top_k,
                max_tokens_to_sample=self.max_tokens,
                extra_headers=None
                if self.legacy_message
                else {
                    "anthropic-beta": "messages-2023-12-15",
                    "anthropic-version": "2023-06-01",
                },
                extra_body=None if self.legacy_message else {"message": msgs},
                stop_sequences=["}\n\n"] if self.enforce_json_output else NOT_GIVEN,
            )
            if self.enforce_json_output:
                output = "{" + ret.completion + "}"
            else:
                output = ret.completion

            prompt_tokens = cli.count_tokens(prompt)
            completion_tokens = cli.count_tokens(output)
            cost = await self.cost_tracker.atrack(
                model_name=self.llm_model_name,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
            )
            self.observer.log_llm_costs(cost)
            if self.verbose:
                logger.info(
                    f"Token usage(prompt/completion): "
                    f"{prompt_tokens}/"
                    f"{completion_tokens}"
                )
            return output
        except Exception as e:
            return self.handle_error(e)

    async def _chat_streaming(self, msgs: List[str]) -> AsyncGenerator[str, None]:  # type: ignore
        """Chat with the Anthropic Claude model for streaming mode"""
        self._check_model()
        try:
            cli = self._async_claude_cli
            prompt = "\n".join(msgs)
            ret = await cli.completions.create(
                model=self.llm_model_name,
                prompt=prompt,
                temperature=self.temperature,
                top_p=self.top_p,
                top_k=self.top_k,
                max_tokens_to_sample=self.max_tokens,
                extra_headers=None
                if self.legacy_message
                else {
                    "anthropic-beta": "messages-2023-12-15",
                    "anthropic-version": "2023-06-01",
                },
                extra_body=None if self.legacy_message else {"message": msgs},
                stream=True,
            )
            async for chunk in ret:
                yield chunk.completion
        except Exception as e:
            self.handle_error(e)

    def count_tokens(self, msgs: List[str]) -> int:
        return self._claude_cli.count_tokens("\n".join(msgs))

    def _get_cache_key(self, msgs: List[str]) -> str:
        s = self.llm_model_name + str(
            [s for s in msgs if not s.startswith("Current date and time:")]
        )
        return quick_hash(s)
