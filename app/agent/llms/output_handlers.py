"""TO BE ARCHIVED"""
import json
import logging
from abc import ABC, abstractmethod
from typing import List, Optional

# from agent.visualizer import Visualizer
from pydantic import BaseModel

from agent.enum import ServerOperationEnum
from agent.models import ChatTurn, Citation, ChatTurnStates
from agent.observer import Observer
from biz import datasdk
from biz.search.snippet import SnippetSearcher
from common.utils.strutil import ellipse

logger = logging.getLogger(__name__)


class LLMOutputHandler(ABC):
    """Parse, format, notify etc. the output from the LLM model"""

    @abstractmethod
    async def handle_complete(self, output: str) -> str:
        pass

    @abstractmethod
    async def handle_streaming_chunk(
        self, observer: Observer, chunk: str
    ) -> Optional[str]:
        """handle and process streaming chunk by different output

        returns:
        1. None, means no return, no streaming to client
        2. "", general output, but it mostly means the end of streaming.
        3. other strings. normal string output, just streaming it.
        """
        pass

    @abstractmethod
    async def handle_streaming_end(self, observer: Observer) -> None:
        pass


class DefaultLL<PERSON>ut<PERSON><PERSON>andler(LLMOutputHandler):
    async def handle_complete(self, output: str) -> str:
        return output

    async def handle_streaming_chunk(
        self, observer: Observer, chunk: str
    ) -> Optional[str]:
        await observer.notify_streaming(chunk, False)
        return chunk

    async def handle_streaming_end(self, observer: Observer) -> None:
        await observer.notify_streaming("", True)


class HttpLLMOutputHandler(DefaultLLMOutputHandler):
    chat_turn_id: int
    _chunks: list

    def __init__(self, chat_turn_id: int) -> None:
        self.chat_turn_id = chat_turn_id
        self._chunks = []

    async def handle_streaming_chunk(
        self, observer: Observer, chunk: str
    ) -> Optional[str]:
        self._chunks.append(chunk)
        return chunk

    async def handle_streaming_end(self, observer: Observer) -> None:
        turn_stat, created = await ChatTurnStates.objects.aget_or_create(
            chat_turn_id=self.chat_turn_id
        )
        turn_stat.extra_data = observer.states
        turn_stat.retrieval = observer.retrieval
        turn_stat.llm_cost = observer.get_llm_costs_str()
        turn_stat.perf = observer.perf
        turn_stat.llm_prompts = observer.llm_prompts
        turn_stat.llm_outputs = observer.llm_outputs
        turn_stat.errors = observer.format_error_admin()
        await turn_stat.asave()


class CitationLLMOutputHandler(BaseModel, LLMOutputHandler):
    """Parse the LLM output, extract citations, search in the original documents and
    notify front-end for citations with highlights.
    """

    chat_turn: ChatTurn
    citation_start: bool = False
    cur_citation: str = ""
    citations: List[Citation] = []
    searcher: Optional[SnippetSearcher] = None

    class Config:
        arbitrary_types_allowed = True

    async def handle_complete(self, output: str) -> str:
        # TODO: implement this
        return output

    async def handle_streaming_chunk(
        self, observer: Observer, chunk: str
    ) -> Optional[str]:
        if "{" in chunk:
            self.citation_start = True
            await observer.notify_operation(ServerOperationEnum.PREPARING_CITATION)
        if self.citation_start:
            self.cur_citation += chunk
            if "}" in chunk:
                self.cur_citation, remaining = self.cur_citation.split("}", 1)
                self.cur_citation += "}"
                await self._handle_citation(observer, self.cur_citation)
                self.cur_citation = ""
                self.citation_start = False
                return remaining
            return None
        else:
            await observer.notify_streaming(chunk, False)
            return chunk

    async def handle_streaming_end(self, observer: Observer) -> None:
        """This method should ALWAYS be called at the end of the streaming mode."""
        if self.cur_citation:
            await self._handle_citation(observer, self.cur_citation)
        await observer.notify_streaming("", True)

    async def _handle_citation(self, observer: Observer, citation_text: str) -> None:
        try:
            citation_data = json.loads(citation_text)
            citation = Citation(
                chat_turn=self.chat_turn,
                number=citation_data["number"],
                doc_key=citation_data["source"],
                quotes=citation_data["quotes"],
            )
        except Exception:
            await observer.notify_operation(ServerOperationEnum.CITATION_COMPLETED)
            observer.log_error(
                Exception(f"Failed to parse citation: {ellipse(citation_text, 1000)}")
            )
            return

        doc_key = citation_data["source"]
        doc = datasdk.get_doc_by_key(doc_key)
        meta = doc.meta

        citation.doc_key = meta.key
        # Override citation source with original URL for SEC and Earnings
        citation.url = meta.original_url

        try:
            await self._highlight_citation(doc.content.get_data(), citation)
            citation.doc_type = meta.doc_type.value
            citation.pub_date = meta.pub_date
            citation.title = meta.display_title
            self.citations.append(citation)

            await observer.notify_citation(citation)
        finally:
            await observer.notify_operation(ServerOperationEnum.CITATION_COMPLETED)

    async def _highlight_citation(self, content_data: str, citation: Citation) -> None:
        """Search and Highlight the citation in the original document.

        TODO: use a more advanced search approach rather than exact match. Especially
                for non-text documents like HTML or PDF.
        """
        data = content_data
        logger.debug(f"Highlighting citation: {ellipse(citation.quotes, 1000)}")
        logger.debug(f"Original doc: {ellipse(data, 1000)}")

        if self.searcher is not None:
            _, citation.highlight_spans = self.searcher.simple_search(
                citation.quotes, data
            )
        else:
            cleaned_quotes = citation.quotes.rstrip('. ,\n"').lstrip('. ,\n"')
            try:
                i = str(data).index(cleaned_quotes)
                j = i + len(cleaned_quotes)
                citation.highlight_spans = [(i, j)]
            except ValueError:
                logger.error(
                    f"Can't find citation: {cleaned_quotes} in {citation.doc_key}"
                )
