import asyncio
import json
import traceback
from typing import Any

from django.core.management.base import BaseCommand

from agent.agents.table_view_agent import PreloadDiffViewAgent, PreloadPeersViewAgent
from agent.conf import PREPARED_DATA_BUCKET
from agent.config.agent import PreloadDiffViewAgentConfig, PreloadPeersViewAgentConfig
from agent.observer import Observer
from biz import datasdk
from biz.enums import TickerScopeEnum
from common.services import s3
from common.utils.asyncioutil import run_concurrent_tasks
from common.utils.lang import JsonEncoder
from common.utils.terminal import debug


class Command(BaseCommand):
    help = "Preload table view data for MVP 3.3"

    def add_arguments(self, parser: Any) -> None:
        parser.add_argument(
            "--num-tasks", type=int, help="num of concurrent tasks", default=8
        )
        parser.add_argument(
            "--scope",
            default=TickerScopeEnum.TOP.value,
            type=str,
            choices=TickerScopeEnum.values(),
            required=False,
            help="Which scope of tickers to run for. default: Top 7 tickers.",
        )
        parser.add_argument(
            "--tickers",
            default=[],
            nargs="+",
            type=str,
            required=False,
            help="which tickers to run for. Ignored if '--scope' specified.",
        )
        parser.add_argument(
            "--override-search-agent-model",
            action="store_true",
            help="whether replace search agent's synthesis LLM model name with preload agent's",
        )

    async def run_for_one_ticker(
        self, ticker: str, override_search_agent_model: bool = False
    ) -> None:
        try:
            # diff view
            config = PreloadDiffViewAgentConfig(
                override_search_agent_model=override_search_agent_model
            )
            agent = PreloadDiffViewAgent(config=config, observer=Observer())
            data = await agent.run(ticker=ticker)
            s3.put_json(
                PREPARED_DATA_BUCKET,
                f"diff_view/{ticker}.json",
                json.dumps(data, indent=2, cls=JsonEncoder),
            )
            # peers view
            config = PreloadPeersViewAgentConfig(
                override_search_agent_model=override_search_agent_model
            )
            agent = PreloadPeersViewAgent(config=config, observer=Observer())
            data = await agent.run(ticker=ticker)
            s3.put_json(
                PREPARED_DATA_BUCKET,
                f"peers_view/{ticker}.json",
                json.dumps(data, indent=2, cls=JsonEncoder),
            )
        except Exception as e:
            traceback.print_exc()
            self.stdout.write(self.style.ERROR(f"Failed for {ticker}: {e}"))

    def handle(self, *args: Any, **kwargs: Any) -> None:
        scope = kwargs["scope"]
        tickers = kwargs["tickers"]
        override_search_agent_model = kwargs["override_search_agent_model"]
        if not tickers:
            tickers = datasdk.ticker_list_by_scope(scope)
        debug("preloading table view for tickers: {tickers}")
        args_list = [(ticker, override_search_agent_model) for ticker in tickers]
        asyncio.run(
            run_concurrent_tasks(
                method=self.run_for_one_ticker,
                args_list=args_list,
                concurrency=kwargs.get("num_tasks", 8),
                show_progress=True,
            )
        )
