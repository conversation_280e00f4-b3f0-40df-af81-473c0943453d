import asyncio
import json
import logging
from datetime import datetime
from typing import Any, List

from agent.agents.search_filter import SearchFilterAgent
from agent.config.agent import SearchFilterAgentConfig as AgentConfig
from agent.constants import EVAL_SAMPLE_DATA_DIR
from agent.enum import AppPlatform
from agent.eval.relevant_snippet import <PERSON><PERSON>hunk, RelevantSnippetSample
from agent.memory import ShortTermMemory
from agent.models import Cha<PERSON>, Chat<PERSON><PERSON>, get_eval_user
from agent.observer import Observer
from agent.prompts.search_filter import (
    RELEVANT_CLASSIFIER_FEW_SHOTS,
    RELEVANT_CLASSIFIER_SYS_PROMPT,
)
from biz.enums import DocTypeEnum
from common.utils.asyncioutil import run_concurrent_tasks
from django.contrib.auth.models import User
from django.core.management.base import BaseCommand

logger = logging.getLogger(__name__)


QUERIES = [
    #####################################################################
    # questions for the final golden test set. we shouldn't use it for training
    #####################################################################
    # "How does Amazon plan to expand its AWS services in the next year?",
    # "How are retail companies adapting to the shift towards online shopping?",
    # "did LLY complain about the merger of CTLT",
    # "According to recent earnings call, will power semiconductor see headwinds in coming quarters?",
    # "what's Tesls's plan on humanoid robot?",
    # "find me salesforce mgmt narratiive on ai",
    # "How is Nike leveraging technology for product innovation?",
    #####################################################################
    # real user queries, could be used for generating training data (Part 1)
    #####################################################################
    # "Any updates of nvidia customer adoption on GPU?",
    # "Elon Musk's latest on comments FSD?",
    # "What guidance did Apple provide on next quarter's revenue expectations?",
    # "What are the beverage spending trends disclosed in the recent earning call?",
    # "what did snowflake say about snowpark's financials over time?",
    # "Did Jensen mention anything about his prediction of the accelerated AI capex in the future",
    # "Generative AI investment on Meta, Google and Microsoft",
    # "AI-related capex of major Cloud Service Provider",
    # "What is the growth rate Microsoft suggests Azure could grow in the coming quarter?",
    # "what did tim cook say about China demand in the most recent earnings call?",
    # "how does nvidia think about new players entering into the space to compete like Groq",
    # "How does Elon's attitude around price cut changes through time?",
    # "What are McDonald's growth strategies for digital sales?",
    # "what is apple's buyback decision on May 2023?",
    # "View history on: iPhone segment performance across AAPL's earning call",
    # even more real user queries
    #####################################################################
    # real user queries, could be used for generating training data (Part 2)
    #####################################################################
    # "Did the management provide any guidance on revenue of coinbase in 2024?",
    # "management view of PDD on Temu's US market",
    # "How large is the potential impact does ETH ETF can have on Coinbase",
    # "what nvidia says about expectation of blackwell sales",
    # "what has Micron said regarding its market share in 2025",
    # "what is the margin trajectory for amazon",
    # "what's the impact of AI for meta?",
    # "please listing snowflake's new AI product change in 24Q1",
    # "can you give the ads market sentiment of the big ads companies",
    # "What are McDonald's growth strategies for digital sales?",
    # "how does vistra hedge their electricity price",
    # "Can you list apple's buyback history in the past 10 years, including the announcement date and amount",
    # "What's Musk's view on the Lidar?",
    # "what is nvidia's opinion towards inference and training cost percentages",
    # "apple's attitude towards openai and perplexity",
    # "How is servicenow ai doing",
    #####################################################################
    # real user queries, could be used for generating training data (Part 3) 18
    #####################################################################
    # "how did servicenow's customers use the Now Assist product to improve efficiency?",
    # "what is salesforce's annual recurring revenue?",
    # "What's booking's core strategies in the Asia market?",
    # "please help me find all the information about Airbnb's geography revenue breakdown",
    # "what's the newest mobile app/website channel share of booking?",
    # "please help me find all the numbers of the room nights booked of Expedia",
    # "when was the first time booking mentioned alternative accommodation",
    # "Expedia's view on 2024 travel demand?",
    # "Microsoft's capex outlook?",
    # "View history on: Driving Revenue Growth across COIN's earning call",
    # "What's the sector with fastest revenue growth of Coinbase?",
    # "How market opinions are divided towards Coinbase after the 2024Q1 financial report releasing?",
    # "View history on: DAU and International Growth across RBLX's earning call",
    # "How does Nike plan to address supply chain challenges?",
    # "Did the management provide any guidance on revenue of coinbase in 2024?",
    # "View Technology Leadership in DRAM and NAND on peers: Micron Technology, Inc., NVIDIA Corporation",
    # "View AWS Growth and Generative AI Investment on peers: Amazon.com, Inc., Microsoft Corporation",
    # "please listing snowflake's new AI product change in 24Q1",
    #####################################################################
    # real user queries, could be used for generating training data (Part 4) 16
    #####################################################################
    # "Did General Motors discuss electric vehicle production targets?",
    # "what's temu's gmv as a percentage of PDD's total gmv?",
    # "What's NVDIA say about the supply and demand of semiconducter sector?",
    # "what's the US market's GMV as a percentage of total GMV for PDD?",
    # "when should we expect tsla to launch fsd in china?",
    # "what's the market share of LLY",
    # "View history on: Operational Improvements and Cash Flow across CTLT's earning call",
    # "View Revenue Growth from New Products on peers: Eli Lilly and Company, Viking Therapeutics, Inc.",
    # "who compliant about the merger deal between catalent and novo nordisk",
    # "did patheon or TMO ceo complain about CTLT's merger deal?",
    # "What are McDonald's growth strategies for digital sales?",
    # "how much revenue did Anthropic contribute to AWS",
    # "how much did OpenAI contribute to Microsoft's revenue",
    # "What is the impact of FSD on Tesla's revenue?",
    # "what did butterfly ceo say in their investor day 2024?",
    # "aws/azure market share",
    #####################################################################
    # Synthetic queries, could be used for generating training data (Part 1) 11
    #####################################################################
    # "What are the most frequently asked questions by analysts in the past two quarters for snowflake?",
    # "Did Microsoft announce any new partnerships in its recent quarterly earnings call?",
    # "Give me all analyst questions related to ai pc for broadcom and apple?",
    # "Did Intel reveal any new semiconductor technology advancements?",
    # "How US banks talk about consumer lending trend in the last quarter?",
    # "What Apple CEO said about AI at the most recent earning call?",
    # "How does Tesla plan to address the chip shortage in the next quarter?",
    # "What are the key takeaways from the most recent earnings call of Microsoft?",
    # "Did Boeing provide updates on 737 MAX production and deliveries?",
    # "Did any pharmaceutical companies announce breakthroughs in vaccine development in their latest press release?",
    "Did Coca-Cola announce any new sustainability initiatives?",
]


class Command(BaseCommand):
    help = "Run evaluation on sample dataset and generate report"

    def add_arguments(self, parser: Any) -> None:
        parser.add_argument(
            "--num-tasks", type=int, help="num of concurrent tasks", default=4
        )

    def handle(self, *args: Any, **kwargs: Any) -> None:
        user = get_eval_user()
        asyncio.run(self.gen_eval_examples(user))

    async def gen_for_one_query(
        self, query: str, use_gpt: bool = True
    ) -> RelevantSnippetSample:
        config: AgentConfig = AgentConfig(
            new_design=True,
            doc_type_filter=[
                # DocTypeEnum.SEC_FILING.value,
                DocTypeEnum.EARNING_CALL.value,
                # DocTypeEnum.NEWS.value,  # disable "NEWS" for now
                DocTypeEnum.PRESS_RELEASE.value,
                DocTypeEnum.CONFERENCE.value,
                DocTypeEnum.YOUTUBE,
                DocTypeEnum.TWITTER,
            ],
        )
        chat_turn = await ChatTurn.objects.acreate(chat=self.chat, user_msg=query)
        agent = SearchFilterAgent(
            chat_turn=chat_turn,
            observer=self.observer,
            config=config,
            streaming=False,
        )
        fake_memory = ShortTermMemory(self.chat.user, chat_id=self.chat.id)
        try:
            await agent.clarify_context(user_msg=query, memory=fake_memory)
        except json.decoder.JSONDecodeError as e:
            logger.error(f"Failed to clarify context: {e}")
            return None

        fetched_data = await agent.fetch_data()

        if use_gpt:
            # Use GPT as teacher model to generate samples
            page_size = 3
            chunks = []
            rel_snippet_by_id = {}
            added_index_ids = set()  # to check for duplicates
            for i in range(0, len(fetched_data), page_size):
                data_chunk = fetched_data[i : i + page_size]  # noqa: E203
                try:
                    results = await agent._classify_snippets(
                        query,
                        data_chunk,
                        sys_prompt_override=RELEVANT_CLASSIFIER_SYS_PROMPT,
                        few_shots_override=RELEVANT_CLASSIFIER_FEW_SHOTS,
                    )
                except Exception as e:
                    logger.error(f"Error when classifying snippets: {e}")
                    continue
                # order might change, so we need to map back to the original
                res_by_id = {r["index_id"]: r for r in results}
                for d in data_chunk:
                    result = res_by_id.get(d.index_id)
                    if result is None:
                        logger.error(f"Result not found for index_id: {d.index_id}")
                        continue
                    if d.index_id in added_index_ids:
                        logger.error(f"Duplicate index_id: {d.index_id}")
                        continue
                    chunks.append(
                        DocChunk(
                            source=d.source,
                            content=d.content,
                            is_relevant=result["is_relevant"],
                            relevant_snippet=result["highlights"],
                            reason=result["reason"],
                        )
                    )
                    added_index_ids.add(d.index_id)
            return RelevantSnippetSample(question=query, chunks=chunks)
        else:
            # Use cross-encoder as student model to generate samples
            _, rel_snippet_by_id = await agent.score_and_filter(fetched_data, query)
            return RelevantSnippetSample(
                question=query,
                chunks=[
                    DocChunk(
                        source=d.source,
                        content=d.content,
                        is_relevant=d.index_id in rel_snippet_by_id,
                        score=d.score,
                        relevant_snippet=rel_snippet_by_id.get(d.index_id),
                    )
                    for d in fetched_data
                ],
            )

    async def gen_eval_examples(self, user: User, concurrent: int = 8) -> None:
        self.chat = await Chat.objects.acreate(user=user, platform=AppPlatform.WEB)
        self.observer = Observer()

        args_list = [(query,) for query in QUERIES]
        samples: List[RelevantSnippetSample] = await run_concurrent_tasks(
            method=self.gen_for_one_query,
            args_list=args_list,
            concurrency=concurrent,
            show_progress=True,
        )

        # report cost
        logger.warning("======= LLM Cost =======")
        logger.warning(self.observer.get_llm_costs_str())

        # write samples to file in eval directory
        eval_type = "relevant_snippet"
        filename = f"gpt_generated/{datetime.now().isoformat()}"
        with open(EVAL_SAMPLE_DATA_DIR / f"{eval_type}/{filename}.json", "w") as f:
            json.dump(
                {
                    "last_updated": datetime.now().isoformat(),
                    "samples": [s.model_dump() for s in samples if s is not None],
                },
                f,
                indent=4,
            )
