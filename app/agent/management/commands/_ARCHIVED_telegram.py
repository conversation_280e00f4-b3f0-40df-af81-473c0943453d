"""ARCHIVED"""
# # type: ignore
# # fmt: off
# import asyncio
# import logging
# import os
# from typing import List, Optional, Tuple
#
# from asgiref.sync import sync_to_async
# from django.contrib.auth.models import User
# from django.core.cache import cache
# from django.core.management.base import BaseCommand
# from django.db.models import Q
# from dotenv import load_dotenv
# from telegram import Update, constants
# from telegram.ext import (
#     ApplicationBuilder,
#     CommandHandler,
#     ContextTypes,
#     MessageHandler,
#     filters,
# )
#
# from agent.chat import ChatEngine
# from agent.enum import AppPlatform
# from agent.models import Chat, ChatTurn, Profile
# from agent.observer import Notifier, Observer, DbNotifier
# from biz.structs import FetchedData
# from common.utils.strutil import ellipse
#
# # Load environment variables from .env file
# load_dotenv()
#
# TEMP_FILE_DIR = "app/agent/temp"
# TELEGRAM_API_TOKEN = os.getenv("TELEGRAM_API_TOKEN")
#
# logger = logging.getLogger(__name__)
#
#
# async def send_tg_msg(context: ContextTypes.DEFAULT_TYPE, chat_id: int, msg: str):
#     # Telegram has a limit on resp per msg.
#     # If resp is more than 4000 characters, chunk it and send it in multiple messages
#     if len(msg) > 4000:
#         for i in range(0, len(msg), 4000):
#             await context.bot.send_message(
#                 chat_id=chat_id, text=msg[i : i + 4000]  # noqa: E203
#             )
#     else:
#         await context.bot.send_message(chat_id=chat_id, text=msg)
#
#
# class TelegramNotifier(Notifier):
#
#     def __init__(
#         self,
#         chat_turn: ChatTurn,
#         context: ContextTypes.DEFAULT_TYPE,
#         telegram_chat_id: int,
#     ):
#         self.chat_turn = chat_turn
#         self.context = context
#         self.telegram_chat_id = telegram_chat_id
#
#     async def notify(self, message: str):
#         await send_tg_msg(self.context, self.telegram_chat_id, message)
#
#     async def notify_planning(self, data: str) -> None:
#         await send_tg_msg(self.context, self.telegram_chat_id, data)
#
#     async def notify_sources(self, data: List[FetchedData | Tuple]) -> None:
#         await send_tg_msg(
#             self.context,
#             self.telegram_chat_id,
#             f"considering {len(data)} sources"
#         )
#
#
# async def echo(update: Update, context: ContextTypes.DEFAULT_TYPE):
#     await context.bot.send_message(
#         chat_id=update.effective_chat.id, text=update.message.text
#     )
#
#
# async def msg(update: Update, context: ContextTypes.DEFAULT_TYPE):
#     telegram_user = update.message.from_user
#     message = update.message.text
#
#     # TODO(sam:auth): use Profile first, when using `allauth`, there might be another way.
#     username = "telegram_" + str(telegram_user.id)
#     try:
#         user = await User.objects.filter(
#             Q(username=username) | Q(profile__sso_provider="telegram", profile__sso_id=telegram_user.id)
#         ).aget()
#         logger.debug('user with this telegram id found.')
#     except User.DoesNotExist:
#         # TODO: email
#         user = await User.objects.acreate(
#             username=username,
#             first_name=telegram_user.first_name,
#             last_name=telegram_user.last_name,
#         )
#         await Profile.objects.acreate(
#             user=user, sso_provider="telegram", sso_id=telegram_user.id
#         )
#         logger.debug('created user with this telegram id.')
#
#     # Show typing indicator while waiting for AI response
#     asyncio.create_task(
#         context.bot.send_chat_action(
#             chat_id=update.effective_chat.id, action=constants.ChatAction.TYPING
#         )
#     )
#
#     # match chat id with telegram chat id. if not, create new chat.
#     chat_id = await _get_chat_id(user.id, update.effective_chat.id)
#     chat, created = await Chat.acreate_if_id_not_exist(
#         chat_id, user=user, title=ellipse(message), platform=AppPlatform.TELEGRAM.value,
#     )
#     chat_turn = await ChatTurn.objects.acreate(chat=chat, user_msg=message)
#     await _set_chat_id(user.id, update.effective_chat.id, chat.id) if created else None
#
#     observer = Observer(
#         notifiers=[
#             TelegramNotifier(chat_turn=chat_turn, context=context, telegram_chat_id=update.effective_chat.id),
#             DbNotifier(chat_turn=chat_turn),
#         ]
#     )
#
#     chat_engine = ChatEngine(
#         user,
#         chat_turn=chat_turn,
#         observer=observer,
#         streaming=False,
#     )
#     resp = await chat_engine.answer(message)
#
#     asyncio.create_task(send_tg_msg(context, update.effective_chat.id, resp))
#
#
# def generate_conversation_cache_key(uid: int) -> str:
#     return f"{uid}_telegram_conv_key"
#
#
# async def _get_chat_id(user_id: int, telegram_chat_id: int) -> Optional[int]:
#     return await sync_to_async(cache.get)(f"{user_id}-{telegram_chat_id}")
#
#
# async def _set_chat_id(user_id: int, telegram_chat_id: int, chat_id: int) -> None:
#     asyncio.create_task(
#         sync_to_async(cache.set)(f"{user_id}-{telegram_chat_id}", chat_id, 3600 * 72)
#     )
#
#
# async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
#     await context.bot.send_message(
#         chat_id=update.effective_chat.id,
#         text="""
# Hi! I'm here to help you with your stock investment. Ask me anything!
# """,
#     )
#
#
# class Command(BaseCommand):
#     help = "Starts the telegram bot"
#
#     def handle(self, *args, **kwargs):
#         application = ApplicationBuilder().token(TELEGRAM_API_TOKEN).build()
#
#         start_handler = CommandHandler("start", start)
#         msg_handler = MessageHandler(
#             (filters.TEXT | filters.VOICE) & (~filters.COMMAND), msg
#         )
#         application.add_handler(start_handler)
#         application.add_handler(msg_handler)
#         application.run_polling()
