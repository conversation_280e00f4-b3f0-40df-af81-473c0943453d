import asyncio
import logging
import time
import traceback
from datetime import datetime, timedelta, timezone
from typing import Any, List

from django.core.management.base import BaseCommand

from agent.agents.write_market_moving_blog import WriteMarketMovingBlogAgent
from agent.conf import S3_BLOG_ENV
from agent.config.agent import WriteMarketMovingBlogAgentConfig
from agent.content import MarketMovingBlogPage
from agent.enum import CMSStatusEnum
from agent.models import CmsMarketMoving
from agent.observer import Observer
from biz import datasdk
from common.django_ext.model import User
from common.utils.asyncioutil import run_concurrent_tasks

logger = logging.getLogger(__name__)

MIN_DATE = (datetime.now(timezone.utc) - timedelta(days=3)).strftime("%Y-%m-%d")
MAX_DATE = datetime.now(timezone.utc).strftime("%Y-%m-%d")
VERSION = ""
TICKERS = datasdk.SP500_COMPANY_TICKER_LIST + datasdk.RUSSELL_2000_TICKER_LIST


class Command(BaseCommand):
    help = "write blog based on executive quotes"

    def add_arguments(self, parser: Any) -> None:
        pass

    async def write_blog(self, ticker: str) -> MarketMovingBlogPage | None:
        config = WriteMarketMovingBlogAgentConfig(min_date=MIN_DATE, max_date=MAX_DATE)
        agent = WriteMarketMovingBlogAgent(config=config, observer=Observer())
        try:
            blog = await agent.run(ticker=ticker)
            if not blog:
                return None
            blog.persist_s3(suffix=VERSION)
            editor = await User.objects.aget(username="editor")
            doc_key = (
                f"{S3_BLOG_ENV}/market-moving/detail/{blog.meta.file_name}/content.json"
            )
            defaults = blog.meta.as_dict()
            defaults["status"] = CMSStatusEnum.PUBLISHED.value
            defaults["author"] = editor
            defaults.pop("cover")
            await CmsMarketMoving.objects.aupdate_or_create(
                doc_key=doc_key, defaults=defaults
            )
            # NOTE: temporarily disabled and we manually request indexing
            blog.request_google_indexing()
        except Exception as e:
            logger.error(f"Failed to write blog for {ticker}: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
        return blog

    async def write_blogs(
        self, tickers: List[str], concurrent: int = 8
    ) -> List[MarketMovingBlogPage | None]:
        args_list = [(ticker,) for ticker in tickers]
        return await run_concurrent_tasks(
            method=self.write_blog,
            args_list=args_list,
            concurrency=concurrent,
            show_progress=True,
        )

    def handle(self, *args: Any, **kwargs: Any) -> None:
        # temprorily set logging level to avoid too much logs
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)

        # s3.put_json(BLOG_BUCKET, f"{ENV}/insights/blog-list.json", json.dumps([]))
        # return

        # batch write blogs, with batch size = 100
        batch_size = 100
        success_cnt = 0
        for i in range(0, len(TICKERS), batch_size):
            tickers = TICKERS[i : i + batch_size]
            blogs = asyncio.run(self.write_blogs(tickers=tickers))
            success_cnt += len([b for b in blogs if b is not None])
            time.sleep(10)
        logger.info(f"Successfully wrote {success_cnt}/{len(TICKERS)} blogs")
