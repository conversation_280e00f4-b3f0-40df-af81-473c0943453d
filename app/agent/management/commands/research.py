import asyncio
import j<PERSON>
from typing import Any, List

from agent.agents.research import (
    En<PERSON><PERSON>,
    EntityEnricherAgent,
    Field,
    NameField,
    ResearchAgent,
    Schema,
    ShortDescField,
    URLField,
    entities_to_tsv,
)
from playwright.async_api import async_playwright
from agent.config.agent import EntityEnricherAgentConfig, ResearchAgentConfig
from agent.observer import Observer
from aiflow.observer import AIFlowNotifier, AIFlowObserver
from django.core.management.base import BaseCommand

EXAMPLE_TASKS = {
    "HVAC": """
Build a comprehensive list of commercial HVAC companies located in the United States. This list should include company names, addresses, contact information, and any available websites.
""",
    "big_manufacturing_companies": """
Build a list of largest manufacturing companies that are integrating digital and advanced technologies into their operations. Please gather data on these companies' technological initiatives, case studies, and the impact on their manufacturing processes, as well as compile a list of their top operations executives. (Examples of titles: Senior VP/Global Head of Manufacturing or Digital Manufacturing, COO, CEO, Senior VP/Global Head of Engineering, Chief Digital Officer, Chief Data Officer, etc.)
""",
    "moving": """
Build a list of direct moving carriers in the United States. The goal is to obtain comprehensive insights into their pricing structures, policies, and any additional services they offer.
""",
    "events": """
Use the following site to gather events in the Los Angeles area:
Timeout LA
Focus on events within a 20-minute drive from Santa Monica.
Example cities to include:Venice Marina Del ReyPacific PalisadesCulver CityBeverly HillsWest Hollywood

Additional Sources to Check:
Santa Monica Events
Secret Los Angeles
We Like LA
LAistMajor Sporting Events:Search for upcoming college football, professional football, soccer, basketball, and hockey events.
Deliverable:
Compile all the gathered events into a spreadsheet and send it to me weekly.ETA: Provide an estimated time of completion for the first submission.Conduct a short project interview to confirm their availability and understanding.""",
    "ground_search_rescue": """
Create a list of at least 100+ teams in North America that do search and rescue. Include their contact emails, if there is no email, include other contact information. Ground Search and Rescue activities are primarily conducted by volunteer teams that may have a website or social media (often Facebook) presence. In the US, the Search and Rescue teams are often tied to the County Sheriff's office, but have a separate email address. Local Fire Departments, Municipal Police, and National/Regional Parks teams may also have specific Search and Rescue contacts. Search and rescue activities may be delegated to Emergency Management teams.
""",
}


def read_entities_from_tsv(fpath: str) -> List[Entity]:
    with open(fpath, "r") as f:
        lines = f.readlines()
        header = lines[0].rstrip().split("\t")
        assert header[0] == "name" and header[1] == "url"
        schema = Schema(
            fields=[
                NameField(),
                URLField(),
                *[Field(name=field) for field in header[2:]],
            ]
        )
        entities = []
        for line in lines[1:]:
            line_values = line.rstrip("\n").split("\t")
            attrs = {
                field.name: line_values[i] for i, field in enumerate(schema.fields)
            }
            name = attrs.pop("name")
            url = attrs.pop("url")
            entities.append(
                Entity(
                    name=name,
                    url=url,
                    field_value_by_name=attrs,
                )
            )
        return entities


def write_file(fpath: str, schema: Schema, table: List[Entity]) -> None:
    with open(fpath, "w") as f:
        f.write(schema.as_tsv() + "\n")
        f.write(entities_to_tsv(table))


class Command(BaseCommand):
    help = "Web research"

    async def research(self, task_name: str, schema: Schema) -> None:
        task = EXAMPLE_TASKS[task_name]
        config = ResearchAgentConfig()
        table: List[Entity] = await ResearchAgent(
            config=config, observer=Observer(notifiers=[]), streaming=False
        ).run(task=task, schema=schema)
        write_file(f"research_{task_name}.tsv", schema, table)

    async def enrich(
        self,
        task_name: str,
        schema: Schema,
        entities: List[Entity],
        print_result: bool = True,
    ) -> List[Entity]:
        task = EXAMPLE_TASKS[task_name]
        async with async_playwright() as playwright:
            # Try Firefox instead of Chromium due to ERR_HTTP2_PROTOCOL_ERROR
            browser = await playwright.firefox.launch(headless=True)

            enriched_entities: List[Entity] = []
            tasks = []
            for entity in entities:
                config = EntityEnricherAgentConfig()
                tasks.append(
                    EntityEnricherAgent(
                        config=config,
                        observer=AIFlowObserver(
                            notifiers=[
                                AIFlowNotifier(aiflow=None),
                            ]
                        ),
                        task=task,
                        task_schema=schema,
                    ).run(
                        task=task_name,
                        schema=schema,
                        entity=entity,
                        browser=browser,
                    )
                )
            results = await asyncio.gather(*tasks)
            if print_result:
                for verified_entity in results:
                    if verified_entity:
                        print(json.dumps(verified_entity.model_dump(), indent=2))
                        enriched_entities.append(verified_entity)
                    else:
                        print(f"This entity {entity.name} is not valid, skipping")

            await browser.close()

        return enriched_entities
        # write_file(f"research_{task_name}_enriched.tsv", schema, enriched_entities)

    def handle(self, *args: Any, **kwargs: Any) -> None:
        task_name = "HVAC"

        schema = Schema(
            fields=[
                NameField(),
                URLField(),
                ShortDescField(),
                Field(name="address", description="The address of the company"),
                Field(name="phone", description="The phone number of the company"),
            ]
        )

        # entities = read_entities_from_tsv(f"research_{task_name}.tsv")
        entities = [
            Entity.from_dict(schema, data)
            for data in [
                {
                    "name": "Service Logic",
                    "url": "https://www.servicelogic.com/",
                    "field_data": [
                        {
                            "field_name": "address",
                            "field_value": "",
                        },
                        {
                            "field_name": "phone",
                            "field_value": "",
                        },
                    ],
                },
                {
                    "name": "YORK",
                    "url": "https://www.york.com/",
                    "field_data": [
                        {
                            "field_name": "address",
                            "field_value": "",
                        },
                        {
                            "field_name": "phone",
                            "field_value": "",
                        },
                    ],
                },
                {
                    "name": "Daikin",
                    "url": "https://www.daikin.com/",
                    "field_data": [
                        {
                            "field_name": "address",
                            "field_value": "",
                        },
                        {
                            "field_name": "phone",
                            "field_value": "",
                        },
                    ],
                },
                {
                    "name": "Bryant",
                    "url": "https://www.bryant.com/en/us/",
                    "field_data": [
                        {
                            "field_name": "address",
                            "field_value": "",
                        },
                        {
                            "field_name": "phone",
                            "field_value": "",
                        },
                    ],
                },
                {
                    "name": "AAON Heating and Cooling Products",
                    "url": "https://www.aaon.com/",
                    "field_data": [
                        {
                            "field_name": "address",
                            "field_value": "",
                        },
                        {
                            "field_name": "phone",
                            "field_value": "",
                        },
                    ],
                },
                {
                    "name": "Carrier",
                    "url": "https://www.carrier.com/",
                    "field_data": [
                        {
                            "field_name": "address",
                            "field_value": "",
                        },
                        {
                            "field_name": "phone",
                            "field_value": "",
                        },
                    ],
                },
                {
                    "name": "Ductless",
                    "url": "https://www.ductless.com/",
                    "field_data": [
                        {
                            "field_name": "address",
                            "field_value": "",
                        },
                        {
                            "field_name": "phone",
                            "field_value": "",
                        },
                    ],
                },
                {
                    "name": "Addison HVAC",
                    "url": "https://www.addison-hvac.com/",
                    "field_data": [
                        {
                            "field_name": "address",
                            "field_value": "",
                        },
                        {
                            "field_name": "phone",
                            "field_value": "",
                        },
                    ],
                },
            ]
        ]
        asyncio.run(self.enrich(task_name, schema, entities))

        # asyncio.run(self.research(task_name, schema))
        self.stdout.write(self.style.SUCCESS("Successfully called function"))
