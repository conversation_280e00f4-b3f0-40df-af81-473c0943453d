"""ARCHIVED"""
# import argparse
# import asyncio
# from typing import Any
#
# from asgiref.sync import async_to_sync
# from django.core.management.base import BaseCommand
# from django.utils import timezone
#
# from agent.models import Metric, MetricsLoadingReport
# from biz.constants import SUPPORTED_UNIQUE_TICKER_LIST, SUPPORTED_UNIQUE_TICKER_SET
# from common.services import slack
# from common.utils.terminal import success, error, info, warn, debug
# from datasvc.constants import GENERAL_FINANCIAL_METRICS, ALL_FINANCIAL_METRICS
# from datasvc.providers.financial_modeling_prep import FMPAPI
#
# # use KEY_COMPANY_TICKER_LIST from constants.py ?
# DEFAULT_TICKERS = [
#     "AAPL",
#     "GOOG",
#     "MSFT",
#     "AMZN",
#     "META",
#     "TSLA",
#     "NVDA",
#     "AMD",
#     "CSCO",
# ]
#
#
# class Command(BaseCommand):
#     help = "Load specified quarters' metrics data for specified companies."
#
#     _report: dict[str, Any] | None = None
#
#     def add_arguments(self, parser: argparse.ArgumentParser) -> None:
#         parser.add_argument(
#             "tickers",
#             nargs="*",
#             type=str,
#             help=(
#                 "The tickers of companies to load data for. Multiple tickers are blank separated. "
#                 '"ALL" for all supported company tickers.'
#             ),
#         )
#         parser.add_argument(
#             "--concurrency",
#             "-c",
#             type=int,
#             default=10,
#             help="how many tickers are processed same time.",
#         )
#         parser.add_argument(
#             "--num-quarters",
#             "-n",
#             type=int,
#             default=4,
#             help="how many quarters until now of the metrics will be fetched.",
#         )
#         parser.add_argument(
#             "--gen-report",
#             "-r",
#             action="store_true",
#             help="shall it generates report data",
#         )
#         parser.add_argument(
#             "--print-report",
#             action="store_true",
#             help="print report",
#         )
#         parser.add_argument(
#             "--send-report",
#             action="store_true",
#             help="send report to pre-defined channel such as Slack, Email, etc.",
#         )
#         parser.add_argument(
#             "--clean-report",
#             action="store_true",
#             help="clean report. only effective with --send-report or --print-report",
#         )
#
#     def handle(self, *args: Any, **kwargs: Any) -> None:  # noqa: C901
#         tickers: list = kwargs["tickers"]
#         concurrence: int = kwargs["concurrency"]
#         quarters: int = kwargs["num_quarters"]
#         gen_report: bool = kwargs["gen_report"]
#         print_report: bool = kwargs["print_report"]
#         send_report: bool = kwargs["send_report"]
#         clean_report: bool = kwargs["clean_report"]
#
#         if len(tickers) == 0:
#             tickers = DEFAULT_TICKERS
#             warn(
#                 'WARN: ticker not present. Use "ALL" if you want all tickers (e.g load_metrics --print-report ALL). '
#                 f"Now using {tickers} by default."
#             )
#         elif len(tickers) == 1 and tickers[0].upper() == "ALL":
#             tickers = []
#         else:
#             for ticker in set(tickers):
#                 if ticker not in SUPPORTED_UNIQUE_TICKER_SET:
#                     error(
#                         f"ERROR: {ticker} is not in supported companies. "
#                         "Please check if there are typos in your input."
#                     )
#                     exit(1)
#
#         report_only = print_report or send_report
#
#         if print_report:
#             async_to_sync(self.print_report)(tickers)
#         if send_report:
#             async_to_sync(self.send_report)(tickers)
#             success("Metrics loading report successfully sent")
#         if report_only:
#             if clean_report:
#                 async_to_sync(self.clean_report)(tickers)
#             exit(0)
#
#         start_at = timezone.now()
#         async_to_sync(self.load_metrics)(tickers, quarters, concurrence)
#         success("Successfully loaded metrics.")
#         end_at = timezone.now()
#
#         if gen_report:
#             self._report = {"report": {}, "start": start_at, "end": end_at}
#
#     async def load_metrics(  # noqa: C901
#         self, tickers: list[str], quarters: int = 4, step: int = 10
#     ) -> None:
#         if not tickers:
#             tickers = SUPPORTED_UNIQUE_TICKER_LIST
#
#         # rate limit: 300 calls/min for starter plan.
#         delay = 60 / (300 / 6 / min(step, len(tickers))) + 0.1
#         start = timezone.now()
#         calls = 0
#
#         for i in range(0, len(tickers), step):
#             part_of_tickers = tickers[i : i + step]  # noqa: E203
#             info(f"Loading {quarters} quarters' metrics data for {part_of_tickers} ...")
#
#             tickers_to_numbers: dict[str, dict[str, dict]] = {
#                 ticker: {name: {} for name in ALL_FINANCIAL_METRICS}
#                 for ticker in part_of_tickers
#             }
#
#             # TODO: We could save original historical data into S3 first, then fetch from S3 store.
#             # So that we can reduce the frequency/count to call API to reduce latency or save costs.
#             # However, FMPAPI is subscribed not cost by invoking. So let it be.
#             financials = await FMPAPI().fetch_financials(
#                 tickers=part_of_tickers,
#                 period="quarter",
#                 metrics=GENERAL_FINANCIAL_METRICS,
#                 num_quarters=quarters,
#             )
#             for fin in financials:
#                 ticker, dt = fin.company_ticker, fin.date
#                 for name in GENERAL_FINANCIAL_METRICS:
#                     if name in fin.data:
#                         tickers_to_numbers[ticker][name][dt] = fin.data[name]
#
#             segments_data = await FMPAPI().fetch_revenue_segments_per_ticker(
#                 tickers=part_of_tickers,
#                 period="quarter",
#             )
#             for sd in segments_data:
#                 ticker, name = sd["ticker"], sd["name"]
#                 for k, v in sd["data"].items():
#                     tickers_to_numbers[ticker][name][k] = v
#
#             metrics_new, metrics_update = [], []
#
#             async for metric in Metric.objects.filter(ticker__in=part_of_tickers).all():
#                 ticker, name = metric.ticker, metric.name
#                 d = tickers_to_numbers.get(ticker, {}).get(name, {})
#                 increment = set(d.keys()) - set(metric.data.keys())
#                 if len(increment) > 0:
#                     metric.data.update(d)
#                     metrics_update.append(metric)
#                     self.update_report(ticker, len(increment))
#                 # clean
#                 if d:
#                     tickers_to_numbers[ticker].pop(name)
#                     if not tickers_to_numbers[ticker]:
#                         tickers_to_numbers.pop(ticker)
#
#             for ticker, metrics_data in tickers_to_numbers.items():
#                 for name, numbers in metrics_data.items():
#                     if len(numbers) > 0:
#                         metrics_new.append(
#                             Metric(ticker=ticker, name=name, data=numbers)
#                         )
#                         self.update_report(ticker, len(numbers))
#
#             # TODO: the following should be in transaction ?
#             await Metric.objects.abulk_create(metrics_new)
#             await Metric.objects.abulk_update(metrics_update, ["data"])
#             await self.save_report()
#
#             # TODO: dynamically adjust by calls/dur
#             calls += len(part_of_tickers)
#             dur = timezone.now() - start
#             debug(
#                 f"sleep {delay} seconds to avoid rate limit. ({calls} calls, {dur.seconds} seconds)"
#             )
#             await asyncio.sleep(delay)
#
#     def update_report(self, ticker: str, count: int) -> None:
#         if self._report is None:
#             return
#
#         created_at = self._report["start"]
#         report = self._report["report"]
#         if ticker in report:
#             rep = report[ticker]
#             rep.min = min(rep.min, count)
#             rep.max = max(rep.max, count)
#             rep.total += count
#         else:
#             rep = MetricsLoadingReport(
#                 ticker=ticker, min=count, max=count, total=count, created_at=created_at
#             )
#         report[ticker] = rep
#
#     async def clean_report(self, tickers: list[str]) -> None:
#         qs = MetricsLoadingReport.objects
#         if len(tickers) > 0:
#             qs = qs.filter(ticker__in=tickers)
#         await qs.all().adelete()
#
#     async def save_report(self) -> None:
#         if self._report is None:
#             return
#
#         report = self._report["report"]
#         await self.clean_report(list(report.keys()))
#         await MetricsLoadingReport.objects.abulk_create(list(report.values()))
#
#     async def load_report(self, tickers: list[str]) -> list[MetricsLoadingReport]:
#         qs = MetricsLoadingReport.objects
#         if len(tickers) > 0:
#             qs = qs.filter(ticker__in=tickers)
#         result = [m async for m in qs.all()]
#         return result
#
#     async def print_report(self, tickers: list[str], post_delete: bool = False) -> None:
#         sb = await self._gen_text_report(tickers)
#         report = "\n".join(sb)
#         info(report)
#
#         if post_delete:
#             await self.clean_report(tickers)
#
#     async def send_report(self, tickers: list[str], post_delete: bool = False) -> None:
#         sb = await self._gen_text_report(tickers)
#         report = "\n".join(sb)
#         ts = slack.send_message(report)
#         if ts and post_delete:
#             await self.clean_report(tickers)
#
#     async def _gen_text_report(self, tickers: list[str]) -> list[str]:
#         tickers_by_cnt: dict[str, list[str]] = {"1": [], "2": [], "3+": []}
#         start_time = timezone.now()
#         for rep in await self.load_report(tickers):
#             key = str(rep.max) if 2 >= rep.max else "3+"
#             tickers_by_cnt[key].append(rep.ticker)
#             if rep.created_at < start_time:
#                 start_time = rep.created_at
#         sb = [
#             "===== Metrics Loading Report =====",
#             f"Started around: `{start_time.strftime('%Y-%m-%d %H:%M:%S')} UTC`",
#             *[f"{k:2} quarters: {', '.join(v)}" for k, v in tickers_by_cnt.items()],
#         ]
#         return sb
