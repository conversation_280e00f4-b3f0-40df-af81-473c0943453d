import asyncio
import json
import traceback
from typing import Any, Optional

from django.core.management.base import BaseCommand

from agent.config.agent import PlanRetrievalAgentConfig as AgentConfig
from agent.functions import ALL_FUNCTIONS
from agent.observer import Observer


class DebugObserver(Observer):
    def log_error(self, error: BaseException, msg: Optional[str] = None) -> None:
        super().log_error(error, msg)
        print(traceback.print_exception(error))


class Command(BaseCommand):
    help = "Call functions in a adhoc way. Good for debugging."

    async def run_func(self) -> None:
        # search earning call
        search_earning_func = ALL_FUNCTIONS["fetch_numbers"]
        data = await search_earning_func.run(
            {
                "tickers": ["SMCI"],
                "metrics": [
                    "revenue"
                    # "revenue-product-segmentation",
                    # "revenue-geographic-segmentation",
                ],
            },
            config=AgentConfig(),
            observer=DebugObserver(),
        )
        print(json.dumps(data, indent=2))

    def handle(self, *args: Any, **kwargs: Any) -> None:
        asyncio.run(self.run_func())
        self.stdout.write(self.style.SUCCESS("Successfully called function"))
