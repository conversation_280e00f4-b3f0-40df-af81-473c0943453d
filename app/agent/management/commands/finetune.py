import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Any, List, Tuple

from django.core.management.base import BaseCommand
from sentence_transformers import CrossEncoder, InputExample, SentencesDataset
from torch.utils.data import DataLoader

from agent.config.agent import SearchFilterAgentConfig as AgentConfig
from agent.constants import EVAL_SAMPLE_DATA_DIR
from agent.eval.relevant_snippet import RelevantSnippetEval
from agent.models import Chat, aget_eval_user
from agent.observer import Observer
from biz.constants import PRELOADED_MODELS_DIR

logger = logging.getLogger(__name__)


# class FineTunableCrossEncoder(CrossEncoder):

#     def fine_tune(
#         self,
#         texts_a: List[str],
#         texts_b: List[str],
#         labels: List[int],
#         epochs: int = 1,
#         batch_size: int = 16,
#         learning_rate: float = 1e-5,
#     ) -> None:
#         from sentence_transformers import SentencesDataset, InputExample
#         from sentence_transformers.readers import Input<PERSON>xample
#         from sentence_transformers.losses import CosineSimilarityLoss

#         train_examples = [
#             InputExample(texts=[text_a, text_b], label=label)
#             for text_a, text_b, label in zip(texts_a, texts_b, labels)
#         ]
# train_dataset = SentencesDataset(train_examples, model=self)
# train_dataloader = DataLoader(
#     train_dataset, shuffle=True, batch_size=batch_size
# )

#         train_loss = CosineSimilarityLoss(model=self)

#         # Fit model
#         self.fit(
#             train_objectives=[(train_dataloader, train_loss)],
#             epochs=epochs,
#             warmup_steps=100,
#             scheduler="warmupcosine",
#             optimizer_params={"lr": learning_rate},
#         )


def load_data(dataset: str) -> List[Tuple[str, str, float]]:
    with open(EVAL_SAMPLE_DATA_DIR / f"relevant_snippet/{dataset}.json", "r") as f:
        data = json.loads(f.read())
        return [
            (
                line["question"],
                chunk["content"],
                1.0 if chunk["is_relevant"] else 0.0,
            )
            for line in data["samples"]
            for chunk in line["chunks"]
        ]


class Command(BaseCommand):
    help = "Run evaluation on sample dataset and generate report"

    def handle(self, *args: Any, **kwargs: Any) -> None:
        base_model_name = "cross-encoder/ms-marco-TinyBERT-L-2-v2"
        # base_model_name = "cross-encoder/ms-marco-MiniLM-L-6-v2"Z
        test_data_file = "golden-gpt4-7shots"

        # for epochs in [5, 10, 20]:
        #     for train_data_file in ["silver30", "silver60", "silver90"]:
        #         asyncio.run(
        #             self.finetune_cross_encoder(
        #                 base_model_name,
        #                 train_data_file=train_data_file,
        #                 test_data_file=test_data_file,
        #                 epochs=epochs,
        #             )
        #         )
        for epochs, train_data_file in [(5, "silver50"), (20, "silver75")]:
            asyncio.run(
                self.finetune_cross_encoder(
                    base_model_name,
                    train_data_file=train_data_file,
                    test_data_file=test_data_file,
                    epochs=epochs,
                )
            )

    async def finetune_cross_encoder(
        self,
        base_model_name: str,
        train_data_file: str,
        test_data_file: str,
        epochs: int = 20,
        concurrency: int = 8,
    ) -> None:
        # Load the data
        training_data = load_data(train_data_file)

        # Preprocess the data
        train_examples = [
            InputExample(texts=[text_a, text_b], label=label)
            for text_a, text_b, label in training_data
        ]
        train_dataset = SentencesDataset(train_examples, model=self)
        train_dataloader: DataLoader = DataLoader(
            train_dataset, shuffle=True, batch_size=16
        )
        logger.info(f"Loaded {len(train_examples)} training examples")

        # Initialize the model
        model = CrossEncoder(base_model_name)

        # Fine-tune the model
        model.fit(train_dataloader, epochs=epochs)
        new_model_name = f"{base_model_name}-finetuned-{train_data_file}-epochs{epochs}-{datetime.now().strftime('%Y-%m-%d')}"
        model.save(os.path.join(PRELOADED_MODELS_DIR, new_model_name))

        # pairs = [
        #     (
        #         "Elon Musk's latest on comments FSD?",
        #         "The latest version of Tesla\u2019s (supervised) self-driving will blow your mind",
        #     ),
        #     (
        #         "Elon Musk's latest on comments FSD?",
        #         "Under your supervision, FSD V12.3 can drive your Tesla almost anywhere",
        #     ),
        # ]
        # import ipdb

        # ipdb.set_trace()
        # print(sigmoid(model.predict(pairs)))

        # Eval on test data
        # new_model_name = (
        #     "cross-encoder/ms-marco-TinyBERT-L-2-v2-finetuned-train-2024-06-04_06:45:40"
        # )
        # new_model_name = "cross-encoder/ms-marco-TinyBERT-L-2-v2-finetuned-19q"
        # new_model_name = "cross-encoder/ms-marco-TinyBERT-L-2-v2"
        user = await aget_eval_user()
        chat = await Chat.objects.acreate(user=user)
        eval_instance = RelevantSnippetEval(
            observer=Observer(),
            chat=chat,
            config=AgentConfig(
                new_design=True,
                snippet_classifier_model_name=new_model_name,
                relevance_threshold=0.5,
            ),
        )

        data_dir = f"relevant_snippet/{test_data_file}"
        results = await eval_instance.run(data_dir, concurrency=concurrency)
        eval_instance.report_eval_result(
            results,
            "relevant_snippet",
            dataset=test_data_file,
            verbose=False,
            save_to_results_dir=True,
            send_to_slack=False,
        )
