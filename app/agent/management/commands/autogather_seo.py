import asyncio
import logging
import re
from typing import Any, Dict, List, Optional
from datetime import datetime, timezone
from django.core.management.base import BaseCommand
from tqdm import tqdm

from agent.enum import CMSStatusEnum
from agent.models import CmsAutogatherFindInfluencer
from agent.observer import Observer
from aiflow.observer import AIFlowNotifier, AIFlowObserver
from agent.agents.write_autogather_seo_blog import (
    InfluencersByRange,
    WriteAutogatherInfluencerSEOBlogAgentConfig,
    WriteAutogatherInfluencerSEOBlogAgent,
    WriteAutogatherInfluencerSummaryAgent,
    WriteAutogatherInfluencerSummaryAgentConfig,
)
from agent.agents.influencer import InfluencerFinderAgent, InfluencerSchemaInferrerAgent
from agent.config.agent import InfluencerFinderAgentConfig
from agent.content import AutogatherInfluencersBlogPageMeta
from common.django_ext.model import User

logger = logging.getLogger(__name__)

VERSION = "TEST"


class TaskShape:
    def __init__(
        self,
        task_desc: str = "",
        doc_key: str = "",
        ranges: list[str] = [],
        engagement_rate_map: dict[str, str] = {},
        current_range: str = "",
        platform: str = "",
    ):
        self.task_desc = task_desc
        self.doc_key = doc_key
        self.platform = platform
        self.ranges = ranges
        self.current_range = current_range
        self.engagement_rate_map = engagement_rate_map


class EngagementNotifier(AIFlowNotifier):
    def __init__(self, task_shape: TaskShape):
        self.task_shape = task_shape

    async def broadcast(self, data: Dict) -> None:
        pass

    async def notify(self, data: Dict) -> None:
        pass

    async def notify_text_card(
        self, text: str, title: Optional[str] = None, pin: Optional[bool] = False
    ) -> None:
        pass

    async def notify_status(self, status: str) -> None:
        pass

    async def notify_add_column(
        self,
        column_id: str,
        column_description: str,
        column_width: int,
        editable: bool,
        cell_editable: bool,
    ) -> None:
        pass

    async def notify_add_cell(
        self,
        row_id: str,
        column_id: str,
        value: str,
        sources: Optional[List[Dict]] = [],
        done: bool = False,
    ) -> None:
        if column_id == "engagement_rate":
            self.task_shape.engagement_rate_map[str(row_id)] = value

    async def notify_debug_info(
        self,
        row_id: str,
        debug_info: dict,
    ) -> None:
        pass

    async def notify_evaluation(self, row_id: str, evaluation: dict) -> None:
        pass

    async def notify_progress(self, total: int, current: int) -> None:
        pass


class Command(BaseCommand):
    help = "write autogather's blog"

    def add_arguments(self, parser: Any) -> None:
        pass

    def _add_engagement_rates(self, results: List[Dict], task_shape: TaskShape) -> None:
        """Add engagement rates to results based on task_shape's engagement_rate_map."""
        for i, result in enumerate(results):
            index_str = str(i)
            if index_str in task_shape.engagement_rate_map:
                value = task_shape.engagement_rate_map[index_str]
            else:
                value = ""

            result["engagement_rate"] = value
            match = re.search(r"\d+(?:\.\d+)?", value)
            if match:
                engagement_rate = float(match.group())
            else:
                engagement_rate = 0
            result["engagement_rate_number"] = engagement_rate

    def _add_profile_data(
        self, results: List[Dict], agent: InfluencerFinderAgent
    ) -> None:
        """Add profile data from retrieval_eval_states to results."""
        for entity in results:
            for _, eval_items in agent.retrieval_eval_states.items():
                for eval_item in eval_items:
                    if eval_item["profile"]["url"] == entity["url"]:
                        # Copy only allowed fields from profile, exclude email
                        entity["profile"] = {
                            "handle": eval_item["profile"]["handle"],
                            "full_name": eval_item["profile"]["full_name"],
                            "url": eval_item["profile"]["url"],
                            "followers": eval_item["profile"]["followers"],
                            "posts_count": eval_item["profile"]["posts_count"],
                            "biography": eval_item["profile"]["biography"],
                        }
                        entity["qualified"] = eval_item["qualified"]
                        entity["evaluation"] = eval_item["evaluation"]

    def _filter_and_sort_results(self, results: List[Dict]) -> List[Dict]:
        """Filter and sort results based on evaluation criteria and engagement rate."""

        # Filter entities to keep only those where all objects in evaluation have match="yes"
        def try_get_match(item: dict) -> bool:
            try:
                return item.get("match") == "yes"
            except AttributeError:
                return False

        filtered_results = [
            entity
            for entity in results
            if "evaluation" in entity
            and isinstance(entity["evaluation"], dict)
            and all(try_get_match(item) for _, item in entity["evaluation"].items())
        ]

        # Sort filtered results by engagement_rate_number in descending order
        filtered_results.sort(key=lambda x: x["engagement_rate_number"], reverse=True)

        # limit to 10
        return filtered_results[:10]

    async def save_blog(
        self, task_shape: TaskShape, influencers_by_range: List[InfluencersByRange]
    ) -> CmsAutogatherFindInfluencer:
        """Create and save a blog based on the filtered results."""
        editor = await User.objects.aget(username="editor")
        influencers_by_range = [i.to_dict() for i in influencers_by_range]

        defaults = {
            "title": task_shape.task_desc,
            "file_name": "",
            "author": editor,
            "status": CMSStatusEnum.DRAFT.value,
            "platform": task_shape.platform,
            "summary": "",
            "by_niche": [],
            "by_country": [],
            "by_city": [],
            "relevant_tags": [],
            "influencers_by_range": influencers_by_range,
            "influencers": [],
            "published_date": datetime.now(timezone.utc).isoformat(),
        }
        doc, _ = await CmsAutogatherFindInfluencer.objects.aupdate_or_create(
            doc_key=task_shape.doc_key, defaults=defaults
        )

        # re-generate influencers
        unique_influencers = {}
        for range_data in influencers_by_range:
            for influencer in range_data.get("influencers", []):
                url = influencer.get("url", "")
                if url and url not in unique_influencers:
                    unique_influencers[url] = {
                        "name": influencer.get("name", ""),
                        "url": url,
                        "handle": influencer.get("profile", {}).get("handle", ""),
                    }

        # Convert the dictionary values to a list
        influencers = list(unique_influencers.values())
        doc.influencers = influencers
        await doc.asave()
        return doc

    # re-generate influencers
    async def refresh_computed_data(
        self, doc: CmsAutogatherFindInfluencer
    ) -> CmsAutogatherFindInfluencer:
        unique_influencers = {}
        for range_data in doc.influencers_by_range:
            for influencer in range_data.get("influencers", []):
                url = influencer.get("url", "")
                if url and url not in unique_influencers:
                    unique_influencers[url] = {
                        "name": influencer.get("name", ""),
                        "url": url,
                        "handle": influencer.get("profile", {}).get("handle", ""),
                    }

        # Convert the dictionary values to a list
        influencers = list(unique_influencers.values())
        doc.influencers = influencers
        await doc.asave()

        return doc

    async def summary_blog(
        self, task_shape: TaskShape, doc: CmsAutogatherFindInfluencer
    ) -> AutogatherInfluencersBlogPageMeta:
        config = WriteAutogatherInfluencerSEOBlogAgentConfig()
        agent = WriteAutogatherInfluencerSEOBlogAgent(
            config=config, observer=Observer()
        )

        res = await agent.run(
            task_desc=task_shape.task_desc,
            platform=task_shape.platform,
            influencers_by_range=doc.influencers_by_range,
        )
        if doc.file_name == "":
            # Warning: If file_name is modified, it may cause 404 because the file URL has changed
            doc.file_name = res.file_name
        doc.title = res.title
        doc.summary = res.summary
        doc.by_niche = res.by_niche
        doc.by_country = res.by_country
        doc.by_city = res.by_city
        doc.relevant_tags = res.relevant_tags
        await doc.asave()
        return doc

    async def summary_influencer(
        self, task_shape: TaskShape, doc: CmsAutogatherFindInfluencer
    ) -> None:
        for range in doc.influencers_by_range:
            config = WriteAutogatherInfluencerSummaryAgentConfig()
            agent = WriteAutogatherInfluencerSummaryAgent(
                config=config, observer=Observer()
            )
            summarys = await agent.run(
                task_desc=f"{task_shape.task_desc}, have {range.get('follower_count_range')} followers",
                platform=task_shape.platform,
                influencers_by_range=range,
            )
            influencers = range.get("influencers")
            for i, summary in enumerate(summarys):
                influencers[i]["summary"] = summary
            range["influencers"] = influencers
        await doc.asave()
        return doc

    async def finder_influencer(self, task_shape: TaskShape) -> List[dict]:
        """Write summary for a task."""

        observer = AIFlowObserver(notifiers=[EngagementNotifier(task_shape)])
        schema_agent = InfluencerSchemaInferrerAgent(
            config=InfluencerFinderAgentConfig(),
            observer=observer,
        )

        task_schema, task_short_title, _ = await schema_agent.run(
            task=task_shape.task_desc
        )
        if len(task_schema.criteria) < 2:
            logger.info(f"Task description: {task_shape.task_desc}")
            logger.info(f"Task criteria: {task_schema.criteria}")
            logger.error("Task schema must have at least 2 criteria")
            raise ValueError("Task schema must have at least 2 criteria")
        finder_agent = InfluencerFinderAgent(
            config=InfluencerFinderAgentConfig(
                platform=task_shape.platform
                if task_shape.platform in ("tiktok", "instagram", "youtube")
                else "youtube",
                entities_limit=200,
            ),
            task=task_shape.task_desc,
            observer=observer,
            target_entity_definition="influencer",
            task_schema=task_schema,
            streaming=False,
        )

        editor = await User.objects.aget(username="editor")
        results = await finder_agent.run(
            username=editor.username,
            platform=task_shape.platform,
            task_short_title=task_short_title,
        )

        # exclude email, avoid send to browser
        results = [
            {k: v for k, v in result.all_fields_as_dict().items() if k != "email"}
            for result in results
        ]

        # Remove duplicates based on URL
        # I found that sometimes the same influencer is returned multiple times
        # so i remove duplicates
        unique_urls = {}
        unique_results = []
        for result in results:
            # the above two urls are the same
            # url: "https://www.instagram.com/Doctorbowl",
            # url: "https://www.instagram.com/doctorbowl"
            url = result.get("url", "").lower()
            if url and url not in unique_urls:
                unique_urls[url] = True
                unique_results.append(result)
        results = unique_results

        # Process the results
        self._add_engagement_rates(results, task_shape)
        self._add_profile_data(results, finder_agent)
        filtered_results = self._filter_and_sort_results(results)

        if len(filtered_results) == 0:
            logger.error(
                f"No filtered results found for {task_short_title}, skipping..."
            )
            return []
        return filtered_results

    async def fetch_table(self, task_shape: TaskShape) -> CmsAutogatherFindInfluencer:
        try:
            return await CmsAutogatherFindInfluencer.objects.filter(
                doc_key=task_shape.doc_key,
            ).aget()
        except CmsAutogatherFindInfluencer.DoesNotExist:
            logger.info(
                f"No existing records found for {task_shape.doc_key}, creating new records..."
            )
            influencers_by_range = []
            for range in task_shape.ranges:
                influencer = await self.finder_influencer(
                    TaskShape(
                        task_desc=f"{task_shape.task_desc}, have {range} followers",
                        platform=task_shape.platform,
                        current_range=range,
                    )
                )

                if len(influencer) > 0:
                    influencers_by_range.append(
                        InfluencersByRange(
                            follower_count_range=range, influencers=influencer
                        )
                    )
            return await self.save_blog(task_shape, influencers_by_range)
        except Exception as e:
            logger.error(f"Error fetching table for {task_shape.doc_key}: {e}")
            # Either handle the error appropriately here or return a default value
            # For example:
            return None

    async def write_summary(self, task_shape: TaskShape) -> CmsAutogatherFindInfluencer:
        # Import: define the task uniquely by doc_key
        task_shape.doc_key = f"{task_shape.task_desc}:{task_shape.platform}:{','.join(task_shape.ranges)}"

        # Fetch the table
        doc = await self.fetch_table(task_shape)

        # If the status is DRAFT, generate the summary,
        # to avoid effecting the online blog
        if doc.status == CMSStatusEnum.DRAFT.value:
            await self.refresh_computed_data(doc)

            # generate page title and summary,...
            await self.summary_blog(task_shape, doc)

            await self.summary_influencer(task_shape, doc)

        return doc

    async def write_summaries(
        self,
        list: List[TaskShape],
        concurrent: int = 1,
    ) -> List[dict]:
        summaries = []
        semaphore = asyncio.Semaphore(concurrent)

        async def call_with_semaphore(
            param: TaskShape,
        ) -> Any | None:
            async with semaphore:
                return await self.write_summary(param)

        futures = [asyncio.ensure_future(call_with_semaphore(item)) for item in list]
        for future in tqdm(
            asyncio.as_completed(futures),
            total=len(list),
            disable=False,
        ):
            summary = await future
            summaries.append(summary)
        return summaries

    def handle(self, *args: Any, **kwargs: Any) -> None:
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)

        platforms = ["tiktok", "youtube", "instagram"]
        ranges = ["1K-10K", "10K-100K", "100K-1M", "1M+"]

        tasks = [
            "dedicated parenting experts and family content creators who consistently produce high-quality content about parenting tips, family activities, and child development",
            "content creators dedicated to Bosnian culture who consistently share authentic local culture, lifestyle, and unique experiences of Bosnia",
            "content creators deeply rooted in Medellin who consistently showcase authentic Colombian culture and Medellin city life experiences",
            "content creators deeply rooted in Austin who actively engage in Texas local events and consistently share Austin's unique culture, food scene, and lifestyle",
            "content creators dedicated to Chinese culture who consistently produce quality content about Chinese culture, lifestyle, and contemporary trends",
            "Colombian fitness experts and athletes who consistently share professional workout content and promote healthy lifestyle",
            "content creators dedicated to Honduran culture who consistently share authentic local traditions and lifestyle",
            "content creators dedicated to Venezuelan culture who consistently share authentic local experiences and cultural content",
            "content creators deeply rooted in Michigan who consistently showcase local culture and lifestyle across Michigan cities",
        ]

        task_list = [
            TaskShape(
                task_desc=task,
                platform=platform,
                ranges=ranges,
            )
            for task in tasks
            for platform in platforms
        ]

        summarys = asyncio.run(self.write_summaries(task_list, 4))
        logger.info(
            f"Successfully wrote {len([b for b in summarys if b is not None])}/{len(summarys)} blogs"
        )
