import os
import logging
import xml.etree.ElementTree as ET
from collections import defaultdict
from typing import Any, Dict, List, Optional

import requests
from common.services.google.index import index_url
from common.utils.asyncioutil import run_concurrent_tasks
from django.core.management.base import BaseCommand
from google.oauth2 import service_account
from googleapiclient.discovery import build

SITE_DOMAIN = "sc-domain:onwish.ai"
SITEMAP_URL = "https://www.onwish.ai/sitemap.xml"
SERVICE_ACCOUNT_FILE = os.getenv("GOOGLE_SERVICE_ACCOUNT_CREDENTIALS_JSON_PATH")

# Scopes for the Search Console API
SCOPES = ["https://www.googleapis.com/auth/webmasters.readonly"]

# Authenticate and build the service
credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE, scopes=SCOPES
)
service = build("searchconsole", "v1", credentials=credentials)


logger = logging.getLogger(__name__)


def inspect_url(inspection_url: str) -> Dict | None:
    try:
        request = {"inspectionUrl": inspection_url, "siteUrl": SITE_DOMAIN}
        response = service.urlInspection().index().inspect(body=request).execute()
        return response["inspectionResult"]["indexStatusResult"]["coverageState"]
    except Exception as e:
        logger.error(f"Error inspecting {inspection_url}: {e}")
        return None


def fetch_sitemap_urls() -> List[str]:
    response = requests.get(SITEMAP_URL)
    if response.status_code == 200:
        root = ET.fromstring(response.content)
        urls = [
            elem.text
            for elem in root.findall(
                ".//{http://www.sitemaps.org/schemas/sitemap/0.9}loc"
            )
        ]
        return [url for url in urls if url]
    else:
        raise Exception(f"Failed to fetch sitemap: {response.status_code}")


async def check_urls(urls: List[str]) -> List[Optional[dict]]:
    args_list = [(url,) for url in urls]
    return await run_concurrent_tasks(
        method=inspect_url,
        args_list=args_list,
        concurrency=8,
        show_progress=False,
    )


class Command(BaseCommand):
    help = "SEO related commands"

    def check_index_status(self) -> List[str]:
        """Check the index status from Google of all the urls in the sitemap.
        Return a list of urls that are not indexed"""
        not_indexed = []
        urls = fetch_sitemap_urls()
        res = {url: inspect_url(url) for url in urls}
        # group by status -> all the urls with the same status
        by_status = defaultdict(list)
        for url, status in res.items():
            if status:
                by_status[status].append(url)
            else:
                by_status["Failed to inspect"].append(url)  # type: ignore

        # print the result
        print("=========== Overview ===========\n")
        for status, urls in by_status.items():
            print(f"{status}: {len(urls)}")
        print("\n=========== Not indexed ===========\n")
        for status, urls in by_status.items():
            if status != "Submitted and indexed":
                print(f"STATUS: {status}")
                for url in urls:
                    print(f" - {url}")
                    not_indexed.append(url)
                print()

        return not_indexed

    def index_urls(self, urls: List[str]) -> None:
        """Index the urls in the sitemap"""
        for url in urls:
            print(f"Indexing {url}")
            try:
                index_url(url)
            except Exception as e:
                print(f"Failed to index {url}: {e}")

    def handle(self, *args: Any, **kwargs: Any) -> None:
        not_indexed = self.check_index_status()
        self.index_urls(not_indexed)
        self.stdout.write(self.style.SUCCESS("Successfully called function"))
