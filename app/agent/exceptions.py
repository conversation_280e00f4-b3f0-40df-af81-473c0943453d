import traceback


class AgentError(Exception):
    def __init__(self, message: str) -> None:
        self.message = message
        self.traceback = traceback.format_exc()
        super().__init__(self.message)


class OutputParsingError(AgentError):
    """Raise when output from GPT model is not in expected format"""


class FunctionError(AgentError):
    """Raise when a function fails"""


class InvalidFuncParamError(AgentError):
    """Raise when a function parameter is invalid"""
