{"_name_or_path": "cross-encoder/ms-marco-TinyBERT-L-2-v2", "architectures": ["BertForSequenceClassification"], "attention_probs_dropout_prob": 0.1, "classifier_dropout": null, "gradient_checkpointing": false, "hidden_act": "gelu", "hidden_dropout_prob": 0.1, "hidden_size": 128, "id2label": {"0": "LABEL_0"}, "initializer_range": 0.02, "intermediate_size": 512, "label2id": {"LABEL_0": 0}, "layer_norm_eps": 1e-12, "max_position_embeddings": 512, "model_type": "bert", "num_attention_heads": 2, "num_hidden_layers": 2, "pad_token_id": 0, "position_embedding_type": "absolute", "sbert_ce_default_activation_function": "torch.nn.modules.linear.Identity", "torch_dtype": "float32", "transformers_version": "4.33.2", "type_vocab_size": 2, "use_cache": true, "vocab_size": 30522}