import json
import pytest
from unittest.mock import Magic<PERSON>ock, patch

from agent.agents.deep_find import IterDRAGStrategy, <PERSON><PERSON><PERSON>, EntityField


class TestIterDRAGStrategy:
    def test_convert_field_value_string(self):
        strategy = IterDRAGStrategy()
        
        # Test string conversion
        assert strategy._convert_field_value("test", {"type": "string"}) == "test"
        assert strategy._convert_field_value(123, {"type": "string"}) == "123"
        assert strategy._convert_field_value(None, {"type": "string"}) == ""
        assert strategy._convert_field_value("", {"type": "string"}) == ""
        assert strategy._convert_field_value("  ", {"type": "string"}) == ""
        
    def test_convert_field_value_integer(self):
        strategy = IterDRAGStrategy()
        
        # Test integer conversion
        assert strategy._convert_field_value(123, {"type": "integer"}) == 123
        assert strategy._convert_field_value("456", {"type": "integer"}) == 456
        assert strategy._convert_field_value(None, {"type": "integer"}) is None
        assert strategy._convert_field_value("", {"type": "integer"}) is None
        assert strategy._convert_field_value("  ", {"type": "integer"}) is None
        assert strategy._convert_field_value("not a number", {"type": "integer"}) is None
        
    def test_convert_field_value_number(self):
        strategy = IterDRAGStrategy()
        
        # Test number conversion
        assert strategy._convert_field_value(123.45, {"type": "number"}) == 123.45
        assert strategy._convert_field_value("456.78", {"type": "number"}) == 456.78
        assert strategy._convert_field_value(None, {"type": "number"}) is None
        assert strategy._convert_field_value("", {"type": "number"}) is None
        assert strategy._convert_field_value("  ", {"type": "number"}) is None
        assert strategy._convert_field_value("not a number", {"type": "number"}) is None
        
    def test_convert_field_value_boolean(self):
        strategy = IterDRAGStrategy()
        
        # Test boolean conversion
        assert strategy._convert_field_value(True, {"type": "boolean"}) is True
        assert strategy._convert_field_value(False, {"type": "boolean"}) is False
        assert strategy._convert_field_value("true", {"type": "boolean"}) is True
        assert strategy._convert_field_value("false", {"type": "boolean"}) is False
        assert strategy._convert_field_value("yes", {"type": "boolean"}) is True
        assert strategy._convert_field_value("no", {"type": "boolean"}) is False
        assert strategy._convert_field_value("1", {"type": "boolean"}) is True
        assert strategy._convert_field_value("0", {"type": "boolean"}) is False
        assert strategy._convert_field_value(None, {"type": "boolean"}) is None
        assert strategy._convert_field_value("", {"type": "boolean"}) is None
        assert strategy._convert_field_value("  ", {"type": "boolean"}) is None
        assert strategy._convert_field_value("invalid", {"type": "boolean"}) is None
        
    def test_convert_field_value_array(self):
        strategy = IterDRAGStrategy()
        
        # Test array conversion
        assert strategy._convert_field_value([1, 2, 3], {"type": "array"}) == [1, 2, 3]
        assert strategy._convert_field_value("a, b, c", {"type": "array"}) == ["a", "b", "c"]
        assert strategy._convert_field_value('["x", "y", "z"]', {"type": "array"}) == ["x", "y", "z"]
        assert strategy._convert_field_value(None, {"type": "array"}) == []
        assert strategy._convert_field_value("", {"type": "array"}) == []
        assert strategy._convert_field_value("single", {"type": "array"}) == ["single"]
        
    def test_convert_field_value_object(self):
        strategy = IterDRAGStrategy()
        
        # Test object conversion
        assert strategy._convert_field_value({"key": "value"}, {"type": "object"}) == {"key": "value"}
        assert strategy._convert_field_value('{"key": "value"}', {"type": "object"}) == {"key": "value"}
        assert strategy._convert_field_value(None, {"type": "object"}) == {}
        assert strategy._convert_field_value("", {"type": "object"}) == {"value": ""}
        assert strategy._convert_field_value("not json", {"type": "object"}) == {"value": "not json"}
        
    def test_convert_field_value_unknown_type(self):
        strategy = IterDRAGStrategy()
        
        # Test unknown type
        assert strategy._convert_field_value("test", {"type": "unknown"}) == "test"
        assert strategy._convert_field_value(123, {"type": "unknown"}) == 123
        assert strategy._convert_field_value(None, {"type": "unknown"}) is None
        
    def test_convert_field_value_no_type(self):
        strategy = IterDRAGStrategy()
        
        # Test no type specified
        assert strategy._convert_field_value("test", {}) == "test"
        assert strategy._convert_field_value(123, {}) == 123
        assert strategy._convert_field_value(None, {}) is None
