from unittest.mock import AsyncMock
from unittest.mock import patch

import pytest
from django.utils import timezone

from agent.llms.output_handlers import CitationLLMOutputHandler
from agent.models import ChatTurn, Citation
from agent.observer import Observer
from biz.enums import DocTypeEnum
from biz.search.snippet import SnippetSearcher
from biz.structs import DocMeta, Doc

TEST_CHUNKS = [
    "As",
    " of",
    " March",
    " ",
    "31",
    ",",
    " ",
    "202",
    "3",
    ",",
    " Meta",
    " had",
    " a",
    " head",
    "count",
    " of",
    " ",
    "77",
    ",",
    "114",
    " employees",
    ",",
    " which",
    " was",
    " a",
    " decrease",
    " of",
    " ",
    "1",
    "%",
    " year",
    "-over",
    "-year",
    ".",
    " This",
    " number",
    " included",
    " employees",
    " that",
    " would",
    " be",
    " impacted",
    " by",
    " the",
    " ",
    "202",
    "3",
    " layoffs",
    " [",
    "1",
    "]",
    '{"',
    "number",
    '":',
    " ",
    "1",
    ",",
    ' "',
    "source",
    '":',
    ' "',
    "https",
    "://",
    "www",
    ".sec",
    ".gov",
    "/",
    "Arch",
    "ives",
    "/",
    "ed",
    "gar",
    "/data",
    "/",
    "132",
    "680",
    "1",
    "/",
    "000",
    "132",
    "680",
    "123",
    "000",
    "067",
    "/meta",
    "-",
    "202",
    "303",
    "31",
    ".htm",
    '",',
    ' "',
    "quotes",
    '":',
    ' "',
    "Head",
    "count",
    " was",
    " ",
    "77",
    ",",
    "114",
    " as",
    " of",
    " March",
    " ",
    "31",
    ",",
    " ",
    "202",
    "3",
    ",",
    " a",
    " decrease",
    " of",
    " ",
    "1",
    "%",
    " year",
    "-over",
    "-year",
    ".",
    " Sub",
    "stant",
    "ially",
    " all",
    " employees",
    " impacted",
    " by",
    " the",
    " ",
    "202",
    "2",
    " lay",
    "off",
    " are",
    " no",
    " longer",
    " reflected",
    " in",
    " our",
    " reported",
    " head",
    "count",
    " as",
    " of",
    " March",
    " ",
    "31",
    ",",
    " ",
    "202",
    "3",
    " .",
    " Further",
    ",",
    " the",
    " employees",
    " that",
    " would",
    " be",
    " impacted",
    " by",
    " the",
    " ",
    "202",
    "3",
    " layoffs",
    " are",
    " included",
    " in",
    " our",
    " reported",
    " head",
    "count",
    " as",
    " of",
    " March",
    " ",
    "31",
    ",",
    " ",
    "202",
    "3",
    '."',
    "}",
    ".",
    "By",
    " June",
    " ",
    "30",
    ",",
    " ",
    "202",
    "3",
    ",",
    " the",
    " head",
    "count",
    " had",
    " decreased",
    " to",
    " ",
    "71",
    ",",
    "469",
    ",",
    " which",
    " was",
    " a",
    " decrease",
    " of",
    " ",
    "14",
    "%",
    " year",
    "-over",
    "-year",
    ".",
    " Approximately",
    " half",
    " of",
    " the",
    " employees",
    " impacted",
    " by",
    " the",
    " ",
    "202",
    "3",
    " layoffs",
    " were",
    " included",
    " in",
    " the",
    " reported",
    " head",
    "count",
    " as",
    " of",
    " this",
    " date",
    " [",
    "2",
    "]",
    '{"',
    "number",
    '":',
    " ",
    "2",
    ",",
    ' "',
    "source",
    '":',
    ' "',
    "https",
    "://",
    "www",
    ".sec",
    ".gov",
    "/",
    "Arch",
    "ives",
    "/",
    "ed",
    "gar",
    "/data",
    "/",
    "132",
    "680",
    "1",
    "/",
    "000",
    "132",
    "680",
    "123",
    "000",
    "093",
    "/meta",
    "-",
    "202",
    "306",
    "30",
    ".htm",
    '",',
    ' "',
    "quotes",
    '":',
    ' "',
    "Head",
    "count",
    " was",
    " ",
    "71",
    ",",
    "469",
    " as",
    " of",
    " June",
    " ",
    "30",
    ",",
    " ",
    "202",
    "3",
    ",",
    " a",
    " decrease",
    " of",
    " ",
    "14",
    "%",
    " year",
    "-over",
    "-year",
    ".",
    " Approximately",
    " half",
    " of",
    " the",
    " employees",
    " impacted",
    " by",
    " the",
    " ",
    "202",
    "3",
    " layoffs",
    " are",
    " included",
    " in",
    " our",
    " reported",
    " head",
    "count",
    " as",
    " of",
    " June",
    " ",
    "30",
    ",",
    " ",
    "202",
    "3",
    '."',
    "}",
    ".",
    "As",
    " of",
    " September",
    " ",
    "30",
    ",",
    " ",
    "202",
    "3",
    ",",
    " the",
    " head",
    "count",
    " was",
    " further",
    " reduced",
    " to",
    " ",
    "66",
    ",",
    "185",
    ",",
    " marking",
    " a",
    " decrease",
    " of",
    " ",
    "24",
    "%",
    " year",
    "-over",
    "-year",
    ".",
    " A",
    " substantial",
    " majority",
    " of",
    " the",
    " employees",
    " impacted",
    " by",
    " the",
    " layoffs",
    " were",
    " no",
    " longer",
    " included",
    " in",
    " the",
    " reported",
    " head",
    "count",
    " as",
    " of",
    " September",
    " ",
    "30",
    ",",
    " ",
    "202",
    "3",
    " [",
    "3",
    "]",
    '{"',
    "number",
    '":',
    " ",
    "3",
    ",",
    ' "',
    "source",
    '":',
    ' "',
    "https",
    "://",
    "www",
    ".sec",
    ".gov",
    "/",
    "Arch",
    "ives",
    "/",
    "ed",
    "gar",
    "/data",
    "/",
    "132",
    "680",
    "1",
    "/",
    "000",
    "132",
    "680",
    "123",
    "000",
    "103",
    "/meta",
    "-",
    "202",
    "309",
    "30",
    ".htm",
    '",',
    ' "',
    "quotes",
    '":',
    ' "',
    "Head",
    "count",
    " was",
    " ",
    "66",
    ",",
    "185",
    " as",
    " of",
    " September",
    " ",
    "30",
    ",",
    " ",
    "202",
    "3",
    ",",
    " a",
    " decrease",
    " of",
    " ",
    "24",
    "%",
    " year",
    "-over",
    "-year",
    ".",
    " A",
    " substantial",
    " majority",
    " of",
    " the",
    " employees",
    " impacted",
    " by",
    " the",
    " layoffs",
    " are",
    " no",
    " longer",
    " included",
    " in",
    " our",
    " reported",
    " head",
    "count",
    " as",
    " of",
    " September",
    " ",
    "30",
    ",",
    " ",
    "202",
    "3",
    '."',
    "}.",
    "",
]


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_citation_handler_streaming(test_chat_turn: ChatTurn) -> None:
    # Prepare the original docs data in DB
    ticker = "META"
    original_content_earning1 = "Headcount was 77,114 as of March 31, 2023, a decrease of 1% year-over-year. Substantially all employees impacted by the 2022 layoff are no longer reflected in our reported headcount as of March 31, 2023. Further, the employees that would be impacted by the 2023 layoffs are included in our reported headcount as of March 31, 2023."
    original_content_earning2 = "Headcount was 71,469 as of June 30, 2023, a decrease of 14% year-over-year. Approximately half of the employees impacted by the 2023 layoffs are included in our reported headcount as of June 30, 2023."
    original_content_earning3 = "Headcount was 66,185 as of September 30, 2023, a decrease of 24% year-over-year. A substantial majority of the employees impacted by the layoffs are no longer included in our reported headcount as of September 30, 2023."
    sec_filing1 = Doc(
        meta=DocMeta.new(
            doc_type=DocTypeEnum.SEC_FILING,
            key="sec_filing/META/2023/1",
            ticker=ticker,
            fiscal_quarter=1,
            fiscal_year=2023,
            filing_type="10-Q",
            original_url="https://www.sec.gov/Archives/edgar/data/1326801/000132680123000067/meta-20230331.htm",
            pub_date=timezone.datetime(year=2023, month=3, day=31),
        ),
        data=original_content_earning1,
    )
    sec_filing2 = Doc(
        meta=DocMeta.new(
            doc_type=DocTypeEnum.SEC_FILING,
            key="sec_filing/META/2023/2",
            ticker=ticker,
            fiscal_quarter=2,
            fiscal_year=2023,
            filing_type="10-Q",
            original_url="https://www.sec.gov/Archives/edgar/data/1326801/000132680123000093/meta-20230630.htm",
            pub_date=timezone.datetime(year=2023, month=6, day=30),
        ),
        data=original_content_earning2,
    )
    sec_filing3 = Doc(
        meta=DocMeta.new(
            doc_type=DocTypeEnum.SEC_FILING,
            key="sec_filing/META/2023/3",
            ticker=ticker,
            fiscal_quarter=3,
            fiscal_year=2023,
            filing_type="10-Q",
            original_url="https://www.sec.gov/Archives/edgar/data/1326801/000132680123000103/meta-20230930.htm",
            pub_date=timezone.datetime(year=2023, month=9, day=30),
        ),
        data=original_content_earning3,
    )
    mock_observer = AsyncMock(spec=Observer)

    # Mock out datasdk.get_doc_by_key() to return the corresponding DocChunk object
    def mock_datasdk_get_doc_by_key(url: str) -> Doc:
        if (
            url
            == "https://www.sec.gov/Archives/edgar/data/1326801/000132680123000067/meta-20230331.htm"
        ):
            return sec_filing1
        elif (
            url
            == "https://www.sec.gov/Archives/edgar/data/1326801/000132680123000093/meta-20230630.htm"
        ):
            return sec_filing2
        elif (
            url
            == "https://www.sec.gov/Archives/edgar/data/1326801/000132680123000103/meta-20230930.htm"
        ):
            return sec_filing3
        else:
            raise ValueError(f"Unexpected source: {url}")

    with patch(
        "biz.datasdk.get_doc_by_key",
        new=mock_datasdk_get_doc_by_key,
    ):
        handler = CitationLLMOutputHandler(
            chat_turn=test_chat_turn, searcher=SnippetSearcher()
        )

        output = ""
        for chunk in TEST_CHUNKS:
            c = await handler.handle_streaming_chunk(mock_observer, chunk)
            output += c if c else ""
        await handler.handle_streaming_end(mock_observer)
        assert (
            output
            == "As of March 31, 2023, Meta had a headcount of 77,114 employees, which was a decrease of 1% year-over-year. This number included employees that would be impacted by the 2023 layoffs [1].By June 30, 2023, the headcount had decreased to 71,469, which was a decrease of 14% year-over-year. Approximately half of the employees impacted by the 2023 layoffs were included in the reported headcount as of this date [2].As of September 30, 2023, the headcount was further reduced to 66,185, marking a decrease of 24% year-over-year. A substantial majority of the employees impacted by the layoffs were no longer included in the reported headcount as of September 30, 2023 [3]."
        )

        assert [
            c.to_dict(keys=["number", "url", "quotes"], excludes=[])
            for c in handler.citations
        ] == [
            c.to_dict(keys=["number", "url", "quotes"], excludes=[])
            for c in [
                Citation(
                    number=1,
                    url="https://www.sec.gov/Archives/edgar/data/1326801/000132680123000067/meta-20230331.htm",
                    quotes="Headcount was 77,114 as of March 31, 2023, a decrease of 1% year-over-year. Substantially all employees impacted by the 2022 layoff are no longer reflected in our reported headcount as of March 31, 2023 . Further, the employees that would be impacted by the 2023 layoffs are included in our reported headcount as of March 31, 2023.",
                ),
                Citation(
                    number=2,
                    url="https://www.sec.gov/Archives/edgar/data/1326801/000132680123000093/meta-20230630.htm",
                    quotes="Headcount was 71,469 as of June 30, 2023, a decrease of 14% year-over-year. Approximately half of the employees impacted by the 2023 layoffs are included in our reported headcount as of June 30, 2023.",
                ),
                Citation(
                    number=3,
                    url="https://www.sec.gov/Archives/edgar/data/1326801/000132680123000103/meta-20230930.htm",
                    quotes="Headcount was 66,185 as of September 30, 2023, a decrease of 24% year-over-year. A substantial majority of the employees impacted by the layoffs are no longer included in our reported headcount as of September 30, 2023.",
                ),
            ]
        ]


@pytest.mark.asyncio
@pytest.mark.django_db
@pytest.mark.skip(reason="TODO: adjust after implementing handle_complete")
async def test_citation_handler_complete(test_chat_turn: ChatTurn) -> None:
    handler = CitationLLMOutputHandler(chat_turn=test_chat_turn)
    test_input = """
NVIDIA's revenue grew by $4,613 million in the last quarter, from $13,507 million to $18,120 million [1][2].

@@

[1] https://www.sec.gov/Archives/edgar/data/1045810/000104581023000175/nvda-20230730.htm
"COMPANY: NVDA, DATE: 2023-07-30, PERIOD: quarter, METRIC: {'revenue': 13507000000}"

[2] https://www.sec.gov/Archives/edgar/data/1045810/000104581023000227/nvda-20231029.htm
"COMPANY: NVDA, DATE: 2023-10-29, PERIOD: quarter, METRIC: {'revenue': 18120000000}"
"""
    output = await handler.handle_complete(test_input)
    assert [
        c.to_dict(keys=["number", "url", "quotes"], excludes=[])
        for c in handler.citations
    ] == [
        c.to_dict(keys=["number", "url", "quotes"], excludes=[])
        for c in [
            Citation(
                number=1,
                source="https://www.sec.gov/Archives/edgar/data/1045810/000104581023000175/nvda-20230730.htm",
                quotes="\"COMPANY: NVDA, DATE: 2023-07-30, PERIOD: quarter, METRIC: {'revenue': 13507000000}\"",
            ),
            Citation(
                number=2,
                source="https://www.sec.gov/Archives/edgar/data/1045810/000104581023000227/nvda-20231029.htm",
                quotes="\"COMPANY: NVDA, DATE: 2023-10-29, PERIOD: quarter, METRIC: {'revenue': 18120000000}\"",
            ),
        ]
    ]
    assert output == (
        "\nNVIDIA's revenue grew by $4,613 million in the last quarter, "
        "from $13,507 million to $18,120 million [1][2].\n\n"
        "[1] https://www.sec.gov/Archives/edgar/data/1045810/000104581023000175/nvda-20230730.htm\n"
        "[2] https://www.sec.gov/Archives/edgar/data/1045810/000104581023000227/nvda-20231029.htm\n"
    )
