import logging
from datetime import datetime
from typing import <PERSON><PERSON>
from unittest.mock import Mo<PERSON>, PropertyMock, patch

import pytest
from asgiref.sync import async_to_sync
from django.contrib.auth.models import User

from agent.memory import ShortTermMemory
from agent.models import Chat, ChatMsg, ChatTurn
from agent.prompts.prompt_builder import OpenAiPromptBuilder
from biz.enums import DocTypeEnum
from biz.structs import DocChunk, DocMeta


@pytest.fixture
def setup_prompt_builder(
    test_user: User, test_chat: Chat, test_chat_turn: ChatTurn
) -> Tuple:
    logging.getLogger().setLevel(logging.INFO)
    memory = ShortTermMemory(test_user, test_chat.id, buffer_size=8)
    async_to_sync(ChatMsg.save_user_msg)(text="hi", chat_turn=test_chat_turn)
    async_to_sync(ChatMsg.save_ai_msg)(text="hi back", chat_turn=test_chat_turn)
    async_to_sync(memory.load)(mins=30, only_user_msg=False)

    fetched_data = [
        # Financials(
        #     source="sec",
        #     company_ticker="META",
        #     date="2021-10-31",
        #     report_period="annual",
        #     data={"Revenue": 100},
        # ),
        DocChunk(
            meta=DocMeta.new(
                doc_type=DocTypeEnum.SEC_FILING.value,
                ticker="META",
                key="sec_filing/META/2021/3",
                fiscal_year=2021,
                fiscal_quarter=3,
                filing_type="10-Q",
                pub_date=datetime(2021, 10, 31),
            ),
            chunk_id=1,
            data='{"Revenue": 100}',
        ),
        DocChunk(
            meta=DocMeta.new(
                doc_type=DocTypeEnum.NEWS.value,
                ticker="OPENAI",
                key="news/META/2021/3",
                title="title",
                pub_date=datetime(2021, 10, 31),
                source="openai.com",
            ),
            chunk_id=1,
            data="some news content",
        ),
        DocChunk(
            meta=DocMeta.new(
                doc_type=DocTypeEnum.EARNING_CALL.value,
                ticker="META",
                key="earning_call/META/2021/3",
                fiscal_year=2021,
                fiscal_quarter=1,
                pub_date=datetime(2021, 3, 31),
                source="sec.com",
            ),
            chunk_id=1,
            data="some earnings call content",
        ),
    ]
    return memory, fetched_data


@pytest.mark.django_db
@patch("agent.prompts.prompt_builder.datetime")
def test_build_with_only_user_msgs(
    mock_datetime: Mock, setup_prompt_builder: Tuple
) -> None:
    memory, fetched_data = setup_prompt_builder
    mock_datetime.now.return_value = datetime(2023, 10, 17, 9, 18, 54)
    prompt_builder = OpenAiPromptBuilder(
        sys_prompt="You are a helpful assistant!",
        user_msg="How's the weather?",
        llm_model_name="gpt-4",
        include_memory_msgs_type="only_user_msgs",
        memory=memory,
        fetched_data=fetched_data,
        include_cur_time=True,
    )
    expected_prompt_msgs = [
        {"role": "system", "content": "You are a helpful assistant!"},
        {"role": "user", "content": "hi"},
        {"role": "system", "content": "Current date and time: 2023-10-17 09:18:54"},
        {
            "role": "user",
            # "content": '"""\nCOMPANY: META, DATE: 2021-10-31, PERIOD: annual, METRIC: {\'Revenue\': 100}, SOURCE: sec\n"""\n"""\nINDEX_ID: News:openai.com-1 TITLE: title, SOURCE: openai.com, CONTENT: some news content\n"""\n"""\nEARNINGS CALL: INDEX_ID: Earning:META-2021-Q1-1 TICKER: META, 2021 Q1\nCONTENT: some earnings call content.\nSOURCE: earning\n"""\n\nQuestion: How\'s the weather?',
            "content": (
                '"""\n'
                "SEC FILING, INDEX_ID: `sec_filing/META/2021/3-1`, TICKER: META, "
                "2021-Q3, FILING TYPE: 10-Q, SECTION: , DOC_KEY: "
                '`sec_filing/META/2021/3`, CONTENT: {"Revenue": 100}\n'
                '"""\n'
                '"""\n'
                "NEWS, INDEX_ID: `news/META/2021/3-1`, TICKER: OPENAI, DATE: 2021-10-31, "
                "TITLE: title, DOC_KEY: `news/META/2021/3`, CONTENT: some news "
                "content\n"
                '"""\n'
                '"""\n'
                "EARNINGS CALL, INDEX_ID: `earning_call/META/2021/3-1`, TICKER: "
                "META, 2021-Q1, DOC_KEY: `earning_call/META/2021/3`, CONTENT: some "
                "earnings call content\n"
                '"""\n'
                "\n"
                "Question: How's the weather?"
            ),
        },
    ]
    assert prompt_builder.build() == expected_prompt_msgs


@pytest.mark.django_db
@patch("agent.prompts.prompt_builder.datetime")
def test_build_with_all_msgs(mock_datetime: Mock, setup_prompt_builder: Tuple) -> None:
    memory, fetched_data = setup_prompt_builder
    mock_datetime.now.return_value = datetime(2023, 10, 17, 9, 18, 54)
    prompt_builder = OpenAiPromptBuilder(
        sys_prompt="You are a helpful assistant!",
        user_msg="How's the weather?",
        llm_model_name="gpt-4",
        include_memory_msgs_type="all_msgs",
        memory=memory,
        fetched_data=fetched_data,
        include_cur_time=True,
    )
    expected_prompt_msgs = [
        {"role": "system", "content": "You are a helpful assistant!"},
        {"role": "user", "content": "hi"},
        {"role": "assistant", "content": "hi back"},
        {"role": "system", "content": "Current date and time: 2023-10-17 09:18:54"},
        {
            "role": "user",
            # "content": '"""\nCOMPANY: META, DATE: 2021-10-31, PERIOD: annual, METRIC: {\'Revenue\': 100}, SOURCE: sec\n"""\n"""\nINDEX_ID: News:openai.com-1 TITLE: title, SOURCE: openai.com, CONTENT: some news content\n"""\n"""\nEARNINGS CALL: INDEX_ID: Earning:META-2021-Q1-1 TICKER: META, 2021 Q1\nCONTENT: some earnings call content.\nSOURCE: earning\n"""\n\nQuestion: How\'s the weather?',
            "content": (
                '"""\n'
                "SEC FILING, INDEX_ID: `sec_filing/META/2021/3-1`, TICKER: META, "
                "2021-Q3, FILING TYPE: 10-Q, SECTION: , DOC_KEY: "
                '`sec_filing/META/2021/3`, CONTENT: {"Revenue": 100}\n'
                '"""\n'
                '"""\n'
                "NEWS, INDEX_ID: `news/META/2021/3-1`, TICKER: OPENAI, DATE: "
                "2021-10-31, TITLE: title, DOC_KEY: `news/META/2021/3`, CONTENT: "
                "some news content\n"
                '"""\n'
                '"""\n'
                "EARNINGS CALL, INDEX_ID: `earning_call/META/2021/3-1`, TICKER: "
                "META, 2021-Q1, DOC_KEY: `earning_call/META/2021/3`, CONTENT: some "
                "earnings call content\n"
                '"""\n'
                "\n"
                "Question: How's the weather?"
            ),
        },
    ]
    assert prompt_builder.build() == expected_prompt_msgs


@pytest.mark.django_db
@patch(
    "agent.prompts.prompt_builder.PromptBuilder.total_token_limit",
    new_callable=PropertyMock,
)
@patch("agent.prompts.prompt_builder.datetime")
def test_build_truncate_fetched_data(
    mock_datetime: Mock,
    mock_total_token_limit: PropertyMock,
    setup_prompt_builder: Tuple,
) -> None:
    memory, fetched_data = setup_prompt_builder
    mock_datetime.now.return_value = datetime(2023, 10, 17, 9, 18, 54)

    # case 1
    mock_total_token_limit.return_value = 70
    prompt_builder = OpenAiPromptBuilder(
        sys_prompt="You are a helpful assistant!",
        user_msg="How's the weather?",
        llm_model_name="gpt-4",
        include_memory_msgs_type="all_msgs",
        memory=memory,
        fetched_data=fetched_data,
        include_cur_time=True,
    )
    expected_prompt_msgs = [
        {"role": "system", "content": "You are a helpful assistant!"},
        {"role": "user", "content": "hi"},
        {"role": "assistant", "content": "hi back"},
        {"role": "system", "content": "Current date and time: 2023-10-17 09:18:54"},
        {"role": "user", "content": "\nQuestion: How's the weather?"},
    ]
    assert prompt_builder.build() == expected_prompt_msgs

    # case 2
    mock_total_token_limit.return_value = 130  # TODO(data): changed from 100
    prompt_builder = OpenAiPromptBuilder(
        sys_prompt="You are a helpful assistant!",
        user_msg="How's the weather?",
        llm_model_name="gpt-4",
        include_memory_msgs_type="all_msgs",
        memory=memory,
        fetched_data=fetched_data,
        include_cur_time=True,
    )
    expected_prompt_msgs = [
        {"role": "system", "content": "You are a helpful assistant!"},
        {"role": "user", "content": "hi"},
        {"role": "assistant", "content": "hi back"},
        {"role": "system", "content": "Current date and time: 2023-10-17 09:18:54"},
        {
            "role": "user",
            # "content": '"""\nCOMPANY: META, DATE: 2021-10-31, PERIOD: annual, METRIC: {\'Revenue\': 100}, SOURCE: sec\n"""\n\nQuestion: How\'s the weather?',
            "content": (
                '"""\n'
                "SEC FILING, INDEX_ID: `sec_filing/META/2021/3-1`, TICKER: META, "
                "2021-Q3, FILING TYPE: 10-Q, SECTION: , DOC_KEY: "
                '`sec_filing/META/2021/3`, CONTENT: {"Revenue": 100}\n'
                '"""\n'
                "\n"
                "Question: How's the weather?"
            ),
        },
    ]
    assert prompt_builder.build() == expected_prompt_msgs

    # case 3
    mock_total_token_limit.return_value = 240  # TODO(data): changed from 170
    prompt_builder = OpenAiPromptBuilder(
        sys_prompt="You are a helpful assistant!",
        user_msg="How's the weather?",
        llm_model_name="gpt-4",
        include_memory_msgs_type="all_msgs",
        memory=memory,
        fetched_data=fetched_data,
        include_cur_time=True,
    )
    expected_prompt_msgs = [
        {"role": "system", "content": "You are a helpful assistant!"},
        {"role": "user", "content": "hi"},
        {"role": "assistant", "content": "hi back"},
        {"role": "system", "content": "Current date and time: 2023-10-17 09:18:54"},
        {
            "role": "user",
            # "content": '"""\nCOMPANY: META, DATE: 2021-10-31, PERIOD: annual, METRIC: {\'Revenue\': 100}, SOURCE: sec\n"""\n"""\nINDEX_ID: News:openai.com-1 TITLE: title, SOURCE: openai.com, CONTENT: some news content\n"""\n"""\nEARNINGS CALL: INDEX_ID: Earning:META-2021-Q1-1 TICKER: META, 2021 Q1\nCONTENT: some earnings call content.\nSOURCE: earning\n"""\n\nQuestion: How\'s the weather?',
            "content": (
                '"""\n'
                "SEC FILING, INDEX_ID: `sec_filing/META/2021/3-1`, TICKER: META, "
                "2021-Q3, FILING TYPE: 10-Q, SECTION: , DOC_KEY: "
                '`sec_filing/META/2021/3`, CONTENT: {"Revenue": 100}\n'
                '"""\n'
                '"""\n'
                "NEWS, INDEX_ID: `news/META/2021/3-1`, TICKER: OPENAI, DATE: "
                "2021-10-31, TITLE: title, DOC_KEY: `news/META/2021/3`, CONTENT: "
                "some news content\n"
                '"""\n'
                '"""\n'
                "EARNINGS CALL, INDEX_ID: `earning_call/META/2021/3-1`, TICKER: "
                "META, 2021-Q1, DOC_KEY: `earning_call/META/2021/3`, CONTENT: some "
                "earnings call content\n"
                '"""\n'
                "\n"
                "Question: How's the weather?"
            ),
        },
    ]
    assert prompt_builder.build() == expected_prompt_msgs
