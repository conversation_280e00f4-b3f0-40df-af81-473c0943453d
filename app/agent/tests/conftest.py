"""Create some objects that can be used across all tests."""
from typing import Any

import pytest

from agent.enum import AppPlatform
from agent.models import Chat, ChatTurn
from common.django_ext.model import User


@pytest.fixture(scope="function")
def test_user(db: Any) -> User:
    return User.objects.create(id=1)


@pytest.fixture(scope="function")
def test_chat(test_user: User) -> Chat:
    return Chat.objects.create(
        user=test_user, title="unittest chat 1", platform=AppPlatform.TEST.value
    )


@pytest.fixture(scope="function")
def test_chat_turn(test_chat: Chat) -> ChatTurn:
    return ChatTurn.objects.create(chat=test_chat)
