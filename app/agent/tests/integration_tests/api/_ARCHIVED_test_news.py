"""ARCHIVED"""
# import pytest
#
#
# @pytest.mark.skip(
#     reason="ARCHIVED. Currently we don't use WebSearchAPI, will re-add if needed."
# )
# @pytest.mark.django_db
# def test_fetch() -> None:
#     queries = [
#         "google stock price",
#     ]
# config = PlanRetrievalAgentConfig()
# fetched_docs = async_to_sync(
#     WebSearchAPI(
#         config=config,
#         with_full_content=True,
#         verify_ssl=False,
#     ).fetch_news
# )(
#     queries,
#     from_date=datetime(2023, 9, 20),
#     to_date=datetime(2023, 9, 27),
# )
# titles = [d.title for d in fetched_docs]
# titles_str = "\n".join(titles)
# logging.info(f"News API fetched titles:\n{titles_str}")
#
# embedding = OpenAIEmbeddingModel()
# for t in titles:
#     assert embedding.text_similarity(queries[0], t) > 0.3
# for d in fetched_docs:
#     assert len(d.content) > 300
#     assert d.published_at < datetime(
#         2023, 9, 28, tzinfo=timezone.utc
#     ) and d.published_at > datetime(2023, 9, 19, tzinfo=timezone.utc)
