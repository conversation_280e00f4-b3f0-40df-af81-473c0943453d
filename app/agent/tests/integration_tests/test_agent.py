import logging
from typing import Tuple, List, Set, Union

import pytest
from asgiref.sync import async_to_sync
from django.contrib.auth.models import User

from agent.chat import ChatEngine
from agent.config.doc import DOC_TYPE_TO_FUNCTIONS
from agent.constants import NO_FETCHED_DATA_MSG
from agent.enum import MessageRoleEnum, AppPlatform
from agent.models import Chat, ChatTurn
from agent.observer import DbNotifier, Observer
from agent.prompts.default import ANSWER_NOT_FOUND_MSG
from biz.enums import DocTypeEnum


class TestAgent:
    """Be aware that this test is costly to run due to openai api call."""

    all_doc_type_filters: List[str] = []

    def setup_method(self) -> None:
        self.user = User.objects.create(id="123")
        self.chat = Chat.objects.create(
            user=self.user, title="", platform=AppPlatform.TEST.value
        )
        self.chat_turn = ChatTurn.objects.create(chat=self.chat)
        self.all_doc_type_filters = [
            name for name in DOC_TYPE_TO_FUNCTIONS.keys() if not name.startswith("_")
        ]
        logging.getLogger().setLevel(logging.INFO)

    def _call_agent(
        self,
        msg: str,
        doc_type_filters: List[str],
    ) -> Tuple[ChatEngine, str]:
        observer: Observer = Observer()
        observer.notifiers = [DbNotifier(chat_turn=self.chat_turn)]
        chat_engine = ChatEngine(
            self.user,
            chat_turn=self.chat_turn,
            observer=observer,
            streaming=False,
        )
        resp = async_to_sync(chat_engine.answer)(msg, doc_type_filters)
        logging.info(f"Test Agent response: {resp}")
        return chat_engine, resp

    def _check_chat_msgs(self) -> None:
        """Check if chat msgs are saved to db"""
        chat_msgs = list(self.chat.chatmsg_set.all())
        assert chat_msgs[0].role == MessageRoleEnum.USER.value
        assert chat_msgs[1].role == MessageRoleEnum.PLANNING.value
        assert "Thinking hard" in chat_msgs[1].text
        assert chat_msgs[2].role == MessageRoleEnum.PLANNING.value
        assert "My thoughts" in chat_msgs[2].text
        assert chat_msgs[-1].role == MessageRoleEnum.ASSISTANT.value
        # delete all chat msgs in self.chat for next test
        self.chat.chatmsg_set.all().delete()
        # TODO: Check display msgs are saved to db as well!

    @pytest.mark.skip(reason="temporarily disable to save costs")
    @pytest.mark.django_db
    def test_smoke_plan_retrieval_agent(self) -> None:
        """Just to make sure agent runs successfully returns a response"""
        chat_engine, resp = self._call_agent(
            msg="why meta's stock price declined in 2022?",
            doc_type_filters=self.all_doc_type_filters,
        )
        # make sure it fetches enough data
        assert len(chat_engine.observer.retrieval["fetched_data"]) > 3
        assert resp

    @pytest.mark.skip(reason="temporarily disable to save costs")
    @pytest.mark.django_db
    def test_fetch_financials(self) -> None:
        chat_engine, resp = self._call_agent(
            msg="How much is META's revenue in 2023 Q1?",
            doc_type_filters=self.all_doc_type_filters,
        )
        self._check_chat_msgs()
        assert "$ 28,645" in resp or "$28,645" in resp or "$28.645" in resp

    @pytest.mark.skip(reason="temporarily disable to save costs")
    @pytest.mark.django_db
    def test_search_earning_calls(self) -> None:
        chat_engine, resp = self._call_agent(
            msg="How are analysts questions changing from 2023 q1 to q2 calls for Meta?",
            doc_type_filters=self.all_doc_type_filters,
        )
        assert "AI" in resp
        assert "hiring" in resp
        assert "Threads" in resp

    @pytest.mark.skip(reason="temporarily disable to save costs")
    @pytest.mark.django_db
    def test_search_news(self) -> None:
        chat_engine, resp = self._call_agent(
            msg="what did zuck say about AI Agent in the past earning call? (as of 2023-12)",
            doc_type_filters=["NEWS"],
        )

        self._expect_planning_msgs(chat_engine, "🤖 Searching web with")
        assert resp == NO_FETCHED_DATA_MSG or resp == ANSWER_NOT_FOUND_MSG

    @pytest.mark.skip(reason="temporarily disable to save costs")
    @pytest.mark.django_db
    def test_search_financial_statement(self) -> None:
        chat_engine, resp = self._call_agent(
            msg="what did zuck say about AI Agent in the past earning call? (as of 2023-12)",
            doc_type_filters=[DocTypeEnum.SEC_FILING],
        )

        self._expect_planning_msgs(chat_engine, "🤖 Searching SEC filings")
        assert resp == NO_FETCHED_DATA_MSG or resp == ANSWER_NOT_FOUND_MSG

    @pytest.mark.skip(reason="temporarily disable to save costs")
    @pytest.mark.django_db
    def test_search_earning_call(self) -> None:
        chat_engine, resp = self._call_agent(
            msg="what did zuck say about AI Agent in the past earning call? (as of 2023-12)",
            doc_type_filters=["EARNING_CALL"],
        )

        self._expect_planning_msgs(chat_engine, "🤖 Searching earning calls")
        assert "Zuckerberg" in resp
        assert "AI" in resp
        assert "earning call" in resp

    def _expect_planning_msgs(
        self, chat_engine: ChatEngine, texts: Union[str, Set[str]]
    ) -> None:
        if isinstance(texts, str):
            texts = {texts}
        planning_msgs = {
            "🤖 Searching web with",
            "🤖 Searching SEC filings",
            "🤖 Searching earning calls",
        }
        unexpected_msgs = planning_msgs - texts

        expected_found = {}
        unexpected_found = ""

        for msg in chat_engine.chat.chatmsg_set.filter(
            role=MessageRoleEnum.PLANNING.value
        ).all():
            for t in texts:
                if t in msg.text:
                    expected_found[t] = msg.text
                    break
            if not unexpected_found:
                for t in unexpected_msgs:
                    if t in msg.text:
                        unexpected_found = msg.text
                        break

        assert len(expected_found) == len(
            texts
        ), f"expected search not found. {','.join(texts - set(expected_found.keys()))}"
        assert not unexpected_found, f"unexpected search found {unexpected_found}"
