gold-beast-system/                      # Project root directory
├── README.md                           # Project documentation
├── main.py                             # System entry point
├── requirements.txt                    # Project dependencies
├── project_structure.txt               # This file
├── .gitignore                          # Git ignore file
├── api/                                # API server
│   ├── README.md                       # API documentation
│   ├── __init__.py
│   ├── routers/                        # API route handlers
│   │   ├── __init__.py
│   │   ├── axioma_router.py            # Axioma attribution API
│   │   ├── backtrader_router.py        # Backtrader API
│   │   ├── brinson_router.py           # Brinson attribution API
│   │   └── quantstats_router.py        # QuantStats API
│   ├── utils/                          # API utilities
│   │   ├── __init__.py
│   │   └── visualization.py            # Visualization utilities
│   ├── main.py                         # FastAPI application
│   └── run_server.py                   # Server entry point
├── backtest_system/                    # Backtesting system
│   ├── __init__.py
│   ├── backtrader_result.py            # Backtrader result class
│   ├── data/                           # Backtesting data
│   │   ├── __init__.py
│   │   ├── feed_builder.py             # Feed builder wrapper (imports from data_system.loader)
│   │   └── utils.py                    # Data utilities
│   ├── engine/                         # Backtesting engines
│   │   ├── __init__.py
│   │   └── backtrader_engine.py        # Backtrader engine implementation
│   ├── portfolio_metrics.py            # Portfolio performance metrics
│   └── visualization.py                # Visualization tools
├── core/                               # Core tools and configuration
│   ├── __init__.py
│   ├── conf/                           # Configuration files
│   │   ├── __init__.py
│   │   ├── matplotlib_config.py        # Matplotlib configuration
│   │   └── mlflow_config.py            # MLflow configuration
│   ├── docs/                           # Documentation
│   │   └── project_structure.md        # Project structure docs
│   ├── scripts/                        # Utility scripts
│   │   ├── __init__.py
│   │   └── start_mlflow_ui.py          # Start MLflow UI
│   └── utils/                          # Utility functions
│       ├── __init__.py
│       ├── backtest_utils.py           # Backtesting utilities
│       ├── date_utils.py               # Date utilities
│       ├── db_utils.py                 # Database utilities
│       ├── logger.py                   # Logging utilities
│       ├── mlflow_utils.py             # MLflow utilities
│       ├── paths.py                    # Path constants
│       ├── report_utils.py             # Report generation utilities
│       ├── viz_utils.py                # Visualization utilities
│       └── quantstats_integration.py   # QuantStats integration utilities
├── data_system/                        # Data system
│   ├── __init__.py
│   ├── collectors/                     # Data collectors
│   │   ├── __init__.py
│   │   └── data_collector.py           # Data collector
│   ├── datasets/                       # Data storage
│   │   ├── raw_data/                   # Raw data files
│   │   └── processed_data/             # Processed data files
│   │       └── jkp/                    # JKP data
│   │           └── full_usa.pkl        # USA data
│   ├── features/                       # Feature engineering
│   │   └── __init__.py
│   ├── ingestion/                      # Data ingestion
│   │   ├── __init__.py
│   │   └── feed/                       # Feed builders
│   │       └── __init__.py
│   ├── loader.py                       # Data loading and feed building utilities
│   ├── processors/                     # Data processors
│   │   ├── __init__.py
│   │   ├── base_processor.py           # Base processor
│   │   └── ohlcv_processor.py          # OHLCV data processor
│   └── scripts/                        # Data scripts
│       ├── __init__.py
│       └── import_to_db.py             # Import data to database
├── examples/                           # Example scripts
│   ├── axioma_attribution_example.py   # Axioma attribution example
│   ├── backtrader_example.py           # Backtrader example
│   ├── brinson_attribution_example.py  # Brinson attribution example
│   └── portfolio_analysis_example.py   # Portfolio analysis example
├── gold-beast-ui/                      # Frontend application
│   ├── README.md                       # Frontend documentation
│   ├── package.json                    # NPM dependencies
│   ├── src/                            # Source code
│   │   ├── components/                 # Vue components
│   │   │   └── report/                 # Report components
│   │   │       ├── ReportViewer.vue    # Main report viewer
│   │   │       ├── BlockRenderer.vue   # Block renderer
│   │   │       ├── SvgBlock.vue        # SVG chart renderer
│   │   │       ├── TableBlock.vue      # Table renderer
│   │   │       ├── MetricsBlock.vue    # Metrics renderer
│   │   │       └── TextBlock.vue       # Text renderer
│   │   ├── views/                      # Vue views
│   │   │   ├── axioma/                 # Axioma attribution view
│   │   │   ├── backtrader/             # Backtrader view
│   │   │   ├── brinson/                # Brinson attribution view
│   │   │   └── quantstats/             # QuantStats view
│   │   ├── router/                     # Vue Router configuration
│   │   ├── store/                      # Pinia store
│   │   ├── App.vue                     # Root component
│   │   └── main.ts                     # Application entry point
│   └── vite.config.ts                  # Vite configuration
├── model_system/                       # Model system
│   ├── __init__.py
│   ├── conf/                           # Configuration files
│   │   ├── __init__.py
│   │   └── project_aipm/               # AIPM project configuration
│   │       └── config.yaml             # Configuration for AIPM models
│   ├── data/                           # Data module
│   │   ├── __init__.py
│   │   ├── data_module.py              # Data module implementation
│   │   └── processors/                 # Data processors
│   │       ├── __init__.py
│   │       └── optimized_portfolio_processor.py # Portfolio data processor
│   ├── models/                         # Model definitions
│   │   ├── __init__.py
│   │   ├── base_model.py               # Base model class
│   │   ├── improved_transformer.py     # ImprovedPortfolioTransformer model
│   │   ├── model_factory.py            # Model factory for loading models
│   │   └── losses/                     # Loss functions
│   │       ├── __init__.py
│   │       └── msrr_loss.py            # MSRR loss
│   ├── model_eval/                     # Model evaluation
│   │   ├── __init__.py
│   │   └── micro_bt.py                 # Micro backtesting utilities
│   ├── scripts/                        # Training scripts
│   │   ├── __init__.py
│   │   └── run_experiment.py           # Script for running experiments
│   ├── trainer/                        # Model trainers
│   │   ├── __init__.py
│   │   └── model_trainer.py            # Model trainer implementation
│   ├── utils/                          # Utilities
│   │   ├── __init__.py
│   │   ├── rolling.py                  # Rolling window utilities
│   │   └── window.py                   # Window slicing utilities
│   └── outputs/                        # Model outputs and results
│       ├── artifacts/                  # Production-ready models
│       ├── experiments/                # Experiment results
│       └── mlruns/                     # MLflow experiment metadata
├── notebooks/                          # Jupyter notebooks
│   └── feature_exploration.ipynb       # Feature exploration
├── trading_system/                     # Trading system
│   ├── __init__.py
│   ├── strategies/                     # Trading strategies
│   │   ├── __init__.py
│   │   ├── base_strategy.py            # BaseStrategy abstract class
│   │   ├── bt_portfolio_strategy.py    # Backtrader portfolio strategy
│   │   └── portfolio_strategy.py       # PortfolioStrategy implementation
│   ├── execution/                      # Order execution
│   │   ├── __init__.py
│   │   └── execution_engine.py         # Execution engine
│   └── risk/                           # Risk management
│       ├── __init__.py
│       └── risk_manager.py             # RiskManager implementation
└── venv/                               # Virtual environment (gitignored)
