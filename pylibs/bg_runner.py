"""A background runner to serve all kinds of background services.

TODO:
1. add thread safe code.
2. add interface `BackgroundRunnable` to make its children can run as bg daemon.

"""

import threading
from logging import getLogger
from typing import Any, Callable


logger = getLogger(__name__)


BackgroundRunnableServeFunc = Callable[[threading.Event], None]

# class BackgroundRunnable(Protocol):
#     # run_as_bg_daemon:BackgroundRunnableServeFunc
#
#     name: str
#     stop_event: threading.Event
#     thread: threading.Thread|None = None
#
#     def run_as_bg_daemon(self, stop_event:threading.Event)->None:
#         """run as a background daemon
#
#         :param stop_event: the background runnable must watch the event to stop serving
#         :return:
#         """


class BackgroundJob:
    name: str
    run_as_bg_daemon: BackgroundRunnableServeFunc
    stop_event: threading.Event
    thread: threading.Thread | None = None

    def __init__(self, name: str, start: BackgroundRunnableServeFunc) -> None:
        self.name = name
        self.run_as_bg_daemon = start
        self.stop_event = threading.Event()


class BackgroundRunner:
    _t: threading.Thread | None = None
    _stop_flag: threading.Event = threading.Event()
    _jobs: dict[str, BackgroundJob] = dict()
    _is_started: bool = False

    # def __init__(self) -> None:
    #     import signal
    #     from djcommon.utils import is_serving_as_django_runserver, is_serving_as_sgi
    #
    #     if is_serving_as_django_runserver() or is_serving_as_sgi():
    #         signal.signal(signal.SIGINT, self._stop_self_handler)
    #         signal.signal(signal.SIGTERM, self._stop_self_handler)

    def __del__(self) -> None:
        self.stop()

    @property
    def is_running(self) -> bool:
        return self._is_started

    def start(self) -> None:
        self._t = threading.Thread(target=self._start_jobs, name="bg_runner", daemon=True)
        self._t.start()
        self._is_started = True
        logger.warning("BackgroundRunner started.")

    def _start_jobs(self) -> None:
        for job in self._jobs.values():
            self._start_a_job(job)

    def _start_a_job(self, job: BackgroundJob) -> None:
        assert job.thread is None
        if not self._is_started:
            logger.warning(f"Can not start a job while {self.__class__.__name__} is not started.")
            return
        t = threading.Thread(
            target=job.run_as_bg_daemon,
            name=f"bg_{job.name}",
            daemon=True,
            args=(job.stop_event,),
        )
        job.thread = t
        t.start()
        logger.debug(f'bg job "{job.name}" started.')

    def stop(self, name: str = "") -> None:
        job: BackgroundJob | None
        if not name:
            # stop all then stop self
            for job in list(self._jobs.values()):
                self._stop_a_job(job)
            # logger is already stopped here
            print("stopping bg job manager")
            self._stop_flag.set()
        else:
            job = self._jobs.get(name)
            self._stop_a_job(job)
        self._is_started = False

    def _stop_a_job(self, job: BackgroundJob | None) -> None:
        if job:
            logger.debug(f"stopping bg job - {job.name}")
            job.stop_event.set()
            if job.name in self._jobs:
                self._jobs.pop(job.name)

    def _stop_self_handler(self, sig_num: int, frame: Any) -> None:
        self.stop()

    def add(self, name: str, start: BackgroundRunnableServeFunc) -> None:
        if name in self._jobs:
            logger.debug(f'ignore new job "{name}" due to it was running')
            return

        job = BackgroundJob(name=name, start=start)
        self._jobs[name] = job
        if self._is_started:
            self._start_a_job(job)


__bg_runner = BackgroundRunner()


def get_bg_runner() -> BackgroundRunner:
    return __bg_runner
