"""
Database access wrappers for Django ORM
"""

import sys
import os
import django
from django.conf import settings
import logging

log = logging.getLogger("djorm")


def setup_django_orm(settings_module):
    if settings.configured:
        log.debug("Skip: Django ORM already configured.")
        return
    setting_dict = {k: v for k, v in settings_module.__dict__.items() if k.isupper()}
    settings.configure(**setting_dict)
    django.setup()
    log.info("Django ORM configured.")


def manage(settings_module_str: str = "djcommon.djorm.settings"):
    """Run administrative tasks."""
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", settings_module_str)
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)
