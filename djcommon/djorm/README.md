# djorm

Helper to use a Django ORM app across Django or None-Django projects.

## create your own `myorm` app

- create app `myorm`

```sh
django-admin startapp myorm
```

- create `manage.py` (if not presents) as below. 

Function `manage()` can take a string arg to specify `settings module`.
If no argument specified, default value is `djcommon.djorm.settings`.
Better create your own `mydjorm/settings.py` and use it as `manage('mydjorm.settings')`.

```python
from djcommon.djorm.configure import manage

if __name__ == '__main__':
    manage()

```

- create your models in this app by using `Mixin`s
- migrate to generate db tables.

```sh
python manage.py makemigrations
python manage.py migrate
```

## use in another Django project

- add to `INSTALLED_APPS`

## use in another None-Django project

- import setup at very beginning before using the models from your `djorm` app.
