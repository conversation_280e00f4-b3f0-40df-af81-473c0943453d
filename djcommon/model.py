import json
import sys
from datetime import datetime
from typing import Iterable, Self, Optional, Any, get_origin

from asgiref.sync import sync_to_async
from django.contrib.auth import get_user_model
from django.db import models, connections, DEFAULT_DB_ALIAS, transaction

from pycommon.utils.lang import BaseEnum, JsonEncoder, JsonDecoder

try:
    from pydantic import BaseModel
    from pydantic_core import PydanticUndefined
except ImportError:
    # in case of pydantic not installed
    BaseModel = None  # type: ignore
    PydanticUndefined = None  # type: ignore

User = get_user_model()


class DictMixin(models.Model):
    # id = models.BigAutoField(primary_key=True)

    _dict_keys: Iterable[str] = []  # fields will be output to dict
    _dict_excludes: Iterable[str] = []  # fields will not be output to dict
    _store_keys: Iterable[str] = []  # fields are stored in Storage (Object, CDN, ...).

    @property
    def dict_keys(self) -> Iterable[str]:
        # TODO: make it compatible with django Meta fields/excludes
        # if len(self._dict_keys) == 0 and hasattr(self.Meta, "fields"):
        #     self._dict_keys = self.Meta.fields
        return self._dict_keys

    @classmethod
    def from_dict(cls, d: dict) -> Self:
        """create instance form a dict"""
        obj = cls()
        obj.update_from_dict(d)
        return obj

    def to_dict(
        self,
        keys: Optional[Iterable[str]] = None,
        excludes: Optional[Iterable[str]] = None,
    ) -> dict:
        """
        :param keys: fields to be included in dict. None - ignore. empty([],(),{}) - override cls._dict_keys
        :param excludes: fields to be excluded from dict. None - ignore, empty([],(),{})  - override cls._dict_exclude
        :return:
        """

        def handle_value(value: Any) -> Any:
            """Converts datetime to string, calls to_dict if possible, or returns the value."""
            if hasattr(value, "to_dict"):
                return value.to_dict()
            else:
                return value

        d = self.__dict__
        keys = set(keys if keys is not None else self.dict_keys)
        excludes = set(excludes if excludes is not None else self._dict_excludes)
        assert not keys & excludes, "conflict in keys and excludes"
        keys = keys or {k for k in d.keys() if not k.startswith("_")}
        keys = {f for f in keys if f not in excludes}
        return {k: handle_value(d[k]) for k in keys}

    def update_from_dict(self, d: dict, keys: Optional[Iterable[str]] = None) -> None:
        keys = set(keys if keys is not None else self.dict_keys)
        keys = keys or {k for k in self.__dict__.keys() if not k.startswith("_")}
        for k, v in d.items():
            setattr(self, k, v) if keys and k in keys and hasattr(self, k) else None

    def update_from_object(self, obj: Any, keys: Iterable[str] = ()) -> None:
        keys = keys or self.dict_keys
        for k in keys:
            v = getattr(obj, k)
            setattr(self, k, v) if v and hasattr(self, k) else None

    def to_json_str(
        self,
        keys: Optional[Iterable[str]] = None,
        excludes: Optional[Iterable[str]] = None,
    ) -> str:
        return json.dumps(self.to_dict(keys, excludes), indent=4, sort_keys=True)

    class Meta:
        abstract = True


class UserMixin(models.Model):
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
    )

    @sync_to_async
    def get_user(self) -> User:  # type: ignore
        return self.user

    class Meta:
        abstract = True


class UserOneToOneMixin(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)

    @sync_to_async
    def get_user(self) -> User:  # type: ignore
        return self.user

    class Meta:
        abstract = True


class CreateTimeMixin(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, null=False)

    class Meta:
        abstract = True

    @property
    def created_at_str(self) -> str:
        # TODO(data): make it timezone aware
        return self.created_at.strftime("%Y-%m-%d %H:%M:%S")


class UpdateTimeMixin(models.Model):
    updated_at = models.DateTimeField(auto_now=True, null=False)

    class Meta:
        abstract = True

    @property
    def updated_at_str(self) -> str:
        # TODO(data): make it timezone aware
        return self.updated_at.strftime("%Y-%m-%d %H:%M:%S")


# Django class models
def convert_pydantic_to_django(  # noqa: C901
    pydantic_model: type[BaseModel],
    django_model_name: str,
    python_module: str,
    base_django_models: list[type(models.Model)] | None = None,  # type: ignore
    extra_fields: list[str] | None = None,
) -> type[models.Model]:
    """convert a Pydantic model to Django model required attributes.
    use `django_model = type(django_model_name, tuple(base_django_models), attributes)`
    to create Django model.

        - django_model_name: name of generated Django Model.
        - base_django_models: base Django models for this generated model.

    :param pydantic_model: the Pydantic which is being converted.
    :param django_model_name: name of generated Django Model.
    :param python_module: the python module (package) name. just use __name__.
    :param base_django_models: base Django models for this generated model.
    :param extra_fields: extra fields which will not be converted automatically.
    :return:
    """

    # instruction for customized attributes for Field
    """
    Meta Models are designed to be easily converted to the following which have same fields.
    - Django model
    - Python structs
    - Index (VectorDB) meta
    e.g. A meta named "MyMeta", it has properties "foo", "bar" and "baaz".
    - With `MyMetaModel = convert_pydantic_to_django(MyMeta, "MyMetaModel")`, you will got a MyMetaModel Django model.
    it will has "foo", "bar" and "baaz" columns.
    - With `class MyMetaStruct(MyMeta):`, you will got a MyMetaStruct Python class with "foo", "bar", "baaz" properties.
    - While indexing, the indexer will index these fields ("foo", "bar", "baaz") as well if specified "index_meta".
    Instruction for customized attributes for Field
    - index_filter: this field will be indexed as Index(vectorDB) meta and it can be filtered.
    - index_meta: this field will be indexed as Index(vectorDB) meta and it can NOT be filtered.
    - db_index: this field will be added to DB index when it added as DB Column. (django model indexed=True)
    - db_unique: this field will be set as unique when it added as DB Column. (django model unique=True)
    - db_excluded: this field will NOT be added as a DB column if True.
    """

    if BaseModel is None or PydanticUndefined is None:
        raise ImportError("can not import pydantic classes.")

    class DjangoModelMeta:
        pass

    base_django_models = base_django_models or [models.Model]
    extra_fields = extra_fields or []

    django_model_meta_cls = getattr(pydantic_model, "DjangoModelMeta", DjangoModelMeta)
    attributes = {"__module__": python_module, "Meta": django_model_meta_cls}

    for field_name, field in pydantic_model.model_fields.items():
        extra: dict = field.json_schema_extra or {}  # type: ignore

        if extra.get("db_excluded", False):
            continue  # Skip this field

        field_type = field.annotation
        assert field_type is not None
        django_field = None

        # Determine database features.
        required = field.is_required()
        max_length: int = 255
        for m in field.metadata:
            if hasattr(m, "max_length") and m.max_length and m.max_length > 0:
                max_length = m.max_length
                break
        blank = not required

        django_common_fields = {
            "null": False,
            "unique": extra.get("db_unique", False),
            "db_index": extra.get("db_index", False),
            "help_text": field.description or "",
        }
        if extra.get("db_choices"):
            django_common_fields["choices"] = extra.get("db_choices")

        if field.default not in [PydanticUndefined, None]:
            django_common_fields["default"] = field.get_default()
        if field.default_factory not in [PydanticUndefined, None]:
            django_common_fields["default"] = field.default_factory

        if field_type.__name__ == "Literal":
            choices = [(item, item) for item in field_type.__args__]
            django_field = models.CharField(
                max_length=max_length,
                choices=choices,
                **django_common_fields,
            )
        elif issubclass(field_type, str):
            if max_length == sys.maxsize:
                django_field = models.TextField(
                    blank=blank,
                    **django_common_fields,
                )
            elif issubclass(field_type, BaseEnum):
                choices = field_type.choices()  # type: ignore
                django_field = models.CharField(
                    choices=choices,
                    max_length=max_length,
                    blank=blank,
                    **django_common_fields,
                )
            else:
                django_field = models.CharField(
                    max_length=max_length,
                    blank=blank,
                    **django_common_fields,
                )
        elif issubclass(field_type, int):
            if issubclass(field_type, BaseEnum):
                choices = field_type.choices()  # type: ignore
                django_field = models.IntegerField(
                    choices=choices,
                    **django_common_fields,
                )
            else:
                django_field = models.IntegerField(
                    **django_common_fields,
                )
        elif issubclass(field_type, float):
            django_field = models.FloatField(
                **django_common_fields,
            )
        elif issubclass(field_type, datetime):
            django_field = models.DateTimeField(
                **django_common_fields,
            )
        elif issubclass(field_type, list) or get_origin(field_type) is list:
            django_field = models.JSONField(
                **django_common_fields,
            )
        elif issubclass(field_type, dict) or get_origin(field_type) is dict:
            django_field = models.JSONField(
                **django_common_fields,
            )
        elif field_type is Any:
            django_field = models.JSONField(
                encoder=JsonEncoder,
                decoder=JsonDecoder,
                **django_common_fields,
            )
        else:
            raise ValueError(f"Unsupported field type: {field_name}({type(field_type)})")

        if django_field:
            attributes[field_name] = django_field

        # extra fields
        # TODO(data): no type hint
        for f in extra_fields:
            attributes[f] = getattr(pydantic_model, f)

    model = type(django_model_name, tuple(base_django_models), attributes)
    assert issubclass(model, models.Model)
    return model


def get_model_db_table_name(model_cls: type(models.Model)) -> str:  # type:ignore
    # ctp = ContentType.objects.get_for_model(model)
    # return "_".join([ctp.app_label, ctp.model])
    return model_cls.objects.model._meta.db_table


def create_model_db_table(
    model_cls: type(models.Model),  # type: ignore
    db_alias: str = DEFAULT_DB_ALIAS,
) -> None:
    f"""apply Django models in DB by creating its table

    :param model_cls: which Django Model's db table will be created.
    :param db_alias: the database alias in setting. default is "{DEFAULT_DB_ALIAS}".
    :return:
    """
    connection = connections[db_alias]
    with connection.schema_editor() as schema_editor:
        with transaction.atomic():
            schema_editor.create_model(model_cls)


def drop_model_db_table(
    model_cls: type(models.Model),  # type: ignore
    db_alias: str = DEFAULT_DB_ALIAS,
) -> None:
    """drop a db table of Django models

    :param model_cls: which Django Model's db table will be dropped.
    :param db_alias: the database alias in setting. default is "{DEFAULT_DB_ALIAS}".
    :return:
    """
    connection = connections[db_alias]
    with connection.schema_editor() as schema_editor:
        with transaction.atomic():
            schema_editor.delete_model(model_cls)
