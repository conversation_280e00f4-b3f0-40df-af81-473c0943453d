import asyncio
import logging
from typing import Any
from typing import Callable

from asgiref.sync import sync_to_async
from django.core.cache import BaseCache

from common.utils.asyncutils import run_coroutine_sync

logger = logging.getLogger(__name__)


def django_cache_get(
    cache: BaseCache,
    key: str,
    default: Any = None,
    args: list | tuple = [],
    kwargs: dict = {},
    timeout: int = 300,
    version: Any = None,
    touch: bool = False,
) -> Any:
    """

    :param cache: the Django cache instance
    :param key: cache key
    :param default: default value or function. if no value for given key, will return default value or return value
                    of default function. The new value will be store in cache.
    :param args: positional arguments to run default function
    :param kwargs: keyword arguments to run default function
    :param timeout: how many seconds will the new value expired. Default 300 seconds.
    :param version: if your configured cache supports version, use it.
    :param touch: Whether update timeout even if no new value is sent. default False.
    :return:
    """
    if touch:
        raise NotImplementedError('argument "touch" is not implemented')

    value = cache.get(key, version)
    if value:
        logging.warning(f"Cache hit for {key}")
    elif default is not None:
        if asyncio.iscoroutinefunction(default):
            value = run_coroutine_sync(default(*args, **kwargs))
        elif asyncio.iscoroutine(default):
            assert not (args or kwargs)
            value = run_coroutine_sync(default)
        elif isinstance(default, Callable):
            value = default(*args, **kwargs)
        else:
            value = default
        if value:
            cache.set(key, value, timeout=timeout, version=version)
    return value


async def django_cache_aget(
    cache: BaseCache,
    key: str,
    default: Any = None,
    args: list | tuple = (),
    kwargs: dict = {},
    timeout: int = 300,
    version: Any = None,
    touch: bool = False,
) -> Any:
    return sync_to_async(django_cache_get)(cache, key, default, args, kwargs, timeout, version, touch)
