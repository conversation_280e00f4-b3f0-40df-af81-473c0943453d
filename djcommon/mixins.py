from __future__ import annotations

from typing import Union
from uuid import uuid4, UUID

from django.contrib.auth import get_user_model
from django.db import models
from django.utils.translation import pgettext_lazy as _p

UserModel = get_user_model()

UniqueIdentity = Union[int, str, UUID]

class AbstractBaseModel(models.Model):
    _p("doc string", """abstract base class for all models""")

    class Meta:
        abstract = True
        # app_label = DB_APP_LABEL

    # ----- uid related -----
    # This "uid" is used to identify unique object.
    # It's not same as "pk" in Django query filter.
    # Although they are the same in most scenairos.

    uid_field = "id"

    @property
    def uid(self) -> UniqueIdentity:
        return getattr(self, self.uid_field)

    @uid.setter
    def uid(self, v: UniqueIdentity):
        setattr(self, self.uid_field, v)


# ----- identifier Mixin Models -----


class AutoIdPkModelMixin(models.Model):
    _p("doc string", """automatic int id primary key mixin""")

    id = models.AutoField(
        "ID",
        db_column="id",
        primary_key=True,
        editable=False,
        help_text=_p("field help", "the automatic PK int identifier"),
    )

    class Meta:
        abstract = True


class AutoIdModelMixin(models.Model):
    _p("doc string", """automatic int id mixin (unique index, not primary key)""")

    id = models.AutoField(
        "ID",
        db_column="id",
        unique=True,
        editable=False,
        help_text=_p("field help", "the automatic unique int identifier"),
    )

    class Meta:
        abstract = True


class UuidPkModelMixin(models.Model):
    _p("doc string", """uuid primary key mixin""")

    uuid = models.UUIDField(
        "UUID",
        db_column="uuid",
        primary_key=True,
        editable=False,
        auto_created=True,
        default=uuid4,
        help_text=_p("field help", "the uuid PK identifier"),
    )

    class Meta:
        abstract = True


class UuidModelMixin(models.Model):
    _p("doc string", """uuid mixin (unique index, not primary key)""")

    uuid = models.UUIDField(
        "UUID",
        db_column="uuid",
        unique=True,
        editable=False,
        auto_created=True,
        default=uuid4,
        help_text=_p("field help", "the uuid unique identifier"),
    )

    class Meta:
        abstract = True


# ----- user Mixin Models -----


class UserFKModelMixin(models.Model):
    _p("doc string", """user foreign key mixin""")

    user = models.ForeignKey(UserModel, db_column="user_id", on_delete=models.CASCADE)

    class Meta:
        abstract = True


class User1v1ModelMixin(models.Model):
    _p("doc string", """user one to one field mixin""")

    user = models.OneToOneField(UserModel, db_column="user_id", on_delete=models.CASCADE)

    class Meta:
        abstract = True


# ----- datetime Mixin Models -----


class CreateTimeModelMixin(models.Model):
    _p("doc string", """create time mixin""")

    created_at = models.DateTimeField(
        _p("field name", "create time"),
        db_column="created_at",
        auto_now_add=True,
        help_text=_p("field help", "when the record was inserted into database"),
    )

    class Meta:
        abstract = True


class UpdateTimeModelMixin(models.Model):
    _p("doc string", """update time mixin""")

    updated_at = models.DateTimeField(
        _p("field name", "update time"),
        db_column="updated_at",
        auto_now=True,
        help_text=_p("field help", "when the record was last updated"),
    )

    class Meta:
        abstract = True


class DeleteTimeModelMixin(models.Model):
    _p("doc string", """delete time mixin""")

    deleted_at = models.DateTimeField(
        _p("field name", "delete time"),
        editable=False,
        db_column="deleted_at",
        null=True,
        db_index=True,
        help_text=_p("field help", "when the record was soft deleted"),
    )

    class Meta:
        abstract = True


# same as UpdateTimeMixin
class TimestampModelMixin(models.Model):
    _p("doc string", """timestamp mixin""")

    timestamp = models.DateTimeField(
        _p("field name", "timestamp"),
        db_column="timestamp",
        auto_now=True,
        help_text=_p("field help", "when the record was last updated"),
    )

    class Meta:
        abstract = True


# soft delete mixin is same as DeleteTimeMixin
SoftDeletionModelMixin = DeleteTimeModelMixin


__all__ = [
    kls.__name__
    for kls in (
        AbstractBaseModel,
        AutoIdPkModelMixin,
        AutoIdModelMixin,
        UserFKModelMixin,
        CreateTimeModelMixin,
        UpdateTimeModelMixin,
        DeleteTimeModelMixin,
        TimestampModelMixin,
        SoftDeletionModelMixin,
    )
]
