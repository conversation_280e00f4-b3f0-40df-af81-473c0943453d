"""
django setting definitions.
"""

from os import getenv
from pathlib import Path
from pycommon.config import (
    DEBUG,
    APP_NAME,
    APP_PREFIX_,
    APP_DOMAIN,
    DOMAIN,
    ALLOWED_HOSTS,
    LANGUAGE_CODE,
    TIME_ZONE,
    DB_TYPE,
    DB_HOST,
    DB_PORT,
    DB_NAME,
    DB_USER,
    DB_PASSWORD,
)


LANGUAGE_CODE, TIME_ZONE,  # avoid lint warning

SECRET_KEY = getenv(f"{APP_PREFIX_}SECRET_KEY", "-sd0923h48fy78h73t27*#_=kdiy&#T")

LANGUAGE_COOKIE_NAME = getenv(f"{APP_PREFIX_}LANGUAGE_COOKIE_NAME", "lang")
LANGUAGE_COOKIE_DOMAIN = getenv(f"{APP_PREFIX_}LANGUAGE_COOKIE_DOMAIN", DOMAIN)
LANGUAGES = [
    # ('zh', _('Chinese')),
    ("zh-hans", "简体中文"),
    # ('zh-hant', _('Chinese Traditional')),
    ("en", "English"),
]
LOCALE_PATHS = ("locale",)  # 必须指定LOCALE_PATHS


BASE_DIR: Path = Path(__file__).resolve().parent.parent

if ALLOWED_HOSTS:
    try:
        ALLOWED_HOSTS = [host.strip() for host in ALLOWED_HOSTS.split(",")]
    except:
        ALLOWED_HOSTS = ["*"] if DEBUG else []
        print(f"WARNING: env {APP_PREFIX_}ALLOWED_HOSTS incorrect, use default.")
else:
    ALLOWED_HOSTS = ["*"] if DEBUG else [f"{APP_DOMAIN}", "127.0.0.1", "localhost"]


DATABASES = {
    "default": {
        "ENGINE": f"django.db.backends.{DB_TYPE}",
    }
}

if DB_TYPE in ["postgresql", "mysql"]:
    DATABASES["default"].update(
        {
            "NAME": f"{DB_NAME}",
            "USER": f"{DB_USER}",
            "PASSWORD": f"{DB_PASSWORD}",
            "HOST": f"{DB_HOST}",
            "PORT": f"{DB_PORT}",
        }
    )
elif DB_TYPE == "sqlite3":
    DATABASES["default"].update(
        {
            "NAME": f"{DB_NAME}.sqlite3",
        }
    )
else:
    raise Exception(f"Unsupported datbase type {DB_TYPE}.")


MEDIA_ROOT = getenv(f"{APP_PREFIX_}MEDIA_ROOT", f"/opt/{APP_NAME}/media")
MEDIA_URL = "m/"
