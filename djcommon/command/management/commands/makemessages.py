"""to override `makemessages` Django command"""

# see https://stackoverflow.com/a/71938023/687197

from django.core.management.commands import makemessages
from os import linesep
from sys import stderr


class Command(makemessages.Command):
    help = f"** Patched: support _p as pgettext similar. **{linesep}{makemessages.Command.help}"
    # help = "Make messages in en_US and zh_CN, support _p, ignore venv folder."

    def handle(self, *args, **options):
        xgettext_options = getattr(self, "xgettext_options")
        if xgettext_options:
            # _p:1c,2 represents _p('msgctxt', 'msgid'), function with two arguments like pgettext_lazy
            xgettext_options.append("--keyword=_p:1c,2")
        else:
            stderr.writelines('Fail to patch "makemessages" command. "xgettext_options" property not found.')
        return super().handle(*args, **options)
