from django.db.models.signals import post_save
from django.dispatch import receiver
from pycommon.logwrapper import getLogger

from .mixins import UserModel


log = getLogger(__package__ + "." + __file__)

# TODO: is it worth to wrap ?


@receiver(post_save, sender=UserModel)
def user_created_handler(sender, **kwargs):
    """to create a profile when user created."""
    created = kwargs.get("created", False)
    if created:
        user = kwargs.get("instance")
        profile = ProfileModel(user_id=user.id)
        profile.save()
        log.info("Profile for %s created." % user)
